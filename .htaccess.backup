# BEGIN W3TC WEBP
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_ACCEPT} image/webp
    RewriteCond %{REQUEST_FILENAME} (.+)\.(jpe?g|png|gif)$
    RewriteCond %1\.webp -f
    RewriteCond %{QUERY_STRING} !type=original
    RewriteRule (.+)\.(jpe?g|png|gif)$ $1.webp [NC,T=image/webp,E=webp,L]
</IfModule>
<IfModule mod_headers.c>
    <FilesMatch "\.(jpe?g|png|gif)$">
        Header append Vary Accept
    </FilesMatch>
</IfModule>
AddType image/webp .webp
# END W3TC WEBP

# BEGIN W3TC Browser Cache
<IfModule mod_mime.c>
    AddType text/css .css
    AddType text/x-component .htc
    AddType application/x-javascript .js
    AddType application/javascript .js2
    AddType text/javascript .js3
    AddType text/x-js .js4
    AddType text/html .html .htm
    AddType text/richtext .rtf .rtx
    AddType text/plain .txt
    AddType text/xsd .xsd
    AddType text/xsl .xsl
    AddType text/xml .xml
    AddType video/asf .asf .asx .wax .wmv .wmx
    AddType video/avi .avi
    AddType image/avif .avif
    AddType image/avif-sequence .avifs
    AddType image/bmp .bmp
    AddType application/java .class
    AddType video/divx .divx
    AddType application/msword .doc .docx
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-msdownload .exe
    AddType image/gif .gif
    AddType application/x-gzip .gz .gzip
    AddType image/x-icon .ico
    AddType image/jpeg .jpg .jpeg .jpe
    AddType image/webp .webp
    AddType application/json .json
    AddType application/vnd.ms-access .mdb
    AddType audio/midi .mid .midi
    AddType video/quicktime .mov .qt
    AddType audio/mpeg .mp3 .m4a
    AddType video/mp4 .mp4 .m4v
    AddType video/mpeg .mpeg .mpg .mpe
    AddType video/webm .webm
    AddType application/vnd.ms-project .mpp
    AddType application/x-font-otf .otf
    AddType application/vnd.ms-opentype ._otf
    AddType application/vnd.oasis.opendocument.database .odb
    AddType application/vnd.oasis.opendocument.chart .odc
    AddType application/vnd.oasis.opendocument.formula .odf
    AddType application/vnd.oasis.opendocument.graphics .odg
    AddType application/vnd.oasis.opendocument.presentation .odp
    AddType application/vnd.oasis.opendocument.spreadsheet .ods
    AddType application/vnd.oasis.opendocument.text .odt
    AddType audio/ogg .ogg
    AddType video/ogg .ogv
    AddType application/pdf .pdf
    AddType image/png .png
    AddType application/vnd.ms-powerpoint .pot .pps .ppt .pptx
    AddType audio/x-realaudio .ra .ram
    AddType image/svg+xml .svg .svgz
    AddType application/x-shockwave-flash .swf
    AddType application/x-tar .tar
    AddType image/tiff .tif .tiff
    AddType application/x-font-ttf .ttf .ttc
    AddType application/vnd.ms-opentype ._ttf
    AddType audio/wav .wav
    AddType audio/wma .wma
    AddType application/vnd.ms-write .wri
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-excel .xla .xls .xlsx .xlt .xlw
    AddType application/zip .zip
</IfModule>
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css A31536000
    ExpiresByType text/x-component A31536000
    ExpiresByType application/x-javascript A31536000
    ExpiresByType application/javascript A31536000
    ExpiresByType text/javascript A31536000
    ExpiresByType text/x-js A31536000
    ExpiresByType text/html A3600
    ExpiresByType text/richtext A3600
    ExpiresByType text/plain A3600
    ExpiresByType text/xsd A3600
    ExpiresByType text/xsl A3600
    ExpiresByType text/xml A3600
    ExpiresByType video/asf A31536000
    ExpiresByType video/avi A31536000
    ExpiresByType image/avif A31536000
    ExpiresByType image/avif-sequence A31536000
    ExpiresByType image/bmp A31536000
    ExpiresByType application/java A31536000
    ExpiresByType video/divx A31536000
    ExpiresByType application/msword A31536000
    ExpiresByType application/vnd.ms-fontobject A31536000
    ExpiresByType application/x-msdownload A31536000
    ExpiresByType image/gif A31536000
    ExpiresByType application/x-gzip A31536000
    ExpiresByType image/x-icon A31536000
    ExpiresByType image/jpeg A31536000
    ExpiresByType image/webp A31536000
    ExpiresByType application/json A31536000
    ExpiresByType application/vnd.ms-access A31536000
    ExpiresByType audio/midi A31536000
    ExpiresByType video/quicktime A31536000
    ExpiresByType audio/mpeg A31536000
    ExpiresByType video/mp4 A31536000
    ExpiresByType video/mpeg A31536000
    ExpiresByType video/webm A31536000
    ExpiresByType application/vnd.ms-project A31536000
    ExpiresByType application/x-font-otf A31536000
    ExpiresByType application/vnd.ms-opentype A31536000
    ExpiresByType application/vnd.oasis.opendocument.database A31536000
    ExpiresByType application/vnd.oasis.opendocument.chart A31536000
    ExpiresByType application/vnd.oasis.opendocument.formula A31536000
    ExpiresByType application/vnd.oasis.opendocument.graphics A31536000
    ExpiresByType application/vnd.oasis.opendocument.presentation A31536000
    ExpiresByType application/vnd.oasis.opendocument.spreadsheet A31536000
    ExpiresByType application/vnd.oasis.opendocument.text A31536000
    ExpiresByType audio/ogg A31536000
    ExpiresByType video/ogg A31536000
    ExpiresByType application/pdf A31536000
    ExpiresByType image/png A31536000
    ExpiresByType application/vnd.ms-powerpoint A31536000
    ExpiresByType audio/x-realaudio A31536000
    ExpiresByType image/svg+xml A31536000
    ExpiresByType application/x-shockwave-flash A31536000
    ExpiresByType application/x-tar A31536000
    ExpiresByType image/tiff A31536000
    ExpiresByType application/x-font-ttf A31536000
    ExpiresByType application/vnd.ms-opentype A31536000
    ExpiresByType audio/wav A31536000
    ExpiresByType audio/wma A31536000
    ExpiresByType application/vnd.ms-write A31536000
    ExpiresByType application/font-woff A31536000
    ExpiresByType application/font-woff2 A31536000
    ExpiresByType application/vnd.ms-excel A31536000
    ExpiresByType application/zip A31536000
</IfModule>
<IfModule mod_deflate.c>
    <IfModule mod_filter.c>
        AddOutputFilterByType DEFLATE text/css text/x-component application/x-javascript application/javascript text/javascript text/x-js text/html text/richtext text/plain text/xsd text/xsl text/xml image/bmp application/java application/msword application/vnd.ms-fontobject application/x-msdownload image/x-icon application/json application/vnd.ms-access video/webm application/vnd.ms-project application/x-font-otf application/vnd.ms-opentype application/vnd.oasis.opendocument.database application/vnd.oasis.opendocument.chart application/vnd.oasis.opendocument.formula application/vnd.oasis.opendocument.graphics application/vnd.oasis.opendocument.presentation application/vnd.oasis.opendocument.spreadsheet application/vnd.oasis.opendocument.text audio/ogg application/pdf application/vnd.ms-powerpoint image/svg+xml application/x-shockwave-flash image/tiff application/x-font-ttf application/vnd.ms-opentype audio/wav application/vnd.ms-write application/font-woff application/font-woff2 application/vnd.ms-excel
    <IfModule mod_mime.c>
        # DEFLATE by extension
        AddOutputFilter DEFLATE js css htm html xml
    </IfModule>
    </IfModule>
</IfModule>
<FilesMatch "\.(css|htc|less|js|js2|js3|js4|CSS|HTC|LESS|JS|JS2|JS3|JS4)$">
    FileETag MTime Size
    <IfModule mod_headers.c>
        Header set Pragma "public"
        Header append Cache-Control "public"
        Header unset Set-Cookie
    </IfModule>
</FilesMatch>
<FilesMatch "\.(html|htm|rtf|rtx|txt|xsd|xsl|xml|HTML|HTM|RTF|RTX|TXT|XSD|XSL|XML)$">
    FileETag MTime Size
    <IfModule mod_headers.c>
        Header set Pragma "public"
        Header append Cache-Control "public"
    </IfModule>
</FilesMatch>
<FilesMatch "\.(asf|asx|wax|wmv|wmx|avi|avif|avifs|bmp|class|divx|doc|docx|eot|exe|gif|gz|gzip|ico|jpg|jpeg|jpe|webp|json|mdb|mid|midi|mov|qt|mp3|m4a|mp4|m4v|mpeg|mpg|mpe|webm|mpp|otf|_otf|odb|odc|odf|odg|odp|ods|odt|ogg|ogv|pdf|png|pot|pps|ppt|pptx|ra|ram|svg|svgz|swf|tar|tif|tiff|ttf|ttc|_ttf|wav|wma|wri|woff|woff2|xla|xls|xlsx|xlt|xlw|zip|ASF|ASX|WAX|WMV|WMX|AVI|AVIF|AVIFS|BMP|CLASS|DIVX|DOC|DOCX|EOT|EXE|GIF|GZ|GZIP|ICO|JPG|JPEG|JPE|WEBP|JSON|MDB|MID|MIDI|MOV|QT|MP3|M4A|MP4|M4V|MPEG|MPG|MPE|WEBM|MPP|OTF|_OTF|ODB|ODC|ODF|ODG|ODP|ODS|ODT|OGG|OGV|PDF|PNG|POT|PPS|PPT|PPTX|RA|RAM|SVG|SVGZ|SWF|TAR|TIF|TIFF|TTF|TTC|_TTF|WAV|WMA|WRI|WOFF|WOFF2|XLA|XLS|XLSX|XLT|XLW|ZIP)$">
    FileETag MTime Size
    <IfModule mod_headers.c>
        Header set Pragma "public"
        Header append Cache-Control "public"
        Header unset Set-Cookie
    </IfModule>
</FilesMatch>
<IfModule mod_headers.c>
    Header set Referrer-Policy "no-referrer-when-downgrade"
</IfModule>
# END W3TC Browser Cache
# BEGIN W3TC Page Cache core
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /paylearn
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%{QUERY_STRING}]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)ScCid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)_branch_match_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)_bta_c(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)_bta_tid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)_ga(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)_gl(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)_ke(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)adgroupid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)adid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)age\-verified(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)ao_noptimize(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)campaignid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)campid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)click_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)cn\-reloaded(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)customid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)dicbo(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)dm_i(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)ef_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)epik(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)fb_action_ids(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)fb_action_types(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)fb_source(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)fbclid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)gclid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)gclsrc(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)gdffi(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)gdfms(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)gdftrk(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_acc(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_ad(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_cam(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_grp(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_kw(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_mt(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_net(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_src(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_tgt(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)hsa_ver(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)igshid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)li_fat_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_campaign(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_cid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_content(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_group(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_keyword(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_medium(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_placement(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)matomo_source(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mc_cid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mc_eid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mkcid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mkevt(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mkrid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mkwid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)msclkid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_campaign(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_cid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_content(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_group(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_keyword(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_medium(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_placement(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)mtm_source(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pcrid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)piwik_campaign(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)piwik_keyword(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)piwik_kwd(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_campaign(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_cid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_content(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_keyword(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_kwd(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_medium(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pk_source(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)pp(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)redirect_log_mongo_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)redirect_mongo_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)ref(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)s_kwcid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)sb_referer_host(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)si(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)sscid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)tblci(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)toolid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)trk_contact(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)trk_module(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)trk_msg(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)trk_sid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)ttclid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)twclid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)usqp(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_campaign(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_content(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_expid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_id(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_medium(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_source(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)utm_term(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^(.*?&|)wbraid(=[^&]*)?(&.*|)$ [NC]
    RewriteRule ^ - [E=W3TC_QUERY_STRING:%1%3]
    RewriteCond %{ENV:W3TC_QUERY_STRING} ^&+$
    RewriteRule ^ - [E=W3TC_QUERY_STRING]
    RewriteCond %{HTTPS} =on
    RewriteRule .* - [E=W3TC_SSL:_ssl]
    RewriteCond %{SERVER_PORT} =443
    RewriteRule .* - [E=W3TC_SSL:_ssl]
    RewriteCond %{HTTP:X-Forwarded-Proto} =https [NC]
    RewriteRule .* - [E=W3TC_SSL:_ssl]
    RewriteCond %{HTTP:Accept-Encoding} gzip
    RewriteRule .* - [E=W3TC_ENC:_gzip]
    RewriteCond %{HTTP_COOKIE} w3tc_preview [NC]
    RewriteRule .* - [E=W3TC_PREVIEW:_preview]
    RewriteCond %{REQUEST_URI} \/$
    RewriteRule .* - [E=W3TC_SLASH:_slash]
    RewriteCond %{REQUEST_METHOD} !=POST
    RewriteCond %{ENV:W3TC_QUERY_STRING} =""
    RewriteCond %{HTTP_COOKIE} !(comment_author|wp\-postpass|w3tc_logged_out|wordpress_logged_in|wptouch_switch_toggle) [NC]
    RewriteCond "%{DOCUMENT_ROOT}C:/xampp/htdocs/paylearn/wp-content/cache/page_enhanced/%{HTTP_HOST}/%{REQUEST_URI}/_index%{ENV:W3TC_SLASH}%{ENV:W3TC_SSL}%{ENV:W3TC_PREVIEW}.html%{ENV:W3TC_ENC}" -f
    RewriteRule .* "C:/xampp/htdocs/paylearn/wp-content/cache/page_enhanced/%{HTTP_HOST}/%{REQUEST_URI}/_index%{ENV:W3TC_SLASH}%{ENV:W3TC_SSL}%{ENV:W3TC_PREVIEW}.html%{ENV:W3TC_ENC}" [L]
    RewriteCond %{REQUEST_METHOD} !=POST
    RewriteCond %{ENV:W3TC_QUERY_STRING} =""
    RewriteCond %{HTTP_COOKIE} !(comment_author|wp\-postpass|w3tc_logged_out|wordpress_logged_in|wptouch_switch_toggle) [NC]
    RewriteCond "%{DOCUMENT_ROOT}C:/xampp/htdocs/paylearn/wp-content/cache/page_enhanced/%{HTTP_HOST}/%{REQUEST_URI}/_index%{ENV:W3TC_SLASH}%{ENV:W3TC_SSL}%{ENV:W3TC_PREVIEW}.xml%{ENV:W3TC_ENC}" -f
    RewriteRule .* "C:/xampp/htdocs/paylearn/wp-content/cache/page_enhanced/%{HTTP_HOST}/%{REQUEST_URI}/_index%{ENV:W3TC_SLASH}%{ENV:W3TC_SSL}%{ENV:W3TC_PREVIEW}.xml%{ENV:W3TC_ENC}" [L]
</IfModule>
# END W3TC Page Cache core
# BEGIN WordPress
# The directives (lines) between "BEGIN WordPress" and "END WordPress" are
# dynamically generated, and should only be modified via WordPress filters.
# Any changes to the directives between these markers will be overwritten.
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /paylearn/
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /paylearn/index.php [L]
</IfModule>

# END WordPress
