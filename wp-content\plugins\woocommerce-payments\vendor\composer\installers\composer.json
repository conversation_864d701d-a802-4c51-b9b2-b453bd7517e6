{"name": "composer/installers", "type": "composer-plugin", "license": "MIT", "description": "A multi-framework Composer library installer", "keywords": ["installer", "Aimeos", "AGL", "AnnotateCms", "Attogram", "Bitrix", "CakePHP", "Chef", "Cockpit", "CodeIgniter", "concrete5", "Craft", "Croogo", "DokuWiki", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ExpressionEngine", "eZ Platform", "FuelPHP", "Grav", "<PERSON><PERSON>", "ImageCMS", "iTop", "<PERSON><PERSON><PERSON>", "Kanboard", "Known", "<PERSON><PERSON>", "Lan Management System", "<PERSON><PERSON>", "Lavalite", "Lithium", "Magento", "majima", "<PERSON><PERSON>", "MantisBT", "Mautic", "Maya", "MODX", "MODX Evo", "MediaWiki", "OXID", "osclass", "MODULEWork", "<PERSON><PERSON><PERSON>", "<PERSON>wi<PERSON>", "pxcms", "phpBB", "Plentymarkets", "PPI", "<PERSON><PERSON><PERSON>", "Porto", "ProcessWire", "RadPHP", "ReIndex", "Roundcube", "shopware", "SilverStripe", "SMF", "Starbug", "SyDES", "<PERSON><PERSON><PERSON>", "symfony", "Thelia", "TYPO3", "WHMCS", "WolfCMS", "WordPress", "YAWIK", "Zend", "<PERSON><PERSON><PERSON>"], "homepage": "https://composer.github.io/installers/", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "autoload-dev": {"psr-4": {"Composer\\Installers\\Test\\": "tests/Composer/Installers/Test"}}, "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "replace": {"shama/baton": "*", "roundcube/plugin-installer": "*"}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "symfony/phpunit-bridge": "^4.2 || ^5", "phpstan/phpstan": "^0.12.55", "symfony/process": "^2.3", "phpstan/phpstan-phpunit": "^0.12.16"}, "scripts": {"test": "SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT=1 vendor/bin/simple-phpunit", "phpstan": "vendor/bin/phpstan analyse"}}