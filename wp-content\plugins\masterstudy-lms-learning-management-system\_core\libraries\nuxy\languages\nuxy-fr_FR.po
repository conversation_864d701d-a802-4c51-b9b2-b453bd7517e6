msgid ""
msgstr ""
"Project-Id-Version: NUXY\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-23 06:46+0000\n"
"PO-Revision-Date: 2024-12-23 13:44+0000\n"
"Last-Translator: \n"
"Language-Team: French (France)\n"
"Language: fr_FR\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.10; wp-6.5.4\n"
"X-Domain: nuxy"

#: taxonomy_meta/fields/image.php:10
msgid "Add image"
msgstr "Ajouter une image"

#: metaboxes/metabox.php:248
msgid "Backup Font Family"
msgstr "Sauvegarde de la famille de polices"

#: metaboxes/google_fonts.php:22
msgid "Black 900"
msgstr "Noir 900"

#: metaboxes/google_fonts.php:23
msgid "Black 900 italic"
msgstr "Noir 900 italique"

#: metaboxes/google_fonts.php:20
msgid "Bold 700"
msgstr "Audacieux 700"

#: metaboxes/google_fonts.php:21
msgid "Bold 700 italic"
msgstr "Gras 700 italique"

#: metaboxes/google_fonts.php:53
msgid "Capitalize"
msgstr "Capitaliser"

#: metaboxes/google_fonts.php:43
msgid "Center"
msgstr "Centre"

#: settings/settings.php:94
msgid "Choose Page"
msgstr "Choisir la page"

#: metaboxes/metabox.php:42
msgid "Choose User"
msgstr "Choisir l'utilisateur"

#: metaboxes/fields/text.php:28
msgid "Copied"
msgstr "Copié"

#: metaboxes/metabox.php:254
msgid "Copy settings"
msgstr "Copier les paramètres"

#: metaboxes/metabox.php:258
msgid "Couldn't copy settings"
msgstr "Impossible de copier les paramètres"

#: metaboxes/google_fonts.php:29
msgid "Cyrillic"
msgstr "Cyrillique"

#: metaboxes/google_fonts.php:30
msgid "Cyrillic ext"
msgstr "Cyrillique ext"

#: metaboxes/fields/duration.php:35
msgid "Days"
msgstr "Jours"

#: metaboxes/google_fonts.php:41
msgid "Default"
msgstr "Défaut"

#: metaboxes/metabox.php:301
msgid "Delete"
msgstr "Supprimer"

#: taxonomy_meta/fields/image.php:13
msgid "Delete image"
msgstr "Supprimer l'image"

#: metaboxes/metabox.php:266
msgid ""
"Download and store Google Fonts locally. Set the fonts in the typography."
msgstr ""
"Téléchargez et stockez les polices Google localement. Définissez les polices "
"dans la typographie."

#: metaboxes/metabox.php:265
msgid "Download Google Fonts"
msgstr "Télécharger les polices Google"

#: metaboxes/fields/duration.php:24
msgid "duration"
msgstr "durée"

#: metaboxes/fields/repeater.php:25 metaboxes/fields/textarea.php:25
#: metaboxes/fields/text.php:27
msgid "Enter"
msgstr "Entrer"

#: metaboxes/fields/number.php:22
msgid "Enter numbers..."
msgstr "Entrer des nombres..."

#: helpers/file_upload.php:21
msgid "Error occurred, please try again"
msgstr "Une erreur s'est produite, veuillez réessayer"

#: metaboxes/metabox.php:259
msgid "Export options"
msgstr "Options d'exportation"

#: metaboxes/metabox.php:252
msgid "Font Color"
msgstr "Couleur de la police"

#: metaboxes/metabox.php:247
msgid "Font Family"
msgstr "Famille de polices"

#: metaboxes/metabox.php:243
msgid "Font size"
msgstr "Taille de la police"

#: metaboxes/metabox.php:250
msgid "Font Subsets"
msgstr "Sous-ensembles de polices"

#: metaboxes/metabox.php:263
msgid "Font Synchronize"
msgstr "Synchronisation des polices"

#: metaboxes/metabox.php:249
msgid "Font Weignt & Style"
msgstr "Poids et style de la police"

#: metaboxes/google_fonts.php:31
msgid "Greek"
msgstr "Grec"

#: metaboxes/google_fonts.php:32
msgid "Greek ext"
msgstr "grec ext"

#: metaboxes/fields/duration.php:34
msgid "Hours"
msgstr "Heures"

#. Author URI of the plugin
msgid "https://stylemixthemes.com"
msgstr "https://stylemixthemes.com"

#: metaboxes/fields/image.php:24
msgid "Image URL"
msgstr "URL de limage"

#: metaboxes/metabox.php:260
msgid "Import options"
msgstr "Options d'import"

#: metaboxes/metabox.php:255
msgid "Import settings"
msgstr "Importer les paramètres"

#: metaboxes/metabox-display.php:27
msgid "Import/Export"
msgstr "Importer/Exporter"

#: helpers/file_upload.php:65
msgid "Invalid file extension"
msgstr "Extension de fichier invalide"

#: metaboxes/google_fonts.php:33
msgid "Latin"
msgstr "Latin"

#: metaboxes/google_fonts.php:34
msgid "Latin ext"
msgstr "Latin ext"

#: metaboxes/google_fonts.php:42
msgid "Left"
msgstr "Gauche"

#: metaboxes/metabox.php:246
msgid "Letter spacing"
msgstr "Espacement entre les lettres"

#: metaboxes/google_fonts.php:14
msgid "Light 300"
msgstr "Lumière 300"

#: metaboxes/google_fonts.php:15
msgid "Light 300 italic"
msgstr "Léger 300 italique"

#: metaboxes/metabox.php:244
msgid "Line height"
msgstr "Hauteur de ligne"

#: metaboxes/google_fonts.php:52
msgid "Lowercase"
msgstr "Minuscule"

#: metaboxes/google_fonts.php:18
msgid "Medium 500"
msgstr "Moyen 500"

#: metaboxes/google_fonts.php:19
msgid "Medium 500 italic"
msgstr "Moyen 500 italique"

#: metaboxes/fields/duration.php:33
msgid "Minutes"
msgstr "Minutes"

#: metaboxes/google_fonts.php:50
msgid "Normal"
msgstr "Normale"

#. Name of the plugin
msgid "NUXY"
msgstr "NUXY"

#: settings/settings.php:162
msgid "Oops, something went wrong"
msgstr "Oups, quelque chose s'est mal passé"

#: helpers/file_upload.php:53
msgid "Please, select file"
msgstr "Veuillez sélectionner le fichier"

#: metaboxes/metabox.php:302
msgid "Preview"
msgstr "Aperçu"

#: metaboxes/google_fonts.php:16
msgid "Regular 400"
msgstr "Régulier 400"

#: metaboxes/google_fonts.php:17
msgid "Regular 400 italic"
msgstr "Régulier 400 italique"

#: metaboxes/fields/image.php:27
msgid "Remove"
msgstr "Supprimer"

#: metaboxes/fields/image.php:26
msgid "Replace"
msgstr "Remplacer"

#: metaboxes/google_fonts.php:44
msgid "Right"
msgstr "Droit"

#: settings/view/header.php:82
msgid "Save Settings"
msgstr "Sauvegarder les paramètres"

#: settings/settings.php:158
msgid "Saved!"
msgstr "Sauvé!"

#: settings/view/header.php:47
msgid "Search"
msgstr "Chercher"

#: taxonomy_meta/fields/image.php:45
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr "Sélectionnez ou téléchargez les médias de votre choix"

#: settings/settings.php:159
msgid "Settings are changed"
msgstr "Les paramètres sont modifiés"

#: settings/settings.php:163
msgid "Settings are not changed"
msgstr "Les paramètres ne sont pas modifiés"

#: metaboxes/metabox.php:257
msgid "Settings copied to buffer"
msgstr "Paramètres copiés dans la mémoire tampon"

#: metaboxes/metabox.php:261
msgid "Sorry, no matching options."
msgstr "Désolé, aucune option ne correspond"

#. Author of the plugin
msgid "StylemixThemes"
msgstr "StylemixThemes"

#: metaboxes/metabox.php:264
msgid ""
"Sync and update your fonts if they are displayed incorrectly on your website."
msgstr ""
"Synchronisez et mettez à jour vos polices si elles s'affichent de manière "
"incorrecte sur votre site Web."

#: metaboxes/metabox.php:262
msgid "Synchronize"
msgstr "Synchroniser"

#: metaboxes/metabox.php:251
msgid "Text Align"
msgstr "Alignement du texte"

#: metaboxes/metabox.php:253
msgid "Text transform"
msgstr "Transformation du texte"

#: metaboxes/google_fonts.php:12
msgid "Thin 100"
msgstr "Mince 100"

#: metaboxes/google_fonts.php:13
msgid "Thin 100 italic"
msgstr "Italique fin 100"

#: metaboxes/fields/image.php:25
msgid "Upload"
msgstr "Télécharger"

#: metaboxes/google_fonts.php:51
msgid "Uppercase"
msgstr "Majuscule"

#: taxonomy_meta/fields/image.php:47
msgid "Use this media"
msgstr "Utiliser ce média"

#: metaboxes/google_fonts.php:35
msgid "Vietnamese"
msgstr "Vietnamien"

#: metaboxes/metabox.php:256
msgid ""
"WARNING! This will overwrite all existing option values, please proceed with "
"caution!"
msgstr ""
"ATTENTION ! Ceci va écraser toutes les valeurs d'options existantes, "
"veuillez procéder avec "

#: metaboxes/metabox.php:245
msgid "Word spacing"
msgstr "Espacement entre les mots"

#. Description of the plugin
msgid "WordPress Custom Fields & Theme Options with Vue.js."
msgstr "Champs personnalisés et options de thème WordPress avec Vue.js."
