# Nuxy
[![Latest Version](https://img.shields.io/badge/release-v1.0.0-blue?style=flat-square)](https://github.com/StylemixThemes/nuxy/releases)
[![GNU Licensed](https://img.shields.io/badge/license-GNU%20v3.0-brightgreen)](https://github.com/StylemixThemes/nuxy/blob/master/LICENSE)

Custom fields for WordPress Options and Post Types.

WordPress so we tried to combine these two awesome techniques to build something to make development easier and faster.
Every field is a Vue Component, which saves in global data via WordPress default functions. Framework positioned as developer helper to build complicated options with extra logic, encapsulated from the main Application.



## Three-in-one

Today we have a lot of frameworks for adding options pages and custom metaboxes to posts or custom post types in WordPress.
Yet, we wanted to create a framework where you can create fields to use on options pages, post types, and even in categories or taxonomies.
In one framework you can create all options-related things for different plugins and themes and have unlimited content possibilities.

## [Full Documentation](https://docs.stylemixthemes.com/nuxy)

- [Installation](https://docs.stylemixthemes.com/nuxy/installation)
- [Theme Options page](https://docs.stylemixthemes.com/nuxy/adding-page-options)
- [Custom Fields](#)
	- [Post type Custom Fields](https://docs.stylemixthemes.com/nuxy/adding-post-options)
	- [Category Custom Fields](https://docs.stylemixthemes.com/nuxy/adding-taxonomy-options)
- [Getting Fields](https://docs.stylemixthemes.com/nuxy/getting-fields)
- [Available Fields](https://docs.stylemixthemes.com/nuxy/fields/general-field-parameters)
- [Adding own Custom Field](https://docs.stylemixthemes.com/nuxy/registering-field)