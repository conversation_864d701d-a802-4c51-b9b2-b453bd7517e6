/*!
 * vue-range-slider v1.0.3
 * (c) 2016-2018 xwpongithub
 * Released under the MIT License.
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):t.VueRangeSlider=i()}(this,function(){"use strict";var i,o=(i="undefined"!=typeof window&&window.devicePixelRatio||1,function(t){return Math.round(t*i)/i}),t=function(){for(var t=navigator.userAgent.toLowerCase(),i=["Android","iPhone","SymbianOS","Windows Phone","iPad","iPod"],e=!1,s=0;s<i.length;s++)if(0<t.indexOf(i[s].toLowerCase())){e=!0;break}return e}();function s(t){return Array.prototype.isArray?Array.isArray(t):t instanceof Array||"[object Array]"===Object.prototype.toString.call(t)}function n(t,e){return Object.prototype.toString.call(t)!==Object.prototype.toString.call(e)||(s(t)&&t.length===e.length?t.some(function(t,i){return t!==e[i]}):t!==e)}var e=document.createElement("div").style,r=function(){var t={webkit:"webkitTransform",Moz:"MozTransform",O:"OTransform",ms:"msTransform",standard:"transform"};for(var i in t)if(void 0!==e[t[i]])return i;return!1}();function a(t){return!1!==r&&("standard"===r?"transitionEnd"===t?"transitionend":t:r+t.charAt(0).toUpperCase()+t.substr(1))}function h(t,i,e,s){t.addEventListener(i,e,{passive:!1,capture:!!s})}function l(t,i,e,s){t.removeEventListener(i,e,{passive:!1,capture:!!s})}var u=a("transform"),d=a("transitionDuration"),c=a("transitionEnd"),f="touchstart",p="touchmove",g="touchend",m="touchcancel",y="mousedown",v="mousemove",S="mouseup",b="mouseleave",x="keydown",w="keyup",V="resize",R={name:"vue-range-slider",props:{show:{type:Boolean,default:!0},value:{type:[String,Number,Array,Object],default:0},min:{type:Number,default:0},max:{type:Number,default:100},step:{type:Number,default:1},width:{type:[Number,String],default:"auto"},height:{type:[Number,String],default:6},dotSize:{type:Number,default:16},dotWidth:{type:Number,required:!1},dotHeight:{type:Number,required:!1},stopPropagation:{type:Boolean,default:!1},eventType:{type:String,default:"auto"},realTime:{type:Boolean,default:!1},tooltip:{type:[String,Boolean],default:"always",validator:function(t){return-1<["hover","always"].indexOf(t)}},direction:{type:String,default:"horizontal",validator:function(t){return-1<["horizontal","vertical"].indexOf(t)}},reverse:{type:Boolean,default:!1},disabled:{type:[Boolean,Array],default:!1},piecewiseLabel:{type:Boolean,default:!1},piecewise:{type:Boolean,default:!1},processDraggable:{type:Boolean,default:!1},clickable:{type:Boolean,default:!0},fixed:{type:Boolean,default:!1},debug:{type:Boolean,default:!0},minRange:{type:Number},maxRange:{type:Number},tooltipMerge:{type:Boolean,default:!0},startAnimation:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},enableCross:{type:Boolean,default:!0},speed:{type:Number,default:.5},useKeyboard:{type:Boolean,default:!1},actionsKeyboard:{type:Array,default:function(){return[function(t){return t-1},function(t){return t+1}]}},data:Array,formatter:[String,Function],mergeFormatter:[String,Function],tooltipDir:[Array,String],tooltipStyle:[Array,Object,Function],sliderStyle:[Array,Object,Function],focusStyle:[Array,Object,Function],disabledStyle:Object,processStyle:Object,bgStyle:Object,piecewiseStyle:Object,piecewiseActiveStyle:Object,disabledDotStyle:[Array,Object,Function],labelStyle:Object,labelActiveStyle:Object},data:function(){return{currentValue:0,size:0,fixedValue:0,focusSlider:0,currentSlider:0,flag:!1,processFlag:!1,processSign:!1,keydownFlag:!1,focusFlag:!1,dragFlag:!1,crossFlag:!1,isComponentExists:!0,isMounted:!1}},render:function(r){var n=this,t=[];if(this.isRange){var i=r("div",{ref:"dot0",staticClass:"slider-dot",class:[this.tooltipStatus,{"slider-dot-focus":this.focusFlag&&0===this.focusSlider,"slider-dot-dragging":this.flag&&0===this.currentSlider,"slider-dot-disabled":!this.boolDisabled&&this.disabledArray[0]}],style:[this.dotStyles,!this.boolDisabled&&this.disabledArray[0]?this.disabledDotStyles[0]:null,this.sliderStyles[0],this.focusFlag&&0===this.focusSlider?this.focusStyles[0]:null]},[r("div",{ref:"tooltip0",staticClass:"slider-tooltip-wrap",class:"slider-tooltip-".concat(this.tooltipDirection[0])},[this._t("tooltip",[r("span",{staticClass:"slider-tooltip",style:this.tooltipStyles[0]},this.formatter?this.formatting(this.val[0]):this.val[0])],{value:this.val[0],index:0,disabled:!this.boolDisabled&&this.disabledArray[0]})])]);t.push(i);var e=r("div",{ref:"dot1",staticClass:"slider-dot",class:[this.tooltipStatus,{"slider-dot-focus":this.focusFlag&&1===this.focusSlider,"slider-dot-dragging":this.flag&&1===this.currentSlider,"slider-dot-disabled":!this.boolDisabled&&this.disabledArray[1]}],style:[this.dotStyles,!this.boolDisabled&&this.disabledArray[1]?this.disabledDotStyles[1]:null,this.sliderStyles[1],this.focusFlag&&1===this.focusSlider?this.focusStyles[1]:null]},[r("div",{ref:"tooltip1",staticClass:"slider-tooltip-wrap",class:"slider-tooltip-".concat(this.tooltipDirection[1])},[this._t("tooltip",[r("span",{staticClass:"slider-tooltip",style:this.tooltipStyles[1]},this.formatter?this.formatting(this.val[1]):this.val[1])],{value:this.val[1],index:1,disabled:!this.boolDisabled&&this.disabledArray[1]})])]);t.push(e)}else{var s=r("div",{ref:"dot",staticClass:"slider-dot",class:[this.tooltipStatus,{"slider-dot-focus":this.focusFlag&&0===this.focusSlider,"slider-dot-dragging":this.flag&&0===this.currentSlider}],style:[this.dotStyles,this.sliderStyles,this.focusFlag&&0===this.focusSlider?this.focusStyles:null]},[r("div",{staticClass:"slider-tooltip-wrap",class:"slider-tooltip-".concat(this.tooltipDirection)},[this._t("tooltip",[r("span",{staticClass:"slider-tooltip",style:this.tooltipStyles},this.formatter?this.formatting(this.val):this.val)],{value:this.val})])]);t.push(s)}var a=this.piecewiseDotWrap.length,o=r("ul",{staticClass:"slider-piecewise"},this._l(this.piecewiseDotWrap,function(t,i){var e=[];n.piecewise&&e.push(r("span",{staticClass:"piecewise-dot",style:[n.piecewiseStyle,t.inRange?n.piecewiseActiveStyle:null]}));var s=[];return n.piecewiseLabel&&s.push(r("span",{staticClass:"piecewise-label",style:[n.labelStyle,t.inRange?n.labelActiveStyle:null]},t.label)),r("li",{key:i,staticClass:"piecewise-item",style:[n.piecewiseDotStyle,t.style]},[n._t("piecewise",e,{label:t.label,index:i,first:0===i,last:i===a-1,active:t.inRange}),n._t("label",s,{label:t.label,index:i,first:0===i,last:i===a-1,active:t.inRange})])}));t.push(o);var h=r("div",{ref:"process",staticClass:"slider-process",class:{"slider-process-draggable":this.isRange&&this.processDraggable},style:this.processStyle,on:{click:function(t){return n.processClick(t)}}},[r("div",{ref:"mergedTooltip",staticClass:"merged-tooltip slider-tooltip-wrap",class:"slider-tooltip-".concat(this.isRange?this.tooltipDirection[0]:this.tooltipDirection),style:this.tooltipMergedPosition},[this._t("tooltip",[r("span",{staticClass:"slider-tooltip",style:this.tooltipStyles},this.mergeFormatter?this.mergeFormatting(this.val[0],this.val[1]):this.formatter?this.val[0]===this.val[1]?this.formatting(this.val[0]):"".concat(this.formatting(this.val[0])," - ").concat(this.formatting(this.val[1])):this.val[0]===this.val[1]?this.val[0]:"".concat(this.val[0]," - ").concat(this.val[1]))],{value:this.val,merge:!0})])]);return t.push(h),this.isRange||this.data||t.push(r("input",{staticClass:"slider-input",attrs:{type:"range",min:this.min,max:this.max},domProps:{value:this.val},on:{input:function(t){return n.val=t.target.value}}})),r("div",{ref:"wrap",staticClass:"vue-range-slider slider-component",class:[this.flowDirection,this.disabledClass,this.stateClass,{"slider-has-label":this.piecewiseLabel}],style:[this.wrapStyles,this.boolDisabled?this.disabledStyle:null],directives:[{name:"show",value:this.show}],on:{click:function(t){return n.wrapClick(t)}}},[r("div",{ref:"elem",staticClass:"slider",style:[this.elemStyles,this.bgStyle],attrs:{"aria-hidden":!0}},t)])},computed:{val:{get:function(){return this.data?this.isRange?[this.data[this.currentValue[0]],this.data[this.currentValue[1]]]:this.data[this.currentValue]:this.currentValue},set:function(t){if(this.data)if(this.isRange){var i=this.data.indexOf(t[0]),e=this.data.indexOf(t[1]);-1<i&&-1<e&&(this.currentValue=[i,e])}else{var s=this.data.indexOf(t);-1<s&&(this.currentValue=s)}else this.currentValue=t}},currentIndex:function(){return this.isRange?this.data?this.currentValue:[this.getIndexByValue(this.currentValue[0]),this.getIndexByValue(this.currentValue[1])]:this.getIndexByValue(this.currentValue)},tooltipMergedPosition:function(){if(!this.isMounted)return{};var t=this.tooltipDirection[0];if(this.$refs.dot0){var i={};return"vertical"===this.direction?i[t]="-".concat(this.dotHeightVal/2-this.width/2+9,"px"):(i[t]="-".concat(this.dotWidthVal/2-this.height/2+9,"px"),i.left="50%"),i}},tooltipDirection:function(){var t=this.tooltipDir||("vertical"===this.direction?"left":"top");return s(t)?this.isRange?t:t[1]:this.isRange?[t,t]:t},piecewiseDotWrap:function(){if(!this.piecewise&&!this.piecewiseLabel)return!1;for(var t=[],i=0;i<=this.total;i++){var e="vertical"===this.direction?{bottom:"".concat(this.gap*i-this.width/2,"px"),left:0}:{left:"".concat(this.gap*i-this.height/2,"px"),top:0},s=this.reverse?this.total-i:i,r=this.data?this.data[s]:this.spacing*s+this.min;t.push({style:e,label:this.formatter?this.formatting(r):r,inRange:s>=this.indexRange[0]&&s<=this.indexRange[1]})}return t},total:function(){return this.data?this.data.length-1:(Math.floor((this.maximum-this.minimum)*this.multiple)%(this.step*this.multiple)!=0&&this.printError("Prop[step] is illegal, Please make sure that the step can be divisible"),(this.maximum-this.minimum)/this.step)},piecewiseDotStyle:function(){return"vertical"===this.direction?{width:"".concat(this.width,"px"),height:"".concat(this.width,"px")}:{width:"".concat(this.height,"px"),height:"".concat(this.height,"px")}},dotStyles:function(){return"vertical"===this.direction?{width:"".concat(this.dotWidthVal,"px"),height:"".concat(this.dotHeightVal,"px"),left:"".concat(-(this.dotWidthVal-this.width)/2,"px")}:{width:"".concat(this.dotWidthVal,"px"),height:"".concat(this.dotHeightVal,"px"),top:"".concat(-(this.dotHeightVal-this.height)/2,"px")}},sliderStyles:function(){return s(this.sliderStyle)?this.isRange?this.sliderStyle:this.sliderStyle[1]:"function"==typeof this.sliderStyle?this.sliderStyle(this.val,this.currentIndex):this.isRange?[this.sliderStyle,this.sliderStyle]:this.sliderStyle},tooltipStyles:function(){return s(this.tooltipStyle)?this.isRange?this.tooltipStyle:this.tooltipStyle[1]:"function"==typeof this.tooltipStyle?this.tooltipStyle(this.val,this.currentIndex):this.isRange?[this.tooltipStyle,this.tooltipStyle]:this.tooltipStyle},focusStyles:function(){return s(this.focusStyle)?this.isRange?this.focusStyle:this.focusStyle[1]:"function"==typeof this.focusStyle?this.focusStyle(this.val,this.currentIndex):this.isRange?[this.focusStyle,this.focusStyle]:this.focusStyle},elemStyles:function(){return"vertical"===this.direction?{width:"".concat(this.width,"px"),height:"100%"}:{height:"".concat(this.height,"px")}},wrapStyles:function(){return"vertical"===this.direction?{height:"number"==typeof this.height?"".concat(this.height,"px"):this.height,padding:"".concat(this.dotHeightVal/2,"px ").concat(this.dotWidthVal/2,"px")}:{width:"number"==typeof this.width?"".concat(this.width,"px"):this.width,padding:"".concat(this.dotHeightVal/2,"px ").concat(this.dotWidthVal/2,"px")}},stateClass:function(){return{"slider-state-process-drag":this.processFlag,"slider-state-drag":this.flag&&!this.processFlag&&!this.keydownFlag,"slider-state-focus":this.focusFlag}},tooltipStatus:function(){return"hover"===this.tooltip&&this.flag?"slider-always":this.tooltip?"slider-".concat(this.tooltip):""},tooltipClass:function(){return["slider-tooltip-".concat(this.tooltipDirection),"slider-tooltip"]},minimum:function(){return this.data?0:this.min},maximum:function(){return this.data?this.data.length-1:this.max},multiple:function(){var t="".concat(this.step).split(".")[1];return t?Math.pow(10,t.length):1},indexRange:function(){return this.isRange?this.currentIndex:[0,this.currentIndex]},spacing:function(){return this.data?1:this.step},gap:function(){return this.size/this.total},dotWidthVal:function(){return"number"==typeof this.dotWidth?this.dotWidth:this.dotSize},dotHeightVal:function(){return"number"==typeof this.dotHeight?this.dotHeight:this.dotSize},disabledArray:function(){return s(this.disabled)?this.disabled:[this.disabled,this.disabled]},boolDisabled:function(){return this.disabledArray.every(function(t){return!0===t})},disabledClass:function(){return this.boolDisabled?"slider-disabled":""},flowDirection:function(){return"slider-".concat(this.direction+(this.reverse?"-reverse":""))},isRange:function(){return s(this.value)},isDisabled:function(){return"none"===this.eventType||this.boolDisabled},isFixed:function(){return this.fixed||this.minRange},position:function(){return this.isRange?[(this.currentValue[0]-this.minimum)/this.spacing*this.gap,(this.currentValue[1]-this.minimum)/this.spacing*this.gap]:(this.currentValue-this.minimum)/this.spacing*this.gap},limit:function(){return this.isRange?this.isFixed?[[0,(this.total-this.fixedValue)*this.gap],[this.fixedValue*this.gap,this.size]]:[[0,this.position[1]],[this.position[0],this.size]]:[0,this.size]},valueLimit:function(){return this.isRange?this.isFixed?[[this.minimum,this.maximum-this.fixedValue*(this.spacing*this.multiple)/this.multiple],[this.minimum+this.fixedValue*(this.spacing*this.multiple)/this.multiple,this.maximum]]:[[this.minimum,this.currentValue[1]],[this.currentValue[0],this.maximum]]:[this.minimum,this.maximum]},idleSlider:function(){return 0===this.currentSlider?1:0},slider:function(){return this.isRange?[this.$refs.dot0,this.$refs.dot1]:this.$refs.dot}},methods:{setValue:function(t,i,e){var s=this;if(n(this.val,t)){var r=this.limitValue(t);this.val=this.isRange?r.concat():r,this.computedFixedValue(),this.syncValue(i)}this.$nextTick(function(){return s.setPosition(e)})},setIndex:function(t){var i;s(t)&&this.isRange?(i=this.data?[this.data[t[0]],this.data[t[1]]]:[this.getValueByIndex(t[0]),this.getValueByIndex(t[1])],this.setValue(i)):(t=this.getValueByIndex(t),this.isRange&&(this.currentSlider=t>(this.currentValue[1]-this.currentValue[0])/2+this.currentValue[0]?1:0),this.setCurrentValue(t))},wrapClick:function(t){if(this.isDisabled||!this.clickable||this.processFlag||this.dragFlag)return!1;var i=this.getPos(t);if(this.isRange)if(this.disabledArray.every(function(t){return!1===t}))this.currentSlider=i>(this.position[1]-this.position[0])/2+this.position[0]?1:0;else if(this.disabledArray[0]){if(i<this.position[0])return!1;this.currentSlider=1}else if(this.disabledArray[1]){if(i>this.position[1])return!1;this.currentSlider=0}if(this.disabledArray[this.currentSlider])return!1;if(this.setValueOnPos(i),this.isRange&&this.tooltipMerge){var e=setInterval(this.handleOverlapTooltip,16.7);setTimeout(function(){return window.clearInterval(e)},1e3*this.speed)}},processClick:function(t){this.fixed&&t.stopPropagation()},syncValue:function(t){var i=this.isRange?this.val.concat():this.val;this.$emit("input",i),this.keydownFlag&&this.$emit("on-keypress",i),t||this.$emit("slide-end",i)},getPos:function(t){return this.realTime&&this.getStaticData(),"vertical"===this.direction?this.reverse?t.pageY-this.offset:this.size-(t.pageY-this.offset):this.reverse?this.size-(t.pageX-this.offset):t.pageX-this.offset},setValueOnPos:function(t,i){var e=this.isRange?this.limit[this.currentSlider]:this.limit,s=this.isRange?this.valueLimit[this.currentSlider]:this.valueLimit,r=Math.round(t/this.gap);if(t>=e[0]&&t<=e[1]){var n=this.getValueByIndex(r);this.setTransform(t),this.setCurrentValue(n,i),this.isRange&&(this.fixed||this.isLessRange(t,r))&&(this.setTransform(t+this.fixedValue*this.gap*(0===this.currentSlider?1:-1),!0),this.setCurrentValue((n*this.multiple+this.fixedValue*this.spacing*this.multiple*(0===this.currentSlider?1:-1))/this.multiple,i,!0))}else{var a=t<e[0]?0:1,o=0===a?1:0;this.setTransform(e[a]),this.setCurrentValue(s[a]),this.isRange&&(this.fixed||this.isLessRange(t,r))?(this.setTransform(this.limit[this.idleSlider][a],!0),this.setCurrentValue(this.valueLimit[this.idleSlider][a],i,!0)):!this.isRange||!this.enableCross&&!this.crossFlag||this.isFixed||this.disabledArray[a]||this.currentSlider!==o||(this.focusSlider=a,this.currentSlider=a)}this.crossFlag=!1},setCurrentValue:function(t,i,e){var s=e?this.idleSlider:this.currentSlider;if(t<this.minimum||t>this.maximum)return!1;this.isRange?n(this.currentValue[s],t)&&(this.currentValue.splice(s,1,t),this.lazy&&this.flag||this.syncValue()):n(this.currentValue,t)&&(this.currentValue=t,this.lazy&&this.flag||this.syncValue()),i||this.setPosition()},setPosition:function(t){this.flag||this.setTransitionTime(void 0===t?this.speed:t),this.isRange?(this.setTransform(this.position[0],1===this.currentSlider),this.setTransform(this.position[1],0===this.currentSlider)):this.setTransform(this.position),this.flag||this.setTransitionTime(0)},setTransform:function(t,i){var e=i?this.idleSlider:this.currentSlider,s=o(("vertical"===this.direction?this.dotHeightVal/2-t:t-this.dotWidthVal/2)*(this.reverse?-1:1)),r="vertical"===this.direction?"translateY(".concat(s,"px)"):"translateX(".concat(s,"px)"),n=this.fixed?"".concat(this.fixedValue*this.gap,"px"):"".concat(0===e?this.position[1]-t:t-this.position[0],"px"),a=this.fixed?"".concat(0===e?t:t-this.fixedValue*this.gap,"px"):"".concat(0===e?t:this.position[0],"px");this.isRange?(this.slider[e].style[u]=r,"vertical"===this.direction?(this.$refs.process.style.height=n,this.$refs.process.style[this.reverse?"top":"bottom"]=a):(this.$refs.process.style.width=n,this.$refs.process.style[this.reverse?"right":"left"]=a)):(this.slider.style[u]=r,"vertical"===this.direction?(this.$refs.process.style.height="".concat(t,"px"),this.$refs.process.style[this.reverse?"top":"bottom"]=0):(this.$refs.process.style.width="".concat(t,"px"),this.$refs.process.style[this.reverse?"right":"left"]=0))},setTransitionTime:function(t){if(t||this.$refs.process.offsetWidth,this.isRange){for(var i=this.slider.length,e=0;e<i;e++)this.slider[e].style[d]="".concat(t,"s");this.$refs.process.style[d]="".concat(t,"s")}else this.slider.style[d]="".concat(t,"s"),this.$refs.process.style[d]="".concat(t,"s")},computedFixedValue:function(){if(!this.isFixed)return this.fixedValue=0,!1;this.fixedValue=this.currentIndex[1]-this.currentIndex[0],this.fixedValue=Math.max(this.fixed?this.currentIndex[1]-this.currentIndex[0]:0,this.minRange||0)},limitValue:function(i){var e=this;if(this.data)return i;var s=function(t){return t<e.min?(e.printError("The value of the slider is ".concat(i,", the minimum value is ").concat(e.min,", the value of this slider can not be less than the minimum value")),e.min):t>e.max?(e.printError("The value of the slider is ".concat(i,", the maximum value is ").concat(e.max,", the value of this slider can not be greater than the maximum value")),e.max):t};return this.isRange?i.map(function(t){return s(t)}):s(i)},getStaticData:function(){this.$refs.elem&&(this.size="vertical"===this.direction?this.$refs.elem.offsetHeight:this.$refs.elem.offsetWidth,this.offset="vertical"===this.direction?this.$refs.elem.getBoundingClientRect().top+window.pageYOffset||document.documentElement.scrollTop:this.$refs.elem.getBoundingClientRect().left)},handleOverlapTooltip:function(){var t=this.tooltipDirection[0]===this.tooltipDirection[1];if(this.isRange&&t){var i=this.reverse?this.$refs.tooltip1:this.$refs.tooltip0,e=this.reverse?this.$refs.tooltip0:this.$refs.tooltip1,s=i.getBoundingClientRect(),r=e.getBoundingClientRect(),n=s.right,a=r.left,o=s.top,h=r.top+r.height,l="horizontal"===this.direction&&a<n,u="vertical"===this.direction&&o<h;l||u?this.handleDisplayMergedTooltip(!0):this.handleDisplayMergedTooltip(!1)}},handleDisplayMergedTooltip:function(t){var i=this.$refs.tooltip0,e=this.$refs.tooltip1,s=this.$refs.process.getElementsByClassName("merged-tooltip")[0];s.style.visibility=t?(i.style.visibility="hidden",e.style.visibility="hidden","visible"):(i.style.visibility="visible",e.style.visibility="visible","hidden")},isLessRange:function(t,i){if(!this.isRange||!this.minRange&&!this.maxRange)return!1;var e=0===this.currentSlider?this.currentIndex[1]-i:i-this.currentIndex[0];return this.minRange&&e<=this.minRange?(this.fixedValue=this.minRange,!0):this.maxRange&&e>=this.maxRange?(this.fixedValue=this.maxRange,!0):(this.computedFixedValue(),!1)},getValueByIndex:function(t){return(this.spacing*this.multiple*t+this.minimum*this.multiple)/this.multiple},getIndexByValue:function(t){return Math.round((t-this.minimum)*this.multiple)/(this.spacing*this.multiple)},formatting:function(t){return"string"==typeof this.formatter?this.formatter.replace(/{value}/,t):this.formatter(t)},mergeFormatting:function(e,s){return"string"==typeof this.mergeFormatter?this.mergeFormatter.replace(/{(value1|value2)}/g,function(t,i){return"value1"===i?e:s}):this.mergeFormatter(e,s)},_start:function(t){var i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0,e=2<arguments.length?arguments[2]:void 0;if(this.disabledArray[i])return!1;if(this.stopPropagation&&t.stopPropagation(),this.isRange){if(this.currentSlider=i,e){if(!this.processDraggable)return!1;this.processFlag=!0,this.processSign={pos:this.position,start:this.getPos(t.targetTouches&&t.targetTouches[0]?t.targetTouches[0]:t)}}this.enableCross||this.val[0]!==this.val[1]||(this.crossFlag=!0)}!e&&this.useKeyboard&&(this.focusFlag=!0,this.focusSlider=i),this.flag=!0,this.$emit("drag-start",this)},_move:function(t){if(t.preventDefault(),this.stopPropagation&&t.stopPropagation(),!this.flag)return!1;t.targetTouches&&t.targetTouches[0]&&(t=t.targetTouches[0]),this.processFlag?(this.currentSlider=0,this.setValueOnPos(this.processSign.pos[0]+this.getPos(t)-this.processSign.start,!0),this.currentSlider=1,this.setValueOnPos(this.processSign.pos[1]+this.getPos(t)-this.processSign.start,!0)):(this.dragFlag=!0,this.setValueOnPos(this.getPos(t),!0)),this.isRange&&this.tooltipMerge&&this.handleOverlapTooltip()},_end:function(t){var i=this;if(this.stopPropagation&&t.stopPropagation(),!this.flag)return!1;this.$emit("drag-end",this),this.lazy&&n(this.val,this.value)&&this.syncValue(),this.flag=!1,this.$nextTick(function(){i.crossFlag=!1,i.dragFlag=!1,i.processFlag=!1}),this.setPosition()},blurSlider:function(t){var i=this.isRange?this.$refs["dot".concat(this.focusSlider)]:this.$refs.dot;if(!i||i===t.target)return!1;this.focusFlag=!1},handleKeydown:function(t){if(t.preventDefault(),t.stopPropagation(),!this.useKeyboard)return!1;switch(t.which||t.keyCode){case 37:case 40:this.keydownFlag=!0,this.flag=!0,this.changeFocusSlider(this.actionsKeyboard[0]);break;case 38:case 39:this.keydownFlag=!0,this.flag=!0,this.changeFocusSlider(this.actionsKeyboard[1])}},handleKeyup:function(){this.keydownFlag&&(this.keydownFlag=!1,this.flag=!1)},changeFocusSlider:function(r){var n=this;if(this.isRange){var t=this.currentIndex.map(function(t,i){if(i===n.focusSlider||n.fixed){var e=r(t),s=n.fixed?n.valueLimit[i]:[0,n.total];if(e<=s[1]&&e>=s[0])return e}return t});t[0]>t[1]&&(this.focusSlider=0===this.focusSlider?1:0,t=t.reverse()),this.setIndex(t)}else this.setIndex(r(this.currentIndex))},bindEvents:function(){var i=this;this.processStartFn=function(t){i._start(t,0,!0)},this.dot0StartFn=function(t){i._start(t,0)},this.dot1StartFn=function(t){i._start(t,1)},t?(h(this.$refs.process,f,this.processStartFn),h(document,p,this._move),h(document,g,this._end),h(document,m,this._end),this.isRange?(h(this.$refs.dot0,f,this.dot0StartFn),h(this.$refs.dot1,f,this.dot1StartFn)):h(this.$refs.dot,f,this._start)):(h(this.$refs.process,y,this.processStartFn),h(document,y,this.blurSlider),h(document,v,this._move),h(document,S,this._end),h(document,b,this._end),this.isRange?(h(this.$refs.dot0,y,this.dot0StartFn),h(this.$refs.dot1,y,this.dot1StartFn)):h(this.$refs.dot,y,this._start)),h(document,x,this.handleKeydown),h(document,w,this.handleKeyup),h(window,V,this.refresh),this.isRange&&this.tooltipMerge&&(h(this.$refs.dot0,c,this.handleOverlapTooltip),h(this.$refs.dot1,c,this.handleOverlapTooltip))},unbindEvents:function(){t?(l(this.$refs.process,f,this.processStartFn),l(document,p,this._move),l(document,g,this._end),l(document,m,this._end),this.isRange?(l(this.$refs.dot0,f,this.dot0StartFn),l(this.$refs.dot1,f,this.dot1StartFn)):l(this.$refs.dot,f,this._start)):(l(this.$refs.process,y,this.processStartFn),l(document,y,this.blurSlider),l(document,v,this._move),l(document,S,this._end),l(document,b,this._end),this.isRange?(l(this.$refs.dot0,y,this.dot0StartFn),l(this.$refs.dot1,y,this.dot1StartFn)):l(this.$refs.dot,y,this._start)),l(document,x,this.handleKeydown),l(document,w,this.handleKeyup),l(window,V,this.refresh),this.isRange&&this.tooltipMerge&&(l(this.$refs.dot0,c,this.handleOverlapTooltip),l(this.$refs.dot1,c,this.handleOverlapTooltip))},refresh:function(){this.$refs.elem&&(this.getStaticData(),this.computedFixedValue(),this.setPosition(),this.unbindEvents(),this.bindEvents())},printError:function(t){this.debug}},mounted:function(){var t=this;if(this.isComponentExists=!0,"undefined"==typeof window||"undefined"==typeof document)return this.printError("window or document is undefined, can not be initialization.");this.$nextTick(function(){t.getStaticData(),t.setValue(t.limitValue(t.value),!0,t.startAnimation?t.speed:0),t.bindEvents(),t.isRange&&t.tooltipMerge&&!t.startAnimation&&t.handleOverlapTooltip()}),this.isMounted=!0},watch:{value:function(t){this.flag||this.setValue(t,!0)},show:function(t){t&&!this.size&&this.$nextTick(this.refresh)},max:function(t){if(t<this.min)return this.printError("The maximum value can not be less than the minimum value.");var i=this.limitValue(this.val);this.setValue(i),this.refresh()},min:function(t){if(t>this.max)return this.printError("The minimum value can not be greater than the maximum value.");var i=this.limitValue(this.val);this.setValue(i),this.refresh()},fixed:function(){this.computedFixedValue()},minRange:function(){this.computedFixedValue()}},beforeDestroy:function(){this.isComponentExists=!1,this.unbindEvents(),this.refresh()},deactivated:function(){this.isComponentExists=!1,this.unbindEvents(),this.refresh()}};return R.version="1.0.3",R.install=function(t){t.component(R.name,R)},"undefined"!=typeof window&&window.Vue&&window.Vue.use(R),R});