(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);throw new Error("Cannot find module '"+o+"'")}var f=n[o]={exports:{}};t[o][0].call(f.exports,function(e){var n=t[o][1][e];return s(n?n:e)},f,f.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
"use strict";

Vue.component('wpcfto_group_title', {
  props: ['fields', 'field_label', 'field_name', 'field_id', 'field_icon', 'field_preview_position', 'field_button'],
  data: function data() {
    return {
      fields: {}
    };
  },
  methods: {
    handleButtonClick: function handleButtonClick() {
      if (this.field_button && this.field_button.link) {
        window.open(this.field_button.link, '_blank');
      }
    },
    handleTooltipHover: function handleTooltipHover() {
      var tooltip = document.querySelector('.wpcfto-group-title-button-tooltip-content');
      var settings = document.querySelector('.stm_metaboxes_grid');

      if (tooltip && settings) {
        settings.classList.add('stm_metaboxes_grid--tooltip-hover');
        tooltip.classList.add('show-tooltip');
      }
    },
    handleTooltipLeave: function handleTooltipLeave() {
      var tooltip = document.querySelector('.wpcfto-group-title-button-tooltip-content');
      var settings = document.querySelector('.stm_metaboxes_grid');

      if (tooltip && settings) {
        settings.classList.remove('stm_metaboxes_grid--tooltip-hover');
        tooltip.classList.remove('show-tooltip');
      }
    },
    handleScroll: function handleScroll() {
      this.handleTooltipLeave();
    }
  },
  mounted: function mounted() {
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy: function beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },
  template: "\n    <div \n      class=\"wpcfto_generic_field wpcfto_generic_field__group_title\" \n      :class=\"field_preview_position\"\n    >\n      <i :class=\"field_icon\"></i>\n      <div class=\"wpcfto-group-title-wrapper\">\n        <wpcfto_fields_aside_before :fields=\"fields\" :field_label=\"field_label\"></wpcfto_fields_aside_before>\n        <div \n          v-if=\"field_button\" \n          class=\"wpcfto-group-title-button-container\"  \n        >\n          <a \n            :href=\"field_button.link\" \n            target=\"_blank\" \n            class=\"wpcfto-group-title-button\" \n            :class=\"{'tooltip-exists': field_button.tooltip && field_button.tooltip.trim() !== ''}\" \n            @click.prevent=\"handleButtonClick\" \n            :title=\"field_button.tooltip\"\n            style=\"position: relative;\"\n            @mouseover=\"handleTooltipHover\"\n            @mouseleave=\"handleTooltipLeave\"\n          >\n            <i :class=\"field_button.icon\"></i> {{ field_button.text }}\n            <span\n              v-if=\"field_button.tooltip\" \n              class=\"wpcfto-group-title-button-tooltip-content\" \n              v-html=\"field_button.tooltip\"\n            ></span>\n          </a>\n        </div>\n      </div>\n    </div>\n  "
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
},{}]},{},[1])