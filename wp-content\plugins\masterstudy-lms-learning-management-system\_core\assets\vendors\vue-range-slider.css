.range-slider {
  display: inline-block;
  padding: 0 10px;
  height: 20px;
  width: 130px;
}

.range-slider.disabled {
  opacity: 0.5;
}

.range-slider-inner {
  display: inline-block;
  position: relative;
  height: 100%;
  width: 100%;
}

.range-slider-rail,
.range-slider-fill {
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  height: 4px;
  border-radius: 2px;
  -webkit-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
          transform: translateY(-50%);
}

.range-slider-rail {
  width: 100%;
  background-color: #e2e2e2;
}

.range-slider-fill {
  background-color: #21fb92;
}

.range-slider-knob {
  display: block;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  height: 20px;
  width: 20px;
  border: 1px solid #f5f5f5;
  border-radius: 50%;
  background-color: #fff;
  -webkit-box-shadow: 1px 1px rgba(0, 0, 0, 0.2);
          box-shadow: 1px 1px rgba(0, 0, 0, 0.2);
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  cursor: pointer;
}

.range-slider-hidden {
  display: none;
}
