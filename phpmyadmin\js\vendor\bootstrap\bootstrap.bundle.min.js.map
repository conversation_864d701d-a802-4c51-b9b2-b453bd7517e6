{"version": 3, "names": ["TRANSITION_END", "getSelector", "element", "selector", "getAttribute", "hrefAttribute", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "replace", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "keys", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "key", "value", "entries", "_unused", "defineProperty", "configurable", "get", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "normalizeData", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "match", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLID", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "_isAnimated", "SELECTOR_ACTIVE", "clearInterval", "carousel", "slideIndex", "carousels", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "selectorElements", "top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "for<PERSON>ach", "styles", "assign", "effect", "_ref2", "initialStyles", "position", "options", "strategy", "margin", "arrow", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "getUAString", "uaData", "userAgentData", "brands", "item", "brand", "version", "userAgent", "isLayoutViewport", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "height", "visualViewport", "addVisualOffsets", "x", "offsetLeft", "y", "offsetTop", "getLayoutRect", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "len", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "_Object$assign", "commonStyles", "_ref4", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "computeStyles$1", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "_element$ownerDocumen", "isBody", "updatedList", "rectToClientRect", "rect", "getClientRectFromMixedType", "clippingParent", "html", "layoutViewport", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "v", "withinMaxClamp", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "isElementScaled", "modifiers", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "display", "popperConfig", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "sel", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "EVENT_HIDDEN", "EVENT_SHOW", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "initialOverflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "EVENT_HIDE_PREVENTED", "<PERSON><PERSON><PERSON>", "blur", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "allowedAttributeList", "attributeName", "nodeValue", "attributeRegex", "regex", "DefaultAllowlist", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "allowList", "content", "extraClass", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "click", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "scrollTo", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "id", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "activeNodes", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "SELECTOR_INNER_ELEM", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"], "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/utils/userAgent.js", "../../node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shout-out Angus <PERSON> (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(object)\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getElementFromSelector,\n  getjQuery,\n  getNextActiveElement,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // todo: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const handlerKey of Object.keys(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const keyHandlers of Object.keys(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    let evt = new Event(event, { bubbles, cancelable: true })\n    evt = hydrateObj(evt, args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta) {\n  for (const [key, value] of Object.entries(meta || {})) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v5.2.3): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isElement, toType } from './index'\nimport Manipulator from '../dom/manipulator'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const property of Object.keys(configTypes)) {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport { executeAfterTransition, getElement } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Config from './util/config'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.2.3'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\n/**\n * Constants\n */\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config'\nimport EventHandler from '../dom/event-handler'\nimport { execute } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport Swipe from './util/swipe'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getSelectorFromElement,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  for (const element of selectorElements) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.2/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // todo: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.2/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow } from './index'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, isRTL, isVisible, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer'\nimport { getElement, isElement } from '../util/index'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg(this) : arg\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index'\nimport { DefaultAllowlist } from './util/sanitizer'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport TemplateFactory from './util/template-factory'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 0],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // todo v6 remove this OR make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg.call(this._element) : arg\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, getNextActiveElement, isDisabled } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.3): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;0OAOA,MAEMA,EAAiB,gBAuBjBC,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAgBH,EAAQE,aAAa,QAMzC,IAAKC,IAAmBA,EAAcC,SAAS,OAASD,EAAcE,WAAW,KAC/E,OAAO,KAILF,EAAcC,SAAS,OAASD,EAAcE,WAAW,OAC3DF,EAAiB,IAAGA,EAAcG,MAAM,KAAK,MAG/CL,EAAWE,GAAmC,MAAlBA,EAAwBA,EAAcI,OAAS,IAC5E,CAED,OAAON,CAAP,EAGIO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKQ,SAASC,cAAcT,GAAYA,EAGrC,IAAP,EAGIU,EAAyBX,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWQ,SAASC,cAAcT,GAAY,IAArD,EA0BIW,EAAuBZ,IAC3BA,EAAQa,cAAc,IAAIC,MAAMhB,GAAhC,EAGIiB,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCX,SAASC,cAAcM,GAGzB,KAGHK,EAAYrB,IAChB,IAAKe,EAAUf,IAAgD,IAApCA,EAAQsB,iBAAiBF,OAClD,OAAO,EAGT,MAAMG,EAAgF,YAA7DC,iBAAiBxB,GAASyB,iBAAiB,cAE9DC,EAAgB1B,EAAQ2B,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkB1B,EAAS,CAC7B,MAAM4B,EAAU5B,EAAQ2B,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEV,CAED,OAAOL,CAAP,EAGIO,EAAa9B,IACZA,GAAWA,EAAQkB,WAAaa,KAAKC,gBAItChC,EAAQiC,UAAUC,SAAS,mBAIC,IAArBlC,EAAQmC,SACVnC,EAAQmC,SAGVnC,EAAQoC,aAAa,aAAoD,UAArCpC,EAAQE,aAAa,aAG5DmC,EAAiBrC,IACrB,IAAKS,SAAS6B,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBvC,EAAQwC,YAA4B,CAC7C,MAAMC,EAAOzC,EAAQwC,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC5C,CAED,OAAIzC,aAAmB0C,WACd1C,EAIJA,EAAQ6B,WAINQ,EAAerC,EAAQ6B,YAHrB,IAGT,EAGIc,EAAO,OAUPC,EAAS5C,IACbA,EAAQ6C,YAAR,EAGIC,EAAY,IACZC,OAAOC,SAAWvC,SAASwC,KAAKb,aAAa,qBACxCW,OAAOC,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQ,IAAuC,QAAjC1C,SAAS6B,gBAAgBc,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAEjB,GA/ByB,YAAxBpD,SAASuD,YAENd,EAA0B9B,QAC7BX,SAASwD,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,GACD,IAILL,EAA0BgB,KAAKX,IAE/BA,GAOF,EAgBIY,EAAUZ,IACU,mBAAbA,GACTA,GACD,EAGGa,EAAyB,CAACb,EAAUc,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQZ,GAIV,MACMgB,EA/LiCvE,KACvC,IAAKA,EACH,OAAO,EAIT,IAAIwE,mBAAEA,EAAFC,gBAAsBA,GAAoB1B,OAAOvB,iBAAiBxB,GAEtE,MAAM0E,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBlE,MAAM,KAAK,GACnDmE,EAAkBA,EAAgBnE,MAAM,KAAK,GAnFf,KAqFtBqE,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,CAOT,EA2KyBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,aACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBAAoBpF,EAAgBkF,GACtDb,EAAQZ,GAAR,EAGFc,EAAkBJ,iBAAiBnE,EAAgBkF,GACnDG,YAAW,KACJJ,GACHnE,EAAqByD,EACtB,GACAE,EAJH,EAgBIa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKjE,OACxB,IAAIsE,EAAQL,EAAKM,QAAQL,GAIzB,OAAe,IAAXI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAK,EAE1BC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,KAArD,EC1SIM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAazG,EAAS0G,GAC7B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiBnG,EAAQmG,UAAYA,GAChE,CAED,SAASQ,EAAiB3G,GACxB,MAAM0G,EAAMD,EAAazG,GAKzB,OAHAA,EAAQmG,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACtB,CAoCD,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAOC,OAAOC,OAAOJ,GAClBK,MAAKC,GAASA,EAAML,WAAaA,GAAYK,EAAMJ,qBAAuBA,GAC9E,CAED,SAASK,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAiC,iBAAZvC,EAErB8B,EAAWS,EAAcD,EAAsBtC,GAAWsC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKd,EAAamB,IAAIF,KACpBA,EAAYH,GAGP,CAACE,EAAaT,EAAUU,EAChC,CAED,SAASG,EAAW3H,EAASqH,EAAmBrC,EAASsC,EAAoBM,GAC3E,GAAiC,iBAAtBP,IAAmCrH,EAC5C,OAGF,IAAKuH,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAIzF,GAAID,KAAqBjB,EAAc,CACrC,MAAMyB,EAAejE,GACZ,SAAUuD,GACf,IAAKA,EAAMW,eAAkBX,EAAMW,gBAAkBX,EAAMY,iBAAmBZ,EAAMY,eAAe7F,SAASiF,EAAMW,eAChH,OAAOlE,EAAGoE,KAAKC,KAAMd,E,EAK3BL,EAAWe,EAAaf,EACzB,CAED,MAAMD,EAASF,EAAiB3G,GAC1BkI,EAAWrB,EAAOW,KAAeX,EAAOW,GAAa,IACrDW,EAAmBvB,EAAYsB,EAAUpB,EAAUS,EAAcvC,EAAU,MAEjF,GAAImD,EAGF,YAFAA,EAAiBP,OAASO,EAAiBP,QAAUA,GAKvD,MAAMlB,EAAMD,EAAaK,EAAUO,EAAkBe,QAAQrC,EAAgB,KACvEnC,EAAK2D,EAxEb,SAAoCvH,EAASC,EAAU2D,GACrD,OAAO,SAASoB,EAAQmC,GACtB,MAAMkB,EAAcrI,EAAQsI,iBAAiBrI,GAE7C,IAAK,IAAIgF,OAAEA,GAAWkC,EAAOlC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOpD,WACtE,IAAK,MAAM0G,KAAcF,EACvB,GAAIE,IAAetD,EAUnB,OANAuD,EAAWrB,EAAO,CAAEY,eAAgB9C,IAEhCD,EAAQ4C,QACVa,EAAaC,IAAI1I,EAASmH,EAAMwB,KAAM1I,EAAU2D,GAG3CA,EAAGgF,MAAM3D,EAAQ,CAACkC,G,CAIhC,CAqDG0B,CAA2B7I,EAASgF,EAAS8B,GArFjD,SAA0B9G,EAAS4D,GACjC,OAAO,SAASoB,EAAQmC,GAOtB,OANAqB,EAAWrB,EAAO,CAAEY,eAAgB/H,IAEhCgF,EAAQ4C,QACVa,EAAaC,IAAI1I,EAASmH,EAAMwB,KAAM/E,GAGjCA,EAAGgF,MAAM5I,EAAS,CAACmH,G,CAE7B,CA4EG2B,CAAiB9I,EAAS8G,GAE5BlD,EAAGmD,mBAAqBQ,EAAcvC,EAAU,KAChDpB,EAAGkD,SAAWA,EACdlD,EAAGgE,OAASA,EACZhE,EAAGuC,SAAWO,EACdwB,EAASxB,GAAO9C,EAEhB5D,EAAQiE,iBAAiBuD,EAAW5D,EAAI2D,EACzC,CAED,SAASwB,EAAc/I,EAAS6G,EAAQW,EAAWxC,EAAS+B,GAC1D,MAAMnD,EAAKgD,EAAYC,EAAOW,GAAYxC,EAAS+B,GAE9CnD,IAIL5D,EAAQkF,oBAAoBsC,EAAW5D,EAAIoF,QAAQjC,WAC5CF,EAAOW,GAAW5D,EAAGuC,UAC7B,CAED,SAAS8C,EAAyBjJ,EAAS6G,EAAQW,EAAW0B,GAC5D,MAAMC,EAAoBtC,EAAOW,IAAc,GAE/C,IAAK,MAAM4B,KAAcpC,OAAOqC,KAAKF,GACnC,GAAIC,EAAWhJ,SAAS8I,GAAY,CAClC,MAAM/B,EAAQgC,EAAkBC,GAChCL,EAAc/I,EAAS6G,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBACjE,CAEJ,CAED,SAASU,EAAaN,GAGpB,OADAA,EAAQA,EAAMiB,QAAQpC,EAAgB,IAC/BI,EAAae,IAAUA,CAC/B,CAED,MAAMsB,EAAe,CACnBa,GAAGtJ,EAASmH,EAAOnC,EAASsC,GAC1BK,EAAW3H,EAASmH,EAAOnC,EAASsC,GAAoB,E,EAG1DiC,IAAIvJ,EAASmH,EAAOnC,EAASsC,GAC3BK,EAAW3H,EAASmH,EAAOnC,EAASsC,GAAoB,E,EAG1DoB,IAAI1I,EAASqH,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmCrH,EAC5C,OAGF,MAAOuH,EAAaT,EAAUU,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GACrFkC,EAAchC,IAAcH,EAC5BR,EAASF,EAAiB3G,GAC1BmJ,EAAoBtC,EAAOW,IAAc,GACzCiC,EAAcpC,EAAkBhH,WAAW,KAEjD,QAAwB,IAAbyG,EAAX,CAUA,GAAI2C,EACF,IAAK,MAAMC,KAAgB1C,OAAOqC,KAAKxC,GACrCoC,EAAyBjJ,EAAS6G,EAAQ6C,EAAcrC,EAAkBsC,MAAM,IAIpF,IAAK,MAAMC,KAAe5C,OAAOqC,KAAKF,GAAoB,CACxD,MAAMC,EAAaQ,EAAYxB,QAAQnC,EAAe,IAEtD,IAAKuD,GAAenC,EAAkBjH,SAASgJ,GAAa,CAC1D,MAAMjC,EAAQgC,EAAkBS,GAChCb,EAAc/I,EAAS6G,EAAQW,EAAWL,EAAML,SAAUK,EAAMJ,mBACjE,CACF,CAfA,KARD,CAEE,IAAKC,OAAOqC,KAAKF,GAAmB/H,OAClC,OAGF2H,EAAc/I,EAAS6G,EAAQW,EAAWV,EAAUS,EAAcvC,EAAU,KAE7E,C,EAkBH6E,QAAQ7J,EAASmH,EAAO2C,GACtB,GAAqB,iBAAV3C,IAAuBnH,EAChC,OAAO,KAGT,MAAMwD,EAAIV,IAIV,IAAIiH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH/C,IADFM,EAAaN,IAQZ3D,IACjBuG,EAAcvG,EAAE1C,MAAMqG,EAAO2C,GAE7BtG,EAAExD,GAAS6J,QAAQE,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,IAAIC,EAAM,IAAIxJ,MAAMqG,EAAO,CAAE6C,UAASO,YAAY,IAelD,OAdAD,EAAM9B,EAAW8B,EAAKR,GAElBI,GACFI,EAAIE,iBAGFP,GACFjK,EAAQa,cAAcyJ,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACR,GAGH,SAAS9B,EAAWiC,EAAKC,GACvB,IAAK,MAAOC,EAAKC,KAAU5D,OAAO6D,QAAQH,GAAQ,IAChD,IACED,EAAIE,GAAOC,CAQZ,CAPC,MAAME,GACN9D,OAAO+D,eAAeN,EAAKE,EAAK,CAC9BK,cAAc,EACdC,IAAG,IACML,GAGZ,CAGH,OAAOH,CACR,CClTD,MAAMS,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAIrL,EAAS2K,EAAKW,GACXJ,EAAWxD,IAAI1H,IAClBkL,EAAWG,IAAIrL,EAAS,IAAImL,KAG9B,MAAMI,EAAcL,EAAWD,IAAIjL,GAI9BuL,EAAY7D,IAAIiD,IAA6B,IAArBY,EAAYC,KAMzCD,EAAYF,IAAIV,EAAKW,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAYlC,QAAQ,M,EAOhI4B,IAAG,CAACjL,EAAS2K,IACPO,EAAWxD,IAAI1H,IACVkL,EAAWD,IAAIjL,GAASiL,IAAIN,IAG9B,KAGTkB,OAAO7L,EAAS2K,GACd,IAAKO,EAAWxD,IAAI1H,GAClB,OAGF,MAAMuL,EAAcL,EAAWD,IAAIjL,GAEnCuL,EAAYO,OAAOnB,GAGM,IAArBY,EAAYC,MACdN,EAAWY,OAAO9L,EAErB,GC9CH,SAAS+L,EAAcnB,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAUjG,OAAOiG,GAAOoB,WAC1B,OAAOrH,OAAOiG,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOqB,KAAKC,MAAMC,mBAAmBvB,GAGtC,CAFC,MAAME,GACN,OAAOF,CACR,CACF,CAED,SAASwB,EAAiBzB,GACxB,OAAOA,EAAIvC,QAAQ,UAAUiE,GAAQ,IAAGA,EAAIC,iBAC7C,CAED,MAAMC,EAAc,CAClBC,iBAAiBxM,EAAS2K,EAAKC,GAC7B5K,EAAQyM,aAAc,WAAUL,EAAiBzB,KAAQC,E,EAG3D8B,oBAAoB1M,EAAS2K,GAC3B3K,EAAQ2M,gBAAiB,WAAUP,EAAiBzB,K,EAGtDiC,kBAAkB5M,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM6M,EAAa,GACbC,EAAS9F,OAAOqC,KAAKrJ,EAAQ+M,SAASC,QAAOrC,GAAOA,EAAItK,WAAW,QAAUsK,EAAItK,WAAW,cAElG,IAAK,MAAMsK,KAAOmC,EAAQ,CACxB,IAAIG,EAAUtC,EAAIvC,QAAQ,MAAO,IACjC6E,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQtD,MAAM,EAAGsD,EAAQ7L,QACrEyL,EAAWI,GAAWlB,EAAc/L,EAAQ+M,QAAQpC,GACrD,CAED,OAAOkC,C,EAGTM,iBAAgB,CAACnN,EAAS2K,IACjBoB,EAAc/L,EAAQE,aAAc,WAAUkM,EAAiBzB,QCpD1E,MAAMyC,EAEOC,qBACT,MAAO,EACR,CAEUC,yBACT,MAAO,EACR,CAEU5J,kBACT,MAAM,IAAI6J,MAAM,sEACjB,CAEDC,WAAWC,GAIT,OAHAA,EAASxF,KAAKyF,gBAAgBD,GAC9BA,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACR,CAEDE,kBAAkBF,GAChB,OAAOA,CACR,CAEDC,gBAAgBD,EAAQzN,GACtB,MAAM6N,EAAa9M,EAAUf,GAAWuM,EAAYY,iBAAiBnN,EAAS,UAAY,GAE1F,MAAO,IACFiI,KAAK6F,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9C9M,EAAUf,GAAWuM,EAAYK,kBAAkB5M,GAAW,MAC5C,iBAAXyN,EAAsBA,EAAS,GAE7C,CAEDG,iBAAiBH,EAAQM,EAAc9F,KAAK6F,YAAYR,aACtD,IAAK,MAAMU,KAAYhH,OAAOqC,KAAK0E,GAAc,CAC/C,MAAME,EAAgBF,EAAYC,GAC5BpD,EAAQ6C,EAAOO,GACfE,EAAYnN,EAAU6J,GAAS,UJzCrC5J,OADSA,EI0C+C4J,GJxClD,GAAE5J,IAGLgG,OAAOmH,UAAUnC,SAAShE,KAAKhH,GAAQoN,MAAM,eAAe,GAAG9B,cIuClE,IAAK,IAAI+B,OAAOJ,GAAeK,KAAKJ,GAClC,MAAM,IAAIK,UACP,GAAEtG,KAAK6F,YAAYpK,KAAK8K,0BAA0BR,qBAA4BE,yBAAiCD,MAGrH,CJjDUjN,KIkDZ,ECxCH,MAAMyN,UAAsBrB,EAC1BU,YAAY9N,EAASyN,GACnBiB,SAEA1O,EAAUmB,EAAWnB,MAKrBiI,KAAK0G,SAAW3O,EAChBiI,KAAK2G,QAAU3G,KAAKuF,WAAWC,GAE/BrC,EAAKC,IAAIpD,KAAK0G,SAAU1G,KAAK6F,YAAYe,SAAU5G,MACpD,CAGD6G,UACE1D,EAAKS,OAAO5D,KAAK0G,SAAU1G,KAAK6F,YAAYe,UAC5CpG,EAAaC,IAAIT,KAAK0G,SAAU1G,KAAK6F,YAAYiB,WAEjD,IAAK,MAAMC,KAAgBhI,OAAOiI,oBAAoBhH,MACpDA,KAAK+G,GAAgB,IAExB,CAEDE,eAAe3L,EAAUvD,EAASmP,GAAa,GAC7C/K,EAAuBb,EAAUvD,EAASmP,EAC3C,CAED3B,WAAWC,GAIT,OAHAA,EAASxF,KAAKyF,gBAAgBD,EAAQxF,KAAK0G,UAC3ClB,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACR,CAGiB2B,mBAACpP,GACjB,OAAOoL,EAAKH,IAAI9J,EAAWnB,GAAUiI,KAAK4G,SAC3C,CAEyBO,2BAACpP,EAASyN,EAAS,IAC3C,OAAOxF,KAAKoH,YAAYrP,IAAY,IAAIiI,KAAKjI,EAA2B,iBAAXyN,EAAsBA,EAAS,KAC7F,CAEU6B,qBACT,MApDY,OAqDb,CAEUT,sBACT,MAAQ,MAAK5G,KAAKvE,MACnB,CAEUqL,uBACT,MAAQ,IAAG9G,KAAK4G,UACjB,CAEeO,iBAAC3L,GACf,MAAQ,GAAEA,IAAOwE,KAAK8G,WACvB,ECvEH,MAAMQ,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUT,YACvCtL,EAAO+L,EAAU9L,KAEvB+E,EAAaa,GAAG7I,SAAUiP,EAAa,qBAAoBjM,OAAU,SAAU0D,GAK7E,GAJI,CAAC,IAAK,QAAQ/G,SAAS6H,KAAK0H,UAC9BxI,EAAMqD,iBAGJ1I,EAAWmG,MACb,OAGF,MAAMhD,EAAStE,EAAuBsH,OAASA,KAAKtG,QAAS,IAAG8B,KAC/C+L,EAAUI,oBAAoB3K,GAGtCwK,I,GAbX,ECeF,MAAMI,UAAcpB,EAEP/K,kBACT,MAhBS,OAiBV,CAGDoM,QAGE,GAFmBrH,EAAaoB,QAAQ5B,KAAK0G,SAjB5B,kBAmBFzE,iBACb,OAGFjC,KAAK0G,SAAS1M,UAAU4J,OApBJ,QAsBpB,MAAMsD,EAAalH,KAAK0G,SAAS1M,UAAUC,SAvBvB,QAwBpB+F,KAAKiH,gBAAe,IAAMjH,KAAK8H,mBAAmB9H,KAAK0G,SAAUQ,EAClE,CAGDY,kBACE9H,KAAK0G,SAAS9C,SACdpD,EAAaoB,QAAQ5B,KAAK0G,SA/BR,mBAgClB1G,KAAK6G,SACN,CAGqBM,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoB3H,MAEvC,GAAsB,iBAAXwF,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAOpN,WAAW,MAAmB,gBAAXoN,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQxF,KANZ,CAOF,GACF,EAOHsH,EAAqBM,EAAO,SAM5BxM,EAAmBwM,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAe3B,EAER/K,kBACT,MAhBS,QAiBV,CAGD2M,SAEEpI,KAAK0G,SAASlC,aAAa,eAAgBxE,KAAK0G,SAAS1M,UAAUoO,OAjB7C,UAkBvB,CAGqBjB,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoB3H,MAEzB,WAAXwF,GACFwC,EAAKxC,IAER,GACF,EAOHhF,EAAaa,GAAG7I,SAlCc,2BAkCkB0P,GAAsBhJ,IACpEA,EAAMqD,iBAEN,MAAM8F,EAASnJ,EAAMlC,OAAOtD,QAAQwO,GACvBC,EAAOR,oBAAoBU,GAEnCD,QAAL,IAOFhN,EAAmB+M,GCxDnB,MAAMG,EAAiB,CACrBrJ,KAAI,CAACjH,EAAUD,EAAUS,SAAS6B,kBACzB,GAAGkO,UAAUC,QAAQtC,UAAU7F,iBAAiBN,KAAKhI,EAASC,IAGvEyQ,QAAO,CAACzQ,EAAUD,EAAUS,SAAS6B,kBAC5BmO,QAAQtC,UAAUzN,cAAcsH,KAAKhI,EAASC,GAGvD0Q,SAAQ,CAAC3Q,EAASC,IACT,GAAGuQ,UAAUxQ,EAAQ2Q,UAAU3D,QAAO4D,GAASA,EAAMC,QAAQ5Q,KAGtE6Q,QAAQ9Q,EAASC,GACf,MAAM6Q,EAAU,GAChB,IAAIC,EAAW/Q,EAAQ6B,WAAWF,QAAQ1B,GAE1C,KAAO8Q,GACLD,EAAQ5M,KAAK6M,GACbA,EAAWA,EAASlP,WAAWF,QAAQ1B,GAGzC,OAAO6Q,C,EAGTE,KAAKhR,EAASC,GACZ,IAAIgR,EAAWjR,EAAQkR,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQ5Q,GACnB,MAAO,CAACgR,GAGVA,EAAWA,EAASC,sBACrB,CAED,MAAO,E,EAGTC,KAAKnR,EAASC,GACZ,IAAIkR,EAAOnR,EAAQoR,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQ5Q,GACf,MAAO,CAACkR,GAGVA,EAAOA,EAAKC,kBACb,CAED,MAAO,E,EAGTC,kBAAkBrR,GAChB,MAAMsR,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAItR,GAAa,GAAEA,2BAAiCuR,KAAK,KAE3D,OAAOvJ,KAAKf,KAAKoK,EAAYtR,GAASgN,QAAOyE,IAAO3P,EAAW2P,IAAOpQ,EAAUoQ,IACjF,GCpDGpE,EAAU,CACdqE,YAAa,KACbC,aAAc,KACdC,cAAe,MAGXtE,EAAc,CAClBoE,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,UAAczE,EAClBU,YAAY9N,EAASyN,GACnBiB,QACAzG,KAAK0G,SAAW3O,EAEXA,GAAY6R,EAAMC,gBAIvB7J,KAAK2G,QAAU3G,KAAKuF,WAAWC,GAC/BxF,KAAK8J,QAAU,EACf9J,KAAK+J,sBAAwBhJ,QAAQjG,OAAOkP,cAC5ChK,KAAKiK,cACN,CAGU7E,qBACT,OAAOA,CACR,CAEUC,yBACT,OAAOA,CACR,CAEU5J,kBACT,MArDS,OAsDV,CAGDoL,UACErG,EAAaC,IAAIT,KAAK0G,SAzDR,YA0Df,CAGDwD,OAAOhL,GACAc,KAAK+J,sBAMN/J,KAAKmK,wBAAwBjL,KAC/Bc,KAAK8J,QAAU5K,EAAMkL,SANrBpK,KAAK8J,QAAU5K,EAAMmL,QAAQ,GAAGD,OAQnC,CAEDE,KAAKpL,GACCc,KAAKmK,wBAAwBjL,KAC/Bc,KAAK8J,QAAU5K,EAAMkL,QAAUpK,KAAK8J,SAGtC9J,KAAKuK,eACLrO,EAAQ8D,KAAK2G,QAAQ8C,YACtB,CAEDe,MAAMtL,GACJc,KAAK8J,QAAU5K,EAAMmL,SAAWnL,EAAMmL,QAAQlR,OAAS,EACrD,EACA+F,EAAMmL,QAAQ,GAAGD,QAAUpK,KAAK8J,OACnC,CAEDS,eACE,MAAME,EAAY9M,KAAK+M,IAAI1K,KAAK8J,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAYzK,KAAK8J,QAEnC9J,KAAK8J,QAAU,EAEVa,GAILzO,EAAQyO,EAAY,EAAI3K,KAAK2G,QAAQgD,cAAgB3J,KAAK2G,QAAQ+C,aACnE,CAEDO,cACMjK,KAAK+J,uBACPvJ,EAAaa,GAAGrB,KAAK0G,SAxGA,wBAwG6BxH,GAASc,KAAKkK,OAAOhL,KACvEsB,EAAaa,GAAGrB,KAAK0G,SAxGF,sBAwG6BxH,GAASc,KAAKsK,KAAKpL,KAEnEc,KAAK0G,SAAS1M,UAAU4Q,IAvGG,mBAyG3BpK,EAAaa,GAAGrB,KAAK0G,SAhHD,uBAgH6BxH,GAASc,KAAKkK,OAAOhL,KACtEsB,EAAaa,GAAGrB,KAAK0G,SAhHF,sBAgH6BxH,GAASc,KAAKwK,MAAMtL,KACpEsB,EAAaa,GAAGrB,KAAK0G,SAhHH,qBAgH6BxH,GAASc,KAAKsK,KAAKpL,KAErE,CAEDiL,wBAAwBjL,GACtB,OAAOc,KAAK+J,wBAjHS,QAiHiB7K,EAAM2L,aAlHrB,UAkHyD3L,EAAM2L,YACvF,CAGiB1D,qBAChB,MAAO,iBAAkB3O,SAAS6B,iBAAmByQ,UAAUC,eAAiB,CACjF,ECpHH,MASMC,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,GAAkB,QAGlBC,GAAc,mBAQdC,GAAsB,WACtBC,GAAoB,SAepBC,GAAmB,CACvBC,UAAkBL,GAClBM,WAAmBP,GAGf9F,GAAU,CACdsG,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF1G,GAAc,CAClBqG,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiBxF,EACrBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAEfxF,KAAKiM,UAAY,KACjBjM,KAAKkM,eAAiB,KACtBlM,KAAKmM,YAAa,EAClBnM,KAAKoM,aAAe,KACpBpM,KAAKqM,aAAe,KAEpBrM,KAAKsM,mBAAqBhE,EAAeG,QAzCjB,uBAyC8CzI,KAAK0G,UAC3E1G,KAAKuM,qBAEDvM,KAAK2G,QAAQkF,OAASR,IACxBrL,KAAKwM,OAER,CAGUpH,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MA9FS,UA+FV,CAGDyN,OACElJ,KAAKyM,OAAOzB,EACb,CAED0B,mBAIOlU,SAASmU,QAAUvT,EAAU4G,KAAK0G,WACrC1G,KAAKkJ,MAER,CAEDH,OACE/I,KAAKyM,OAAOxB,EACb,CAEDW,QACM5L,KAAKmM,YACPxT,EAAqBqH,KAAK0G,UAG5B1G,KAAK4M,gBACN,CAEDJ,QACExM,KAAK4M,iBACL5M,KAAK6M,kBAEL7M,KAAKiM,UAAYa,aAAY,IAAM9M,KAAK0M,mBAAmB1M,KAAK2G,QAAQ+E,SACzE,CAEDqB,oBACO/M,KAAK2G,QAAQkF,OAId7L,KAAKmM,WACP3L,EAAac,IAAItB,KAAK0G,SAAU0E,IAAY,IAAMpL,KAAKwM,UAIzDxM,KAAKwM,QACN,CAEDQ,GAAGvP,GACD,MAAMwP,EAAQjN,KAAKkN,YACnB,GAAIzP,EAAQwP,EAAM9T,OAAS,GAAKsE,EAAQ,EACtC,OAGF,GAAIuC,KAAKmM,WAEP,YADA3L,EAAac,IAAItB,KAAK0G,SAAU0E,IAAY,IAAMpL,KAAKgN,GAAGvP,KAI5D,MAAM0P,EAAcnN,KAAKoN,cAAcpN,KAAKqN,cAC5C,GAAIF,IAAgB1P,EAClB,OAGF,MAAM6P,EAAQ7P,EAAQ0P,EAAcnC,EAAaC,EAEjDjL,KAAKyM,OAAOa,EAAOL,EAAMxP,GAC1B,CAEDoJ,UACM7G,KAAKqM,cACPrM,KAAKqM,aAAaxF,UAGpBJ,MAAMI,SACP,CAGDnB,kBAAkBF,GAEhB,OADAA,EAAO+H,gBAAkB/H,EAAOkG,SACzBlG,CACR,CAED+G,qBACMvM,KAAK2G,QAAQgF,UACfnL,EAAaa,GAAGrB,KAAK0G,SApKJ,uBAoK6BxH,GAASc,KAAKwN,SAAStO,KAG5C,UAAvBc,KAAK2G,QAAQiF,QACfpL,EAAaa,GAAGrB,KAAK0G,SAvKD,0BAuK6B,IAAM1G,KAAK4L,UAC5DpL,EAAaa,GAAGrB,KAAK0G,SAvKD,0BAuK6B,IAAM1G,KAAK+M,uBAG1D/M,KAAK2G,QAAQmF,OAASlC,EAAMC,eAC9B7J,KAAKyN,yBAER,CAEDA,0BACE,IAAK,MAAMC,KAAOpF,EAAerJ,KAhKX,qBAgKmCe,KAAK0G,UAC5DlG,EAAaa,GAAGqM,EAhLI,yBAgLmBxO,GAASA,EAAMqD,mBAGxD,MAqBMoL,EAAc,CAClBjE,aAAc,IAAM1J,KAAKyM,OAAOzM,KAAK4N,kBAAkB1C,IACvDvB,cAAe,IAAM3J,KAAKyM,OAAOzM,KAAK4N,kBAAkBzC,KACxD1B,YAxBkB,KACS,UAAvBzJ,KAAK2G,QAAQiF,QAYjB5L,KAAK4L,QACD5L,KAAKoM,cACPyB,aAAa7N,KAAKoM,cAGpBpM,KAAKoM,aAAelP,YAAW,IAAM8C,KAAK+M,qBAjNjB,IAiN+D/M,KAAK2G,QAAQ+E,UAArG,GASF1L,KAAKqM,aAAe,IAAIzC,EAAM5J,KAAK0G,SAAUiH,EAC9C,CAEDH,SAAStO,GACP,GAAI,kBAAkBmH,KAAKnH,EAAMlC,OAAO0K,SACtC,OAGF,MAAMiD,EAAYY,GAAiBrM,EAAMwD,KACrCiI,IACFzL,EAAMqD,iBACNvC,KAAKyM,OAAOzM,KAAK4N,kBAAkBjD,IAEtC,CAEDyC,cAAcrV,GACZ,OAAOiI,KAAKkN,YAAYxP,QAAQ3F,EACjC,CAED+V,2BAA2BrQ,GACzB,IAAKuC,KAAKsM,mBACR,OAGF,MAAMyB,EAAkBzF,EAAeG,QA1NnB,UA0N4CzI,KAAKsM,oBAErEyB,EAAgB/T,UAAU4J,OAAO0H,IACjCyC,EAAgBrJ,gBAAgB,gBAEhC,MAAMsJ,EAAqB1F,EAAeG,QAAS,sBAAqBhL,MAAWuC,KAAKsM,oBAEpF0B,IACFA,EAAmBhU,UAAU4Q,IAAIU,IACjC0C,EAAmBxJ,aAAa,eAAgB,QAEnD,CAEDqI,kBACE,MAAM9U,EAAUiI,KAAKkM,gBAAkBlM,KAAKqN,aAE5C,IAAKtV,EACH,OAGF,MAAMkW,EAAkBvR,OAAOwR,SAASnW,EAAQE,aAAa,oBAAqB,IAElF+H,KAAK2G,QAAQ+E,SAAWuC,GAAmBjO,KAAK2G,QAAQ4G,eACzD,CAEDd,OAAOa,EAAOvV,EAAU,MACtB,GAAIiI,KAAKmM,WACP,OAGF,MAAM9O,EAAgB2C,KAAKqN,aACrBc,EAASb,IAAUtC,EACnBoD,EAAcrW,GAAWoF,EAAqB6C,KAAKkN,YAAa7P,EAAe8Q,EAAQnO,KAAK2G,QAAQoF,MAE1G,GAAIqC,IAAgB/Q,EAClB,OAGF,MAAMgR,EAAmBrO,KAAKoN,cAAcgB,GAEtCE,EAAeC,GACZ/N,EAAaoB,QAAQ5B,KAAK0G,SAAU6H,EAAW,CACpD1O,cAAeuO,EACfzD,UAAW3K,KAAKwO,kBAAkBlB,GAClC3J,KAAM3D,KAAKoN,cAAc/P,GACzB2P,GAAIqB,IAMR,GAFmBC,EA5RF,qBA8RFrM,iBACb,OAGF,IAAK5E,IAAkB+Q,EAGrB,OAGF,MAAMK,EAAY1N,QAAQf,KAAKiM,WAC/BjM,KAAK4L,QAEL5L,KAAKmM,YAAa,EAElBnM,KAAK8N,2BAA2BO,GAChCrO,KAAKkM,eAAiBkC,EAEtB,MAAMM,EAAuBP,EAnSR,sBADF,oBAqSbQ,EAAiBR,EAnSH,qBACA,qBAoSpBC,EAAYpU,UAAU4Q,IAAI+D,GAE1BhU,EAAOyT,GAEP/Q,EAAcrD,UAAU4Q,IAAI8D,GAC5BN,EAAYpU,UAAU4Q,IAAI8D,GAa1B1O,KAAKiH,gBAXoB,KACvBmH,EAAYpU,UAAU4J,OAAO8K,EAAsBC,GACnDP,EAAYpU,UAAU4Q,IAAIU,IAE1BjO,EAAcrD,UAAU4J,OAAO0H,GAAmBqD,EAAgBD,GAElE1O,KAAKmM,YAAa,EAElBmC,EAAalD,GAAb,GAGoC/N,EAAe2C,KAAK4O,eAEtDH,GACFzO,KAAKwM,OAER,CAEDoC,cACE,OAAO5O,KAAK0G,SAAS1M,UAAUC,SAlUV,QAmUtB,CAEDoT,aACE,OAAO/E,EAAeG,QA9TGoG,wBA8T2B7O,KAAK0G,SAC1D,CAEDwG,YACE,OAAO5E,EAAerJ,KAnUJ,iBAmUwBe,KAAK0G,SAChD,CAEDkG,iBACM5M,KAAKiM,YACP6C,cAAc9O,KAAKiM,WACnBjM,KAAKiM,UAAY,KAEpB,CAED2B,kBAAkBjD,GAChB,OAAIzP,IACKyP,IAAcO,EAAiBD,EAAaD,EAG9CL,IAAcO,EAAiBF,EAAaC,CACpD,CAEDuD,kBAAkBlB,GAChB,OAAIpS,IACKoS,IAAUrC,EAAaC,EAAiBC,GAG1CmC,IAAUrC,EAAaE,GAAkBD,CACjD,CAGqB/D,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOgE,GAASrE,oBAAoB3H,KAAMwF,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAOpN,WAAW,MAAmB,gBAAXoN,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IACN,OAVCwC,EAAKgF,GAAGxH,EAWX,GACF,EAOHhF,EAAaa,GAAG7I,SAjYc,6BAeF,uCAkXyC,SAAU0G,GAC7E,MAAMlC,EAAStE,EAAuBsH,MAEtC,IAAKhD,IAAWA,EAAOhD,UAAUC,SAASoR,IACxC,OAGFnM,EAAMqD,iBAEN,MAAMwM,EAAW/C,GAASrE,oBAAoB3K,GACxCgS,EAAahP,KAAK/H,aAAa,oBAErC,OAAI+W,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhDzI,EAAYY,iBAAiBlF,KAAM,UACrC+O,EAAS7F,YACT6F,EAAShC,sBAIXgC,EAAShG,YACTgG,EAAShC,oBACV,IAEDvM,EAAaa,GAAGvG,OA9Za,6BA8ZgB,KAC3C,MAAMmU,EAAY3G,EAAerJ,KA9YR,6BAgZzB,IAAK,MAAM8P,KAAYE,EACrBjD,GAASrE,oBAAoBoH,EAC9B,IAOH3T,EAAmB4Q,IClcnB,MAWMkD,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aASxBlH,GAAuB,8BAEvB9C,GAAU,CACdiK,OAAQ,KACRjH,QAAQ,GAGJ/C,GAAc,CAClBgK,OAAQ,iBACRjH,OAAQ,WAOV,MAAMkH,WAAiB9I,EACrBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAEfxF,KAAKuP,kBAAmB,EACxBvP,KAAKwP,cAAgB,GAErB,MAAMC,EAAanH,EAAerJ,KAAKiJ,IAEvC,IAAK,MAAMwH,KAAQD,EAAY,CAC7B,MAAMzX,EAAWO,EAAuBmX,GAClCC,EAAgBrH,EAAerJ,KAAKjH,GACvC+M,QAAO6K,GAAgBA,IAAiB5P,KAAK0G,WAE/B,OAAb1O,GAAqB2X,EAAcxW,QACrC6G,KAAKwP,cAAcvT,KAAKyT,EAE3B,CAED1P,KAAK6P,sBAEA7P,KAAK2G,QAAQ0I,QAChBrP,KAAK8P,0BAA0B9P,KAAKwP,cAAexP,KAAK+P,YAGtD/P,KAAK2G,QAAQyB,QACfpI,KAAKoI,QAER,CAGUhD,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MA9ES,UA+EV,CAGD2M,SACMpI,KAAK+P,WACP/P,KAAKgQ,OAELhQ,KAAKiQ,MAER,CAEDA,OACE,GAAIjQ,KAAKuP,kBAAoBvP,KAAK+P,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANIlQ,KAAK2G,QAAQ0I,SACfa,EAAiBlQ,KAAKmQ,uBA9EH,wCA+EhBpL,QAAOhN,GAAWA,IAAYiI,KAAK0G,WACnC4C,KAAIvR,GAAWuX,GAAS3H,oBAAoB5P,EAAS,CAAEqQ,QAAQ,OAGhE8H,EAAe/W,QAAU+W,EAAe,GAAGX,iBAC7C,OAIF,GADmB/O,EAAaoB,QAAQ5B,KAAK0G,SAvG7B,oBAwGDzE,iBACb,OAGF,IAAK,MAAMmO,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAYrQ,KAAKsQ,gBAEvBtQ,KAAK0G,SAAS1M,UAAU4J,OAAOuL,IAC/BnP,KAAK0G,SAAS1M,UAAU4Q,IAAIwE,IAE5BpP,KAAK0G,SAAS6J,MAAMF,GAAa,EAEjCrQ,KAAK8P,0BAA0B9P,KAAKwP,eAAe,GACnDxP,KAAKuP,kBAAmB,EAExB,MAYMiB,EAAc,SADSH,EAAU,GAAG9J,cAAgB8J,EAAU3O,MAAM,KAG1E1B,KAAKiH,gBAdY,KACfjH,KAAKuP,kBAAmB,EAExBvP,KAAK0G,SAAS1M,UAAU4J,OAAOwL,IAC/BpP,KAAK0G,SAAS1M,UAAU4Q,IAAIuE,GAAqBD,IAEjDlP,KAAK0G,SAAS6J,MAAMF,GAAa,GAEjC7P,EAAaoB,QAAQ5B,KAAK0G,SAjIX,oBAiIf,GAM4B1G,KAAK0G,UAAU,GAC7C1G,KAAK0G,SAAS6J,MAAMF,GAAc,GAAErQ,KAAK0G,SAAS8J,MACnD,CAEDR,OACE,GAAIhQ,KAAKuP,mBAAqBvP,KAAK+P,WACjC,OAIF,GADmBvP,EAAaoB,QAAQ5B,KAAK0G,SA/I7B,oBAgJDzE,iBACb,OAGF,MAAMoO,EAAYrQ,KAAKsQ,gBAEvBtQ,KAAK0G,SAAS6J,MAAMF,GAAc,GAAErQ,KAAK0G,SAAS+J,wBAAwBJ,OAE1E1V,EAAOqF,KAAK0G,UAEZ1G,KAAK0G,SAAS1M,UAAU4Q,IAAIwE,IAC5BpP,KAAK0G,SAAS1M,UAAU4J,OAAOuL,GAAqBD,IAEpD,IAAK,MAAMtN,KAAW5B,KAAKwP,cAAe,CACxC,MAAMzX,EAAUW,EAAuBkJ,GAEnC7J,IAAYiI,KAAK+P,SAAShY,IAC5BiI,KAAK8P,0BAA0B,CAAClO,IAAU,EAE7C,CAED5B,KAAKuP,kBAAmB,EASxBvP,KAAK0G,SAAS6J,MAAMF,GAAa,GAEjCrQ,KAAKiH,gBATY,KACfjH,KAAKuP,kBAAmB,EACxBvP,KAAK0G,SAAS1M,UAAU4J,OAAOwL,IAC/BpP,KAAK0G,SAAS1M,UAAU4Q,IAAIuE,IAC5B3O,EAAaoB,QAAQ5B,KAAK0G,SA1KV,qBA0KhB,GAK4B1G,KAAK0G,UAAU,EAC9C,CAEDqJ,SAAShY,EAAUiI,KAAK0G,UACtB,OAAO3O,EAAQiC,UAAUC,SAASiV,GACnC,CAGDxJ,kBAAkBF,GAGhB,OAFAA,EAAO4C,OAASrH,QAAQyE,EAAO4C,QAC/B5C,EAAO6J,OAASnW,EAAWsM,EAAO6J,QAC3B7J,CACR,CAED8K,gBACE,OAAOtQ,KAAK0G,SAAS1M,UAAUC,SAtLL,uBAEhB,QACC,QAoLZ,CAED4V,sBACE,IAAK7P,KAAK2G,QAAQ0I,OAChB,OAGF,MAAM3G,EAAW1I,KAAKmQ,uBAAuBjI,IAE7C,IAAK,MAAMnQ,KAAW2Q,EAAU,CAC9B,MAAMgI,EAAWhY,EAAuBX,GAEpC2Y,GACF1Q,KAAK8P,0BAA0B,CAAC/X,GAAUiI,KAAK+P,SAASW,GAE3D,CACF,CAEDP,uBAAuBnY,GACrB,MAAM0Q,EAAWJ,EAAerJ,KA3MA,6BA2MiCe,KAAK2G,QAAQ0I,QAE9E,OAAO/G,EAAerJ,KAAKjH,EAAUgI,KAAK2G,QAAQ0I,QAAQtK,QAAOhN,IAAY2Q,EAASvQ,SAASJ,IAChG,CAED+X,0BAA0Ba,EAAcC,GACtC,GAAKD,EAAaxX,OAIlB,IAAK,MAAMpB,KAAW4Y,EACpB5Y,EAAQiC,UAAUoO,OAvNK,aAuNyBwI,GAChD7Y,EAAQyM,aAAa,gBAAiBoM,EAEzC,CAGqBzJ,uBAAC3B,GACrB,MAAMmB,EAAU,GAKhB,MAJsB,iBAAXnB,GAAuB,YAAYa,KAAKb,KACjDmB,EAAQyB,QAAS,GAGZpI,KAAK+H,MAAK,WACf,MAAMC,EAAOsH,GAAS3H,oBAAoB3H,KAAM2G,GAEhD,GAAsB,iBAAXnB,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IACN,CACF,GACF,EAOHhF,EAAaa,GAAG7I,SA1Pc,6BA0PkB0P,IAAsB,SAAUhJ,IAEjD,MAAzBA,EAAMlC,OAAO0K,SAAoBxI,EAAMY,gBAAmD,MAAjCZ,EAAMY,eAAe4H,UAChFxI,EAAMqD,iBAGR,MAAMvK,EAAWO,EAAuByH,MAClC6Q,EAAmBvI,EAAerJ,KAAKjH,GAE7C,IAAK,MAAMD,KAAW8Y,EACpBvB,GAAS3H,oBAAoB5P,EAAS,CAAEqQ,QAAQ,IAASA,QAE5D,IAMDhN,EAAmBkU,IC3SZ,IAAIwB,GAAM,MACNC,GAAS,SACTC,GAAQ,QACRC,GAAO,OACPC,GAAO,OACPC,GAAiB,CAACL,GAAKC,GAAQC,GAAOC,IACtCG,GAAQ,QACRC,GAAM,MACNC,GAAkB,kBAClBC,GAAW,WACXC,GAAS,SACTC,GAAY,YACZC,GAAmCP,GAAeQ,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIrJ,OAAO,CAACsJ,EAAY,IAAMT,GAAOS,EAAY,IAAMR,IAChE,GAAG,IACQS,GAA0B,GAAGvJ,OAAO4I,GAAgB,CAACD,KAAOS,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIrJ,OAAO,CAACsJ,EAAWA,EAAY,IAAMT,GAAOS,EAAY,IAAMR,IAC3E,GAAG,IAEQU,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAc,cACdC,GAAQ,QACRC,GAAa,aACbC,GAAiB,CAACT,GAAYC,GAAMC,GAAWC,GAAYC,GAAMC,GAAWC,GAAaC,GAAOC,IC9B5F,SAASE,GAAY1a,GAClC,OAAOA,GAAWA,EAAQ2a,UAAY,IAAIrO,cAAgB,IAC5D,CCFe,SAASsO,GAAUC,GAChC,GAAY,MAARA,EACF,OAAO9X,OAGT,GAAwB,oBAApB8X,EAAK7O,WAAkC,CACzC,IAAI8O,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwBhY,MACjE,CAEE,OAAO8X,CACT,CCTA,SAAS9Z,GAAU8Z,GAEjB,OAAOA,aADUD,GAAUC,GAAMpK,SACIoK,aAAgBpK,OACvD,CAEA,SAASuK,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,WACvD,CAEA,SAASC,GAAaL,GAEpB,MAA0B,oBAAfnY,aAKJmY,aADUD,GAAUC,GAAMnY,YACImY,aAAgBnY,WACvD,CCwDA,MAAAyY,GAAe,CACb1X,KAAM,cACN2X,SAAS,EACTC,MAAO,QACPzX,GA5EF,SAAqB0X,GACnB,IAAIC,EAAQD,EAAKC,MACjBvU,OAAOqC,KAAKkS,EAAMC,UAAUC,SAAQ,SAAUhY,GAC5C,IAAI+U,EAAQ+C,EAAMG,OAAOjY,IAAS,GAC9BoJ,EAAa0O,EAAM1O,WAAWpJ,IAAS,GACvCzD,EAAUub,EAAMC,SAAS/X,GAExBuX,GAAchb,IAAa0a,GAAY1a,KAO5CgH,OAAO2U,OAAO3b,EAAQwY,MAAOA,GAC7BxR,OAAOqC,KAAKwD,GAAY4O,SAAQ,SAAUhY,GACxC,IAAImH,EAAQiC,EAAWpJ,IAET,IAAVmH,EACF5K,EAAQ2M,gBAAgBlJ,GAExBzD,EAAQyM,aAAahJ,GAAgB,IAAVmH,EAAiB,GAAKA,EAEzD,IACA,GACA,EAoDEgR,OAlDF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MACdO,EAAgB,CAClBrC,OAAQ,CACNsC,SAAUR,EAAMS,QAAQC,SACxB/C,KAAM,IACNH,IAAK,IACLmD,OAAQ,KAEVC,MAAO,CACLJ,SAAU,YAEZrC,UAAW,IASb,OAPA1S,OAAO2U,OAAOJ,EAAMC,SAAS/B,OAAOjB,MAAOsD,EAAcrC,QACzD8B,EAAMG,OAASI,EAEXP,EAAMC,SAASW,OACjBnV,OAAO2U,OAAOJ,EAAMC,SAASW,MAAM3D,MAAOsD,EAAcK,OAGnD,WACLnV,OAAOqC,KAAKkS,EAAMC,UAAUC,SAAQ,SAAUhY,GAC5C,IAAIzD,EAAUub,EAAMC,SAAS/X,GACzBoJ,EAAa0O,EAAM1O,WAAWpJ,IAAS,GAGvC+U,EAFkBxR,OAAOqC,KAAKkS,EAAMG,OAAOU,eAAe3Y,GAAQ8X,EAAMG,OAAOjY,GAAQqY,EAAcrY,IAE7EmW,QAAO,SAAUpB,EAAOxK,GAElD,OADAwK,EAAMxK,GAAY,GACXwK,CACf,GAAS,IAEEwC,GAAchb,IAAa0a,GAAY1a,KAI5CgH,OAAO2U,OAAO3b,EAAQwY,MAAOA,GAC7BxR,OAAOqC,KAAKwD,GAAY4O,SAAQ,SAAUY,GACxCrc,EAAQ2M,gBAAgB0P,EAChC,IACA,GACA,CACA,EASEC,SAAU,CAAC,kBCjFE,SAASC,GAAiBzC,GACvC,OAAOA,EAAUxZ,MAAM,KAAK,EAC9B,CCHO,IAAIuF,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACX0W,GAAQ5W,KAAK4W,MCFT,SAASC,KACtB,IAAIC,EAAS3J,UAAU4J,cAEvB,OAAc,MAAVD,GAAkBA,EAAOE,OACpBF,EAAOE,OAAOrL,KAAI,SAAUsL,GACjC,OAAOA,EAAKC,MAAQ,IAAMD,EAAKE,OACrC,IAAOvL,KAAK,KAGHuB,UAAUiK,SACnB,CCTe,SAASC,KACtB,OAAQ,iCAAiC3O,KAAKmO,KAChD,CCCe,SAAS/D,GAAsB1Y,EAASkd,EAAcC,QAC9C,IAAjBD,IACFA,GAAe,QAGO,IAApBC,IACFA,GAAkB,GAGpB,IAAIC,EAAapd,EAAQ0Y,wBACrB2E,EAAS,EACTC,EAAS,EAETJ,GAAgBlC,GAAchb,KAChCqd,EAASrd,EAAQud,YAAc,GAAIf,GAAMY,EAAWI,OAASxd,EAAQud,aAAmB,EACxFD,EAAStd,EAAQ6C,aAAe,GAAI2Z,GAAMY,EAAWK,QAAUzd,EAAQ6C,cAAoB,GAG7F,IACI6a,GADO3c,GAAUf,GAAW4a,GAAU5a,GAAW+C,QAC3B2a,eAEtBC,GAAoBV,MAAsBE,EAC1CS,GAAKR,EAAWlE,MAAQyE,GAAoBD,EAAiBA,EAAeG,WAAa,IAAMR,EAC/FS,GAAKV,EAAWrE,KAAO4E,GAAoBD,EAAiBA,EAAeK,UAAY,IAAMT,EAC7FE,EAAQJ,EAAWI,MAAQH,EAC3BI,EAASL,EAAWK,OAASH,EACjC,MAAO,CACLE,MAAOA,EACPC,OAAQA,EACR1E,IAAK+E,EACL7E,MAAO2E,EAAIJ,EACXxE,OAAQ8E,EAAIL,EACZvE,KAAM0E,EACNA,EAAGA,EACHE,EAAGA,EAEP,CCrCe,SAASE,GAAche,GACpC,IAAIod,EAAa1E,GAAsB1Y,GAGnCwd,EAAQxd,EAAQud,YAChBE,EAASzd,EAAQ6C,aAUrB,OARI+C,KAAK+M,IAAIyK,EAAWI,MAAQA,IAAU,IACxCA,EAAQJ,EAAWI,OAGjB5X,KAAK+M,IAAIyK,EAAWK,OAASA,IAAW,IAC1CA,EAASL,EAAWK,QAGf,CACLG,EAAG5d,EAAQ6d,WACXC,EAAG9d,EAAQ+d,UACXP,MAAOA,EACPC,OAAQA,EAEZ,CCvBe,SAASvb,GAASoV,EAAQ1G,GACvC,IAAIqN,EAAWrN,EAAMpO,aAAeoO,EAAMpO,cAE1C,GAAI8U,EAAOpV,SAAS0O,GAClB,OAAO,EAEJ,GAAIqN,GAAY/C,GAAa+C,GAAW,CACzC,IAAI9M,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQmG,EAAO4G,WAAW/M,GAC5B,OAAO,EAITA,EAAOA,EAAKtP,YAAcsP,EAAKgN,IACvC,OAAehN,EACf,CAGE,OAAO,CACT,CCrBe,SAAS3P,GAAiBxB,GACvC,OAAO4a,GAAU5a,GAASwB,iBAAiBxB,EAC7C,CCFe,SAASoe,GAAepe,GACrC,MAAO,CAAC,QAAS,KAAM,MAAM2F,QAAQ+U,GAAY1a,KAAa,CAChE,CCFe,SAASqe,GAAmBre,GAEzC,QAASe,GAAUf,GAAWA,EAAQ8a,cACtC9a,EAAQS,WAAasC,OAAOtC,UAAU6B,eACxC,CCFe,SAASgc,GAActe,GACpC,MAA6B,SAAzB0a,GAAY1a,GACPA,EAMPA,EAAQue,cACRve,EAAQ6B,aACRqZ,GAAalb,GAAWA,EAAQme,KAAO,OAEvCE,GAAmBre,EAGvB,CCVA,SAASwe,GAAoBxe,GAC3B,OAAKgb,GAAchb,IACoB,UAAvCwB,GAAiBxB,GAAS+b,SAInB/b,EAAQye,aAHN,IAIX,CAwCe,SAASC,GAAgB1e,GAItC,IAHA,IAAI+C,EAAS6X,GAAU5a,GACnBye,EAAeD,GAAoBxe,GAEhCye,GAAgBL,GAAeK,IAA6D,WAA5Cjd,GAAiBid,GAAc1C,UACpF0C,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9B/D,GAAY+D,IAA0D,SAA9B/D,GAAY+D,IAAwE,WAA5Cjd,GAAiBid,GAAc1C,UAC3HhZ,EAGF0b,GAhDT,SAA4Bze,GAC1B,IAAI2e,EAAY,WAAWrQ,KAAKmO,MAGhC,GAFW,WAAWnO,KAAKmO,OAEfzB,GAAchb,IAII,UAFXwB,GAAiBxB,GAEnB+b,SACb,OAAO,KAIX,IAAI6C,EAAcN,GAActe,GAMhC,IAJIkb,GAAa0D,KACfA,EAAcA,EAAYT,MAGrBnD,GAAc4D,IAAgB,CAAC,OAAQ,QAAQjZ,QAAQ+U,GAAYkE,IAAgB,GAAG,CAC3F,IAAIC,EAAMrd,GAAiBod,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAerZ,QAAQkZ,EAAII,aAAsBN,GAAgC,WAAnBE,EAAII,YAA2BN,GAAaE,EAAI7R,QAAyB,SAAf6R,EAAI7R,OACjO,OAAO4R,EAEPA,EAAcA,EAAY/c,UAEhC,CAEE,OAAO,IACT,CAgByBqd,CAAmBlf,IAAY+C,CACxD,CCpEe,SAASoc,GAAyBrF,GAC/C,MAAO,CAAC,MAAO,UAAUnU,QAAQmU,IAAc,EAAI,IAAM,GAC3D,CCDO,SAASsF,GAAOtZ,EAAK8E,EAAO/E,GACjC,OAAOwZ,GAAQvZ,EAAKwZ,GAAQ1U,EAAO/E,GACrC,CCFe,SAAS0Z,GAAmBC,GACzC,OAAOxY,OAAO2U,OAAO,GCDd,CACL5C,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GDHuCsG,EACjD,CEHe,SAASC,GAAgB7U,EAAOvB,GAC7C,OAAOA,EAAKuQ,QAAO,SAAU8F,EAAS/U,GAEpC,OADA+U,EAAQ/U,GAAOC,EACR8U,CACX,GAAK,GACL,CCuFA,MAAAC,GAAe,CACblc,KAAM,QACN2X,SAAS,EACTC,MAAO,OACPzX,GA9EF,SAAe0X,GACb,IAAIsE,EAEArE,EAAQD,EAAKC,MACb9X,EAAO6X,EAAK7X,KACZuY,EAAUV,EAAKU,QACf6D,EAAetE,EAAMC,SAASW,MAC9B2D,EAAgBvE,EAAMwE,cAAcD,cACpCE,EAAgBzD,GAAiBhB,EAAMzB,WACvCmG,EAAOd,GAAyBa,GAEhCE,EADa,CAAChH,GAAMD,IAAOtT,QAAQqa,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBW,EAAS5E,GAItD,OAAOgE,GAAsC,iBAH7CY,EAA6B,mBAAZA,EAAyBA,EAAQnZ,OAAO2U,OAAO,GAAIJ,EAAM6E,MAAO,CAC/EtG,UAAWyB,EAAMzB,aACbqG,GACkDA,EAAUV,GAAgBU,EAAS/G,IAC7F,CAmBsBiH,CAAgBrE,EAAQmE,QAAS5E,GACjD+E,EAAYtC,GAAc6B,GAC1BU,EAAmB,MAATN,EAAelH,GAAMG,GAC/BsH,EAAmB,MAATP,EAAejH,GAASC,GAClCwH,EAAUlF,EAAM6E,MAAM1G,UAAUwG,GAAO3E,EAAM6E,MAAM1G,UAAUuG,GAAQH,EAAcG,GAAQ1E,EAAM6E,MAAM3G,OAAOyG,GAC9GQ,EAAYZ,EAAcG,GAAQ1E,EAAM6E,MAAM1G,UAAUuG,GACxDU,EAAoBjC,GAAgBmB,GACpCe,EAAaD,EAA6B,MAATV,EAAeU,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9C5a,EAAM0Z,EAAce,GACpB1a,EAAM+a,EAAaN,EAAUJ,GAAOV,EAAcgB,GAClDQ,EAASJ,EAAa,EAAIN,EAAUJ,GAAO,EAAIa,EAC/CE,EAAS7B,GAAOtZ,EAAKkb,EAAQnb,GAE7Bqb,EAAWjB,EACf1E,EAAMwE,cAActc,KAASmc,EAAwB,IAA0BsB,GAAYD,EAAQrB,EAAsBuB,aAAeF,EAASD,EAAQpB,EAnB3J,CAoBA,EA4CEhE,OA1CF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MAEd6F,EADUvF,EAAMG,QACWhc,QAC3B6f,OAAoC,IAArBuB,EAA8B,sBAAwBA,EAErD,MAAhBvB,IAKwB,iBAAjBA,IACTA,EAAetE,EAAMC,SAAS/B,OAAO/Y,cAAcmf,MAahD3d,GAASqZ,EAAMC,SAAS/B,OAAQoG,KAQrCtE,EAAMC,SAASW,MAAQ0D,EACzB,EASEvD,SAAU,CAAC,iBACX+E,iBAAkB,CAAC,oBCnGN,SAASC,GAAaxH,GACnC,OAAOA,EAAUxZ,MAAM,KAAK,EAC9B,CCOA,IAAIihB,GAAa,CACfxI,IAAK,OACLE,MAAO,OACPD,OAAQ,OACRE,KAAM,QAgBD,SAASsI,GAAY3F,GAC1B,IAAI4F,EAEAhI,EAASoC,EAAMpC,OACfiI,EAAa7F,EAAM6F,WACnB5H,EAAY+B,EAAM/B,UAClB6H,EAAY9F,EAAM8F,UAClBC,EAAU/F,EAAM+F,QAChB7F,EAAWF,EAAME,SACjB8F,EAAkBhG,EAAMgG,gBACxBC,EAAWjG,EAAMiG,SACjBC,EAAelG,EAAMkG,aACrBC,EAAUnG,EAAMmG,QAChBC,EAAaL,EAAQhE,EACrBA,OAAmB,IAAfqE,EAAwB,EAAIA,EAChCC,EAAaN,EAAQ9D,EACrBA,OAAmB,IAAfoE,EAAwB,EAAIA,EAEhCC,EAAgC,mBAAjBJ,EAA8BA,EAAa,CAC5DnE,EAAGA,EACHE,EAAGA,IACA,CACHF,EAAGA,EACHE,EAAGA,GAGLF,EAAIuE,EAAMvE,EACVE,EAAIqE,EAAMrE,EACV,IAAIsE,EAAOR,EAAQxF,eAAe,KAC9BiG,EAAOT,EAAQxF,eAAe,KAC9BkG,EAAQpJ,GACRqJ,EAAQxJ,GACRyJ,EAAMzf,OAEV,GAAI+e,EAAU,CACZ,IAAIrD,EAAeC,GAAgBjF,GAC/BgJ,EAAa,eACbC,EAAY,cAEZjE,IAAiB7D,GAAUnB,IAGmB,WAA5CjY,GAFJid,EAAeJ,GAAmB5E,IAECsC,UAAsC,aAAbA,IAC1D0G,EAAa,eACbC,EAAY,gBAOZ5I,IAAcf,KAAQe,IAAcZ,IAAQY,IAAcb,KAAU0I,IAAcrI,MACpFiJ,EAAQvJ,GAGR8E,IAFckE,GAAWvD,IAAiB+D,GAAOA,EAAI9E,eAAiB8E,EAAI9E,eAAeD,OACzFgB,EAAagE,IACEf,EAAWjE,OAC1BK,GAAK+D,EAAkB,GAAK,GAG1B/H,IAAcZ,KAASY,IAAcf,IAAOe,IAAcd,IAAW2I,IAAcrI,MACrFgJ,EAAQrJ,GAGR2E,IAFcoE,GAAWvD,IAAiB+D,GAAOA,EAAI9E,eAAiB8E,EAAI9E,eAAeF,MACzFiB,EAAaiE,IACEhB,EAAWlE,MAC1BI,GAAKiE,EAAkB,GAAK,EAElC,CAEE,IAgBMc,EAhBFC,EAAe5b,OAAO2U,OAAO,CAC/BI,SAAUA,GACT+F,GAAYP,IAEXsB,GAAyB,IAAjBd,EAnFd,SAA2BzG,GACzB,IAAIsC,EAAItC,EAAKsC,EACTE,EAAIxC,EAAKwC,EAETgF,EADM/f,OACIggB,kBAAoB,EAClC,MAAO,CACLnF,EAAGpB,GAAMoB,EAAIkF,GAAOA,GAAO,EAC3BhF,EAAGtB,GAAMsB,EAAIgF,GAAOA,GAAO,EAE/B,CA0EsCE,CAAkB,CACpDpF,EAAGA,EACHE,EAAGA,IACA,CACHF,EAAGA,EACHE,EAAGA,GAML,OAHAF,EAAIiF,EAAMjF,EACVE,EAAI+E,EAAM/E,EAEN+D,EAGK7a,OAAO2U,OAAO,GAAIiH,IAAeD,EAAiB,IAAmBJ,GAASF,EAAO,IAAM,GAAIM,EAAeL,GAASF,EAAO,IAAM,GAAIO,EAAe7D,WAAa0D,EAAIO,kBAAoB,IAAM,EAAI,aAAenF,EAAI,OAASE,EAAI,MAAQ,eAAiBF,EAAI,OAASE,EAAI,SAAU6E,IAG5R3b,OAAO2U,OAAO,GAAIiH,IAAenB,EAAkB,IAAoBc,GAASF,EAAOvE,EAAI,KAAO,GAAI2D,EAAgBa,GAASF,EAAOxE,EAAI,KAAO,GAAI6D,EAAgB3C,UAAY,GAAI2C,GAC9L,CAuDA,MAAAwB,GAAe,CACbxf,KAAM,gBACN2X,SAAS,EACTC,MAAO,cACPzX,GAzDF,SAAuBsf,GACrB,IAAI3H,EAAQ2H,EAAM3H,MACdS,EAAUkH,EAAMlH,QAChBmH,EAAwBnH,EAAQ6F,gBAChCA,OAA4C,IAA1BsB,GAA0CA,EAC5DC,EAAoBpH,EAAQ8F,SAC5BA,OAAiC,IAAtBsB,GAAsCA,EACjDC,EAAwBrH,EAAQ+F,aAChCA,OAAyC,IAA1BsB,GAA0CA,EAYzDT,EAAe,CACjB9I,UAAWyC,GAAiBhB,EAAMzB,WAClC6H,UAAWL,GAAa/F,EAAMzB,WAC9BL,OAAQ8B,EAAMC,SAAS/B,OACvBiI,WAAYnG,EAAM6E,MAAM3G,OACxBoI,gBAAiBA,EACjBG,QAAoC,UAA3BzG,EAAMS,QAAQC,UAGgB,MAArCV,EAAMwE,cAAcD,gBACtBvE,EAAMG,OAAOjC,OAASzS,OAAO2U,OAAO,GAAIJ,EAAMG,OAAOjC,OAAQ+H,GAAYxa,OAAO2U,OAAO,GAAIiH,EAAc,CACvGhB,QAASrG,EAAMwE,cAAcD,cAC7B/D,SAAUR,EAAMS,QAAQC,SACxB6F,SAAUA,EACVC,aAAcA,OAIe,MAA7BxG,EAAMwE,cAAc5D,QACtBZ,EAAMG,OAAOS,MAAQnV,OAAO2U,OAAO,GAAIJ,EAAMG,OAAOS,MAAOqF,GAAYxa,OAAO2U,OAAO,GAAIiH,EAAc,CACrGhB,QAASrG,EAAMwE,cAAc5D,MAC7BJ,SAAU,WACV+F,UAAU,EACVC,aAAcA,OAIlBxG,EAAM1O,WAAW4M,OAASzS,OAAO2U,OAAO,GAAIJ,EAAM1O,WAAW4M,OAAQ,CACnE,wBAAyB8B,EAAMzB,WAEnC,EAQE7J,KAAM,ICjLR,IAAIqT,GAAU,CACZA,SAAS,GAsCX,MAAAC,GAAe,CACb9f,KAAM,iBACN2X,SAAS,EACTC,MAAO,QACPzX,GAAI,WAAc,EAClBgY,OAxCF,SAAgBN,GACd,IAAIC,EAAQD,EAAKC,MACbjQ,EAAWgQ,EAAKhQ,SAChB0Q,EAAUV,EAAKU,QACfwH,EAAkBxH,EAAQyH,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkB1H,EAAQ2H,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7C3gB,EAAS6X,GAAUW,EAAMC,SAAS/B,QAClCmK,EAAgB,GAAGpT,OAAO+K,EAAMqI,cAAclK,UAAW6B,EAAMqI,cAAcnK,QAYjF,OAVIgK,GACFG,EAAcnI,SAAQ,SAAUoI,GAC9BA,EAAa5f,iBAAiB,SAAUqH,EAASwY,OAAQR,GAC/D,IAGMK,GACF5gB,EAAOkB,iBAAiB,SAAUqH,EAASwY,OAAQR,IAG9C,WACDG,GACFG,EAAcnI,SAAQ,SAAUoI,GAC9BA,EAAa3e,oBAAoB,SAAUoG,EAASwY,OAAQR,GACpE,IAGQK,GACF5gB,EAAOmC,oBAAoB,SAAUoG,EAASwY,OAAQR,GAE5D,CACA,EASErT,KAAM,IC/CR,IAAI8T,GAAO,CACT7K,KAAM,QACND,MAAO,OACPD,OAAQ,MACRD,IAAK,UAEQ,SAASiL,GAAqBlK,GAC3C,OAAOA,EAAU1R,QAAQ,0BAA0B,SAAU6b,GAC3D,OAAOF,GAAKE,EAChB,GACA,CCVA,IAAIF,GAAO,CACT1K,MAAO,MACPC,IAAK,SAEQ,SAAS4K,GAA8BpK,GACpD,OAAOA,EAAU1R,QAAQ,cAAc,SAAU6b,GAC/C,OAAOF,GAAKE,EAChB,GACA,CCPe,SAASE,GAAgBtJ,GACtC,IAAI2H,EAAM5H,GAAUC,GAGpB,MAAO,CACLuJ,WAHe5B,EAAI6B,YAInBC,UAHc9B,EAAI+B,YAKtB,CCNe,SAASC,GAAoBxkB,GAQ1C,OAAO0Y,GAAsB2F,GAAmBre,IAAUkZ,KAAOiL,GAAgBnkB,GAASokB,UAC5F,CCXe,SAASK,GAAezkB,GAErC,IAAI0kB,EAAoBljB,GAAiBxB,GACrC2kB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6BvW,KAAKqW,EAAWE,EAAYD,EAClE,CCLe,SAASE,GAAgBjK,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAalV,QAAQ+U,GAAYG,KAAU,EAEvDA,EAAKC,cAAc7X,KAGxB+X,GAAcH,IAAS4J,GAAe5J,GACjCA,EAGFiK,GAAgBxG,GAAczD,GACvC,CCJe,SAASkK,GAAkB/kB,EAASqF,GACjD,IAAI2f,OAES,IAAT3f,IACFA,EAAO,IAGT,IAAIwe,EAAeiB,GAAgB9kB,GAC/BilB,EAASpB,KAAqE,OAAlDmB,EAAwBhlB,EAAQ8a,oBAAyB,EAASkK,EAAsB/hB,MACpHuf,EAAM5H,GAAUiJ,GAChB5e,EAASggB,EAAS,CAACzC,GAAKhS,OAAOgS,EAAI9E,gBAAkB,GAAI+G,GAAeZ,GAAgBA,EAAe,IAAMA,EAC7GqB,EAAc7f,EAAKmL,OAAOvL,GAC9B,OAAOggB,EAASC,EAChBA,EAAY1U,OAAOuU,GAAkBzG,GAAcrZ,IACrD,CCzBe,SAASkgB,GAAiBC,GACvC,OAAOpe,OAAO2U,OAAO,GAAIyJ,EAAM,CAC7BlM,KAAMkM,EAAKxH,EACX7E,IAAKqM,EAAKtH,EACV7E,MAAOmM,EAAKxH,EAAIwH,EAAK5H,MACrBxE,OAAQoM,EAAKtH,EAAIsH,EAAK3H,QAE1B,CCqBA,SAAS4H,GAA2BrlB,EAASslB,EAAgBrJ,GAC3D,OAAOqJ,IAAmB9L,GAAW2L,GCzBxB,SAAyBnlB,EAASic,GAC/C,IAAIuG,EAAM5H,GAAU5a,GAChBulB,EAAOlH,GAAmBre,GAC1B0d,EAAiB8E,EAAI9E,eACrBF,EAAQ+H,EAAKzE,YACbrD,EAAS8H,EAAK1E,aACdjD,EAAI,EACJE,EAAI,EAER,GAAIJ,EAAgB,CAClBF,EAAQE,EAAeF,MACvBC,EAASC,EAAeD,OACxB,IAAI+H,EAAiBvI,MAEjBuI,IAAmBA,GAA+B,UAAbvJ,KACvC2B,EAAIF,EAAeG,WACnBC,EAAIJ,EAAeK,UAEzB,CAEE,MAAO,CACLP,MAAOA,EACPC,OAAQA,EACRG,EAAGA,EAAI4G,GAAoBxkB,GAC3B8d,EAAGA,EAEP,CDDwD2H,CAAgBzlB,EAASic,IAAalb,GAAUukB,GAdxG,SAAoCtlB,EAASic,GAC3C,IAAImJ,EAAO1M,GAAsB1Y,GAAS,EAAoB,UAAbic,GASjD,OARAmJ,EAAKrM,IAAMqM,EAAKrM,IAAM/Y,EAAQ0lB,UAC9BN,EAAKlM,KAAOkM,EAAKlM,KAAOlZ,EAAQ2lB,WAChCP,EAAKpM,OAASoM,EAAKrM,IAAM/Y,EAAQ6gB,aACjCuE,EAAKnM,MAAQmM,EAAKlM,KAAOlZ,EAAQ8gB,YACjCsE,EAAK5H,MAAQxd,EAAQ8gB,YACrBsE,EAAK3H,OAASzd,EAAQ6gB,aACtBuE,EAAKxH,EAAIwH,EAAKlM,KACdkM,EAAKtH,EAAIsH,EAAKrM,IACPqM,CACT,CAG0HQ,CAA2BN,EAAgBrJ,GAAYkJ,GEtBlK,SAAyBnlB,GACtC,IAAIglB,EAEAO,EAAOlH,GAAmBre,GAC1B6lB,EAAY1B,GAAgBnkB,GAC5BiD,EAA0D,OAAlD+hB,EAAwBhlB,EAAQ8a,oBAAyB,EAASkK,EAAsB/hB,KAChGua,EAAQ3X,GAAI0f,EAAKO,YAAaP,EAAKzE,YAAa7d,EAAOA,EAAK6iB,YAAc,EAAG7iB,EAAOA,EAAK6d,YAAc,GACvGrD,EAAS5X,GAAI0f,EAAKQ,aAAcR,EAAK1E,aAAc5d,EAAOA,EAAK8iB,aAAe,EAAG9iB,EAAOA,EAAK4d,aAAe,GAC5GjD,GAAKiI,EAAUzB,WAAaI,GAAoBxkB,GAChD8d,GAAK+H,EAAUvB,UAMnB,MAJiD,QAA7C9iB,GAAiByB,GAAQsiB,GAAM3S,YACjCgL,GAAK/X,GAAI0f,EAAKzE,YAAa7d,EAAOA,EAAK6d,YAAc,GAAKtD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRG,EAAGA,EACHE,EAAGA,EAEP,CFCkMkI,CAAgB3H,GAAmBre,IACrO,CG1Be,SAASimB,GAAe3K,GACrC,IAOIsG,EAPAlI,EAAY4B,EAAK5B,UACjB1Z,EAAUsb,EAAKtb,QACf8Z,EAAYwB,EAAKxB,UACjBkG,EAAgBlG,EAAYyC,GAAiBzC,GAAa,KAC1D6H,EAAY7H,EAAYwH,GAAaxH,GAAa,KAClDoM,EAAUxM,EAAUkE,EAAIlE,EAAU8D,MAAQ,EAAIxd,EAAQwd,MAAQ,EAC9D2I,EAAUzM,EAAUoE,EAAIpE,EAAU+D,OAAS,EAAIzd,EAAQyd,OAAS,EAGpE,OAAQuC,GACN,KAAKjH,GACH6I,EAAU,CACRhE,EAAGsI,EACHpI,EAAGpE,EAAUoE,EAAI9d,EAAQyd,QAE3B,MAEF,KAAKzE,GACH4I,EAAU,CACRhE,EAAGsI,EACHpI,EAAGpE,EAAUoE,EAAIpE,EAAU+D,QAE7B,MAEF,KAAKxE,GACH2I,EAAU,CACRhE,EAAGlE,EAAUkE,EAAIlE,EAAU8D,MAC3BM,EAAGqI,GAEL,MAEF,KAAKjN,GACH0I,EAAU,CACRhE,EAAGlE,EAAUkE,EAAI5d,EAAQwd,MACzBM,EAAGqI,GAEL,MAEF,QACEvE,EAAU,CACRhE,EAAGlE,EAAUkE,EACbE,EAAGpE,EAAUoE,GAInB,IAAIsI,EAAWpG,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZoG,EAAkB,CACpB,IAAIlG,EAAmB,MAAbkG,EAAmB,SAAW,QAExC,OAAQzE,GACN,KAAKtI,GACHuI,EAAQwE,GAAYxE,EAAQwE,IAAa1M,EAAUwG,GAAO,EAAIlgB,EAAQkgB,GAAO,GAC7E,MAEF,KAAK5G,GACHsI,EAAQwE,GAAYxE,EAAQwE,IAAa1M,EAAUwG,GAAO,EAAIlgB,EAAQkgB,GAAO,GAKrF,CAEE,OAAO0B,CACT,CC3De,SAASyE,GAAe9K,EAAOS,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIsK,EAAWtK,EACXuK,EAAqBD,EAASxM,UAC9BA,OAAmC,IAAvByM,EAAgChL,EAAMzB,UAAYyM,EAC9DC,EAAoBF,EAASrK,SAC7BA,OAAiC,IAAtBuK,EAA+BjL,EAAMU,SAAWuK,EAC3DC,EAAoBH,EAASI,SAC7BA,OAAiC,IAAtBD,EAA+BlN,GAAkBkN,EAC5DE,EAAwBL,EAASM,aACjCA,OAAyC,IAA1BD,EAAmCnN,GAAWmN,EAC7DE,EAAwBP,EAASQ,eACjCA,OAA2C,IAA1BD,EAAmCpN,GAASoN,EAC7DE,EAAuBT,EAASU,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBX,EAASnG,QAC5BA,OAA+B,IAArB8G,EAA8B,EAAIA,EAC5CzH,EAAgBD,GAAsC,iBAAZY,EAAuBA,EAAUV,GAAgBU,EAAS/G,KACpG8N,EAAaJ,IAAmBrN,GAASC,GAAYD,GACrDiI,EAAanG,EAAM6E,MAAM3G,OACzBzZ,EAAUub,EAAMC,SAASwL,EAAcE,EAAaJ,GACpDK,EJkBS,SAAyBnnB,EAAS0mB,EAAUE,EAAc3K,GACvE,IAAImL,EAAmC,oBAAbV,EAlB5B,SAA4B1mB,GAC1B,IAAIuZ,EAAkBwL,GAAkBzG,GAActe,IAElDqnB,EADoB,CAAC,WAAY,SAAS1hB,QAAQnE,GAAiBxB,GAAS+b,WAAa,GACnDf,GAAchb,GAAW0e,GAAgB1e,GAAWA,EAE9F,OAAKe,GAAUsmB,GAKR9N,EAAgBvM,QAAO,SAAUsY,GACtC,OAAOvkB,GAAUukB,IAAmBpjB,GAASojB,EAAgB+B,IAAmD,SAAhC3M,GAAY4K,EAChG,IANW,EAOX,CAK6DgC,CAAmBtnB,GAAW,GAAGwQ,OAAOkW,GAC/FnN,EAAkB,GAAG/I,OAAO4W,EAAqB,CAACR,IAClDW,EAAsBhO,EAAgB,GACtCiO,EAAejO,EAAgBK,QAAO,SAAU6N,EAASnC,GAC3D,IAAIF,EAAOC,GAA2BrlB,EAASslB,EAAgBrJ,GAK/D,OAJAwL,EAAQ1O,IAAMlT,GAAIuf,EAAKrM,IAAK0O,EAAQ1O,KACpC0O,EAAQxO,MAAQnT,GAAIsf,EAAKnM,MAAOwO,EAAQxO,OACxCwO,EAAQzO,OAASlT,GAAIsf,EAAKpM,OAAQyO,EAAQzO,QAC1CyO,EAAQvO,KAAOrT,GAAIuf,EAAKlM,KAAMuO,EAAQvO,MAC/BuO,CACX,GAAKpC,GAA2BrlB,EAASunB,EAAqBtL,IAK5D,OAJAuL,EAAahK,MAAQgK,EAAavO,MAAQuO,EAAatO,KACvDsO,EAAa/J,OAAS+J,EAAaxO,OAASwO,EAAazO,IACzDyO,EAAa5J,EAAI4J,EAAatO,KAC9BsO,EAAa1J,EAAI0J,EAAazO,IACvByO,CACT,CInC2BE,CAAgB3mB,GAAUf,GAAWA,EAAUA,EAAQ2nB,gBAAkBtJ,GAAmB9C,EAAMC,SAAS/B,QAASiN,EAAUE,EAAc3K,GACjK2L,EAAsBlP,GAAsB6C,EAAMC,SAAS9B,WAC3DoG,EAAgBmG,GAAe,CACjCvM,UAAWkO,EACX5nB,QAAS0hB,EACTzF,SAAU,WACVnC,UAAWA,IAET+N,EAAmB1C,GAAiBne,OAAO2U,OAAO,GAAI+F,EAAY5B,IAClEgI,EAAoBhB,IAAmBrN,GAASoO,EAAmBD,EAGnEG,EAAkB,CACpBhP,IAAKoO,EAAmBpO,IAAM+O,EAAkB/O,IAAMyG,EAAczG,IACpEC,OAAQ8O,EAAkB9O,OAASmO,EAAmBnO,OAASwG,EAAcxG,OAC7EE,KAAMiO,EAAmBjO,KAAO4O,EAAkB5O,KAAOsG,EAActG,KACvED,MAAO6O,EAAkB7O,MAAQkO,EAAmBlO,MAAQuG,EAAcvG,OAExE+O,EAAazM,EAAMwE,cAAckB,OAErC,GAAI6F,IAAmBrN,IAAUuO,EAAY,CAC3C,IAAI/G,EAAS+G,EAAWlO,GACxB9S,OAAOqC,KAAK0e,GAAiBtM,SAAQ,SAAU9Q,GAC7C,IAAIsd,EAAW,CAAChP,GAAOD,IAAQrT,QAAQgF,IAAQ,EAAI,GAAK,EACpDsV,EAAO,CAAClH,GAAKC,IAAQrT,QAAQgF,IAAQ,EAAI,IAAM,IACnDod,EAAgBpd,IAAQsW,EAAOhB,GAAQgI,CAC7C,GACA,CAEE,OAAOF,CACT,CC5De,SAASG,GAAqB3M,EAAOS,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIsK,EAAWtK,EACXlC,EAAYwM,EAASxM,UACrB4M,EAAWJ,EAASI,SACpBE,EAAeN,EAASM,aACxBzG,EAAUmG,EAASnG,QACnBgI,EAAiB7B,EAAS6B,eAC1BC,EAAwB9B,EAAS+B,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EzG,EAAYL,GAAaxH,GACzBC,EAAa4H,EAAYwG,EAAiBxO,GAAsBA,GAAoB3M,QAAO,SAAU8M,GACvG,OAAOwH,GAAaxH,KAAe6H,CACvC,IAAOvI,GACDmP,EAAoBxO,EAAW/M,QAAO,SAAU8M,GAClD,OAAOuO,EAAsB1iB,QAAQmU,IAAc,CACvD,IAEmC,IAA7ByO,EAAkBnnB,SACpBmnB,EAAoBxO,GAQtB,IAAIyO,EAAYD,EAAkB3O,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAauM,GAAe9K,EAAO,CACrCzB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdzG,QAASA,IACR5D,GAAiBzC,IACbD,CACX,GAAK,IACH,OAAO7S,OAAOqC,KAAKmf,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,EACpC,GACA,CC2FA,MAAAC,GAAe,CACbnlB,KAAM,OACN2X,SAAS,EACTC,MAAO,OACPzX,GA5HF,SAAc0X,GACZ,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACfvY,EAAO6X,EAAK7X,KAEhB,IAAI8X,EAAMwE,cAActc,GAAMolB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoB9M,EAAQoK,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBhN,EAAQiN,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BnN,EAAQoN,mBACtCjJ,EAAUnE,EAAQmE,QAClBuG,EAAW1K,EAAQ0K,SACnBE,EAAe5K,EAAQ4K,aACvBI,EAAchL,EAAQgL,YACtBqC,EAAwBrN,EAAQmM,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBrM,EAAQqM,sBAChCiB,EAAqB/N,EAAMS,QAAQlC,UACnCkG,EAAgBzD,GAAiB+M,GAEjCF,EAAqBD,IADHnJ,IAAkBsJ,GACqCnB,EAjC/E,SAAuCrO,GACrC,GAAIyC,GAAiBzC,KAAeX,GAClC,MAAO,GAGT,IAAIoQ,EAAoBvF,GAAqBlK,GAC7C,MAAO,CAACoK,GAA8BpK,GAAYyP,EAAmBrF,GAA8BqF,GACrG,CA0B6IC,CAA8BF,GAA3E,CAACtF,GAAqBsF,KAChHvP,EAAa,CAACuP,GAAoB9Y,OAAO4Y,GAAoBxP,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIrJ,OAAO+L,GAAiBzC,KAAeX,GAAO+O,GAAqB3M,EAAO,CACnFzB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdzG,QAASA,EACTgI,eAAgBA,EAChBE,sBAAuBA,IACpBvO,EACT,GAAK,IACC2P,EAAgBlO,EAAM6E,MAAM1G,UAC5BgI,EAAanG,EAAM6E,MAAM3G,OACzBiQ,EAAY,IAAIve,IAChBwe,GAAqB,EACrBC,EAAwB7P,EAAW,GAE9B8P,EAAI,EAAGA,EAAI9P,EAAW3Y,OAAQyoB,IAAK,CAC1C,IAAI/P,EAAYC,EAAW8P,GAEvBC,EAAiBvN,GAAiBzC,GAElCiQ,EAAmBzI,GAAaxH,KAAeT,GAC/C2Q,EAAa,CAACjR,GAAKC,IAAQrT,QAAQmkB,IAAmB,EACtD5J,EAAM8J,EAAa,QAAU,SAC7BrF,EAAW0B,GAAe9K,EAAO,CACnCzB,UAAWA,EACX4M,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACb7G,QAASA,IAEP8J,EAAoBD,EAAaD,EAAmB9Q,GAAQC,GAAO6Q,EAAmB/Q,GAASD,GAE/F0Q,EAAcvJ,GAAOwB,EAAWxB,KAClC+J,EAAoBjG,GAAqBiG,IAG3C,IAAIC,EAAmBlG,GAAqBiG,GACxCE,EAAS,GAUb,GARIpB,GACFoB,EAAOjmB,KAAKygB,EAASmF,IAAmB,GAGtCZ,GACFiB,EAAOjmB,KAAKygB,EAASsF,IAAsB,EAAGtF,EAASuF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,CACb,IAAQ,CACFT,EAAwB9P,EACxB6P,GAAqB,EACrB,KACN,CAEID,EAAUre,IAAIyO,EAAWqQ,EAC7B,CAEE,GAAIR,EAqBF,IAnBA,IAEIW,EAAQ,SAAeC,GACzB,IAAIC,EAAmBzQ,EAAW7S,MAAK,SAAU4S,GAC/C,IAAIqQ,EAAST,EAAUze,IAAI6O,GAE3B,GAAIqQ,EACF,OAAOA,EAAOxgB,MAAM,EAAG4gB,GAAIH,OAAM,SAAUC,GACzC,OAAOA,CACnB,GAEA,IAEM,GAAIG,EAEF,OADAZ,EAAwBY,EACjB,OAEf,EAEaD,EAnBYpC,EAAiB,EAAI,EAmBZoC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpChP,EAAMzB,YAAc8P,IACtBrO,EAAMwE,cAActc,GAAMolB,OAAQ,EAClCtN,EAAMzB,UAAY8P,EAClBrO,EAAMkP,OAAQ,EA5GlB,CA8GA,EAQEpJ,iBAAkB,CAAC,UACnBpR,KAAM,CACJ4Y,OAAO,IC7IX,SAAS6B,GAAe/F,EAAUS,EAAMuF,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB/M,EAAG,EACHE,EAAG,IAIA,CACL/E,IAAK4L,EAAS5L,IAAMqM,EAAK3H,OAASkN,EAAiB7M,EACnD7E,MAAO0L,EAAS1L,MAAQmM,EAAK5H,MAAQmN,EAAiB/M,EACtD5E,OAAQ2L,EAAS3L,OAASoM,EAAK3H,OAASkN,EAAiB7M,EACzD5E,KAAMyL,EAASzL,KAAOkM,EAAK5H,MAAQmN,EAAiB/M,EAExD,CAEA,SAASgN,GAAsBjG,GAC7B,MAAO,CAAC5L,GAAKE,GAAOD,GAAQE,IAAM2R,MAAK,SAAUC,GAC/C,OAAOnG,EAASmG,IAAS,CAC7B,GACA,CA+BA,MAAAC,GAAe,CACbtnB,KAAM,OACN2X,SAAS,EACTC,MAAO,OACPgG,iBAAkB,CAAC,mBACnBzd,GAlCF,SAAc0X,GACZ,IAAIC,EAAQD,EAAKC,MACb9X,EAAO6X,EAAK7X,KACZgmB,EAAgBlO,EAAM6E,MAAM1G,UAC5BgI,EAAanG,EAAM6E,MAAM3G,OACzBkR,EAAmBpP,EAAMwE,cAAciL,gBACvCC,EAAoB5E,GAAe9K,EAAO,CAC5CuL,eAAgB,cAEdoE,EAAoB7E,GAAe9K,EAAO,CAC5CyL,aAAa,IAEXmE,EAA2BT,GAAeO,EAAmBxB,GAC7D2B,EAAsBV,GAAeQ,EAAmBxJ,EAAYiJ,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7C7P,EAAMwE,cAActc,GAAQ,CAC1B0nB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpB/P,EAAM1O,WAAW4M,OAASzS,OAAO2U,OAAO,GAAIJ,EAAM1O,WAAW4M,OAAQ,CACnE,+BAAgC4R,EAChC,sBAAuBC,GAE3B,GCJAC,GAAe,CACb9nB,KAAM,SACN2X,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACX1Y,GA5BF,SAAgBiY,GACd,IAAIN,EAAQM,EAAMN,MACdS,EAAUH,EAAMG,QAChBvY,EAAOoY,EAAMpY,KACb+nB,EAAkBxP,EAAQiF,OAC1BA,OAA6B,IAApBuK,EAA6B,CAAC,EAAG,GAAKA,EAC/Cvb,EAAO8J,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAWsG,EAAOa,GACxD,IAAIjB,EAAgBzD,GAAiBzC,GACjC2R,EAAiB,CAACvS,GAAMH,IAAKpT,QAAQqa,IAAkB,GAAK,EAAI,EAEhE1E,EAAyB,mBAAX2F,EAAwBA,EAAOja,OAAO2U,OAAO,GAAIyE,EAAO,CACxEtG,UAAWA,KACPmH,EACFyK,EAAWpQ,EAAK,GAChBqQ,EAAWrQ,EAAK,GAIpB,OAFAoQ,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAACvS,GAAMD,IAAOtT,QAAQqa,IAAkB,EAAI,CACjDpC,EAAG+N,EACH7N,EAAG4N,GACD,CACF9N,EAAG8N,EACH5N,EAAG6N,EAEP,CASqBC,CAAwB9R,EAAWyB,EAAM6E,MAAOa,GAC1DpH,CACX,GAAK,IACCgS,EAAwB5b,EAAKsL,EAAMzB,WACnC8D,EAAIiO,EAAsBjO,EAC1BE,EAAI+N,EAAsB/N,EAEW,MAArCvC,EAAMwE,cAAcD,gBACtBvE,EAAMwE,cAAcD,cAAclC,GAAKA,EACvCrC,EAAMwE,cAAcD,cAAchC,GAAKA,GAGzCvC,EAAMwE,cAActc,GAAQwM,CAC9B,GC1BA6b,GAAe,CACbroB,KAAM,gBACN2X,SAAS,EACTC,MAAO,OACPzX,GApBF,SAAuB0X,GACrB,IAAIC,EAAQD,EAAKC,MACb9X,EAAO6X,EAAK7X,KAKhB8X,EAAMwE,cAActc,GAAQwiB,GAAe,CACzCvM,UAAW6B,EAAM6E,MAAM1G,UACvB1Z,QAASub,EAAM6E,MAAM3G,OACrBwC,SAAU,WACVnC,UAAWyB,EAAMzB,WAErB,EAQE7J,KAAM,ICgHR8b,GAAe,CACbtoB,KAAM,kBACN2X,SAAS,EACTC,MAAO,OACPzX,GA/HF,SAAyB0X,GACvB,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACfvY,EAAO6X,EAAK7X,KACZqlB,EAAoB9M,EAAQoK,SAC5B2C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBhN,EAAQiN,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDtC,EAAW1K,EAAQ0K,SACnBE,EAAe5K,EAAQ4K,aACvBI,EAAchL,EAAQgL,YACtB7G,EAAUnE,EAAQmE,QAClB6L,EAAkBhQ,EAAQiQ,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBlQ,EAAQmQ,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDvH,EAAW0B,GAAe9K,EAAO,CACnCmL,SAAUA,EACVE,aAAcA,EACdzG,QAASA,EACT6G,YAAaA,IAEXhH,EAAgBzD,GAAiBhB,EAAMzB,WACvC6H,EAAYL,GAAa/F,EAAMzB,WAC/BsS,GAAmBzK,EACnByE,EAAWjH,GAAyBa,GACpCiJ,ECrCY,MDqCS7C,ECrCH,IAAM,IDsCxBtG,EAAgBvE,EAAMwE,cAAcD,cACpC2J,EAAgBlO,EAAM6E,MAAM1G,UAC5BgI,EAAanG,EAAM6E,MAAM3G,OACzB4S,EAA4C,mBAAjBF,EAA8BA,EAAanlB,OAAO2U,OAAO,GAAIJ,EAAM6E,MAAO,CACvGtG,UAAWyB,EAAMzB,aACbqS,EACFG,EAA2D,iBAAtBD,EAAiC,CACxEjG,SAAUiG,EACVpD,QAASoD,GACPrlB,OAAO2U,OAAO,CAChByK,SAAU,EACV6C,QAAS,GACRoD,GACCE,EAAsBhR,EAAMwE,cAAckB,OAAS1F,EAAMwE,cAAckB,OAAO1F,EAAMzB,WAAa,KACjG7J,EAAO,CACT2N,EAAG,EACHE,EAAG,GAGL,GAAKgC,EAAL,CAIA,GAAIiJ,EAAe,CACjB,IAAIyD,EAEAC,EAAwB,MAAbrG,EAAmBrN,GAAMG,GACpCwT,EAAuB,MAAbtG,EAAmBpN,GAASC,GACtCiH,EAAmB,MAAbkG,EAAmB,SAAW,QACpCnF,EAASnB,EAAcsG,GACvBtgB,EAAMmb,EAAS0D,EAAS8H,GACxB5mB,EAAMob,EAAS0D,EAAS+H,GACxBC,EAAWV,GAAUvK,EAAWxB,GAAO,EAAI,EAC3C0M,EAASjL,IAActI,GAAQoQ,EAAcvJ,GAAOwB,EAAWxB,GAC/D2M,EAASlL,IAActI,IAASqI,EAAWxB,IAAQuJ,EAAcvJ,GAGjEL,EAAetE,EAAMC,SAASW,MAC9BmE,EAAY2L,GAAUpM,EAAe7B,GAAc6B,GAAgB,CACrErC,MAAO,EACPC,OAAQ,GAENqP,EAAqBvR,EAAMwE,cAAc,oBAAsBxE,EAAMwE,cAAc,oBAAoBI,QxBhFtG,CACLpH,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GwB6EF6T,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAW7N,GAAO,EAAGqK,EAAcvJ,GAAMI,EAAUJ,IACnDgN,EAAYd,EAAkB3C,EAAcvJ,GAAO,EAAIyM,EAAWM,EAAWF,EAAkBT,EAA4BlG,SAAWwG,EAASK,EAAWF,EAAkBT,EAA4BlG,SACxM+G,EAAYf,GAAmB3C,EAAcvJ,GAAO,EAAIyM,EAAWM,EAAWD,EAAkBV,EAA4BlG,SAAWyG,EAASI,EAAWD,EAAkBV,EAA4BlG,SACzMzF,EAAoBpF,EAAMC,SAASW,OAASuC,GAAgBnD,EAAMC,SAASW,OAC3EiR,EAAezM,EAAiC,MAAbyF,EAAmBzF,EAAkB+E,WAAa,EAAI/E,EAAkBgF,YAAc,EAAI,EAC7H0H,EAAwH,OAAjGb,EAA+C,MAAvBD,OAA8B,EAASA,EAAoBnG,IAAqBoG,EAAwB,EAEvJc,EAAYrM,EAASkM,EAAYE,EACjCE,EAAkBnO,GAAO6M,EAAS3M,GAAQxZ,EAF9Bmb,EAASiM,EAAYG,EAAsBD,GAEKtnB,EAAKmb,EAAQgL,EAAS5M,GAAQxZ,EAAKynB,GAAaznB,GAChHia,EAAcsG,GAAYmH,EAC1Btd,EAAKmW,GAAYmH,EAAkBtM,CACvC,CAEE,GAAIiI,EAAc,CAChB,IAAIsE,EAEAC,EAAyB,MAAbrH,EAAmBrN,GAAMG,GAErCwU,GAAwB,MAAbtH,EAAmBpN,GAASC,GAEvC0U,GAAU7N,EAAcmJ,GAExB2E,GAAmB,MAAZ3E,EAAkB,SAAW,QAEpC4E,GAAOF,GAAUhJ,EAAS8I,GAE1BK,GAAOH,GAAUhJ,EAAS+I,IAE1BK,IAAuD,IAAxC,CAAChV,GAAKG,IAAMvT,QAAQqa,GAEnCgO,GAAyH,OAAjGR,EAAgD,MAAvBjB,OAA8B,EAASA,EAAoBtD,IAAoBuE,EAAyB,EAEzJS,GAAaF,GAAeF,GAAOF,GAAUlE,EAAcmE,IAAQlM,EAAWkM,IAAQI,GAAuB1B,EAA4BrD,QAEzIiF,GAAaH,GAAeJ,GAAUlE,EAAcmE,IAAQlM,EAAWkM,IAAQI,GAAuB1B,EAA4BrD,QAAU6E,GAE5IK,GAAmBlC,GAAU8B,G1BzH9B,SAAwBjoB,EAAK8E,EAAO/E,GACzC,IAAIuoB,EAAIhP,GAAOtZ,EAAK8E,EAAO/E,GAC3B,OAAOuoB,EAAIvoB,EAAMA,EAAMuoB,CACzB,C0BsHoDC,CAAeJ,GAAYN,GAASO,IAAc9O,GAAO6M,EAASgC,GAAaJ,GAAMF,GAAS1B,EAASiC,GAAaJ,IAEpKhO,EAAcmJ,GAAWkF,GACzBle,EAAKgZ,GAAWkF,GAAmBR,EACvC,CAEEpS,EAAMwE,cAActc,GAAQwM,CAvE9B,CAwEA,EAQEoR,iBAAkB,CAAC,WE1HN,SAASiN,GAAiBC,EAAyB9P,EAAcuD,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICnBoCnH,ECJO7a,EFuBvCwuB,EAA0BxT,GAAcyD,GACxCgQ,EAAuBzT,GAAcyD,IAf3C,SAAyBze,GACvB,IAAIolB,EAAOplB,EAAQ0Y,wBACf2E,EAASb,GAAM4I,EAAK5H,OAASxd,EAAQud,aAAe,EACpDD,EAASd,GAAM4I,EAAK3H,QAAUzd,EAAQ6C,cAAgB,EAC1D,OAAkB,IAAXwa,GAA2B,IAAXC,CACzB,CAU4DoR,CAAgBjQ,GACtEnc,EAAkB+b,GAAmBI,GACrC2G,EAAO1M,GAAsB6V,EAAyBE,EAAsBzM,GAC5EyB,EAAS,CACXW,WAAY,EACZE,UAAW,GAET1C,EAAU,CACZhE,EAAG,EACHE,EAAG,GAkBL,OAfI0Q,IAA4BA,IAA4BxM,MACxB,SAA9BtH,GAAY+D,IAChBgG,GAAeniB,MACbmhB,GCnCgC5I,EDmCT4D,KClCd7D,GAAUC,IAAUG,GAAcH,GCJxC,CACLuJ,YAFyCpkB,EDQb6a,GCNRuJ,WACpBE,UAAWtkB,EAAQskB,WDGZH,GAAgBtJ,IDoCnBG,GAAcyD,KAChBmD,EAAUlJ,GAAsB+F,GAAc,IACtCb,GAAKa,EAAakH,WAC1B/D,EAAQ9D,GAAKW,EAAaiH,WACjBpjB,IACTsf,EAAQhE,EAAI4G,GAAoBliB,KAI7B,CACLsb,EAAGwH,EAAKlM,KAAOuK,EAAOW,WAAaxC,EAAQhE,EAC3CE,EAAGsH,EAAKrM,IAAM0K,EAAOa,UAAY1C,EAAQ9D,EACzCN,MAAO4H,EAAK5H,MACZC,OAAQ2H,EAAK3H,OAEjB,CGvDA,SAASlI,GAAMoZ,GACb,IAAIpd,EAAM,IAAIpG,IACVyjB,EAAU,IAAIpoB,IACdqoB,EAAS,GAKb,SAASpG,EAAKqG,GACZF,EAAQ/b,IAAIic,EAASrrB,MACN,GAAG+M,OAAOse,EAASxS,UAAY,GAAIwS,EAASzN,kBAAoB,IACtE5F,SAAQ,SAAUsT,GACzB,IAAKH,EAAQlnB,IAAIqnB,GAAM,CACrB,IAAIC,EAAczd,EAAItG,IAAI8jB,GAEtBC,GACFvG,EAAKuG,EAEf,CACA,IACIH,EAAO3qB,KAAK4qB,EAChB,CAQE,OAzBAH,EAAUlT,SAAQ,SAAUqT,GAC1Bvd,EAAIlG,IAAIyjB,EAASrrB,KAAMqrB,EAC3B,IAiBEH,EAAUlT,SAAQ,SAAUqT,GACrBF,EAAQlnB,IAAIonB,EAASrrB,OAExBglB,EAAKqG,EAEX,IACSD,CACT,CChBA,IAAII,GAAkB,CACpBnV,UAAW,SACX6U,UAAW,GACX1S,SAAU,YAGZ,SAASiT,KACP,IAAK,IAAItB,EAAOuB,UAAU/tB,OAAQ0I,EAAO,IAAI6B,MAAMiiB,GAAOwB,EAAO,EAAGA,EAAOxB,EAAMwB,IAC/EtlB,EAAKslB,GAAQD,UAAUC,GAGzB,OAAQtlB,EAAK+gB,MAAK,SAAU7qB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQ0Y,sBACvC,GACA,CAEO,SAAS2W,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCT,GAAkBS,EAC3E,OAAO,SAAsBhW,EAAWD,EAAQuC,QAC9B,IAAZA,IACFA,EAAU2T,GAGZ,IC/C6B/rB,EAC3BgsB,ED8CErU,EAAQ,CACVzB,UAAW,SACX+V,iBAAkB,GAClB7T,QAAShV,OAAO2U,OAAO,GAAIsT,GAAiBU,GAC5C5P,cAAe,GACfvE,SAAU,CACR9B,UAAWA,EACXD,OAAQA,GAEV5M,WAAY,GACZ6O,OAAQ,IAENoU,EAAmB,GACnBC,GAAc,EACdzkB,EAAW,CACbiQ,MAAOA,EACPyU,WAAY,SAAoBC,GAC9B,IAAIjU,EAAsC,mBAArBiU,EAAkCA,EAAiB1U,EAAMS,SAAWiU,EACzFC,IACA3U,EAAMS,QAAUhV,OAAO2U,OAAO,GAAIgU,EAAgBpU,EAAMS,QAASA,GACjET,EAAMqI,cAAgB,CACpBlK,UAAW3Y,GAAU2Y,GAAaqL,GAAkBrL,GAAaA,EAAUiO,eAAiB5C,GAAkBrL,EAAUiO,gBAAkB,GAC1IlO,OAAQsL,GAAkBtL,IAI5B,IEzE4BkV,EAC9BwB,EFwEMN,EDvCG,SAAwBlB,GAErC,IAAIkB,EAAmBta,GAAMoZ,GAE7B,OAAOlU,GAAeb,QAAO,SAAUC,EAAKwB,GAC1C,OAAOxB,EAAIrJ,OAAOqf,EAAiB7iB,QAAO,SAAU8hB,GAClD,OAAOA,EAASzT,QAAUA,CAChC,IACA,GAAK,GACL,CC8B+B+U,EEzEKzB,EFyEsB,GAAGne,OAAOif,EAAkBlU,EAAMS,QAAQ2S,WExE9FwB,EAASxB,EAAU/U,QAAO,SAAUuW,EAAQE,GAC9C,IAAIC,EAAWH,EAAOE,EAAQ5sB,MAK9B,OAJA0sB,EAAOE,EAAQ5sB,MAAQ6sB,EAAWtpB,OAAO2U,OAAO,GAAI2U,EAAUD,EAAS,CACrErU,QAAShV,OAAO2U,OAAO,GAAI2U,EAAStU,QAASqU,EAAQrU,SACrD/L,KAAMjJ,OAAO2U,OAAO,GAAI2U,EAASrgB,KAAMogB,EAAQpgB,QAC5CogB,EACEF,CACX,GAAK,IAEInpB,OAAOqC,KAAK8mB,GAAQ5e,KAAI,SAAU5G,GACvC,OAAOwlB,EAAOxlB,EAClB,MFsGQ,OAvCA4Q,EAAMsU,iBAAmBA,EAAiB7iB,QAAO,SAAUujB,GACzD,OAAOA,EAAEnV,OACnB,IAoJMG,EAAMsU,iBAAiBpU,SAAQ,SAAU0G,GACvC,IAAI1e,EAAO0e,EAAM1e,KACb+sB,EAAgBrO,EAAMnG,QACtBA,OAA4B,IAAlBwU,EAA2B,GAAKA,EAC1C5U,EAASuG,EAAMvG,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAI6U,EAAY7U,EAAO,CACrBL,MAAOA,EACP9X,KAAMA,EACN6H,SAAUA,EACV0Q,QAASA,IAKX8T,EAAiB5rB,KAAKusB,GAFT,WAAkB,EAGzC,CACA,IAjIenlB,EAASwY,QACxB,EAMM4M,YAAa,WACX,IAAIX,EAAJ,CAIA,IAAIY,EAAkBpV,EAAMC,SACxB9B,EAAYiX,EAAgBjX,UAC5BD,EAASkX,EAAgBlX,OAG7B,GAAKyV,GAAiBxV,EAAWD,GAAjC,CASA8B,EAAM6E,MAAQ,CACZ1G,UAAW4U,GAAiB5U,EAAWgF,GAAgBjF,GAAoC,UAA3B8B,EAAMS,QAAQC,UAC9ExC,OAAQuE,GAAcvE,IAOxB8B,EAAMkP,OAAQ,EACdlP,EAAMzB,UAAYyB,EAAMS,QAAQlC,UAKhCyB,EAAMsU,iBAAiBpU,SAAQ,SAAUqT,GACvC,OAAOvT,EAAMwE,cAAc+O,EAASrrB,MAAQuD,OAAO2U,OAAO,GAAImT,EAAS7e,KACjF,IAGQ,IAAK,IAAIvK,EAAQ,EAAGA,EAAQ6V,EAAMsU,iBAAiBzuB,OAAQsE,IAUzD,IAAoB,IAAhB6V,EAAMkP,MAAV,CAMA,IAAImG,EAAwBrV,EAAMsU,iBAAiBnqB,GAC/C9B,EAAKgtB,EAAsBhtB,GAC3BitB,EAAyBD,EAAsB5U,QAC/CsK,OAAsC,IAA3BuK,EAAoC,GAAKA,EACpDptB,EAAOmtB,EAAsBntB,KAEf,mBAAPG,IACT2X,EAAQ3X,EAAG,CACT2X,MAAOA,EACPS,QAASsK,EACT7iB,KAAMA,EACN6H,SAAUA,KACNiQ,EAdlB,MAHYA,EAAMkP,OAAQ,EACd/kB,GAAS,CAnCrB,CAbA,CAmEA,EAGMoe,QClM2BlgB,EDkMV,WACf,OAAO,IAAIktB,SAAQ,SAAUC,GAC3BzlB,EAASolB,cACTK,EAAQxV,EAClB,GACA,ECrMS,WAUL,OATKqU,IACHA,EAAU,IAAIkB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBpB,OAAU1f,EACV6gB,EAAQntB,IAClB,GACA,KAGWgsB,CACX,GD2LMqB,QAAS,WACPf,IACAH,GAAc,CACtB,GAGI,IAAKb,GAAiBxV,EAAWD,GAK/B,OAAOnO,EAmCT,SAAS4kB,IACPJ,EAAiBrU,SAAQ,SAAU7X,GACjC,OAAOA,GACf,IACMksB,EAAmB,EACzB,CAEI,OAvCAxkB,EAAS0kB,WAAWhU,GAASgV,MAAK,SAAUzV,IACrCwU,GAAe/T,EAAQkV,eAC1BlV,EAAQkV,cAAc3V,EAE9B,IAmCWjQ,CACX,CACA,CACO,IAAI6lB,GAA4B9B,KG1PnC8B,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAClM,GAAgBzD,GAAesR,GAAeC,MCMlEF,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAClM,GAAgBzD,GAAesR,GAAeC,GAAapQ,GAAQqQ,GAAMtG,GAAiB7O,GAAOlE,M,+lBCiBnHvU,GAAO,WAOP6tB,GAAe,UACfC,GAAiB,YAOjBC,GAAwB,6BACxBC,GAA0B,+BAG1Bva,GAAkB,OAOlBhH,GAAuB,4DACvBwhB,GAA8B,GAAExhB,UAChCyhB,GAAgB,iBAKhBC,GAAgB1uB,IAAU,UAAY,YACtC2uB,GAAmB3uB,IAAU,YAAc,UAC3C4uB,GAAmB5uB,IAAU,aAAe,eAC5C6uB,GAAsB7uB,IAAU,eAAiB,aACjD8uB,GAAkB9uB,IAAU,aAAe,cAC3C+uB,GAAiB/uB,IAAU,cAAgB,aAI3CkK,GAAU,CACd8kB,WAAW,EACXzL,SAAU,kBACV0L,QAAS,UACTnR,OAAQ,CAAC,EAAG,GACZoR,aAAc,KACd3Y,UAAW,UAGPpM,GAAc,CAClB6kB,UAAW,mBACXzL,SAAU,mBACV0L,QAAS,SACTnR,OAAQ,0BACRoR,aAAc,yBACd3Y,UAAW,2BAOb,MAAM4Y,WAAiB7jB,EACrBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAEfxF,KAAKsqB,QAAU,KACftqB,KAAKuqB,QAAUvqB,KAAK0G,SAAS9M,WAE7BoG,KAAKwqB,MAAQliB,EAAeY,KAAKlJ,KAAK0G,SAAUijB,IAAe,IAC7DrhB,EAAeS,KAAK/I,KAAK0G,SAAUijB,IAAe,IAClDrhB,EAAeG,QAAQkhB,GAAe3pB,KAAKuqB,SAC7CvqB,KAAKyqB,UAAYzqB,KAAK0qB,eACvB,CAGUtlB,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,OAAOA,EACR,CAGD2M,SACE,OAAOpI,KAAK+P,WAAa/P,KAAKgQ,OAAShQ,KAAKiQ,MAC7C,CAEDA,OACE,GAAIpW,EAAWmG,KAAK0G,WAAa1G,KAAK+P,WACpC,OAGF,MAAMlQ,EAAgB,CACpBA,cAAeG,KAAK0G,UAKtB,IAFkBlG,EAAaoB,QAAQ5B,KAAK0G,SA3F5B,mBA2FkD7G,GAEpDoC,iBAAd,CAUA,GANAjC,KAAK2qB,gBAMD,iBAAkBnyB,SAAS6B,kBAAoB2F,KAAKuqB,QAAQ7wB,QAtFxC,eAuFtB,IAAK,MAAM3B,IAAW,GAAGwQ,UAAU/P,SAASwC,KAAK0N,UAC/ClI,EAAaa,GAAGtJ,EAAS,YAAa2C,GAI1CsF,KAAK0G,SAASkkB,QACd5qB,KAAK0G,SAASlC,aAAa,iBAAiB,GAE5CxE,KAAKwqB,MAAMxwB,UAAU4Q,IAAIsE,IACzBlP,KAAK0G,SAAS1M,UAAU4Q,IAAIsE,IAC5B1O,EAAaoB,QAAQ5B,KAAK0G,SAjHT,oBAiHgC7G,EAnBhD,CAoBF,CAEDmQ,OACE,GAAInW,EAAWmG,KAAK0G,YAAc1G,KAAK+P,WACrC,OAGF,MAAMlQ,EAAgB,CACpBA,cAAeG,KAAK0G,UAGtB1G,KAAK6qB,cAAchrB,EACpB,CAEDgH,UACM7G,KAAKsqB,SACPtqB,KAAKsqB,QAAQtB,UAGfviB,MAAMI,SACP,CAEDgV,SACE7b,KAAKyqB,UAAYzqB,KAAK0qB,gBAClB1qB,KAAKsqB,SACPtqB,KAAKsqB,QAAQzO,QAEhB,CAGDgP,cAAchrB,GAEZ,IADkBW,EAAaoB,QAAQ5B,KAAK0G,SApJ5B,mBAoJkD7G,GACpDoC,iBAAd,CAMA,GAAI,iBAAkBzJ,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGwQ,UAAU/P,SAASwC,KAAK0N,UAC/ClI,EAAaC,IAAI1I,EAAS,YAAa2C,GAIvCsF,KAAKsqB,SACPtqB,KAAKsqB,QAAQtB,UAGfhpB,KAAKwqB,MAAMxwB,UAAU4J,OAAOsL,IAC5BlP,KAAK0G,SAAS1M,UAAU4J,OAAOsL,IAC/BlP,KAAK0G,SAASlC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBzE,KAAKwqB,MAAO,UAC5ChqB,EAAaoB,QAAQ5B,KAAK0G,SAxKR,qBAwKgC7G,EAlBjD,CAmBF,CAED0F,WAAWC,GAGT,GAAgC,iBAFhCA,EAASiB,MAAMlB,WAAWC,IAERiM,YAA2B3Y,EAAU0M,EAAOiM,YACV,mBAA3CjM,EAAOiM,UAAUhB,sBAGxB,MAAM,IAAInK,UAAW,GAAE7K,GAAK8K,+GAG9B,OAAOf,CACR,CAEDmlB,gBACE,QAAsB,IAAXG,GACT,MAAM,IAAIxkB,UAAU,gEAGtB,IAAIykB,EAAmB/qB,KAAK0G,SAEG,WAA3B1G,KAAK2G,QAAQ8K,UACfsZ,EAAmB/qB,KAAKuqB,QACfzxB,EAAUkH,KAAK2G,QAAQ8K,WAChCsZ,EAAmB7xB,EAAW8G,KAAK2G,QAAQ8K,WACA,iBAA3BzR,KAAK2G,QAAQ8K,YAC7BsZ,EAAmB/qB,KAAK2G,QAAQ8K,WAGlC,MAAM2Y,EAAepqB,KAAKgrB,mBAC1BhrB,KAAKsqB,QAAUQ,GAAoBC,EAAkB/qB,KAAKwqB,MAAOJ,EAClE,CAEDra,WACE,OAAO/P,KAAKwqB,MAAMxwB,UAAUC,SAASiV,GACtC,CAED+b,gBACE,MAAMC,EAAiBlrB,KAAKuqB,QAE5B,GAAIW,EAAelxB,UAAUC,SAzMN,WA0MrB,OAAO+vB,GAGT,GAAIkB,EAAelxB,UAAUC,SA5MJ,aA6MvB,OAAOgwB,GAGT,GAAIiB,EAAelxB,UAAUC,SA/MA,iBAgN3B,MAhMsB,MAmMxB,GAAIixB,EAAelxB,UAAUC,SAlNE,mBAmN7B,MAnMyB,SAuM3B,MAAMkxB,EAAkF,QAA1E5xB,iBAAiByG,KAAKwqB,OAAOhxB,iBAAiB,iBAAiBlB,OAE7E,OAAI4yB,EAAelxB,UAAUC,SA7NP,UA8NbkxB,EAAQtB,GAAmBD,GAG7BuB,EAAQpB,GAAsBD,EACtC,CAEDY,gBACE,OAAkD,OAA3C1qB,KAAK0G,SAAShN,QA5ND,UA6NrB,CAED0xB,aACE,MAAMpS,OAAEA,GAAWhZ,KAAK2G,QAExB,MAAsB,iBAAXqS,EACFA,EAAO3gB,MAAM,KAAKiR,KAAI3G,GAASjG,OAAOwR,SAASvL,EAAO,MAGzC,mBAAXqW,EACFqS,GAAcrS,EAAOqS,EAAYrrB,KAAK0G,UAGxCsS,CACR,CAEDgS,mBACE,MAAMM,EAAwB,CAC5BzZ,UAAW7R,KAAKirB,gBAChBvE,UAAW,CAAC,CACVlrB,KAAM,kBACNuY,QAAS,CACP0K,SAAUze,KAAK2G,QAAQ8X,WAG3B,CACEjjB,KAAM,SACNuY,QAAS,CACPiF,OAAQhZ,KAAKorB,iBAcnB,OARIprB,KAAKyqB,WAAsC,WAAzBzqB,KAAK2G,QAAQwjB,WACjC7lB,EAAYC,iBAAiBvE,KAAKwqB,MAAO,SAAU,UACnDc,EAAsB5E,UAAY,CAAC,CACjClrB,KAAM,cACN2X,SAAS,KAIN,IACFmY,KACsC,mBAA9BtrB,KAAK2G,QAAQyjB,aAA8BpqB,KAAK2G,QAAQyjB,aAAakB,GAAyBtrB,KAAK2G,QAAQyjB,aAEzH,CAEDmB,iBAAgB7oB,IAAEA,EAAF1F,OAAOA,IACrB,MAAMiQ,EAAQ3E,EAAerJ,KA5QF,8DA4Q+Be,KAAKwqB,OAAOzlB,QAAOhN,GAAWqB,EAAUrB,KAE7FkV,EAAM9T,QAMXgE,EAAqB8P,EAAOjQ,EAAQ0F,IAAQ6mB,IAAiBtc,EAAM9U,SAAS6E,IAAS4tB,OACtF,CAGqBzjB,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOqiB,GAAS1iB,oBAAoB3H,KAAMwF,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IANJ,CAOF,GACF,CAEgB2B,kBAACjI,GAChB,GA/TuB,IA+TnBA,EAAMmJ,QAAiD,UAAfnJ,EAAMwB,MAlUtC,QAkU0DxB,EAAMwD,IAC1E,OAGF,MAAM8oB,EAAcljB,EAAerJ,KAAKyqB,IAExC,IAAK,MAAMthB,KAAUojB,EAAa,CAChC,MAAMC,EAAUpB,GAASjjB,YAAYgB,GACrC,IAAKqjB,IAAyC,IAA9BA,EAAQ9kB,QAAQujB,UAC9B,SAGF,MAAMwB,EAAexsB,EAAMwsB,eACrBC,EAAeD,EAAavzB,SAASszB,EAAQjB,OACnD,GACEkB,EAAavzB,SAASszB,EAAQ/kB,WACC,WAA9B+kB,EAAQ9kB,QAAQujB,YAA2ByB,GACb,YAA9BF,EAAQ9kB,QAAQujB,WAA2ByB,EAE5C,SAIF,GAAIF,EAAQjB,MAAMvwB,SAASiF,EAAMlC,UAA4B,UAAfkC,EAAMwB,MAzV1C,QAyV8DxB,EAAMwD,KAAoB,qCAAqC2D,KAAKnH,EAAMlC,OAAO0K,UACvJ,SAGF,MAAM7H,EAAgB,CAAEA,cAAe4rB,EAAQ/kB,UAE5B,UAAfxH,EAAMwB,OACRb,EAAc4H,WAAavI,GAG7BusB,EAAQZ,cAAchrB,EACvB,CACF,CAE2BsH,6BAACjI,GAI3B,MAAM0sB,EAAU,kBAAkBvlB,KAAKnH,EAAMlC,OAAO0K,SAC9CmkB,EA7WS,WA6WO3sB,EAAMwD,IACtBopB,EAAkB,CAACxC,GAAcC,IAAgBpxB,SAAS+G,EAAMwD,KAEtE,IAAKopB,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGF3sB,EAAMqD,iBAGN,MAAMwpB,EAAkB/rB,KAAK4I,QAAQV,IACnClI,KACCsI,EAAeS,KAAK/I,KAAMkI,IAAsB,IAC/CI,EAAeY,KAAKlJ,KAAMkI,IAAsB,IAChDI,EAAeG,QAAQP,GAAsBhJ,EAAMY,eAAelG,YAEhEyJ,EAAWgnB,GAAS1iB,oBAAoBokB,GAE9C,GAAID,EAIF,OAHA5sB,EAAM8sB,kBACN3oB,EAAS4M,YACT5M,EAASkoB,gBAAgBrsB,GAIvBmE,EAAS0M,aACX7Q,EAAM8sB,kBACN3oB,EAAS2M,OACT+b,EAAgBnB,QAEnB,EAOHpqB,EAAaa,GAAG7I,SAAUixB,GAAwBvhB,GAAsBmiB,GAAS4B,uBACjFzrB,EAAaa,GAAG7I,SAAUixB,GAAwBE,GAAeU,GAAS4B,uBAC1EzrB,EAAaa,GAAG7I,SAAUgxB,GAAsBa,GAAS6B,YACzD1rB,EAAaa,GAAG7I,SA7Yc,6BA6YkB6xB,GAAS6B,YACzD1rB,EAAaa,GAAG7I,SAAUgxB,GAAsBthB,IAAsB,SAAUhJ,GAC9EA,EAAMqD,iBACN8nB,GAAS1iB,oBAAoB3H,MAAMoI,QACpC,IAMDhN,EAAmBivB,ICpbnB,MAAM8B,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJ1mB,cACE7F,KAAK0G,SAAWlO,SAASwC,IAC1B,CAGDwxB,WAEE,MAAMC,EAAgBj0B,SAAS6B,gBAAgBwe,YAC/C,OAAOlb,KAAK+M,IAAI5P,OAAO4xB,WAAaD,EACrC,CAEDzc,OACE,MAAMuF,EAAQvV,KAAKwsB,WACnBxsB,KAAK2sB,mBAEL3sB,KAAK4sB,sBAAsB5sB,KAAK0G,SAAU2lB,IAAkBQ,GAAmBA,EAAkBtX,IAEjGvV,KAAK4sB,sBAAsBT,GAAwBE,IAAkBQ,GAAmBA,EAAkBtX,IAC1GvV,KAAK4sB,sBAAsBR,GAAyBE,IAAiBO,GAAmBA,EAAkBtX,GAC3G,CAEDiN,QACExiB,KAAK8sB,wBAAwB9sB,KAAK0G,SAAU,YAC5C1G,KAAK8sB,wBAAwB9sB,KAAK0G,SAAU2lB,IAC5CrsB,KAAK8sB,wBAAwBX,GAAwBE,IACrDrsB,KAAK8sB,wBAAwBV,GAAyBE,GACvD,CAEDS,gBACE,OAAO/sB,KAAKwsB,WAAa,CAC1B,CAGDG,mBACE3sB,KAAKgtB,sBAAsBhtB,KAAK0G,SAAU,YAC1C1G,KAAK0G,SAAS6J,MAAMmM,SAAW,QAChC,CAEDkQ,sBAAsB50B,EAAUi1B,EAAe3xB,GAC7C,MAAM4xB,EAAiBltB,KAAKwsB,WAW5BxsB,KAAKmtB,2BAA2Bn1B,GAVHD,IAC3B,GAAIA,IAAYiI,KAAK0G,UAAY5L,OAAO4xB,WAAa30B,EAAQ8gB,YAAcqU,EACzE,OAGFltB,KAAKgtB,sBAAsBj1B,EAASk1B,GACpC,MAAMJ,EAAkB/xB,OAAOvB,iBAAiBxB,GAASyB,iBAAiByzB,GAC1El1B,EAAQwY,MAAM6c,YAAYH,EAAgB,GAAE3xB,EAASoB,OAAOC,WAAWkwB,QAAvE,GAIH,CAEDG,sBAAsBj1B,EAASk1B,GAC7B,MAAMI,EAAct1B,EAAQwY,MAAM/W,iBAAiByzB,GAC/CI,GACF/oB,EAAYC,iBAAiBxM,EAASk1B,EAAeI,EAExD,CAEDP,wBAAwB90B,EAAUi1B,GAahCjtB,KAAKmtB,2BAA2Bn1B,GAZHD,IAC3B,MAAM4K,EAAQ2B,EAAYY,iBAAiBnN,EAASk1B,GAEtC,OAAVtqB,GAKJ2B,EAAYG,oBAAoB1M,EAASk1B,GACzCl1B,EAAQwY,MAAM6c,YAAYH,EAAetqB,IALvC5K,EAAQwY,MAAM+c,eAAeL,EAK/B,GAIH,CAEDE,2BAA2Bn1B,EAAUu1B,GACnC,GAAIz0B,EAAUd,GACZu1B,EAASv1B,QAIX,IAAK,MAAMw1B,KAAOllB,EAAerJ,KAAKjH,EAAUgI,KAAK0G,UACnD6mB,EAASC,EAEZ,EC/FH,MAEMte,GAAkB,OAClBue,GAAmB,wBAEnBroB,GAAU,CACdsoB,UAAW,iBACXC,cAAe,KACfzmB,YAAY,EACZ9N,WAAW,EACXw0B,YAAa,QAGTvoB,GAAc,CAClBqoB,UAAW,SACXC,cAAe,kBACfzmB,WAAY,UACZ9N,UAAW,UACXw0B,YAAa,oBAOf,MAAMC,WAAiB1oB,EACrBU,YAAYL,GACViB,QACAzG,KAAK2G,QAAU3G,KAAKuF,WAAWC,GAC/BxF,KAAK8tB,aAAc,EACnB9tB,KAAK0G,SAAW,IACjB,CAGUtB,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MA3CS,UA4CV,CAGDwU,KAAK3U,GACH,IAAK0E,KAAK2G,QAAQvN,UAEhB,YADA8C,EAAQZ,GAIV0E,KAAK+tB,UAEL,MAAMh2B,EAAUiI,KAAKguB,cACjBhuB,KAAK2G,QAAQO,YACfvM,EAAO5C,GAGTA,EAAQiC,UAAU4Q,IAAIsE,IAEtBlP,KAAKiuB,mBAAkB,KACrB/xB,EAAQZ,EAAR,GAEH,CAED0U,KAAK1U,GACE0E,KAAK2G,QAAQvN,WAKlB4G,KAAKguB,cAAch0B,UAAU4J,OAAOsL,IAEpClP,KAAKiuB,mBAAkB,KACrBjuB,KAAK6G,UACL3K,EAAQZ,EAAR,KARAY,EAAQZ,EAUX,CAEDuL,UACO7G,KAAK8tB,cAIVttB,EAAaC,IAAIT,KAAK0G,SAAU+mB,IAEhCztB,KAAK0G,SAAS9C,SACd5D,KAAK8tB,aAAc,EACpB,CAGDE,cACE,IAAKhuB,KAAK0G,SAAU,CAClB,MAAMwnB,EAAW11B,SAAS21B,cAAc,OACxCD,EAASR,UAAY1tB,KAAK2G,QAAQ+mB,UAC9B1tB,KAAK2G,QAAQO,YACfgnB,EAASl0B,UAAU4Q,IAjGH,QAoGlB5K,KAAK0G,SAAWwnB,CACjB,CAED,OAAOluB,KAAK0G,QACb,CAEDhB,kBAAkBF,GAGhB,OADAA,EAAOooB,YAAc10B,EAAWsM,EAAOooB,aAChCpoB,CACR,CAEDuoB,UACE,GAAI/tB,KAAK8tB,YACP,OAGF,MAAM/1B,EAAUiI,KAAKguB,cACrBhuB,KAAK2G,QAAQinB,YAAYQ,OAAOr2B,GAEhCyI,EAAaa,GAAGtJ,EAAS01B,IAAiB,KACxCvxB,EAAQ8D,KAAK2G,QAAQgnB,cAArB,IAGF3tB,KAAK8tB,aAAc,CACpB,CAEDG,kBAAkB3yB,GAChBa,EAAuBb,EAAU0E,KAAKguB,cAAehuB,KAAK2G,QAAQO,WACnE,EClIH,MAEMJ,GAAa,gBAMbunB,GAAmB,WAEnBjpB,GAAU,CACdkpB,WAAW,EACXC,YAAa,MAGTlpB,GAAc,CAClBipB,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkBrpB,EACtBU,YAAYL,GACViB,QACAzG,KAAK2G,QAAU3G,KAAKuF,WAAWC,GAC/BxF,KAAKyuB,WAAY,EACjBzuB,KAAK0uB,qBAAuB,IAC7B,CAGUtpB,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MA1CS,WA2CV,CAGDkzB,WACM3uB,KAAKyuB,YAILzuB,KAAK2G,QAAQ2nB,WACftuB,KAAK2G,QAAQ4nB,YAAY3D,QAG3BpqB,EAAaC,IAAIjI,SAAUsO,IAC3BtG,EAAaa,GAAG7I,SArDG,wBAqDsB0G,GAASc,KAAK4uB,eAAe1vB,KACtEsB,EAAaa,GAAG7I,SArDO,4BAqDsB0G,GAASc,KAAK6uB,eAAe3vB,KAE1Ec,KAAKyuB,WAAY,EAClB,CAEDK,aACO9uB,KAAKyuB,YAIVzuB,KAAKyuB,WAAY,EACjBjuB,EAAaC,IAAIjI,SAAUsO,IAC5B,CAGD8nB,eAAe1vB,GACb,MAAMqvB,YAAEA,GAAgBvuB,KAAK2G,QAE7B,GAAIzH,EAAMlC,SAAWxE,UAAY0G,EAAMlC,SAAWuxB,GAAeA,EAAYt0B,SAASiF,EAAMlC,QAC1F,OAGF,MAAMuW,EAAWjL,EAAec,kBAAkBmlB,GAE1B,IAApBhb,EAASpa,OACXo1B,EAAY3D,QACH5qB,KAAK0uB,uBAAyBL,GACvC9a,EAASA,EAASpa,OAAS,GAAGyxB,QAE9BrX,EAAS,GAAGqX,OAEf,CAEDiE,eAAe3vB,GApFD,QAqFRA,EAAMwD,MAIV1C,KAAK0uB,qBAAuBxvB,EAAM6vB,SAAWV,GAxFzB,UAyFrB,EC3FH,MAQMW,GAAgB,kBAChBC,GAAc,gBAQdC,GAAkB,aAElBhgB,GAAkB,OAClBigB,GAAoB,eAOpB/pB,GAAU,CACd8oB,UAAU,EACVtD,OAAO,EACPjf,UAAU,GAGNtG,GAAc,CAClB6oB,SAAU,mBACVtD,MAAO,UACPjf,SAAU,WAOZ,MAAMyjB,WAAc5oB,EAClBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAEfxF,KAAKqvB,QAAU/mB,EAAeG,QAxBV,gBAwBmCzI,KAAK0G,UAC5D1G,KAAKsvB,UAAYtvB,KAAKuvB,sBACtBvvB,KAAKwvB,WAAaxvB,KAAKyvB,uBACvBzvB,KAAK+P,UAAW,EAChB/P,KAAKuP,kBAAmB,EACxBvP,KAAK0vB,WAAa,IAAInD,GAEtBvsB,KAAKuM,oBACN,CAGUnH,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MAnES,OAoEV,CAGD2M,OAAOvI,GACL,OAAOG,KAAK+P,SAAW/P,KAAKgQ,OAAShQ,KAAKiQ,KAAKpQ,EAChD,CAEDoQ,KAAKpQ,GACCG,KAAK+P,UAAY/P,KAAKuP,kBAIR/O,EAAaoB,QAAQ5B,KAAK0G,SAAUuoB,GAAY,CAChEpvB,kBAGYoC,mBAIdjC,KAAK+P,UAAW,EAChB/P,KAAKuP,kBAAmB,EAExBvP,KAAK0vB,WAAW1f,OAEhBxX,SAASwC,KAAKhB,UAAU4Q,IAAIskB,IAE5BlvB,KAAK2vB,gBAEL3vB,KAAKsvB,UAAUrf,MAAK,IAAMjQ,KAAK4vB,aAAa/vB,KAC7C,CAEDmQ,OACOhQ,KAAK+P,WAAY/P,KAAKuP,mBAIT/O,EAAaoB,QAAQ5B,KAAK0G,SAnG5B,iBAqGFzE,mBAIdjC,KAAK+P,UAAW,EAChB/P,KAAKuP,kBAAmB,EACxBvP,KAAKwvB,WAAWV,aAEhB9uB,KAAK0G,SAAS1M,UAAU4J,OAAOsL,IAE/BlP,KAAKiH,gBAAe,IAAMjH,KAAK6vB,cAAc7vB,KAAK0G,SAAU1G,KAAK4O,gBAClE,CAED/H,UACE,IAAK,MAAMipB,IAAe,CAACh1B,OAAQkF,KAAKqvB,SACtC7uB,EAAaC,IAAIqvB,EAxHJ,aA2Hf9vB,KAAKsvB,UAAUzoB,UACf7G,KAAKwvB,WAAWV,aAChBroB,MAAMI,SACP,CAEDkpB,eACE/vB,KAAK2vB,eACN,CAGDJ,sBACE,OAAO,IAAI1B,GAAS,CAClBz0B,UAAW2H,QAAQf,KAAK2G,QAAQunB,UAChChnB,WAAYlH,KAAK4O,eAEpB,CAED6gB,uBACE,OAAO,IAAIjB,GAAU,CACnBD,YAAavuB,KAAK0G,UAErB,CAEDkpB,aAAa/vB,GAENrH,SAASwC,KAAKf,SAAS+F,KAAK0G,WAC/BlO,SAASwC,KAAKozB,OAAOpuB,KAAK0G,UAG5B1G,KAAK0G,SAAS6J,MAAM4Z,QAAU,QAC9BnqB,KAAK0G,SAAShC,gBAAgB,eAC9B1E,KAAK0G,SAASlC,aAAa,cAAc,GACzCxE,KAAK0G,SAASlC,aAAa,OAAQ,UACnCxE,KAAK0G,SAAS2V,UAAY,EAE1B,MAAM2T,EAAY1nB,EAAeG,QAxIT,cAwIsCzI,KAAKqvB,SAC/DW,IACFA,EAAU3T,UAAY,GAGxB1hB,EAAOqF,KAAK0G,UAEZ1G,KAAK0G,SAAS1M,UAAU4Q,IAAIsE,IAa5BlP,KAAKiH,gBAXsB,KACrBjH,KAAK2G,QAAQikB,OACf5qB,KAAKwvB,WAAWb,WAGlB3uB,KAAKuP,kBAAmB,EACxB/O,EAAaoB,QAAQ5B,KAAK0G,SArKX,iBAqKkC,CAC/C7G,iBADF,GAKsCG,KAAKqvB,QAASrvB,KAAK4O,cAC5D,CAEDrC,qBACE/L,EAAaa,GAAGrB,KAAK0G,SA1KM,4BA0K2BxH,IACpD,GArLa,WAqLTA,EAAMwD,IAIV,OAAI1C,KAAK2G,QAAQgF,UACfzM,EAAMqD,sBACNvC,KAAKgQ,aAIPhQ,KAAKiwB,4BAAL,IAGFzvB,EAAaa,GAAGvG,OA3LE,mBA2LoB,KAChCkF,KAAK+P,WAAa/P,KAAKuP,kBACzBvP,KAAK2vB,eACN,IAGHnvB,EAAaa,GAAGrB,KAAK0G,SA/LQ,8BA+L2BxH,IAEtDsB,EAAac,IAAItB,KAAK0G,SAlMC,0BAkM8BwpB,IAC/ClwB,KAAK0G,WAAaxH,EAAMlC,QAAUgD,KAAK0G,WAAawpB,EAAOlzB,SAIjC,WAA1BgD,KAAK2G,QAAQunB,SAKbluB,KAAK2G,QAAQunB,UACfluB,KAAKgQ,OALLhQ,KAAKiwB,6BAMN,GAZH,GAeH,CAEDJ,aACE7vB,KAAK0G,SAAS6J,MAAM4Z,QAAU,OAC9BnqB,KAAK0G,SAASlC,aAAa,eAAe,GAC1CxE,KAAK0G,SAAShC,gBAAgB,cAC9B1E,KAAK0G,SAAShC,gBAAgB,QAC9B1E,KAAKuP,kBAAmB,EAExBvP,KAAKsvB,UAAUtf,MAAK,KAClBxX,SAASwC,KAAKhB,UAAU4J,OAAOsrB,IAC/BlvB,KAAKmwB,oBACLnwB,KAAK0vB,WAAWlN,QAChBhiB,EAAaoB,QAAQ5B,KAAK0G,SAAUsoB,GAApC,GAEH,CAEDpgB,cACE,OAAO5O,KAAK0G,SAAS1M,UAAUC,SA7NX,OA8NrB,CAEDg2B,6BAEE,GADkBzvB,EAAaoB,QAAQ5B,KAAK0G,SA5OlB,0BA6OZzE,iBACZ,OAGF,MAAMmuB,EAAqBpwB,KAAK0G,SAASoX,aAAetlB,SAAS6B,gBAAgBue,aAC3EyX,EAAmBrwB,KAAK0G,SAAS6J,MAAMqM,UAEpB,WAArByT,GAAiCrwB,KAAK0G,SAAS1M,UAAUC,SAASk1B,MAIjEiB,IACHpwB,KAAK0G,SAAS6J,MAAMqM,UAAY,UAGlC5c,KAAK0G,SAAS1M,UAAU4Q,IAAIukB,IAC5BnvB,KAAKiH,gBAAe,KAClBjH,KAAK0G,SAAS1M,UAAU4J,OAAOurB,IAC/BnvB,KAAKiH,gBAAe,KAClBjH,KAAK0G,SAAS6J,MAAMqM,UAAYyT,CAAhC,GACCrwB,KAAKqvB,QAFR,GAGCrvB,KAAKqvB,SAERrvB,KAAK0G,SAASkkB,QACf,CAMD+E,gBACE,MAAMS,EAAqBpwB,KAAK0G,SAASoX,aAAetlB,SAAS6B,gBAAgBue,aAC3EsU,EAAiBltB,KAAK0vB,WAAWlD,WACjC8D,EAAoBpD,EAAiB,EAE3C,GAAIoD,IAAsBF,EAAoB,CAC5C,MAAMrqB,EAAW7K,IAAU,cAAgB,eAC3C8E,KAAK0G,SAAS6J,MAAMxK,GAAa,GAAEmnB,KACpC,CAED,IAAKoD,GAAqBF,EAAoB,CAC5C,MAAMrqB,EAAW7K,IAAU,eAAiB,cAC5C8E,KAAK0G,SAAS6J,MAAMxK,GAAa,GAAEmnB,KACpC,CACF,CAEDiD,oBACEnwB,KAAK0G,SAAS6J,MAAMggB,YAAc,GAClCvwB,KAAK0G,SAAS6J,MAAMigB,aAAe,EACpC,CAGqBrpB,uBAAC3B,EAAQ3F,GAC7B,OAAOG,KAAK+H,MAAK,WACf,MAAMC,EAAOonB,GAAMznB,oBAAoB3H,KAAMwF,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQ3F,EANZ,CAOF,GACF,EAOHW,EAAaa,GAAG7I,SA9Sc,0BAUD,4BAoSyC,SAAU0G,GAC9E,MAAMlC,EAAStE,EAAuBsH,MAElC,CAAC,IAAK,QAAQ7H,SAAS6H,KAAK0H,UAC9BxI,EAAMqD,iBAGR/B,EAAac,IAAItE,EAAQiyB,IAAYwB,IAC/BA,EAAUxuB,kBAKdzB,EAAac,IAAItE,EAAQgyB,IAAc,KACjC51B,EAAU4G,OACZA,KAAK4qB,OACN,GAHH,IAQF,MAAM8F,EAAcpoB,EAAeG,QA5Tf,eA6ThBioB,GACFtB,GAAMhoB,YAAYspB,GAAa1gB,OAGpBof,GAAMznB,oBAAoB3K,GAElCoL,OAAOpI,KACb,IAEDsH,EAAqB8nB,IAMrBh0B,EAAmBg0B,IC7VnB,MAOMlgB,GAAkB,OAClByhB,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAKhBC,GAAwB,6BACxB9B,GAAgB,sBAOhB5pB,GAAU,CACd8oB,UAAU,EACVviB,UAAU,EACV6P,QAAQ,GAGJnW,GAAc,CAClB6oB,SAAU,mBACVviB,SAAU,UACV6P,OAAQ,WAOV,MAAMuV,WAAkBvqB,EACtBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAEfxF,KAAK+P,UAAW,EAChB/P,KAAKsvB,UAAYtvB,KAAKuvB,sBACtBvvB,KAAKwvB,WAAaxvB,KAAKyvB,uBACvBzvB,KAAKuM,oBACN,CAGUnH,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MA5DS,WA6DV,CAGD2M,OAAOvI,GACL,OAAOG,KAAK+P,SAAW/P,KAAKgQ,OAAShQ,KAAKiQ,KAAKpQ,EAChD,CAEDoQ,KAAKpQ,GACCG,KAAK+P,UAISvP,EAAaoB,QAAQ5B,KAAK0G,SA5D5B,oBA4DkD,CAAE7G,kBAEtDoC,mBAIdjC,KAAK+P,UAAW,EAChB/P,KAAKsvB,UAAUrf,OAEVjQ,KAAK2G,QAAQ6U,SAChB,IAAI+Q,IAAkBvc,OAGxBhQ,KAAK0G,SAASlC,aAAa,cAAc,GACzCxE,KAAK0G,SAASlC,aAAa,OAAQ,UACnCxE,KAAK0G,SAAS1M,UAAU4Q,IAAI+lB,IAY5B3wB,KAAKiH,gBAVoB,KAClBjH,KAAK2G,QAAQ6U,SAAUxb,KAAK2G,QAAQunB,UACvCluB,KAAKwvB,WAAWb,WAGlB3uB,KAAK0G,SAAS1M,UAAU4Q,IAAIsE,IAC5BlP,KAAK0G,SAAS1M,UAAU4J,OAAO+sB,IAC/BnwB,EAAaoB,QAAQ5B,KAAK0G,SAnFX,qBAmFkC,CAAE7G,iBAAnD,GAGoCG,KAAK0G,UAAU,GACtD,CAEDsJ,OACOhQ,KAAK+P,WAIQvP,EAAaoB,QAAQ5B,KAAK0G,SA7F5B,qBA+FFzE,mBAIdjC,KAAKwvB,WAAWV,aAChB9uB,KAAK0G,SAASsqB,OACdhxB,KAAK+P,UAAW,EAChB/P,KAAK0G,SAAS1M,UAAU4Q,IAAIgmB,IAC5B5wB,KAAKsvB,UAAUtf,OAcfhQ,KAAKiH,gBAZoB,KACvBjH,KAAK0G,SAAS1M,UAAU4J,OAAOsL,GAAiB0hB,IAChD5wB,KAAK0G,SAAShC,gBAAgB,cAC9B1E,KAAK0G,SAAShC,gBAAgB,QAEzB1E,KAAK2G,QAAQ6U,SAChB,IAAI+Q,IAAkB/J,QAGxBhiB,EAAaoB,QAAQ5B,KAAK0G,SAAUsoB,GAApC,GAGoChvB,KAAK0G,UAAU,IACtD,CAEDG,UACE7G,KAAKsvB,UAAUzoB,UACf7G,KAAKwvB,WAAWV,aAChBroB,MAAMI,SACP,CAGD0oB,sBACE,MAUMn2B,EAAY2H,QAAQf,KAAK2G,QAAQunB,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtBt0B,YACA8N,YAAY,EACZ0mB,YAAa5tB,KAAK0G,SAAS9M,WAC3B+zB,cAAev0B,EAjBK,KACU,WAA1B4G,KAAK2G,QAAQunB,SAKjBluB,KAAKgQ,OAJHxP,EAAaoB,QAAQ5B,KAAK0G,SAAUoqB,GAItC,EAW2C,MAE9C,CAEDrB,uBACE,OAAO,IAAIjB,GAAU,CACnBD,YAAavuB,KAAK0G,UAErB,CAED6F,qBACE/L,EAAaa,GAAGrB,KAAK0G,SAvJM,gCAuJ2BxH,IAtKvC,WAuKTA,EAAMwD,MAIL1C,KAAK2G,QAAQgF,SAKlB3L,KAAKgQ,OAJHxP,EAAaoB,QAAQ5B,KAAK0G,SAAUoqB,IAItC,GAEH,CAGqB3pB,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAO+oB,GAAUppB,oBAAoB3H,KAAMwF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAOpN,WAAW,MAAmB,gBAAXoN,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQxF,KANZ,CAOF,GACF,EAOHQ,EAAaa,GAAG7I,SA5Lc,8BAGD,gCAyLyC,SAAU0G,GAC9E,MAAMlC,EAAStE,EAAuBsH,MAMtC,GAJI,CAAC,IAAK,QAAQ7H,SAAS6H,KAAK0H,UAC9BxI,EAAMqD,iBAGJ1I,EAAWmG,MACb,OAGFQ,EAAac,IAAItE,EAAQgyB,IAAc,KAEjC51B,EAAU4G,OACZA,KAAK4qB,OACN,IAIH,MAAM8F,EAAcpoB,EAAeG,QAAQooB,IACvCH,GAAeA,IAAgB1zB,GACjC+zB,GAAU3pB,YAAYspB,GAAa1gB,OAGxB+gB,GAAUppB,oBAAoB3K,GACtCoL,OAAOpI,KACb,IAEDQ,EAAaa,GAAGvG,OAvOa,8BAuOgB,KAC3C,IAAK,MAAM9C,KAAYsQ,EAAerJ,KAAK4xB,IACzCE,GAAUppB,oBAAoB3P,GAAUiY,MACzC,IAGHzP,EAAaa,GAAGvG,OA/NM,uBA+NgB,KACpC,IAAK,MAAM/C,KAAWuQ,EAAerJ,KAAK,gDACG,UAAvC1F,iBAAiBxB,GAAS+b,UAC5Bid,GAAUppB,oBAAoB5P,GAASiY,MAE1C,IAGH1I,EAAqBypB,IAMrB31B,EAAmB21B,ICjRnB,MAAME,GAAgB,IAAI1yB,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI2yB,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmB,CAAChd,EAAWid,KACnC,MAAMC,EAAgBld,EAAU1B,SAASrO,cAEzC,OAAIgtB,EAAqBl5B,SAASm5B,IAC5BL,GAAcxxB,IAAI6xB,IACbvwB,QAAQmwB,GAAiB7qB,KAAK+N,EAAUmd,YAAcJ,GAAiB9qB,KAAK+N,EAAUmd,YAO1FF,EAAqBtsB,QAAOysB,GAAkBA,aAA0BprB,SAC5Ewc,MAAK6O,GAASA,EAAMprB,KAAKirB,IAD5B,EAIWI,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAlCP,kBAmC7BjR,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BkR,KAAM,GACNjR,EAAG,GACHkR,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ3Q,EAAG,GACHlU,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChD8kB,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IC/DA/tB,GAAU,CACdguB,UAAW1B,GACX2B,QAAS,GACTC,WAAY,GACZhW,MAAM,EACNiW,UAAU,EACVC,WAAY,KACZC,SAAU,eAGNpuB,GAAc,CAClB+tB,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZhW,KAAM,UACNiW,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzBC,MAAO,iCACP37B,SAAU,oBAOZ,MAAM47B,WAAwBzuB,EAC5BU,YAAYL,GACViB,QACAzG,KAAK2G,QAAU3G,KAAKuF,WAAWC,EAChC,CAGUJ,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MA/CS,iBAgDV,CAGDo4B,aACE,OAAO90B,OAAOC,OAAOgB,KAAK2G,QAAQ0sB,SAC/B/pB,KAAI9D,GAAUxF,KAAK8zB,yBAAyBtuB,KAC5CT,OAAOhE,QACX,CAEDgzB,aACE,OAAO/zB,KAAK6zB,aAAa16B,OAAS,CACnC,CAED66B,cAAcX,GAGZ,OAFArzB,KAAKi0B,cAAcZ,GACnBrzB,KAAK2G,QAAQ0sB,QAAU,IAAKrzB,KAAK2G,QAAQ0sB,WAAYA,GAC9CrzB,IACR,CAEDk0B,SACE,MAAMC,EAAkB37B,SAAS21B,cAAc,OAC/CgG,EAAgBC,UAAYp0B,KAAKq0B,eAAer0B,KAAK2G,QAAQ8sB,UAE7D,IAAK,MAAOz7B,EAAUs8B,KAASv1B,OAAO6D,QAAQ5C,KAAK2G,QAAQ0sB,SACzDrzB,KAAKu0B,YAAYJ,EAAiBG,EAAMt8B,GAG1C,MAAMy7B,EAAWU,EAAgBzrB,SAAS,GACpC4qB,EAAatzB,KAAK8zB,yBAAyB9zB,KAAK2G,QAAQ2sB,YAM9D,OAJIA,GACFG,EAASz5B,UAAU4Q,OAAO0oB,EAAWj7B,MAAM,MAGtCo7B,CACR,CAGD9tB,iBAAiBH,GACfiB,MAAMd,iBAAiBH,GACvBxF,KAAKi0B,cAAczuB,EAAO6tB,QAC3B,CAEDY,cAAcO,GACZ,IAAK,MAAOx8B,EAAUq7B,KAAYt0B,OAAO6D,QAAQ4xB,GAC/C/tB,MAAMd,iBAAiB,CAAE3N,WAAU27B,MAAON,GAAWK,GAExD,CAEDa,YAAYd,EAAUJ,EAASr7B,GAC7B,MAAMy8B,EAAkBnsB,EAAeG,QAAQzQ,EAAUy7B,GAEpDgB,KAILpB,EAAUrzB,KAAK8zB,yBAAyBT,IAOpCv6B,EAAUu6B,GACZrzB,KAAK00B,sBAAsBx7B,EAAWm6B,GAAUoB,GAI9Cz0B,KAAK2G,QAAQ2W,KACfmX,EAAgBL,UAAYp0B,KAAKq0B,eAAehB,GAIlDoB,EAAgBE,YAActB,EAd5BoB,EAAgB7wB,SAenB,CAEDywB,eAAeG,GACb,OAAOx0B,KAAK2G,QAAQ4sB,SDzDjB,SAAsBqB,EAAYxB,EAAWyB,GAClD,IAAKD,EAAWz7B,OACd,OAAOy7B,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIh6B,OAAOi6B,WACKC,gBAAgBJ,EAAY,aACxDrhB,EAAW,GAAGhL,UAAUusB,EAAgB95B,KAAKqF,iBAAiB,MAEpE,IAAK,MAAMtI,KAAWwb,EAAU,CAC9B,MAAM0hB,EAAcl9B,EAAQ2a,SAASrO,cAErC,IAAKtF,OAAOqC,KAAKgyB,GAAWj7B,SAAS88B,GAAc,CACjDl9B,EAAQ6L,SAER,QACD,CAED,MAAMsxB,EAAgB,GAAG3sB,UAAUxQ,EAAQ6M,YACrCuwB,EAAoB,GAAG5sB,OAAO6qB,EAAU,MAAQ,GAAIA,EAAU6B,IAAgB,IAEpF,IAAK,MAAM7gB,KAAa8gB,EACjB9D,GAAiBhd,EAAW+gB,IAC/Bp9B,EAAQ2M,gBAAgB0P,EAAU1B,SAGvC,CAED,OAAOoiB,EAAgB95B,KAAKo5B,SAC7B,CCwBkCgB,CAAaZ,EAAKx0B,KAAK2G,QAAQysB,UAAWpzB,KAAK2G,QAAQ6sB,YAAcgB,CACrG,CAEDV,yBAAyBU,GACvB,MAAsB,mBAARA,EAAqBA,EAAIx0B,MAAQw0B,CAChD,CAEDE,sBAAsB38B,EAAS08B,GAC7B,GAAIz0B,KAAK2G,QAAQ2W,KAGf,OAFAmX,EAAgBL,UAAY,QAC5BK,EAAgBrG,OAAOr2B,GAIzB08B,EAAgBE,YAAc58B,EAAQ48B,WACvC,ECzIH,MACMU,GAAwB,IAAI92B,IAAI,CAAC,WAAY,YAAa,eAE1D+2B,GAAkB,OAElBpmB,GAAkB,OAGlBqmB,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO56B,IAAU,OAAS,QAC1B66B,OAAQ,SACRC,KAAM96B,IAAU,QAAU,QAGtBkK,GAAU,CACdguB,UAAW1B,GACXuE,WAAW,EACXxX,SAAU,kBACVyX,WAAW,EACXC,YAAa,GACbC,MAAO,EACPjV,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C7D,MAAM,EACNtE,OAAQ,CAAC,EAAG,GACZnH,UAAW,MACXuY,aAAc,KACdmJ,UAAU,EACVC,WAAY,KACZx7B,UAAU,EACVy7B,SAAU,+GAIV4C,MAAO,GACPz0B,QAAS,eAGLyD,GAAc,CAClB+tB,UAAW,SACX6C,UAAW,UACXxX,SAAU,mBACVyX,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPjV,mBAAoB,QACpB7D,KAAM,UACNtE,OAAQ,0BACRnH,UAAW,oBACXuY,aAAc,yBACdmJ,SAAU,UACVC,WAAY,kBACZx7B,SAAU,mBACVy7B,SAAU,SACV4C,MAAO,4BACPz0B,QAAS,UAOX,MAAM00B,WAAgB9vB,EACpBX,YAAY9N,EAASyN,GACnB,QAAsB,IAAXslB,GACT,MAAM,IAAIxkB,UAAU,+DAGtBG,MAAM1O,EAASyN,GAGfxF,KAAKu2B,YAAa,EAClBv2B,KAAKw2B,SAAW,EAChBx2B,KAAKy2B,WAAa,KAClBz2B,KAAK02B,eAAiB,GACtB12B,KAAKsqB,QAAU,KACftqB,KAAK22B,iBAAmB,KACxB32B,KAAK42B,YAAc,KAGnB52B,KAAK62B,IAAM,KAEX72B,KAAK82B,gBAEA92B,KAAK2G,QAAQ3O,UAChBgI,KAAK+2B,WAER,CAGU3xB,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MAxHS,SAyHV,CAGDu7B,SACEh3B,KAAKu2B,YAAa,CACnB,CAEDU,UACEj3B,KAAKu2B,YAAa,CACnB,CAEDW,gBACEl3B,KAAKu2B,YAAcv2B,KAAKu2B,UACzB,CAEDnuB,SACOpI,KAAKu2B,aAIVv2B,KAAK02B,eAAeS,OAASn3B,KAAK02B,eAAeS,MAC7Cn3B,KAAK+P,WACP/P,KAAKo3B,SAIPp3B,KAAKq3B,SACN,CAEDxwB,UACEgH,aAAa7N,KAAKw2B,UAElBh2B,EAAaC,IAAIT,KAAK0G,SAAShN,QAAQ67B,IAAiBC,GAAkBx1B,KAAKs3B,mBAE3Et3B,KAAK0G,SAASzO,aAAa,2BAC7B+H,KAAK0G,SAASlC,aAAa,QAASxE,KAAK0G,SAASzO,aAAa,2BAGjE+H,KAAKu3B,iBACL9wB,MAAMI,SACP,CAEDoJ,OACE,GAAoC,SAAhCjQ,KAAK0G,SAAS6J,MAAM4Z,QACtB,MAAM,IAAI7kB,MAAM,uCAGlB,IAAMtF,KAAKw3B,mBAAoBx3B,KAAKu2B,WAClC,OAGF,MAAM9F,EAAYjwB,EAAaoB,QAAQ5B,KAAK0G,SAAU1G,KAAK6F,YAAY0I,UAzJxD,SA2JTkpB,GADar9B,EAAe4F,KAAK0G,WACL1G,KAAK0G,SAASmM,cAAcxY,iBAAiBJ,SAAS+F,KAAK0G,UAE7F,GAAI+pB,EAAUxuB,mBAAqBw1B,EACjC,OAIFz3B,KAAKu3B,iBAEL,MAAMV,EAAM72B,KAAK03B,iBAEjB13B,KAAK0G,SAASlC,aAAa,mBAAoBqyB,EAAI5+B,aAAa,OAEhE,MAAMi+B,UAAEA,GAAcl2B,KAAK2G,QAe3B,GAbK3G,KAAK0G,SAASmM,cAAcxY,gBAAgBJ,SAAS+F,KAAK62B,OAC7DX,EAAU9H,OAAOyI,GACjBr2B,EAAaoB,QAAQ5B,KAAK0G,SAAU1G,KAAK6F,YAAY0I,UA1KpC,cA6KnBvO,KAAKsqB,QAAUtqB,KAAK2qB,cAAckM,GAElCA,EAAI78B,UAAU4Q,IAAIsE,IAMd,iBAAkB1W,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGwQ,UAAU/P,SAASwC,KAAK0N,UAC/ClI,EAAaa,GAAGtJ,EAAS,YAAa2C,GAc1CsF,KAAKiH,gBAVY,KACfzG,EAAaoB,QAAQ5B,KAAK0G,SAAU1G,KAAK6F,YAAY0I,UA7LvC,WA+LU,IAApBvO,KAAKy2B,YACPz2B,KAAKo3B,SAGPp3B,KAAKy2B,YAAa,CAAlB,GAG4Bz2B,KAAK62B,IAAK72B,KAAK4O,cAC9C,CAEDoB,OACE,GAAKhQ,KAAK+P,aAIQvP,EAAaoB,QAAQ5B,KAAK0G,SAAU1G,KAAK6F,YAAY0I,UAjNxD,SAkNDtM,iBAAd,CASA,GALYjC,KAAK03B,iBACb19B,UAAU4J,OAAOsL,IAIjB,iBAAkB1W,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGwQ,UAAU/P,SAASwC,KAAK0N,UAC/ClI,EAAaC,IAAI1I,EAAS,YAAa2C,GAI3CsF,KAAK02B,eAAL,OAAqC,EACrC12B,KAAK02B,eAAL,OAAqC,EACrC12B,KAAK02B,eAAL,OAAqC,EACrC12B,KAAKy2B,WAAa,KAelBz2B,KAAKiH,gBAbY,KACXjH,KAAK23B,yBAIJ33B,KAAKy2B,YACRz2B,KAAKu3B,iBAGPv3B,KAAK0G,SAAShC,gBAAgB,oBAC9BlE,EAAaoB,QAAQ5B,KAAK0G,SAAU1G,KAAK6F,YAAY0I,UA/OtC,WA+Of,GAG4BvO,KAAK62B,IAAK72B,KAAK4O,cA/B5C,CAgCF,CAEDiN,SACM7b,KAAKsqB,SACPtqB,KAAKsqB,QAAQzO,QAEhB,CAGD2b,iBACE,OAAOz2B,QAAQf,KAAK43B,YACrB,CAEDF,iBAKE,OAJK13B,KAAK62B,MACR72B,KAAK62B,IAAM72B,KAAK63B,kBAAkB73B,KAAK42B,aAAe52B,KAAK83B,2BAGtD93B,KAAK62B,GACb,CAEDgB,kBAAkBxE,GAChB,MAAMwD,EAAM72B,KAAK+3B,oBAAoB1E,GAASa,SAG9C,IAAK2C,EACH,OAAO,KAGTA,EAAI78B,UAAU4J,OAAO0xB,GAAiBpmB,IAEtC2nB,EAAI78B,UAAU4Q,IAAK,MAAK5K,KAAK6F,YAAYpK,aAEzC,MAAMu8B,E5EjSKC,KACb,GACEA,GAAUt6B,KAAKu6B,MAnBH,IAmBSv6B,KAAKw6B,gBACnB3/B,SAAS4/B,eAAeH,IAEjC,OAAOA,CAAP,E4E4RgBI,CAAOr4B,KAAK6F,YAAYpK,MAAMsI,WAQ5C,OANA8yB,EAAIryB,aAAa,KAAMwzB,GAEnBh4B,KAAK4O,eACPioB,EAAI78B,UAAU4Q,IAAI0qB,IAGbuB,CACR,CAEDyB,WAAWjF,GACTrzB,KAAK42B,YAAcvD,EACfrzB,KAAK+P,aACP/P,KAAKu3B,iBACLv3B,KAAKiQ,OAER,CAED8nB,oBAAoB1E,GAalB,OAZIrzB,KAAK22B,iBACP32B,KAAK22B,iBAAiB3C,cAAcX,GAEpCrzB,KAAK22B,iBAAmB,IAAI/C,GAAgB,IACvC5zB,KAAK2G,QAGR0sB,UACAC,WAAYtzB,KAAK8zB,yBAAyB9zB,KAAK2G,QAAQwvB,eAIpDn2B,KAAK22B,gBACb,CAEDmB,yBACE,MAAO,CACL,iBAA0B93B,KAAK43B,YAElC,CAEDA,YACE,OAAO53B,KAAK8zB,yBAAyB9zB,KAAK2G,QAAQ0vB,QAAUr2B,KAAK0G,SAASzO,aAAa,yBACxF,CAGDsgC,6BAA6Br5B,GAC3B,OAAOc,KAAK6F,YAAY8B,oBAAoBzI,EAAMY,eAAgBE,KAAKw4B,qBACxE,CAED5pB,cACE,OAAO5O,KAAK2G,QAAQsvB,WAAcj2B,KAAK62B,KAAO72B,KAAK62B,IAAI78B,UAAUC,SAASq7B,GAC3E,CAEDvlB,WACE,OAAO/P,KAAK62B,KAAO72B,KAAK62B,IAAI78B,UAAUC,SAASiV,GAChD,CAEDyb,cAAckM,GACZ,MAAMhlB,EAA8C,mBAA3B7R,KAAK2G,QAAQkL,UACpC7R,KAAK2G,QAAQkL,UAAU9R,KAAKC,KAAM62B,EAAK72B,KAAK0G,UAC5C1G,KAAK2G,QAAQkL,UACT4mB,EAAa9C,GAAc9jB,EAAUtL,eAC3C,OAAOukB,GAAoB9qB,KAAK0G,SAAUmwB,EAAK72B,KAAKgrB,iBAAiByN,GACtE,CAEDrN,aACE,MAAMpS,OAAEA,GAAWhZ,KAAK2G,QAExB,MAAsB,iBAAXqS,EACFA,EAAO3gB,MAAM,KAAKiR,KAAI3G,GAASjG,OAAOwR,SAASvL,EAAO,MAGzC,mBAAXqW,EACFqS,GAAcrS,EAAOqS,EAAYrrB,KAAK0G,UAGxCsS,CACR,CAED8a,yBAAyBU,GACvB,MAAsB,mBAARA,EAAqBA,EAAIz0B,KAAKC,KAAK0G,UAAY8tB,CAC9D,CAEDxJ,iBAAiByN,GACf,MAAMnN,EAAwB,CAC5BzZ,UAAW4mB,EACX/R,UAAW,CACT,CACElrB,KAAM,OACNuY,QAAS,CACPoN,mBAAoBnhB,KAAK2G,QAAQwa,qBAGrC,CACE3lB,KAAM,SACNuY,QAAS,CACPiF,OAAQhZ,KAAKorB,eAGjB,CACE5vB,KAAM,kBACNuY,QAAS,CACP0K,SAAUze,KAAK2G,QAAQ8X,WAG3B,CACEjjB,KAAM,QACNuY,QAAS,CACPhc,QAAU,IAAGiI,KAAK6F,YAAYpK,eAGlC,CACED,KAAM,kBACN2X,SAAS,EACTC,MAAO,aACPzX,GAAIqM,IAGFhI,KAAK03B,iBAAiBlzB,aAAa,wBAAyBwD,EAAKsL,MAAMzB,UAAvE,KAMR,MAAO,IACFyZ,KACsC,mBAA9BtrB,KAAK2G,QAAQyjB,aAA8BpqB,KAAK2G,QAAQyjB,aAAakB,GAAyBtrB,KAAK2G,QAAQyjB,aAEzH,CAED0M,gBACE,MAAM4B,EAAW14B,KAAK2G,QAAQ/E,QAAQvJ,MAAM,KAE5C,IAAK,MAAMuJ,KAAW82B,EACpB,GAAgB,UAAZ92B,EACFpB,EAAaa,GAAGrB,KAAK0G,SAAU1G,KAAK6F,YAAY0I,UAxZpC,SAwZ4DvO,KAAK2G,QAAQ3O,UAAUkH,IAC7Ec,KAAKu4B,6BAA6Br5B,GAC1CkJ,QAAR,SAEG,GAnaU,WAmaNxG,EAA4B,CACrC,MAAM+2B,EAAU/2B,IAAY6zB,GAC1Bz1B,KAAK6F,YAAY0I,UA3ZF,cA4ZfvO,KAAK6F,YAAY0I,UA9ZL,WA+ZRqqB,EAAWh3B,IAAY6zB,GAC3Bz1B,KAAK6F,YAAY0I,UA7ZF,cA8ZfvO,KAAK6F,YAAY0I,UAhaJ,YAkaf/N,EAAaa,GAAGrB,KAAK0G,SAAUiyB,EAAS34B,KAAK2G,QAAQ3O,UAAUkH,IAC7D,MAAMusB,EAAUzrB,KAAKu4B,6BAA6Br5B,GAClDusB,EAAQiL,eAA8B,YAAfx3B,EAAMwB,KAAqBg1B,GAAgBD,KAAiB,EACnFhK,EAAQ4L,QAAR,IAEF72B,EAAaa,GAAGrB,KAAK0G,SAAUkyB,EAAU54B,KAAK2G,QAAQ3O,UAAUkH,IAC9D,MAAMusB,EAAUzrB,KAAKu4B,6BAA6Br5B,GAClDusB,EAAQiL,eAA8B,aAAfx3B,EAAMwB,KAAsBg1B,GAAgBD,IACjEhK,EAAQ/kB,SAASzM,SAASiF,EAAMW,eAElC4rB,EAAQ2L,QAAR,GAEH,CAGHp3B,KAAKs3B,kBAAoB,KACnBt3B,KAAK0G,UACP1G,KAAKgQ,MACN,EAGHxP,EAAaa,GAAGrB,KAAK0G,SAAShN,QAAQ67B,IAAiBC,GAAkBx1B,KAAKs3B,kBAC/E,CAEDP,YACE,MAAMV,EAAQr2B,KAAK0G,SAASzO,aAAa,SAEpCo+B,IAIAr2B,KAAK0G,SAASzO,aAAa,eAAkB+H,KAAK0G,SAASiuB,YAAYr8B,QAC1E0H,KAAK0G,SAASlC,aAAa,aAAc6xB,GAG3Cr2B,KAAK0G,SAASlC,aAAa,yBAA0B6xB,GACrDr2B,KAAK0G,SAAShC,gBAAgB,SAC/B,CAED2yB,SACMr3B,KAAK+P,YAAc/P,KAAKy2B,WAC1Bz2B,KAAKy2B,YAAa,GAIpBz2B,KAAKy2B,YAAa,EAElBz2B,KAAK64B,aAAY,KACX74B,KAAKy2B,YACPz2B,KAAKiQ,MACN,GACAjQ,KAAK2G,QAAQyvB,MAAMnmB,MACvB,CAEDmnB,SACMp3B,KAAK23B,yBAIT33B,KAAKy2B,YAAa,EAElBz2B,KAAK64B,aAAY,KACV74B,KAAKy2B,YACRz2B,KAAKgQ,MACN,GACAhQ,KAAK2G,QAAQyvB,MAAMpmB,MACvB,CAED6oB,YAAY97B,EAAS+7B,GACnBjrB,aAAa7N,KAAKw2B,UAClBx2B,KAAKw2B,SAAWt5B,WAAWH,EAAS+7B,EACrC,CAEDnB,uBACE,OAAO54B,OAAOC,OAAOgB,KAAK02B,gBAAgBv+B,UAAS,EACpD,CAEDoN,WAAWC,GACT,MAAMuzB,EAAiBz0B,EAAYK,kBAAkB3E,KAAK0G,UAE1D,IAAK,MAAMsyB,KAAiBj6B,OAAOqC,KAAK23B,GAClC1D,GAAsB51B,IAAIu5B,WACrBD,EAAeC,GAW1B,OAPAxzB,EAAS,IACJuzB,KACmB,iBAAXvzB,GAAuBA,EAASA,EAAS,IAEtDA,EAASxF,KAAKyF,gBAAgBD,GAC9BA,EAASxF,KAAK0F,kBAAkBF,GAChCxF,KAAK2F,iBAAiBH,GACfA,CACR,CAEDE,kBAAkBF,GAkBhB,OAjBAA,EAAO0wB,WAAiC,IAArB1wB,EAAO0wB,UAAsB19B,SAASwC,KAAO9B,EAAWsM,EAAO0wB,WAEtD,iBAAjB1wB,EAAO4wB,QAChB5wB,EAAO4wB,MAAQ,CACbnmB,KAAMzK,EAAO4wB,MACbpmB,KAAMxK,EAAO4wB,QAIW,iBAAjB5wB,EAAO6wB,QAChB7wB,EAAO6wB,MAAQ7wB,EAAO6wB,MAAMtyB,YAGA,iBAAnByB,EAAO6tB,UAChB7tB,EAAO6tB,QAAU7tB,EAAO6tB,QAAQtvB,YAG3ByB,CACR,CAEDgzB,qBACE,MAAMhzB,EAAS,GAEf,IAAK,MAAM9C,KAAO1C,KAAK2G,QACjB3G,KAAK6F,YAAYT,QAAQ1C,KAAS1C,KAAK2G,QAAQjE,KACjD8C,EAAO9C,GAAO1C,KAAK2G,QAAQjE,IAU/B,OANA8C,EAAOxN,UAAW,EAClBwN,EAAO5D,QAAU,SAKV4D,CACR,CAED+xB,iBACMv3B,KAAKsqB,UACPtqB,KAAKsqB,QAAQtB,UACbhpB,KAAKsqB,QAAU,MAGbtqB,KAAK62B,MACP72B,KAAK62B,IAAIjzB,SACT5D,KAAK62B,IAAM,KAEd,CAGqB1vB,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOsuB,GAAQ3uB,oBAAoB3H,KAAMwF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IANJ,CAOF,GACF,EAOHpK,EAAmBk7B,ICxmBnB,MAKMlxB,GAAU,IACXkxB,GAAQlxB,QACXiuB,QAAS,GACTra,OAAQ,CAAC,EAAG,GACZnH,UAAW,QACX4hB,SAAU,8IAKV7xB,QAAS,SAGLyD,GAAc,IACfixB,GAAQjxB,YACXguB,QAAS,kCAOX,MAAM4F,WAAgB3C,GAETlxB,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MAtCS,SAuCV,CAGD+7B,iBACE,OAAOx3B,KAAK43B,aAAe53B,KAAKk5B,aACjC,CAGDpB,yBACE,MAAO,CACL,kBAAkB93B,KAAK43B,YACvB,gBAAoB53B,KAAKk5B,cAE5B,CAEDA,cACE,OAAOl5B,KAAK8zB,yBAAyB9zB,KAAK2G,QAAQ0sB,QACnD,CAGqBlsB,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOixB,GAAQtxB,oBAAoB3H,KAAMwF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IANJ,CAOF,GACF,EAOHpK,EAAmB69B,IC9EnB,MAMME,GAAe,qBAIf7tB,GAAoB,SAGpB8tB,GAAwB,SASxBh0B,GAAU,CACd4T,OAAQ,KACRqgB,WAAY,eACZC,cAAc,EACdt8B,OAAQ,KACRu8B,UAAW,CAAC,GAAK,GAAK,IAGlBl0B,GAAc,CAClB2T,OAAQ,gBACRqgB,WAAY,SACZC,aAAc,UACdt8B,OAAQ,UACRu8B,UAAW,SAOb,MAAMC,WAAkBhzB,EACtBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAGfxF,KAAKy5B,aAAe,IAAIv2B,IACxBlD,KAAK05B,oBAAsB,IAAIx2B,IAC/BlD,KAAK25B,aAA6D,YAA9CpgC,iBAAiByG,KAAK0G,UAAUkW,UAA0B,KAAO5c,KAAK0G,SAC1F1G,KAAK45B,cAAgB,KACrB55B,KAAK65B,UAAY,KACjB75B,KAAK85B,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBh6B,KAAKi6B,SACN,CAGU70B,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MArES,WAsEV,CAGDw+B,UACEj6B,KAAKk6B,mCACLl6B,KAAKm6B,2BAEDn6B,KAAK65B,UACP75B,KAAK65B,UAAUO,aAEfp6B,KAAK65B,UAAY75B,KAAKq6B,kBAGxB,IAAK,MAAMC,KAAWt6B,KAAK05B,oBAAoB16B,SAC7CgB,KAAK65B,UAAUU,QAAQD,EAE1B,CAEDzzB,UACE7G,KAAK65B,UAAUO,aACf3zB,MAAMI,SACP,CAGDnB,kBAAkBF,GAWhB,OATAA,EAAOxI,OAAS9D,EAAWsM,EAAOxI,SAAWxE,SAASwC,KAGtDwK,EAAO6zB,WAAa7zB,EAAOwT,OAAU,GAAExT,EAAOwT,oBAAsBxT,EAAO6zB,WAE3C,iBAArB7zB,EAAO+zB,YAChB/zB,EAAO+zB,UAAY/zB,EAAO+zB,UAAUlhC,MAAM,KAAKiR,KAAI3G,GAASjG,OAAOC,WAAWgG,MAGzE6C,CACR,CAED20B,2BACOn6B,KAAK2G,QAAQ2yB,eAKlB94B,EAAaC,IAAIT,KAAK2G,QAAQ3J,OAAQm8B,IAEtC34B,EAAaa,GAAGrB,KAAK2G,QAAQ3J,OAAQm8B,GAAaC,IAAuBl6B,IACvE,MAAMs7B,EAAoBx6B,KAAK05B,oBAAoB12B,IAAI9D,EAAMlC,OAAO8e,MACpE,GAAI0e,EAAmB,CACrBt7B,EAAMqD,iBACN,MAAM/H,EAAOwF,KAAK25B,cAAgB7+B,OAC5B0a,EAASglB,EAAkB1kB,UAAY9V,KAAK0G,SAASoP,UAC3D,GAAItb,EAAKigC,SAEP,YADAjgC,EAAKigC,SAAS,CAAE3pB,IAAK0E,EAAQklB,SAAU,WAKzClgC,EAAK6hB,UAAY7G,CAClB,KAEJ,CAED6kB,kBACE,MAAMtmB,EAAU,CACdvZ,KAAMwF,KAAK25B,aACXJ,UAAWv5B,KAAK2G,QAAQ4yB,UACxBF,WAAYr5B,KAAK2G,QAAQ0yB,YAG3B,OAAO,IAAIsB,sBAAqB/3B,GAAW5C,KAAK46B,kBAAkBh4B,IAAUmR,EAC7E,CAGD6mB,kBAAkBh4B,GAChB,MAAMi4B,EAAgBlH,GAAS3zB,KAAKy5B,aAAaz2B,IAAK,IAAG2wB,EAAM32B,OAAO89B,MAChEnM,EAAWgF,IACf3zB,KAAK85B,oBAAoBC,gBAAkBpG,EAAM32B,OAAO8Y,UACxD9V,KAAK+6B,SAASF,EAAclH,GAA5B,EAGIqG,GAAmBh6B,KAAK25B,cAAgBnhC,SAAS6B,iBAAiBgiB,UAClE2e,EAAkBhB,GAAmBh6B,KAAK85B,oBAAoBE,gBACpEh6B,KAAK85B,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAMrG,KAAS/wB,EAAS,CAC3B,IAAK+wB,EAAMsH,eAAgB,CACzBj7B,KAAK45B,cAAgB,KACrB55B,KAAKk7B,kBAAkBL,EAAclH,IAErC,QACD,CAED,MAAMwH,EAA2BxH,EAAM32B,OAAO8Y,WAAa9V,KAAK85B,oBAAoBC,gBAEpF,GAAIiB,GAAmBG,GAGrB,GAFAxM,EAASgF,IAEJqG,EACH,YAOCgB,GAAoBG,GACvBxM,EAASgF,EAEZ,CACF,CAEDuG,mCACEl6B,KAAKy5B,aAAe,IAAIv2B,IACxBlD,KAAK05B,oBAAsB,IAAIx2B,IAE/B,MAAMk4B,EAAc9yB,EAAerJ,KAAKm6B,GAAuBp5B,KAAK2G,QAAQ3J,QAE5E,IAAK,MAAMq+B,KAAUD,EAAa,CAEhC,IAAKC,EAAOvf,MAAQjiB,EAAWwhC,GAC7B,SAGF,MAAMb,EAAoBlyB,EAAeG,QAAQ4yB,EAAOvf,KAAM9b,KAAK0G,UAG/DtN,EAAUohC,KACZx6B,KAAKy5B,aAAar2B,IAAIi4B,EAAOvf,KAAMuf,GACnCr7B,KAAK05B,oBAAoBt2B,IAAIi4B,EAAOvf,KAAM0e,GAE7C,CACF,CAEDO,SAAS/9B,GACHgD,KAAK45B,gBAAkB58B,IAI3BgD,KAAKk7B,kBAAkBl7B,KAAK2G,QAAQ3J,QACpCgD,KAAK45B,cAAgB58B,EACrBA,EAAOhD,UAAU4Q,IAAIU,IACrBtL,KAAKs7B,iBAAiBt+B,GAEtBwD,EAAaoB,QAAQ5B,KAAK0G,SAjNN,wBAiNgC,CAAE7G,cAAe7C,IACtE,CAEDs+B,iBAAiBt+B,GAEf,GAAIA,EAAOhD,UAAUC,SAlNQ,iBAmN3BqO,EAAeG,QAxMY,mBAwMsBzL,EAAOtD,QAzMpC,cA0MjBM,UAAU4Q,IAAIU,SAInB,IAAK,MAAMiwB,KAAajzB,EAAeO,QAAQ7L,EAnNnB,qBAsN1B,IAAK,MAAM4X,KAAQtM,EAAeS,KAAKwyB,EAlNhB,sDAmNrB3mB,EAAK5a,UAAU4Q,IAAIU,GAGxB,CAED4vB,kBAAkB7rB,GAChBA,EAAOrV,UAAU4J,OAAO0H,IAExB,MAAMkwB,EAAclzB,EAAerJ,KAAM,gBAAgDoQ,GACzF,IAAK,MAAMuD,KAAQ4oB,EACjB5oB,EAAK5Y,UAAU4J,OAAO0H,GAEzB,CAGqBnE,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOwxB,GAAU7xB,oBAAoB3H,KAAMwF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAOpN,WAAW,MAAmB,gBAAXoN,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IANJ,CAOF,GACF,EAOHhF,EAAaa,GAAGvG,OAlQa,8BAkQgB,KAC3C,IAAK,MAAM2gC,KAAOnzB,EAAerJ,KA9PT,0BA+PtBu6B,GAAU7xB,oBAAoB8zB,EAC/B,IAOHrgC,EAAmBo+B,ICnRnB,MAYMkC,GAAiB,YACjBC,GAAkB,aAClBrS,GAAe,UACfC,GAAiB,YAEjBje,GAAoB,SACpBgqB,GAAkB,OAClBpmB,GAAkB,OAUlBhH,GAAuB,2EACvB0zB,GAAuB,gHAAqB1zB,KAQlD,MAAM2zB,WAAYr1B,EAChBX,YAAY9N,GACV0O,MAAM1O,GACNiI,KAAKuqB,QAAUvqB,KAAK0G,SAAShN,QAfN,uCAiBlBsG,KAAKuqB,UAOVvqB,KAAK87B,sBAAsB97B,KAAKuqB,QAASvqB,KAAK+7B,gBAE9Cv7B,EAAaa,GAAGrB,KAAK0G,SA3CF,kBA2C2BxH,GAASc,KAAKwN,SAAStO,KACtE,CAGUzD,kBACT,MAzDS,KA0DV,CAGDwU,OACE,MAAM+rB,EAAYh8B,KAAK0G,SACvB,GAAI1G,KAAKi8B,cAAcD,GACrB,OAIF,MAAME,EAASl8B,KAAKm8B,iBAEdC,EAAYF,EAChB17B,EAAaoB,QAAQs6B,EAnEP,cAmE2B,CAAEr8B,cAAem8B,IAC1D,KAEgBx7B,EAAaoB,QAAQo6B,EApEvB,cAoE8C,CAAEn8B,cAAeq8B,IAEjEj6B,kBAAqBm6B,GAAaA,EAAUn6B,mBAI1DjC,KAAKq8B,YAAYH,EAAQF,GACzBh8B,KAAKs8B,UAAUN,EAAWE,GAC3B,CAGDI,UAAUvkC,EAASwkC,GACZxkC,IAILA,EAAQiC,UAAU4Q,IAAIU,IAEtBtL,KAAKs8B,UAAU5jC,EAAuBX,IAgBtCiI,KAAKiH,gBAdY,KACsB,QAAjClP,EAAQE,aAAa,SAKzBF,EAAQ2M,gBAAgB,YACxB3M,EAAQyM,aAAa,iBAAiB,GACtCxE,KAAKw8B,gBAAgBzkC,GAAS,GAC9ByI,EAAaoB,QAAQ7J,EAhGN,eAgG4B,CACzC8H,cAAe08B,KARfxkC,EAAQiC,UAAU4Q,IAAIsE,GAOxB,GAK4BnX,EAASA,EAAQiC,UAAUC,SAASq7B,KACnE,CAED+G,YAAYtkC,EAASwkC,GACdxkC,IAILA,EAAQiC,UAAU4J,OAAO0H,IACzBvT,EAAQi5B,OAERhxB,KAAKq8B,YAAY3jC,EAAuBX,IAcxCiI,KAAKiH,gBAZY,KACsB,QAAjClP,EAAQE,aAAa,SAKzBF,EAAQyM,aAAa,iBAAiB,GACtCzM,EAAQyM,aAAa,WAAY,MACjCxE,KAAKw8B,gBAAgBzkC,GAAS,GAC9ByI,EAAaoB,QAAQ7J,EA7HL,gBA6H4B,CAAE8H,cAAe08B,KAP3DxkC,EAAQiC,UAAU4J,OAAOsL,GAO3B,GAG4BnX,EAASA,EAAQiC,UAAUC,SAASq7B,KACnE,CAED9nB,SAAStO,GACP,IAAM,CAACw8B,GAAgBC,GAAiBrS,GAAcC,IAAgBpxB,SAAS+G,EAAMwD,KACnF,OAGFxD,EAAM8sB,kBACN9sB,EAAMqD,iBACN,MAAM4L,EAAS,CAACwtB,GAAiBpS,IAAgBpxB,SAAS+G,EAAMwD,KAC1D+5B,EAAoBt/B,EAAqB6C,KAAK+7B,eAAeh3B,QAAOhN,IAAY8B,EAAW9B,KAAWmH,EAAMlC,OAAQmR,GAAQ,GAE9HsuB,IACFA,EAAkB7R,MAAM,CAAE8R,eAAe,IACzCb,GAAIl0B,oBAAoB80B,GAAmBxsB,OAE9C,CAED8rB,eACE,OAAOzzB,EAAerJ,KAAK28B,GAAqB57B,KAAKuqB,QACtD,CAED4R,iBACE,OAAOn8B,KAAK+7B,eAAe98B,MAAK0J,GAAS3I,KAAKi8B,cAActzB,MAAW,IACxE,CAEDmzB,sBAAsBzsB,EAAQ3G,GAC5B1I,KAAK28B,yBAAyBttB,EAAQ,OAAQ,WAE9C,IAAK,MAAM1G,KAASD,EAClB1I,KAAK48B,6BAA6Bj0B,EAErC,CAEDi0B,6BAA6Bj0B,GAC3BA,EAAQ3I,KAAK68B,iBAAiBl0B,GAC9B,MAAMm0B,EAAW98B,KAAKi8B,cAActzB,GAC9Bo0B,EAAY/8B,KAAKg9B,iBAAiBr0B,GACxCA,EAAMnE,aAAa,gBAAiBs4B,GAEhCC,IAAcp0B,GAChB3I,KAAK28B,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHn0B,EAAMnE,aAAa,WAAY,MAGjCxE,KAAK28B,yBAAyBh0B,EAAO,OAAQ,OAG7C3I,KAAKi9B,mCAAmCt0B,EACzC,CAEDs0B,mCAAmCt0B,GACjC,MAAM3L,EAAStE,EAAuBiQ,GAEjC3L,IAILgD,KAAK28B,yBAAyB3/B,EAAQ,OAAQ,YAE1C2L,EAAMmyB,IACR96B,KAAK28B,yBAAyB3/B,EAAQ,kBAAoB,IAAG2L,EAAMmyB,MAEtE,CAED0B,gBAAgBzkC,EAASmlC,GACvB,MAAMH,EAAY/8B,KAAKg9B,iBAAiBjlC,GACxC,IAAKglC,EAAU/iC,UAAUC,SAxLN,YAyLjB,OAGF,MAAMmO,EAAS,CAACpQ,EAAU01B,KACxB,MAAM31B,EAAUuQ,EAAeG,QAAQzQ,EAAU+kC,GAC7ChlC,GACFA,EAAQiC,UAAUoO,OAAOslB,EAAWwP,EACrC,EAGH90B,EAjM6B,mBAiMIkD,IACjClD,EAjM2B,iBAiMI8G,IAC/B6tB,EAAUv4B,aAAa,gBAAiB04B,EACzC,CAEDP,yBAAyB5kC,EAASqc,EAAWzR,GACtC5K,EAAQoC,aAAaia,IACxBrc,EAAQyM,aAAa4P,EAAWzR,EAEnC,CAEDs5B,cAAcvsB,GACZ,OAAOA,EAAK1V,UAAUC,SAASqR,GAChC,CAGDuxB,iBAAiBntB,GACf,OAAOA,EAAK9G,QAAQgzB,IAAuBlsB,EAAOpH,EAAeG,QAAQmzB,GAAqBlsB,EAC/F,CAGDstB,iBAAiBttB,GACf,OAAOA,EAAKhW,QAlNO,gCAkNoBgW,CACxC,CAGqBvI,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAO6zB,GAAIl0B,oBAAoB3H,MAErC,GAAsB,iBAAXwF,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAOpN,WAAW,MAAmB,gBAAXoN,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,IANJ,CAOF,GACF,EAOHhF,EAAaa,GAAG7I,SA9Pc,eA8PkB0P,IAAsB,SAAUhJ,GAC1E,CAAC,IAAK,QAAQ/G,SAAS6H,KAAK0H,UAC9BxI,EAAMqD,iBAGJ1I,EAAWmG,OAIf67B,GAAIl0B,oBAAoB3H,MAAMiQ,MAC/B,IAKDzP,EAAaa,GAAGvG,OA3Qa,eA2QgB,KAC3C,IAAK,MAAM/C,KAAWuQ,EAAerJ,KAtPF,iGAuPjC48B,GAAIl0B,oBAAoB5P,EACzB,IAMHqD,EAAmBygC,IC9RnB,MAcMsB,GAAkB,OAClBjuB,GAAkB,OAClByhB,GAAqB,UAErBtrB,GAAc,CAClB4wB,UAAW,UACXmH,SAAU,UACVhH,MAAO,UAGHhxB,GAAU,CACd6wB,WAAW,EACXmH,UAAU,EACVhH,MAAO,KAOT,MAAMiH,WAAc72B,EAClBX,YAAY9N,EAASyN,GACnBiB,MAAM1O,EAASyN,GAEfxF,KAAKw2B,SAAW,KAChBx2B,KAAKs9B,sBAAuB,EAC5Bt9B,KAAKu9B,yBAA0B,EAC/Bv9B,KAAK82B,eACN,CAGU1xB,qBACT,OAAOA,EACR,CAEUC,yBACT,OAAOA,EACR,CAEU5J,kBACT,MAtDS,OAuDV,CAGDwU,OACoBzP,EAAaoB,QAAQ5B,KAAK0G,SAjD5B,iBAmDFzE,mBAIdjC,KAAKw9B,gBAEDx9B,KAAK2G,QAAQsvB,WACfj2B,KAAK0G,SAAS1M,UAAU4Q,IAvDN,QAiEpB5K,KAAK0G,SAAS1M,UAAU4J,OAAOu5B,IAC/BxiC,EAAOqF,KAAK0G,UACZ1G,KAAK0G,SAAS1M,UAAU4Q,IAAIsE,GAAiByhB,IAE7C3wB,KAAKiH,gBAXY,KACfjH,KAAK0G,SAAS1M,UAAU4J,OAAO+sB,IAC/BnwB,EAAaoB,QAAQ5B,KAAK0G,SA9DX,kBAgEf1G,KAAKy9B,oBAAL,GAO4Bz9B,KAAK0G,SAAU1G,KAAK2G,QAAQsvB,WAC3D,CAEDjmB,OACOhQ,KAAK09B,YAIQl9B,EAAaoB,QAAQ5B,KAAK0G,SAlF5B,iBAoFFzE,mBAUdjC,KAAK0G,SAAS1M,UAAU4Q,IAAI+lB,IAC5B3wB,KAAKiH,gBAPY,KACfjH,KAAK0G,SAAS1M,UAAU4Q,IAAIuyB,IAC5Bn9B,KAAK0G,SAAS1M,UAAU4J,OAAO+sB,GAAoBzhB,IACnD1O,EAAaoB,QAAQ5B,KAAK0G,SA1FV,kBA0FhB,GAI4B1G,KAAK0G,SAAU1G,KAAK2G,QAAQsvB,YAC3D,CAEDpvB,UACE7G,KAAKw9B,gBAEDx9B,KAAK09B,WACP19B,KAAK0G,SAAS1M,UAAU4J,OAAOsL,IAGjCzI,MAAMI,SACP,CAED62B,UACE,OAAO19B,KAAK0G,SAAS1M,UAAUC,SAASiV,GACzC,CAIDuuB,qBACOz9B,KAAK2G,QAAQy2B,WAIdp9B,KAAKs9B,sBAAwBt9B,KAAKu9B,0BAItCv9B,KAAKw2B,SAAWt5B,YAAW,KACzB8C,KAAKgQ,MAAL,GACChQ,KAAK2G,QAAQyvB,QACjB,CAEDuH,eAAez+B,EAAO0+B,GACpB,OAAQ1+B,EAAMwB,MACZ,IAAK,YACL,IAAK,WACHV,KAAKs9B,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACH59B,KAAKu9B,wBAA0BK,EASnC,GAAIA,EAEF,YADA59B,KAAKw9B,gBAIP,MAAMpvB,EAAclP,EAAMW,cACtBG,KAAK0G,WAAa0H,GAAepO,KAAK0G,SAASzM,SAASmU,IAI5DpO,KAAKy9B,oBACN,CAED3G,gBACEt2B,EAAaa,GAAGrB,KAAK0G,SArKA,sBAqK2BxH,GAASc,KAAK29B,eAAez+B,GAAO,KACpFsB,EAAaa,GAAGrB,KAAK0G,SArKD,qBAqK2BxH,GAASc,KAAK29B,eAAez+B,GAAO,KACnFsB,EAAaa,GAAGrB,KAAK0G,SArKF,oBAqK2BxH,GAASc,KAAK29B,eAAez+B,GAAO,KAClFsB,EAAaa,GAAGrB,KAAK0G,SArKD,qBAqK2BxH,GAASc,KAAK29B,eAAez+B,GAAO,IACpF,CAEDs+B,gBACE3vB,aAAa7N,KAAKw2B,UAClBx2B,KAAKw2B,SAAW,IACjB,CAGqBrvB,uBAAC3B,GACrB,OAAOxF,KAAK+H,MAAK,WACf,MAAMC,EAAOq1B,GAAM11B,oBAAoB3H,KAAMwF,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQxF,KACd,CACF,GACF,E,OAOHsH,EAAqB+1B,IAMrBjiC,EAAmBiiC,IC1MJ,CACbz1B,QACAO,SACA6D,YACAsD,YACA+a,YACA+E,SACA2B,aACAkI,WACAO,aACAqC,OACAwB,SACA/G,W"}