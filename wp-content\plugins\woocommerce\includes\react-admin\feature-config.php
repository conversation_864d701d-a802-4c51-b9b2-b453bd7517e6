<?php
// WARNING: Do not directly edit this file.
// This file is auto-generated as part of the build process and things may break.
if ( ! function_exists( 'wc_admin_get_feature_config' ) ) {
	function wc_admin_get_feature_config() {
		return array(
			'activity-panels' => true,
			'analytics' => true,
			'product-block-editor' => true,
			'coupons' => true,
			'core-profiler' => true,
			'customer-effort-score-tracks' => true,
			'import-products-task' => true,
			'experimental-fashion-sample-products' => true,
			'shipping-smart-defaults' => true,
			'shipping-setting-tour' => true,
			'homescreen' => true,
			'marketing' => true,
			'minified-js' => false,
			'mobile-app-banner' => true,
			'navigation' => true,
			'new-product-management-experience' => false,
			'onboarding' => true,
			'onboarding-tasks' => true,
			'product-variation-management' => false,
			'remote-inbox-notifications' => true,
			'remote-free-extensions' => true,
			'payment-gateway-suggestions' => true,
			'settings' => false,
			'shipping-label-banner' => true,
			'subscriptions' => true,
			'store-alerts' => true,
			'transient-notices' => true,
			'woo-mobile-welcome' => true,
			'wc-pay-promotion' => true,
			'wc-pay-welcome-page' => true,
			'async-product-editor-category-field' => false,
		);
	}
}
