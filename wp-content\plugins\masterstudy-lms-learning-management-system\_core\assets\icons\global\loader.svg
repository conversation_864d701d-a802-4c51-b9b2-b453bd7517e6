<svg width="50" id="loader-animation" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g id="Loader" clip-path="url(#clip0_16876_68538)">
        <path id="Vector" d="M25.1953 5.48837C25.1976 5.33538 25.1686 5.18354 25.1101 5.04217C25.0515 4.90079 24.9647 4.77286 24.855 4.66624C24.7453 4.55961 24.6149 4.47653 24.4719 4.4221C24.3289 4.36767 24.1763 4.34303 24.0234 4.3497C20.0385 4.57471 16.2038 5.94787 12.9818 8.30356C9.75984 10.6593 7.28827 13.8969 5.86521 17.6258C4.44215 21.3548 4.12839 25.4159 4.96177 29.3192C5.79516 33.2225 7.7401 36.8013 10.562 39.6239C13.3839 42.4466 16.9622 44.3925 20.8652 45.2269C24.7683 46.0614 28.8295 45.7487 32.5588 44.3266C36.2882 42.9046 39.5265 40.4339 41.883 37.2125C44.2396 33.9912 45.6138 30.1568 45.8398 26.172C45.8451 26.0193 45.8195 25.8671 45.7644 25.7246C45.7093 25.5821 45.626 25.4522 45.5195 25.3427C45.4129 25.2333 45.2853 25.1465 45.1444 25.0876C45.0034 25.0287 44.852 24.9989 44.6992 25.0001C44.1445 25.0001 43.5918 24.9884 43.0371 25.0001C42.732 25.0096 42.4418 25.1341 42.2245 25.3484C42.0072 25.5628 41.8789 25.8514 41.8652 26.1563C41.6434 29.3542 40.5064 32.4207 38.5898 34.9901C36.6732 37.5596 34.058 39.5235 31.0561 40.6477C28.0541 41.7718 24.7922 42.0088 21.6593 41.3302C18.5264 40.6517 15.6549 39.0863 13.3871 36.8207C11.1193 34.5552 9.55112 31.6852 8.86948 28.553C8.18784 25.4208 8.42156 22.1586 9.54275 19.1556C10.6639 16.1525 12.6253 13.5354 15.1929 11.6163C17.7604 9.69718 20.8258 8.55713 24.0234 8.33212C24.3359 8.3154 24.6304 8.18103 24.8478 7.95602C25.0652 7.73101 25.1893 7.43205 25.1953 7.11923V5.48837Z" fill="#227AFF"/>
    </g>
    <defs>
        <clipPath id="clip0_16876_68538">
            <rect width="50" height="50" fill="white"/>
        </clipPath>
    </defs>
    <style>
        #loader-animation {
            animation-name: spin;
            animation-duration: 1000ms;
            animation-iteration-count: infinite;
            animation-timing-function: linear;
        }

        @keyframes spin {
            from {
                transform:rotate(0deg);
            }
            to {
                transform:rotate(360deg);
            }
        }
    </style>
</svg>
