msgid ""
msgstr ""
"Project-Id-Version: NUXY\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-23 06:46+0000\n"
"PO-Revision-Date: 2024-12-23 12:23+0000\n"
"Last-Translator: \n"
"Language-Team: German\n"
"Language: de_DE\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.10; wp-6.5.4\n"
"X-Domain: nuxy"

#: taxonomy_meta/fields/image.php:10
msgid "Add image"
msgstr "Bild hinzufügen"

#: metaboxes/metabox.php:248
msgid "Backup Font Family"
msgstr "Schriftfamilie sichern"

#: metaboxes/google_fonts.php:22
msgid "Black 900"
msgstr "Schwarz 900"

#: metaboxes/google_fonts.php:23
msgid "Black 900 italic"
msgstr "Schwarz 900 kursiv"

#: metaboxes/google_fonts.php:20
msgid "Bold 700"
msgstr "Fett 700"

#: metaboxes/google_fonts.php:21
msgid "Bold 700 italic"
msgstr "Fett 700 kursiv"

#: metaboxes/google_fonts.php:53
msgid "Capitalize"
msgstr "Profitieren"

#: metaboxes/google_fonts.php:43
msgid "Center"
msgstr "Center"

#: settings/settings.php:94
msgid "Choose Page"
msgstr "Seite wählen"

#: metaboxes/metabox.php:42
msgid "Choose User"
msgstr "Benutzer wählen"

#: metaboxes/fields/text.php:28
msgid "Copied"
msgstr "Kopiert"

#: metaboxes/metabox.php:254
msgid "Copy settings"
msgstr "Einstellungen kopieren"

#: metaboxes/metabox.php:258
msgid "Couldn't copy settings"
msgstr "Konnte die Einstellungen nicht kopieren"

#: metaboxes/google_fonts.php:29
msgid "Cyrillic"
msgstr "Kyrillisch"

#: metaboxes/google_fonts.php:30
msgid "Cyrillic ext"
msgstr "Kyrillische Erweiterung"

#: metaboxes/fields/duration.php:35
msgid "Days"
msgstr "Tage"

#: metaboxes/google_fonts.php:41
msgid "Default"
msgstr "Standard"

#: metaboxes/metabox.php:301
msgid "Delete"
msgstr "Löschen"

#: taxonomy_meta/fields/image.php:13
msgid "Delete image"
msgstr "Bild löschen"

#: metaboxes/metabox.php:266
msgid ""
"Download and store Google Fonts locally. Set the fonts in the typography."
msgstr ""
"Google Fonts herunterladen und lokal speichern. Schriftarten in der "
"Typografie festlegen."

#: metaboxes/metabox.php:265
msgid "Download Google Fonts"
msgstr "Google Fonts herunterladen"

#: metaboxes/fields/duration.php:24
msgid "duration"
msgstr "Dauer"

#: metaboxes/fields/repeater.php:25 metaboxes/fields/textarea.php:25
#: metaboxes/fields/text.php:27
msgid "Enter"
msgstr "Eingeben"

#: metaboxes/fields/number.php:22
msgid "Enter numbers..."
msgstr "Zahlen eingeben..."

#: helpers/file_upload.php:21
msgid "Error occurred, please try again"
msgstr "Fehler aufgetreten, bitte versuchen Sie es erneut"

#: metaboxes/metabox.php:259
msgid "Export options"
msgstr "Exportoptionen"

#: metaboxes/metabox.php:252
msgid "Font Color"
msgstr "Schriftfarbe"

#: metaboxes/metabox.php:247
msgid "Font Family"
msgstr "Schriftfamilie"

#: metaboxes/metabox.php:243
msgid "Font size"
msgstr "Schriftgröße"

#: metaboxes/metabox.php:250
msgid "Font Subsets"
msgstr "Schriftart-Untergruppen"

#: metaboxes/metabox.php:263
msgid "Font Synchronize"
msgstr "Schriftart synchronisieren"

#: metaboxes/metabox.php:249
msgid "Font Weignt & Style"
msgstr "Schriftart & Stil"

#: metaboxes/google_fonts.php:31
msgid "Greek"
msgstr "Griechisch"

#: metaboxes/google_fonts.php:32
msgid "Greek ext"
msgstr "Griechische Erweiterung"

#: metaboxes/fields/duration.php:34
msgid "Hours"
msgstr "Stunden"

#. Author URI of the plugin
msgid "https://stylemixthemes.com"
msgstr "https://stylemixthemes.com"

#: metaboxes/fields/image.php:24
msgid "Image URL"
msgstr "Bild-URL"

#: metaboxes/metabox.php:260
msgid "Import options"
msgstr "Importoptionen"

#: metaboxes/metabox.php:255
msgid "Import settings"
msgstr "Einstellungen importieren"

#: metaboxes/metabox-display.php:27
msgid "Import/Export"
msgstr "Import/Export"

#: helpers/file_upload.php:65
msgid "Invalid file extension"
msgstr "Ungültige Dateierweiterung"

#: metaboxes/google_fonts.php:33
msgid "Latin"
msgstr "Lateinisch"

#: metaboxes/google_fonts.php:34
msgid "Latin ext"
msgstr "Lateinische Erweiterung"

#: metaboxes/google_fonts.php:42
msgid "Left"
msgstr "Links"

#: metaboxes/metabox.php:246
msgid "Letter spacing"
msgstr "Buchstabenabstände"

#: metaboxes/google_fonts.php:14
msgid "Light 300"
msgstr "Licht 300"

#: metaboxes/google_fonts.php:15
msgid "Light 300 italic"
msgstr "Light 300 kursiv"

#: metaboxes/metabox.php:244
msgid "Line height"
msgstr "Zeilenhöhe"

#: metaboxes/google_fonts.php:52
msgid "Lowercase"
msgstr "Kleinbuchstaben"

#: metaboxes/google_fonts.php:18
msgid "Medium 500"
msgstr "Mittel 500"

#: metaboxes/google_fonts.php:19
msgid "Medium 500 italic"
msgstr "Medium 500 kursiv"

#: metaboxes/fields/duration.php:33
msgid "Minutes"
msgstr "Minuten"

#: metaboxes/google_fonts.php:50
msgid "Normal"
msgstr "Normal"

#. Name of the plugin
msgid "NUXY"
msgstr "NUXY"

#: settings/settings.php:162
msgid "Oops, something went wrong"
msgstr "Ups, etwas ist schiefgegangen"

#: helpers/file_upload.php:53
msgid "Please, select file"
msgstr "Bitte, Datei auswählen"

#: metaboxes/metabox.php:302
msgid "Preview"
msgstr "Vorschau"

#: metaboxes/google_fonts.php:16
msgid "Regular 400"
msgstr "Regulär 400"

#: metaboxes/google_fonts.php:17
msgid "Regular 400 italic"
msgstr "Normal 400 kursiv"

#: metaboxes/fields/image.php:27
msgid "Remove"
msgstr "Entfernen"

#: metaboxes/fields/image.php:26
msgid "Replace"
msgstr "Ersetzen"

#: metaboxes/google_fonts.php:44
msgid "Right"
msgstr "Rechts"

#: settings/view/header.php:82
msgid "Save Settings"
msgstr "Einstellungen speichern"

#: settings/settings.php:158
msgid "Saved!"
msgstr "Gespeichert!"

#: settings/view/header.php:47
msgid "Search"
msgstr "Suchen"

#: taxonomy_meta/fields/image.php:45
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr "Medien Ihrer Wahl auswählen oder hochladen"

#: settings/settings.php:159
msgid "Settings are changed"
msgstr "Einstellungen wurden geändert"

#: settings/settings.php:163
msgid "Settings are not changed"
msgstr "Einstellungen wurden nicht geändert"

#: metaboxes/metabox.php:257
msgid "Settings copied to buffer"
msgstr "Einstellungen in Puffer kopiert"

#: metaboxes/metabox.php:261
msgid "Sorry, no matching options."
msgstr "Entschuldigung, keine passenden Optionen."

#. Author of the plugin
msgid "StylemixThemes"
msgstr "StylemixThemes"

#: metaboxes/metabox.php:264
msgid ""
"Sync and update your fonts if they are displayed incorrectly on your website."
msgstr ""
"Synchronisieren und aktualisieren Sie Ihre Schriftarten, wenn sie auf Ihrer "
"Website falsch angezeigt werden."

#: metaboxes/metabox.php:262
msgid "Synchronize"
msgstr "Synchronisieren"

#: metaboxes/metabox.php:251
msgid "Text Align"
msgstr "Text ausrichten"

#: metaboxes/metabox.php:253
msgid "Text transform"
msgstr "Text transformieren"

#: metaboxes/google_fonts.php:12
msgid "Thin 100"
msgstr "Dünn 100"

#: metaboxes/google_fonts.php:13
msgid "Thin 100 italic"
msgstr "Dünn 100 kursiv"

#: metaboxes/fields/image.php:25
msgid "Upload"
msgstr "Hochladen"

#: metaboxes/google_fonts.php:51
msgid "Uppercase"
msgstr "Großbuchstaben"

#: taxonomy_meta/fields/image.php:47
msgid "Use this media"
msgstr "Dieses Medium verwenden"

#: metaboxes/google_fonts.php:35
msgid "Vietnamese"
msgstr "Vietnamesisch"

#: metaboxes/metabox.php:256
msgid ""
"WARNING! This will overwrite all existing option values, please proceed with "
"caution!"
msgstr ""
"WARNUNG! Dies überschreibt alle existierenden Optionswerte, bitte gehen Sie "
"mit"

#: metaboxes/metabox.php:245
msgid "Word spacing"
msgstr "Wortabstände"

#. Description of the plugin
msgid "WordPress Custom Fields & Theme Options with Vue.js."
msgstr "Benutzerdefinierte Felder und Designoptionen für WordPress mit Vue.js."
