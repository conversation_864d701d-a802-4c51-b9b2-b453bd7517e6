// zoom
.effect-1{
    position:relative;
    &:after{
        content: '';
        display: block;
        @include size(0px,1px);
        @include transition(all 0.3s ease 0s);
        left:0;
        bottom:0;
        right:0;
        background:transparent;
        margin:auto;
    }
    &:hover{
        &:after{
            @include size(100%,1px);
            background:$theme-color;
        }
    }
}
.zoom-2{
    overflow: hidden;
    display: block;
    @include border-radius(3px);
    img{
        position: relative;
        width: percentage(1);
        height: auto;
        @include transition(all .2s ease-out);
        @include transform-origin(center,center);
    }
    &:hover{
        img{
            @include scale(1.2);
        }
    }
}

// filter grayscale
.filter-grayscale {
    @include transition(all 0.6s ease-out 0s);
    &:hover{
        @include filter(grayscale(100%));
    }
}

// filter brightness
.filter-brightness {
    @include transition(all 0.4s ease-out 0s);
    &:hover{
        @include filter(brightness(0.4));
    }
}

// filter blur
.filter-blur{
    @include transition(all 0.6s ease-out 0s);
    &:hover{
        @include filter(blur(5px));
    }
}
.tab-pane{
    //@include animation-name(fadeInUp);
   // @include animation-duration(0.3s);
    //@include animation-fill-mode(both);
}
.close{ 
    .fa{
        @include transition(all 1s ease-in-out);
    }
    &:hover{
        .fa{
            @include rotate(360deg);
        }
    }
}

.image-overlay-1{
    &:after,&:before{
        content:"";
        display: block;
        position: absolute;
        z-index: 100;
        background: rgba($black,.7);
        @include square(percentage(1));
        @include rtl-left(0);
        @include opacity(0);
        @include transition(all 0.3s ease 0s);
    }
    &:after{
        top: -100%;
    }
    &:before{
        bottom: -100%;
    }
    &:hover{
        &:after{
            top: -50%;
            @include opacity(1);
        }
        &:before{
            bottom: -50%;
            @include opacity(1);
        }
    }
}


// Plus Button Effects
.image-plus-1{
    position: relative;
    &::before{
        overflow: hidden;
        position: absolute;
        top: 0;
        content: "";
        z-index: 100;
        @include square(percentage(1));
        @include rtl-left(0);
        @include opacity(0);
        @include transition(all 0.3s ease 0s);
        @include transform(scale(1.5));
        background: url('#{$image-theme-path}plus.png') no-repeat scroll center center / 60px 60px rgba($black,.6);
    }
    &:hover{
        &::before{
            visibility: visible;
            @include opacity(.6);
            @include transform(scale(1));
        }
    }
}

.image-plus-2{
    position: relative;
    &::before{
        content: "";
        z-index: 199;
        top: 0;
        position: absolute;
        background: url('#{$image-theme-path}plus.png') no-repeat scroll center center / 60px 60px rgba($black,.8);
        @include square(percentage(1));
        @include rtl-left(0);
        @include transition(all 0.3s ease 0s);
        @include opacity(0);
        @include background-size(10px 10px, 100% 100%);
        @include background-origin(padding-box, padding-box);
    }
    &:hover{
        &::before{
            opacity: .6;
            visibility: visible;
            @include background-size(60px 60px, 100% 100%);
        }
    }
}

.image-plus-3{
    position: relative;
    &::before{
        content: "";
        top: 0;
        overflow: hidden;
        position: absolute;
        z-index: 100;
        @include transform(scale(0.5) rotateX(180deg));
        @include square(percentage(1));
        @include rtl-left(0);
        @include opacity(0);
        @include transition(all 0.3s ease 0s);
        background: rgba($black,.8);
    }
    &:hover{
        &::before{
            visibility: visible;
            @include opacity(.6);
            @include transform(scale(1) rotateX(0deg));
        }
    }
}

// Icon Effect 1
.icon-effect-1{
    position: relative;
    &:before{
        content: "";
        display: block;
        @include scale(0.5);
        @include border-radius(percentage(.5));
        @include transition(transform 0.5s cubic-bezier(0.19,1,0.22,1),background-color 0.2s cubic-bezier(0.19,1,0.22,1));
        @include vertical-center(100%,100%);
        background-color: transparent;
    }
    &:hover{
        &:before{
            @include scale(1);
            @include transition(transform 0.5s cubic-bezier(0.19,1,0.22,1),background-color 0.2s cubic-bezier(0.19,1,0.22,1));
        }
    }
} 
