<?php
/**
 * Configuration lists for Jetpack Search Fields
 *
 * Post Meta: list of post meta keys that are available in the index
 *   and how they are configured.
 *
 * Custom Taxonomy: list of custom taxonomies that are indexed.
 *
 * The reason we need an allowed list is that Elasticsearch runs into scaling problems
 * with more than 200k-ish total fields. The barrier to adding new fields is low,
 * just open a PR.
 *
 * Although the comments indicate specific plugins, you don't need to be running
 * that plugin for the indexing to work. The metakey just has to match.
 *
 * If you need a new meta key or taxonomy also consider using:
 *   jetpack-search-meta0 - jetpack-search-meta9
 *   jetpack-search-tag0 - jetpack-search-tag9
 *
 * @package automattic/jetpack-sync
 */

namespace Automattic\Jetpack\Sync\Modules;

/**
 * Class to handle sync for Jetpack Search.
 */
class Search extends Module {

	/**
	 * Sync module name.
	 *
	 * @access public
	 *
	 * @return string
	 */
	public function name() {
		return 'search';
	}

	/**
	 * Constructor.
	 */
	public function __construct() {
		// Post meta whitelists.
		add_filter( 'jetpack_sync_post_meta_whitelist', array( $this, 'add_search_post_meta_whitelist' ), 10 );
		// Add options
		add_filter( 'jetpack_sync_options_whitelist', array( $this, 'add_search_options_whitelist' ), 10 );
	}

	/**
	 * Post meta search specification.
	 *
	 * We sync and index all meta keys in this list. Additionally there are a few
	 * options.
	 *
	 * 'metakey' => [ 'searchable_in_all_content' => true ],
	 *    Field will be included in the all_content fields
	 *
	 * 'metakey' => [ 'available' => false, 'alternatives' => [ 'metakey_processed' ] ],
	 *   Field not in meta.* but has data in an alternative field(s) name that
	 *   should work similarly. For instance, woocommerce total_sales does not go into
	 *   the index, but the percentage of sales does.
	 *
	 * @static
	 * @access private
	 * @var array
	 */
	private static $postmeta_to_sync = array(
		// jetpack.
		'jetpack-search-meta0'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta1'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta2'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta3'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta4'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta5'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta6'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta7'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta8'                   => array( 'searchable_in_all_content' => true ),
		'jetpack-search-meta9'                   => array( 'searchable_in_all_content' => true ),

		// woocommerce.
		'exclude_product_categories'             => array(),
		'exclude_product_ids'                    => array(),
		'free_shipping'                          => array(),
		'id_field'                               => array(),
		'individual_use'                         => array(),
		'limit_usage_to_x_items'                 => array(),
		'maximum_amount'                         => array(),
		'minimum_amount'                         => array(),
		'post_id'                                => array(),
		'product_categories'                     => array( 'searchable_in_all_content' => true ),
		'product_ids'                            => array(),
		'total_sales'                            => array(
			'available'    => false,
			'alternatives' => array(
				'wc.percent_of_sales',
			),
		),
		'usage_limit'                            => array(),
		'usage_limit_per_user'                   => array(),
		'_crosssell_ids'                         => array(),
		'_downloadable'                          => array(),
		'_featured'                              => array(),
		'_height'                                => array(),
		'_length'                                => array(),
		'_price'                                 => array(
			'alternatives' => array(
				'wc.price',
				'wc.min_price',
				'wc.max_price',
			),
		),
		'_prices_include_tax'                    => array(),
		'_product_attributes'                    => array(),
		'_product_version'                       => array(),
		'_regular_price'                         => array(
			'alternatives' => array(
				'wc.regular_price',
			),
		),
		'_sale_price'                            => array(
			'alternatives' => array(
				'wc.sale_price',
			),
		),
		'_sale_price_dates_from'                 => array(),
		'_sale_price_dates_to'                   => array(),
		'_sku'                                   => array( 'searchable_in_all_content' => true ),
		'_stock_status'                          => array(),
		'_wc_average_rating'                     => array(
			'alternatives' => array(
				'wc.ave_rating_score',
			),
		),
		'_wc_rating_count'                       => array(
			'alternatives' => array(
				'wc.rating', // wc.rating.count_1, wc.rating.count_2, ...
			),
		),
		'_wc_review_count'                       => array(),
		'_weight'                                => array(),
		'_width'                                 => array(),

		// co-authors plus.
		'cap-description'                        => array( 'searchable_in_all_content' => true ),
		'cap-user_login'                         => array( 'searchable_in_all_content' => true ),
		'cap-user_email'                         => array(),
		'cap-last_name'                          => array( 'searchable_in_all_content' => true ),
		'cap-first_name'                         => array( 'searchable_in_all_content' => true ),
		'cap-display_name'                       => array( 'searchable_in_all_content' => true ),
		'cap-website'                            => array(),
		'cap-jabber'                             => array(),
		'cap-aim'                                => array(),
		'cap-twitter'                            => array(),
		'cap-facebook'                           => array(),
		'cap-google_plus'                        => array(),
		'cap-job_title'                          => array( 'searchable_in_all_content' => true ),

		// bbpress.
		'bbpl_like'                              => array(),
		'bbpress_discussion_comments_copied'     => array(),
		'bbpress_discussion_tags_copied'         => array(),
		'bbpress_discussion_topic_id'            => array(),
		'bbpress_discussion_use_defaults'        => array(),
		'bbpress_page_header_bg'                 => array(),
		'bbpress_title_bg'                       => array(),
		'use_bbpress_discussion_topic'           => array(),

		// wpml.
		'tm_meta_wpml'                           => array(),
		'wpml_language'                          => array(),
		'wpml_media_lang'                        => array(),
		'wpml_media_processed'                   => array(),

		// blogger import.
		'blogger_author'                         => array( 'searchable_in_all_content' => true ),
		'blogger_blog'                           => array( 'searchable_in_all_content' => true ),
		'blogger_permalink'                      => array( 'searchable_in_all_content' => true ),

		// geo.
		'geo_address'                            => array( 'searchable_in_all_content' => true ),
		'geo_latitude'                           => array(),
		'geo_longitude'                          => array(),
		'geo_public'                             => array(),
		'geolocated'                             => array(),
		'geolocation_city'                       => array( 'searchable_in_all_content' => true ),
		'geolocation_country_long'               => array( 'searchable_in_all_content' => true ),
		'geolocation_country_short'              => array( 'searchable_in_all_content' => true ),
		'geolocation_formatted_address'          => array( 'searchable_in_all_content' => true ),
		'geolocation_lat'                        => array(),
		'geolocation_long'                       => array(),
		'geolocation_postcode'                   => array( 'searchable_in_all_content' => true ),
		'geolocation_state_long'                 => array( 'searchable_in_all_content' => true ),
		'geolocation_state_short'                => array( 'searchable_in_all_content' => true ),

		// wp-ultimate-recipe.
		'recipe_alternate_image'                 => array(),
		'recipe_cook_time'                       => array(),
		'recipe_cook_time_text'                  => array(),
		'recipe_description'                     => array( 'searchable_in_all_content' => true ),
		'recipe_ingredients'                     => array( 'searchable_in_all_content' => true ),
		'recipe_instructions'                    => array( 'searchable_in_all_content' => true ),
		'recipe_notes'                           => array( 'searchable_in_all_content' => true ),
		'recipe_nutritional'                     => array( 'searchable_in_all_content' => true ),
		'recipe_passive_time'                    => array(),
		'recipe_passive_time_text'               => array(),
		'recipe_prep_time'                       => array(),
		'recipe_prep_time_text'                  => array(),
		'recipe_rating'                          => array(),
		'recipe_servings'                        => array(),
		'recipe_servings_normalized'             => array(),
		'recipe_servings_type'                   => array(),
		'recipe_terms'                           => array( 'searchable_in_all_content' => true ),
		'recipe_terms_with_parents'              => array(),
		'recipe_title'                           => array( 'searchable_in_all_content' => true ),
		'recipe_user_ratings'                    => array(),
		'recipe_user_ratings_rating'             => array(),

		// generic fields.
		// from advanced-custom-fields and metabox.io .
		'Link'                                   => array(),
		'Location'                               => array(),
		'Title'                                  => array( 'searchable_in_all_content' => true ),
		'ad_code'                                => array(),
		'address'                                => array(),
		'admin_mail'                             => array(),
		'admin_only'                             => array(),
		'advertisers'                            => array( 'searchable_in_all_content' => true ),
		'age'                                    => array(),
		'aliases'                                => array(),
		'alternate_title'                        => array(),
		'amazon'                                 => array(),
		'answer'                                 => array( 'searchable_in_all_content' => true ),
		'area'                                   => array(),
		'attention'                              => array(),
		'attr'                                   => array(),
		'author'                                 => array( 'searchable_in_all_content' => true ),
		'author_name'                            => array( 'searchable_in_all_content' => true ),
		'blog'                                   => array(),
		'blog_id'                                => array(),
		'call_to_action'                         => array(),
		'campaign_preview'                       => array(),
		'canonical_url'                          => array(),
		'catch_text'                             => array(),
		'category'                               => array( 'searchable_in_all_content' => true ),
		'classificacao'                          => array(),
		'classification'                         => array(),
		'code'                                   => array(),
		'codigo'                                 => array(),
		'company'                                => array( 'searchable_in_all_content' => true ),
		'company_website'                        => array(),
		'config'                                 => array(),
		'construction'                           => array(),
		'container_ids'                          => array(),
		'content'                                => array( 'searchable_in_all_content' => true ),
		'content_body-full_content'              => array( 'searchable_in_all_content' => true ),
		'copyright'                              => array(),
		'custom_page_title'                      => array( 'searchable_in_all_content' => true ),
		'custom_permalink'                       => array(),
		'customize'                              => array(),
		'data'                                   => array(),
		'date'                                   => array(),
		'day'                                    => array(),
		'descripcion'                            => array( 'searchable_in_all_content' => true ),
		'description'                            => array( 'searchable_in_all_content' => true ),
		'display_settings'                       => array(),
		'display_type'                           => array(),
		'duration'                               => array(),
		'embed'                                  => array(),
		'entity_ids'                             => array(),
		'entity_types'                           => array(),
		'event_subtitle'                         => array( 'searchable_in_all_content' => true ),
		'excluded_container_ids'                 => array(),
		'exclusions'                             => array(),
		'experience'                             => array(),
		'external_url'                           => array(),
		'featured'                               => array(),
		'featured_image'                         => array(),
		'featured_post'                          => array(),
		'featured_story'                         => array(),
		'fee'                                    => array(),
		'filter'                                 => array(),
		'follow'                                 => array(),
		'footer_text'                            => array(),
		'from_header'                            => array(),
		'fullscreen_view'                        => array(),
		'gallery'                                => array(),
		'genre'                                  => array( 'searchable_in_all_content' => true ),
		'guests'                                 => array( 'searchable_in_all_content' => true ),
		'has_variations'                         => array(),
		'hashtag'                                => array(),
		'header_image'                           => array(),
		'hidden_from_ui'                         => array(),
		'hide_on_screen'                         => array(),
		'homepage_order'                         => array(),
		'hours'                                  => array(),
		'i18n'                                   => array(),
		'id'                                     => array(),
		'image'                                  => array(),
		'image_size'                             => array(),
		'image_source'                           => array(),
		'index'                                  => array(),
		'intro_text'                             => array( 'searchable_in_all_content' => true ),
		'job_mention'                            => array( 'searchable_in_all_content' => true ),
		'keywords'                               => array( 'searchable_in_all_content' => true ),
		'latest_news'                            => array(),
		'layout'                                 => array(),
		'link'                                   => array(),
		'link_dump'                              => array( 'searchable_in_all_content' => true ),
		'link_url'                               => array(),
		'location'                               => array(),
		'logo'                                   => array(),
		'main_title'                             => array( 'searchable_in_all_content' => true ),
		'maximum_entity_count'                   => array(),
		'media'                                  => array(),
		'mentions'                               => array(),
		'messages'                               => array(),
		'meta_description'                       => array( 'searchable_in_all_content' => true ),
		'meta_id'                                => array(),
		'meta_index'                             => array(),
		'meta_key'                               => array(),
		'meta_value'                             => array(),
		'modal-dialog-id'                        => array(),
		'name'                                   => array( 'searchable_in_all_content' => true ),
		'nombre'                                 => array( 'searchable_in_all_content' => true ),
		'notes'                                  => array( 'searchable_in_all_content' => true ),
		'options'                                => array(),
		'order_by'                               => array(),
		'order_direction'                        => array(),
		'original_cats'                          => array(),
		'original_headers'                       => array(),
		'original_link'                          => array(),
		'original_message'                       => array(),
		'original_subject'                       => array(),
		'original_title'                         => array(),
		'original_to'                            => array(),
		'other_setting'                          => array(),
		'page_canonical'                         => array(),
		'page_layout'                            => array(),
		'page_sidebar'                           => array(),
		'page_tags'                              => array(),
		'panels_data'                            => array(),
		'parking'                                => array(),
		'pdf_upload'                             => array(),
		'photo'                                  => array(),
		'play_time'                              => array(),
		'position'                               => array(),
		'post-rating'                            => array(),
		'post_background'                        => array(),
		'post_color'                             => array(),
		'post_sidebar'                           => array(),
		'post_subtitle'                          => array( 'searchable_in_all_content' => true ),
		'price'                                  => array(),
		'publication'                            => array(),
		'rating'                                 => array(),
		'ratings_average'                        => array(),
		'ratings_score'                          => array(),
		'ratings_users'                          => array(),
		'relation'                               => array(),
		'reply_to_header'                        => array(),
		'required'                               => array(),
		'returns'                                => array(),
		'review_post'                            => array(),
		'rule'                                   => array(),
		'section'                                => array( 'searchable_in_all_content' => true ),
		'session_transcript'                     => array(),
		'settings'                               => array(),
		'sex'                                    => array(),
		'shares_count'                           => array(),
		'show_description'                       => array( 'searchable_in_all_content' => true ),
		'show_page_title'                        => array(),
		'side'                                   => array(),
		'sidebar'                                => array(),
		'site'                                   => array(),
		'situation'                              => array(),
		'slide_template'                         => array(),
		'slug'                                   => array(),
		'sortorder'                              => array(),
		'source'                                 => array(),
		'start_date'                             => array(),
		'status'                                 => array(),
		'styles'                                 => array(),
		'subtitle'                               => array( 'searchable_in_all_content' => true ),
		'subtitulo'                              => array(),
		'success'                                => array(),
		'summary'                                => array( 'searchable_in_all_content' => true ),
		'synopsis'                               => array( 'searchable_in_all_content' => true ),
		'tel'                                    => array(),
		'tema'                                   => array(),
		'testimonial'                            => array(),
		'testimonial_author'                     => array(),
		'text_already_subscribed'                => array(),
		'text_error'                             => array(),
		'text_invalid_email'                     => array(),
		'text_not_subscribed'                    => array(),
		'text_required_field_missing'            => array(),
		'text_subscribed'                        => array(),
		'text_unsubscribed'                      => array(),
		'thumbnail'                              => array(),
		'time'                                   => array(),
		'time_jump_list'                         => array( 'searchable_in_all_content' => true ),
		'title'                                  => array( 'searchable_in_all_content' => true ),
		'title_view'                             => array(),
		'titre'                                  => array( 'searchable_in_all_content' => true ),
		'titulo'                                 => array( 'searchable_in_all_content' => true ),
		'to_header'                              => array(),
		'toc'                                    => array(),
		'transcript'                             => array( 'searchable_in_all_content' => true ),
		'transport_uri'                          => array(),
		'type'                                   => array(),
		'url'                                    => array(),
		'validation'                             => array(),
		'value'                                  => array(),
		'values'                                 => array(),
		'variation'                              => array(),
		'video'                                  => array(),
		'video_type'                             => array(),
		'video_url'                              => array(),
		'videopress_guid'                        => array(),
		'website'                                => array(),
		'weight'                                 => array(),
		'year'                                   => array(),

		// wp.com martketplace search - @see https://wp.me/pdh6GB-Ax#comment-2104
		'_app_icon'                              => array(),
		'_featured_product_video'                => array(),
		'wpcom_marketplace_org_slug'             => array(),
		'_wc_general_product_dependency_theme'   => array(),
		'_wc_general_product_dependency_plugin'  => array(),
		'wpcom_marketplace_product_extra_fields' => array(),

	); // end indexed post meta.

	/**
	 * Postmeta being considered for indexing
	 *   but currently not in the index
	 *   this list is really only for documentation.
	 *
	 * @static
	 * @access private
	 * @var array
	 */
	private static $unindexed_postmeta = array(

		// Core.
		'_wp_attached_file'                          => array(),
		'_wp_attachment_context'                     => array(),
		'_wp_attachment_image_alt'                   => array(),
		'_wp_attachment_is_custom_header'            => array(),
		'_wp_attachment_metadata'                    => array(),
		'_wp_desired_post_slug'                      => array(),
		'_wp_old_date'                               => array(),
		'_wp_old_slug'                               => array(),
		'_wp_page_template'                          => array(),

		// WooCommerce products.
		// See https://github.com/woocommerce/woocommerce/blob/8ed6e7436ff87c2153ed30edd83c1ab8abbdd3e9/includes/data-stores/class-wc-product-data-store-cpt.php#L21 .
		'_backorders'                                => array(),
		'_default_attributes'                        => array(),
		'_download_expiry'                           => array(),
		'_download_limit'                            => array(),
		'_download_permissions_granted'              => array(),
		'_downloadable_files'                        => array(),
		'_file_paths'                                => array(),
		'_manage_stock'                              => array(),
		'_product_image_gallery'                     => array(),
		'_purchase_note'                             => array(),
		'_recorded_sales'                            => array(),
		'_sold_individually'                         => array(),
		'_stock'                                     => array(),
		'_tax_class'                                 => array(),
		'_tax_status'                                => array(),
		'_thumbnail_id'                              => array(),
		'_upsell_ids'                                => array(),
		'_variation_description'                     => array(),
		'_virtual'                                   => array(),
		'_visibility'                                => array(),
		'coupon_amount'                              => array(),
		'default_source'                             => array(),
		'discount_type'                              => array(),
		'exclude_sale_items'                         => array(),
		'expiry_date'                                => array(),

		// Woocommerce orders and refunds.
		// See https://github.com/woocommerce/woocommerce/blob/8ed6e7436ff87c2153ed30edd83c1ab8abbdd3e9/includes/data-stores/class-wc-order-data-store-cpt.php#L27 .
		// See https://github.com/woocommerce/woocommerce/blob/b8a2815ae546c836467008739e7ff5150cb08e93/includes/data-stores/class-wc-order-refund-data-store-cpt.php#L20 .
		'_billing_address_1'                         => array(),
		'_billing_address_2'                         => array(),
		'_billing_address_index'                     => array(),
		'_billing_city'                              => array(),
		'_billing_company'                           => array(),
		'_billing_country'                           => array(),
		'_billing_email'                             => array(),
		'_billing_first_name'                        => array(),
		'_billing_last_name'                         => array(),
		'_billing_phone'                             => array(),
		'_billing_postcode'                          => array(),
		'_billing_state'                             => array(),
		'_cart_discount'                             => array(),
		'_cart_discount_tax'                         => array(),
		'_completed_date'                            => array(),
		'_created_via'                               => array(),
		'_customer_ip_address'                       => array(),
		'_customer_user_agent'                       => array(),
		'_date_completed'                            => array(),
		'_date_paid'                                 => array(),
		'_order_currency'                            => array(),
		'_order_key'                                 => array(),
		'_order_shipping'                            => array(),
		'_order_shipping_tax'                        => array(),
		'_order_stock_reduced'                       => array(),
		'_order_tax'                                 => array(),
		'_order_total'                               => array(),
		'_order_version'                             => array(),
		'_paid_date'                                 => array(),
		'_payment_method'                            => array(),
		'_payment_method_title'                      => array(),
		'_payment_tokens'                            => array(),
		'_recorded_coupon_usage_counts'              => array(),
		'_refund_amount'                             => array(),
		'_refund_reason'                             => array(),
		'_refunded_by'                               => array(),
		'_shipping_address_1'                        => array(),
		'_shipping_address_2'                        => array(),
		'_shipping_address_index'                    => array(),
		'_shipping_city'                             => array(),
		'_shipping_company'                          => array(),
		'_shipping_country'                          => array(),
		'_shipping_first_name'                       => array(),
		'_shipping_last_name'                        => array(),
		'_shipping_postcode'                         => array(),
		'_shipping_state'                            => array(),
		'_transaction_id'                            => array(),

		// aioseop.
		'_aioseop_description'                       => array(),
		'_aioseop_keywords'                          => array(),
		'_aioseop_title'                             => array(),

		// yoast.
		'_yoast_wpseo_authorship'                    => array(),
		'_yoast_wpseo_bctitle'                       => array(),
		'_yoast_wpseo_canonical'                     => array(),
		'_yoast_wpseo_content_score'                 => array(),
		'_yoast_wpseo_focuskw'                       => array(),
		'_yoast_wpseo_focuskw_text_input'            => array(),
		'_yoast_wpseo_google-plus-description'       => array(),
		'_yoast_wpseo_google-plus-image'             => array(),
		'_yoast_wpseo_linkdex'                       => array(),
		'_yoast_wpseo_meta-robots-adv'               => array(),
		'_yoast_wpseo_meta-robots-nofollow'          => array(),
		'_yoast_wpseo_meta-robots-noindex'           => array(),
		'_yoast_wpseo_metadesc'                      => array(),
		'_yoast_wpseo_metakeywords'                  => array(),
		'_yoast_wpseo_opengraph-description'         => array(),
		'_yoast_wpseo_opengraph-image'               => array(),
		'_yoast_wpseo_opengraph-title'               => array(),
		'_yoast_wpseo_primary_byline'                => array(),
		'_yoast_wpseo_primary_category'              => array(),
		'_yoast_wpseo_primary_product_cat'           => array(),
		'_yoast_wpseo_primary_sponsor-type'          => array(),
		'_yoast_wpseo_primary_tema_category'         => array(),
		'_yoast_wpseo_primary_wpdmcategory'          => array(),
		'_yoast_wpseo_primary_wt_portfolio_category' => array(),
		'_yoast_wpseo_redirect'                      => array(),
		'_yoast_wpseo_sitemap-include'               => array(),
		'_yoast_wpseo_sitemap-prio'                  => array(),
		'_yoast_wpseo_title'                         => array(),
		'_yoast_wpseo_twitter-description'           => array(),
		'_yoast_wpseo_twitter-image'                 => array(),

		// bbpress.
		'bbppu_read_by'                              => array(),
		'_bbp_activity_id'                           => array(),
		'_bbp_attachment'                            => array(),
		'_bbp_attachment_upload_error'               => array(),
		'_bbp_forum_id'                              => array(),
		'_bbp_forum_parent_id'                       => array(),
		'_bbp_forum_subforum_count'                  => array(),
		'_bbp_forum_type'                            => array(),
		'_bbp_group_ids'                             => array(),
		'_bbp_last_active_id'                        => array(),
		'_bbp_last_active_time'                      => array(),
		'_bbp_last_reply_id'                         => array(),
		'_bbp_last_topic_id'                         => array(),
		'_bbp_old_forum_id'                          => array(),
		'_bbp_old_sticky_status'                     => array(),
		'_bbp_old_topic_id'                          => array(),
		'_bbp_post_id'                               => array(),
		'_bbp_reply_count'                           => array(),
		'_bbp_reply_is_private'                      => array(),
		'_bbp_reply_to'                              => array(),
		'_bbp_revision_log'                          => array(),
		'_bbp_status'                                => array(),
		'_bbp_sticky_topics'                         => array(),
		'_bbp_topic_count'                           => array(),
		'_bbp_topic_id'                              => array(),
		'_bbp_total_reply_count'                     => array(),
		'_bbp_total_topic_count'                     => array(),
		'_bbp_voice_count'                           => array(),

		// ???
		'_locale'                                    => array(),

		// wp-job-manager.
		'_job_title'                                 => array(),
		'_job_description'                           => array(),

		// wpml.
		'_wpml_media_duplicate'                      => array(),
		'_wpml_media_featured'                       => array(),

		// generic fields.
		'ad_clicks_count'                            => array(),
		'email'                                      => array(),
		'usage_count'                                => array(),
		'user_mail'                                  => array(),
		'views'                                      => array(),
		'_EventAllDay'                               => array(),
		'_EventCost'                                 => array(),
		'_EventCurrencyPosition'                     => array(),
		'_EventCurrencySymbol'                       => array(),
		'_EventDuration'                             => array(),
		'_EventEndDate'                              => array(),
		'_EventEndDateUTC'                           => array(),
		'_EventOrganizerID'                          => array(),
		'_EventOrigin'                               => array(),
		'_EventShowMap'                              => array(),
		'_EventShowMapLink'                          => array(),
		'_EventStartDate'                            => array(),
		'_EventStartDateUTC'                         => array(),
		'_EventTimezone'                             => array(),
		'_EventTimezoneAbbr'                         => array(),
		'_EventURL'                                  => array(),
		'_EventVenueID'                              => array(),
		'_OrganizerEmail'                            => array(),
		'_OrganizerOrganizer'                        => array(),
		'_OrganizerOrigin'                           => array(),
		'_OrganizerPhone'                            => array(),
		'_OrganizerWebsite'                          => array(),
		'_VenueAddress'                              => array(),
		'_VenueCity'                                 => array(),
		'_VenueCountry'                              => array(),
		'_VenueOrigin'                               => array(),
		'_VenuePhone'                                => array(),
		'_VenueProvince'                             => array(),
		'_VenueShowMap'                              => array(),
		'_VenueShowMapLink'                          => array(),
		'_VenueState'                                => array(),
		'_VenueStateProvince'                        => array(),
		'_VenueURL'                                  => array(),
		'_VenueVenue'                                => array(),
		'_VenueVenueID'                              => array(),
		'_VenueZip'                                  => array(),
		'_description'                               => array(),
		'_edit_last'                                 => array(),
		'_feedback_all_fields'                       => array(),
		'_feedback_author'                           => array(),
		'_feedback_author_email'                     => array(),
		'_feedback_author_url'                       => array(),
		'_feedback_contact_form_url'                 => array(),
		'_feedback_ip'                               => array(),
		'_feedback_subject'                          => array(),
		'_layout'                                    => array(),
		'_links_to'                                  => array(),
		'_links_to_target'                           => array(),
		'_mail'                                      => array(),
		'_mail_2'                                    => array(),
		'_messages'                                  => array(),
		'_numero'                                    => array(),
		'_post_restored_from'                        => array(),
		'_video_url'                                 => array(),
		'_website'                                   => array(),

	); // end unindexed post meta.

	/**
	 * List of indexed taxonomy slugs - VARCHAR(32)
	 *
	 * @access private
	 * @static
	 *
	 * @var array
	 */
	private static $taxonomies_to_sync = array(

		// Core.
		'link_category',
		'nav_menu',
		'post_format', // Special, limited to certain values.

		// bbpress.
		'topic',
		'topic-tag',
		'topics',

		// buddypress.
		'bp-email-type',
		'bp-email-type',
		'bp_docs_access',
		'bp_docs_associated_item',
		'bp_docs_comment_access',
		'bp_docs_doc_in_folder',
		'bp_docs_folder_in_group',
		'bp_docs_tag',
		'bp_member_type',

		// co-authors plus.
		'author',

		// events calendar plus.
		// the events calendar.
		'event-categories',
		'event-category',
		'event-tag',
		'event-tags',
		'event-type',
		'event-venue',
		'event_category',
		'event_location',
		'event_organizer',
		'event_tag',
		'event_type',
		'event_type_2',
		'event_users',
		'events_categories',
		'events_category',
		'events_feeds',
		'events_tags',
		'tribe_events_cat',

		// jetpack.
		'jetpack-portfolio-tag',
		'jetpack-portfolio-type',
		'jetpack-search-tag0',
		'jetpack-search-tag1',
		'jetpack-search-tag2',
		'jetpack-search-tag3',
		'jetpack-search-tag4',
		'jetpack-search-tag5',
		'jetpack-search-tag6',
		'jetpack-search-tag7',
		'jetpack-search-tag8',
		'jetpack-search-tag9',

		// nextgen gallery.
		'ngg_tag',

		// polylang.
		// wpml.
		'language',
		'post_translations',
		'term_language',
		'term_translations',
		'translation_priority',

		// woocommerce.
		'pa_accessory-type',
		'pa_actor',
		'pa_age',
		'pa_ambulance',
		'pa_amount',
		'pa_arm-roll',
		'pa_aspectratio',
		'pa_audiencerating',
		'pa_author',
		'pa_axle',
		'pa_battery',
		'pa_belakang',
		'pa_binding',
		'pa_body-type',
		'pa_bore-x-stroke-mm',
		'pa_box-cargo',
		'pa_brakes',
		'pa_brand',
		'pa_brands',
		'pa_bus',
		'pa_c',
		'pa_cabin-to-end',
		'pa_capacity',
		'pa_catalognumberlist',
		'pa_ce-keurmerk',
		'pa_chassis-front',
		'pa_chassis-rear',
		'pa_chassis-weight-kg',
		'pa_chip-log',
		'pa_clothing-size',
		'pa_clutch',
		'pa_clutch-type',
		'pa_collection',
		'pa_color',
		'pa_colors',
		'pa_colour',
		'pa_compactor',
		'pa_condition',
		'pa_cor',
		'pa_couleur',
		'pa_country',
		'pa_countryregion-of-manufacture',
		'pa_crane',
		'pa_creator',
		'pa_culoare',
		'pa_customerpackagetype',
		'pa_depan',
		'pa_depan-belakang',
		'pa_department',
		'pa_design',
		'pa_diameter',
		'pa_diameter-cakram',
		'pa_dimension-mm',
		'pa_dimensions',
		'pa_director',
		'pa_disc-diameter',
		'pa_drive-system',
		'pa_dump',
		'pa_ean',
		'pa_eanlist',
		'pa_edition',
		'pa_electric-battery',
		'pa_engine-model',
		'pa_engine-size',
		'pa_ethnicity',
		'pa_exhaust-brake',
		'pa_fabric',
		'pa_farbe',
		'pa_farg',
		'pa_farge',
		'pa_features',
		'pa_final-gear-ratio',
		'pa_finish',
		'pa_fire-fighting',
		'pa_fits',
		'pa_flat-bed',
		'pa_flavour',
		'pa_format',
		'pa_fragrance',
		'pa_frame',
		'pa_front',
		'pa_front-overhang',
		'pa_front-rear',
		'pa_front-tread',
		'pa_fuel-tank',
		'pa_fuel-type',
		'pa_garantie',
		'pa_geadviseerd-accu-type',
		'pa_gear-ratio',
		'pa_gender',
		'pa_genre',
		'pa_gewicht-exclusief-accu',
		'pa_gift-card-amount',
		'pa_grade-ability-tan-o',
		'pa_groesse',
		'pa_gtin',
		'pa_gvwr-gcwr',
		'pa_hardwareplatform',
		'pa_hazardousmaterialtype',
		'pa_height',
		'pa_hekmotor-of-boegmotor',
		'pa_helmet-size',
		'pa_hersteller',
		'pa_high-blow-tank',
		'pa_hoehe',
		'pa_inhoud',
		'pa_isadultproduct',
		'pa_isbn',
		'pa_iseligiblefortradein',
		'pa_itemdimensions',
		'pa_itempartnumber',
		'pa_kemudi-tipe',
		'pa_kleur',
		'pa_kopling-tipe',
		'pa_label',
		'pa_languages',
		'pa_lbs',
		'pa_legaldisclaimer',
		'pa_lengte-aansluitkabel',
		'pa_length',
		'pa_liquid-tank',
		'pa_location',
		'pa_losse-motor-complete-set',
		'pa_maat',
		'pa_main-brake',
		'pa_make',
		'pa_manufacturer',
		'pa_manufacturer-part-number',
		'pa_manufacturermaximumage',
		'pa_manufacturerminimumage',
		'pa_manufacturerpartswarrantydesc',
		'pa_masseinheit',
		'pa_material',
		'pa_mau-sac',
		'pa_maximum-power-ps-rpm',
		'pa_maximum-speed',
		'pa_maximum-torque-kgm-rpm',
		'pa_mediatype',
		'pa_megethos',
		'pa_merk',
		'pa_metal-type',
		'pa_min-turning-circle',
		'pa_mixer',
		'pa_model',
		'pa_model-tipe',
		'pa_model-type',
		'pa_modelo',
		'pa_mount',
		'pa_mpn',
		'pa_nicotine-strength',
		'pa_nos-of-cylinder',
		'pa_nos-of-tire',
		'pa_numberofdiscs',
		'pa_numberofitems',
		'pa_numberofpages',
		'pa_offset',
		'pa_open-cargo',
		'pa_operatingsystem',
		'pa_options',
		'pa_other-part-number',
		'pa_overall-height',
		'pa_overall-length',
		'pa_overall-width',
		'pa_overview',
		'pa_packagedimensions',
		'pa_packagequantity',
		'pa_pages',
		'pa_parking-brake',
		'pa_part-number',
		'pa_partnumber',
		'pa_pattern',
		'pa_pattern2',
		'pa_performa',
		'pa_pictureformat',
		'pa_pin-size',
		'pa_piston-displacement-cc',
		'pa_ploshhad',
		'pa_plug-type',
		'pa_power',
		'pa_product',
		'pa_productgroup',
		'pa_producttypename',
		'pa_publicationdate',
		'pa_publisher',
		'pa_quantity',
		'pa_rear',
		'pa_rear-overhang',
		'pa_rear-tread',
		'pa_refrigerated-box',
		'pa_region',
		'pa_regioncode',
		'pa_releasedate',
		'pa_rem-parkir',
		'pa_rem-pelambat',
		'pa_rem-utama',
		'pa_reverse',
		'pa_runningtime',
		'pa_scent',
		'pa_schachtlengte',
		'pa_seeds',
		'pa_series',
		'pa_setting',
		'pa_sex',
		'pa_shape',
		'pa_shirt-size',
		'pa_size',
		'pa_sizes',
		'pa_sku',
		'pa_sky-lift',
		'pa_sleeve-length',
		'pa_snelheidsregeling',
		'pa_staart',
		'pa_steering',
		'pa_steering-type',
		'pa_storlek',
		'pa_studio',
		'pa_stuwkracht-lbs',
		'pa_style',
		'pa_suspensions',
		'pa_taille',
		'pa_talla',
		'pa_tamanho',
		'pa_tamano',
		'pa_taxi',
		'pa_ticket-type',
		'pa_tire-size',
		'pa_total-chassis-weight',
		'pa_towing-truck',
		'pa_tradeinvalue',
		'pa_trailer-t-head',
		'pa_transmisi-tipe',
		'pa_transmission',
		'pa_transmission-type',
		'pa_types',
		'pa_ukuran',
		'pa_upc',
		'pa_upclist',
		'pa_variation',
		'pa_vehicle-carrier',
		'pa_vergelijkbaar-stuwkracht',
		'pa_vermogen',
		'pa_voltage',
		'pa_volume',
		'pa_warranty',
		'pa_weight',
		'pa_wheel-base',
		'pa_wheel-configuration',
		'pa_wheel-disc-size',
		'pa_width',
		'pa_zout-water-geschikt',
		'product',
		'product-brand',
		'product_brand',
		'product-category',
		'product_cat',
		'product_delivery_time',
		'product_delivery_times',
		'product_price_label',
		'product_sale_labels',
		'product_shipping_class',
		'product_tag',
		'product_type',
		'product_unit',
		'product_visibility',
		'products',

		// wp-job-manager.
		'job-category',
		'job-location',
		'job-type',
		'job_cat',
		'job_category',
		'job_listing_category',
		'job_listing_label',
		'job_listing_region',
		'job_listing_tag',
		'job_listing_type',
		'job_salary',
		'job_tag',
		'job_type',
		'jobman_category',
		'jobpost_category',
		'jobpost_job_type',
		'jobpost_location',
		'resume_category',
		'resume_groups',
		'resume_job_type',
		'resume_job_type',
		'resume_languages',
		'resume_region',
		'resume_skill',
		'resume_specialities',

		// generic.
		'_resource',
		'acadp_categories',
		'acadp_locations',
		'action-group',
		'activity',
		'actor',
		'actors',
		'ad-group',
		'adace-ad-group',
		'adace-sponsor',
		'additional_features',
		'adv_location',
		'advanced_ads_groups',
		'advert_category',
		'affcoups_coupon_category',
		'affcoups_coupon_type',
		'ai_log_context',
		'ai_log_level',
		'al_product-cat',
		'aol_ad_category',
		'aol_ad_location',
		'aol_ad_type',
		'aol_application_status',
		'area',
		'article-slug',
		'asgarosforum-category',
		'asgarosforum-usergroup',
		'attachment_category',
		'attachment_tag',
		'atum_location',
		'audience_type',
		'avhec_catgroup',
		'bartype',
		'baths',
		'beds',
		'bepro_listing_types',
		'blog_category',
		'booked_custom_calendars',
		'brand',
		'brands',
		'business',
		'business_cat',
		'business_category',
		'bwg_tag',
		'byline',
		'calendar_category',
		'calendar_feed',
		'calendar_type',
		'campaign_category',
		'campaign_tag',
		'carousel_cat',
		'carousels_category',
		'case27_job_listing_tags',
		'categories',
		'category_media',
		'category_portfolio',
		'celebrity_cat',
		'chapters',
		'chronosly_category',
		'city',
		'classified_listing_type',
		'client-types',
		'clients_groups',
		'cm-business-category',
		'cmdm_category',
		'cn_log_type',
		'coderevolution_post_source',
		'collection',
		'community',
		'companies',
		'company',
		'cont_category',
		'content_audit',
		'country',
		'course',
		'course-cat',
		'course-category',
		'course_cat',
		'course_category',
		'course_difficulty',
		'course_tag',
		'courses_type',
		'cp_campaign',
		'cp_recipe_category',
		'csco_post_featured',
		'ct_status',
		'ctl-stories',
		'cuisine',
		'dc_vendor_shop',
		'ddownload_category',
		'ddownload_tag',
		'dealstore',
		'department',
		'departments',
		'department-company',
		'developed-by',
		'dfads_group',
		'dgfw_gift_categories',
		'director',
		'district',
		'dlm_download_category',
		'dlm_download_tag',
		'doc_tag',
		'document-category',
		'document-type',
		'download_artist',
		'download_category',
		'download_tag',
		'downloads_filter',
		'dps_book',
		'dt_gallery_category',
		'dt_logos_category',
		'dt_portfolio_category',
		'dt_team_category',
		'dt_testimonials_category',
		'dtcast',
		'dtcreator',
		'dtdirector',
		'dtnetworks',
		'dtstudio',
		'dtyear',
		'dvteamtaxonomy',
		'dwqa-question_category',
		'dwqa-question_tag',
		'eafl_category',
		'easy-testimonial-category',
		'ecwd_event_category',
		'edd_log_type',
		'edition',
		'ef_editorial_meta',
		'ef_usergroup',
		'element_category',
		'elementor_library_type',
		'employees_category',
		'encyclopedia-tag',
		'envira-tag',
		'epkb_post_type_1_category',
		'espresso_event_categories',
		'espresso_event_type',
		'essential_grid_category',
		'et_post_format',
		'faq-group',
		'faq-tags',
		'faq-topic',
		'faq_cat',
		'faq_categories',
		'faq_category',
		'faqs-category',
		'fdm-menu-section',
		'feature',
		'featured_item_category',
		'featured_item_tag',
		'feedback_type',
		'feeds',
		'fl-builder-template-type',
		'flamingo_inbound_channel',
		'follow_up_email_campaign',
		'follow_up_email_type',
		'following_users',
		'football-team-taxo',
		'fpd_design_category',
		'gallery-category',
		'gallery_cat',
		'gallery_categories',
		'gallery_category',
		'gallery_entries',
		'gallerycat',
		'gd_event_tags',
		'gd_eventcategory',
		'gd_place_tags',
		'gd_placecategory',
		'genre',
		'genres',
		'gg_connect_hub',
		'give_log_type',
		'gn-genre',
		'gn-location-1',
		'gn-location-2',
		'gn-location-3',
		'gp_hubs',
		'gp_portfolios',
		'gp_videos',
		'group',
		'group-documents-category',
		'groups',
		'hashtags',
		'hotel_facility',
		'ia_invited_groups',
		'ia_invitees',
		'incsub_wiki_category',
		'industry',
		'ingredient',
		'insight-type',
		'issue',
		'issuem_issue',
		'issuem_issue_tags',
		'jbp_category',
		'karma-slider-category',
		'klaviyo_shop_cart_status',
		'kwlogos-carousel',
		'layout_category',
		'layout_type',
		'ld_course_category',
		'ld_course_tag',
		'ld_lesson_category',
		'ld_lesson_tag',
		'ld_topic_tag',
		'lesson-tag',
		'level',
		'lingotek_hash',
		'lingotek_profile',
		'link_library_category',
		'linkage',
		'list-tags',
		'listing-category',
		'listing_amenities',
		'listing_category',
		'liveblog',
		'llms_access_plan_visibility',
		'llms_product_visibility',
		'localisation',
		'location',
		'location-tag',
		'locations',
		'magazine',
		'map_location_categories',
		'masonry_gallery_category',
		'mc-event-category',
		'mec_category',
		'mec_location',
		'mec_organizer',
		'media-category',
		'media-tags',
		'media_category',
		'media_folder',
		'member_cat',
		'mentions',
		'mesh_template_types',
		'ml-slider',
		'module',
		'module-tag',
		'module_width',
		'movie_cat',
		'mpp-component',
		'mpp-status',
		'mpp-type',
		'muvicast',
		'muvicountry',
		'muvidirector',
		'muviindex',
		'muviquality',
		'muviyear',
		'news-category',
		'news-tag',
		'news-type',
		'news_type_cat',
		'news_category',
		'nova_menu',
		'nova_menu_item_label',
		'offer-types',
		'organization',
		'our_team_category',
		'page_category',
		'parisrestaurant',
		'parissauna',
		'partner_category',
		'partners',
		'paswdestinatari',
		'paypal_ipn_type',
		'pdf_lv_tag',
		'pec_events_category',
		'people',
		'people-department',
		'people-expertise',
		'people-location',
		'perfect_quotes_category',
		'performer',
		'person',
		'personnal-category',
		'pexcontentslider_category',
		'pexfullslider_category',
		'pexnivoslider_category',
		'pexpricing_category',
		'pexservice_category',
		'pextestimonial_category',
		'pf_feed_item_tag',
		'pg_sas_type',
		'photo_tag',
		'phototype',
		'pj-categs',
		'pj-tags',
		'pl-categs',
		'placement',
		'plan_status',
		'platform',
		'player',
		'plugins_categories',
		'podcast',
		'pojo_sidebars',
		'popup_category',
		'pornstars',
		'portada',
		'portcat',
		'portfolio-category',
		'portfolio-gallery',
		'portfolio-status',
		'portfolio-skills',
		'portfolio-tag',
		'portfolio-tags',
		'portfolio-type',
		'portfolio-types',
		'portfolio_cat',
		'portfolio_categories',
		'portfolio_category',
		'portfolio_cats',
		'portfolio_client',
		'portfolio_entries',
		'portfolio_filter',
		'portfolio_in',
		'portfolio_label',
		'portfolio_skills',
		'portfolio_tag',
		'portfolio_tags',
		'portfolio_type',
		'posicao',
		'post-type',
		'post_format',
		'post_series',
		'pp_editorial_meta',
		'pp_notify_role',
		'pp_usergroup',
		'pricingcats',
		'print_section',
		'print_status',
		'product_asset_class',
		'product_name',
		'programs',
		'project-attributes',
		'project-cat',
		'project-category',
		'project-type',
		'project_category',
		'project_tag',
		'projects_category',
		'projects_tag',
		'prominence',
		'promotion-categories',
		'property-city',
		'property-feature',
		'property-status',
		'property-type',
		'property-types',
		'property_action_category',
		'property_area',
		'property_category',
		'property_city',
		'property_feature',
		'property_status',
		'property_type',
		'province',
		'provinces',
		'publisher',
		'pwb-brand',
		'qmn_log_type',
		'qualification',
		'qualifications',
		'quality',
		'question-category',
		'question-tag',
		'question-type',
		'question_cat',
		'question_category',
		'question_tag',
		'quiz',
		'quiz-type',
		'quote_status',
		'rating',
		'reaction',
		'recipe-category',
		'recipe_category',
		'recipe_type',
		'region',
		'registrant-event',
		'related_keywords',
		'release-date',
		'resource-type',
		'resource_category',
		'resource_type',
		'resourcetype',
		'review-type',
		'review_category',
		'rodzaj',
		'role',
		'room_category',
		'room_tag',
		'roomtype',
		'rubriek_categorie',
		'savedreply',
		'schools',
		'scope',
		'scores_cat',
		'sdm_categories',
		'sdm_tags',
		'season',
		'secondary_html_features',
		'section',
		'sector',
		'series',
		'series_of_posts',
		'services_group',
		'serving',
		'shop_cart_status',
		'shop_cat',
		'shop_order_status',
		'shop_vendor',
		'shop_warranty_status',
		'shopp_category',
		'shopr_category',
		'show',
		'simple_link_category',
		'site-review-category',
		'sizes',
		'skill',
		'skill_level',
		'skills',
		'sld_cat',
		'slide-page',
		'slide-types',
		'slide_categories',
		'slide_type',
		'slider',
		'slider-locations',
		'slider_category',
		'slides_category',
		'slideshow',
		'sm-category',
		'snax_format',
		'sngg_media_tags',
		'solution_channel',
		'source_domain',
		'source_id',
		'sp_league',
		'sp_position',
		'sp_role',
		'sp_season',
		'sp_venue',
		'speaker',
		'speakers',
		'special-feature',
		'specialty',
		'spnl_log_type',
		'sponsor_categories',
		'sponsor_category',
		'sponsor_type',
		'spot_tag',
		'st_af_category',
		'st_af_tags',
		'staff',
		'staff-member-category',
		'staff-member-group',
		'staff_category',
		'staffgroups',
		'state',
		'status',
		'store',
		'stores',
		'studio',
		'study_level',
		'style',
		'style_category',
		'sub_transaction_action',
		'sub_transaction_result',
		'subcategory',
		'subject',
		'subscription_status',
		'swift-slider-category',
		'syn_sitegroup',
		'szbl-content-tag',
		'task-queue',
		'tax_feature',
		'tcb_symbols_tax',
		'tcp_product_category',
		'team',
		'team-category',
		'team_cat',
		'team_categories',
		'team_category',
		'team_cats',
		'team_department',
		'team_designation',
		'team_group',
		'team_member_position',
		'team_mfcategory',
		'teams',
		'tenant_categories',
		'tenant_location',
		'tender-category',
		'test-type',
		'testimonial-category',
		'testimonial-group',
		'testimonial-types',
		'testimonial_categories',
		'testimonial_category',
		'testimonials-category',
		'testimonials_category',
		'th_events_cat',
		'th_galleries_cat',
		'thegem_clients_sets',
		'thegem_news_sets',
		'thegem_portfolios',
		'thegem_quickfinders',
		'thegem_teams',
		'thegem_testimonials_sets',
		'theme',
		'themefusion_es_groups',
		'themes_categories',
		'themo_cpt_group',
		'themo_project_type',
		'themo_room_type',
		'thirstylink-category',
		'ticket_channel',
		'ticket_priority',
		'timeline_post_tag',
		'tipo',
		'tipologie',
		'tips',
		'tm-testimonials_category',
		'tm_testimonial_group',
		'tooltips_categories',
		'tour_category',
		'tour_destination',
		'tour_facility',
		'tour_phys',
		'tour_type',
		'tp_event_category',
		'transmission',
		'treatment-type',
		'tribe_events_cat',
		'truethemes-gallery-category',
		'tsas-category',
		'tshowcase-categories',
		'tsml_region',
		'ttshowcase_groups',
		'tvo_tags',
		'type',
		'types',
		'u_course_cat',
		'u_department',
		'u_event_cat',
		'ufaq-category',
		'ufaq-tag',
		'um_hashtag',
		'um_user_tag',
		'uncodeblock_category',
		'upg_cate',
		'urp-review-category',
		'us_portfolio_category',
		'us_testimonial_category',
		'user-group',
		'user_category',
		'user_status',
		'vendor',
		'venue',
		'video-category',
		'video-series',
		'video-tag',
		'video_category',
		'video_tag',
		'videos',
		'videos_categories',
		'voice_category',
		'vtmin_rule_category',
		'vtprd_rule_category',
		'w2dc-category',
		'w2dc-location',
		'w2dc-tag',
		'wcb_sponsor_level',
		'wcb_track',
		'wccf_checkout_field_field_type',
		'wccf_checkout_field_status',
		'wccf_order_field_field_type',
		'wccf_order_field_status',
		'wccf_product_field_field_type',
		'wccf_product_field_status',
		'wccf_product_prop_field_type',
		'wccf_product_prop_status',
		'wccf_user_field_field_type',
		'wccf_user_field_status',
		'wcfm_knowledgebase_category',
		'wcm_task_category',
		'wcpv_product_vendors',
		'wcs-instructor',
		'wcs-room',
		'wcs-type',
		'wdca_ad_categories',
		'webinar_type_cat',
		'where',
		'who',
		'wiki-category',
		'wiki_cats',
		'wl_entity_type',
		'workout_entries',
		'works-category',
		'wp-rest-api-log-method',
		'wp-rest-api-log-source',
		'wp-rest-api-log-status',
		'wp-type-activity-types',
		'wp-type-contacts-subtype',
		'wp-type-group',
		'wp_bannerize_tax',
		'wp_log_type',
		'wp_super_faq_category',
		'wpbdm-region',
		'wpbdp_category',
		'wpbdp_tag',
		'wpcm_make_model',
		'wpdmcategory',
		'wpfb_file_category',
		'wpfcas-category',
		'wpfd-category',
		'wplead_list_category',
		'wplss_logo_showcase_cat',
		'wpm-testimonial-category',
		'wpmf-category',
		'wpostahs-slider-category',
		'wprm_course',
		'wprm_cuisine',
		'wprm_ingredient',
		'wprm_keyword',
		'wprss_category',
		'wps_forum',
		'wpsc-variation',
		'wpsc_log_type',
		'wpsc_product_category',
		'wpseo_locations_category',
		'wpsisac_slider-category',
		'wpsl_store_category',
		'wpt_category',
		'wpt_result',
		'wpt_scale',
		'wpv_sermons_category',
		'wpvqgr_tag',
		'writer',
		'wyz_business_category',
		'wyz_business_rating_category',
		'wyz_business_tag',
		'wzkb_category',
		'year',
		'years',
		'yith_product_brand',
		'yith_shop_vendor',
		'yst_prominent_words',
		'zipcode',
		'zoninator_zones',
		'zrf_field_group',

		// End The Backlog  @see https://wp.me/p9MPsk-X0.
		'bill-status',
		'etb-audience',
		'etb-state',
		'etb-target',
		'etb-topic',
		'etb-year',
		'foia-response-status',
		'target-type',
		'timeline-pillar',
		'timeline-type',

		// wp.com martketplace search - @see https://wp.me/pdh6GB-Ax#comment-2104
		'wpcom_marketplace_categories',

	); // end taxonomies.

	/**
	 * List of options to sync
	 *
	 * @access private
	 * @static
	 *
	 * @var array
	 */
	private static $options_to_sync = array(
		'jetpack_search_color_theme',
		'jetpack_search_result_format',
		'jetpack_search_default_sort',
		'jetpack_search_overlay_trigger',
		'jetpack_search_excluded_post_types',
		'jetpack_search_highlight_color',
		'jetpack_search_enable_sort',
		'jetpack_search_inf_scroll',
		'jetpack_search_show_powered_by',
		'instant_search_enabled',
	); // end options.

	/*
	 * Taxonomies we know don't sync.
	 * See also sync/src/class-defaults.php
	 *
	 * 'network'
	 * 'post_status'
	 * 'product_cat'
	 * 'tags'
	 *
	 */

	//
	// Hooks into sync.

	/**
	 * Add Search post meta to the post meta whitelist.
	 *
	 * @param array $list Existing post meta whitelist.
	 * @return array Updated post meta whitelist.
	 */
	public function add_search_post_meta_whitelist( $list ) {
		return array_merge( $list, $this->get_all_postmeta_keys() );
	}

	/**
	 * Add Search options to the options whitelist.
	 *
	 * @param array $list Existing options whitelist.
	 * @return array Updated options whitelist.
	 */
	public function add_search_options_whitelist( $list ) {
		return array_merge( $list, $this->get_all_option_keys() );
	}

	//
	// Indexing functions for wp.com.

	/**
	 *
	 * Check whether a postmeta or taxonomy 'key' is in the indexable
	 * list. This is called by the indexing code on wp.com to decide
	 * whether to include something in the index.
	 *
	 * @static
	 * @access public
	 *
	 * @param string $type Either 'postmeta' or 'taxonomy'.
	 * @param string $key The postmeta key or taxonomy name.
	 * @return boolean
	 */
	public static function is_indexable( $type, $key ) {
		switch ( $type ) {
			case 'postmeta':
				return isset( self::$postmeta_to_sync[ $key ] );
			case 'taxonomy':
				return in_array( $key, self::$taxonomies_to_sync, true );
		}
		return false;
	}

	/**
	 *
	 * Get the indexing spec for a postmeta key.
	 *
	 * @static
	 * @access public
	 *
	 * @param string $key The postmeta key.
	 * @return array The spec.
	 */
	public static function get_postmeta_spec( $key ) {
		return self::$postmeta_to_sync[ $key ];
	}

	/**
	 * Get all post meta keys that get synced.
	 *
	 * @access public
	 *
	 * @return array List of post meta keys that get synced.
	 */
	public static function get_all_postmeta_keys() {
		return array_keys( self::$postmeta_to_sync );
	}

	/**
	 * Get all option keys that get synced.
	 *
	 * @access public
	 *
	 * @return array List of option keys that get synced.
	 */
	public static function get_all_option_keys() {
		return self::$options_to_sync;
	}

	/**
	 * Get all unindexed postmeta.
	 *  This is mostly for testing.
	 *
	 * @access public
	 *
	 * @return array List of postmeta that are not synced.
	 */
	public static function get_all_unindexed_postmeta_keys() {
		return array_keys( self::$unindexed_postmeta );
	}

	/**
	 * Get all taxonomies that get synced.
	 *  This is mostly for testing.
	 *
	 * @access public
	 *
	 * @return array List of taxonomies that get synced.
	 */
	public static function get_all_taxonomies() {
		return self::$taxonomies_to_sync;
	}

}
