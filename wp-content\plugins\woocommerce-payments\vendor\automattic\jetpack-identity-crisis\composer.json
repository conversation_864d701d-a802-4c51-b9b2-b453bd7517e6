{"name": "automattic/jetpack-identity-crisis", "description": "Identity Crisis.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"automattic/jetpack-connection": "^1.51.7", "automattic/jetpack-constants": "^1.6.22", "automattic/jetpack-status": "^1.16.4", "automattic/jetpack-logo": "^1.6.1", "automattic/jetpack-assets": "^1.18.1"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "yoast/phpunit-polyfills": "1.0.4", "automattic/wordbless": "@dev"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"build-development": ["pnpm run build"], "build-production": ["NODE_ENV='production' pnpm run build"], "phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"], "post-install-cmd": "WorDBless\\Composer\\InstallDropin::copy", "post-update-cmd": "WorDBless\\Composer\\InstallDropin::copy", "watch": ["Composer\\Config::disableProcessTimeout", "pnpm run watch"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-identity-crisis", "textdomain": "jetpack-idc", "version-constants": {"::PACKAGE_VERSION": "src/class-identity-crisis.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-identity-crisis/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.8.x-dev"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}