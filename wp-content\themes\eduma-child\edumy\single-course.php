<?php
/**
 * Template for displaying content of single course.
 *
 * <AUTHOR>
 * @package LearnPress/Templates
 * @version 4.0.0
 */

defined( 'ABSPATH' ) || exit;

if ( edumy_is_new_learnpress('4.0.0') ) {
	/**
	 * Header for page
	 */
	get_header( );

	/**
	 * @since 3.0.0
	 */
	//do_action( 'learn-press/before-main-content' );
	//do_action( 'learn-press/before-main-content-single-course' );

	while ( have_posts() ) {
		the_post();
		learn_press_get_template( 'content-single-course' );
	}

	/**
	 * @since 3.0.0
	 */
	// do_action( 'learn-press/after-main-content-single-course' );
	// do_action( 'learn-press/after-main-content' );

	/**
	 * LP sidebar
	 */
	// do_action( 'learn-press/sidebar' );

	/**
	 * Footer for page
	 */
	get_footer( );
} else {
	get_header();

	// Start the Loop.
	while ( have_posts() ) : the_post();
		the_content();
		
	// End the loop.
	endwhile;

	get_footer();
}