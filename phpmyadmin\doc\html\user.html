
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>User Guide &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="Configuring phpMyAdmin" href="settings.html" />
    <link rel="prev" title="Configuration" href="config.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="settings.html" title="Configuring phpMyAdmin"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="config.html" title="Configuration"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">User Guide</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="user-guide">
<h1>User Guide<a class="headerlink" href="#user-guide" title="Permalink to this headline">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="settings.html">Configuring phpMyAdmin</a></li>
<li class="toctree-l1"><a class="reference internal" href="two_factor.html">Two-factor authentication</a><ul>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html#authentication-application-2fa">Authentication Application (2FA)</a></li>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html#hardware-security-key-fido-u2f">Hardware Security Key (FIDO U2F)</a></li>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html#simple-two-factor-authentication">Simple two-factor authentication</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="transformations.html">Transformations</a><ul>
<li class="toctree-l2"><a class="reference internal" href="transformations.html#introduction">Introduction</a></li>
<li class="toctree-l2"><a class="reference internal" href="transformations.html#usage">Usage</a></li>
<li class="toctree-l2"><a class="reference internal" href="transformations.html#file-structure">File structure</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="bookmarks.html">Bookmarks</a><ul>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html#storing-bookmarks">Storing bookmarks</a></li>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html#variables-inside-bookmarks">Variables inside bookmarks</a></li>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html#browsing-a-table-using-a-bookmark">Browsing a table using a bookmark</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="privileges.html">User management</a><ul>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#creating-a-new-user">Creating a new user</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#editing-an-existing-user">Editing an existing user</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#deleting-a-user">Deleting a user</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#assigning-privileges-to-user-for-a-specific-database">Assigning privileges to user for a specific database</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html#configurable-menus-and-user-groups">Configurable menus and user groups</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="relations.html">Relations</a><ul>
<li class="toctree-l2"><a class="reference internal" href="relations.html#technical-info">Technical info</a></li>
<li class="toctree-l2"><a class="reference internal" href="relations.html#relation-view">Relation view</a></li>
<li class="toctree-l2"><a class="reference internal" href="relations.html#designer">Designer</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="charts.html">Charts</a><ul>
<li class="toctree-l2"><a class="reference internal" href="charts.html#chart-implementation">Chart implementation</a></li>
<li class="toctree-l2"><a class="reference internal" href="charts.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="import_export.html">Import and export</a><ul>
<li class="toctree-l2"><a class="reference internal" href="import_export.html#import">Import</a></li>
<li class="toctree-l2"><a class="reference internal" href="import_export.html#export">Export</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="themes.html">Custom Themes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="themes.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="themes.html#creating-custom-theme">Creating custom theme</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="other.html">Other sources of information</a><ul>
<li class="toctree-l2"><a class="reference internal" href="other.html#printed-book">Printed Book</a></li>
<li class="toctree-l2"><a class="reference internal" href="other.html#tutorials">Tutorials</a></li>
</ul>
</li>
</ul>
</div>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h4>Previous topic</h4>
  <p class="topless"><a href="config.html"
                        title="previous chapter">Configuration</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="settings.html"
                        title="next chapter">Configuring phpMyAdmin</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/user.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="settings.html" title="Configuring phpMyAdmin"
             >next</a> |</li>
        <li class="right" >
          <a href="config.html" title="Configuration"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">User Guide</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>