/**
 * @license
 * Video.js 7.8.3 <http://videojs.com/>
 * Copyright Brightcove, Inc. <https://www.brightcove.com/>
 * Available under Apache License Version 2.0
 * <https://github.com/videojs/video.js/blob/master/LICENSE>
 *
 * Includes vtt.js <https://github.com/mozilla/vtt.js>
 * Available under Apache License Version 2.0
 * <https://github.com/mozilla/vtt.js/blob/master/LICENSE>
 */ !function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("global/window"),require("global/document")):"function"==typeof define&&define.amd?define(["global/window","global/document"],t):(e=e||self).videojs=t(e.window,e.document)}(this,function(e,t){"use strict";e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e,t=t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t;var i="7.8.3",n=[],r=function t(i){function r(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];s("log",u,t)}var s,a,o,u="info";return s=(a=i,o=r,function(t,i,r){var s=o.levels[i],u=RegExp("^("+s+")$");if("log"!==t&&r.unshift(t.toUpperCase()+":"),r.unshift(a+":"),n){n.push([].concat(r));var l=n.length-1e3;n.splice(0,0<l?l:0)}if(e.console){var c=e.console[t];c||"debug"!==t||(c=e.console.info||e.console.log),c&&s&&u.test(t)&&c[Array.isArray(r)?"apply":"call"](e.console,r)}}),r.createLogger=function(e){return t(i+": "+e)},r.levels={all:"debug|log|warn|error",off:"",debug:"debug|log|warn|error",info:"log|warn|error",warn:"warn|error",error:"error",DEFAULT:u},r.level=function(e){if("string"==typeof e){if(!r.levels.hasOwnProperty(e))throw Error('"'+e+'" in not a valid log level');u=e}return u},(r.history=function(){return n?[].concat(n):[]}).filter=function(e){return(n||[]).filter(function(t){return RegExp(".*"+e+".*").test(t[0])})},r.history.clear=function(){n&&(n.length=0)},r.history.disable=function(){null!==n&&(n.length=0,n=null)},r.history.enable=function(){null===n&&(n=[])},r.error=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return s("error",u,t)},r.warn=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return s("warn",u,t)},r.debug=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return s("debug",u,t)},r}("VIDEOJS"),s=r.createLogger;function a(e,t){return e(t={exports:{}},t.exports),t.exports}var o=a(function(e){function t(){return e.exports=t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},t.apply(this,arguments)}e.exports=t}),u=Object.prototype.toString,l=function(e){return d(e)?Object.keys(e):[]};function c(e,t){l(e).forEach(function(i){return t(e[i],i)})}function h(e){for(var t=arguments.length,i=Array(1<t?t-1:0),n=1;n<t;n++)i[n-1]=arguments[n];return Object.assign?o.apply(void 0,[e].concat(i)):(i.forEach(function(t){t&&c(t,function(t,i){e[i]=t})}),e)}function d(e){return!!e&&"object"==typeof e}function p(e){return d(e)&&"[object Object]"===u.call(e)&&e.constructor===Object}function f(t,i){if(!t||!i||"function"!=typeof e.getComputedStyle)return"";var n=e.getComputedStyle(t);return n?n.getPropertyValue(i)||n[i]:""}function m(e){return"string"==typeof e&&Boolean(e.trim())}function g(e){if(0<=e.indexOf(" "))throw Error("class has illegal whitespace characters")}function v(){return t===e.document}function y(e){return d(e)&&1===e.nodeType}function $(){try{return e.parent!==e.self}catch(t){return!0}}function b(e){return function(i,n){if(!m(i))return t[e](null);m(n)&&(n=t.querySelector(n));var r=y(n)?n:t;return r[e]&&r[e](i)}}function T(e,i,n,s){void 0===e&&(e="div"),void 0===i&&(i={}),void 0===n&&(n={});var a=t.createElement(e);return Object.getOwnPropertyNames(i).forEach(function(e){var t=i[e];-1!==e.indexOf("aria-")||"role"===e||"type"===e?(r.warn("Setting attributes in the second argument of createEl()\nhas been deprecated. Use the third argument instead.\ncreateEl(type, properties, attributes). Attempting to set "+e+" to "+t+"."),a.setAttribute(e,t)):"textContent"===e?_(a,t):a[e]!==t&&(a[e]=t)}),Object.getOwnPropertyNames(n).forEach(function(e){a.setAttribute(e,n[e])}),s&&F(a,s),a}function _(e,t){return void 0===e.textContent?e.innerText=t:e.textContent=t,e}function S(e,t){t.firstChild?t.insertBefore(e,t.firstChild):t.appendChild(e)}function k(e,t){var i;return g(t),e.classList?e.classList.contains(t):RegExp("(^|\\s)"+(i=t)+"($|\\s)").test(e.className)}function C(e,t){return e.classList?e.classList.add(t):k(e,t)||(e.className=(e.className+" "+t).trim()),e}function E(e,t){return e.classList?e.classList.remove(t):(g(t),e.className=e.className.split(/\s+/).filter(function(e){return e!==t}).join(" ")),e}function w(e,t,i){var n=k(e,t);if("function"==typeof i&&(i=i(e,t)),"boolean"!=typeof i&&(i=!n),i!==n)return i?C(e,t):E(e,t),e}function x(e,t){Object.getOwnPropertyNames(t).forEach(function(i){var n=t[i];null==n||!1===n?e.removeAttribute(i):e.setAttribute(i,!0===n?"":n)})}function P(e){var t={};if(e&&e.attributes&&0<e.attributes.length)for(var i=e.attributes,n=i.length-1;0<=n;n--){var r=i[n].name,s=i[n].value;"boolean"!=typeof e[r]&&-1===",autoplay,controls,playsinline,loop,muted,default,defaultMuted,".indexOf(","+r+",")||(s=null!==s),t[r]=s}return t}function L(e,t){return e.getAttribute(t)}function I(e,t,i){e.setAttribute(t,i)}function D(e,t){e.removeAttribute(t)}function A(){t.body.focus(),t.onselectstart=function(){return!1}}function O(){t.onselectstart=function(){return!0}}function R(e){if(e&&e.getBoundingClientRect&&e.parentNode){var t=e.getBoundingClientRect(),i={};return["bottom","height","left","right","top","width"].forEach(function(e){void 0!==t[e]&&(i[e]=t[e])}),i.height||(i.height=parseFloat(f(e,"height"))),i.width||(i.width=parseFloat(f(e,"width"))),i}}function N(i){if(i.getBoundingClientRect&&i.parentNode&&(n=i.getBoundingClientRect()),!n)return{left:0,top:0};var n,r=t.documentElement,s=t.body,a=r.clientLeft||s.clientLeft||0,o=e.pageXOffset||s.scrollLeft,u=n.left+o-a,l=r.clientTop||s.clientTop||0,c=e.pageYOffset||s.scrollTop;return{left:Math.round(u),top:Math.round(n.top+c-l)}}function U(e,t){var i={},n=N(e),r=e.offsetWidth,s=e.offsetHeight,a=n.top,o=n.left,u=t.pageY,l=t.pageX;return t.changedTouches&&(l=t.changedTouches[0].pageX,u=t.changedTouches[0].pageY),i.y=Math.max(0,Math.min(1,(a-u+s)/s)),i.x=Math.max(0,Math.min(1,(l-o)/r)),i}function M(e){return d(e)&&3===e.nodeType}function B(e){for(;e.firstChild;)e.removeChild(e.firstChild);return e}function j(e){return"function"==typeof e&&(e=e()),(Array.isArray(e)?e:[e]).map(function(e){return"function"==typeof e&&(e=e()),y(e)||M(e)?e:"string"==typeof e&&/\S/.test(e)?t.createTextNode(e):void 0}).filter(function(e){return e})}function F(e,t){return j(t).forEach(function(t){return e.appendChild(t)}),e}function H(e,t){return F(B(e),t)}function q(e){return void 0===e.button&&void 0===e.buttons||0===e.button&&void 0===e.buttons||"mouseup"===e.type&&0===e.button&&0===e.buttons||0===e.button&&1===e.buttons}var V,W=b("querySelector"),z=b("querySelectorAll"),G=Object.freeze({__proto__:null,isReal:v,isEl:y,isInFrame:$,createEl:T,textContent:_,prependTo:S,hasClass:k,addClass:C,removeClass:E,toggleClass:w,setAttributes:x,getAttributes:P,getAttribute:L,setAttribute:I,removeAttribute:D,blockTextSelection:A,unblockTextSelection:O,getBoundingClientRect:R,findPosition:N,getPointerPosition:U,isTextNode:M,emptyEl:B,normalizeContent:j,appendContent:F,insertContent:H,isSingleLeftClick:q,$:W,$$:z}),X=!1,K=function(){if(v()&&!1!==V.options.autoSetup){var e=Array.prototype.slice.call(t.getElementsByTagName("video")),i=Array.prototype.slice.call(t.getElementsByTagName("audio")),n=Array.prototype.slice.call(t.getElementsByTagName("video-js")),r=e.concat(i,n);if(r&&0<r.length)for(var s=0,a=r.length;s<a;s++){var o=r[s];if(!o||!o.getAttribute){Y(1);break}void 0===o.player&&null!==o.getAttribute("data-setup")&&V(o)}else X||Y(1)}};function Y(t,i){i&&(V=i),e.setTimeout(K,t)}function Q(){X=!0,e.removeEventListener("load",Q)}function J(e){var i=t.createElement("style");return i.className=e,i}function Z(e,t){e.styleSheet?e.styleSheet.cssText=t:e.textContent=t}v()&&("complete"===t.readyState?Q():e.addEventListener("load",Q));var ee,et=3;function ei(){return et++}e.WeakMap||(ee=function(){function t(){this.vdata="vdata"+Math.floor(e.performance&&e.performance.now()||Date.now()),this.data={}}var i=t.prototype;return i.set=function(e,t){var i=e[this.vdata]||ei();return e[this.vdata]||(e[this.vdata]=i),this.data[i]=t,this},i.get=function(e){var t=e[this.vdata];if(t)return this.data[t];r("We have no data for this element",e)},i.has=function(e){return e[this.vdata]in this.data},i.delete=function(e){var t=e[this.vdata];t&&(delete this.data[t],delete e[this.vdata])},t}());var en,er=e.WeakMap?new WeakMap:new ee;function es(e,t){if(er.has(e)){var i=er.get(e);0===i.handlers[t].length&&(delete i.handlers[t],e.removeEventListener?e.removeEventListener(t,i.dispatcher,!1):e.detachEvent&&e.detachEvent("on"+t,i.dispatcher)),Object.getOwnPropertyNames(i.handlers).length<=0&&(delete i.handlers,delete i.dispatcher,delete i.disabled),0===Object.getOwnPropertyNames(i).length&&er.delete(e)}}function ea(e,t,i,n){i.forEach(function(i){e(t,i,n)})}function eo(i){if(i.fixed_)return i;function n(){return!0}function r(){return!1}if(!i||!i.isPropagationStopped){var s=i||e.event;for(var a in i={},s)"layerX"!==a&&"layerY"!==a&&"keyLocation"!==a&&"webkitMovementX"!==a&&"webkitMovementY"!==a&&("returnValue"===a&&s.preventDefault||(i[a]=s[a]));if(i.target||(i.target=i.srcElement||t),i.relatedTarget||(i.relatedTarget=i.fromElement===i.target?i.toElement:i.fromElement),i.preventDefault=function(){s.preventDefault&&s.preventDefault(),i.returnValue=!1,s.returnValue=!1,i.defaultPrevented=!0},i.defaultPrevented=!1,i.stopPropagation=function(){s.stopPropagation&&s.stopPropagation(),i.cancelBubble=!0,s.cancelBubble=!0,i.isPropagationStopped=n},i.isPropagationStopped=r,i.stopImmediatePropagation=function(){s.stopImmediatePropagation&&s.stopImmediatePropagation(),i.isImmediatePropagationStopped=n,i.stopPropagation()},i.isImmediatePropagationStopped=r,null!==i.clientX&&void 0!==i.clientX){var o=t.documentElement,u=t.body;i.pageX=i.clientX+(o&&o.scrollLeft||u&&u.scrollLeft||0)-(o&&o.clientLeft||u&&u.clientLeft||0),i.pageY=i.clientY+(o&&o.scrollTop||u&&u.scrollTop||0)-(o&&o.clientTop||u&&u.clientTop||0)}i.which=i.charCode||i.keyCode,null!==i.button&&void 0!==i.button&&(i.button=1&i.button?0:4&i.button?1:2&i.button?2:0)}return i.fixed_=!0,i}var eu=function(){if("boolean"!=typeof en){en=!1;try{var t=Object.defineProperty({},"passive",{get:function(){en=!0}});e.addEventListener("test",null,t),e.removeEventListener("test",null,t)}catch(i){}}return en},el=["touchstart","touchmove"];function ec(e,t,i){if(Array.isArray(t))return ea(ec,e,t,i);er.has(e)||er.set(e,{});var n=er.get(e);if(n.handlers||(n.handlers={}),n.handlers[t]||(n.handlers[t]=[]),i.guid||(i.guid=ei()),n.handlers[t].push(i),n.dispatcher||(n.disabled=!1,n.dispatcher=function(t,i){if(!n.disabled){t=eo(t);var s=n.handlers[t.type];if(s)for(var a=s.slice(0),o=0,u=a.length;o<u&&!t.isImmediatePropagationStopped();o++)try{a[o].call(e,t,i)}catch(l){r.error(l)}}}),1===n.handlers[t].length){if(e.addEventListener){var s=!1;eu()&&-1<el.indexOf(t)&&(s={passive:!0}),e.addEventListener(t,n.dispatcher,s)}else e.attachEvent&&e.attachEvent("on"+t,n.dispatcher)}}function eh(e,t,i){if(er.has(e)){var n=er.get(e);if(n.handlers){if(Array.isArray(t))return ea(eh,e,t,i);var r=function(e,t){n.handlers[t]=[],es(e,t)};if(void 0!==t){var s=n.handlers[t];if(s){if(i){if(i.guid)for(var a=0;a<s.length;a++)s[a].guid===i.guid&&s.splice(a--,1);es(e,t)}else r(e,t)}}else for(var o in n.handlers)Object.prototype.hasOwnProperty.call(n.handlers||{},o)&&r(e,o)}}}function ed(e,t,i){var n=er.has(e)?er.get(e):{},r=e.parentNode||e.ownerDocument;if("string"==typeof t?t={type:t,target:e}:t.target||(t.target=e),t=eo(t),n.dispatcher&&n.dispatcher.call(e,t,i),r&&!t.isPropagationStopped()&&!0===t.bubbles)ed.call(null,r,t,i);else if(!r&&!t.defaultPrevented&&t.target&&t.target[t.type]){er.has(t.target)||er.set(t.target,{});var s=er.get(t.target);t.target[t.type]&&(s.disabled=!0,"function"==typeof t.target[t.type]&&t.target[t.type](),s.disabled=!1)}return!t.defaultPrevented}function ep(e,t,i){if(Array.isArray(t))return ea(ep,e,t,i);function n(){eh(e,t,n),i.apply(this,arguments)}n.guid=i.guid=i.guid||ei(),ec(e,t,n)}function ef(e,t,i){function n(){eh(e,t,n),i.apply(this,arguments)}n.guid=i.guid=i.guid||ei(),ec(e,t,n)}function em(e,t,i){t.guid||(t.guid=ei());var n=t.bind(e);return n.guid=i?i+"_"+t.guid:t.guid,n}function eg(t,i){var n=e.performance.now();return function(){var r=e.performance.now();i<=r-n&&(t.apply(void 0,arguments),n=r)}}function ev(){}var ey,e8=Object.freeze({__proto__:null,fixEvent:eo,on:ec,off:eh,trigger:ed,one:ep,any:ef});function e$(e){return"string"==typeof e&&/\S/.test(e)||Array.isArray(e)&&!!e.length}function eb(e){if(!e.nodeName&&!eC(e))throw Error("Invalid target; must be a DOM node or evented object.")}function eT(e){if(!e$(e))throw Error("Invalid event type; must be a non-empty string or array.")}function e_(e){if("function"!=typeof e)throw Error("Invalid listener; must be a function.")}function eS(e,t){var i,n,r,s=t.length<3||t[0]===e||t[0]===e.eventBusEl_;return r=s?(i=e.eventBusEl_,3<=t.length&&t.shift(),n=t[0],t[1]):(i=t[0],n=t[1],t[2]),eb(i),eT(n),e_(r),{isTargetingSelf:s,target:i,type:n,listener:r=em(e,r)}}function ek(e,t,i,n){eb(e),e.nodeName?e8[t](e,i,n):e[t](i,n)}ev.prototype.allowedEvents_={},ev.prototype.addEventListener=ev.prototype.on=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},ec(this,e,t),this.addEventListener=i},ev.prototype.removeEventListener=ev.prototype.off=function(e,t){eh(this,e,t)},ev.prototype.one=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},ep(this,e,t),this.addEventListener=i},ev.prototype.any=function(e,t){var i=this.addEventListener;this.addEventListener=function(){},ef(this,e,t),this.addEventListener=i},ev.prototype.dispatchEvent=ev.prototype.trigger=function(e){var t=e.type||e;"string"==typeof e&&(e={type:t}),e=eo(e),this.allowedEvents_[t]&&this["on"+t]&&this["on"+t](e),ed(this,e)},ev.prototype.queueTrigger=function(t){var i=this;ey=ey||new Map;var n=t.type||t,r=ey.get(this);r||(r=new Map,ey.set(this,r));var s=r.get(n);r.delete(n),e.clearTimeout(s);var a=e.setTimeout(function(){0===r.size&&(r=null,ey.delete(i)),i.trigger(t)},0);r.set(n,a)};var eC=function(e){return e instanceof ev||!!e.eventBusEl_&&["on","one","off","trigger"].every(function(t){return"function"==typeof e[t]})},eE={on:function(){for(var e=this,t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];var r=eS(this,i),s=r.isTargetingSelf,a=r.target,o=r.type,u=r.listener;if(ek(a,"on",o,u),!s){var l=function(){return e.off(a,o,u)};l.guid=u.guid;var c=function(){return e.off("dispose",l)};c.guid=u.guid,ek(this,"on","dispose",l),ek(a,"on","dispose",c)}},one:function(){for(var e=this,t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];var r=eS(this,i),s=r.isTargetingSelf,a=r.target,o=r.type,u=r.listener;if(s)ek(a,"one",o,u);else{var l=function t(){e.off(a,o,t);for(var i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];u.apply(null,n)};l.guid=u.guid,ek(a,"one",o,l)}},any:function(){for(var e=this,t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];var r=eS(this,i),s=r.isTargetingSelf,a=r.target,o=r.type,u=r.listener;if(s)ek(a,"any",o,u);else{var l=function t(){e.off(a,o,t);for(var i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];u.apply(null,n)};l.guid=u.guid,ek(a,"any",o,l)}},off:function(e,t,i){if(!e||e$(e))eh(this.eventBusEl_,e,t);else{var n=e,r=t;eb(n),eT(r),e_(i),i=em(this,i),this.off("dispose",i),n.nodeName?(eh(n,r,i),eh(n,"dispose",i)):eC(n)&&(n.off(r,i),n.off("dispose",i))}},trigger:function(e,t){return ed(this.eventBusEl_,e,t)}};function ew(t,i){void 0===i&&(i={});var n=i.eventBusKey;if(n){if(!t[n].nodeName)throw Error('The eventBusKey "'+n+'" does not refer to an element.');t.eventBusEl_=t[n]}else t.eventBusEl_=T("span",{className:"vjs-event-bus"});return h(t,eE),t.eventedCallbacks&&t.eventedCallbacks.forEach(function(e){e()}),t.on("dispose",function(){t.off(),e.setTimeout(function(){t.eventBusEl_=null},0)}),t}var e0={state:{},setState:function(e){var t,i=this;return"function"==typeof e&&(e=e()),c(e,function(e,n){i.state[n]!==e&&((t=t||{})[n]={from:i.state[n],to:e}),i.state[n]=e}),t&&eC(this)&&this.trigger({changes:t,type:"statechanged"}),t}};function ex(e,t){return h(e,e0),e.state=h({},e.state,t),"function"==typeof e.handleStateChanged&&eC(e)&&e.on("statechanged",e.handleStateChanged),e}function eP(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toLowerCase()})}function eL(e){return"string"!=typeof e?e:e.replace(/./,function(e){return e.toUpperCase()})}function eI(){for(var e={},t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];return i.forEach(function(t){t&&c(t,function(t,i){p(t)?(p(e[i])||(e[i]={}),e[i]=eI(e[i],t)):e[i]=t})}),e}var eD=function(){function t(t,i,n){if(!t&&this.play?this.player_=t=this:this.player_=t,this.isDisposed_=!1,this.parentComponent_=null,this.options_=eI({},this.options_),i=this.options_=eI(this.options_,i),this.id_=i.id||i.el&&i.el.id,!this.id_){var r,s=t&&t.id&&t.id()||"no_player";this.id_=s+"_component_"+ei()}this.name_=i.name||null,i.el?this.el_=i.el:!1!==i.createEl&&(this.el_=this.createEl()),!1!==i.evented&&ew(this,{eventBusKey:this.el_?"el_":null}),ex(this,this.constructor.defaultState),this.children_=[],this.childIndex_={},this.childNameIndex_={},e.Set||(r=function(){function e(){this.set_={}}var t=e.prototype;return t.has=function(e){return e in this.set_},t.delete=function(e){var t=this.has(e);return delete this.set_[e],t},t.add=function(e){return this.set_[e]=1,this},t.forEach=function(e,t){for(var i in this.set_)e.call(t,i,i,this)},e}()),this.setTimeoutIds_=e.Set?new Set:new r,this.setIntervalIds_=e.Set?new Set:new r,this.rafIds_=e.Set?new Set:new r,(this.clearingTimersOnDispose_=!1)!==i.initChildren&&this.initChildren(),this.ready(n),!1!==i.reportTouchActivity&&this.enableTouchActivity()}var i=t.prototype;return i.dispose=function(){if(!this.isDisposed_){if(this.trigger({type:"dispose",bubbles:!1}),this.isDisposed_=!0,this.children_)for(var e=this.children_.length-1;0<=e;e--)this.children_[e].dispose&&this.children_[e].dispose();this.children_=null,this.childIndex_=null,this.childNameIndex_=null,this.parentComponent_=null,this.el_&&(this.el_.parentNode&&this.el_.parentNode.removeChild(this.el_),er.has(this.el_)&&er.delete(this.el_),this.el_=null),this.player_=null}},i.isDisposed=function(){return Boolean(this.isDisposed_)},i.player=function(){return this.player_},i.options=function(e){return e&&(this.options_=eI(this.options_,e)),this.options_},i.el=function(){return this.el_},i.createEl=function(e,t,i){return T(e,t,i)},i.localize=function(e,t,i){void 0===i&&(i=e);var n=this.player_.language&&this.player_.language(),r=this.player_.languages&&this.player_.languages(),s=r&&r[n],a=n&&n.split("-")[0],o=r&&r[a],u=i;return s&&s[e]?u=s[e]:o&&o[e]&&(u=o[e]),t&&(u=u.replace(/\{(\d+)\}/g,function(e,i){var n=t[i-1],r=n;return void 0===n&&(r=e),r})),u},i.contentEl=function(){return this.contentEl_||this.el_},i.id=function(){return this.id_},i.name=function(){return this.name_},i.children=function(){return this.children_},i.getChildById=function(e){return this.childIndex_[e]},i.getChild=function(e){if(e)return this.childNameIndex_[e]},i.getDescendant=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];t=t.reduce(function(e,t){return e.concat(t)},[]);for(var n=this,r=0;r<t.length;r++)if(!(n=n.getChild(t[r]))||!n.getChild)return;return n},i.addChild=function(e,i,n){var r,s;if(void 0===i&&(i={}),void 0===n&&(n=this.children_.length),"string"==typeof e){s=eL(e);var a=i.componentClass||s;i.name=s;var o=t.getComponent(a);if(!o)throw Error("Component "+a+" does not exist");if("function"!=typeof o)return null;r=new o(this.player_||this,i)}else r=e;if(r.parentComponent_&&r.parentComponent_.removeChild(r),this.children_.splice(n,0,r),r.parentComponent_=this,"function"==typeof r.id&&(this.childIndex_[r.id()]=r),(s=s||r.name&&eL(r.name()))&&(this.childNameIndex_[s]=r,this.childNameIndex_[eP(s)]=r),"function"==typeof r.el&&r.el()){var u=null;this.children_[n+1]&&(this.children_[n+1].el_?u=this.children_[n+1].el_:y(this.children_[n+1])&&(u=this.children_[n+1])),this.contentEl().insertBefore(r.el(),u)}return r},i.removeChild=function(e){if("string"==typeof e&&(e=this.getChild(e)),e&&this.children_){for(var t=!1,i=this.children_.length-1;0<=i;i--)if(this.children_[i]===e){t=!0,this.children_.splice(i,1);break}if(t){e.parentComponent_=null,this.childIndex_[e.id()]=null,this.childNameIndex_[eL(e.name())]=null,this.childNameIndex_[eP(e.name())]=null;var n=e.el();n&&n.parentNode===this.contentEl()&&this.contentEl().removeChild(e.el())}}},i.initChildren=function(){var e=this,i=this.options_.children;if(i){var n,r=this.options_,s=t.getComponent("Tech");(n=Array.isArray(i)?i:Object.keys(i)).concat(Object.keys(this.options_).filter(function(e){return!n.some(function(t){return"string"==typeof t?e===t:e===t.name})})).map(function(t){var n,r;return r="string"==typeof t?i[n=t]||e.options_[n]||{}:(n=t.name,t),{name:n,opts:r}}).filter(function(e){var i=t.getComponent(e.opts.componentClass||eL(e.name));return i&&!s.isTech(i)}).forEach(function(t){var i=t.name,n=t.opts;if(void 0!==r[i]&&(n=r[i]),!1!==n){!0===n&&(n={}),n.playerOptions=e.options_.playerOptions;var s=e.addChild(i,n);s&&(e[i]=s)}})}},i.buildCSSClass=function(){return""},i.ready=function(e,t){if(void 0===t&&(t=!1),e)return this.isReady_?void(t?e.call(this):this.setTimeout(e,1)):(this.readyQueue_=this.readyQueue_||[],void this.readyQueue_.push(e))},i.triggerReady=function(){this.isReady_=!0,this.setTimeout(function(){var e=this.readyQueue_;this.readyQueue_=[],e&&0<e.length&&e.forEach(function(e){e.call(this)},this),this.trigger("ready")},1)},i.$=function(e,t){return W(e,t||this.contentEl())},i.$$=function(e,t){return z(e,t||this.contentEl())},i.hasClass=function(e){return k(this.el_,e)},i.addClass=function(e){C(this.el_,e)},i.removeClass=function(e){E(this.el_,e)},i.toggleClass=function(e,t){w(this.el_,e,t)},i.show=function(){this.removeClass("vjs-hidden")},i.hide=function(){this.addClass("vjs-hidden")},i.lockShowing=function(){this.addClass("vjs-lock-showing")},i.unlockShowing=function(){this.removeClass("vjs-lock-showing")},i.getAttribute=function(e){return L(this.el_,e)},i.setAttribute=function(e,t){I(this.el_,e,t)},i.removeAttribute=function(e){D(this.el_,e)},i.width=function(e,t){return this.dimension("width",e,t)},i.height=function(e,t){return this.dimension("height",e,t)},i.dimensions=function(e,t){this.width(e,!0),this.height(t)},i.dimension=function(e,t,i){if(void 0!==t)return null!==t&&t==t||(t=0),-1!==(""+t).indexOf("%")||-1!==(""+t).indexOf("px")?this.el_.style[e]=t:this.el_.style[e]="auto"===t?"":t+"px",void(i||this.trigger("componentresize"));if(!this.el_)return 0;var n=this.el_.style[e],r=n.indexOf("px");return -1!==r?parseInt(n.slice(0,r),10):parseInt(this.el_["offset"+eL(e)],10)},i.currentDimension=function(e){var t=0;if("width"!==e&&"height"!==e)throw Error("currentDimension only accepts width or height value");if(t=f(this.el_,e),0===(t=parseFloat(t))||isNaN(t)){var i="offset"+eL(e);t=this.el_[i]}return t},i.currentDimensions=function(){return{width:this.currentDimension("width"),height:this.currentDimension("height")}},i.currentWidth=function(){return this.currentDimension("width")},i.currentHeight=function(){return this.currentDimension("height")},i.focus=function(){this.el_.focus()},i.blur=function(){this.el_.blur()},i.handleKeyDown=function(e){this.player_&&(e.stopPropagation(),this.player_.handleKeyDown(e))},i.handleKeyPress=function(e){this.handleKeyDown(e)},i.emitTapEvents=function(){var t,i=0,n=null;function r(){t=!1}this.on("touchstart",function(r){1===r.touches.length&&(n={pageX:r.touches[0].pageX,pageY:r.touches[0].pageY},i=e.performance.now(),t=!0)}),this.on("touchmove",function(e){if(1<e.touches.length)t=!1;else if(n){var i=e.touches[0].pageX-n.pageX,r=e.touches[0].pageY-n.pageY;10<Math.sqrt(i*i+r*r)&&(t=!1)}}),this.on("touchleave",r),this.on("touchcancel",r),this.on("touchend",function(r){n=null,!0===t&&e.performance.now()-i<200&&(r.preventDefault(),this.trigger("tap"))})},i.enableTouchActivity=function(){if(this.player()&&this.player().reportUserActivity){var e,t=em(this.player(),this.player().reportUserActivity);this.on("touchstart",function(){t(),this.clearInterval(e),e=this.setInterval(t,250)});var i=function(i){t(),this.clearInterval(e)};this.on("touchmove",t),this.on("touchend",i),this.on("touchcancel",i)}},i.setTimeout=function(t,i){var n,r=this;return t=em(this,t),this.clearTimersOnDispose_(),n=e.setTimeout(function(){r.setTimeoutIds_.has(n)&&r.setTimeoutIds_.delete(n),t()},i),this.setTimeoutIds_.add(n),n},i.clearTimeout=function(t){return this.setTimeoutIds_.has(t)&&(this.setTimeoutIds_.delete(t),e.clearTimeout(t)),t},i.setInterval=function(t,i){t=em(this,t),this.clearTimersOnDispose_();var n=e.setInterval(t,i);return this.setIntervalIds_.add(n),n},i.clearInterval=function(t){return this.setIntervalIds_.has(t)&&(this.setIntervalIds_.delete(t),e.clearInterval(t)),t},i.requestAnimationFrame=function(t){var i,n=this;return this.supportsRaf_?(this.clearTimersOnDispose_(),t=em(this,t),i=e.requestAnimationFrame(function(){n.rafIds_.has(i)&&n.rafIds_.delete(i),t()}),this.rafIds_.add(i),i):this.setTimeout(t,1e3/60)},i.cancelAnimationFrame=function(t){return this.supportsRaf_?(this.rafIds_.has(t)&&(this.rafIds_.delete(t),e.cancelAnimationFrame(t)),t):this.clearTimeout(t)},i.clearTimersOnDispose_=function(){var e=this;this.clearingTimersOnDispose_||(this.clearingTimersOnDispose_=!0,this.one("dispose",function(){[["rafIds_","cancelAnimationFrame"],["setTimeoutIds_","clearTimeout"],["setIntervalIds_","clearInterval"]].forEach(function(t){var i=t[0],n=t[1];e[i].forEach(e[n],e)}),e.clearingTimersOnDispose_=!1}))},t.registerComponent=function(e,i){if("string"!=typeof e||!e)throw Error('Illegal component name, "'+e+'"; must be a non-empty string.');var n,r=t.getComponent("Tech"),s=r&&r.isTech(i),a=t===i||t.prototype.isPrototypeOf(i.prototype);if(s||!a)throw Error('Illegal component, "'+e+'"; '+(n=s?"techs must be registered using Tech.registerTech()":"must be a Component subclass")+".");e=eL(e),t.components_||(t.components_={});var o=t.getComponent("Player");if("Player"===e&&o&&o.players){var u=o.players,l=Object.keys(u);if(u&&0<l.length&&l.map(function(e){return u[e]}).every(Boolean))throw Error("Can not register Player component after player has been created.")}return t.components_[e]=i,t.components_[eP(e)]=i},t.getComponent=function(e){if(e&&t.components_)return t.components_[e]},t}();eD.prototype.supportsRaf_="function"==typeof e.requestAnimationFrame&&"function"==typeof e.cancelAnimationFrame,eD.registerComponent("Component",eD);var eA=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e};a(function(e){function t(i){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?e.exports=t=function(e){return typeof e}:e.exports=t=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(i)}e.exports=t}),a(function(e){function t(i){return e.exports=t=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},t(i)}e.exports=t});var eO,e2,eR,eN,e3=function(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t},e4=e.navigator&&e.navigator.userAgent||"",eU=/AppleWebKit\/([\d.]+)/i.exec(e4),e1=eU?parseFloat(eU.pop()):null,eM=/iPod/i.test(e4),e6=(eO=e4.match(/OS (\d+)_/i))&&eO[1]?eO[1]:null,eB=/Android/i.test(e4),ej=function(){var e=e4.match(/Android (\d+)(?:\.(\d+))?(?:\.(\d+))*/i);if(!e)return null;var t=e[1]&&parseFloat(e[1]),i=e[2]&&parseFloat(e[2]);return t&&i?parseFloat(e[1]+"."+e[2]):t||null}(),eF=eB&&ej<5&&e1<537,e7=/Firefox/i.test(e4),e5=/Edg/i.test(e4),eH=!e5&&(/Chrome/i.test(e4)||/CriOS/i.test(e4)),eq=(e2=e4.match(/(Chrome|CriOS)\/(\d+)/))&&e2[2]?parseFloat(e2[2]):null,eV=(!(eN=(eR=/MSIE\s(\d+)\.\d/.exec(e4))&&parseFloat(eR[1]))&&/Trident\/7.0/i.test(e4)&&/rv:11.0/.test(e4)&&(eN=11),eN),eW=/Safari/i.test(e4)&&!eH&&!eB&&!e5,ez=/Windows/i.test(e4),eG=v()&&("ontouchstart"in e||e.navigator.maxTouchPoints||e.DocumentTouch&&e.document instanceof e.DocumentTouch),eX=/iPad/i.test(e4)||eW&&eG&&!/iPhone/i.test(e4),eK=/iPhone/i.test(e4)&&!eX,eY=eK||eX||eM,e9=(eW||eY)&&!eH,eQ=Object.freeze({__proto__:null,IS_IPOD:eM,IOS_VERSION:e6,IS_ANDROID:eB,ANDROID_VERSION:ej,IS_NATIVE_ANDROID:eF,IS_FIREFOX:e7,IS_EDGE:e5,IS_CHROME:eH,CHROME_VERSION:eq,IE_VERSION:eV,IS_SAFARI:eW,IS_WINDOWS:ez,TOUCH_ENABLED:eG,IS_IPAD:eX,IS_IPHONE:eK,IS_IOS:eY,IS_ANY_SAFARI:e9});function eJ(e,t,i,n){return function(e,t,i){if("number"!=typeof t||t<0||i<t)throw Error("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+t+") is non-numeric or out of bounds (0-"+i+").")}(e,n,i.length-1),i[n][t]}function eZ(e){return void 0===e||0===e.length?{length:0,start:function(){throw Error("This TimeRanges object is empty")},end:function(){throw Error("This TimeRanges object is empty")}}:{length:e.length,start:eJ.bind(null,"start",0,e),end:eJ.bind(null,"end",1,e)}}function te(e,t){return Array.isArray(e)?eZ(e):void 0===e||void 0===t?eZ():eZ([[e,t]])}function tt(e,t){var i,n,r=0;if(!t)return 0;e&&e.length||(e=te(0,0));for(var s=0;s<e.length;s++)i=e.start(s),t<(n=e.end(s))&&(n=t),r+=n-i;return r/t}for(var ti,tn={prefixed:!0},tr=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror","fullscreen"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror","-webkit-full-screen"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror","-moz-full-screen"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError","-ms-fullscreen"]],ts=tr[0],ta=0;ta<tr.length;ta++)if(tr[ta][1]in t){ti=tr[ta];break}if(ti){for(var to=0;to<ti.length;to++)tn[ts[to]]=ti[to];tn.prefixed=ti[0]!==ts[0]}function tu(e){if(e instanceof tu)return e;"number"==typeof e?this.code=e:"string"==typeof e?this.message=e:d(e)&&("number"==typeof e.code&&(this.code=e.code),h(this,e)),this.message||(this.message=tu.defaultMessages[this.code]||"")}tu.prototype.code=0,tu.prototype.message="",tu.prototype.status=null,tu.errorTypes=["MEDIA_ERR_CUSTOM","MEDIA_ERR_ABORTED","MEDIA_ERR_NETWORK","MEDIA_ERR_DECODE","MEDIA_ERR_SRC_NOT_SUPPORTED","MEDIA_ERR_ENCRYPTED"],tu.defaultMessages={1:"You aborted the media playback",2:"A network error caused the media download to fail part-way.",3:"The media playback was aborted due to a corruption problem or because the media used features your browser did not support.",4:"The media could not be loaded, either because the server or network failed or because the format is not supported.",5:"The media is encrypted and we do not have the keys to decrypt it."};for(var tl=0;tl<tu.errorTypes.length;tl++)tu[tu.errorTypes[tl]]=tl,tu.prototype[tu.errorTypes[tl]]=tl;var tc=function(e,t){var i,n=null;try{i=JSON.parse(e,t)}catch(r){n=r}return[n,i]};function th(e){return null!=e&&"function"==typeof e.then}function td(e){th(e)&&e.then(null,function(e){})}function tp(e){return["kind","label","language","id","inBandMetadataTrackDispatchType","mode","src"].reduce(function(t,i,n){return e[i]&&(t[i]=e[i]),t},{cues:e.cues&&Array.prototype.map.call(e.cues,function(e){return{startTime:e.startTime,endTime:e.endTime,text:e.text,id:e.id}})})}var tf=function(e){var t=e.$$("track"),i=Array.prototype.map.call(t,function(e){return e.track});return Array.prototype.map.call(t,function(e){var t=tp(e.track);return e.src&&(t.src=e.src),t}).concat(Array.prototype.filter.call(e.textTracks(),function(e){return -1===i.indexOf(e)}).map(tp))},tm=a(function(e,t){function i(e){if(e&&"object"==typeof e){var t=e.which||e.keyCode||e.charCode;t&&(e=t)}if("number"==typeof e)return a[e];var i,s=String(e);return(i=n[s.toLowerCase()])?i:(i=r[s.toLowerCase()])||(1===s.length?s.charCodeAt(0):void 0)}i.isEventKey=function(e,t){if(e&&"object"==typeof e){var i,s=e.which||e.keyCode||e.charCode;if(null==s)return!1;if("string"==typeof t){if((i=n[t.toLowerCase()])||(i=r[t.toLowerCase()]))return i===s}else if("number"==typeof t)return t===s;return!1}};var n=(t=e.exports=i).code=t.codes={backspace:8,tab:9,enter:13,shift:16,ctrl:17,alt:18,"pause/break":19,"caps lock":20,esc:27,space:32,"page up":33,"page down":34,end:35,home:36,left:37,up:38,right:39,down:40,insert:45,delete:46,command:91,"left command":91,"right command":93,"numpad *":106,"numpad +":107,"numpad -":109,"numpad .":110,"numpad /":111,"num lock":144,"scroll lock":145,"my computer":182,"my calculator":183,";":186,"=":187,",":188,"-":189,".":190,"/":191,"`":192,"[":219,"\\":220,"]":221,"'":222},r=t.aliases={windows:91,"⇧":16,"⌥":18,"⌃":17,"⌘":91,ctl:17,control:17,option:18,pause:19,break:19,caps:20,return:13,escape:27,spc:32,spacebar:32,pgup:33,pgdn:34,ins:45,del:46,cmd:91};for(s=97;s<123;s++)n[String.fromCharCode(s)]=s-32;for(var s=48;s<58;s++)n[s-48]=s;for(s=1;s<13;s++)n["f"+s]=s+111;for(s=0;s<10;s++)n["numpad "+s]=s+96;var a=t.names=t.title={};for(s in n)a[n[s]]=s;for(var o in r)n[o]=r[o]}),tg=(tm.code,tm.codes,tm.aliases,tm.names,tm.title,"vjs-modal-dialog"),tv=function(i){function n(e,t){var n;return(n=i.call(this,e,t)||this).opened_=n.hasBeenOpened_=n.hasBeenFilled_=!1,n.closeable(!n.options_.uncloseable),n.content(n.options_.content),n.contentEl_=T("div",{className:tg+"-content"},{role:"document"}),n.descEl_=T("p",{className:tg+"-description vjs-control-text",id:n.el().getAttribute("aria-describedby")}),_(n.descEl_,n.description()),n.el_.appendChild(n.descEl_),n.el_.appendChild(n.contentEl_),n}e3(n,i);var r=n.prototype;return r.createEl=function(){return i.prototype.createEl.call(this,"div",{className:this.buildCSSClass(),tabIndex:-1},{"aria-describedby":this.id()+"_description","aria-hidden":"true","aria-label":this.label(),role:"dialog"})},r.dispose=function(){this.contentEl_=null,this.descEl_=null,this.previouslyActiveEl_=null,i.prototype.dispose.call(this)},r.buildCSSClass=function(){return tg+" vjs-hidden "+i.prototype.buildCSSClass.call(this)},r.label=function(){return this.localize(this.options_.label||"Modal Window")},r.description=function(){var e=this.options_.description||this.localize("This is a modal window.");return this.closeable()&&(e+=" "+this.localize("This modal can be closed by pressing the Escape key or activating the close button.")),e},r.open=function(){if(!this.opened_){var e=this.player();this.trigger("beforemodalopen"),this.opened_=!0,!this.options_.fillAlways&&(this.hasBeenOpened_||this.hasBeenFilled_)||this.fill(),this.wasPlaying_=!e.paused(),this.options_.pauseOnOpen&&this.wasPlaying_&&e.pause(),this.on("keydown",this.handleKeyDown),this.hadControls_=e.controls(),e.controls(!1),this.show(),this.conditionalFocus_(),this.el().setAttribute("aria-hidden","false"),this.trigger("modalopen"),this.hasBeenOpened_=!0}},r.opened=function(e){return"boolean"==typeof e&&this[e?"open":"close"](),this.opened_},r.close=function(){if(this.opened_){var e=this.player();this.trigger("beforemodalclose"),this.opened_=!1,this.wasPlaying_&&this.options_.pauseOnOpen&&e.play(),this.off("keydown",this.handleKeyDown),this.hadControls_&&e.controls(!0),this.hide(),this.el().setAttribute("aria-hidden","true"),this.trigger("modalclose"),this.conditionalBlur_(),this.options_.temporary&&this.dispose()}},r.closeable=function(e){if("boolean"==typeof e){var t=this.closeable_=!!e,i=this.getChild("closeButton");if(t&&!i){var n=this.contentEl_;this.contentEl_=this.el_,i=this.addChild("closeButton",{controlText:"Close Modal Dialog"}),this.contentEl_=n,this.on(i,"close",this.close)}!t&&i&&(this.off(i,"close",this.close),this.removeChild(i),i.dispose())}return this.closeable_},r.fill=function(){this.fillWith(this.content())},r.fillWith=function(e){var t=this.contentEl(),i=t.parentNode,n=t.nextSibling;this.trigger("beforemodalfill"),this.hasBeenFilled_=!0,i.removeChild(t),this.empty(),H(t,e),this.trigger("modalfill"),n?i.insertBefore(t,n):i.appendChild(t);var r=this.getChild("closeButton");r&&i.appendChild(r.el_)},r.empty=function(){this.trigger("beforemodalempty"),B(this.contentEl()),this.trigger("modalempty")},r.content=function(e){return void 0!==e&&(this.content_=e),this.content_},r.conditionalFocus_=function(){var e=t.activeElement,i=this.player_.el_;this.previouslyActiveEl_=null,(i.contains(e)||i===e)&&(this.previouslyActiveEl_=e,this.focus())},r.conditionalBlur_=function(){this.previouslyActiveEl_&&(this.previouslyActiveEl_.focus(),this.previouslyActiveEl_=null)},r.handleKeyDown=function(e){if(e.stopPropagation(),tm.isEventKey(e,"Escape")&&this.closeable())return e.preventDefault(),void this.close();if(tm.isEventKey(e,"Tab")){for(var i,n=this.focusableEls_(),r=this.el_.querySelector(":focus"),s=0;s<n.length;s++)if(r===n[s]){i=s;break}t.activeElement===this.el_&&(i=0),e.shiftKey&&0===i?(n[n.length-1].focus(),e.preventDefault()):e.shiftKey||i!==n.length-1||(n[0].focus(),e.preventDefault())}},r.focusableEls_=function(){var t=this.el_.querySelectorAll("*");return Array.prototype.filter.call(t,function(t){return(t instanceof e.HTMLAnchorElement||t instanceof e.HTMLAreaElement)&&t.hasAttribute("href")||(t instanceof e.HTMLInputElement||t instanceof e.HTMLSelectElement||t instanceof e.HTMLTextAreaElement||t instanceof e.HTMLButtonElement)&&!t.hasAttribute("disabled")||t instanceof e.HTMLIFrameElement||t instanceof e.HTMLObjectElement||t instanceof e.HTMLEmbedElement||t.hasAttribute("tabindex")&&-1!==t.getAttribute("tabindex")||t.hasAttribute("contenteditable")})},n}(eD);tv.prototype.options_={pauseOnOpen:!0,temporary:!0},eD.registerComponent("ModalDialog",tv);var ty=function(e){function t(t){var i;void 0===t&&(t=[]),(i=e.call(this)||this).tracks_=[],Object.defineProperty(eA(i),"length",{get:function(){return this.tracks_.length}});for(var n=0;n<t.length;n++)i.addTrack(t[n]);return i}e3(t,e);var i=t.prototype;return i.addTrack=function(e){var t=this.tracks_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.tracks_[t]}}),-1===this.tracks_.indexOf(e)&&(this.tracks_.push(e),this.trigger({track:e,type:"addtrack",target:this}))},i.removeTrack=function(e){for(var t,i=0,n=this.length;i<n;i++)if(this[i]===e){(t=this[i]).off&&t.off(),this.tracks_.splice(i,1);break}t&&this.trigger({track:t,type:"removetrack",target:this})},i.getTrackById=function(e){for(var t=null,i=0,n=this.length;i<n;i++){var r=this[i];if(r.id===e){t=r;break}}return t},t}(ev);for(var t8 in ty.prototype.allowedEvents_={change:"change",addtrack:"addtrack",removetrack:"removetrack"},ty.prototype.allowedEvents_)ty.prototype["on"+t8]=null;function t$(e,t){for(var i=0;i<e.length;i++)Object.keys(e[i]).length&&t.id!==e[i].id&&(e[i].enabled=!1)}function tb(e,t){for(var i=0;i<e.length;i++)Object.keys(e[i]).length&&t.id!==e[i].id&&(e[i].selected=!1)}function tT(i){var n=["protocol","hostname","port","pathname","search","hash","host"],r=t.createElement("a");r.href=i;var s,a=""===r.host&&"file:"!==r.protocol;a&&((s=t.createElement("div")).innerHTML='<a href="'+i+'"></a>',r=s.firstChild,s.setAttribute("style","display:none; position:absolute;"),t.body.appendChild(s));for(var o={},u=0;u<n.length;u++)o[n[u]]=r[n[u]];return"http:"===o.protocol&&(o.host=o.host.replace(/:80$/,"")),"https:"===o.protocol&&(o.host=o.host.replace(/:443$/,"")),o.protocol||(o.protocol=e.location.protocol),a&&t.body.removeChild(s),o}function t_(e){if(!e.match(/^https?:\/\//)){var i=t.createElement("div");i.innerHTML='<a href="'+e+'">x</a>',e=i.firstChild.href}return e}function tS(e){if("string"==typeof e){var t=/^(\/?)([\s\S]*?)((?:\.{1,2}|[^\/]+?)(\.([^\.\/\?]+)))(?:[\/]*|[\?].*)$/.exec(e);if(t)return t.pop().toLowerCase()}return""}function tk(t,i){void 0===i&&(i=e.location);var n=tT(t);return(":"===n.protocol?i.protocol:n.protocol)+n.host!==i.protocol+i.host}var tC=function(e){function t(t){var i;void 0===t&&(t=[]);for(var n=t.length-1;0<=n;n--)if(t[n].enabled){t$(t,t[n]);break}return(i=e.call(this,t)||this).changing_=!1,i}e3(t,e);var i=t.prototype;return i.addTrack=function(t){var i=this;t.enabled&&t$(this,t),e.prototype.addTrack.call(this,t),t.addEventListener&&(t.enabledChange_=function(){i.changing_||(i.changing_=!0,t$(i,t),i.changing_=!1,i.trigger("change"))},t.addEventListener("enabledchange",t.enabledChange_))},i.removeTrack=function(t){e.prototype.removeTrack.call(this,t),t.removeEventListener&&t.enabledChange_&&(t.removeEventListener("enabledchange",t.enabledChange_),t.enabledChange_=null)},t}(ty),tE=function(e){function t(t){var i;void 0===t&&(t=[]);for(var n=t.length-1;0<=n;n--)if(t[n].selected){tb(t,t[n]);break}return(i=e.call(this,t)||this).changing_=!1,Object.defineProperty(eA(i),"selectedIndex",{get:function(){for(var e=0;e<this.length;e++)if(this[e].selected)return e;return -1},set:function(){}}),i}e3(t,e);var i=t.prototype;return i.addTrack=function(t){var i=this;t.selected&&tb(this,t),e.prototype.addTrack.call(this,t),t.addEventListener&&(t.selectedChange_=function(){i.changing_||(i.changing_=!0,tb(i,t),i.changing_=!1,i.trigger("change"))},t.addEventListener("selectedchange",t.selectedChange_))},i.removeTrack=function(t){e.prototype.removeTrack.call(this,t),t.removeEventListener&&t.selectedChange_&&(t.removeEventListener("selectedchange",t.selectedChange_),t.selectedChange_=null)},t}(ty),tw=function(e){function t(){return e.apply(this,arguments)||this}e3(t,e);var i=t.prototype;return i.addTrack=function(t){var i=this;e.prototype.addTrack.call(this,t),this.queueChange_||(this.queueChange_=function(){return i.queueTrigger("change")}),this.triggerSelectedlanguagechange||(this.triggerSelectedlanguagechange_=function(){return i.trigger("selectedlanguagechange")}),t.addEventListener("modechange",this.queueChange_),-1===["metadata","chapters"].indexOf(t.kind)&&t.addEventListener("modechange",this.triggerSelectedlanguagechange_)},i.removeTrack=function(t){e.prototype.removeTrack.call(this,t),t.removeEventListener&&(this.queueChange_&&t.removeEventListener("modechange",this.queueChange_),this.selectedlanguagechange_&&t.removeEventListener("modechange",this.triggerSelectedlanguagechange_))},t}(ty),t0=function(){function e(e){void 0===e&&(e=[]),this.trackElements_=[],Object.defineProperty(this,"length",{get:function(){return this.trackElements_.length}});for(var t=0,i=e.length;t<i;t++)this.addTrackElement_(e[t])}var t=e.prototype;return t.addTrackElement_=function(e){var t=this.trackElements_.length;""+t in this||Object.defineProperty(this,t,{get:function(){return this.trackElements_[t]}}),-1===this.trackElements_.indexOf(e)&&this.trackElements_.push(e)},t.getTrackElementByTrack_=function(e){for(var t,i=0,n=this.trackElements_.length;i<n;i++)if(e===this.trackElements_[i].track){t=this.trackElements_[i];break}return t},t.removeTrackElement_=function(e){for(var t=0,i=this.trackElements_.length;t<i;t++)if(e===this.trackElements_[t]){this.trackElements_[t].track&&"function"==typeof this.trackElements_[t].track.off&&this.trackElements_[t].track.off(),"function"==typeof this.trackElements_[t].off&&this.trackElements_[t].off(),this.trackElements_.splice(t,1);break}},e}(),tx=function(){function e(t){e.prototype.setCues_.call(this,t),Object.defineProperty(this,"length",{get:function(){return this.length_}})}var t=e.prototype;return t.setCues_=function(e){var t=this.length||0,i=0,n=e.length;function r(e){""+e in this||Object.defineProperty(this,""+e,{get:function(){return this.cues_[e]}})}if(this.cues_=e,this.length_=e.length,t<n)for(i=t;i<n;i++)r.call(this,i)},t.getCueById=function(e){for(var t=null,i=0,n=this.length;i<n;i++){var r=this[i];if(r.id===e){t=r;break}}return t},e}(),tP={alternative:"alternative",captions:"captions",main:"main",sign:"sign",subtitles:"subtitles",commentary:"commentary"},tL={alternative:"alternative",descriptions:"descriptions",main:"main","main-desc":"main-desc",translation:"translation",commentary:"commentary"},tI={subtitles:"subtitles",captions:"captions",descriptions:"descriptions",chapters:"chapters",metadata:"metadata"},tD={disabled:"disabled",hidden:"hidden",showing:"showing"},tA=function(e){function t(t){function i(e){Object.defineProperty(eA(n),e,{get:function(){return r[e]},set:function(){}})}void 0===t&&(t={}),n=e.call(this)||this;var n,r={id:t.id||"vjs_track_"+ei(),kind:t.kind||"",label:t.label||"",language:t.language||""};for(var s in r)i(s);return n}return e3(t,e),t}(ev),tO=Object.freeze({__proto__:null,parseUrl:tT,getAbsoluteURL:t_,getFileExtension:tS,isCrossOrigin:tk}),t2=function(e){var t=tR.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)},tR=Object.prototype.toString,tN=function(e){var t={};return e&&e.trim().split("\n").forEach(function(e){var i=e.indexOf(":"),n=e.slice(0,i).trim().toLowerCase(),r=e.slice(i+1).trim();void 0===t[n]?t[n]=r:Array.isArray(t[n])?t[n].push(r):t[n]=[t[n],r]}),t},t3=tU;function t4(e,t,i){var n=e;return t2(t)?(i=t,"string"==typeof e&&(n={uri:e})):n=o({},t,{uri:e}),n.callback=i,n}function tU(e,t,i){return t1(t=t4(e,t,i))}function t1(e){if(void 0===e.callback)throw Error("callback argument missing");var t=!1,i=function(i,n,r){t||(t=!0,e.callback(i,n,r))};function n(e){return clearTimeout(o),e instanceof Error||(e=Error(""+(e||"Unknown XMLHttpRequest Error"))),e.statusCode=0,i(e,m)}function r(){if(!a){clearTimeout(o);var t,n=m,r=null;return 0!==(t=e.useXDR&&void 0===u.status?200:1223===u.status?204:u.status)?(n={body:function(){var e=void 0;if(e=u.response?u.response:u.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(i){}return null}(u),f)try{e=JSON.parse(e)}catch(t){}return e}(),statusCode:t,method:c,headers:{},url:l,rawRequest:u},u.getAllResponseHeaders&&(n.headers=tN(u.getAllResponseHeaders()))):r=Error("Internal XMLHttpRequest Error"),i(r,n,n.body)}}var s,a,o,u=e.xhr||null,l=(u=u||(e.cors||e.useXDR?new tU.XDomainRequest:new tU.XMLHttpRequest)).url=e.uri||e.url,c=u.method=e.method||"GET",h=e.body||e.data,d=u.headers=e.headers||{},p=!!e.sync,f=!1,m={body:void 0,headers:{},statusCode:0,method:c,url:l,rawRequest:u};if("json"in e&&!1!==e.json&&(f=!0,d.accept||d.Accept||(d.Accept="application/json"),"GET"!==c&&"HEAD"!==c&&(d["content-type"]||d["Content-Type"]||(d["Content-Type"]="application/json"),h=JSON.stringify(!0===e.json?h:e.json))),u.onreadystatechange=function(){4===u.readyState&&setTimeout(r,0)},u.onload=r,u.onerror=n,u.onprogress=function(){},u.onabort=function(){a=!0},u.ontimeout=n,u.open(c,l,!p,e.username,e.password),p||(u.withCredentials=!!e.withCredentials),!p&&0<e.timeout&&(o=setTimeout(function(){if(!a){a=!0,u.abort("timeout");var e=Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",n(e)}},e.timeout)),u.setRequestHeader)for(s in d)d.hasOwnProperty(s)&&u.setRequestHeader(s,d[s]);else if(e.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(e.headers))throw Error("Headers cannot be set on an XDomainRequest object");return"responseType"in e&&(u.responseType=e.responseType),"beforeSend"in e&&"function"==typeof e.beforeSend&&e.beforeSend(u),u.send(h||null),u}function tM(t,i){var n=new e.WebVTT.Parser(e,e.vttjs,e.WebVTT.StringDecoder()),s=[];n.oncue=function(e){i.addCue(e)},n.onparsingerror=function(e){s.push(e)},n.onflush=function(){i.trigger({type:"loadeddata",target:i})},n.parse(t),0<s.length&&(e.console&&e.console.groupCollapsed&&e.console.groupCollapsed("Text Track parsing errors for "+i.src),s.forEach(function(e){return r.error(e)}),e.console&&e.console.groupEnd&&e.console.groupEnd()),n.flush()}function t6(t,i){var n={uri:t},s=tk(t);s&&(n.cors=s),t3(n,em(this,function(t,n,s){if(t)return r.error(t,n);i.loaded_=!0,"function"!=typeof e.WebVTT?i.tech_&&i.tech_.any(["vttjsloaded","vttjserror"],function(e){if("vttjserror"!==e.type)return tM(s,i);r.error("vttjs failed to load, stopping trying to process "+i.src)}):tM(s,i)}))}tU.XMLHttpRequest=e.XMLHttpRequest||function(){},tU.XDomainRequest="withCredentials"in new tU.XMLHttpRequest?tU.XMLHttpRequest:e.XDomainRequest,function(e,t){for(var i=0;i<e.length;i++)t(e[i])}(["get","put","post","patch","head","delete"],function(e){tU["delete"===e?"del":e]=function(t,i,n){return(i=t4(t,i,n)).method=e.toUpperCase(),t1(i)}}),t3.default=tU;var tB=function(t){function i(e){if(void 0===e&&(e={}),!e.tech)throw Error("A tech was not provided.");var i,n=eI(e,{kind:tI[e.kind]||"subtitles",language:e.language||e.srclang||""}),r=tD[n.mode]||"disabled",s=n.default;"metadata"!==n.kind&&"chapters"!==n.kind||(r="hidden"),(i=t.call(this,n)||this).tech_=n.tech,i.cues_=[],i.activeCues_=[],i.preload_=!1!==i.tech_.preloadTextTracks;var a=new tx(i.cues_),o=new tx(i.activeCues_),u=!1,l=em(eA(i),function(){this.activeCues=this.activeCues,u&&(this.trigger("cuechange"),u=!1)});return"disabled"!==r&&i.tech_.ready(function(){i.tech_.on("timeupdate",l)},!0),Object.defineProperties(eA(i),{default:{get:function(){return s},set:function(){}},mode:{get:function(){return r},set:function(e){var t=this;tD[e]&&(r=e,this.preload_||"disabled"===r||0!==this.cues.length||t6(this.src,this),"disabled"!==r?this.tech_.ready(function(){t.tech_.on("timeupdate",l)},!0):this.tech_.off("timeupdate",l),this.trigger("modechange"))}},cues:{get:function(){return this.loaded_?a:null},set:function(){}},activeCues:{get:function(){if(!this.loaded_)return null;if(0===this.cues.length)return o;for(var e=this.tech_.currentTime(),t=[],i=0,n=this.cues.length;i<n;i++){var r=this.cues[i];r.startTime<=e&&r.endTime>=e?t.push(r):r.startTime===r.endTime&&r.startTime<=e&&r.startTime+.5>=e&&t.push(r)}if(u=!1,t.length!==this.activeCues_.length)u=!0;else for(var s=0;s<t.length;s++)-1===this.activeCues_.indexOf(t[s])&&(u=!0);return this.activeCues_=t,o.setCues_(this.activeCues_),o},set:function(){}}}),n.src?(i.src=n.src,i.preload_||(i.loaded_=!0),(i.preload_||s||"subtitles"!==n.kind&&"captions"!==n.kind)&&t6(i.src,eA(i))):i.loaded_=!0,i}e3(i,t);var n=i.prototype;return n.addCue=function(t){var i=t;if(e.vttjs&&!(t instanceof e.vttjs.VTTCue)){for(var n in i=new e.vttjs.VTTCue(t.startTime,t.endTime,t.text),t)n in i||(i[n]=t[n]);i.id=t.id,i.originalCue_=t}for(var r=this.tech_.textTracks(),s=0;s<r.length;s++)r[s]!==this&&r[s].removeCue(i);this.cues_.push(i),this.cues.setCues_(this.cues_)},n.removeCue=function(e){for(var t=this.cues_.length;t--;){var i=this.cues_[t];if(i===e||i.originalCue_&&i.originalCue_===e){this.cues_.splice(t,1),this.cues.setCues_(this.cues_);break}}},i}(tA);tB.prototype.allowedEvents_={cuechange:"cuechange"};var tj=function(e){function t(t){void 0===t&&(t={});var i,n=eI(t,{kind:tL[t.kind]||""});i=e.call(this,n)||this;var r=!1;return Object.defineProperty(eA(i),"enabled",{get:function(){return r},set:function(e){"boolean"==typeof e&&e!==r&&(r=e,this.trigger("enabledchange"))}}),n.enabled&&(i.enabled=n.enabled),i.loaded_=!0,i}return e3(t,e),t}(tA),tF=function(e){function t(t){void 0===t&&(t={});var i,n=eI(t,{kind:tP[t.kind]||""});i=e.call(this,n)||this;var r=!1;return Object.defineProperty(eA(i),"selected",{get:function(){return r},set:function(e){"boolean"==typeof e&&e!==r&&(r=e,this.trigger("selectedchange"))}}),n.selected&&(i.selected=n.selected),i}return e3(t,e),t}(tA),t7=function(e){function t(t){void 0===t&&(t={}),i=e.call(this)||this;var i,n,r=new tB(t);return i.kind=r.kind,i.src=r.src,i.srclang=r.language,i.label=r.label,i.default=r.default,Object.defineProperties(eA(i),{readyState:{get:function(){return n}},track:{get:function(){return r}}}),n=0,r.addEventListener("loadeddata",function(){n=2,i.trigger({type:"load",target:eA(i)})}),i}return e3(t,e),t}(ev);t7.prototype.allowedEvents_={load:"load"},t7.NONE=0,t7.LOADING=1,t7.LOADED=2,t7.ERROR=3;var t5={audio:{ListClass:tC,TrackClass:tj,capitalName:"Audio"},video:{ListClass:tE,TrackClass:tF,capitalName:"Video"},text:{ListClass:tw,TrackClass:tB,capitalName:"Text"}};Object.keys(t5).forEach(function(e){t5[e].getterName=e+"Tracks",t5[e].privateName=e+"Tracks_"});var tH={remoteText:{ListClass:tw,TrackClass:tB,capitalName:"RemoteText",getterName:"remoteTextTracks",privateName:"remoteTextTracks_"},remoteTextEl:{ListClass:t0,TrackClass:t7,capitalName:"RemoteTextTrackEls",getterName:"remoteTextTrackEls",privateName:"remoteTextTrackEls_"}},tq=o({},t5,tH);tH.names=Object.keys(tH),t5.names=Object.keys(t5),tq.names=[].concat(tH.names).concat(t5.names);var tV=Object.create||function(e){if(1!==arguments.length)throw Error("Object.create shim only accepts one parameter.");return tW.prototype=e,new tW};function tW(){}function tz(e,t){this.name="ParsingError",this.code=e.code,this.message=t||e.message}function tG(e){function t(e,t,i,n){return 3600*(0|e)+60*(0|t)+(0|i)+(0|n)/1e3}var i=e.match(/^(\d+):(\d{1,2})(:\d{1,2})?\.(\d{3})/);return i?i[3]?t(i[1],i[2],i[3].replace(":",""),i[4]):59<i[1]?t(i[1],i[2],0,i[4]):t(0,i[1],i[2],i[4]):null}function tX(){this.values=tV(null)}function tK(e,t,i,n){var r=n?e.split(n):[e];for(var s in r)if("string"==typeof r[s]){var a=r[s].split(i);2===a.length&&t(a[0],a[1])}}function tY(e,t,i){var n=e;function r(){var t=tG(e);if(null===t)throw new tz(tz.Errors.BadTimeStamp,"Malformed timestamp: "+n);return e=e.replace(/^[^\sa-zA-Z-]+/,""),t}function s(){e=e.replace(/^\s+/,"")}if(s(),t.startTime=r(),s(),"-->"!==e.substr(0,3))throw new tz(tz.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '-->'): "+n);e=e.substr(3),s(),t.endTime=r(),s(),function(e,t){var n=new tX;tK(e,function(e,t){switch(e){case"region":for(var r=i.length-1;0<=r;r--)if(i[r].id===t){n.set(e,i[r].region);break}break;case"vertical":n.alt(e,t,["rl","lr"]);break;case"line":var s=t.split(","),a=s[0];n.integer(e,a),n.percent(e,a)&&n.set("snapToLines",!1),n.alt(e,a,["auto"]),2===s.length&&n.alt("lineAlign",s[1],["start","center","end"]);break;case"position":s=t.split(","),n.percent(e,s[0]),2===s.length&&n.alt("positionAlign",s[1],["start","center","end"]);break;case"size":n.percent(e,t);break;case"align":n.alt(e,t,["start","center","end","left","right"])}},/:/,/\s/),t.region=n.get("region",null),t.vertical=n.get("vertical","");try{t.line=n.get("line","auto")}catch(r){}t.lineAlign=n.get("lineAlign","start"),t.snapToLines=n.get("snapToLines",!0),t.size=n.get("size",100);try{t.align=n.get("align","center")}catch(s){t.align=n.get("align","middle")}try{t.position=n.get("position","auto")}catch(a){t.position=n.get("position",{start:0,left:0,center:50,middle:50,end:100,right:100},t.align)}t.positionAlign=n.get("positionAlign",{start:"start",left:"start",center:"center",middle:"center",end:"end",right:"end"},t.align)}(e,t)}((tz.prototype=tV(Error.prototype)).constructor=tz).Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},tX.prototype={set:function(e,t){this.get(e)||""===t||(this.values[e]=t)},get:function(e,t,i){return i?this.has(e)?this.values[e]:t[i]:this.has(e)?this.values[e]:t},has:function(e){return e in this.values},alt:function(e,t,i){for(var n=0;n<i.length;++n)if(t===i[n]){this.set(e,t);break}},integer:function(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))},percent:function(e,t){return!!(t.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&0<=(t=parseFloat(t))&&t<=100)&&(this.set(e,t),!0)}};var t9=t.createElement("textarea"),tQ={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},tJ={white:"rgba(255,255,255,1)",lime:"rgba(0,255,0,1)",cyan:"rgba(0,255,255,1)",red:"rgba(255,0,0,1)",yellow:"rgba(255,255,0,1)",magenta:"rgba(255,0,255,1)",blue:"rgba(0,0,255,1)",black:"rgba(0,0,0,1)"},tZ={v:"title",lang:"lang"},ie={rt:"ruby"};function it(e,t){function i(){if(!t)return null;var e,i=t.match(/^([^<]*)(<[^>]*>?)?/);return e=i[1]?i[1]:i[2],t=t.substr(e.length),e}function n(t,i){var n=tQ[t];if(!n)return null;var r=e.document.createElement(n),s=tZ[t];return s&&i&&(r[s]=i.trim()),r}for(var r,s,a,o,u=e.document.createElement("div"),l=u,c=[];null!==(r=i());)if("<"!==r[0])l.appendChild(e.document.createTextNode((s=r,t9.innerHTML=s,s=t9.textContent,t9.textContent="",s)));else{if("/"===r[1]){c.length&&c[c.length-1]===r.substr(2).replace(">","")&&(c.pop(),l=l.parentNode);continue}var h,d=tG(r.substr(1,r.length-2));if(d){h=e.document.createProcessingInstruction("timestamp",d),l.appendChild(h);continue}var p=r.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!p||!(h=n(p[1],p[3]))||(a=l,ie[(o=h).localName]&&ie[o.localName]!==a.localName))continue;if(p[2]){var f=p[2].split(".");f.forEach(function(e){var t=/^bg_/.test(e),i=t?e.slice(3):e;if(tJ.hasOwnProperty(i)){var n=t?"background-color":"color",r=tJ[i];h.style[n]=r}}),h.className=f.join(" ")}c.push(p[1]),l.appendChild(h),l=h}return u}var ii=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function ir(e){for(var t=0;t<ii.length;t++){var i=ii[t];if(e>=i[0]&&e<=i[1])return!0}return!1}function is(){}function ia(e,t,i){is.call(this),this.cue=t,this.cueDiv=it(e,t.text);var n={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(n,this.cueDiv),this.div=e.document.createElement("div"),n={direction:function(e){var t=[],i="";if(!e||!e.childNodes)return"ltr";function n(e,t){for(var i=t.childNodes.length-1;0<=i;i--)e.push(t.childNodes[i])}function r(e){if(!e||!e.length)return null;var t=e.pop(),i=t.textContent||t.innerText;if(i){var s=i.match(/^.*(\n|\r)/);return s?s[e.length=0]:i}return"ruby"===t.tagName?r(e):t.childNodes?(n(e,t),r(e)):void 0}for(n(t,e);i=r(t);)for(var s=0;s<i.length;s++)if(ir(i.charCodeAt(s)))return"rtl";return"ltr"}(this.cueDiv),writingMode:""===t.vertical?"horizontal-tb":"lr"===t.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===t.align?"center":t.align,font:i.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(n),this.div.appendChild(this.cueDiv);var r=0;switch(t.positionAlign){case"start":r=t.position;break;case"center":r=t.position-t.size/2;break;case"end":r=t.position-t.size}""===t.vertical?this.applyStyles({left:this.formatStyle(r,"%"),width:this.formatStyle(t.size,"%")}):this.applyStyles({top:this.formatStyle(r,"%"),height:this.formatStyle(t.size,"%")}),this.move=function(e){this.applyStyles({top:this.formatStyle(e.top,"px"),bottom:this.formatStyle(e.bottom,"px"),left:this.formatStyle(e.left,"px"),right:this.formatStyle(e.right,"px"),height:this.formatStyle(e.height,"px"),width:this.formatStyle(e.width,"px")})}}function io(e){var t,i,n,r;if(e.div){i=e.div.offsetHeight,n=e.div.offsetWidth,r=e.div.offsetTop;var s=(s=e.div.childNodes)&&(s=s[0])&&s.getClientRects&&s.getClientRects();e=e.div.getBoundingClientRect(),t=s?Math.max(s[0]&&s[0].height||0,e.height/s.length):0}this.left=e.left,this.right=e.right,this.top=e.top||r,this.height=e.height||i,this.bottom=e.bottom||r+(e.height||i),this.width=e.width||n,this.lineHeight=void 0!==t?t:e.lineHeight}function iu(e,t,i,n){var r=new io(t),s=t.cue,a=function(e){if("number"==typeof e.line&&(e.snapToLines||0<=e.line&&e.line<=100))return e.line;if(!e.track||!e.track.textTrackList||!e.track.textTrackList.mediaElement)return -1;for(var t=e.track,i=t.textTrackList,n=0,r=0;r<i.length&&i[r]!==t;r++)"showing"===i[r].mode&&n++;return -1*++n}(s),o=[];if(s.snapToLines){switch(s.vertical){case"":o=["+y","-y"],u="height";break;case"rl":o=["+x","-x"],u="width";break;case"lr":o=["-x","+x"],u="width"}var u,l=r.lineHeight,c=l*Math.round(a),h=i[u]+l,d=o[0];Math.abs(c)>h&&(c=c<0?-1:1,c*=Math.ceil(h/l)*l),a<0&&(c+=""===s.vertical?i.height:i.width,o=o.reverse()),r.move(d,c)}else{var p=r.lineHeight/i.height*100;switch(s.lineAlign){case"center":a-=p/2;break;case"end":a-=p}switch(s.vertical){case"":t.applyStyles({top:t.formatStyle(a,"%")});break;case"rl":t.applyStyles({left:t.formatStyle(a,"%")});break;case"lr":t.applyStyles({right:t.formatStyle(a,"%")})}o=["+y","-x","+x","-y"],r=new io(t)}var f=function(e,t){for(var r,s=new io(e),a=1,o=0;o<t.length;o++){for(;e.overlapsOppositeAxis(i,t[o])||e.within(i)&&e.overlapsAny(n);)e.move(t[o]);if(e.within(i))return e;var u=e.intersectPercentage(i);u<a&&(r=new io(e),a=u),e=new io(s)}return r||s}(r,o);t.move(f.toCSSCompatValues(i))}function il(){}is.prototype.applyStyles=function(e,t){for(var i in t=t||this.div,e)e.hasOwnProperty(i)&&(t.style[i]=e[i])},is.prototype.formatStyle=function(e,t){return 0===e?0:e+t},(ia.prototype=tV(is.prototype)).constructor=ia,io.prototype.move=function(e,t){switch(t=void 0!==t?t:this.lineHeight,e){case"+x":this.left+=t,this.right+=t;break;case"-x":this.left-=t,this.right-=t;break;case"+y":this.top+=t,this.bottom+=t;break;case"-y":this.top-=t,this.bottom-=t}},io.prototype.overlaps=function(e){return this.left<e.right&&this.right>e.left&&this.top<e.bottom&&this.bottom>e.top},io.prototype.overlapsAny=function(e){for(var t=0;t<e.length;t++)if(this.overlaps(e[t]))return!0;return!1},io.prototype.within=function(e){return this.top>=e.top&&this.bottom<=e.bottom&&this.left>=e.left&&this.right<=e.right},io.prototype.overlapsOppositeAxis=function(e,t){switch(t){case"+x":return this.left<e.left;case"-x":return this.right>e.right;case"+y":return this.top<e.top;case"-y":return this.bottom>e.bottom}},io.prototype.intersectPercentage=function(e){return Math.max(0,Math.min(this.right,e.right)-Math.max(this.left,e.left))*Math.max(0,Math.min(this.bottom,e.bottom)-Math.max(this.top,e.top))/(this.height*this.width)},io.prototype.toCSSCompatValues=function(e){return{top:this.top-e.top,bottom:e.bottom-this.bottom,left:this.left-e.left,right:e.right-this.right,height:this.height,width:this.width}},io.getSimpleBoxPosition=function(e){var t=e.div?e.div.offsetHeight:e.tagName?e.offsetHeight:0,i=e.div?e.div.offsetWidth:e.tagName?e.offsetWidth:0,n=e.div?e.div.offsetTop:e.tagName?e.offsetTop:0;return{left:(e=e.div?e.div.getBoundingClientRect():e.tagName?e.getBoundingClientRect():e).left,right:e.right,top:e.top||n,height:e.height||t,bottom:e.bottom||n+(e.height||t),width:e.width||i}},il.StringDecoder=function(){return{decode:function(e){if(!e)return"";if("string"!=typeof e)throw Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}}},il.convertCueToDOMTree=function(e,t){return e&&t?it(e,t):null},il.processCues=function(e,t,i){if(!e||!t||!i)return null;for(;i.firstChild;)i.removeChild(i.firstChild);var n=e.document.createElement("div");if(n.style.position="absolute",n.style.left="0",n.style.right="0",n.style.top="0",n.style.bottom="0",n.style.margin="1.5%",i.appendChild(n),function(e){for(var t=0;t<e.length;t++)if(e[t].hasBeenReset||!e[t].displayState)return!0;return!1}(t)){var r=[],s=io.getSimpleBoxPosition(n),a={font:Math.round(.05*s.height*100)/100+"px sans-serif"};!function(){for(var i,o,u=0;u<t.length;u++)o=t[u],i=new ia(e,o,a),n.appendChild(i.div),iu(0,i,s,r),o.displayState=i.div,r.push(io.getSimpleBoxPosition(i))}()}else for(var o=0;o<t.length;o++)n.appendChild(t[o].displayState)},(il.Parser=function(e,t,i){i||(i=t,t={}),t=t||{},this.window=e,this.vttjs=t,this.state="INITIAL",this.buffer="",this.decoder=i||new TextDecoder("utf8"),this.regionList=[]}).prototype={reportOrThrowError:function(e){if(!(e instanceof tz))throw e;this.onparsingerror&&this.onparsingerror(e)},parse:function(e){var t,i=this;function n(){for(var e=i.buffer,t=0;t<e.length&&"\r"!==e[t]&&"\n"!==e[t];)++t;var n=e.substr(0,t);return"\r"===e[t]&&++t,"\n"===e[t]&&++t,i.buffer=e.substr(t),n}function r(e){e.match(/X-TIMESTAMP-MAP/)?tK(e,function(e,t){if("X-TIMESTAMP-MAP"===e){var n,r;n=t,r=new tX,tK(n,function(e,t){switch(e){case"MPEGT":r.integer(e+"S",t);break;case"LOCA":r.set(e+"L",tG(t))}},/[^\d]:/,/,/),i.ontimestampmap&&i.ontimestampmap({MPEGTS:r.get("MPEGTS"),LOCAL:r.get("LOCAL")})}},/=/):tK(e,function(e,t){"Region"===e&&!function(e){var t=new tX;if(tK(e,function(e,i){switch(e){case"id":t.set(e,i);break;case"width":t.percent(e,i);break;case"lines":t.integer(e,i);break;case"regionanchor":case"viewportanchor":var n=i.split(",");if(2!==n.length)break;var r=new tX;if(r.percent("x",n[0]),r.percent("y",n[1]),!r.has("x")||!r.has("y"))break;t.set(e+"X",r.get("x")),t.set(e+"Y",r.get("y"));break;case"scroll":t.alt(e,i,["up"])}},/=/,/\s/),t.has("id")){var n=new(i.vttjs.VTTRegion||i.window.VTTRegion);n.width=t.get("width",100),n.lines=t.get("lines",3),n.regionAnchorX=t.get("regionanchorX",0),n.regionAnchorY=t.get("regionanchorY",100),n.viewportAnchorX=t.get("viewportanchorX",0),n.viewportAnchorY=t.get("viewportanchorY",100),n.scroll=t.get("scroll",""),i.onregion&&i.onregion(n),i.regionList.push({id:t.get("id"),region:n})}}(t)},/:/)}e&&(i.buffer+=i.decoder.decode(e,{stream:!0}));try{if("INITIAL"===i.state){if(!/\r\n|\n/.test(i.buffer))return this;var s=(t=n()).match(/^WEBVTT([ \t].*)?$/);if(!s||!s[0])throw new tz(tz.Errors.BadSignature);i.state="HEADER"}for(var a=!1;i.buffer&&/\r\n|\n/.test(i.buffer);)switch(a?a=!1:t=n(),i.state){case"HEADER":/:/.test(t)?r(t):t||(i.state="ID");continue;case"NOTE":t||(i.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(t)){i.state="NOTE";break}if(!t)continue;i.cue=new(i.vttjs.VTTCue||i.window.VTTCue)(0,0,"");try{i.cue.align="center"}catch(o){i.cue.align="middle"}if(i.state="CUE",-1===t.indexOf("-->")){i.cue.id=t;continue}case"CUE":try{tY(t,i.cue,i.regionList)}catch(u){i.reportOrThrowError(u),i.cue=null,i.state="BADCUE";continue}i.state="CUETEXT";continue;case"CUETEXT":var l=-1!==t.indexOf("-->");if(!t||l&&(a=!0)){i.oncue&&i.oncue(i.cue),i.cue=null,i.state="ID";continue}i.cue.text&&(i.cue.text+="\n"),i.cue.text+=t.replace(/\u2028/g,"\n").replace(/u2029/g,"\n");continue;case"BADCUE":t||(i.state="ID");continue}}catch(c){i.reportOrThrowError(c),"CUETEXT"===i.state&&i.cue&&i.oncue&&i.oncue(i.cue),i.cue=null,i.state="INITIAL"===i.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var e=this;try{if(e.buffer+=e.decoder.decode(),(e.cue||"HEADER"===e.state)&&(e.buffer+="\n\n",e.parse()),"INITIAL"===e.state)throw new tz(tz.Errors.BadSignature)}catch(t){e.reportOrThrowError(t)}return e.onflush&&e.onflush(),this}};var ic=il,ih={"":1,lr:1,rl:1},id={start:1,center:1,end:1,left:1,right:1,auto:1,"line-left":1,"line-right":1};function ip(e){return"string"==typeof e&&!!id[e.toLowerCase()]&&e.toLowerCase()}function im(e,t,i){this.hasBeenReset=!1;var n="",r=!1,s=e,a=t,o=i,u=null,l="",c=!0,h="auto",d="start",p="auto",f="auto",m=100,g="center";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return n},set:function(e){n=""+e}},pauseOnExit:{enumerable:!0,get:function(){return r},set:function(e){r=!!e}},startTime:{enumerable:!0,get:function(){return s},set:function(e){if("number"!=typeof e)throw TypeError("Start time must be set to a number.");s=e,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e)throw TypeError("End time must be set to a number.");a=e,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return o},set:function(e){o=""+e,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return u},set:function(e){u=e,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return l},set:function(e){var t,i="string"==typeof(t=e)&&!!ih[t.toLowerCase()]&&t.toLowerCase();if(!1===i)throw SyntaxError("Vertical: an invalid or illegal direction string was specified.");l=i,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return c},set:function(e){c=!!e,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return h},set:function(e){if("number"!=typeof e&&"auto"!==e)throw SyntaxError("Line: an invalid number or illegal string was specified.");h=e,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return d},set:function(e){var t=ip(e);t&&(d=t,this.hasBeenReset=!0)}},position:{enumerable:!0,get:function(){return p},set:function(e){if(e<0||100<e)throw Error("Position must be between 0 and 100.");p=e,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return f},set:function(e){var t=ip(e);t&&(f=t,this.hasBeenReset=!0)}},size:{enumerable:!0,get:function(){return m},set:function(e){if(e<0||100<e)throw Error("Size must be between 0 and 100.");m=e,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return g},set:function(e){var t=ip(e);if(!t)throw SyntaxError("align: an invalid or illegal alignment string was specified.");g=t,this.hasBeenReset=!0}}}),this.displayState=void 0}im.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)};var ig=im,iv={"":!0,up:!0};function iy(e){return"number"==typeof e&&0<=e&&e<=100}function i8(){var e=100,t=3,i=0,n=100,r=0,s=100,a="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return e},set:function(t){if(!iy(t))throw Error("Width must be between 0 and 100.");e=t}},lines:{enumerable:!0,get:function(){return t},set:function(e){if("number"!=typeof e)throw TypeError("Lines must be set to a number.");t=e}},regionAnchorY:{enumerable:!0,get:function(){return n},set:function(e){if(!iy(e))throw Error("RegionAnchorX must be between 0 and 100.");n=e}},regionAnchorX:{enumerable:!0,get:function(){return i},set:function(e){if(!iy(e))throw Error("RegionAnchorY must be between 0 and 100.");i=e}},viewportAnchorY:{enumerable:!0,get:function(){return s},set:function(e){if(!iy(e))throw Error("ViewportAnchorY must be between 0 and 100.");s=e}},viewportAnchorX:{enumerable:!0,get:function(){return r},set:function(e){if(!iy(e))throw Error("ViewportAnchorX must be between 0 and 100.");r=e}},scroll:{enumerable:!0,get:function(){return a},set:function(e){var t,i="string"==typeof(t=e)&&!!iv[t.toLowerCase()]&&t.toLowerCase();!1===i||(a=i)}}})}var i$=a(function(t){var i=t.exports={WebVTT:ic,VTTCue:ig,VTTRegion:i8};e.vttjs=i,e.WebVTT=i.WebVTT;var n=i.VTTCue,r=i.VTTRegion,s=e.VTTCue,a=e.VTTRegion;i.shim=function(){e.VTTCue=n,e.VTTRegion=r},i.restore=function(){e.VTTCue=s,e.VTTRegion=a},e.VTTCue||i.shim()});i$.WebVTT,i$.VTTCue,i$.VTTRegion;var ib=function(i){function n(e,t){var n;return void 0===e&&(e={}),void 0===t&&(t=function(){}),e.reportTouchActivity=!1,(n=i.call(this,null,e,t)||this).hasStarted_=!1,n.on("playing",function(){this.hasStarted_=!0}),n.on("loadstart",function(){this.hasStarted_=!1}),tq.names.forEach(function(t){var i=tq[t];e&&e[i.getterName]&&(n[i.privateName]=e[i.getterName])}),n.featuresProgressEvents||n.manualProgressOn(),n.featuresTimeupdateEvents||n.manualTimeUpdatesOn(),["Text","Audio","Video"].forEach(function(t){!1===e["native"+t+"Tracks"]&&(n["featuresNative"+t+"Tracks"]=!1)}),!1===e.nativeCaptions||!1===e.nativeTextTracks?n.featuresNativeTextTracks=!1:!0!==e.nativeCaptions&&!0!==e.nativeTextTracks||(n.featuresNativeTextTracks=!0),n.featuresNativeTextTracks||n.emulateTextTracks(),n.preloadTextTracks=!1!==e.preloadTextTracks,n.autoRemoteTextTracks_=new tq.text.ListClass,n.initTrackListeners(),e.nativeControlsForTouch||n.emitTapEvents(),n.constructor&&(n.name_=n.constructor.name||"Unknown Tech"),n}e3(n,i);var s=n.prototype;return s.triggerSourceset=function(e){var t=this;this.isReady_||this.one("ready",function(){return t.setTimeout(function(){return t.triggerSourceset(e)},1)}),this.trigger({src:e,type:"sourceset"})},s.manualProgressOn=function(){this.on("durationchange",this.onDurationChange),this.manualProgress=!0,this.one("ready",this.trackProgress)},s.manualProgressOff=function(){this.manualProgress=!1,this.stopTrackingProgress(),this.off("durationchange",this.onDurationChange)},s.trackProgress=function(e){this.stopTrackingProgress(),this.progressInterval=this.setInterval(em(this,function(){var e=this.bufferedPercent();this.bufferedPercent_!==e&&this.trigger("progress"),1===(this.bufferedPercent_=e)&&this.stopTrackingProgress()}),500)},s.onDurationChange=function(e){this.duration_=this.duration()},s.buffered=function(){return te(0,0)},s.bufferedPercent=function(){return tt(this.buffered(),this.duration_)},s.stopTrackingProgress=function(){this.clearInterval(this.progressInterval)},s.manualTimeUpdatesOn=function(){this.manualTimeUpdates=!0,this.on("play",this.trackCurrentTime),this.on("pause",this.stopTrackingCurrentTime)},s.manualTimeUpdatesOff=function(){this.manualTimeUpdates=!1,this.stopTrackingCurrentTime(),this.off("play",this.trackCurrentTime),this.off("pause",this.stopTrackingCurrentTime)},s.trackCurrentTime=function(){this.currentTimeInterval&&this.stopTrackingCurrentTime(),this.currentTimeInterval=this.setInterval(function(){this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},250)},s.stopTrackingCurrentTime=function(){this.clearInterval(this.currentTimeInterval),this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},s.dispose=function(){this.clearTracks(t5.names),this.manualProgress&&this.manualProgressOff(),this.manualTimeUpdates&&this.manualTimeUpdatesOff(),i.prototype.dispose.call(this)},s.clearTracks=function(e){var t=this;(e=[].concat(e)).forEach(function(e){for(var i=t[e+"Tracks"]()||[],n=i.length;n--;){var r=i[n];"text"===e&&t.removeRemoteTextTrack(r),i.removeTrack(r)}})},s.cleanupAutoTextTracks=function(){for(var e=this.autoRemoteTextTracks_||[],t=e.length;t--;){var i=e[t];this.removeRemoteTextTrack(i)}},s.reset=function(){},s.error=function(e){return void 0!==e&&(this.error_=new tu(e),this.trigger("error")),this.error_},s.played=function(){return this.hasStarted_?te(0,0):te()},s.setCurrentTime=function(){this.manualTimeUpdates&&this.trigger({type:"timeupdate",target:this,manuallyTriggered:!0})},s.initTrackListeners=function(){var e=this;t5.names.forEach(function(t){function i(){e.trigger(t+"trackchange")}var n=e[t5[t].getterName]();n.addEventListener("removetrack",i),n.addEventListener("addtrack",i),e.on("dispose",function(){n.removeEventListener("removetrack",i),n.removeEventListener("addtrack",i)})})},s.addWebVttScript_=function(){var i=this;if(!e.WebVTT){if(t.body.contains(this.el())){if(!this.options_["vtt.js"]&&p(i$)&&0<Object.keys(i$).length)return void this.trigger("vttjsloaded");var n=t.createElement("script");n.src=this.options_["vtt.js"]||"https://vjs.zencdn.net/vttjs/0.14.1/vtt.min.js",n.onload=function(){i.trigger("vttjsloaded")},n.onerror=function(){i.trigger("vttjserror")},this.on("dispose",function(){n.onload=null,n.onerror=null}),e.WebVTT=!0,this.el().parentNode.appendChild(n)}else this.ready(this.addWebVttScript_)}},s.emulateTextTracks=function(){function e(e){return n.addTrack(e.track)}function t(e){return n.removeTrack(e.track)}var i=this,n=this.textTracks(),r=this.remoteTextTracks();function s(){return i.trigger("texttrackchange")}function a(){s();for(var e=0;e<n.length;e++){var t=n[e];t.removeEventListener("cuechange",s),"showing"===t.mode&&t.addEventListener("cuechange",s)}}r.on("addtrack",e),r.on("removetrack",t),this.addWebVttScript_(),a(),n.addEventListener("change",a),n.addEventListener("addtrack",a),n.addEventListener("removetrack",a),this.on("dispose",function(){r.off("addtrack",e),r.off("removetrack",t),n.removeEventListener("change",a),n.removeEventListener("addtrack",a),n.removeEventListener("removetrack",a);for(var i=0;i<n.length;i++)n[i].removeEventListener("cuechange",s)})},s.addTextTrack=function(e,t,i){var n,r,s,a,o,u,l;if(!e)throw Error("TextTrack kind is required but was not provided");return n=this,r=e,s=t,a=i,void 0===o&&(o={}),u=n.textTracks(),o.kind=r,s&&(o.label=s),a&&(o.language=a),o.tech=n,l=new tq.text.TrackClass(o),u.addTrack(l),l},s.createRemoteTextTrack=function(e){var t=eI(e,{tech:this});return new tH.remoteTextEl.TrackClass(t)},s.addRemoteTextTrack=function(e,t){var i=this;void 0===e&&(e={});var n=this.createRemoteTextTrack(e);return!0!==t&&!1!==t&&(r.warn('Calling addRemoteTextTrack without explicitly setting the "manualCleanup" parameter to `true` is deprecated and default to `false` in future version of video.js'),t=!0),this.remoteTextTrackEls().addTrackElement_(n),this.remoteTextTracks().addTrack(n.track),!0!==t&&this.ready(function(){return i.autoRemoteTextTracks_.addTrack(n.track)}),n},s.removeRemoteTextTrack=function(e){var t=this.remoteTextTrackEls().getTrackElementByTrack_(e);this.remoteTextTrackEls().removeTrackElement_(t),this.remoteTextTracks().removeTrack(e),this.autoRemoteTextTracks_.removeTrack(e)},s.getVideoPlaybackQuality=function(){return{}},s.requestPictureInPicture=function(){var t=this.options_.Promise||e.Promise;if(t)return t.reject()},s.setPoster=function(){},s.playsinline=function(){},s.setPlaysinline=function(){},s.overrideNativeAudioTracks=function(){},s.overrideNativeVideoTracks=function(){},s.canPlayType=function(){return""},n.canPlayType=function(){return""},n.canPlaySource=function(e,t){return n.canPlayType(e.type)},n.isTech=function(e){return e.prototype instanceof n||e instanceof n||e===n},n.registerTech=function(e,t){if(n.techs_||(n.techs_={}),!n.isTech(t))throw Error("Tech "+e+" must be a Tech");if(!n.canPlayType)throw Error("Techs must have a static canPlayType method on them");if(!n.canPlaySource)throw Error("Techs must have a static canPlaySource method on them");return e=eL(e),n.techs_[e]=t,n.techs_[eP(e)]=t,"Tech"!==e&&n.defaultTechOrder_.push(e),t},n.getTech=function(t){if(t)return n.techs_&&n.techs_[t]?n.techs_[t]:(t=eL(t),e&&e.videojs&&e.videojs[t]?(r.warn("The "+t+" tech was added to the videojs object when it should be registered using videojs.registerTech(name, tech)"),e.videojs[t]):void 0)},n}(eD);tq.names.forEach(function(e){var t=tq[e];ib.prototype[t.getterName]=function(){return this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName]}}),ib.prototype.featuresVolumeControl=!0,ib.prototype.featuresMuteControl=!0,ib.prototype.featuresFullscreenResize=!1,ib.prototype.featuresPlaybackRate=!1,ib.prototype.featuresProgressEvents=!1,ib.prototype.featuresSourceset=!1,ib.prototype.featuresTimeupdateEvents=!1,ib.prototype.featuresNativeTextTracks=!1,ib.withSourceHandlers=function(e){e.registerSourceHandler=function(t,i){var n=e.sourceHandlers;n=n||(e.sourceHandlers=[]),void 0===i&&(i=n.length),n.splice(i,0,t)},e.canPlayType=function(t){for(var i,n=e.sourceHandlers||[],r=0;r<n.length;r++)if(i=n[r].canPlayType(t))return i;return""},e.selectSourceHandler=function(t,i){for(var n=e.sourceHandlers||[],r=0;r<n.length;r++)if(n[r].canHandleSource(t,i))return n[r];return null},e.canPlaySource=function(t,i){var n=e.selectSourceHandler(t,i);return n?n.canHandleSource(t,i):""},["seekable","seeking","duration"].forEach(function(e){var t=this[e];"function"==typeof t&&(this[e]=function(){return this.sourceHandler_&&this.sourceHandler_[e]?this.sourceHandler_[e].apply(this.sourceHandler_,arguments):t.apply(this,arguments)})},e.prototype),e.prototype.setSource=function(t){var i=e.selectSourceHandler(t,this.options_);i||(e.nativeSourceHandler?i=e.nativeSourceHandler:r.error("No source handler found for the current source.")),this.disposeSourceHandler(),this.off("dispose",this.disposeSourceHandler),i!==e.nativeSourceHandler&&(this.currentSource_=t),this.sourceHandler_=i.handleSource(t,this,this.options_),this.one("dispose",this.disposeSourceHandler)},e.prototype.disposeSourceHandler=function(){this.currentSource_&&(this.clearTracks(["audio","video"]),this.currentSource_=null),this.cleanupAutoTextTracks(),this.sourceHandler_&&(this.sourceHandler_.dispose&&this.sourceHandler_.dispose(),this.sourceHandler_=null)}},eD.registerComponent("Tech",ib),ib.registerTech("Tech",ib),ib.defaultTechOrder_=[];var iT={},i_={},iS={};function ik(e,t,i,n){void 0===n&&(n=null);var r="call"+eL(i),s=e.reduce(i0(r),n),a=s===iS,o=a?null:t[i](s);return function(e,t,i,n){for(var r=e.length-1;0<=r;r--){var s=e[r];s[t]&&s[t](n,i)}}(e,i,o,a),o}var iC={buffered:1,currentTime:1,duration:1,muted:1,played:1,paused:1,seekable:1,volume:1},iE={setCurrentTime:1,setMuted:1,setVolume:1},iw={play:1,pause:1};function i0(e){return function(t,i){return t===iS?iS:i[e]?i[e](t):t}}function ix(e){return void 0===e&&(e=""),iP[tS(e).toLowerCase()]||""}var iP={opus:"video/ogg",ogv:"video/ogg",mp4:"video/mp4",mov:"video/mp4",m4v:"video/mp4",mkv:"video/x-matroska",m4a:"audio/mp4",mp3:"audio/mpeg",aac:"audio/aac",oga:"audio/ogg",m3u8:"application/x-mpegURL",jpg:"image/jpeg",jpeg:"image/jpeg",gif:"image/gif",png:"image/png",svg:"image/svg+xml",webp:"image/webp"};function iL(e){if(!e.type){var t=ix(e.src);t&&(e.type=t)}return e}var iI=function(e){function t(t,i,n){var r,s=eI({createEl:!1},i);if(r=e.call(this,t,s,n)||this,i.playerOptions.sources&&0!==i.playerOptions.sources.length)t.src(i.playerOptions.sources);else for(var a=0,o=i.playerOptions.techOrder;a<o.length;a++){var u=eL(o[a]),l=ib.getTech(u);if(u||(l=eD.getComponent(u)),l&&l.isSupported()){t.loadTech_(u);break}}return r}return e3(t,e),t}(eD);eD.registerComponent("MediaLoader",iI);var iD=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).emitTapEvents(),n.enable(),n}e3(t,e);var i=t.prototype;return i.createEl=function(t,i,n){void 0===t&&(t="div"),void 0===i&&(i={}),void 0===n&&(n={}),i=h({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass(),tabIndex:0},i),"button"===t&&r.error("Creating a ClickableComponent with an HTML element of "+t+" is not supported; use a Button instead."),n=h({role:"button"},n),this.tabIndex_=i.tabIndex;var s=e.prototype.createEl.call(this,t,i,n);return this.createControlTextEl(s),s},i.dispose=function(){this.controlTextEl_=null,e.prototype.dispose.call(this)},i.createControlTextEl=function(e){return this.controlTextEl_=T("span",{className:"vjs-control-text"},{"aria-live":"polite"}),e&&e.appendChild(this.controlTextEl_),this.controlText(this.controlText_,e),this.controlTextEl_},i.controlText=function(e,t){if(void 0===t&&(t=this.el()),void 0===e)return this.controlText_||"Need Text";var i=this.localize(e);this.controlText_=e,_(this.controlTextEl_,i),this.nonIconControl||t.setAttribute("title",i)},i.buildCSSClass=function(){return"vjs-control vjs-button "+e.prototype.buildCSSClass.call(this)},i.enable=function(){this.enabled_||(this.enabled_=!0,this.removeClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","false"),void 0!==this.tabIndex_&&this.el_.setAttribute("tabIndex",this.tabIndex_),this.on(["tap","click"],this.handleClick),this.on("keydown",this.handleKeyDown))},i.disable=function(){this.enabled_=!1,this.addClass("vjs-disabled"),this.el_.setAttribute("aria-disabled","true"),void 0!==this.tabIndex_&&this.el_.removeAttribute("tabIndex"),this.off("mouseover",this.handleMouseOver),this.off("mouseout",this.handleMouseOut),this.off(["tap","click"],this.handleClick),this.off("keydown",this.handleKeyDown)},i.handleClick=function(e){this.options_.clickHandler&&this.options_.clickHandler.call(this,arguments)},i.handleKeyDown=function(t){tm.isEventKey(t,"Space")||tm.isEventKey(t,"Enter")?(t.preventDefault(),t.stopPropagation(),this.trigger("click")):e.prototype.handleKeyDown.call(this,t)},t}(eD);eD.registerComponent("ClickableComponent",iD);var iA=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).update(),t.on("posterchange",em(eA(n),n.update)),n}e3(t,e);var i=t.prototype;return i.dispose=function(){this.player().off("posterchange",this.update),e.prototype.dispose.call(this)},i.createEl=function(){return T("div",{className:"vjs-poster",tabIndex:-1})},i.update=function(e){var t=this.player().poster();this.setSrc(t),t?this.show():this.hide()},i.setSrc=function(e){var t="";e&&(t='url("'+e+'")'),this.el_.style.backgroundImage=t},i.handleClick=function(e){if(this.player_.controls()){var t=this.player_.usingPlugin("eme")&&this.player_.eme.sessions&&0<this.player_.eme.sessions.length;!this.player_.tech(!0)||(eV||e5)&&t||this.player_.tech(!0).focus(),this.player_.paused()?td(this.player_.play()):this.player_.pause()}},t}(iD);eD.registerComponent("PosterImage",iA);var iO="#222",i2={monospace:"monospace",sansSerif:"sans-serif",serif:"serif",monospaceSansSerif:'"Andale Mono", "Lucida Console", monospace',monospaceSerif:'"Courier New", monospace',proportionalSansSerif:"sans-serif",proportionalSerif:"serif",casual:'"Comic Sans MS", Impact, fantasy',script:'"Monotype Corsiva", cursive',smallcaps:'"Andale Mono", "Lucida Console", monospace, sans-serif'};function iR(e,t){var i;if(4===e.length)i=e[1]+e[1]+e[2]+e[2]+e[3]+e[3];else{if(7!==e.length)throw Error("Invalid color code provided, "+e+"; must be formatted as e.g. #f0e or #f604e2.");i=e.slice(1)}return"rgba("+parseInt(i.slice(0,2),16)+","+parseInt(i.slice(2,4),16)+","+parseInt(i.slice(4,6),16)+","+t+")"}function iN(e,t,i){try{e.style[t]=i}catch(n){return}}var i3=function(t){function i(i,n,r){var s,a=em(eA(s=t.call(this,i,n,r)||this),s.updateDisplay);return i.on("loadstart",em(eA(s),s.toggleDisplay)),i.on("texttrackchange",a),i.on("loadedmetadata",em(eA(s),s.preselectTrack)),i.ready(em(eA(s),function(){if(i.tech_&&i.tech_.featuresNativeTextTracks)this.hide();else{i.on("fullscreenchange",a),i.on("playerresize",a),e.addEventListener("orientationchange",a),i.on("dispose",function(){return e.removeEventListener("orientationchange",a)});for(var t=this.options_.playerOptions.tracks||[],n=0;n<t.length;n++)this.player_.addRemoteTextTrack(t[n],!0);this.preselectTrack()}})),s}e3(i,t);var n=i.prototype;return n.preselectTrack=function(){for(var e,t,i,n={captions:1,subtitles:1},r=this.player_.textTracks(),s=this.player_.cache_.selectedLanguage,a=0;a<r.length;a++){var o=r[a];s&&s.enabled&&s.language&&s.language===o.language&&o.kind in n?i=o.kind===s.kind?o:i||o:s&&!s.enabled?t=e=i=null:o.default&&("descriptions"!==o.kind||e?o.kind in n&&!t&&(t=o):e=o)}i?i.mode="showing":t?t.mode="showing":e&&(e.mode="showing")},n.toggleDisplay=function(){this.player_.tech_&&this.player_.tech_.featuresNativeTextTracks?this.hide():this.show()},n.createEl=function(){return t.prototype.createEl.call(this,"div",{className:"vjs-text-track-display"},{"aria-live":"off","aria-atomic":"true"})},n.clearDisplay=function(){"function"==typeof e.WebVTT&&e.WebVTT.processCues(e,[],this.el_)},n.updateDisplay=function(){var e=this.player_.textTracks(),t=this.options_.allowMultipleShowingTracks;if(this.clearDisplay(),t){for(var i=[],n=0;n<e.length;++n){var r=e[n];"showing"===r.mode&&i.push(r)}this.updateForTrack(i)}else{for(var s=null,a=null,o=e.length;o--;){var u=e[o];"showing"===u.mode&&("descriptions"===u.kind?s=u:a=u)}a?("off"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","off"),this.updateForTrack(a)):s&&("assertive"!==this.getAttribute("aria-live")&&this.setAttribute("aria-live","assertive"),this.updateForTrack(s))}},n.updateDisplayState=function(t){for(var i=this.player_.textTrackSettings.getValues(),n=t.activeCues,r=n.length;r--;){var s=n[r];if(s){var a=s.displayState;if(i.color&&(a.firstChild.style.color=i.color),i.textOpacity&&iN(a.firstChild,"color",iR(i.color||"#fff",i.textOpacity)),i.backgroundColor&&(a.firstChild.style.backgroundColor=i.backgroundColor),i.backgroundOpacity&&iN(a.firstChild,"backgroundColor",iR(i.backgroundColor||"#000",i.backgroundOpacity)),i.windowColor&&(i.windowOpacity?iN(a,"backgroundColor",iR(i.windowColor,i.windowOpacity)):a.style.backgroundColor=i.windowColor),i.edgeStyle&&("dropshadow"===i.edgeStyle?a.firstChild.style.textShadow="2px 2px 3px #222, 2px 2px 4px #222, 2px 2px 5px "+iO:"raised"===i.edgeStyle?a.firstChild.style.textShadow="1px 1px #222, 2px 2px #222, 3px 3px "+iO:"depressed"===i.edgeStyle?a.firstChild.style.textShadow="1px 1px #ccc, 0 1px #ccc, -1px -1px #222, 0 -1px "+iO:"uniform"===i.edgeStyle&&(a.firstChild.style.textShadow="0 0 4px #222, 0 0 4px #222, 0 0 4px #222, 0 0 4px "+iO)),i.fontPercent&&1!==i.fontPercent){var o=e.parseFloat(a.style.fontSize);a.style.fontSize=o*i.fontPercent+"px",a.style.height="auto",a.style.top="auto",a.style.bottom="2px"}i.fontFamily&&"default"!==i.fontFamily&&("small-caps"===i.fontFamily?a.firstChild.style.fontVariant="small-caps":a.firstChild.style.fontFamily=i2[i.fontFamily])}}},n.updateForTrack=function(t){if(Array.isArray(t)||(t=[t]),"function"==typeof e.WebVTT&&!t.every(function(e){return!e.activeCues})){for(var i=[],n=0;n<t.length;++n)for(var r=t[n],s=0;s<r.activeCues.length;++s)i.push(r.activeCues[s]);e.WebVTT.processCues(e,i,this.el_);for(var a=0;a<t.length;++a){for(var o=t[a],u=0;u<o.activeCues.length;++u){var l=o.activeCues[u].displayState;C(l,"vjs-text-track-cue"),C(l,"vjs-text-track-cue-"+(o.language?o.language:a))}this.player_.textTrackSettings&&this.updateDisplayState(o)}}},i}(eD);eD.registerComponent("TextTrackDisplay",i3);var i4=function(e){function t(){return e.apply(this,arguments)||this}return e3(t,e),t.prototype.createEl=function(){var t=this.player_.isAudio(),i=this.localize(t?"Audio Player":"Video Player"),n=T("span",{className:"vjs-control-text",innerHTML:this.localize("{1} is loading.",[i])}),r=e.prototype.createEl.call(this,"div",{className:"vjs-loading-spinner",dir:"ltr"});return r.appendChild(n),r},t}(eD);eD.registerComponent("LoadingSpinner",i4);var iU=function(e){function t(){return e.apply(this,arguments)||this}e3(t,e);var i=t.prototype;return i.createEl=function(e,t,i){void 0===t&&(t={}),void 0===i&&(i={}),t=h({innerHTML:'<span aria-hidden="true" class="vjs-icon-placeholder"></span>',className:this.buildCSSClass()},t),i=h({type:"button"},i);var n=eD.prototype.createEl.call(this,"button",t,i);return this.createControlTextEl(n),n},i.addChild=function(e,t){void 0===t&&(t={});var i=this.constructor.name;return r.warn("Adding an actionable (user controllable) child to a Button ("+i+") is not supported; use a ClickableComponent instead."),eD.prototype.addChild.call(this,e,t)},i.enable=function(){e.prototype.enable.call(this),this.el_.removeAttribute("disabled")},i.disable=function(){e.prototype.disable.call(this),this.el_.setAttribute("disabled","disabled")},i.handleKeyDown=function(t){tm.isEventKey(t,"Space")||tm.isEventKey(t,"Enter")?t.stopPropagation():e.prototype.handleKeyDown.call(this,t)},t}(iD);eD.registerComponent("Button",iU);var i1=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).mouseused_=!1,n.on("mousedown",n.handleMouseDown),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-big-play-button"},i.handleClick=function(e){var t=this.player_.play();if(this.mouseused_&&e.clientX&&e.clientY){var i=this.player_.usingPlugin("eme")&&this.player_.eme.sessions&&0<this.player_.eme.sessions.length;return td(t),void(!this.player_.tech(!0)||(eV||e5)&&i||this.player_.tech(!0).focus())}var n=this.player_.getChild("controlBar"),r=n&&n.getChild("playToggle");if(r){var s=function(){return r.focus()};th(t)?t.then(s,function(){}):this.setTimeout(s,1)}else this.player_.tech(!0).focus()},i.handleKeyDown=function(t){this.mouseused_=!1,e.prototype.handleKeyDown.call(this,t)},i.handleMouseDown=function(e){this.mouseused_=!0},t}(iU);i1.prototype.controlText_="Play Video",eD.registerComponent("BigPlayButton",i1);var iM=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).controlText(i&&i.controlText||n.localize("Close")),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-close-button "+e.prototype.buildCSSClass.call(this)},i.handleClick=function(e){this.trigger({type:"close",bubbles:!1})},i.handleKeyDown=function(t){tm.isEventKey(t,"Esc")?(t.preventDefault(),t.stopPropagation(),this.trigger("click")):e.prototype.handleKeyDown.call(this,t)},t}(iU);eD.registerComponent("CloseButton",iM);var i6=function(e){function t(t,i){var n;return void 0===i&&(i={}),n=e.call(this,t,i)||this,i.replay=void 0===i.replay||i.replay,n.on(t,"play",n.handlePlay),n.on(t,"pause",n.handlePause),i.replay&&n.on(t,"ended",n.handleEnded),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-play-control "+e.prototype.buildCSSClass.call(this)},i.handleClick=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},i.handleSeeked=function(e){this.removeClass("vjs-ended"),this.player_.paused()?this.handlePause(e):this.handlePlay(e)},i.handlePlay=function(e){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.controlText("Pause")},i.handlePause=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.controlText("Play")},i.handleEnded=function(e){this.removeClass("vjs-playing"),this.addClass("vjs-ended"),this.controlText("Replay"),this.one(this.player_,"seeked",this.handleSeeked)},t}(iU);function iB(e,t){var i=Math.floor((e=e<0?0:e)%60),n=Math.floor(e/60%60),r=Math.floor(e/3600);return(isNaN(e)||e===1/0)&&(r=n=i="-"),(r=0<r||0<Math.floor(t/3600)?r+":":"")+(n=((r||10<=Math.floor(t/60%60))&&n<10?"0"+n:n)+":")+(i=i<10?"0"+i:i)}i6.prototype.controlText_="Play",eD.registerComponent("PlayToggle",i6);var ij=iB;function iF(e,t){return void 0===t&&(t=e),ij(e,t)}var i7=function(e){function i(t,i){var n;return(n=e.call(this,t,i)||this).on(t,["timeupdate","ended"],n.updateContent),n.updateTextNode_(),n}e3(i,e);var n=i.prototype;return n.createEl=function(){var t=this.buildCSSClass(),i=e.prototype.createEl.call(this,"div",{className:t+" vjs-time-control vjs-control",innerHTML:'<span class="vjs-control-text" role="presentation">'+this.localize(this.labelText_)+" </span>"});return this.contentEl_=T("span",{className:t+"-display"},{"aria-live":"off",role:"presentation"}),i.appendChild(this.contentEl_),i},n.dispose=function(){this.contentEl_=null,this.textNode_=null,e.prototype.dispose.call(this)},n.updateTextNode_=function(e){var i=this;void 0===e&&(e=0),e=iF(e),this.formattedTime_!==e&&(this.formattedTime_=e,this.requestAnimationFrame(function(){if(i.contentEl_){var e=i.textNode_;i.textNode_=t.createTextNode(i.formattedTime_),i.textNode_&&(e?i.contentEl_.replaceChild(i.textNode_,e):i.contentEl_.appendChild(i.textNode_))}}))},n.updateContent=function(e){},i}(eD);i7.prototype.labelText_="Time",i7.prototype.controlText_="Time",eD.registerComponent("TimeDisplay",i7);var i5=function(e){function t(){return e.apply(this,arguments)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-current-time"},i.updateContent=function(e){var t;t=this.player_.ended()?this.player_.duration():this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime(),this.updateTextNode_(t)},t}(i7);i5.prototype.labelText_="Current Time",i5.prototype.controlText_="Current Time",eD.registerComponent("CurrentTimeDisplay",i5);var iH=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).on(t,"durationchange",n.updateContent),n.on(t,"loadstart",n.updateContent),n.on(t,"loadedmetadata",n.updateContent),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-duration"},i.updateContent=function(e){var t=this.player_.duration();this.updateTextNode_(t)},t}(i7);iH.prototype.labelText_="Duration",iH.prototype.controlText_="Duration",eD.registerComponent("DurationDisplay",iH);var iq=function(e){function t(){return e.apply(this,arguments)||this}return e3(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-time-control vjs-time-divider",innerHTML:"<div><span>/</span></div>"},{"aria-hidden":!0})},t}(eD);eD.registerComponent("TimeDivider",iq);var iV=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).on(t,"durationchange",n.updateContent),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-remaining-time"},i.createEl=function(){var t=e.prototype.createEl.call(this);return t.insertBefore(T("span",{},{"aria-hidden":!0},"-"),this.contentEl_),t},i.updateContent=function(e){var t;"number"==typeof this.player_.duration()&&(t=this.player_.ended()?0:this.player_.remainingTimeDisplay?this.player_.remainingTimeDisplay():this.player_.remainingTime(),this.updateTextNode_(t))},t}(i7);iV.prototype.labelText_="Remaining Time",iV.prototype.controlText_="Remaining Time",eD.registerComponent("RemainingTimeDisplay",iV);var iW=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).updateShowing(),n.on(n.player(),"durationchange",n.updateShowing),n}e3(t,e);var i=t.prototype;return i.createEl=function(){var t=e.prototype.createEl.call(this,"div",{className:"vjs-live-control vjs-control"});return this.contentEl_=T("div",{className:"vjs-live-display",innerHTML:'<span class="vjs-control-text">'+this.localize("Stream Type")+" </span>"+this.localize("LIVE")},{"aria-live":"off"}),t.appendChild(this.contentEl_),t},i.dispose=function(){this.contentEl_=null,e.prototype.dispose.call(this)},i.updateShowing=function(e){this.player().duration()===1/0?this.show():this.hide()},t}(eD);eD.registerComponent("LiveDisplay",iW);var iz=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).updateLiveEdgeStatus(),n.player_.liveTracker&&n.on(n.player_.liveTracker,"liveedgechange",n.updateLiveEdgeStatus),n}e3(t,e);var i=t.prototype;return i.createEl=function(){var t=e.prototype.createEl.call(this,"button",{className:"vjs-seek-to-live-control vjs-control"});return this.textEl_=T("span",{className:"vjs-seek-to-live-text",innerHTML:this.localize("LIVE")},{"aria-hidden":"true"}),t.appendChild(this.textEl_),t},i.updateLiveEdgeStatus=function(){!this.player_.liveTracker||this.player_.liveTracker.atLiveEdge()?(this.setAttribute("aria-disabled",!0),this.addClass("vjs-at-live-edge"),this.controlText("Seek to live, currently playing live")):(this.setAttribute("aria-disabled",!1),this.removeClass("vjs-at-live-edge"),this.controlText("Seek to live, currently behind live"))},i.handleClick=function(){this.player_.liveTracker.seekToLiveEdge()},i.dispose=function(){this.player_.liveTracker&&this.off(this.player_.liveTracker,"liveedgechange",this.updateLiveEdgeStatus),this.textEl_=null,e.prototype.dispose.call(this)},t}(iU);function iG(e,t,i){return Math.min(i,Math.max(t,isNaN(e=Number(e))?t:e))}iz.prototype.controlText_="Seek to live, currently playing live",eD.registerComponent("SeekToLive",iz);var iX=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).bar=n.getChild(n.options_.barName),n.vertical(!!n.options_.vertical),n.enable(),n}e3(t,e);var i=t.prototype;return i.enabled=function(){return this.enabled_},i.enable=function(){this.enabled()||(this.on("mousedown",this.handleMouseDown),this.on("touchstart",this.handleMouseDown),this.on("keydown",this.handleKeyDown),this.on("click",this.handleClick),this.on(this.player_,"controlsvisible",this.update),this.playerEvent&&this.on(this.player_,this.playerEvent,this.update),this.removeClass("disabled"),this.setAttribute("tabindex",0),this.enabled_=!0)},i.disable=function(){if(this.enabled()){var e=this.bar.el_.ownerDocument;this.off("mousedown",this.handleMouseDown),this.off("touchstart",this.handleMouseDown),this.off("keydown",this.handleKeyDown),this.off("click",this.handleClick),this.off(this.player_,"controlsvisible",this.update),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.removeAttribute("tabindex"),this.addClass("disabled"),this.playerEvent&&this.off(this.player_,this.playerEvent,this.update),this.enabled_=!1}},i.createEl=function(t,i,n){return void 0===i&&(i={}),void 0===n&&(n={}),i.className=i.className+" vjs-slider",i=h({tabIndex:0},i),n=h({role:"slider","aria-valuenow":0,"aria-valuemin":0,"aria-valuemax":100,tabIndex:0},n),e.prototype.createEl.call(this,t,i,n)},i.handleMouseDown=function(e){var t=this.bar.el_.ownerDocument;"mousedown"===e.type&&e.preventDefault(),"touchstart"!==e.type||eH||e.preventDefault(),A(),this.addClass("vjs-sliding"),this.trigger("slideractive"),this.on(t,"mousemove",this.handleMouseMove),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchmove",this.handleMouseMove),this.on(t,"touchend",this.handleMouseUp),this.handleMouseMove(e)},i.handleMouseMove=function(e){},i.handleMouseUp=function(){var e=this.bar.el_.ownerDocument;O(),this.removeClass("vjs-sliding"),this.trigger("sliderinactive"),this.off(e,"mousemove",this.handleMouseMove),this.off(e,"mouseup",this.handleMouseUp),this.off(e,"touchmove",this.handleMouseMove),this.off(e,"touchend",this.handleMouseUp),this.update()},i.update=function(){var e=this;if(this.el_&&this.bar){var t=this.getProgress();return t===this.progress_||(this.progress_=t,this.requestAnimationFrame(function(){var i=e.vertical()?"height":"width";e.bar.el().style[i]=(100*t).toFixed(2)+"%"})),t}},i.getProgress=function(){return Number(iG(this.getPercent(),0,1).toFixed(4))},i.calculateDistance=function(e){var t=U(this.el_,e);return this.vertical()?t.y:t.x},i.handleKeyDown=function(t){tm.isEventKey(t,"Left")||tm.isEventKey(t,"Down")?(t.preventDefault(),t.stopPropagation(),this.stepBack()):tm.isEventKey(t,"Right")||tm.isEventKey(t,"Up")?(t.preventDefault(),t.stopPropagation(),this.stepForward()):e.prototype.handleKeyDown.call(this,t)},i.handleClick=function(e){e.stopPropagation(),e.preventDefault()},i.vertical=function(e){if(void 0===e)return this.vertical_||!1;this.vertical_=!!e,this.vertical_?this.addClass("vjs-slider-vertical"):this.addClass("vjs-slider-horizontal")},t}(eD);function iK(e,t){return iG(e/t*100,0,100).toFixed(2)+"%"}eD.registerComponent("Slider",iX);var iY=function(e){function i(t,i){var n;return(n=e.call(this,t,i)||this).partEls_=[],n.on(t,"progress",n.update),n}e3(i,e);var n=i.prototype;return n.createEl=function(){var i=e.prototype.createEl.call(this,"div",{className:"vjs-load-progress"}),n=T("span",{className:"vjs-control-text"}),r=T("span",{textContent:this.localize("Loaded")}),s=t.createTextNode(": ");return this.percentageEl_=T("span",{className:"vjs-control-text-loaded-percentage",textContent:"0%"}),i.appendChild(n),n.appendChild(r),n.appendChild(s),n.appendChild(this.percentageEl_),i},n.dispose=function(){this.partEls_=null,this.percentageEl_=null,e.prototype.dispose.call(this)},n.update=function(e){var t=this;this.requestAnimationFrame(function(){var e=t.player_.liveTracker,i=t.player_.buffered(),n=e&&e.isLive()?e.seekableEnd():t.player_.duration(),r=t.player_.bufferedEnd(),s=t.partEls_,a=iK(r,n);t.percent_!==a&&(t.el_.style.width=a,_(t.percentageEl_,a),t.percent_=a);for(var o=0;o<i.length;o++){var u=i.start(o),l=i.end(o),c=s[o];c||(c=t.el_.appendChild(T()),s[o]=c),c.dataset.start===u&&c.dataset.end===l||(c.dataset.start=u,c.dataset.end=l,c.style.left=iK(u,r),c.style.width=iK(l-u,r))}for(var h=s.length;h>i.length;h--)t.el_.removeChild(s[h-1]);s.length=i.length})},i}(eD);eD.registerComponent("LoadProgressBar",iY);var i9=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).update=eg(em(eA(n),n.update),30),n}e3(t,e);var i=t.prototype;return i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-time-tooltip"},{"aria-hidden":"true"})},i.update=function(e,t,i){var n=R(this.el_),r=R(this.player_.el()),s=e.width*t;if(r&&n){var a=e.left-r.left+s,o=e.width-s+(r.right-e.right),u=n.width/2;a<u?u+=u-a:o<u&&(u=o),u<0?u=0:u>n.width&&(u=n.width),this.el_.style.right="-"+u+"px",this.write(i)}},i.write=function(e){_(this.el_,e)},i.updateTime=function(e,t,i,n){var r=this;this.rafId_&&this.cancelAnimationFrame(this.rafId_),this.rafId_=this.requestAnimationFrame(function(){var s,a=r.player_.duration();if(r.player_.liveTracker&&r.player_.liveTracker.isLive()){var o=r.player_.liveTracker.liveWindow(),u=o-t*o;s=(u<1?"":"-")+iF(u,o)}else s=iF(i,a);r.update(e,t,s),n&&n()})},t}(eD);eD.registerComponent("TimeTooltip",i9);var iQ=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).update=eg(em(eA(n),n.update),30),n}e3(t,e);var i=t.prototype;return i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-play-progress vjs-slider-bar"},{"aria-hidden":"true"})},i.update=function(e,t){var i=this.getChild("timeTooltip");if(i){var n=this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime();i.updateTime(e,t,n)}},t}(eD);iQ.prototype.options_={children:[]},eY||eB||iQ.prototype.options_.children.push("timeTooltip"),eD.registerComponent("PlayProgressBar",iQ);var iJ=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).update=eg(em(eA(n),n.update),30),n}e3(t,e);var i=t.prototype;return i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-mouse-display"})},i.update=function(e,t){var i=this,n=t*this.player_.duration();this.getChild("timeTooltip").updateTime(e,t,n,function(){i.el_.style.left=e.width*t+"px"})},t}(eD);iJ.prototype.options_={children:["timeTooltip"]},eD.registerComponent("MouseTimeDisplay",iJ);var iZ=function(e){function i(t,i){var n;return(n=e.call(this,t,i)||this).setEventHandlers_(),n}e3(i,e);var n=i.prototype;return n.setEventHandlers_=function(){this.update_=em(this,this.update),this.update=eg(this.update_,30),this.on(this.player_,["ended","durationchange","timeupdate"],this.update),this.player_.liveTracker&&this.on(this.player_.liveTracker,"liveedgechange",this.update),this.updateInterval=null,this.on(this.player_,["playing"],this.enableInterval_),this.on(this.player_,["ended","pause","waiting"],this.disableInterval_),"hidden"in t&&"visibilityState"in t&&this.on(t,"visibilitychange",this.toggleVisibility_)},n.toggleVisibility_=function(e){t.hidden?this.disableInterval_(e):(this.enableInterval_(),this.update())},n.enableInterval_=function(){this.updateInterval||(this.updateInterval=this.setInterval(this.update,30))},n.disableInterval_=function(e){this.player_.liveTracker&&this.player_.liveTracker.isLive()&&e&&"ended"!==e.type||this.updateInterval&&(this.clearInterval(this.updateInterval),this.updateInterval=null)},n.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-progress-holder"},{"aria-label":this.localize("Progress Bar")})},n.update=function(t){var i=this,n=e.prototype.update.call(this);return this.requestAnimationFrame(function(){var e=i.player_.ended()?i.player_.duration():i.getCurrentTime_(),t=i.player_.liveTracker,r=i.player_.duration();t&&t.isLive()&&(r=i.player_.liveTracker.liveCurrentTime()),i.percent_!==n&&(i.el_.setAttribute("aria-valuenow",(100*n).toFixed(2)),i.percent_=n),i.currentTime_===e&&i.duration_===r||(i.el_.setAttribute("aria-valuetext",i.localize("progress bar timing: currentTime={1} duration={2}",[iF(e,r),iF(r,r)],"{1} of {2}")),i.currentTime_=e,i.duration_=r),i.bar&&i.bar.update(R(i.el()),i.getProgress())}),n},n.getCurrentTime_=function(){return this.player_.scrubbing()?this.player_.getCache().currentTime:this.player_.currentTime()},n.getPercent=function(){var e,t=this.getCurrentTime_(),i=this.player_.liveTracker;return i&&i.isLive()?(e=(t-i.seekableStart())/i.liveWindow(),i.atLiveEdge()&&(e=1)):e=t/this.player_.duration(),e},n.handleMouseDown=function(t){q(t)&&(t.stopPropagation(),this.player_.scrubbing(!0),this.videoWasPlaying=!this.player_.paused(),this.player_.pause(),e.prototype.handleMouseDown.call(this,t))},n.handleMouseMove=function(e){if(q(e)){var t,i=this.calculateDistance(e),n=this.player_.liveTracker;if(n&&n.isLive()){if(.99<=i)return void n.seekToLiveEdge();var r=n.seekableStart(),s=n.liveCurrentTime();if(s<=(t=r+i*n.liveWindow())&&(t=s),t<=r&&(t=r+.1),t===1/0)return}else(t=i*this.player_.duration())===this.player_.duration()&&(t-=.1);this.player_.currentTime(t)}},n.enable=function(){e.prototype.enable.call(this);var t=this.getChild("mouseTimeDisplay");t&&t.show()},n.disable=function(){e.prototype.disable.call(this);var t=this.getChild("mouseTimeDisplay");t&&t.hide()},n.handleMouseUp=function(t){e.prototype.handleMouseUp.call(this,t),t&&t.stopPropagation(),this.player_.scrubbing(!1),this.player_.trigger({type:"timeupdate",target:this,manuallyTriggered:!0}),this.videoWasPlaying?td(this.player_.play()):this.update_()},n.stepForward=function(){this.player_.currentTime(this.player_.currentTime()+5)},n.stepBack=function(){this.player_.currentTime(this.player_.currentTime()-5)},n.handleAction=function(e){this.player_.paused()?this.player_.play():this.player_.pause()},n.handleKeyDown=function(t){if(tm.isEventKey(t,"Space")||tm.isEventKey(t,"Enter"))t.preventDefault(),t.stopPropagation(),this.handleAction(t);else if(tm.isEventKey(t,"Home"))t.preventDefault(),t.stopPropagation(),this.player_.currentTime(0);else if(tm.isEventKey(t,"End"))t.preventDefault(),t.stopPropagation(),this.player_.currentTime(this.player_.duration());else if(/^[0-9]$/.test(tm(t))){t.preventDefault(),t.stopPropagation();var i=10*(tm.codes[tm(t)]-tm.codes[0])/100;this.player_.currentTime(this.player_.duration()*i)}else tm.isEventKey(t,"PgDn")?(t.preventDefault(),t.stopPropagation(),this.player_.currentTime(this.player_.currentTime()-60)):tm.isEventKey(t,"PgUp")?(t.preventDefault(),t.stopPropagation(),this.player_.currentTime(this.player_.currentTime()+60)):e.prototype.handleKeyDown.call(this,t)},n.dispose=function(){this.disableInterval_(),this.off(this.player_,["ended","durationchange","timeupdate"],this.update),this.player_.liveTracker&&this.on(this.player_.liveTracker,"liveedgechange",this.update),this.off(this.player_,["playing"],this.enableInterval_),this.off(this.player_,["ended","pause","waiting"],this.disableInterval_),"hidden"in t&&"visibilityState"in t&&this.off(t,"visibilitychange",this.toggleVisibility_),e.prototype.dispose.call(this)},i}(iX);iZ.prototype.options_={children:["loadProgressBar","playProgressBar"],barName:"playProgressBar"},eY||eB||iZ.prototype.options_.children.splice(1,0,"mouseTimeDisplay"),eD.registerComponent("SeekBar",iZ);var ne=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).handleMouseMove=eg(em(eA(n),n.handleMouseMove),30),n.throttledHandleMouseSeek=eg(em(eA(n),n.handleMouseSeek),30),n.enable(),n}e3(t,e);var i=t.prototype;return i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-progress-control vjs-control"})},i.handleMouseMove=function(e){var t=this.getChild("seekBar");if(t){var i=t.getChild("playProgressBar"),n=t.getChild("mouseTimeDisplay");if(i||n){var r=t.el(),s=R(r),a=U(r,e).x;a=iG(0,1,a),n&&n.update(s,a),i&&i.update(s,t.getProgress())}}},i.handleMouseSeek=function(e){var t=this.getChild("seekBar");t&&t.handleMouseMove(e)},i.enabled=function(){return this.enabled_},i.disable=function(){this.children().forEach(function(e){return e.disable&&e.disable()}),this.enabled()&&(this.off(["mousedown","touchstart"],this.handleMouseDown),this.off(this.el_,"mousemove",this.handleMouseMove),this.handleMouseUp(),this.addClass("disabled"),this.enabled_=!1)},i.enable=function(){this.children().forEach(function(e){return e.enable&&e.enable()}),this.enabled()||(this.on(["mousedown","touchstart"],this.handleMouseDown),this.on(this.el_,"mousemove",this.handleMouseMove),this.removeClass("disabled"),this.enabled_=!0)},i.handleMouseDown=function(e){var t=this.el_.ownerDocument,i=this.getChild("seekBar");i&&i.handleMouseDown(e),this.on(t,"mousemove",this.throttledHandleMouseSeek),this.on(t,"touchmove",this.throttledHandleMouseSeek),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchend",this.handleMouseUp)},i.handleMouseUp=function(e){var t=this.el_.ownerDocument,i=this.getChild("seekBar");i&&i.handleMouseUp(e),this.off(t,"mousemove",this.throttledHandleMouseSeek),this.off(t,"touchmove",this.throttledHandleMouseSeek),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchend",this.handleMouseUp)},t}(eD);ne.prototype.options_={children:["seekBar"]},eD.registerComponent("ProgressControl",ne);var nt=function(e){function i(i,n){var r;return(r=e.call(this,i,n)||this).on(i,["enterpictureinpicture","leavepictureinpicture"],r.handlePictureInPictureChange),t.pictureInPictureEnabled||r.disable(),r}e3(i,e);var n=i.prototype;return n.buildCSSClass=function(){return"vjs-picture-in-picture-control "+e.prototype.buildCSSClass.call(this)},n.handlePictureInPictureChange=function(e){this.player_.isInPictureInPicture()?this.controlText("Exit Picture-in-Picture"):this.controlText("Picture-in-Picture")},n.handleClick=function(e){this.player_.isInPictureInPicture()?this.player_.exitPictureInPicture():this.player_.requestPictureInPicture()},i}(iU);nt.prototype.controlText_="Picture-in-Picture",eD.registerComponent("PictureInPictureToggle",nt);var ni=function(e){function i(i,n){var r;return(r=e.call(this,i,n)||this).on(i,"fullscreenchange",r.handleFullscreenChange),!1===t[i.fsApi_.fullscreenEnabled]&&r.disable(),r}e3(i,e);var n=i.prototype;return n.buildCSSClass=function(){return"vjs-fullscreen-control "+e.prototype.buildCSSClass.call(this)},n.handleFullscreenChange=function(e){this.player_.isFullscreen()?this.controlText("Non-Fullscreen"):this.controlText("Fullscreen")},n.handleClick=function(e){this.player_.isFullscreen()?this.player_.exitFullscreen():this.player_.requestFullscreen()},i}(iU);ni.prototype.controlText_="Fullscreen",eD.registerComponent("FullscreenToggle",ni);var nn=function(e){function t(){return e.apply(this,arguments)||this}return e3(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-volume-level",innerHTML:'<span class="vjs-control-text"></span>'})},t}(eD);eD.registerComponent("VolumeLevel",nn);var nr=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).on("slideractive",n.updateLastVolume_),n.on(t,"volumechange",n.updateARIAAttributes),t.ready(function(){return n.updateARIAAttributes()}),n}e3(t,e);var i=t.prototype;return i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-volume-bar vjs-slider-bar"},{"aria-label":this.localize("Volume Level"),"aria-live":"polite"})},i.handleMouseDown=function(t){q(t)&&e.prototype.handleMouseDown.call(this,t)},i.handleMouseMove=function(e){q(e)&&(this.checkMuted(),this.player_.volume(this.calculateDistance(e)))},i.checkMuted=function(){this.player_.muted()&&this.player_.muted(!1)},i.getPercent=function(){return this.player_.muted()?0:this.player_.volume()},i.stepForward=function(){this.checkMuted(),this.player_.volume(this.player_.volume()+.1)},i.stepBack=function(){this.checkMuted(),this.player_.volume(this.player_.volume()-.1)},i.updateARIAAttributes=function(e){var t=this.player_.muted()?0:this.volumeAsPercentage_();this.el_.setAttribute("aria-valuenow",t),this.el_.setAttribute("aria-valuetext",t+"%")},i.volumeAsPercentage_=function(){return Math.round(100*this.player_.volume())},i.updateLastVolume_=function(){var e=this,t=this.player_.volume();this.one("sliderinactive",function(){0===e.player_.volume()&&e.player_.lastVolume_(t)})},t}(iX);nr.prototype.options_={children:["volumeLevel"],barName:"volumeLevel"},nr.prototype.playerEvent="volumechange",eD.registerComponent("VolumeBar",nr);var ns=function(e){function t(t,i){var n,r,s;return void 0===i&&(i={}),i.vertical=i.vertical||!1,(void 0===i.volumeBar||p(i.volumeBar))&&(i.volumeBar=i.volumeBar||{},i.volumeBar.vertical=i.vertical),r=eA(n=e.call(this,t,i)||this),(s=t).tech_&&!s.tech_.featuresVolumeControl&&r.addClass("vjs-hidden"),r.on(s,"loadstart",function(){s.tech_.featuresVolumeControl?r.removeClass("vjs-hidden"):r.addClass("vjs-hidden")}),n.throttledHandleMouseMove=eg(em(eA(n),n.handleMouseMove),30),n.on("mousedown",n.handleMouseDown),n.on("touchstart",n.handleMouseDown),n.on(n.volumeBar,["focus","slideractive"],function(){n.volumeBar.addClass("vjs-slider-active"),n.addClass("vjs-slider-active"),n.trigger("slideractive")}),n.on(n.volumeBar,["blur","sliderinactive"],function(){n.volumeBar.removeClass("vjs-slider-active"),n.removeClass("vjs-slider-active"),n.trigger("sliderinactive")}),n}e3(t,e);var i=t.prototype;return i.createEl=function(){var t="vjs-volume-horizontal";return this.options_.vertical&&(t="vjs-volume-vertical"),e.prototype.createEl.call(this,"div",{className:"vjs-volume-control vjs-control "+t})},i.handleMouseDown=function(e){var t=this.el_.ownerDocument;this.on(t,"mousemove",this.throttledHandleMouseMove),this.on(t,"touchmove",this.throttledHandleMouseMove),this.on(t,"mouseup",this.handleMouseUp),this.on(t,"touchend",this.handleMouseUp)},i.handleMouseUp=function(e){var t=this.el_.ownerDocument;this.off(t,"mousemove",this.throttledHandleMouseMove),this.off(t,"touchmove",this.throttledHandleMouseMove),this.off(t,"mouseup",this.handleMouseUp),this.off(t,"touchend",this.handleMouseUp)},i.handleMouseMove=function(e){this.volumeBar.handleMouseMove(e)},t}(eD);ns.prototype.options_={children:["volumeBar"]},eD.registerComponent("VolumeControl",ns);var na=function(e){function t(t,i){var n,r,s;return r=eA(n=e.call(this,t,i)||this),(s=t).tech_&&!s.tech_.featuresMuteControl&&r.addClass("vjs-hidden"),r.on(s,"loadstart",function(){s.tech_.featuresMuteControl?r.removeClass("vjs-hidden"):r.addClass("vjs-hidden")}),n.on(t,["loadstart","volumechange"],n.update),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-mute-control "+e.prototype.buildCSSClass.call(this)},i.handleClick=function(e){var t=this.player_.volume(),i=this.player_.lastVolume_();0===t?(this.player_.volume(i<.1?.1:i),this.player_.muted(!1)):this.player_.muted(!this.player_.muted())},i.update=function(e){this.updateIcon_(),this.updateControlText_()},i.updateIcon_=function(){var e=this.player_.volume(),t=3;eY&&this.player_.tech_&&this.player_.tech_.el_&&this.player_.muted(this.player_.tech_.el_.muted),0===e||this.player_.muted()?t=0:e<.33?t=1:e<.67&&(t=2);for(var i=0;i<4;i++)E(this.el_,"vjs-vol-"+i);C(this.el_,"vjs-vol-"+t)},i.updateControlText_=function(){var e=this.player_.muted()||0===this.player_.volume()?"Unmute":"Mute";this.controlText()!==e&&this.controlText(e)},t}(iU);na.prototype.controlText_="Mute",eD.registerComponent("MuteToggle",na);var no=function(e){function i(t,i){var n;return void 0===i&&(i={}),void 0!==i.inline?i.inline=i.inline:i.inline=!0,(void 0===i.volumeControl||p(i.volumeControl))&&(i.volumeControl=i.volumeControl||{},i.volumeControl.vertical=!i.inline),(n=e.call(this,t,i)||this).on(t,["loadstart"],n.volumePanelState_),n.on(n.muteToggle,"keyup",n.handleKeyPress),n.on(n.volumeControl,"keyup",n.handleVolumeControlKeyUp),n.on("keydown",n.handleKeyPress),n.on("mouseover",n.handleMouseOver),n.on("mouseout",n.handleMouseOut),n.on(n.volumeControl,["slideractive"],n.sliderActive_),n.on(n.volumeControl,["sliderinactive"],n.sliderInactive_),n}e3(i,e);var n=i.prototype;return n.sliderActive_=function(){this.addClass("vjs-slider-active")},n.sliderInactive_=function(){this.removeClass("vjs-slider-active")},n.volumePanelState_=function(){this.volumeControl.hasClass("vjs-hidden")&&this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-hidden"),this.volumeControl.hasClass("vjs-hidden")&&!this.muteToggle.hasClass("vjs-hidden")&&this.addClass("vjs-mute-toggle-only")},n.createEl=function(){var t="vjs-volume-panel-horizontal";return this.options_.inline||(t="vjs-volume-panel-vertical"),e.prototype.createEl.call(this,"div",{className:"vjs-volume-panel vjs-control "+t})},n.dispose=function(){this.handleMouseOut(),e.prototype.dispose.call(this)},n.handleVolumeControlKeyUp=function(e){tm.isEventKey(e,"Esc")&&this.muteToggle.focus()},n.handleMouseOver=function(e){this.addClass("vjs-hover"),ec(t,"keyup",em(this,this.handleKeyPress))},n.handleMouseOut=function(e){this.removeClass("vjs-hover"),eh(t,"keyup",em(this,this.handleKeyPress))},n.handleKeyPress=function(e){tm.isEventKey(e,"Esc")&&this.handleMouseOut()},i}(eD);no.prototype.options_={children:["muteToggle","volumeControl"]},eD.registerComponent("VolumePanel",no);var nu=function(e){function i(t,i){var n;return n=e.call(this,t,i)||this,i&&(n.menuButton_=i.menuButton),n.focusedChild_=-1,n.on("keydown",n.handleKeyDown),n.boundHandleBlur_=em(eA(n),n.handleBlur),n.boundHandleTapClick_=em(eA(n),n.handleTapClick),n}e3(i,e);var n=i.prototype;return n.addEventListenerForItem=function(e){e instanceof eD&&(this.on(e,"blur",this.boundHandleBlur_),this.on(e,["tap","click"],this.boundHandleTapClick_))},n.removeEventListenerForItem=function(e){e instanceof eD&&(this.off(e,"blur",this.boundHandleBlur_),this.off(e,["tap","click"],this.boundHandleTapClick_))},n.removeChild=function(t){"string"==typeof t&&(t=this.getChild(t)),this.removeEventListenerForItem(t),e.prototype.removeChild.call(this,t)},n.addItem=function(e){var t=this.addChild(e);t&&this.addEventListenerForItem(t)},n.createEl=function(){var t=this.options_.contentElType||"ul";this.contentEl_=T(t,{className:"vjs-menu-content"}),this.contentEl_.setAttribute("role","menu");var i=e.prototype.createEl.call(this,"div",{append:this.contentEl_,className:"vjs-menu"});return i.appendChild(this.contentEl_),ec(i,"click",function(e){e.preventDefault(),e.stopImmediatePropagation()}),i},n.dispose=function(){this.contentEl_=null,this.boundHandleBlur_=null,this.boundHandleTapClick_=null,e.prototype.dispose.call(this)},n.handleBlur=function(e){var i=e.relatedTarget||t.activeElement;if(!this.children().some(function(e){return e.el()===i})){var n=this.menuButton_;n&&n.buttonPressed_&&i!==n.el().firstChild&&n.unpressButton()}},n.handleTapClick=function(e){if(this.menuButton_){this.menuButton_.unpressButton();var t=this.children();if(Array.isArray(t)){var i=t.filter(function(t){return t.el()===e.target})[0];i&&"CaptionSettingsMenuItem"!==i.name()&&this.menuButton_.focus()}}},n.handleKeyDown=function(e){tm.isEventKey(e,"Left")||tm.isEventKey(e,"Down")?(e.preventDefault(),e.stopPropagation(),this.stepForward()):(tm.isEventKey(e,"Right")||tm.isEventKey(e,"Up"))&&(e.preventDefault(),e.stopPropagation(),this.stepBack())},n.stepForward=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_+1),this.focus(e)},n.stepBack=function(){var e=0;void 0!==this.focusedChild_&&(e=this.focusedChild_-1),this.focus(e)},n.focus=function(e){void 0===e&&(e=0);var t=this.children().slice();t.length&&t[0].className&&/vjs-menu-title/.test(t[0].className)&&t.shift(),0<t.length&&(e<0?e=0:e>=t.length&&(e=t.length-1),t[this.focusedChild_=e].el_.focus())},i}(eD);eD.registerComponent("Menu",nu);var nl=function(e){function i(i,n){void 0===n&&(n={}),(r=e.call(this,i,n)||this).menuButton_=new iU(i,n),r.menuButton_.controlText(r.controlText_),r.menuButton_.el_.setAttribute("aria-haspopup","true");var r,s=iU.prototype.buildCSSClass();return r.menuButton_.el_.className=r.buildCSSClass()+" "+s,r.menuButton_.removeClass("vjs-control"),r.addChild(r.menuButton_),r.update(),r.enabled_=!0,r.on(r.menuButton_,"tap",r.handleClick),r.on(r.menuButton_,"click",r.handleClick),r.on(r.menuButton_,"keydown",r.handleKeyDown),r.on(r.menuButton_,"mouseenter",function(){r.addClass("vjs-hover"),r.menu.show(),ec(t,"keyup",em(eA(r),r.handleMenuKeyUp))}),r.on("mouseleave",r.handleMouseLeave),r.on("keydown",r.handleSubmenuKeyDown),r}e3(i,e);var n=i.prototype;return n.update=function(){var e=this.createMenu();this.menu&&(this.menu.dispose(),this.removeChild(this.menu)),this.menu=e,this.addChild(e),this.buttonPressed_=!1,this.menuButton_.el_.setAttribute("aria-expanded","false"),this.items&&this.items.length<=this.hideThreshold_?this.hide():this.show()},n.createMenu=function(){var e=new nu(this.player_,{menuButton:this});if(this.hideThreshold_=0,this.options_.title){var t=T("li",{className:"vjs-menu-title",innerHTML:eL(this.options_.title),tabIndex:-1});this.hideThreshold_+=1;var i=new eD(this.player_,{el:t});e.addItem(i)}if(this.items=this.createItems(),this.items)for(var n=0;n<this.items.length;n++)e.addItem(this.items[n]);return e},n.createItems=function(){},n.createEl=function(){return e.prototype.createEl.call(this,"div",{className:this.buildWrapperCSSClass()},{})},n.buildWrapperCSSClass=function(){var t="vjs-menu-button";return!0===this.options_.inline?t+="-inline":t+="-popup","vjs-menu-button "+t+" "+iU.prototype.buildCSSClass()+" "+e.prototype.buildCSSClass.call(this)},n.buildCSSClass=function(){var t="vjs-menu-button";return!0===this.options_.inline?t+="-inline":t+="-popup","vjs-menu-button "+t+" "+e.prototype.buildCSSClass.call(this)},n.controlText=function(e,t){return void 0===t&&(t=this.menuButton_.el()),this.menuButton_.controlText(e,t)},n.dispose=function(){this.handleMouseLeave(),e.prototype.dispose.call(this)},n.handleClick=function(e){this.buttonPressed_?this.unpressButton():this.pressButton()},n.handleMouseLeave=function(e){this.removeClass("vjs-hover"),eh(t,"keyup",em(this,this.handleMenuKeyUp))},n.focus=function(){this.menuButton_.focus()},n.blur=function(){this.menuButton_.blur()},n.handleKeyDown=function(e){tm.isEventKey(e,"Esc")||tm.isEventKey(e,"Tab")?(this.buttonPressed_&&this.unpressButton(),tm.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus())):(tm.isEventKey(e,"Up")||tm.isEventKey(e,"Down"))&&(this.buttonPressed_||(e.preventDefault(),this.pressButton()))},n.handleMenuKeyUp=function(e){(tm.isEventKey(e,"Esc")||tm.isEventKey(e,"Tab"))&&this.removeClass("vjs-hover")},n.handleSubmenuKeyPress=function(e){this.handleSubmenuKeyDown(e)},n.handleSubmenuKeyDown=function(e){(tm.isEventKey(e,"Esc")||tm.isEventKey(e,"Tab"))&&(this.buttonPressed_&&this.unpressButton(),tm.isEventKey(e,"Tab")||(e.preventDefault(),this.menuButton_.focus()))},n.pressButton=function(){this.enabled_&&(this.buttonPressed_=!0,this.menu.show(),this.menu.lockShowing(),this.menuButton_.el_.setAttribute("aria-expanded","true"),eY&&$()||this.menu.focus())},n.unpressButton=function(){this.enabled_&&(this.buttonPressed_=!1,this.menu.unlockShowing(),this.menu.hide(),this.menuButton_.el_.setAttribute("aria-expanded","false"))},n.disable=function(){this.unpressButton(),this.enabled_=!1,this.addClass("vjs-disabled"),this.menuButton_.disable()},n.enable=function(){this.enabled_=!0,this.removeClass("vjs-disabled"),this.menuButton_.enable()},i}(eD);eD.registerComponent("MenuButton",nl);var nc=function(e){function t(t,i){var n,r=i.tracks;if((n=e.call(this,t,i)||this).items.length<=1&&n.hide(),!r)return eA(n);var s=em(eA(n),n.update);return r.addEventListener("removetrack",s),r.addEventListener("addtrack",s),n.player_.on("ready",s),n.player_.on("dispose",function(){r.removeEventListener("removetrack",s),r.removeEventListener("addtrack",s)}),n}return e3(t,e),t}(nl);eD.registerComponent("TrackButton",nc);var nh=["Tab","Esc","Up","Down","Right","Left"],nd=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).selectable=i.selectable,n.isSelected_=i.selected||!1,n.multiSelectable=i.multiSelectable,n.selected(n.isSelected_),n.selectable?n.multiSelectable?n.el_.setAttribute("role","menuitemcheckbox"):n.el_.setAttribute("role","menuitemradio"):n.el_.setAttribute("role","menuitem"),n}e3(t,e);var i=t.prototype;return i.createEl=function(t,i,n){return this.nonIconControl=!0,e.prototype.createEl.call(this,"li",h({className:"vjs-menu-item",innerHTML:'<span class="vjs-menu-item-text">'+this.localize(this.options_.label)+"</span>",tabIndex:-1},i),n)},i.handleKeyDown=function(t){nh.some(function(e){return tm.isEventKey(t,e)})||e.prototype.handleKeyDown.call(this,t)},i.handleClick=function(e){this.selected(!0)},i.selected=function(e){this.selectable&&(e?(this.addClass("vjs-selected"),this.el_.setAttribute("aria-checked","true"),this.controlText(", selected"),this.isSelected_=!0):(this.removeClass("vjs-selected"),this.el_.setAttribute("aria-checked","false"),this.controlText(""),this.isSelected_=!1))},t}(iD);eD.registerComponent("MenuItem",nd);var np=function(i){function n(n,r){var s,a,o=r.track,u=n.textTracks();function l(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];a.handleTracksChange.apply(eA(a),t)}function c(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];a.handleSelectedLanguageChange.apply(eA(a),t)}return r.label=o.label||o.language||"Unknown",r.selected="showing"===o.mode,(a=i.call(this,n,r)||this).track=o,a.kinds=(r.kinds||[r.kind||a.track.kind]).filter(Boolean),n.on(["loadstart","texttrackchange"],l),u.addEventListener("change",l),u.addEventListener("selectedlanguagechange",c),a.on("dispose",function(){n.off(["loadstart","texttrackchange"],l),u.removeEventListener("change",l),u.removeEventListener("selectedlanguagechange",c)}),void 0===u.onchange&&a.on(["tap","click"],function(){if("object"!=typeof e.Event)try{s=new e.Event("change")}catch(i){}s||(s=t.createEvent("Event")).initEvent("change",!0,!0),u.dispatchEvent(s)}),a.handleTracksChange(),a}e3(n,i);var r=n.prototype;return r.handleClick=function(e){var t=this.track,n=this.player_.textTracks();if(i.prototype.handleClick.call(this,e),n)for(var r=0;r<n.length;r++){var s=n[r];-1!==this.kinds.indexOf(s.kind)&&(s===t?"showing"!==s.mode&&(s.mode="showing"):"disabled"!==s.mode&&(s.mode="disabled"))}},r.handleTracksChange=function(e){var t="showing"===this.track.mode;t!==this.isSelected_&&this.selected(t)},r.handleSelectedLanguageChange=function(e){if("showing"===this.track.mode){var t=this.player_.cache_.selectedLanguage;t&&t.enabled&&t.language===this.track.language&&t.kind!==this.track.kind||(this.player_.cache_.selectedLanguage={enabled:!0,language:this.track.language,kind:this.track.kind})}},r.dispose=function(){this.track=null,i.prototype.dispose.call(this)},n}(nd);eD.registerComponent("TextTrackMenuItem",np);var nf=function(e){function t(t,i){return i.track={player:t,kind:i.kind,kinds:i.kinds,default:!1,mode:"disabled"},i.kinds||(i.kinds=[i.kind]),i.label?i.track.label=i.label:i.track.label=i.kinds.join(" and ")+" off",i.selectable=!0,i.multiSelectable=!1,e.call(this,t,i)||this}e3(t,e);var i=t.prototype;return i.handleTracksChange=function(e){for(var t=this.player().textTracks(),i=!0,n=0,r=t.length;n<r;n++){var s=t[n];if(-1<this.options_.kinds.indexOf(s.kind)&&"showing"===s.mode){i=!1;break}}i!==this.isSelected_&&this.selected(i)},i.handleSelectedLanguageChange=function(e){for(var t=this.player().textTracks(),i=!0,n=0,r=t.length;n<r;n++){var s=t[n];if(-1<["captions","descriptions","subtitles"].indexOf(s.kind)&&"showing"===s.mode){i=!1;break}}i&&(this.player_.cache_.selectedLanguage={enabled:!1})},t}(np);eD.registerComponent("OffTextTrackMenuItem",nf);var nm=function(e){function t(t,i){return void 0===i&&(i={}),i.tracks=t.textTracks(),e.call(this,t,i)||this}return e3(t,e),t.prototype.createItems=function(e,t){void 0===e&&(e=[]),void 0===t&&(t=np),this.label_&&(i=this.label_+" off"),e.push(new nf(this.player_,{kinds:this.kinds_,kind:this.kind_,label:i})),this.hideThreshold_+=1;var i,n=this.player_.textTracks();Array.isArray(this.kinds_)||(this.kinds_=[this.kind_]);for(var r=0;r<n.length;r++){var s=n[r];if(-1<this.kinds_.indexOf(s.kind)){var a=new t(this.player_,{track:s,kinds:this.kinds_,kind:this.kind_,selectable:!0,multiSelectable:!1});a.addClass("vjs-"+s.kind+"-menu-item"),e.push(a)}}return e},t}(nc);eD.registerComponent("TextTrackButton",nm);var ng=function(e){function t(t,i){var n,r=i.track,s=i.cue,a=t.currentTime();return i.selectable=!0,i.multiSelectable=!1,i.label=s.text,i.selected=s.startTime<=a&&a<s.endTime,(n=e.call(this,t,i)||this).track=r,n.cue=s,r.addEventListener("cuechange",em(eA(n),n.update)),n}e3(t,e);var i=t.prototype;return i.handleClick=function(t){e.prototype.handleClick.call(this),this.player_.currentTime(this.cue.startTime),this.update(this.cue.startTime)},i.update=function(e){var t=this.cue,i=this.player_.currentTime();this.selected(t.startTime<=i&&i<t.endTime)},t}(nd);eD.registerComponent("ChaptersTrackMenuItem",ng);var nv=function(e){function t(t,i,n){return e.call(this,t,i,n)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-chapters-button "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-chapters-button "+e.prototype.buildWrapperCSSClass.call(this)},i.update=function(t){this.track_&&(!t||"addtrack"!==t.type&&"removetrack"!==t.type)||this.setTrack(this.findChaptersTrack()),e.prototype.update.call(this)},i.setTrack=function(e){if(this.track_!==e){if(this.updateHandler_||(this.updateHandler_=this.update.bind(this)),this.track_){var t=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);t&&t.removeEventListener("load",this.updateHandler_),this.track_=null}if(this.track_=e,this.track_){this.track_.mode="hidden";var i=this.player_.remoteTextTrackEls().getTrackElementByTrack_(this.track_);i&&i.addEventListener("load",this.updateHandler_)}}},i.findChaptersTrack=function(){for(var e=this.player_.textTracks()||[],t=e.length-1;0<=t;t--){var i=e[t];if(i.kind===this.kind_)return i}},i.getMenuCaption=function(){return this.track_&&this.track_.label?this.track_.label:this.localize(eL(this.kind_))},i.createMenu=function(){return this.options_.title=this.getMenuCaption(),e.prototype.createMenu.call(this)},i.createItems=function(){var e=[];if(!this.track_)return e;var t=this.track_.cues;if(!t)return e;for(var i=0,n=t.length;i<n;i++){var r=t[i],s=new ng(this.player_,{track:this.track_,cue:r});e.push(s)}return e},t}(nm);nv.prototype.kind_="chapters",nv.prototype.controlText_="Chapters",eD.registerComponent("ChaptersButton",nv);var ny=function(e){function t(t,i,n){r=e.call(this,t,i,n)||this;var r,s=t.textTracks(),a=em(eA(r),r.handleTracksChange);return s.addEventListener("change",a),r.on("dispose",function(){s.removeEventListener("change",a)}),r}e3(t,e);var i=t.prototype;return i.handleTracksChange=function(e){for(var t=this.player().textTracks(),i=!1,n=0,r=t.length;n<r;n++){var s=t[n];if(s.kind!==this.kind_&&"showing"===s.mode){i=!0;break}}i?this.disable():this.enable()},i.buildCSSClass=function(){return"vjs-descriptions-button "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-descriptions-button "+e.prototype.buildWrapperCSSClass.call(this)},t}(nm);ny.prototype.kind_="descriptions",ny.prototype.controlText_="Descriptions",eD.registerComponent("DescriptionsButton",ny);var n8=function(e){function t(t,i,n){return e.call(this,t,i,n)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-subtitles-button "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-subtitles-button "+e.prototype.buildWrapperCSSClass.call(this)},t}(nm);n8.prototype.kind_="subtitles",n8.prototype.controlText_="Subtitles",eD.registerComponent("SubtitlesButton",n8);var n$=function(e){function t(t,i){var n;return i.track={player:t,kind:i.kind,label:i.kind+" settings",selectable:!1,default:!1,mode:"disabled"},i.selectable=!1,i.name="CaptionSettingsMenuItem",(n=e.call(this,t,i)||this).addClass("vjs-texttrack-settings"),n.controlText(", opens "+i.kind+" settings dialog"),n}return e3(t,e),t.prototype.handleClick=function(e){this.player().getChild("textTrackSettings").open()},t}(np);eD.registerComponent("CaptionSettingsMenuItem",n$);var nb=function(e){function t(t,i,n){return e.call(this,t,i,n)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-captions-button "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-captions-button "+e.prototype.buildWrapperCSSClass.call(this)},i.createItems=function(){var t=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(t.push(new n$(this.player_,{kind:this.kind_})),this.hideThreshold_+=1),e.prototype.createItems.call(this,t)},t}(nm);nb.prototype.kind_="captions",nb.prototype.controlText_="Captions",eD.registerComponent("CaptionsButton",nb);var nT=function(e){function t(){return e.apply(this,arguments)||this}return e3(t,e),t.prototype.createEl=function(t,i,n){var r='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"captions"===this.options_.track.kind&&(r+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Captions")+"</span>\n      "),r+="</span>",e.prototype.createEl.call(this,t,h({innerHTML:r},i),n)},t}(np);eD.registerComponent("SubsCapsMenuItem",nT);var n_=function(e){function t(t,i){var n;return void 0===i&&(i={}),(n=e.call(this,t,i)||this).label_="subtitles",-1<["en","en-us","en-ca","fr-ca"].indexOf(n.player_.language_)&&(n.label_="captions"),n.menuButton_.controlText(eL(n.label_)),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-subs-caps-button "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-subs-caps-button "+e.prototype.buildWrapperCSSClass.call(this)},i.createItems=function(){var t=[];return this.player().tech_&&this.player().tech_.featuresNativeTextTracks||!this.player().getChild("textTrackSettings")||(t.push(new n$(this.player_,{kind:this.label_})),this.hideThreshold_+=1),t=e.prototype.createItems.call(this,t,nT)},t}(nm);n_.prototype.kinds_=["captions","subtitles"],n_.prototype.controlText_="Subtitles",eD.registerComponent("SubsCapsButton",n_);var nS=function(e){function t(t,i){var n,r=i.track,s=t.audioTracks();function a(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];n.handleTracksChange.apply(eA(n),t)}return i.label=r.label||r.language||"Unknown",i.selected=r.enabled,(n=e.call(this,t,i)||this).track=r,n.addClass("vjs-"+r.kind+"-menu-item"),s.addEventListener("change",a),n.on("dispose",function(){s.removeEventListener("change",a)}),n}e3(t,e);var i=t.prototype;return i.createEl=function(t,i,n){var r='<span class="vjs-menu-item-text">'+this.localize(this.options_.label);return"main-desc"===this.options_.track.kind&&(r+='\n        <span aria-hidden="true" class="vjs-icon-placeholder"></span>\n        <span class="vjs-control-text"> '+this.localize("Descriptions")+"</span>\n      "),r+="</span>",e.prototype.createEl.call(this,t,h({innerHTML:r},i),n)},i.handleClick=function(t){var i=this.player_.audioTracks();e.prototype.handleClick.call(this,t);for(var n=0;n<i.length;n++){var r=i[n];r.enabled=r===this.track}},i.handleTracksChange=function(e){this.selected(this.track.enabled)},t}(nd);eD.registerComponent("AudioTrackMenuItem",nS);var nk=function(e){function t(t,i){return void 0===i&&(i={}),i.tracks=t.audioTracks(),e.call(this,t,i)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-audio-button "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-audio-button "+e.prototype.buildWrapperCSSClass.call(this)},i.createItems=function(e){void 0===e&&(e=[]),this.hideThreshold_=1;for(var t=this.player_.audioTracks(),i=0;i<t.length;i++){var n=t[i];e.push(new nS(this.player_,{track:n,selectable:!0,multiSelectable:!1}))}return e},t}(nc);nk.prototype.controlText_="Audio Track",eD.registerComponent("AudioTrackButton",nk);var nC=function(e){function t(t,i){var n,r=i.rate,s=parseFloat(r,10);return i.label=r,i.selected=1===s,i.selectable=!0,i.multiSelectable=!1,(n=e.call(this,t,i)||this).label=r,n.rate=s,n.on(t,"ratechange",n.update),n}e3(t,e);var i=t.prototype;return i.handleClick=function(t){e.prototype.handleClick.call(this),this.player().playbackRate(this.rate)},i.update=function(e){this.selected(this.player().playbackRate()===this.rate)},t}(nd);nC.prototype.contentElType="button",eD.registerComponent("PlaybackRateMenuItem",nC);var nE=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).updateVisibility(),n.updateLabel(),n.on(t,"loadstart",n.updateVisibility),n.on(t,"ratechange",n.updateLabel),n}e3(t,e);var i=t.prototype;return i.createEl=function(){var t=e.prototype.createEl.call(this);return this.labelEl_=T("div",{className:"vjs-playback-rate-value",innerHTML:"1x"}),t.appendChild(this.labelEl_),t},i.dispose=function(){this.labelEl_=null,e.prototype.dispose.call(this)},i.buildCSSClass=function(){return"vjs-playback-rate "+e.prototype.buildCSSClass.call(this)},i.buildWrapperCSSClass=function(){return"vjs-playback-rate "+e.prototype.buildWrapperCSSClass.call(this)},i.createMenu=function(){var e=new nu(this.player()),t=this.playbackRates();if(t)for(var i=t.length-1;0<=i;i--)e.addChild(new nC(this.player(),{rate:t[i]+"x"}));return e},i.updateARIAAttributes=function(){this.el().setAttribute("aria-valuenow",this.player().playbackRate())},i.handleClick=function(e){for(var t=this.player().playbackRate(),i=this.playbackRates(),n=i[0],r=0;r<i.length;r++)if(i[r]>t){n=i[r];break}this.player().playbackRate(n)},i.playbackRates=function(){return this.options_.playbackRates||this.options_.playerOptions&&this.options_.playerOptions.playbackRates},i.playbackRateSupported=function(){return this.player().tech_&&this.player().tech_.featuresPlaybackRate&&this.playbackRates()&&0<this.playbackRates().length},i.updateVisibility=function(e){this.playbackRateSupported()?this.removeClass("vjs-hidden"):this.addClass("vjs-hidden")},i.updateLabel=function(e){this.playbackRateSupported()&&(this.labelEl_.innerHTML=this.player().playbackRate()+"x")},t}(nl);nE.prototype.controlText_="Playback Rate",eD.registerComponent("PlaybackRateMenuButton",nE);var nw=function(e){function t(){return e.apply(this,arguments)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-spacer "+e.prototype.buildCSSClass.call(this)},i.createEl=function(){return e.prototype.createEl.call(this,"div",{className:this.buildCSSClass()})},t}(eD);eD.registerComponent("Spacer",nw);var n0=function(e){function t(){return e.apply(this,arguments)||this}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-custom-control-spacer "+e.prototype.buildCSSClass.call(this)},i.createEl=function(){var t=e.prototype.createEl.call(this,{className:this.buildCSSClass()});return t.innerHTML=" ",t},t}(nw);eD.registerComponent("CustomControlSpacer",n0);var nx=function(e){function t(){return e.apply(this,arguments)||this}return e3(t,e),t.prototype.createEl=function(){return e.prototype.createEl.call(this,"div",{className:"vjs-control-bar",dir:"ltr"})},t}(eD);nx.prototype.options_={children:["playToggle","volumePanel","currentTimeDisplay","timeDivider","durationDisplay","progressControl","liveDisplay","seekToLive","remainingTimeDisplay","customControlSpacer","playbackRateMenuButton","chaptersButton","descriptionsButton","subsCapsButton","audioTrackButton","fullscreenToggle"]},"exitPictureInPicture"in t&&nx.prototype.options_.children.splice(nx.prototype.options_.children.length-1,0,"pictureInPictureToggle"),eD.registerComponent("ControlBar",nx);var nP=function(e){function t(t,i){var n;return(n=e.call(this,t,i)||this).on(t,"error",n.open),n}e3(t,e);var i=t.prototype;return i.buildCSSClass=function(){return"vjs-error-display "+e.prototype.buildCSSClass.call(this)},i.content=function(){var e=this.player().error();return e?this.localize(e.message):""},t}(tv);nP.prototype.options_=o({},tv.prototype.options_,{pauseOnOpen:!1,fillAlways:!0,temporary:!1,uncloseable:!0}),eD.registerComponent("ErrorDisplay",nP);var nL="vjs-text-track-settings",nI=["#000","Black"],nD=["#00F","Blue"],nA=["#0FF","Cyan"],nO=["#0F0","Green"],n2=["#F0F","Magenta"],nR=["#F00","Red"],nN=["#FFF","White"],n3=["#FF0","Yellow"],n4=["1","Opaque"],nU=["0.5","Semi-Transparent"],n1=["0","Transparent"],nM={backgroundColor:{selector:".vjs-bg-color > select",id:"captions-background-color-%s",label:"Color",options:[nI,nN,nR,nO,nD,n3,n2,nA]},backgroundOpacity:{selector:".vjs-bg-opacity > select",id:"captions-background-opacity-%s",label:"Transparency",options:[n4,nU,n1]},color:{selector:".vjs-fg-color > select",id:"captions-foreground-color-%s",label:"Color",options:[nN,nI,nR,nO,nD,n3,n2,nA]},edgeStyle:{selector:".vjs-edge-style > select",id:"%s",label:"Text Edge Style",options:[["none","None"],["raised","Raised"],["depressed","Depressed"],["uniform","Uniform"],["dropshadow","Dropshadow"]]},fontFamily:{selector:".vjs-font-family > select",id:"captions-font-family-%s",label:"Font Family",options:[["proportionalSansSerif","Proportional Sans-Serif"],["monospaceSansSerif","Monospace Sans-Serif"],["proportionalSerif","Proportional Serif"],["monospaceSerif","Monospace Serif"],["casual","Casual"],["script","Script"],["small-caps","Small Caps"]]},fontPercent:{selector:".vjs-font-percent > select",id:"captions-font-size-%s",label:"Font Size",options:[["0.50","50%"],["0.75","75%"],["1.00","100%"],["1.25","125%"],["1.50","150%"],["1.75","175%"],["2.00","200%"],["3.00","300%"],["4.00","400%"]],default:2,parser:function(e){return"1.00"===e?null:Number(e)}},textOpacity:{selector:".vjs-text-opacity > select",id:"captions-foreground-opacity-%s",label:"Transparency",options:[n4,nU]},windowColor:{selector:".vjs-window-color > select",id:"captions-window-color-%s",label:"Color"},windowOpacity:{selector:".vjs-window-opacity > select",id:"captions-window-opacity-%s",label:"Transparency",options:[n1,nU,n4]}};function n6(e,t){if(t&&(e=t(e)),e&&"none"!==e)return e}nM.windowColor.options=nM.backgroundColor.options;var nB=function(t){function i(e,i){var n;return i.temporary=!1,(n=t.call(this,e,i)||this).updateDisplay=em(eA(n),n.updateDisplay),n.fill(),n.hasBeenOpened_=n.hasBeenFilled_=!0,n.endDialog=T("p",{className:"vjs-control-text",textContent:n.localize("End of dialog window.")}),n.el().appendChild(n.endDialog),n.setDefaults(),void 0===i.persistTextTrackSettings&&(n.options_.persistTextTrackSettings=n.options_.playerOptions.persistTextTrackSettings),n.on(n.$(".vjs-done-button"),"click",function(){n.saveSettings(),n.close()}),n.on(n.$(".vjs-default-button"),"click",function(){n.setDefaults(),n.updateDisplay()}),c(nM,function(e){n.on(n.$(e.selector),"change",n.updateDisplay)}),n.options_.persistTextTrackSettings&&n.restoreSettings(),n}e3(i,t);var n=i.prototype;return n.dispose=function(){this.endDialog=null,t.prototype.dispose.call(this)},n.createElSelect_=function(e,t,i){var n=this;void 0===t&&(t=""),void 0===i&&(i="label");var r=nM[e],s=r.id.replace("%s",this.id_),a=[t,s].join(" ").trim();return["<"+i+' id="'+s+'" class="'+("label"===i?"vjs-label":"")+'">',this.localize(r.label),"</"+i+">",'<select aria-labelledby="'+a+'">'].concat(r.options.map(function(e){var t=s+"-"+e[1].replace(/\W+/g,"");return['<option id="'+t+'" value="'+e[0]+'" ','aria-labelledby="'+a+" "+t+'">',n.localize(e[1]),"</option>"].join("")})).concat("</select>").join("")},n.createElFgColor_=function(){var e="captions-text-legend-"+this.id_;return['<fieldset class="vjs-fg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Text"),"</legend>",this.createElSelect_("color",e),'<span class="vjs-text-opacity vjs-opacity">',this.createElSelect_("textOpacity",e),"</span>","</fieldset>"].join("")},n.createElBgColor_=function(){var e="captions-background-"+this.id_;return['<fieldset class="vjs-bg-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Background"),"</legend>",this.createElSelect_("backgroundColor",e),'<span class="vjs-bg-opacity vjs-opacity">',this.createElSelect_("backgroundOpacity",e),"</span>","</fieldset>"].join("")},n.createElWinColor_=function(){var e="captions-window-"+this.id_;return['<fieldset class="vjs-window-color vjs-track-setting">','<legend id="'+e+'">',this.localize("Window"),"</legend>",this.createElSelect_("windowColor",e),'<span class="vjs-window-opacity vjs-opacity">',this.createElSelect_("windowOpacity",e),"</span>","</fieldset>"].join("")},n.createElColors_=function(){return T("div",{className:"vjs-track-settings-colors",innerHTML:[this.createElFgColor_(),this.createElBgColor_(),this.createElWinColor_()].join("")})},n.createElFont_=function(){return T("div",{className:"vjs-track-settings-font",innerHTML:['<fieldset class="vjs-font-percent vjs-track-setting">',this.createElSelect_("fontPercent","","legend"),"</fieldset>",'<fieldset class="vjs-edge-style vjs-track-setting">',this.createElSelect_("edgeStyle","","legend"),"</fieldset>",'<fieldset class="vjs-font-family vjs-track-setting">',this.createElSelect_("fontFamily","","legend"),"</fieldset>"].join("")})},n.createElControls_=function(){var e=this.localize("restore all settings to the default values");return T("div",{className:"vjs-track-settings-controls",innerHTML:['<button type="button" class="vjs-default-button" title="'+e+'">',this.localize("Reset"),'<span class="vjs-control-text"> '+e+"</span>","</button>",'<button type="button" class="vjs-done-button">'+this.localize("Done")+"</button>"].join("")})},n.content=function(){return[this.createElColors_(),this.createElFont_(),this.createElControls_()]},n.label=function(){return this.localize("Caption Settings Dialog")},n.description=function(){return this.localize("Beginning of dialog window. Escape will cancel and close the window.")},n.buildCSSClass=function(){return t.prototype.buildCSSClass.call(this)+" vjs-text-track-settings"},n.getValues=function(){var e,t,i,n=this;return e=nM,t=function(e,t,i){var r,s,a=(r=n.$(t.selector),s=t.parser,n6(r.options[r.options.selectedIndex].value,s));return void 0!==a&&(e[i]=a),e},i={},l(e).reduce(function(i,n){return t(i,e[n],n)},i)},n.setValues=function(e){var t=this;c(nM,function(i,n){!function(e,t,i){if(t){for(var n=0;n<e.options.length;n++)if(n6(e.options[n].value,i)===t){e.selectedIndex=n;break}}}(t.$(i.selector),e[n],i.parser)})},n.setDefaults=function(){var e=this;c(nM,function(t){var i=t.hasOwnProperty("default")?t.default:0;e.$(t.selector).selectedIndex=i})},n.restoreSettings=function(){var t;try{t=JSON.parse(e.localStorage.getItem(nL))}catch(i){r.warn(i)}t&&this.setValues(t)},n.saveSettings=function(){if(this.options_.persistTextTrackSettings){var t=this.getValues();try{Object.keys(t).length?e.localStorage.setItem(nL,JSON.stringify(t)):e.localStorage.removeItem(nL)}catch(i){r.warn(i)}}},n.updateDisplay=function(){var e=this.player_.getChild("textTrackDisplay");e&&e.updateDisplay()},n.conditionalBlur_=function(){this.previouslyActiveEl_=null;var e=this.player_.controlBar,t=e&&e.subsCapsButton,i=e&&e.captionsButton;t?t.focus():i&&i.focus()},i}(tv);eD.registerComponent("TextTrackSettings",nB);var nj=function(t){function i(i,n){var r,s=n.ResizeObserver||e.ResizeObserver;null===n.ResizeObserver&&(s=!1);var a=eI({createEl:!s,reportTouchActivity:!1},n);return(r=t.call(this,i,a)||this).ResizeObserver=n.ResizeObserver||e.ResizeObserver,r.loadListener_=null,r.resizeObserver_=null,r.debouncedHandler_=function(t,i,n,r){var s;function a(){var e=this,i=arguments,a=function(){a=s=null,n||t.apply(e,i)};!s&&n&&t.apply(e,i),r.clearTimeout(s),s=r.setTimeout(a,100)}return void 0===r&&(r=e),a.cancel=function(){r.clearTimeout(s),s=null},a}(function(){r.resizeHandler()},100,!1,eA(r)),s?(r.resizeObserver_=new r.ResizeObserver(r.debouncedHandler_),r.resizeObserver_.observe(i.el())):(r.loadListener_=function(){if(r.el_&&r.el_.contentWindow){var e=r.debouncedHandler_,t=r.unloadListener_=function(){eh(this,"resize",e),eh(this,"unload",t),t=null};ec(r.el_.contentWindow,"unload",t),ec(r.el_.contentWindow,"resize",e)}},r.one("load",r.loadListener_)),r}e3(i,t);var n=i.prototype;return n.createEl=function(){return t.prototype.createEl.call(this,"iframe",{className:"vjs-resize-manager",tabIndex:-1},{"aria-hidden":"true"})},n.resizeHandler=function(){this.player_&&this.player_.trigger&&this.player_.trigger("playerresize")},n.dispose=function(){this.debouncedHandler_&&this.debouncedHandler_.cancel(),this.resizeObserver_&&(this.player_.el()&&this.resizeObserver_.unobserve(this.player_.el()),this.resizeObserver_.disconnect()),this.loadListener_&&this.off("load",this.loadListener_),this.el_&&this.el_.contentWindow&&this.unloadListener_&&this.unloadListener_.call(this.el_.contentWindow),this.ResizeObserver=null,this.resizeObserver=null,this.debouncedHandler_=null,this.loadListener_=null,t.prototype.dispose.call(this)},i}(eD);eD.registerComponent("ResizeManager",nj);var nF={trackingThreshold:30,liveTolerance:15},n7=function(i){function n(e,n){var r,s=eI(nF,n,{createEl:!1});return(r=i.call(this,e,s)||this).reset_(),r.on(r.player_,"durationchange",r.handleDurationchange),eV&&"hidden"in t&&"visibilityState"in t&&r.on(t,"visibilitychange",r.handleVisibilityChange),r}e3(n,i);var r=n.prototype;return r.handleVisibilityChange=function(){this.player_.duration()===1/0&&(t.hidden?this.stopTracking():this.startTracking())},r.trackLive_=function(){var t=this.player_.seekable();if(t&&t.length){var i=Number(e.performance.now().toFixed(4)),n=-1===this.lastTime_?0:(i-this.lastTime_)/1e3;this.lastTime_=i,this.pastSeekEnd_=this.pastSeekEnd()+n;var r=this.liveCurrentTime(),s=this.player_.currentTime(),a=this.player_.paused()||this.seekedBehindLive_||Math.abs(r-s)>this.options_.liveTolerance;this.timeupdateSeen_&&r!==1/0||(a=!1),a!==this.behindLiveEdge_&&(this.behindLiveEdge_=a,this.trigger("liveedgechange"))}},r.handleDurationchange=function(){this.player_.duration()===1/0&&this.liveWindow()>=this.options_.trackingThreshold?(this.player_.options_.liveui&&this.player_.addClass("vjs-liveui"),this.startTracking()):(this.player_.removeClass("vjs-liveui"),this.stopTracking())},r.startTracking=function(){this.isTracking()||(this.timeupdateSeen_||(this.timeupdateSeen_=this.player_.hasStarted()),this.trackingInterval_=this.setInterval(this.trackLive_,30),this.trackLive_(),this.on(this.player_,["play","pause"],this.trackLive_),this.timeupdateSeen_?this.on(this.player_,"seeked",this.handleSeeked):(this.one(this.player_,"play",this.handlePlay),this.one(this.player_,"timeupdate",this.handleFirstTimeupdate)))},r.handleFirstTimeupdate=function(){this.timeupdateSeen_=!0,this.on(this.player_,"seeked",this.handleSeeked)},r.handleSeeked=function(){var e=Math.abs(this.liveCurrentTime()-this.player_.currentTime());this.seekedBehindLive_=!this.skipNextSeeked_&&2<e,this.skipNextSeeked_=!1,this.trackLive_()},r.handlePlay=function(){this.one(this.player_,"timeupdate",this.seekToLiveEdge)},r.reset_=function(){this.lastTime_=-1,this.pastSeekEnd_=0,this.lastSeekEnd_=-1,this.behindLiveEdge_=!0,this.timeupdateSeen_=!1,this.seekedBehindLive_=!1,this.skipNextSeeked_=!1,this.clearInterval(this.trackingInterval_),this.trackingInterval_=null,this.off(this.player_,["play","pause"],this.trackLive_),this.off(this.player_,"seeked",this.handleSeeked),this.off(this.player_,"play",this.handlePlay),this.off(this.player_,"timeupdate",this.handleFirstTimeupdate),this.off(this.player_,"timeupdate",this.seekToLiveEdge)},r.stopTracking=function(){this.isTracking()&&(this.reset_(),this.trigger("liveedgechange"))},r.seekableEnd=function(){for(var e=this.player_.seekable(),t=[],i=e?e.length:0;i--;)t.push(e.end(i));return t.length?t.sort()[t.length-1]:1/0},r.seekableStart=function(){for(var e=this.player_.seekable(),t=[],i=e?e.length:0;i--;)t.push(e.start(i));return t.length?t.sort()[0]:0},r.liveWindow=function(){var e=this.liveCurrentTime();return e===1/0?1/0:e-this.seekableStart()},r.isLive=function(){return this.isTracking()},r.atLiveEdge=function(){return!this.behindLiveEdge()},r.liveCurrentTime=function(){return this.pastSeekEnd()+this.seekableEnd()},r.pastSeekEnd=function(){var e=this.seekableEnd();return -1!==this.lastSeekEnd_&&e!==this.lastSeekEnd_&&(this.pastSeekEnd_=0),this.lastSeekEnd_=e,this.pastSeekEnd_},r.behindLiveEdge=function(){return this.behindLiveEdge_},r.isTracking=function(){return"number"==typeof this.trackingInterval_},r.seekToLiveEdge=function(){this.seekedBehindLive_=!1,this.atLiveEdge()||(this.skipNextSeeked_=!0,this.player_.currentTime(this.liveCurrentTime()))},r.dispose=function(){this.off(t,"visibilitychange",this.handleVisibilityChange),this.stopTracking(),i.prototype.dispose.call(this)},n}(eD);function n5(e){var t=e.el();if(t.hasAttribute("src"))return e.triggerSourceset(t.src),!0;var i=e.$$("source"),n=[],r="";if(!i.length)return!1;for(var s=0;s<i.length;s++){var a=i[s].src;a&&-1===n.indexOf(a)&&n.push(a)}return!!n.length&&(1===n.length&&(r=n[0]),e.triggerSourceset(r),!0)}function nH(e,t){for(var i={},n=0;n<e.length&&!((i=Object.getOwnPropertyDescriptor(e[n],t))&&i.set&&i.get);n++);return i.enumerable=!0,i.configurable=!0,i}function nq(t){var i=t.el();if(!i.resetSourceWatch_){var n,r={},s=(n=t,nH([n.el(),e.HTMLMediaElement.prototype,e.Element.prototype,nz],"innerHTML")),a=function(e){return function(){for(var n=arguments.length,r=Array(n),s=0;s<n;s++)r[s]=arguments[s];var a=e.apply(i,r);return n5(t),a}};["append","appendChild","insertAdjacentHTML"].forEach(function(e){i[e]&&(r[e]=i[e],i[e]=a(r[e]))}),Object.defineProperty(i,"innerHTML",eI(s,{set:a(s.set)})),i.resetSourceWatch_=function(){i.resetSourceWatch_=null,Object.keys(r).forEach(function(e){i[e]=r[e]}),Object.defineProperty(i,"innerHTML",s)},t.one("sourceset",i.resetSourceWatch_)}}function nV(e,t,i,n){function r(i){return Object.defineProperty(e,t,{value:i,enumerable:!0,writable:!0})}void 0===n&&(n=!0);var s={configurable:!0,enumerable:!0,get:function(){var e=i();return r(e),e}};return n&&(s.set=r),Object.defineProperty(e,t,s)}eD.registerComponent("LiveTracker",n7);var nW,nz=Object.defineProperty({},"innerHTML",{get:function(){return this.cloneNode(!0).innerHTML},set:function(i){var n=t.createElement(this.nodeName.toLowerCase());n.innerHTML=i;for(var r=t.createDocumentFragment();n.childNodes.length;)r.appendChild(n.childNodes[0]);return this.innerText="",e.Element.prototype.appendChild.call(this,r),this.innerHTML}}),nG=Object.defineProperty({},"src",{get:function(){return this.hasAttribute("src")?t_(e.Element.prototype.getAttribute.call(this,"src")):""},set:function(t){return e.Element.prototype.setAttribute.call(this,"src",t),t}}),nX=function(i){function n(e,t){n=i.call(this,e,t)||this;var n,s=e.source,a=!1;if(s&&(n.el_.currentSrc!==s.src||e.tag&&3===e.tag.initNetworkState_)?n.setSource(s):n.handleLateInit_(n.el_),e.enableSourceset&&n.setupSourcesetHandling_(),n.el_.hasChildNodes()){for(var o=n.el_.childNodes,u=o.length,l=[];u--;){var c=o[u];"track"===c.nodeName.toLowerCase()&&(n.featuresNativeTextTracks?(n.remoteTextTrackEls().addTrackElement_(c),n.remoteTextTracks().addTrack(c.track),n.textTracks().addTrack(c.track),a||n.el_.hasAttribute("crossorigin")||!tk(c.src)||(a=!0)):l.push(c))}for(var h=0;h<l.length;h++)n.el_.removeChild(l[h])}return n.proxyNativeTracks_(),n.featuresNativeTextTracks&&a&&r.warn("Text Tracks are being loaded from another origin but the crossorigin attribute isn't used.\nThis may prevent text tracks from loading."),n.restoreMetadataTracksInIOSNativePlayer_(),(eG||eK||eF)&&!0===e.nativeControlsForTouch&&n.setControls(!0),n.proxyWebkitFullscreen_(),n.triggerReady(),n}e3(n,i);var s=n.prototype;return s.dispose=function(){this.el_&&this.el_.resetSourceset_&&this.el_.resetSourceset_(),n.disposeMediaElement(this.el_),this.options_=null,i.prototype.dispose.call(this)},s.setupSourcesetHandling_=function(){(function t(i){if(i.featuresSourceset){var n=i.el();if(!n.resetSourceset_){var r,s=(r=i,nH([r.el(),e.HTMLMediaElement.prototype,nG],"src")),a=n.setAttribute,o=n.load;Object.defineProperty(n,"src",eI(s,{set:function(e){var t=s.set.call(n,e);return i.triggerSourceset(n.src),t}})),n.setAttribute=function(e,t){var r=a.call(n,e,t);return/src/i.test(e)&&i.triggerSourceset(n.src),r},n.load=function(){var e=o.call(n);return n5(i)||(i.triggerSourceset(""),nq(i)),e},n.currentSrc?i.triggerSourceset(n.currentSrc):n5(i)||nq(i),n.resetSourceset_=function(){n.resetSourceset_=null,n.load=o,n.setAttribute=a,Object.defineProperty(n,"src",s),n.resetSourceWatch_&&n.resetSourceWatch_()}}}})(this)},s.restoreMetadataTracksInIOSNativePlayer_=function(){function e(){t=[];for(var e=0;e<i.length;e++){var n=i[e];"metadata"===n.kind&&t.push({track:n,storedMode:n.mode})}}var t,i=this.textTracks();function n(){for(var e=0;e<t.length;e++){var r=t[e];"disabled"===r.track.mode&&r.track.mode!==r.storedMode&&(r.track.mode=r.storedMode)}i.removeEventListener("change",n)}e(),i.addEventListener("change",e),this.on("dispose",function(){return i.removeEventListener("change",e)}),this.on("webkitbeginfullscreen",function(){i.removeEventListener("change",e),i.removeEventListener("change",n),i.addEventListener("change",n)}),this.on("webkitendfullscreen",function(){i.removeEventListener("change",e),i.addEventListener("change",e),i.removeEventListener("change",n)})},s.overrideNative_=function(e,t){var i=this;if(t===this["featuresNative"+e+"Tracks"]){var n=e.toLowerCase();this[n+"TracksListeners_"]&&Object.keys(this[n+"TracksListeners_"]).forEach(function(e){i.el()[n+"Tracks"].removeEventListener(e,i[n+"TracksListeners_"][e])}),this["featuresNative"+e+"Tracks"]=!t,this[n+"TracksListeners_"]=null,this.proxyNativeTracksForType_(n)}},s.overrideNativeAudioTracks=function(e){this.overrideNative_("Audio",e)},s.overrideNativeVideoTracks=function(e){this.overrideNative_("Video",e)},s.proxyNativeTracksForType_=function(e){var t=this,i=t5[e],n=this.el()[i.getterName],r=this[i.getterName]();if(this["featuresNative"+i.capitalName+"Tracks"]&&n&&n.addEventListener){var s={change:function(i){var n={type:"change",target:r,currentTarget:r,srcElement:r};r.trigger(n),"text"===e&&t[tH.remoteText.getterName]().trigger(n)},addtrack:function(e){r.addTrack(e.track)},removetrack:function(e){r.removeTrack(e.track)}},a=function(){for(var e=[],t=0;t<r.length;t++){for(var i=!1,s=0;s<n.length;s++)if(n[s]===r[t]){i=!0;break}i||e.push(r[t])}for(;e.length;)r.removeTrack(e.shift())};this[i.getterName+"Listeners_"]=s,Object.keys(s).forEach(function(e){var i=s[e];n.addEventListener(e,i),t.on("dispose",function(t){return n.removeEventListener(e,i)})}),this.on("loadstart",a),this.on("dispose",function(e){return t.off("loadstart",a)})}},s.proxyNativeTracks_=function(){var e=this;t5.names.forEach(function(t){e.proxyNativeTracksForType_(t)})},s.createEl=function(){var e=this.options_.tag;if(!e||!this.options_.playerElIngest&&!this.movingMediaElementInDOM){if(e){var i=e.cloneNode(!0);e.parentNode&&e.parentNode.insertBefore(i,e),n.disposeMediaElement(e),e=i}else{e=t.createElement("video");var r=eI({},this.options_.tag&&P(this.options_.tag));eG&&!0===this.options_.nativeControlsForTouch||delete r.controls,x(e,h(r,{id:this.options_.techId,class:"vjs-tech"}))}e.playerId=this.options_.playerId}void 0!==this.options_.preload&&I(e,"preload",this.options_.preload);for(var s=["loop","muted","playsinline","autoplay"],a=0;a<s.length;a++){var o=s[a],u=this.options_[o];void 0!==u&&(u?I(e,o,o):D(e,o),e[o]=u)}return e},s.handleLateInit_=function(e){if(0!==e.networkState&&3!==e.networkState){if(0===e.readyState){var t=!1,i=function(){t=!0};this.on("loadstart",i);var n=function(){t||this.trigger("loadstart")};return this.on("loadedmetadata",n),void this.ready(function(){this.off("loadstart",i),this.off("loadedmetadata",n),t||this.trigger("loadstart")})}var r=["loadstart"];r.push("loadedmetadata"),2<=e.readyState&&r.push("loadeddata"),3<=e.readyState&&r.push("canplay"),4<=e.readyState&&r.push("canplaythrough"),this.ready(function(){r.forEach(function(e){this.trigger(e)},this)})}},s.setCurrentTime=function(e){try{this.el_.currentTime=e}catch(t){r(t,"Video is not ready. (Video.js)")}},s.duration=function(){var e=this;return this.el_.duration===1/0&&eB&&eH&&0===this.el_.currentTime?(this.on("timeupdate",function t(){0<e.el_.currentTime&&(e.el_.duration===1/0&&e.trigger("durationchange"),e.off("timeupdate",t))}),NaN):this.el_.duration||NaN},s.width=function(){return this.el_.offsetWidth},s.height=function(){return this.el_.offsetHeight},s.proxyWebkitFullscreen_=function(){var e=this;if("webkitDisplayingFullscreen"in this.el_){var t=function(){this.trigger("fullscreenchange",{isFullscreen:!1})},i=function(){"webkitPresentationMode"in this.el_&&"picture-in-picture"!==this.el_.webkitPresentationMode&&(this.one("webkitendfullscreen",t),this.trigger("fullscreenchange",{isFullscreen:!0,nativeIOSFullscreen:!0}))};this.on("webkitbeginfullscreen",i),this.on("dispose",function(){e.off("webkitbeginfullscreen",i),e.off("webkitendfullscreen",t)})}},s.supportsFullScreen=function(){if("function"==typeof this.el_.webkitEnterFullScreen){var t=e.navigator&&e.navigator.userAgent||"";if(/Android/.test(t)||!/Chrome|Mac OS X 10.5/.test(t))return!0}return!1},s.enterFullScreen=function(){var e=this.el_;if(e.paused&&e.networkState<=e.HAVE_METADATA)td(this.el_.play()),this.setTimeout(function(){e.pause();try{e.webkitEnterFullScreen()}catch(t){this.trigger("fullscreenerror",t)}},0);else try{e.webkitEnterFullScreen()}catch(t){this.trigger("fullscreenerror",t)}},s.exitFullScreen=function(){this.el_.webkitDisplayingFullscreen?this.el_.webkitExitFullScreen():this.trigger("fullscreenerror",Error("The video is not fullscreen"))},s.requestPictureInPicture=function(){return this.el_.requestPictureInPicture()},s.src=function(e){if(void 0===e)return this.el_.src;this.setSrc(e)},s.reset=function(){n.resetMediaElement(this.el_)},s.currentSrc=function(){return this.currentSource_?this.currentSource_.src:this.el_.currentSrc},s.setControls=function(e){this.el_.controls=!!e},s.addTextTrack=function(e,t,n){return this.featuresNativeTextTracks?this.el_.addTextTrack(e,t,n):i.prototype.addTextTrack.call(this,e,t,n)},s.createRemoteTextTrack=function(e){if(!this.featuresNativeTextTracks)return i.prototype.createRemoteTextTrack.call(this,e);var n=t.createElement("track");return e.kind&&(n.kind=e.kind),e.label&&(n.label=e.label),(e.language||e.srclang)&&(n.srclang=e.language||e.srclang),e.default&&(n.default=e.default),e.id&&(n.id=e.id),e.src&&(n.src=e.src),n},s.addRemoteTextTrack=function(e,t){var n=i.prototype.addRemoteTextTrack.call(this,e,t);return this.featuresNativeTextTracks&&this.el().appendChild(n),n},s.removeRemoteTextTrack=function(e){if(i.prototype.removeRemoteTextTrack.call(this,e),this.featuresNativeTextTracks)for(var t=this.$$("track"),n=t.length;n--;)e!==t[n]&&e!==t[n].track||this.el().removeChild(t[n])},s.getVideoPlaybackQuality=function(){if("function"==typeof this.el().getVideoPlaybackQuality)return this.el().getVideoPlaybackQuality();var t={};return void 0!==this.el().webkitDroppedFrameCount&&void 0!==this.el().webkitDecodedFrameCount&&(t.droppedVideoFrames=this.el().webkitDroppedFrameCount,t.totalVideoFrames=this.el().webkitDecodedFrameCount),e.performance&&"function"==typeof e.performance.now?t.creationTime=e.performance.now():e.performance&&e.performance.timing&&"number"==typeof e.performance.timing.navigationStart&&(t.creationTime=e.Date.now()-e.performance.timing.navigationStart),t},n}(ib);nV(nX,"TEST_VID",function(){if(v()){var e=t.createElement("video"),i=t.createElement("track");return i.kind="captions",i.srclang="en",i.label="English",e.appendChild(i),e}}),nX.isSupported=function(){try{nX.TEST_VID.volume=.5}catch(e){return!1}return!(!nX.TEST_VID||!nX.TEST_VID.canPlayType)},nX.canPlayType=function(e){return nX.TEST_VID.canPlayType(e)},nX.canPlaySource=function(e,t){return nX.canPlayType(e.type)},nX.canControlVolume=function(){try{var e=nX.TEST_VID.volume;return nX.TEST_VID.volume=e/2+.1,e!==nX.TEST_VID.volume}catch(t){return!1}},nX.canMuteVolume=function(){try{var e=nX.TEST_VID.muted;return nX.TEST_VID.muted=!e,nX.TEST_VID.muted?I(nX.TEST_VID,"muted","muted"):D(nX.TEST_VID,"muted"),e!==nX.TEST_VID.muted}catch(t){return!1}},nX.canControlPlaybackRate=function(){if(eB&&eH&&eq<58)return!1;try{var e=nX.TEST_VID.playbackRate;return nX.TEST_VID.playbackRate=e/2+.1,e!==nX.TEST_VID.playbackRate}catch(t){return!1}},nX.canOverrideAttributes=function(){try{var e=function(){};Object.defineProperty(t.createElement("video"),"src",{get:e,set:e}),Object.defineProperty(t.createElement("audio"),"src",{get:e,set:e}),Object.defineProperty(t.createElement("video"),"innerHTML",{get:e,set:e}),Object.defineProperty(t.createElement("audio"),"innerHTML",{get:e,set:e})}catch(i){return!1}return!0},nX.supportsNativeTextTracks=function(){return e9||eY&&eH},nX.supportsNativeVideoTracks=function(){return!(!nX.TEST_VID||!nX.TEST_VID.videoTracks)},nX.supportsNativeAudioTracks=function(){return!(!nX.TEST_VID||!nX.TEST_VID.audioTracks)},nX.Events=["loadstart","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","canplay","canplaythrough","playing","waiting","seeking","seeked","ended","durationchange","timeupdate","progress","play","pause","ratechange","resize","volumechange"],[["featuresVolumeControl","canControlVolume"],["featuresMuteControl","canMuteVolume"],["featuresPlaybackRate","canControlPlaybackRate"],["featuresSourceset","canOverrideAttributes"],["featuresNativeTextTracks","supportsNativeTextTracks"],["featuresNativeVideoTracks","supportsNativeVideoTracks"],["featuresNativeAudioTracks","supportsNativeAudioTracks"]].forEach(function(e){var t=e[0],i=e[1];nV(nX.prototype,t,function(){return nX[i]()},!0)}),nX.prototype.movingMediaElementInDOM=!eY,nX.prototype.featuresFullscreenResize=!0,nX.prototype.featuresProgressEvents=!0,nX.prototype.featuresTimeupdateEvents=!0,nX.patchCanPlayType=function(){!(4<=ej)||e7||eH||(nW=nX.TEST_VID&&nX.TEST_VID.constructor.prototype.canPlayType,nX.TEST_VID.constructor.prototype.canPlayType=function(e){return e&&/^application\/(?:x-|vnd\.apple\.)mpegurl/i.test(e)?"maybe":nW.call(this,e)})},nX.unpatchCanPlayType=function(){var e=nX.TEST_VID.constructor.prototype.canPlayType;return nW&&(nX.TEST_VID.constructor.prototype.canPlayType=nW),e},nX.patchCanPlayType(),nX.disposeMediaElement=function(e){if(e){for(e.parentNode&&e.parentNode.removeChild(e);e.hasChildNodes();)e.removeChild(e.firstChild);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(t){}}()}},nX.resetMediaElement=function(e){if(e){for(var t=e.querySelectorAll("source"),i=t.length;i--;)e.removeChild(t[i]);e.removeAttribute("src"),"function"==typeof e.load&&function(){try{e.load()}catch(t){}}()}},["muted","defaultMuted","autoplay","controls","loop","playsinline"].forEach(function(e){nX.prototype[e]=function(){return this.el_[e]||this.el_.hasAttribute(e)}}),["muted","defaultMuted","autoplay","loop","playsinline"].forEach(function(e){nX.prototype["set"+eL(e)]=function(t){(this.el_[e]=t)?this.el_.setAttribute(e,e):this.el_.removeAttribute(e)}}),["paused","currentTime","buffered","volume","poster","preload","error","seeking","seekable","ended","playbackRate","defaultPlaybackRate","played","networkState","readyState","videoWidth","videoHeight","crossOrigin"].forEach(function(e){nX.prototype[e]=function(){return this.el_[e]}}),["volume","src","poster","preload","playbackRate","defaultPlaybackRate","crossOrigin"].forEach(function(e){nX.prototype["set"+eL(e)]=function(t){this.el_[e]=t}}),["pause","load","play"].forEach(function(e){nX.prototype[e]=function(){return this.el_[e]()}}),ib.withSourceHandlers(nX),nX.nativeSourceHandler={},nX.nativeSourceHandler.canPlayType=function(e){try{return nX.TEST_VID.canPlayType(e)}catch(t){return""}},nX.nativeSourceHandler.canHandleSource=function(e,t){if(e.type)return nX.nativeSourceHandler.canPlayType(e.type);if(e.src){var i=tS(e.src);return nX.nativeSourceHandler.canPlayType("video/"+i)}return""},nX.nativeSourceHandler.handleSource=function(e,t,i){t.setSrc(e.src)},nX.nativeSourceHandler.dispose=function(){},nX.registerSourceHandler(nX.nativeSourceHandler),ib.registerTech("Html5",nX);var nK=["progress","abort","suspend","emptied","stalled","loadedmetadata","loadeddata","timeupdate","resize","volumechange","texttrackchange"],nY={canplay:"CanPlay",canplaythrough:"CanPlayThrough",playing:"Playing",seeked:"Seeked"},n9=["tiny","xsmall","small","medium","large","xlarge","huge"],nQ={};n9.forEach(function(e){var t="x"===e.charAt(0)?"x-"+e.substring(1):e;nQ[e]="vjs-layout-"+t});var nJ={tiny:210,xsmall:320,small:425,medium:768,large:1440,xlarge:2560,huge:1/0},nZ=function(n){function a(e,r,o){if(e.id=e.id||r.id||"vjs_video_"+ei(),(r=h(a.getTagSettings(e),r)).initChildren=!1,r.createEl=!1,r.evented=!1,r.reportTouchActivity=!1,!r.language){if("function"==typeof e.closest){var u,l=e.closest("[lang]");l&&l.getAttribute&&(r.language=l.getAttribute("lang"))}else for(var c=e;c&&1===c.nodeType;){if(P(c).hasOwnProperty("lang")){r.language=c.getAttribute("lang");break}c=c.parentNode}}if((u=n.call(this,null,r,o)||this).boundDocumentFullscreenChange_=em(eA(u),u.documentFullscreenChange_),u.boundFullWindowOnEscKey_=em(eA(u),u.fullWindowOnEscKey),u.isFullscreen_=!1,u.log=s(u.id_),u.fsApi_=tn,u.isPosterFromTech_=!1,u.queuedCallbacks_=[],u.isReady_=!1,u.hasStarted_=!1,u.userActive_=!1,!u.options_||!u.options_.techOrder||!u.options_.techOrder.length)throw Error("No techOrder specified. Did you overwrite videojs.options instead of just changing the properties you want to override?");if(u.tag=e,u.tagAttributes=e&&P(e),u.language(u.options_.language),r.languages){var d={};Object.getOwnPropertyNames(r.languages).forEach(function(e){d[e.toLowerCase()]=r.languages[e]}),u.languages_=d}else u.languages_=a.prototype.options_.languages;u.resetCache_(),u.poster_=r.poster||"",u.controls_=!!r.controls,e.controls=!1,e.removeAttribute("controls"),u.changingSrc_=!1,u.playCallbacks_=[],u.playTerminatedQueue_=[],e.hasAttribute("autoplay")?u.autoplay(!0):u.autoplay(u.options_.autoplay),r.plugins&&Object.keys(r.plugins).forEach(function(e){if("function"!=typeof u[e])throw Error('plugin "'+e+'" does not exist')}),u.scrubbing_=!1,u.el_=u.createEl(),ew(eA(u),{eventBusKey:"el_"}),u.fsApi_.requestFullscreen&&(ec(t,u.fsApi_.fullscreenchange,u.boundDocumentFullscreenChange_),u.on(u.fsApi_.fullscreenchange,u.boundDocumentFullscreenChange_)),u.fluid_&&u.on("playerreset",u.updateStyleEl_);var p=eI(u.options_);r.plugins&&Object.keys(r.plugins).forEach(function(e){u[e](r.plugins[e])}),u.options_.playerOptions=p,u.middleware_=[],u.initChildren(),u.isAudio("audio"===e.nodeName.toLowerCase()),u.controls()?u.addClass("vjs-controls-enabled"):u.addClass("vjs-controls-disabled"),u.el_.setAttribute("role","region"),u.isAudio()?u.el_.setAttribute("aria-label",u.localize("Audio Player")):u.el_.setAttribute("aria-label",u.localize("Video Player")),u.isAudio()&&u.addClass("vjs-audio"),u.flexNotSupported_()&&u.addClass("vjs-no-flex"),eG&&u.addClass("vjs-touch-enabled"),eY||u.addClass("vjs-workinghover"),a.players[u.id_]=eA(u);var f=i.split(".")[0];return u.addClass("vjs-v"+f),u.userActive(!0),u.reportUserActivity(),u.one("play",u.listenForUserActivity_),u.on("stageclick",u.handleStageClick_),u.on("keydown",u.handleKeyDown),u.breakpoints(u.options_.breakpoints),u.responsive(u.options_.responsive),u}e3(a,n);var o=a.prototype;return o.dispose=function(){var e,i=this;this.trigger("dispose"),this.off("dispose"),eh(t,this.fsApi_.fullscreenchange,this.boundDocumentFullscreenChange_),eh(t,"keydown",this.boundFullWindowOnEscKey_),this.styleEl_&&this.styleEl_.parentNode&&(this.styleEl_.parentNode.removeChild(this.styleEl_),this.styleEl_=null),a.players[this.id_]=null,this.tag&&this.tag.player&&(this.tag.player=null),this.el_&&this.el_.player&&(this.el_.player=null),this.tech_&&(this.tech_.dispose(),this.isPosterFromTech_=!1,this.poster_=""),this.playerElIngest_&&(this.playerElIngest_=null),this.tag&&(this.tag=null),e=this,i_[e.id()]=null,tq.names.forEach(function(e){var t=i[tq[e].getterName]();t&&t.off&&t.off()}),n.prototype.dispose.call(this)},o.createEl=function(){var i,r=this.tag,s=this.playerElIngest_=r.parentNode&&r.parentNode.hasAttribute&&r.parentNode.hasAttribute("data-vjs-player"),a="video-js"===this.tag.tagName.toLowerCase();s?i=this.el_=r.parentNode:a||(i=this.el_=n.prototype.createEl.call(this,"div"));var o=P(r);if(a){for(i=this.el_=r,r=this.tag=t.createElement("video");i.children.length;)r.appendChild(i.firstChild);k(i,"video-js")||C(i,"video-js"),i.appendChild(r),s=this.playerElIngest_=i,Object.keys(i).forEach(function(e){try{r[e]=i[e]}catch(t){}})}if(r.setAttribute("tabindex","-1"),o.tabindex="-1",(eV||eH&&ez)&&(r.setAttribute("role","application"),o.role="application"),r.removeAttribute("width"),r.removeAttribute("height"),"width"in o&&delete o.width,"height"in o&&delete o.height,Object.getOwnPropertyNames(o).forEach(function(e){a&&"class"===e||i.setAttribute(e,o[e]),a&&r.setAttribute(e,o[e])}),r.playerId=r.id,r.id+="_html5_api",r.className="vjs-tech",r.player=i.player=this,this.addClass("vjs-paused"),!0!==e.VIDEOJS_NO_DYNAMIC_STYLE){this.styleEl_=J("vjs-styles-dimensions");var u=W(".vjs-styles-defaults"),l=W("head");l.insertBefore(this.styleEl_,u?u.nextSibling:l.firstChild)}this.fill_=!1,this.fluid_=!1,this.width(this.options_.width),this.height(this.options_.height),this.fill(this.options_.fill),this.fluid(this.options_.fluid),this.aspectRatio(this.options_.aspectRatio),this.crossOrigin(this.options_.crossOrigin||this.options_.crossorigin);for(var c=r.getElementsByTagName("a"),h=0;h<c.length;h++){var d=c.item(h);C(d,"vjs-hidden"),d.setAttribute("hidden","hidden")}return r.initNetworkState_=r.networkState,r.parentNode&&!s&&r.parentNode.insertBefore(i,r),S(r,i),this.children_.unshift(r),this.el_.setAttribute("lang",this.language_),this.el_=i},o.crossOrigin=function(e){if(!e)return this.techGet_("crossOrigin");"anonymous"===e||"use-credentials"===e?this.techCall_("setCrossOrigin",e):r.warn('crossOrigin must be "anonymous" or "use-credentials", given "'+e+'"')},o.width=function(e){return this.dimension("width",e)},o.height=function(e){return this.dimension("height",e)},o.dimension=function(e,t){var i=e+"_";if(void 0===t)return this[i]||0;if(""===t||"auto"===t)return this[i]=void 0,void this.updateStyleEl_();var n=parseFloat(t);isNaN(n)?r.error('Improper value "'+t+'" supplied for for '+e):(this[i]=n,this.updateStyleEl_())},o.fluid=function(e){var t,i;if(void 0===e)return!!this.fluid_;this.fluid_=!!e,eC(this)&&this.off("playerreset",this.updateStyleEl_),e?(this.addClass("vjs-fluid"),this.fill(!1),eC(t=function(){this.on("playerreset",this.updateStyleEl_)})?i():(t.eventedCallbacks||(t.eventedCallbacks=[]),t.eventedCallbacks.push(i))):this.removeClass("vjs-fluid"),this.updateStyleEl_()},o.fill=function(e){if(void 0===e)return!!this.fill_;this.fill_=!!e,e?(this.addClass("vjs-fill"),this.fluid(!1)):this.removeClass("vjs-fill")},o.aspectRatio=function(e){if(void 0===e)return this.aspectRatio_;if(!/^\d+\:\d+$/.test(e))throw Error("Improper value supplied for aspect ratio. The format should be width:height, for example 16:9.");this.aspectRatio_=e,this.fluid(!0),this.updateStyleEl_()},o.updateStyleEl_=function(){if(!0!==e.VIDEOJS_NO_DYNAMIC_STYLE){var t,i,n,r=(void 0!==this.aspectRatio_&&"auto"!==this.aspectRatio_?this.aspectRatio_:0<this.videoWidth()?this.videoWidth()+":"+this.videoHeight():"16:9").split(":"),s=r[1]/r[0];t=void 0!==this.width_?this.width_:void 0!==this.height_?this.height_/s:this.videoWidth()||300,i=void 0!==this.height_?this.height_:t*s,n=/^[^a-zA-Z]/.test(this.id())?"dimensions-"+this.id():this.id()+"-dimensions",this.addClass(n),Z(this.styleEl_,"\n      ."+n+" {\n        width: "+t+"px;\n        height: "+i+"px;\n      }\n\n      ."+n+".vjs-fluid {\n        padding-top: "+100*s+"%;\n      }\n    ")}else{var a="number"==typeof this.width_?this.width_:this.options_.width,o="number"==typeof this.height_?this.height_:this.options_.height,u=this.tech_&&this.tech_.el();u&&(0<=a&&(u.width=a),0<=o&&(u.height=o))}},o.loadTech_=function(e,t){var i,n,r=this;this.tech_&&this.unloadTech_();var s=eL(e),a=e.charAt(0).toLowerCase()+e.slice(1);"Html5"!==s&&this.tag&&(ib.getTech("Html5").disposeMediaElement(this.tag),this.tag.player=null,this.tag=null),this.techName_=s,this.isReady_=!1;var o={source:t,autoplay:"string"!=typeof this.autoplay()&&this.autoplay(),nativeControlsForTouch:this.options_.nativeControlsForTouch,playerId:this.id(),techId:this.id()+"_"+a+"_api",playsinline:this.options_.playsinline,preload:this.options_.preload,loop:this.options_.loop,muted:this.options_.muted,poster:this.poster(),language:this.language(),playerElIngest:this.playerElIngest_||!1,"vtt.js":this.options_["vtt.js"],canOverridePoster:!!this.options_.techCanOverridePoster,enableSourceset:this.options_.enableSourceset,Promise:this.options_.Promise};tq.names.forEach(function(e){var t=tq[e];o[t.getterName]=r[t.privateName]}),h(o,this.options_[s]),h(o,this.options_[a]),h(o,this.options_[e.toLowerCase()]),this.tag&&(o.tag=this.tag),t&&t.src===this.cache_.src&&0<this.cache_.currentTime&&(o.startTime=this.cache_.currentTime);var u=ib.getTech(e);if(!u)throw Error("No Tech named '"+s+"' exists! '"+s+"' should be registered using videojs.registerTech()'");this.tech_=new u(o),this.tech_.ready(em(this,this.handleTechReady_),!0),i=this.textTracksJson_||[],n=this.tech_,i.forEach(function(e){var t=n.addRemoteTextTrack(e).track;!e.src&&e.cues&&e.cues.forEach(function(e){return t.addCue(e)})}),n.textTracks(),nK.forEach(function(e){r.on(r.tech_,e,r["handleTech"+eL(e)+"_"])}),Object.keys(nY).forEach(function(e){r.on(r.tech_,e,function(t){0===r.tech_.playbackRate()&&r.tech_.seeking()?r.queuedCallbacks_.push({callback:r["handleTech"+nY[e]+"_"].bind(r),event:t}):r["handleTech"+nY[e]+"_"](t)})}),this.on(this.tech_,"loadstart",this.handleTechLoadStart_),this.on(this.tech_,"sourceset",this.handleTechSourceset_),this.on(this.tech_,"waiting",this.handleTechWaiting_),this.on(this.tech_,"ended",this.handleTechEnded_),this.on(this.tech_,"seeking",this.handleTechSeeking_),this.on(this.tech_,"play",this.handleTechPlay_),this.on(this.tech_,"firstplay",this.handleTechFirstPlay_),this.on(this.tech_,"pause",this.handleTechPause_),this.on(this.tech_,"durationchange",this.handleTechDurationChange_),this.on(this.tech_,"fullscreenchange",this.handleTechFullscreenChange_),this.on(this.tech_,"fullscreenerror",this.handleTechFullscreenError_),this.on(this.tech_,"enterpictureinpicture",this.handleTechEnterPictureInPicture_),this.on(this.tech_,"leavepictureinpicture",this.handleTechLeavePictureInPicture_),this.on(this.tech_,"error",this.handleTechError_),this.on(this.tech_,"loadedmetadata",this.updateStyleEl_),this.on(this.tech_,"posterchange",this.handleTechPosterChange_),this.on(this.tech_,"textdata",this.handleTechTextData_),this.on(this.tech_,"ratechange",this.handleTechRateChange_),this.usingNativeControls(this.techGet_("controls")),this.controls()&&!this.usingNativeControls()&&this.addTechControlsListeners_(),this.tech_.el().parentNode===this.el()||"Html5"===s&&this.tag||S(this.tech_.el(),this.el()),this.tag&&(this.tag.player=null,this.tag=null)},o.unloadTech_=function(){var e=this;tq.names.forEach(function(t){var i=tq[t];e[i.privateName]=e[i.getterName]()}),this.textTracksJson_=tf(this.tech_),this.isReady_=!1,this.tech_.dispose(),this.tech_=!1,this.isPosterFromTech_&&(this.poster_="",this.trigger("posterchange")),this.isPosterFromTech_=!1},o.tech=function(e){return void 0===e&&r.warn("Using the tech directly can be dangerous. I hope you know what you're doing.\nSee https://github.com/videojs/video.js/issues/2617 for more info.\n"),this.tech_},o.addTechControlsListeners_=function(){this.removeTechControlsListeners_(),this.on(this.tech_,"mouseup",this.handleTechClick_),this.on(this.tech_,"dblclick",this.handleTechDoubleClick_),this.on(this.tech_,"touchstart",this.handleTechTouchStart_),this.on(this.tech_,"touchmove",this.handleTechTouchMove_),this.on(this.tech_,"touchend",this.handleTechTouchEnd_),this.on(this.tech_,"tap",this.handleTechTap_)},o.removeTechControlsListeners_=function(){this.off(this.tech_,"tap",this.handleTechTap_),this.off(this.tech_,"touchstart",this.handleTechTouchStart_),this.off(this.tech_,"touchmove",this.handleTechTouchMove_),this.off(this.tech_,"touchend",this.handleTechTouchEnd_),this.off(this.tech_,"mouseup",this.handleTechClick_),this.off(this.tech_,"dblclick",this.handleTechDoubleClick_)},o.handleTechReady_=function(){this.triggerReady(),this.cache_.volume&&this.techCall_("setVolume",this.cache_.volume),this.handleTechPosterChange_(),this.handleTechDurationChange_()},o.handleTechLoadStart_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-seeking"),this.error(null),this.handleTechDurationChange_(),this.paused()?(this.hasStarted(!1),this.trigger("loadstart")):(this.trigger("loadstart"),this.trigger("firstplay")),this.manualAutoplay_(this.autoplay())},o.manualAutoplay_=function(e){var t=this;if(this.tech_&&"string"==typeof e){var i,n=function(){var e=t.muted();function i(){t.muted(e)}t.muted(!0),t.playTerminatedQueue_.push(i);var n=t.play();if(th(n))return n.catch(i)};if("any"===e&&!0!==this.muted()?th(i=this.play())&&(i=i.catch(n)):i="muted"===e&&!0!==this.muted()?n():this.play(),th(i))return i.then(function(){t.trigger({type:"autoplay-success",autoplay:e})}).catch(function(i){t.trigger({type:"autoplay-failure",autoplay:e})})}},o.updateSourceCaches_=function(e){void 0===e&&(e="");var t=e,i="";"string"!=typeof t&&(t=e.src,i=e.type),this.cache_.source=this.cache_.source||{},this.cache_.sources=this.cache_.sources||[],t&&!i&&(i=function(e,t){if(!t)return"";if(e.cache_.source.src===t&&e.cache_.source.type)return e.cache_.source.type;var i=e.cache_.sources.filter(function(e){return e.src===t});if(i.length)return i[0].type;for(var n=e.$$("source"),r=0;r<n.length;r++){var s=n[r];if(s.type&&s.src&&s.src===t)return s.type}return ix(t)}(this,t)),this.cache_.source=eI({},e,{src:t,type:i});for(var n=this.cache_.sources.filter(function(e){return e.src&&e.src===t}),r=[],s=this.$$("source"),a=[],o=0;o<s.length;o++){var u=P(s[o]);r.push(u),u.src&&u.src===t&&a.push(u.src)}a.length&&!n.length?this.cache_.sources=r:n.length||(this.cache_.sources=[this.cache_.source]),this.cache_.src=t},o.handleTechSourceset_=function(e){var t=this;if(!this.changingSrc_){var i=function(e){return t.updateSourceCaches_(e)},n=this.currentSource().src,r=e.src;n&&!/^blob:/.test(n)&&/^blob:/.test(r)&&(this.lastSource_&&(this.lastSource_.tech===r||this.lastSource_.player===n)||(i=function(){})),i(r),e.src||this.tech_.any(["sourceset","loadstart"],function(e){if("sourceset"!==e.type){var i=t.techGet("currentSrc");t.lastSource_.tech=i,t.updateSourceCaches_(i)}})}this.lastSource_={player:this.currentSource().src,tech:e.src},this.trigger({src:e.src,type:"sourceset"})},o.hasStarted=function(e){if(void 0===e)return this.hasStarted_;e!==this.hasStarted_&&(this.hasStarted_=e,this.hasStarted_?(this.addClass("vjs-has-started"),this.trigger("firstplay")):this.removeClass("vjs-has-started"))},o.handleTechPlay_=function(){this.removeClass("vjs-ended"),this.removeClass("vjs-paused"),this.addClass("vjs-playing"),this.hasStarted(!0),this.trigger("play")},o.handleTechRateChange_=function(){0<this.tech_.playbackRate()&&0===this.cache_.lastPlaybackRate&&(this.queuedCallbacks_.forEach(function(e){return e.callback(e.event)}),this.queuedCallbacks_=[]),this.cache_.lastPlaybackRate=this.tech_.playbackRate(),this.trigger("ratechange")},o.handleTechWaiting_=function(){var e=this;this.addClass("vjs-waiting"),this.trigger("waiting");var t=this.currentTime();this.on("timeupdate",function i(){t!==e.currentTime()&&(e.removeClass("vjs-waiting"),e.off("timeupdate",i))})},o.handleTechCanPlay_=function(){this.removeClass("vjs-waiting"),this.trigger("canplay")},o.handleTechCanPlayThrough_=function(){this.removeClass("vjs-waiting"),this.trigger("canplaythrough")},o.handleTechPlaying_=function(){this.removeClass("vjs-waiting"),this.trigger("playing")},o.handleTechSeeking_=function(){this.addClass("vjs-seeking"),this.trigger("seeking")},o.handleTechSeeked_=function(){this.removeClass("vjs-seeking"),this.removeClass("vjs-ended"),this.trigger("seeked")},o.handleTechFirstPlay_=function(){this.options_.starttime&&(r.warn("Passing the `starttime` option to the player will be deprecated in 6.0"),this.currentTime(this.options_.starttime)),this.addClass("vjs-has-started"),this.trigger("firstplay")},o.handleTechPause_=function(){this.removeClass("vjs-playing"),this.addClass("vjs-paused"),this.trigger("pause")},o.handleTechEnded_=function(){this.addClass("vjs-ended"),this.options_.loop?(this.currentTime(0),this.play()):this.paused()||this.pause(),this.trigger("ended")},o.handleTechDurationChange_=function(){this.duration(this.techGet_("duration"))},o.handleTechClick_=function(e){q(e)&&this.controls_&&(this.paused()?td(this.play()):this.pause())},o.handleTechDoubleClick_=function(e){this.controls_&&(Array.prototype.some.call(this.$$(".vjs-control-bar, .vjs-modal-dialog"),function(t){return t.contains(e.target)})||void 0!==this.options_&&void 0!==this.options_.userActions&&void 0!==this.options_.userActions.doubleClick&&!1===this.options_.userActions.doubleClick||(void 0!==this.options_&&void 0!==this.options_.userActions&&"function"==typeof this.options_.userActions.doubleClick?this.options_.userActions.doubleClick.call(this,e):this.isFullscreen()?this.exitFullscreen():this.requestFullscreen()))},o.handleTechTap_=function(){this.userActive(!this.userActive())},o.handleTechTouchStart_=function(){this.userWasActive=this.userActive()},o.handleTechTouchMove_=function(){this.userWasActive&&this.reportUserActivity()},o.handleTechTouchEnd_=function(e){e.preventDefault()},o.handleStageClick_=function(){this.reportUserActivity()},o.toggleFullscreenClass_=function(){this.isFullscreen()?this.addClass("vjs-fullscreen"):this.removeClass("vjs-fullscreen")},o.documentFullscreenChange_=function(e){var i=e.target.player;if(!i||i===this){var n=this.el(),r=t[this.fsApi_.fullscreenElement]===n;!r&&n.matches?r=n.matches(":"+this.fsApi_.fullscreen):!r&&n.msMatchesSelector&&(r=n.msMatchesSelector(":"+this.fsApi_.fullscreen)),this.isFullscreen(r)}},o.handleTechFullscreenChange_=function(e,t){t&&(t.nativeIOSFullscreen&&this.toggleClass("vjs-ios-native-fs"),this.isFullscreen(t.isFullscreen))},o.handleTechFullscreenError_=function(e,t){this.trigger("fullscreenerror",t)},o.togglePictureInPictureClass_=function(){this.isInPictureInPicture()?this.addClass("vjs-picture-in-picture"):this.removeClass("vjs-picture-in-picture")},o.handleTechEnterPictureInPicture_=function(e){this.isInPictureInPicture(!0)},o.handleTechLeavePictureInPicture_=function(e){this.isInPictureInPicture(!1)},o.handleTechError_=function(){var e=this.tech_.error();this.error(e)},o.handleTechTextData_=function(e,t){var i=null;1<arguments.length&&(i=t),this.trigger("textdata",i)},o.getCache=function(){return this.cache_},o.resetCache_=function(){this.cache_={currentTime:0,initTime:0,inactivityTimeout:this.options_.inactivityTimeout,duration:NaN,lastVolume:1,lastPlaybackRate:this.defaultPlaybackRate(),media:null,src:"",source:{},sources:[],volume:1}},o.techCall_=function(e,t){this.ready(function(){if(e in iE){var i,n,s,a;return i=this.middleware_,n=this.tech_,s=e,a=t,n[s](i.reduce(i0(s),a))}if(e in iw)return ik(this.middleware_,this.tech_,e,t);try{this.tech_&&this.tech_[e](t)}catch(o){throw r(o),o}},!0)},o.techGet_=function(e){if(this.tech_&&this.tech_.isReady_){if(e in iC){var t,i,n;return t=this.middleware_,i=this.tech_,n=e,t.reduceRight(i0(n),i[n]())}if(e in iw)return ik(this.middleware_,this.tech_,e);try{return this.tech_[e]()}catch(s){if(void 0===this.tech_[e])throw r("Video.js: "+e+" method not defined for "+this.techName_+" playback technology.",s),s;if("TypeError"===s.name)throw r("Video.js: "+e+" unavailable on "+this.techName_+" playback technology element.",s),this.tech_.isReady_=!1,s;throw r(s),s}}},o.play=function(){var t=this,i=this.options_.Promise||e.Promise;return i?new i(function(e){t.play_(e)}):this.play_()},o.play_=function(e){var t=this;void 0===e&&(e=td),this.playCallbacks_.push(e);var i=Boolean(!this.changingSrc_&&(this.src()||this.currentSrc()));if(this.waitToPlay_&&(this.off(["ready","loadstart"],this.waitToPlay_),this.waitToPlay_=null),!this.isReady_||!i)return this.waitToPlay_=function(e){t.play_()},this.one(["ready","loadstart"],this.waitToPlay_),void(!i&&(e9||eY)&&this.load());var n=this.techGet_("play");null===n?this.runPlayTerminatedQueue_():this.runPlayCallbacks_(n)},o.runPlayTerminatedQueue_=function(){var e=this.playTerminatedQueue_.slice(0);this.playTerminatedQueue_=[],e.forEach(function(e){e()})},o.runPlayCallbacks_=function(e){var t=this.playCallbacks_.slice(0);this.playCallbacks_=[],this.playTerminatedQueue_=[],t.forEach(function(t){t(e)})},o.pause=function(){this.techCall_("pause")},o.paused=function(){return!1!==this.techGet_("paused")},o.played=function(){return this.techGet_("played")||te(0,0)},o.scrubbing=function(e){if(void 0===e)return this.scrubbing_;this.scrubbing_=!!e,e?this.addClass("vjs-scrubbing"):this.removeClass("vjs-scrubbing")},o.currentTime=function(e){return void 0!==e?(e<0&&(e=0),this.isReady_&&!this.changingSrc_&&this.tech_&&this.tech_.isReady_?(this.techCall_("setCurrentTime",e),void(this.cache_.initTime=0)):(this.cache_.initTime=e,this.off("canplay",this.applyInitTime_),void this.one("canplay",this.applyInitTime_))):(this.cache_.currentTime=this.techGet_("currentTime")||0,this.cache_.currentTime)},o.applyInitTime_=function(){this.currentTime(this.cache_.initTime)},o.duration=function(e){if(void 0===e)return void 0!==this.cache_.duration?this.cache_.duration:NaN;(e=parseFloat(e))<0&&(e=1/0),e!==this.cache_.duration&&((this.cache_.duration=e)===1/0?this.addClass("vjs-live"):this.removeClass("vjs-live"),isNaN(e)||this.trigger("durationchange"))},o.remainingTime=function(){return this.duration()-this.currentTime()},o.remainingTimeDisplay=function(){return Math.floor(this.duration())-Math.floor(this.currentTime())},o.buffered=function(){var e=this.techGet_("buffered");return e&&e.length||(e=te(0,0)),e},o.bufferedPercent=function(){return tt(this.buffered(),this.duration())},o.bufferedEnd=function(){var e=this.buffered(),t=this.duration(),i=e.end(e.length-1);return t<i&&(i=t),i},o.volume=function(e){var t;return void 0!==e?(t=Math.max(0,Math.min(1,parseFloat(e))),this.cache_.volume=t,this.techCall_("setVolume",t),void(0<t&&this.lastVolume_(t))):(t=parseFloat(this.techGet_("volume")),isNaN(t)?1:t)},o.muted=function(e){if(void 0===e)return this.techGet_("muted")||!1;this.techCall_("setMuted",e)},o.defaultMuted=function(e){return void 0!==e?this.techCall_("setDefaultMuted",e):this.techGet_("defaultMuted")||!1},o.lastVolume_=function(e){if(void 0===e||0===e)return this.cache_.lastVolume;this.cache_.lastVolume=e},o.supportsFullScreen=function(){return this.techGet_("supportsFullScreen")||!1},o.isFullscreen=function(e){if(void 0===e)return this.isFullscreen_;var t=this.isFullscreen_;return this.isFullscreen_=Boolean(e),this.isFullscreen_!==t&&this.fsApi_.prefixed&&this.trigger("fullscreenchange"),void this.toggleFullscreenClass_()},o.requestFullscreen=function(t){var i=this.options_.Promise||e.Promise;if(i){var n=this;return new i(function(e,i){function r(){n.off(n.fsApi_.fullscreenerror,a),n.off(n.fsApi_.fullscreenchange,s)}function s(){r(),e()}function a(e,t){r(),i(t)}n.one("fullscreenchange",s),n.one("fullscreenerror",a);var o=n.requestFullscreenHelper_(t);if(o)return o.then(r,r),o})}return this.requestFullscreenHelper_()},o.requestFullscreenHelper_=function(e){var t,i=this;if(this.fsApi_.prefixed||(t=this.options_.fullscreen&&this.options_.fullscreen.options||{},void 0!==e&&(t=e)),this.fsApi_.requestFullscreen){var n=this.el_[this.fsApi_.requestFullscreen](t);return n&&n.then(function(){return i.isFullscreen(!0)},function(){return i.isFullscreen(!1)}),n}this.tech_.supportsFullScreen()?this.techCall_("enterFullScreen"):this.enterFullWindow()},o.exitFullscreen=function(){var t=this.options_.Promise||e.Promise;if(t){var i=this;return new t(function(e,t){function n(){i.off(i.fsApi_.fullscreenerror,s),i.off(i.fsApi_.fullscreenchange,r)}function r(){n(),e()}function s(e,i){n(),t(i)}i.one("fullscreenchange",r),i.one("fullscreenerror",s);var a=i.exitFullscreenHelper_();if(a)return a.then(n,n),a})}return this.exitFullscreenHelper_()},o.exitFullscreenHelper_=function(){var e=this;if(this.fsApi_.requestFullscreen){var i=t[this.fsApi_.exitFullscreen]();return i&&i.then(function(){return e.isFullscreen(!1)}),i}this.tech_.supportsFullScreen()?this.techCall_("exitFullScreen"):this.exitFullWindow()},o.enterFullWindow=function(){this.isFullscreen(!0),this.isFullWindow=!0,this.docOrigOverflow=t.documentElement.style.overflow,ec(t,"keydown",this.boundFullWindowOnEscKey_),t.documentElement.style.overflow="hidden",C(t.body,"vjs-full-window"),this.trigger("enterFullWindow")},o.fullWindowOnEscKey=function(e){tm.isEventKey(e,"Esc")&&(!0===this.isFullscreen()?this.exitFullscreen():this.exitFullWindow())},o.exitFullWindow=function(){this.isFullscreen(!1),this.isFullWindow=!1,eh(t,"keydown",this.boundFullWindowOnEscKey_),t.documentElement.style.overflow=this.docOrigOverflow,E(t.body,"vjs-full-window"),this.trigger("exitFullWindow")},o.isInPictureInPicture=function(e){return void 0!==e?(this.isInPictureInPicture_=!!e,void this.togglePictureInPictureClass_()):!!this.isInPictureInPicture_},o.requestPictureInPicture=function(){if("pictureInPictureEnabled"in t)return this.techGet_("requestPictureInPicture")},o.exitPictureInPicture=function(){if("pictureInPictureEnabled"in t)return t.exitPictureInPicture()},o.handleKeyDown=function(e){var t,i,n=this.options_.userActions;n&&n.hotkeys&&(i=(t=this.el_.ownerDocument.activeElement).tagName.toLowerCase(),t.isContentEditable||("input"===i?-1===["button","checkbox","hidden","radio","reset","submit"].indexOf(t.type):-1!==["textarea"].indexOf(i))||("function"==typeof n.hotkeys?n.hotkeys.call(this,e):this.handleHotkeys(e)))},o.handleHotkeys=function(e){var i=this.options_.userActions?this.options_.userActions.hotkeys:{},n=i.fullscreenKey,r=i.muteKey,s=i.playPauseKey;if((void 0===n?function(e){return tm.isEventKey(e,"f")}:n).call(this,e)){e.preventDefault(),e.stopPropagation();var a=eD.getComponent("FullscreenToggle");!1!==t[this.fsApi_.fullscreenEnabled]&&a.prototype.handleClick.call(this,e)}else(void 0===r?function(e){return tm.isEventKey(e,"m")}:r).call(this,e)?(e.preventDefault(),e.stopPropagation(),eD.getComponent("MuteToggle").prototype.handleClick.call(this,e)):(void 0===s?function(e){return tm.isEventKey(e,"k")||tm.isEventKey(e,"Space")}:s).call(this,e)&&(e.preventDefault(),e.stopPropagation(),eD.getComponent("PlayToggle").prototype.handleClick.call(this,e))},o.canPlayType=function(e){for(var t,i=0,n=this.options_.techOrder;i<n.length;i++){var s=n[i],a=ib.getTech(s);if(a=a||eD.getComponent(s)){if(a.isSupported()&&(t=a.canPlayType(e)))return t}else r.error('The "'+s+'" tech is undefined. Skipped browser support check for that tech.')}return""},o.selectSource=function(e){function t(e,t,i){var n;return e.some(function(e){return t.some(function(t){if(n=i(e,t))return!0})}),n}function i(e,t){var i=e[0];if(e[1].canPlaySource(t,s.options_[i.toLowerCase()]))return{source:t,tech:i}}var n,s=this,a=this.options_.techOrder.map(function(e){return[e,ib.getTech(e)]}).filter(function(e){var t=e[0],i=e[1];return i?i.isSupported():(r.error('The "'+t+'" tech is undefined. Skipped browser support check for that tech.'),!1)});return(this.options_.sourceOrder?t(e,a,(n=i,function(e,t){return n(t,e)})):t(a,e,i))||!1},o.src=function(e){var t=this;if(void 0===e)return this.cache_.src||"";var i=function e(t){if(Array.isArray(t)){var i=[];t.forEach(function(t){Array.isArray(t=e(t))?i=i.concat(t):d(t)&&i.push(t)}),t=i}else t="string"==typeof t&&t.trim()?[iL({src:t})]:d(t)&&"string"==typeof t.src&&t.src&&t.src.trim()?[iL(t)]:[];return t}(e);i.length?(this.changingSrc_=!0,this.cache_.sources=i,this.updateSourceCaches_(i[0]),function e(t,i,n){t.setTimeout(function(){return function e(t,i,n,r,s,a){void 0===t&&(t={}),void 0===i&&(i=[]),void 0===s&&(s=[]),void 0===a&&(a=!1);var o=i,u=o[0],l=o.slice(1);if("string"==typeof u)e(t,iT[u],n,r,s,a);else if(u){var c=function e(t,i){var n=i_[t.id()],r=null;if(null==n)return r=i(t),i_[t.id()]=[[i,r]],r;for(var s=0;s<n.length;s++){var a=n[s],o=a[0],u=a[1];o===i&&(r=u)}return null===r&&(r=i(t),n.push([i,r])),r}(r,u);if(!c.setSource)return s.push(c),e(t,l,n,r,s,a);c.setSource(h({},t),function(i,o){if(i)return e(t,l,n,r,s,a);s.push(c),e(o,t.type===o.type?l:iT[o.type],n,r,s,a)})}else l.length?e(t,l,n,r,s,a):a?n(t,s):e(t,iT["*"],n,r,s,!0)}(i,iT[i.type],n,t)},1)}(this,i[0],function(e,n){if(t.middleware_=n,t.cache_.sources=i,t.updateSourceCaches_(e),t.src_(e))return 1<i.length?t.src(i.slice(1)):(t.changingSrc_=!1,t.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0),void t.triggerReady());!function(e,t){e.forEach(function(e){return e.setTech&&e.setTech(t)})}(n,t.tech_)})):this.setTimeout(function(){this.error({code:4,message:this.localize(this.options_.notSupportedMessage)})},0)},o.src_=function(e){var t,i,n=this,r=this.selectSource([e]);return!r||((t=r.tech,i=this.techName_,eL(t)===eL(i))?this.ready(function(){this.tech_.constructor.prototype.hasOwnProperty("setSource")?this.techCall_("setSource",e):this.techCall_("src",e.src),this.changingSrc_=!1},!0):(this.changingSrc_=!0,this.loadTech_(r.tech,r.source),this.tech_.ready(function(){n.changingSrc_=!1})),!1)},o.load=function(){this.techCall_("load")},o.reset=function(){var t=this,i=this.options_.Promise||e.Promise;this.paused()||!i?this.doReset_():td(this.play().then(function(){return t.doReset_()}))},o.doReset_=function(){this.tech_&&this.tech_.clearTracks("text"),this.resetCache_(),this.poster(""),this.loadTech_(this.options_.techOrder[0],null),this.techCall_("reset"),this.resetControlBarUI_(),eC(this)&&this.trigger("playerreset")},o.resetControlBarUI_=function(){this.resetProgressBar_(),this.resetPlaybackRate_(),this.resetVolumeBar_()},o.resetProgressBar_=function(){this.currentTime(0);var e=this.controlBar,t=e.durationDisplay,i=e.remainingTimeDisplay;t&&t.updateContent(),i&&i.updateContent()},o.resetPlaybackRate_=function(){this.playbackRate(this.defaultPlaybackRate()),this.handleTechRateChange_()},o.resetVolumeBar_=function(){this.volume(1),this.trigger("volumechange")},o.currentSources=function(){var e=this.currentSource(),t=[];return 0!==Object.keys(e).length&&t.push(e),this.cache_.sources||t},o.currentSource=function(){return this.cache_.source||{}},o.currentSrc=function(){return this.currentSource()&&this.currentSource().src||""},o.currentType=function(){return this.currentSource()&&this.currentSource().type||""},o.preload=function(e){return void 0!==e?(this.techCall_("setPreload",e),void(this.options_.preload=e)):this.techGet_("preload")},o.autoplay=function(e){var t;if(void 0===e)return this.options_.autoplay||!1;"string"==typeof e&&/(any|play|muted)/.test(e)?(this.options_.autoplay=e,this.manualAutoplay_(e),t=!1):this.options_.autoplay=!!e,t=void 0===t?this.options_.autoplay:t,this.tech_&&this.techCall_("setAutoplay",t)},o.playsinline=function(e){return void 0!==e?(this.techCall_("setPlaysinline",e),this.options_.playsinline=e,this):this.techGet_("playsinline")},o.loop=function(e){return void 0!==e?(this.techCall_("setLoop",e),void(this.options_.loop=e)):this.techGet_("loop")},o.poster=function(e){if(void 0===e)return this.poster_;(e=e||"")!==this.poster_&&(this.poster_=e,this.techCall_("setPoster",e),this.isPosterFromTech_=!1,this.trigger("posterchange"))},o.handleTechPosterChange_=function(){if((!this.poster_||this.options_.techCanOverridePoster)&&this.tech_&&this.tech_.poster){var e=this.tech_.poster()||"";e!==this.poster_&&(this.poster_=e,this.isPosterFromTech_=!0,this.trigger("posterchange"))}},o.controls=function(e){if(void 0===e)return!!this.controls_;e=!!e,this.controls_!==e&&(this.controls_=e,this.usingNativeControls()&&this.techCall_("setControls",e),this.controls_?(this.removeClass("vjs-controls-disabled"),this.addClass("vjs-controls-enabled"),this.trigger("controlsenabled"),this.usingNativeControls()||this.addTechControlsListeners_()):(this.removeClass("vjs-controls-enabled"),this.addClass("vjs-controls-disabled"),this.trigger("controlsdisabled"),this.usingNativeControls()||this.removeTechControlsListeners_()))},o.usingNativeControls=function(e){if(void 0===e)return!!this.usingNativeControls_;e=!!e,this.usingNativeControls_!==e&&(this.usingNativeControls_=e,this.usingNativeControls_?(this.addClass("vjs-using-native-controls"),this.trigger("usingnativecontrols")):(this.removeClass("vjs-using-native-controls"),this.trigger("usingcustomcontrols")))},o.error=function(e){if(void 0===e)return this.error_||null;if(this.options_.suppressNotSupportedError&&e&&4===e.code){var t=function(){this.error(e)};return this.options_.suppressNotSupportedError=!1,this.any(["click","touchstart"],t),void this.one("loadstart",function(){this.off(["click","touchstart"],t)})}if(null===e)return this.error_=e,this.removeClass("vjs-error"),void(this.errorDisplay&&this.errorDisplay.close());this.error_=new tu(e),this.addClass("vjs-error"),r.error("(CODE:"+this.error_.code+" "+tu.errorTypes[this.error_.code]+")",this.error_.message,this.error_),this.trigger("error")},o.reportUserActivity=function(e){this.userActivity_=!0},o.userActive=function(e){if(void 0===e)return this.userActive_;if((e=!!e)!==this.userActive_){if(this.userActive_=e,this.userActive_)return this.userActivity_=!0,this.removeClass("vjs-user-inactive"),this.addClass("vjs-user-active"),void this.trigger("useractive");this.tech_&&this.tech_.one("mousemove",function(e){e.stopPropagation(),e.preventDefault()}),this.userActivity_=!1,this.removeClass("vjs-user-active"),this.addClass("vjs-user-inactive"),this.trigger("userinactive")}},o.listenForUserActivity_=function(){function e(e){r(),this.clearInterval(t)}var t,i,n,r=em(this,this.reportUserActivity);this.on("mousedown",function(){r(),this.clearInterval(t),t=this.setInterval(r,250)}),this.on("mousemove",function(e){e.screenX===i&&e.screenY===n||(i=e.screenX,n=e.screenY,r())}),this.on("mouseup",e),this.on("mouseleave",e);var s,a=this.getChild("controlBar");!a||eY||eB||(a.on("mouseenter",function(e){this.player().cache_.inactivityTimeout=this.player().options_.inactivityTimeout,this.player().options_.inactivityTimeout=0}),a.on("mouseleave",function(e){this.player().options_.inactivityTimeout=this.player().cache_.inactivityTimeout})),this.on("keydown",r),this.on("keyup",r),this.setInterval(function(){if(this.userActivity_){this.userActivity_=!1,this.userActive(!0),this.clearTimeout(s);var e=this.options_.inactivityTimeout;e<=0||(s=this.setTimeout(function(){this.userActivity_||this.userActive(!1)},e))}},250)},o.playbackRate=function(e){if(void 0===e)return this.tech_&&this.tech_.featuresPlaybackRate?this.cache_.lastPlaybackRate||this.techGet_("playbackRate"):1;this.techCall_("setPlaybackRate",e)},o.defaultPlaybackRate=function(e){return void 0!==e?this.techCall_("setDefaultPlaybackRate",e):this.tech_&&this.tech_.featuresPlaybackRate?this.techGet_("defaultPlaybackRate"):1},o.isAudio=function(e){if(void 0===e)return!!this.isAudio_;this.isAudio_=!!e},o.addTextTrack=function(e,t,i){if(this.tech_)return this.tech_.addTextTrack(e,t,i)},o.addRemoteTextTrack=function(e,t){if(this.tech_)return this.tech_.addRemoteTextTrack(e,t)},o.removeRemoteTextTrack=function(e){void 0===e&&(e={});var t=e.track;if(t=t||e,this.tech_)return this.tech_.removeRemoteTextTrack(t)},o.getVideoPlaybackQuality=function(){return this.techGet_("getVideoPlaybackQuality")},o.videoWidth=function(){return this.tech_&&this.tech_.videoWidth&&this.tech_.videoWidth()||0},o.videoHeight=function(){return this.tech_&&this.tech_.videoHeight&&this.tech_.videoHeight()||0},o.language=function(e){if(void 0===e)return this.language_;this.language_=String(e).toLowerCase()},o.languages=function(){return eI(a.prototype.options_.languages,this.languages_)},o.toJSON=function(){var e=eI(this.options_),t=e.tracks;e.tracks=[];for(var i=0;i<t.length;i++){var n=t[i];(n=eI(n)).player=void 0,e.tracks[i]=n}return e},o.createModal=function(e,t){var i=this;(t=t||{}).content=e||"";var n=new tv(this,t);return this.addChild(n),n.on("dispose",function(){i.removeChild(n)}),n.open(),n},o.updateCurrentBreakpoint_=function(){if(this.responsive())for(var e=this.currentBreakpoint(),t=this.currentWidth(),i=0;i<n9.length;i++){var n=n9[i];if(t<=this.breakpoints_[n]){if(e===n)return;e&&this.removeClass(nQ[e]),this.addClass(nQ[n]),this.breakpoint_=n;break}}},o.removeCurrentBreakpoint_=function(){var e=this.currentBreakpointClass();this.breakpoint_="",e&&this.removeClass(e)},o.breakpoints=function(e){return void 0===e||(this.breakpoint_="",this.breakpoints_=h({},nJ,e),this.updateCurrentBreakpoint_()),h(this.breakpoints_)},o.responsive=function(e){return void 0===e?this.responsive_:(e=Boolean(e))!==this.responsive_?((this.responsive_=e)?(this.on("playerresize",this.updateCurrentBreakpoint_),this.updateCurrentBreakpoint_()):(this.off("playerresize",this.updateCurrentBreakpoint_),this.removeCurrentBreakpoint_()),e):void 0},o.currentBreakpoint=function(){return this.breakpoint_},o.currentBreakpointClass=function(){return nQ[this.breakpoint_]||""},o.loadMedia=function(e,t){var i=this;if(e&&"object"==typeof e){this.reset(),this.cache_.media=eI(e);var n=this.cache_.media,r=n.artwork,s=n.poster,a=n.src,o=n.textTracks;!r&&s&&(this.cache_.media.artwork=[{src:s,type:ix(s)}]),a&&this.src(a),s&&this.poster(s),Array.isArray(o)&&o.forEach(function(e){return i.addRemoteTextTrack(e,!1)}),this.ready(t)}},o.getMedia=function(){if(this.cache_.media)return eI(this.cache_.media);var e=this.poster(),t={src:this.currentSources(),textTracks:Array.prototype.map.call(this.remoteTextTracks(),function(e){return{kind:e.kind,label:e.label,language:e.language,src:e.src}})};return e&&(t.poster=e,t.artwork=[{src:t.poster,type:ix(t.poster)}]),t},a.getTagSettings=function(e){var t={sources:[],tracks:[]},i=P(e),n=i["data-setup"];if(k(e,"vjs-fill")&&(i.fill=!0),k(e,"vjs-fluid")&&(i.fluid=!0),null!==n){var s=tc(n||"{}"),a=s[0],o=s[1];a&&r.error(a),h(i,o)}if(h(t,i),e.hasChildNodes())for(var u=e.childNodes,l=0,c=u.length;l<c;l++){var d=u[l],p=d.nodeName.toLowerCase();"source"===p?t.sources.push(P(d)):"track"===p&&t.tracks.push(P(d))}return t},o.flexNotSupported_=function(){var e=t.createElement("i");return!("flexBasis"in e.style||"webkitFlexBasis"in e.style||"mozFlexBasis"in e.style||"msFlexBasis"in e.style||"msFlexOrder"in e.style)},a}(eD);tq.names.forEach(function(e){var t=tq[e];nZ.prototype[t.getterName]=function(){return this.tech_?this.tech_[t.getterName]():(this[t.privateName]=this[t.privateName]||new t.ListClass,this[t.privateName])}}),nZ.prototype.crossorigin=nZ.prototype.crossOrigin,nZ.players={};var re=e.navigator;nZ.prototype.options_={techOrder:ib.defaultTechOrder_,html5:{},flash:{},inactivityTimeout:2e3,playbackRates:[],liveui:!1,children:["mediaLoader","posterImage","textTrackDisplay","loadingSpinner","bigPlayButton","liveTracker","controlBar","errorDisplay","textTrackSettings","resizeManager"],language:re&&(re.languages&&re.languages[0]||re.userLanguage||re.language)||"en",languages:{},notSupportedMessage:"No compatible source was found for this media.",fullscreen:{options:{navigationUI:"hide"}},breakpoints:{},responsive:!1},["ended","seeking","seekable","networkState","readyState"].forEach(function(e){nZ.prototype[e]=function(){return this.techGet_(e)}}),nK.forEach(function(e){nZ.prototype["handleTech"+eL(e)+"_"]=function(){return this.trigger(e)}}),eD.registerComponent("Player",nZ);var rt=a(function(e){function t(i,n){return e.exports=t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(i,n)}e.exports=t});function ri(e){return rh.hasOwnProperty(e)}function rn(e){return ri(e)?rh[e]:void 0}function rr(e,t){e[rc]=e[rc]||{},e[rc][t]=!0}function rs(e,t,i){var n=(i?"before":"")+"pluginsetup";e.trigger(n,t),e.trigger(n+":"+t.name,t)}function ra(e,t){return t.prototype.name=e,function(){rs(this,{name:e,plugin:t,instance:null},!0);for(var i=arguments.length,n=Array(i),r=0;r<i;r++)n[r]=arguments[r];var s=ru(t,[this].concat(n));return this[e]=function(){return s},rs(this,s.getEventHash()),s}}var ro=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}},ru=a(function(e){function t(i,n,r){return ro()?e.exports=t=Reflect.construct:e.exports=t=function(e,t,i){var n=[null];n.push.apply(n,t);var r=new(Function.bind.apply(e,n));return i&&rt(r,i.prototype),r},t.apply(null,arguments)}e.exports=t}),rl="plugin",rc="activePlugins_",rh={},rd=function(){function e(t){if(this.constructor===e)throw Error("Plugin must be sub-classed; not directly instantiated.");this.player=t,ew(this),delete this.trigger,ex(this,this.constructor.defaultState),rr(t,this.name),this.dispose=em(this,this.dispose),t.on("dispose",this.dispose)}var t=e.prototype;return t.version=function(){return this.constructor.VERSION},t.getEventHash=function(e){return void 0===e&&(e={}),e.name=this.name,e.plugin=this.constructor,e.instance=this,e},t.trigger=function(e,t){return void 0===t&&(t={}),ed(this.eventBusEl_,e,this.getEventHash(t))},t.handleStateChanged=function(e){},t.dispose=function(){var e=this.name,t=this.player;this.trigger("dispose"),this.off(),t.off("dispose",this.dispose),t[rc][e]=!1,this.player=this.state=null,t[e]=ra(e,rh[e])},e.isBasic=function(t){var i="string"==typeof t?rn(t):t;return"function"==typeof i&&!e.prototype.isPrototypeOf(i.prototype)},e.registerPlugin=function(t,i){if("string"!=typeof t)throw Error('Illegal plugin name, "'+t+'", must be a string, was '+typeof t+".");if(ri(t))r.warn('A plugin named "'+t+'" already exists. You may want to avoid re-registering plugins!');else if(nZ.prototype.hasOwnProperty(t))throw Error('Illegal plugin name, "'+t+'", cannot share a name with an existing player method!');if("function"!=typeof i)throw Error('Illegal plugin for "'+t+'", must be a function, was '+typeof i+".");return rh[t]=i,t!==rl&&(e.isBasic(i)?nZ.prototype[t]=function(e,t){function i(){rs(this,{name:e,plugin:t,instance:null},!0);var i=t.apply(this,arguments);return rr(this,e),rs(this,{name:e,plugin:t,instance:i}),i}return Object.keys(t).forEach(function(e){i[e]=t[e]}),i}(t,i):nZ.prototype[t]=ra(t,i)),i},e.deregisterPlugin=function(e){if(e===rl)throw Error("Cannot de-register base plugin.");ri(e)&&(delete rh[e],delete nZ.prototype[e])},e.getPlugins=function(e){var t;return void 0===e&&(e=Object.keys(rh)),e.forEach(function(e){var i=rn(e);i&&((t=t||{})[e]=i)}),t},e.getPluginVersion=function(e){var t=rn(e);return t&&t.VERSION||""},e}();rd.getPlugin=rn,rd.BASE_PLUGIN_NAME=rl,rd.registerPlugin(rl,rd),nZ.prototype.usingPlugin=function(e){return!!this[rc]&&!0===this[rc][e]},nZ.prototype.hasPlugin=function(e){return!!ri(e)};var rp=function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&rt(e,t)},rf=function(e){return 0===e.indexOf("#")?e.slice(1):e};function rm(e,t,i){var n=rm.getPlayer(e);if(n)return t&&r.warn('Player "'+e+'" is already initialised. Options will not be applied.'),i&&n.ready(i),n;var s="string"==typeof e?W("#"+rf(e)):e;if(!y(s))throw TypeError("The element or ID supplied is not valid. (videojs)");return s.ownerDocument.defaultView&&s.ownerDocument.body.contains(s)||r.warn("The element supplied is not included in the DOM"),t=t||{},rm.hooks("beforesetup").forEach(function(e){var i=e(s,eI(t));d(i)&&!Array.isArray(i)?t=eI(t,i):r.error("please return an object in beforesetup hooks")}),n=new(eD.getComponent("Player"))(s,t,i),rm.hooks("setup").forEach(function(e){return e(n)}),n}if(rm.hooks_={},rm.hooks=function(e,t){return rm.hooks_[e]=rm.hooks_[e]||[],t&&(rm.hooks_[e]=rm.hooks_[e].concat(t)),rm.hooks_[e]},rm.hook=function(e,t){rm.hooks(e,t)},rm.hookOnce=function(e,t){rm.hooks(e,[].concat(t).map(function(t){return function i(){return rm.removeHook(e,i),t.apply(void 0,arguments)}}))},rm.removeHook=function(e,t){var i=rm.hooks(e).indexOf(t);return!(i<=-1)&&(rm.hooks_[e]=rm.hooks_[e].slice(),rm.hooks_[e].splice(i,1),!0)},!0!==e.VIDEOJS_NO_DYNAMIC_STYLE&&v()){var rg=W(".vjs-styles-defaults");if(!rg){rg=J("vjs-styles-defaults");var rv=W("head");rv&&rv.insertBefore(rg,rv.firstChild),Z(rg,"\n      .video-js {\n        width: 300px;\n        height: 150px;\n      }\n\n      .vjs-fluid {\n        padding-top: 56.25%\n      }\n    ")}}Y(1,rm),rm.VERSION=i,rm.options=nZ.prototype.options_,rm.getPlayers=function(){return nZ.players},rm.getPlayer=function(e){var t,i=nZ.players;if("string"==typeof e){var n=rf(e),r=i[n];if(r)return r;t=W("#"+n)}else t=e;if(y(t)){var s=t,a=s.player,o=s.playerId;if(a||i[o])return a||i[o]}},rm.getAllPlayers=function(){return Object.keys(nZ.players).map(function(e){return nZ.players[e]}).filter(Boolean)},rm.players=nZ.players,rm.getComponent=eD.getComponent,rm.registerComponent=function(e,t){ib.isTech(t)&&r.warn("The "+e+" tech was registered as a component. It should instead be registered using videojs.registerTech(name, tech)"),eD.registerComponent.call(eD,e,t)},rm.getTech=ib.getTech,rm.registerTech=ib.registerTech,rm.use=function(e,t){iT[e]=iT[e]||[],iT[e].push(t)},Object.defineProperty(rm,"middleware",{value:{},writeable:!1,enumerable:!0}),Object.defineProperty(rm.middleware,"TERMINATOR",{value:iS,writeable:!1,enumerable:!0}),rm.browser=eQ,rm.TOUCH_ENABLED=eG,rm.extend=function(e,t){void 0===t&&(t={});var i=function(){e.apply(this,arguments)},n={};for(var r in"object"==typeof t?(t.constructor!==Object.prototype.constructor&&(i=t.constructor),n=t):"function"==typeof t&&(i=t),rp(i,e),e&&(i.super_=e),n)n.hasOwnProperty(r)&&(i.prototype[r]=n[r]);return i},rm.mergeOptions=eI,rm.bind=em,rm.registerPlugin=rd.registerPlugin,rm.deregisterPlugin=rd.deregisterPlugin,rm.plugin=function(e,t){return r.warn("videojs.plugin() is deprecated; use videojs.registerPlugin() instead"),rd.registerPlugin(e,t)},rm.getPlugins=rd.getPlugins,rm.getPlugin=rd.getPlugin,rm.getPluginVersion=rd.getPluginVersion,rm.addLanguage=function(e,t){var i;return e=(""+e).toLowerCase(),rm.options.languages=eI(rm.options.languages,((i={})[e]=t,i)),rm.options.languages[e]},rm.log=r,rm.createLogger=s,rm.createTimeRange=rm.createTimeRanges=te,rm.formatTime=iF,rm.setFormatTime=function(e){ij=e},rm.resetFormatTime=function(){ij=iB},rm.parseUrl=tT,rm.isCrossOrigin=tk,rm.EventTarget=ev,rm.on=ec,rm.one=ep,rm.off=eh,rm.trigger=ed,rm.xhr=t3,rm.TextTrack=tB,rm.AudioTrack=tj,rm.VideoTrack=tF,["isEl","isTextNode","createEl","hasClass","addClass","removeClass","toggleClass","setAttributes","getAttributes","emptyEl","appendContent","insertContent"].forEach(function(e){rm[e]=function(){return r.warn("videojs."+e+"() is deprecated; use videojs.dom."+e+"() instead"),G[e].apply(null,arguments)}}),rm.computedStyle=f,rm.dom=G,rm.url=tO,rm.defineLazyProperty=nV;var ry=a(function(e,t){var i,n,r,s,a;i=/^((?:[a-zA-Z0-9+\-.]+:)?)(\/\/[^\/?#]*)?((?:[^\/\?#]*\/)*.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,n=/^([^\/?#]*)(.*)$/,r=/(?:\/|^)\.(?=\/)/g,s=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,a={buildAbsoluteURL:function(e,t,i){if(i=i||{},e=e.trim(),!(t=t.trim())){if(!i.alwaysNormalize)return e;var r=a.parseURL(e);if(!r)throw Error("Error trying to parse base URL.");return r.path=a.normalizePath(r.path),a.buildURLFromParts(r)}var s=a.parseURL(t);if(!s)throw Error("Error trying to parse relative URL.");if(s.scheme)return i.alwaysNormalize?(s.path=a.normalizePath(s.path),a.buildURLFromParts(s)):t;var o=a.parseURL(e);if(!o)throw Error("Error trying to parse base URL.");if(!o.netLoc&&o.path&&"/"!==o.path[0]){var u=n.exec(o.path);o.netLoc=u[1],o.path=u[2]}o.netLoc&&!o.path&&(o.path="/");var l={scheme:o.scheme,netLoc:s.netLoc,path:null,params:s.params,query:s.query,fragment:s.fragment};if(!s.netLoc&&(l.netLoc=o.netLoc,"/"!==s.path[0])){if(s.path){var c=o.path,h=c.substring(0,c.lastIndexOf("/")+1)+s.path;l.path=a.normalizePath(h)}else l.path=o.path,s.params||(l.params=o.params,s.query||(l.query=o.query))}return null===l.path&&(l.path=i.alwaysNormalize?a.normalizePath(s.path):s.path),a.buildURLFromParts(l)},parseURL:function(e){var t=i.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(r,"");e.length!==(e=e.replace(s,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}},e.exports=a});/*! @name m3u8-parser @version 4.4.0 @license Apache-2.0 */ function r8(){return(r8=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}function r$(e,t){e.prototype=Object.create(t.prototype),(e.prototype.constructor=e).__proto__=t}function rb(e){for(var t,i=e.split(RegExp('(?:^|,)((?:[^=]*)=(?:"[^"]*"|[^,]*))')),n={},r=i.length;r--;)""!==i[r]&&((t=/([^=]*)=(.*)/.exec(i[r]).slice(1))[0]=t[0].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^\s+|\s+$/g,""),t[1]=t[1].replace(/^['"](.*)['"]$/g,"$1"),n[t[0]]=t[1]);return n}var rT=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},t.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},t.trigger=function(e,t){var i,n,r,s=this.listeners[e];if(s){if(2===arguments.length)for(n=s.length,i=0;i<n;++i)s[i].call(this,t);else for(r=Array.prototype.slice.call(arguments,1),n=s.length,i=0;i<n;++i)s[i].apply(this,r)}},t.dispose=function(){this.listeners={}},t.pipe=function(e){this.on("data",function(t){e.push(t)})},e}(),r_=function(e){function t(){var t;return(t=e.call(this)||this).buffer="",t}return r$(t,e),t.prototype.push=function(e){var t;for(this.buffer+=e,t=this.buffer.indexOf("\n");-1<t;t=this.buffer.indexOf("\n"))this.trigger("data",this.buffer.substring(0,t)),this.buffer=this.buffer.substring(t+1)},t}(rT),rS=function(e){function t(){var t;return(t=e.call(this)||this).customParsers=[],t.tagMappers=[],t}r$(t,e);var i=t.prototype;return i.push=function(e){var t,i,n=this;0!==(e=e.trim()).length&&("#"===e[0]?this.tagMappers.reduce(function(t,i){var n=i(e);return n===e?t:t.concat([n])},[e]).forEach(function(e){for(var r=0;r<n.customParsers.length;r++)if(n.customParsers[r].call(n,e))return;if(0===e.indexOf("#EXT")){if(e=e.replace("\r",""),t=/^#EXTM3U/.exec(e))n.trigger("data",{type:"tag",tagType:"m3u"});else{if(t=/^#EXTINF:?([0-9\.]*)?,?(.*)?$/.exec(e))return i={type:"tag",tagType:"inf"},t[1]&&(i.duration=parseFloat(t[1])),t[2]&&(i.title=t[2]),void n.trigger("data",i);if(t=/^#EXT-X-TARGETDURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"targetduration"},t[1]&&(i.duration=parseInt(t[1],10)),void n.trigger("data",i);if(t=/^#ZEN-TOTAL-DURATION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"totalduration"},t[1]&&(i.duration=parseInt(t[1],10)),void n.trigger("data",i);if(t=/^#EXT-X-VERSION:?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"version"},t[1]&&(i.version=parseInt(t[1],10)),void n.trigger("data",i);if(t=/^#EXT-X-MEDIA-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"media-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void n.trigger("data",i);if(t=/^#EXT-X-DISCONTINUITY-SEQUENCE:?(\-?[0-9.]*)?/.exec(e))return i={type:"tag",tagType:"discontinuity-sequence"},t[1]&&(i.number=parseInt(t[1],10)),void n.trigger("data",i);if(t=/^#EXT-X-PLAYLIST-TYPE:?(.*)?$/.exec(e))return i={type:"tag",tagType:"playlist-type"},t[1]&&(i.playlistType=t[1]),void n.trigger("data",i);if(t=/^#EXT-X-BYTERANGE:?([0-9.]*)?@?([0-9.]*)?/.exec(e))return i={type:"tag",tagType:"byterange"},t[1]&&(i.length=parseInt(t[1],10)),t[2]&&(i.offset=parseInt(t[2],10)),void n.trigger("data",i);if(t=/^#EXT-X-ALLOW-CACHE:?(YES|NO)?/.exec(e))return i={type:"tag",tagType:"allow-cache"},t[1]&&(i.allowed=!/NO/.test(t[1])),void n.trigger("data",i);if(t=/^#EXT-X-MAP:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"map"},t[1]){var s=rb(t[1]);if(s.URI&&(i.uri=s.URI),s.BYTERANGE){var a=s.BYTERANGE.split("@"),o=a[0],u=a[1];i.byterange={},o&&(i.byterange.length=parseInt(o,10)),u&&(i.byterange.offset=parseInt(u,10))}}n.trigger("data",i)}else if(t=/^#EXT-X-STREAM-INF:?(.*)$/.exec(e)){if(i={type:"tag",tagType:"stream-inf"},t[1]){if(i.attributes=rb(t[1]),i.attributes.RESOLUTION){var l=i.attributes.RESOLUTION.split("x"),c={};l[0]&&(c.width=parseInt(l[0],10)),l[1]&&(c.height=parseInt(l[1],10)),i.attributes.RESOLUTION=c}i.attributes.BANDWIDTH&&(i.attributes.BANDWIDTH=parseInt(i.attributes.BANDWIDTH,10)),i.attributes["PROGRAM-ID"]&&(i.attributes["PROGRAM-ID"]=parseInt(i.attributes["PROGRAM-ID"],10))}n.trigger("data",i)}else{if(t=/^#EXT-X-MEDIA:?(.*)$/.exec(e))return i={type:"tag",tagType:"media"},t[1]&&(i.attributes=rb(t[1])),void n.trigger("data",i);if(t=/^#EXT-X-ENDLIST/.exec(e))n.trigger("data",{type:"tag",tagType:"endlist"});else if(t=/^#EXT-X-DISCONTINUITY/.exec(e))n.trigger("data",{type:"tag",tagType:"discontinuity"});else{if(t=/^#EXT-X-PROGRAM-DATE-TIME:?(.*)$/.exec(e))return i={type:"tag",tagType:"program-date-time"},t[1]&&(i.dateTimeString=t[1],i.dateTimeObject=new Date(t[1])),void n.trigger("data",i);if(t=/^#EXT-X-KEY:?(.*)$/.exec(e))return i={type:"tag",tagType:"key"},t[1]&&(i.attributes=rb(t[1]),i.attributes.IV&&("0x"===i.attributes.IV.substring(0,2).toLowerCase()&&(i.attributes.IV=i.attributes.IV.substring(2)),i.attributes.IV=i.attributes.IV.match(/.{8}/g),i.attributes.IV[0]=parseInt(i.attributes.IV[0],16),i.attributes.IV[1]=parseInt(i.attributes.IV[1],16),i.attributes.IV[2]=parseInt(i.attributes.IV[2],16),i.attributes.IV[3]=parseInt(i.attributes.IV[3],16),i.attributes.IV=new Uint32Array(i.attributes.IV))),void n.trigger("data",i);if(t=/^#EXT-X-START:?(.*)$/.exec(e))return i={type:"tag",tagType:"start"},t[1]&&(i.attributes=rb(t[1]),i.attributes["TIME-OFFSET"]=parseFloat(i.attributes["TIME-OFFSET"]),i.attributes.PRECISE=/YES/.test(i.attributes.PRECISE)),void n.trigger("data",i);if(t=/^#EXT-X-CUE-OUT-CONT:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-out-cont"},t[1]?i.data=t[1]:i.data="",void n.trigger("data",i);if(t=/^#EXT-X-CUE-OUT:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-out"},t[1]?i.data=t[1]:i.data="",void n.trigger("data",i);if(t=/^#EXT-X-CUE-IN:?(.*)?$/.exec(e))return i={type:"tag",tagType:"cue-in"},t[1]?i.data=t[1]:i.data="",void n.trigger("data",i);n.trigger("data",{type:"tag",data:e.slice(4)})}}}}else n.trigger("data",{type:"comment",text:e.slice(1)})}):this.trigger("data",{type:"uri",uri:e}))},i.addParser=function(e){var t=this,i=e.expression,n=e.customType,r=e.dataParser,s=e.segment;"function"!=typeof r&&(r=function(e){return e}),this.customParsers.push(function(e){if(i.exec(e))return t.trigger("data",{type:"custom",data:r(e),customType:n,segment:s}),!0})},i.addTagMapper=function(e){var t=e.expression,i=e.map;this.tagMappers.push(function(e){return t.test(e)?i(e):e})},t}(rT),rk=function(t){function i(){(i=t.call(this)||this).lineStream=new r_,i.parseStream=new rS,i.lineStream.pipe(i.parseStream);var i,n,r,s=function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(i),a=[],o={},u={AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},l=0;return i.manifest={allowCache:!0,discontinuityStarts:[],segments:[]},i.parseStream.on("data",function(t){var i,c;({tag:function(){(({"allow-cache":function(){this.manifest.allowCache=t.allowed,"allowed"in t||(this.trigger("info",{message:"defaulting allowCache to YES"}),this.manifest.allowCache=!0)},byterange:function(){var e={};"length"in t&&((o.byterange=e).length=t.length,"offset"in t||(this.trigger("info",{message:"defaulting offset to zero"}),t.offset=0)),"offset"in t&&((o.byterange=e).offset=t.offset)},endlist:function(){this.manifest.endList=!0},inf:function(){"mediaSequence"in this.manifest||(this.manifest.mediaSequence=0,this.trigger("info",{message:"defaulting media sequence to zero"})),"discontinuitySequence"in this.manifest||(this.manifest.discontinuitySequence=0,this.trigger("info",{message:"defaulting discontinuity sequence to zero"})),0<t.duration&&(o.duration=t.duration),0===t.duration&&(o.duration=.01,this.trigger("info",{message:"updating zero segment duration to a small value"})),this.manifest.segments=a},key:function(){if(t.attributes){if("NONE"!==t.attributes.METHOD){if(t.attributes.URI){if("urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"===t.attributes.KEYFORMAT)return -1===["SAMPLE-AES","SAMPLE-AES-CTR","SAMPLE-AES-CENC"].indexOf(t.attributes.METHOD)?void this.trigger("warn",{message:"invalid key method provided for Widevine"}):("SAMPLE-AES-CENC"===t.attributes.METHOD&&this.trigger("warn",{message:"SAMPLE-AES-CENC is deprecated, please use SAMPLE-AES-CTR instead"}),"data:text/plain;base64,"!==t.attributes.URI.substring(0,23)?void this.trigger("warn",{message:"invalid key URI provided for Widevine"}):t.attributes.KEYID&&"0x"===t.attributes.KEYID.substring(0,2)?void(this.manifest.contentProtection={"com.widevine.alpha":{attributes:{schemeIdUri:t.attributes.KEYFORMAT,keyId:t.attributes.KEYID.substring(2)},pssh:function(t){for(var i=e.atob(t||""),n=new Uint8Array(i.length),r=0;r<i.length;r++)n[r]=i.charCodeAt(r);return n}(t.attributes.URI.split(",")[1])}}):void this.trigger("warn",{message:"invalid key ID provided for Widevine"}));t.attributes.METHOD||this.trigger("warn",{message:"defaulting key method to AES-128"}),r={method:t.attributes.METHOD||"AES-128",uri:t.attributes.URI},void 0!==t.attributes.IV&&(r.iv=t.attributes.IV)}else this.trigger("warn",{message:"ignoring key declaration without URI"})}else r=null}else this.trigger("warn",{message:"ignoring key declaration without attribute list"})},"media-sequence":function(){isFinite(t.number)?this.manifest.mediaSequence=t.number:this.trigger("warn",{message:"ignoring invalid media sequence: "+t.number})},"discontinuity-sequence":function(){isFinite(t.number)?(this.manifest.discontinuitySequence=t.number,l=t.number):this.trigger("warn",{message:"ignoring invalid discontinuity sequence: "+t.number})},"playlist-type":function(){/VOD|EVENT/.test(t.playlistType)?this.manifest.playlistType=t.playlistType:this.trigger("warn",{message:"ignoring unknown playlist type: "+t.playlist})},map:function(){n={},t.uri&&(n.uri=t.uri),t.byterange&&(n.byterange=t.byterange)},"stream-inf":function(){this.manifest.playlists=a,this.manifest.mediaGroups=this.manifest.mediaGroups||u,t.attributes?(o.attributes||(o.attributes={}),r8(o.attributes,t.attributes)):this.trigger("warn",{message:"ignoring empty stream-inf attributes"})},media:function(){if(this.manifest.mediaGroups=this.manifest.mediaGroups||u,t.attributes&&t.attributes.TYPE&&t.attributes["GROUP-ID"]&&t.attributes.NAME){var e=this.manifest.mediaGroups[t.attributes.TYPE];e[t.attributes["GROUP-ID"]]=e[t.attributes["GROUP-ID"]]||{},i=e[t.attributes["GROUP-ID"]],(c={default:/yes/i.test(t.attributes.DEFAULT)}).default?c.autoselect=!0:c.autoselect=/yes/i.test(t.attributes.AUTOSELECT),t.attributes.LANGUAGE&&(c.language=t.attributes.LANGUAGE),t.attributes.URI&&(c.uri=t.attributes.URI),t.attributes["INSTREAM-ID"]&&(c.instreamId=t.attributes["INSTREAM-ID"]),t.attributes.CHARACTERISTICS&&(c.characteristics=t.attributes.CHARACTERISTICS),t.attributes.FORCED&&(c.forced=/yes/i.test(t.attributes.FORCED)),i[t.attributes.NAME]=c}else this.trigger("warn",{message:"ignoring incomplete or missing media group"})},discontinuity:function(){l+=1,o.discontinuity=!0,this.manifest.discontinuityStarts.push(a.length)},"program-date-time":function(){void 0===this.manifest.dateTimeString&&(this.manifest.dateTimeString=t.dateTimeString,this.manifest.dateTimeObject=t.dateTimeObject),o.dateTimeString=t.dateTimeString,o.dateTimeObject=t.dateTimeObject},targetduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid target duration: "+t.duration}):this.manifest.targetDuration=t.duration},totalduration:function(){!isFinite(t.duration)||t.duration<0?this.trigger("warn",{message:"ignoring invalid total duration: "+t.duration}):this.manifest.totalDuration=t.duration},start:function(){t.attributes&&!isNaN(t.attributes["TIME-OFFSET"])?this.manifest.start={timeOffset:t.attributes["TIME-OFFSET"],precise:t.attributes.PRECISE}:this.trigger("warn",{message:"ignoring start declaration without appropriate attribute list"})},"cue-out":function(){o.cueOut=t.data},"cue-out-cont":function(){o.cueOutCont=t.data},"cue-in":function(){o.cueIn=t.data}})[t.tagType]||function(){}).call(s)},uri:function(){o.uri=t.uri,a.push(o),!this.manifest.targetDuration||"duration"in o||(this.trigger("warn",{message:"defaulting segment duration to the target duration"}),o.duration=this.manifest.targetDuration),r&&(o.key=r),o.timeline=l,n&&(o.map=n),o={}},comment:function(){},custom:function(){t.segment?(o.custom=o.custom||{},o.custom[t.customType]=t.data):(this.manifest.custom=this.manifest.custom||{},this.manifest.custom[t.customType]=t.data)}})[t.type].call(s)}),i}r$(i,t);var n=i.prototype;return n.push=function(e){this.lineStream.push(e)},n.end=function(){this.lineStream.push("\n")},n.addParser=function(e){this.parseStream.addParser(e)},n.addTagMapper=function(e){this.parseStream.addTagMapper(e)},i}(rT);function rC(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var rE,rw=rC(ry),r0=rC(e),rx=function(e,t){return/^[a-z]+:/i.test(t)?t:(/\/\//i.test(e)||(e=rw.buildAbsoluteURL(r0.location&&r0.location.href||"",e)),rw.buildAbsoluteURL(e,t))},rP=(rE=e)&&"object"==typeof rE&&"default"in rE?rE.default:rE,rL=function(e){for(var t,i=(t=e,rP.atob?rP.atob(t):Buffer.from(t,"base64").toString("binary")),n=new Uint8Array(i.length),r=0;r<i.length;r++)n[r]=i.charCodeAt(r);return n},rI=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,rD=RegExp("[\\-\\.0-9"+rI.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),rA=RegExp("^"+rI.source+rD.source+"*(?::"+rI.source+rD.source+"*)?$");function rO(){}function r2(e,t){return t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber,t}function rR(e,t,i,n,r,s){for(var a,o=++t,u=0;;){var l=e.charAt(o);switch(l){case"=":if(1===u)a=e.slice(t,o),u=3;else{if(2!==u)throw Error("attribute equal must after attrName");u=3}break;case"'":case'"':if(3===u||1===u){if(1===u&&(s.warning('attribute value must after "="'),a=e.slice(t,o)),t=o+1,!(0<(o=e.indexOf(l,t))))throw Error("attribute value no end '"+l+"' match");c=e.slice(t,o).replace(/&#?\w+;/g,r),i.add(a,c,t-1),u=5}else{if(4!=u)throw Error('attribute value must after "="');c=e.slice(t,o).replace(/&#?\w+;/g,r),i.add(a,c,t),s.warning('attribute "'+a+'" missed start quot('+l+")!!"),t=o+1,u=5}break;case"/":switch(u){case 0:i.setTagName(e.slice(t,o));case 5:case 6:case 7:u=7,i.closed=!0;case 4:case 1:case 2:break;default:throw Error("attribute invalid close char('/')")}break;case"":return s.error("unexpected end of input"),0==u&&i.setTagName(e.slice(t,o)),o;case">":switch(u){case 0:i.setTagName(e.slice(t,o));case 5:case 6:case 7:break;case 4:case 1:"/"===(c=e.slice(t,o)).slice(-1)&&(i.closed=!0,c=c.slice(0,-1));case 2:2===u&&(c=a),4==u?(s.warning('attribute "'+c+'" missed quot(")!!'),i.add(a,c.replace(/&#?\w+;/g,r),t)):("http://www.w3.org/1999/xhtml"===n[""]&&c.match(/^(?:disabled|checked|selected)$/i)||s.warning('attribute "'+c+'" missed value!! "'+c+'" instead!!'),i.add(c,c,t));break;case 3:throw Error("attribute value missed!!")}return o;case"\x80":l=" ";default:if(l<=" ")switch(u){case 0:i.setTagName(e.slice(t,o)),u=6;break;case 1:a=e.slice(t,o),u=2;break;case 4:var c=e.slice(t,o).replace(/&#?\w+;/g,r);s.warning('attribute "'+c+'" missed quot(")!!'),i.add(a,c,t);case 5:u=6}else switch(u){case 2:i.tagName,"http://www.w3.org/1999/xhtml"===n[""]&&a.match(/^(?:disabled|checked|selected)$/i)||s.warning('attribute "'+a+'" missed value!! "'+a+'" instead2!!'),i.add(a,a,t),t=o,u=1;break;case 5:s.warning('attribute space is required"'+a+'"!!');case 6:u=1,t=o;break;case 3:u=4,t=o;break;case 7:throw Error("elements closed character '/' and '>' must be connected to")}}o++}}function rN(e,t,i){for(var n,r=e.tagName,s=null,a=e.length;a--;){var o=e[a],u=o.qName,l=o.value;if(0<(n=u.indexOf(":")))var c=o.prefix=u.slice(0,n),h=u.slice(n+1),d="xmlns"===c&&h;else c=null,d="xmlns"===(h=u)&&"";o.localName=h,!1!==d&&(null==s&&(s={},rU(i,i={})),i[d]=s[d]=l,o.uri="http://www.w3.org/2000/xmlns/",t.startPrefixMapping(d,l))}for(a=e.length;a--;)(c=(o=e[a]).prefix)&&("xml"===c&&(o.uri="http://www.w3.org/XML/1998/namespace"),"xmlns"!==c&&(o.uri=i[c||""]));h=0<(n=r.indexOf(":"))?(c=e.prefix=r.slice(0,n),e.localName=r.slice(n+1)):(c=null,e.localName=r);var p=e.uri=i[c||""];if(t.startElement(p,h,r,e),!e.closed)return e.currentNSMap=i,e.localNSMap=s,!0;if(t.endElement(p,h,r),s)for(c in s)t.endPrefixMapping(c)}function r3(e,t,i,n,r){if(/^(?:script|textarea)$/i.test(i)){var s=e.indexOf("</"+i+">",t),a=e.substring(t+1,s);if(/[&<]/.test(a))return/^script$/i.test(i)||(a=a.replace(/&#?\w+;/g,n)),r.characters(a,0,a.length),s}return t+1}function r4(e,t,i,n){var r=n[i];return null==r&&((r=e.lastIndexOf("</"+i+">"))<t&&(r=e.lastIndexOf("</"+i)),n[i]=r),r<t}function rU(e,t){for(var i in e)t[i]=e[i]}function r1(e,t,i,n){if("-"===e.charAt(t+2))return"-"!==e.charAt(t+3)?-1:t<(r=e.indexOf("-->",t+4))?(i.comment(e,t+4,r-t-4),r+3):(n.error("Unclosed comment"),-1);if("CDATA["==e.substr(t+3,6)){var r=e.indexOf("]]>",t+9);return i.startCDATA(),i.characters(e,t+9,r-t-9),i.endCDATA(),r+3}var s=function(e,t){var i,n=[],r=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;for(r.lastIndex=t,r.exec(e);i=r.exec(e);)if(n.push(i),i[1])return n}(e,t),a=s.length;if(1<a&&/!doctype/i.test(s[0][0])){var o=s[1][0],u=3<a&&/^public$/i.test(s[2][0])&&s[3][0],l=4<a&&s[4][0],c=s[a-1];return i.startDTD(o,u&&u.replace(/^(['"])(.*?)\1$/,"$2"),l&&l.replace(/^(['"])(.*?)\1$/,"$2")),i.endDTD(),c.index+c[0].length}return -1}function rM(e,t,i){var n=e.indexOf("?>",t);if(n){var r=e.substring(t,n).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(r)return r[0].length,i.processingInstruction(r[1],r[2]),n+2}return -1}function r6(e){}function rB(e,t){return e.__proto__=t,e}rO.prototype={parse:function(e,t,i){var n=this.domBuilder;n.startDocument(),rU(t,t={}),function(e,t,i,n,r){function s(e){var t=e.slice(1,-1);return t in i?i[t]:"#"===t.charAt(0)?function(e){if(65535<e){var t;return String.fromCharCode(55296+((e-=65536)>>10),56320+(1023&e))}return String.fromCharCode(e)}(parseInt(t.substr(1).replace("x","0x"))):(r.error("entity not found:"+e),e)}function a(t){if(f<t){var i=e.substring(f,t).replace(/&#?\w+;/g,s);h&&o(f),n.characters(i,0,t-f),f=t}}function o(t,i){for(;l<=t&&(i=c.exec(e));)l=(u=i.index)+i[0].length,h.lineNumber++;h.columnNumber=t-u+1}for(var u=0,l=0,c=/.*(?:\r\n?|\n)|.*$/g,h=n.locator,d=[{currentNSMap:t}],p={},f=0;;){try{var m=e.indexOf("<",f);if(m<0){if(!e.substr(f).match(/^\s*$/)){var g=n.doc,v=g.createTextNode(e.substr(f));g.appendChild(v),n.currentElement=v}return}switch(f<m&&a(m),e.charAt(m+1)){case"/":var y=e.indexOf(">",m+3),$=e.substring(m+2,y),b=d.pop();y<0?($=e.substring(m+2).replace(/[\s<].*/,""),r.error("end tag name: "+$+" is not complete:"+b.tagName),y=m+1+$.length):$.match(/\s</)&&($=$.replace(/[\s<].*/,""),r.error("end tag name: "+$+" maybe not complete"),y=m+1+$.length);var T=b.localNSMap,_=b.tagName==$;if(_||b.tagName&&b.tagName.toLowerCase()==$.toLowerCase()){if(n.endElement(b.uri,b.localName,$),T)for(var S in T)n.endPrefixMapping(S);_||r.fatalError("end tag name: "+$+" is not match the current start tagName:"+b.tagName)}else d.push(b);y++;break;case"?":h&&o(m),y=rM(e,m,n);break;case"!":h&&o(m),y=r1(e,m,n,r);break;default:h&&o(m);var k=new r6,C=d[d.length-1].currentNSMap,E=(y=rR(e,m,k,C,s,r),k.length);if(!k.closed&&r4(e,y,k.tagName,p)&&(k.closed=!0,i.nbsp||r.warning("unclosed xml attribute")),h&&E){for(var w=r2(h,{}),x=0;x<E;x++){var P=k[x];o(P.offset),P.locator=r2(h,{})}n.locator=w,rN(k,n,C)&&d.push(k),n.locator=h}else rN(k,n,C)&&d.push(k);"http://www.w3.org/1999/xhtml"!==k.uri||k.closed?y++:y=r3(e,y,k.tagName,s,n)}}catch(L){r.error("element parse error: "+L),y=-1}f<y?f=y:a(Math.max(m,f)+1)}}(e,t,i,n,this.errorHandler),n.endDocument()}},r6.prototype={setTagName:function(e){if(!rA.test(e))throw Error("invalid tagName:"+e);this.tagName=e},add:function(e,t,i){if(!rA.test(e))throw Error("invalid attribute:"+e);this[this.length++]={qName:e,value:t,offset:i}},length:0,getLocalName:function(e){return this[e].localName},getLocator:function(e){return this[e].locator},getQName:function(e){return this[e].qName},getURI:function(e){return this[e].uri},getValue:function(e){return this[e].value}},rB({},rB.prototype) instanceof rB||(rB=function(e,t){function i(){}for(t in i.prototype=t,i=new i,e)i[t]=e[t];return i});var rj={XMLReader:rO};function rF(e,t){for(var i in e)t[i]=e[i]}function r7(e,t){var i=e.prototype;if(Object.create){var n=Object.create(t.prototype);i.__proto__=n}if(!(i instanceof t)){var r=function(){};r.prototype=t.prototype,rF(i,r=new r),e.prototype=i=r}i.constructor!=e&&(i.constructor=e)}var r5={},rH=r5.ELEMENT_NODE=1,rq=r5.ATTRIBUTE_NODE=2,rV=r5.TEXT_NODE=3,rW=r5.CDATA_SECTION_NODE=4,rz=r5.ENTITY_REFERENCE_NODE=5,rG=r5.ENTITY_NODE=6,rX=r5.PROCESSING_INSTRUCTION_NODE=7,rK=r5.COMMENT_NODE=8,rY=r5.DOCUMENT_NODE=9,r9=r5.DOCUMENT_TYPE_NODE=10,rQ=r5.DOCUMENT_FRAGMENT_NODE=11,rJ=r5.NOTATION_NODE=12,rZ={},se={},st=(rZ.INDEX_SIZE_ERR=(se[1]="Index size error",1),rZ.DOMSTRING_SIZE_ERR=(se[2]="DOMString size error",2),rZ.HIERARCHY_REQUEST_ERR=(se[3]="Hierarchy request error",3)),si=(rZ.WRONG_DOCUMENT_ERR=(se[4]="Wrong document",4),rZ.INVALID_CHARACTER_ERR=(se[5]="Invalid character",5),rZ.NO_DATA_ALLOWED_ERR=(se[6]="No data allowed",6),rZ.NO_MODIFICATION_ALLOWED_ERR=(se[7]="No modification allowed",7),rZ.NOT_FOUND_ERR=(se[8]="Not found",8)),sn=(rZ.NOT_SUPPORTED_ERR=(se[9]="Not supported",9),rZ.INUSE_ATTRIBUTE_ERR=(se[10]="Attribute in use",10));function sr(e,t){if(t instanceof Error)var i=t;else i=this,Error.call(this,se[e]),this.message=se[e],Error.captureStackTrace&&Error.captureStackTrace(this,sr);return i.code=e,t&&(this.message=this.message+": "+t),i}function ss(){}function sa(e,t){this._node=e,this._refresh=t,so(this)}function so(e){var t=e._node._inc||e._node.ownerDocument._inc;if(e._inc!=t){var i=e._refresh(e._node);s2(e,"length",i.length),rF(i,e),e._inc=t}}function su(){}function sl(e,t){for(var i=e.length;i--;)if(e[i]===t)return i}function sc(e,t,i,n){if(n?t[sl(t,n)]=i:t[t.length++]=i,e){var r,s,a,o=(i.ownerElement=e).ownerDocument;o&&(n&&sv(o,e,n),r=o,s=e,a=i,r&&r._inc++,"http://www.w3.org/2000/xmlns/"==a.namespaceURI&&(s._nsMap[a.prefix?a.localName:""]=a.value))}}function sh(e,t,i){var n=sl(t,i);if(!(0<=n))throw sr(si,Error(e.tagName+"@"+i));for(var r=t.length-1;n<r;)t[n]=t[++n];if(t.length=r,e){var s=e.ownerDocument;s&&(sv(s,e,i),i.ownerElement=null)}}function sd(e){if(this._features={},e)for(var t in e)this._features=e[t]}function sp(){}function sf(e){return("<"==e?"&lt;":">"==e&&"&gt;")||"&"==e&&"&amp;"||'"'==e&&"&quot;"||"&#"+e.charCodeAt()+";"}function sm(e,t){if(t(e))return!0;if(e=e.firstChild)do if(sm(e,t))return!0;while(e=e.nextSibling)}function sg(){}function sv(e,t,i){e&&e._inc++,"http://www.w3.org/2000/xmlns/"==i.namespaceURI&&delete t._nsMap[i.prefix?i.localName:""]}function sy(e,t,i){if(e&&e._inc){e._inc++;var n=t.childNodes;if(i)n[n.length++]=i;else{for(var r=t.firstChild,s=0;r;)r=(n[s++]=r).nextSibling;n.length=s}}}function s8(e,t){var i=t.previousSibling,n=t.nextSibling;return i?i.nextSibling=n:e.firstChild=n,n?n.previousSibling=i:e.lastChild=i,sy(e.ownerDocument,e),t}function s$(e,t,i){var n=t.parentNode;if(n&&n.removeChild(t),t.nodeType===rQ){var r=t.firstChild;if(null==r)return t;var s=t.lastChild}else r=s=t;var a=i?i.previousSibling:e.lastChild;for(r.previousSibling=a,s.nextSibling=i,a?a.nextSibling=r:e.firstChild=r,null==i?e.lastChild=s:i.previousSibling=s;r.parentNode=e,r!==s&&(r=r.nextSibling););return sy(e.ownerDocument||e,e),t.nodeType==rQ&&(t.firstChild=t.lastChild=null),t}function sb(){this._nsMap={}}function sT(){}function s_(){}function sS(){}function sk(){}function sC(){}function sE(){}function sw(){}function s0(){}function sx(){}function sP(){}function sL(){}function sI(){}function sD(e,t){var i=[],n=9==this.nodeType?this.documentElement:this,r=n.prefix,s=n.namespaceURI;if(s&&null==r&&null==(r=n.lookupPrefix(s)))var a=[{namespace:s,prefix:null}];return sO(this,i,e,t,a),i.join("")}function sA(e,t,i){var n=e.prefix||"",r=e.namespaceURI;if(!n&&!r||"xml"===n&&"http://www.w3.org/XML/1998/namespace"===r||"http://www.w3.org/2000/xmlns/"==r)return!1;for(var s=i.length;s--;){var a=i[s];if(a.prefix==n)return a.namespace!=r}return!0}function sO(e,t,i,n,r){if(n){if(!(e=n(e)))return;if("string"==typeof e)return void t.push(e)}switch(e.nodeType){case rH:r=r||[];var s,a=e.attributes,o=a.length,u=e.firstChild,l=e.tagName;i="http://www.w3.org/1999/xhtml"===e.namespaceURI||i,t.push("<",l);for(var c=0;c<o;c++)"xmlns"==(s=a.item(c)).prefix?r.push({prefix:s.localName,namespace:s.value}):"xmlns"==s.nodeName&&r.push({prefix:"",namespace:s.value});for(c=0;c<o;c++){if(sA(s=a.item(c),0,r)){var h=s.prefix||"",d=s.namespaceURI,p=h?" xmlns:"+h:" xmlns";t.push(p,'="',d,'"'),r.push({prefix:h,namespace:d})}sO(s,t,i,n,r)}if(sA(e,0,r)&&(h=e.prefix||"",d=e.namespaceURI,p=h?" xmlns:"+h:" xmlns",t.push(p,'="',d,'"'),r.push({prefix:h,namespace:d})),u||i&&!/^(?:meta|link|img|br|hr|input)$/i.test(l)){if(t.push(">"),i&&/^script$/i.test(l))for(;u;)u.data?t.push(u.data):sO(u,t,i,n,r),u=u.nextSibling;else for(;u;)sO(u,t,i,n,r),u=u.nextSibling;t.push("</",l,">")}else t.push("/>");return;case rY:case rQ:for(u=e.firstChild;u;)sO(u,t,i,n,r),u=u.nextSibling;return;case rq:return t.push(" ",e.name,'="',e.value.replace(/[<&"]/g,sf),'"');case rV:return t.push(e.data.replace(/[<&]/g,sf));case rW:return t.push("<![CDATA[",e.data,"]]>");case rK:return t.push("<!--",e.data,"-->");case r9:var f=e.publicId,m=e.systemId;if(t.push("<!DOCTYPE ",e.name),f)t.push(' PUBLIC "',f),m&&"."!=m&&t.push('" "',m),t.push('">');else if(m&&"."!=m)t.push(' SYSTEM "',m,'">');else{var g=e.internalSubset;g&&t.push(" [",g,"]"),t.push(">")}return;case rX:return t.push("<?",e.target," ",e.data,"?>");case rz:return t.push("&",e.nodeName,";");default:t.push("??",e.nodeName)}}function s2(e,t,i){e[t]=i}rZ.INVALID_STATE_ERR=(se[11]="Invalid state",11),rZ.SYNTAX_ERR=(se[12]="Syntax error",12),rZ.INVALID_MODIFICATION_ERR=(se[13]="Invalid modification",13),rZ.NAMESPACE_ERR=(se[14]="Invalid namespace",14),rZ.INVALID_ACCESS_ERR=(se[15]="Invalid access",15),sr.prototype=Error.prototype,rF(rZ,sr),ss.prototype={length:0,item:function(e){return this[e]||null},toString:function(e,t){for(var i=[],n=0;n<this.length;n++)sO(this[n],i,e,t);return i.join("")}},sa.prototype.item=function(e){return so(this),this[e]},r7(sa,ss),su.prototype={length:0,item:ss.prototype.item,getNamedItem:function(e){for(var t=this.length;t--;){var i=this[t];if(i.nodeName==e)return i}},setNamedItem:function(e){var t=e.ownerElement;if(t&&t!=this._ownerElement)throw new sr(sn);var i=this.getNamedItem(e.nodeName);return sc(this._ownerElement,this,e,i),i},setNamedItemNS:function(e){var t,i=e.ownerElement;if(i&&i!=this._ownerElement)throw new sr(sn);return t=this.getNamedItemNS(e.namespaceURI,e.localName),sc(this._ownerElement,this,e,t),t},removeNamedItem:function(e){var t=this.getNamedItem(e);return sh(this._ownerElement,this,t),t},removeNamedItemNS:function(e,t){var i=this.getNamedItemNS(e,t);return sh(this._ownerElement,this,i),i},getNamedItemNS:function(e,t){for(var i=this.length;i--;){var n=this[i];if(n.localName==t&&n.namespaceURI==e)return n}return null}},sd.prototype={hasFeature:function(e,t){var i=this._features[e.toLowerCase()];return!(!i||t&&!(t in i))},createDocument:function(e,t,i){var n=new sg;if(n.implementation=this,n.childNodes=new ss,(n.doctype=i)&&n.appendChild(i),t){var r=n.createElementNS(e,t);n.appendChild(r)}return n},createDocumentType:function(e,t,i){var n=new sE;return n.name=e,n.nodeName=e,n.publicId=t,n.systemId=i,n}},sp.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(e,t){return s$(this,e,t)},replaceChild:function(e,t){this.insertBefore(e,t),t&&this.removeChild(t)},removeChild:function(e){return s8(this,e)},appendChild:function(e){return this.insertBefore(e,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(e){return function e(t,i,n){var r=new i.constructor;for(var s in i){var a=i[s];"object"!=typeof a&&a!=r[s]&&(r[s]=a)}switch(i.childNodes&&(r.childNodes=new ss),r.ownerDocument=t,r.nodeType){case rH:var o=i.attributes,u=r.attributes=new su,l=o.length;u._ownerElement=r;for(var c=0;c<l;c++)r.setAttributeNode(e(t,o.item(c),!0));break;case rq:n=!0}if(n)for(var h=i.firstChild;h;)r.appendChild(e(t,h,n)),h=h.nextSibling;return r}(this.ownerDocument||this,this,e)},normalize:function(){for(var e=this.firstChild;e;){var t=e.nextSibling;t&&t.nodeType==rV&&e.nodeType==rV?(this.removeChild(t),e.appendData(t.data)):(e.normalize(),e=t)}},isSupported:function(e,t){return this.ownerDocument.implementation.hasFeature(e,t)},hasAttributes:function(){return 0<this.attributes.length},lookupPrefix:function(e){for(var t=this;t;){var i=t._nsMap;if(i){for(var n in i)if(i[n]==e)return n}t=t.nodeType==rq?t.ownerDocument:t.parentNode}return null},lookupNamespaceURI:function(e){for(var t=this;t;){var i=t._nsMap;if(i&&e in i)return i[e];t=t.nodeType==rq?t.ownerDocument:t.parentNode}return null},isDefaultNamespace:function(e){return null==this.lookupPrefix(e)}},rF(r5,sp),rF(r5,sp.prototype),sg.prototype={nodeName:"#document",nodeType:rY,doctype:null,documentElement:null,_inc:1,insertBefore:function(e,t){if(e.nodeType!=rQ)return null==this.documentElement&&e.nodeType==rH&&(this.documentElement=e),s$(this,e,t),e.ownerDocument=this,e;for(var i=e.firstChild;i;){var n=i.nextSibling;this.insertBefore(i,t),i=n}return e},removeChild:function(e){return this.documentElement==e&&(this.documentElement=null),s8(this,e)},importNode:function(e,t){return function e(t,i,n){var r;switch(i.nodeType){case rH:(r=i.cloneNode(!1)).ownerDocument=t;case rQ:break;case rq:n=!0}if((r=r||i.cloneNode(!1)).ownerDocument=t,r.parentNode=null,n)for(var s=i.firstChild;s;)r.appendChild(e(t,s,n)),s=s.nextSibling;return r}(this,e,t)},getElementById:function(e){var t=null;return sm(this.documentElement,function(i){if(i.nodeType==rH&&i.getAttribute("id")==e)return t=i,!0}),t},createElement:function(e){var t=new sb;return t.ownerDocument=this,t.nodeName=e,t.tagName=e,t.childNodes=new ss,(t.attributes=new su)._ownerElement=t},createDocumentFragment:function(){var e=new sP;return e.ownerDocument=this,e.childNodes=new ss,e},createTextNode:function(e){var t=new sS;return t.ownerDocument=this,t.appendData(e),t},createComment:function(e){var t=new sk;return t.ownerDocument=this,t.appendData(e),t},createCDATASection:function(e){var t=new sC;return t.ownerDocument=this,t.appendData(e),t},createProcessingInstruction:function(e,t){var i=new sL;return i.ownerDocument=this,i.tagName=i.target=e,i.nodeValue=i.data=t,i},createAttribute:function(e){var t=new sT;return t.ownerDocument=this,t.name=e,t.nodeName=e,t.localName=e,t.specified=!0,t},createEntityReference:function(e){var t=new sx;return t.ownerDocument=this,t.nodeName=e,t},createElementNS:function(e,t){var i=new sb,n=t.split(":"),r=i.attributes=new su;return i.childNodes=new ss,i.ownerDocument=this,i.nodeName=t,i.tagName=t,i.namespaceURI=e,2==n.length?(i.prefix=n[0],i.localName=n[1]):i.localName=t,r._ownerElement=i},createAttributeNS:function(e,t){var i=new sT,n=t.split(":");return i.ownerDocument=this,i.nodeName=t,i.name=t,i.namespaceURI=e,i.specified=!0,2==n.length?(i.prefix=n[0],i.localName=n[1]):i.localName=t,i}},r7(sg,sp),sg.prototype.getElementsByTagName=(sb.prototype={nodeType:rH,hasAttribute:function(e){return null!=this.getAttributeNode(e)},getAttribute:function(e){var t=this.getAttributeNode(e);return t&&t.value||""},getAttributeNode:function(e){return this.attributes.getNamedItem(e)},setAttribute:function(e,t){var i=this.ownerDocument.createAttribute(e);i.value=i.nodeValue=""+t,this.setAttributeNode(i)},removeAttribute:function(e){var t=this.getAttributeNode(e);t&&this.removeAttributeNode(t)},appendChild:function(e){return e.nodeType===rQ?this.insertBefore(e,null):function(e,t){var i=t.parentNode;if(i){var n=e.lastChild;i.removeChild(t),n=e.lastChild}return n=e.lastChild,t.parentNode=e,t.previousSibling=n,t.nextSibling=null,n?n.nextSibling=t:e.firstChild=t,e.lastChild=t,sy(e.ownerDocument,e,t),t}(this,e)},setAttributeNode:function(e){return this.attributes.setNamedItem(e)},setAttributeNodeNS:function(e){return this.attributes.setNamedItemNS(e)},removeAttributeNode:function(e){return this.attributes.removeNamedItem(e.nodeName)},removeAttributeNS:function(e,t){var i=this.getAttributeNodeNS(e,t);i&&this.removeAttributeNode(i)},hasAttributeNS:function(e,t){return null!=this.getAttributeNodeNS(e,t)},getAttributeNS:function(e,t){var i=this.getAttributeNodeNS(e,t);return i&&i.value||""},setAttributeNS:function(e,t,i){var n=this.ownerDocument.createAttributeNS(e,t);n.value=n.nodeValue=""+i,this.setAttributeNode(n)},getAttributeNodeNS:function(e,t){return this.attributes.getNamedItemNS(e,t)},getElementsByTagName:function(e){return new sa(this,function(t){var i=[];return sm(t,function(n){n===t||n.nodeType!=rH||"*"!==e&&n.tagName!=e||i.push(n)}),i})},getElementsByTagNameNS:function(e,t){return new sa(this,function(i){var n=[];return sm(i,function(r){r===i||r.nodeType!==rH||"*"!==e&&r.namespaceURI!==e||"*"!==t&&r.localName!=t||n.push(r)}),n})}}).getElementsByTagName,sg.prototype.getElementsByTagNameNS=sb.prototype.getElementsByTagNameNS,r7(sb,sp),sT.prototype.nodeType=rq,r7(sT,sp),s_.prototype={data:"",substringData:function(e,t){return this.data.substring(e,e+t)},appendData:function(e){e=this.data+e,this.nodeValue=this.data=e,this.length=e.length},insertData:function(e,t){this.replaceData(e,0,t)},appendChild:function(e){throw Error(se[st])},deleteData:function(e,t){this.replaceData(e,t,"")},replaceData:function(e,t,i){i=this.data.substring(0,e)+i+this.data.substring(e+t),this.nodeValue=this.data=i,this.length=i.length}},r7(s_,sp),sS.prototype={nodeName:"#text",nodeType:rV,splitText:function(e){var t=this.data,i=t.substring(e);t=t.substring(0,e),this.data=this.nodeValue=t,this.length=t.length;var n=this.ownerDocument.createTextNode(i);return this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling),n}},r7(sS,s_),sk.prototype={nodeName:"#comment",nodeType:rK},r7(sk,s_),sC.prototype={nodeName:"#cdata-section",nodeType:rW},r7(sC,s_),sE.prototype.nodeType=r9,r7(sE,sp),sw.prototype.nodeType=rJ,r7(sw,sp),s0.prototype.nodeType=rG,r7(s0,sp),sx.prototype.nodeType=rz,r7(sx,sp),sP.prototype.nodeName="#document-fragment",sP.prototype.nodeType=rQ,r7(sP,sp),sL.prototype.nodeType=rX,r7(sL,sp),sI.prototype.serializeToString=function(e,t,i){return sD.call(e,t,i)},sp.prototype.toString=sD;try{Object.defineProperty&&(Object.defineProperty(sa.prototype,"length",{get:function(){return so(this),this.$$length}}),Object.defineProperty(sp.prototype,"textContent",{get:function(){return function e(t){switch(t.nodeType){case rH:case rQ:var i=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&i.push(e(t)),t=t.nextSibling;return i.join("");default:return t.nodeValue}}(this)},set:function(e){switch(this.nodeType){case rH:case rQ:for(;this.firstChild;)this.removeChild(this.firstChild);(e||String(e))&&this.appendChild(this.ownerDocument.createTextNode(e));break;default:this.data=e,this.value=e,this.nodeValue=e}}}),s2=function(e,t,i){e["$$"+t]=i})}catch(sR){}function sN(e){return!!e&&"object"==typeof e}function s3(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return t.reduce(function(e,t){return Object.keys(t).forEach(function(i){Array.isArray(e[i])&&Array.isArray(t[i])?e[i]=e[i].concat(t[i]):sN(e[i])&&sN(t[i])?e[i]=s3(e[i],t[i]):e[i]=t[i]}),e},{})}function s4(e){return e.reduce(function(e,t){return e.concat(t)},[])}function sU(e){if(!e.length)return[];for(var t=[],i=0;i<e.length;i++)t.push(e[i]);return t}function s1(e){var t=e.baseUrl,i=e.source,n=void 0===i?"":i,r=e.range,s=void 0===r?"":r,a=e.indexRange,o=void 0===a?"":a,u={uri:n,resolvedUri:rx((void 0===t?"":t)||"",n)};if(s||o){var l=(s||o).split("-"),c=parseInt(l[0],10),h=parseInt(l[1],10);u.byterange={length:h-c+1,offset:c}}return u}function sM(e){var t,i=e.type,n=void 0===i?"static":i,r=e.duration,s=e.timescale,a=e.sourceDuration,o=av[n](e),u=(function(e,t){for(var i=[],n=e;n<t;n++)i.push(n);return i})(o.start,o.end).map((t=e,function(e,i){var n=t.duration,r=t.timescale,s=t.periodIndex,a=t.startNumber;return{number:(void 0===a?1:a)+e,duration:n/(void 0===r?1:r),timeline:s,time:i*n}}));if("static"===n){var l=u.length-1;u[l].duration=a-r/(void 0===s?1:s)*l}return u}function s6(e){var t=e.baseUrl,i=e.initialization,n=void 0===i?{}:i,r=e.sourceDuration,s=e.timescale,a=e.indexRange,o=e.duration;if(!t)throw Error(am);var u=s1({baseUrl:t,source:n.sourceURL,range:n.range}),l=s1({baseUrl:t,source:t,indexRange:void 0===a?"":a});if(l.map=u,o){var c=sM(e);c.length&&(l.duration=c[0].duration,l.timeline=c[0].timeline)}else r&&(l.duration=r/(void 0===s?1:s),l.timeline=0);return l.number=0,[l]}function sB(e,t,i){for(var n=e.sidx.map?e.sidx.map:null,r=e.sidx.duration,s=e.timeline||0,a=e.sidx.byterange,o=a.offset+a.length,u=t.timescale,l=t.references.filter(function(e){return 1!==e.referenceType}),c=[],h=o+t.firstOffset,d=0;d<l.length;d++){var p=t.references[d],f=p.referencedSize,m=s6({baseUrl:i,timescale:u,timeline:s,periodIndex:s,duration:p.subsegmentDuration,sourceDuration:r,indexRange:h+"-"+(h+f-1)})[0];n&&(m.map=n),c.push(m),h+=f}return e.segments=c,e}function sj(e){var t;return Object.keys(t=e.reduce(function(e,t){var i,n=t.attributes.id+(t.attributes.lang||"");return e[n]?(t.segments[0]&&(t.segments[0].discontinuity=!0),(i=e[n].segments).push.apply(i,t.segments),t.attributes.contentProtection&&(e[n].attributes.contentProtection=t.attributes.contentProtection)):e[n]=t,e},{})).map(function(e){return t[e]}).map(function(e){var t;return e.discontinuityStarts=(t=e.segments).reduce(function(e,t,i){return t.discontinuity&&e.push(i),e},[]),e})}function sF(e,t){if(void 0===t&&(t={}),!Object.keys(t).length)return e;for(var i in e){var n,r,s=e[i];if(s.sidx){var a=s.sidx.uri+"-"+(r=(n=s.sidx.byterange).offset+n.length-1,n.offset+"-"+r),o=t[a]&&t[a].sidx;s.sidx&&o&&sB(s,o,s.sidx.resolvedUri)}}return e}function s7(e){var t,i=e.attributes,n=e.segments,r=e.sidx,s={attributes:((t={NAME:i.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:i.width,height:i.height},CODECS:i.codecs,BANDWIDTH:i.bandwidth})["PROGRAM-ID"]=1,t),uri:"",endList:"static"===(i.type||"static"),timeline:i.periodIndex,resolvedUri:"",targetDuration:i.duration,segments:n,mediaSequence:n.length?n[0].number:1};return i.contentProtection&&(s.contentProtection=i.contentProtection),r&&(s.sidx=r),s}function s5(e,t){for(var i,n,r,s,a,o,u,l,c,h,d,p,f=e.type,m=void 0===f?"static":f,g=e.minimumUpdatePeriod,v=void 0===g?0:g,y=e.media,$=void 0===y?"":y,b=e.sourceDuration,T=e.timescale,_=void 0===T?1:T,S=e.startNumber,k=void 0===S?1:S,C=e.periodIndex,E=[],w=-1,x=0;x<t.length;x++){var P=t[x],L=P.d,I=P.r||0,D=P.t||0;w<0&&(w=D),D&&w<D&&(w=D);var A=void 0;if(I<0){var O=x+1;A=O===t.length?"dynamic"===m&&0<v&&0<$.indexOf("$Number$")?(n=w,r=L,s=(i=e).NOW,a=i.clientOffset,o=i.availabilityStartTime,l=void 0===(u=i.timescale)?1:u,h=void 0===(c=i.start)?0:c,Math.ceil(((p=(s+a)/1e3+(void 0===(d=i.minimumUpdatePeriod)?0:d)-(o+h))*l-n)/r)):(b*_-w)/L:(t[O].t-w)/L}else A=I+1;for(var R=k+E.length+A,N=k+E.length;N<R;)E.push({number:N,duration:L/_,time:w,timeline:C}),w+=L,N++}return E}function sH(e,t){var i;return e.replace(ay,(i=t,function(e,t,n,r){if("$$"===e)return"$";if(void 0===i[t])return e;var s=""+i[t];return"RepresentationID"===t?s:(r=n?parseInt(r,10):1)<=s.length?s:Array(r-s.length+1).join("0")+s}))}function sq(e,t){var i,n,r={RepresentationID:e.id,Bandwidth:e.bandwidth||0},s=e.initialization,a=void 0===s?{sourceURL:"",range:""}:s,o=s1({baseUrl:e.baseUrl,source:sH(a.sourceURL,r),range:a.range});return(i=e,n=t,i.duration||n?i.duration?sM(i):s5(i,n):[{number:i.startNumber||1,duration:i.sourceDuration,time:0,timeline:i.periodIndex}]).map(function(t){r.Number=t.number,r.Time=t.time;var i=sH(e.media||"",r);return{uri:i,timeline:t.timeline,duration:t.duration,resolvedUri:rx(e.baseUrl||"",i),map:o,number:t.number}})}function sV(e,t){var i=e.duration,n=e.segmentUrls;if(!i&&!t||i&&t)throw Error(ag);var r,s=(void 0===n?[]:n).map(function(t){var i,n,r,s,a,o,u;return i=e,n=t,r=i.baseUrl,o=s1({baseUrl:r,source:(a=void 0===(s=i.initialization)?{}:s).sourceURL,range:a.range}),(u=s1({baseUrl:r,source:n.media,range:n.mediaRange})).map=o,u});return i&&(r=sM(e)),t&&(r=s5(e,t)),r.map(function(e,t){if(s[t]){var i=s[t];return i.timeline=e.timeline,i.duration=e.duration,i.number=e.number,i}}).filter(function(e){return e})}function sW(e){var t,i,n=e.attributes,r=e.segmentInfo;r.template?(i=sq,t=s3(n,r.template)):r.base?(i=s6,t=s3(n,r.base)):r.list&&(i=sV,t=s3(n,r.list));var s={attributes:n};if(!i)return s;var a=i(t,r.timeline);if(t.duration){var o=t,u=o.duration,l=o.timescale;t.duration=u/(void 0===l?1:l)}else a.length?t.duration=a.reduce(function(e,t){return Math.max(e,Math.ceil(t.duration))},0):t.duration=0;return s.attributes=t,s.segments=a,r.base&&t.indexRange&&(s.sidx=a[0],s.segments=[]),s}function sz(e,t){return sU(e.childNodes).filter(function(e){return e.tagName===t})}function sG(e){return e.textContent.trim()}function sX(e){var t=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e);if(!t)return 0;var i=t.slice(1),n=i[0],r=i[1],s=i[2],a=i[3],o=i[4],u=i[5];return 31536e3*parseFloat(n||0)+2592e3*parseFloat(r||0)+86400*parseFloat(s||0)+3600*parseFloat(a||0)+60*parseFloat(o||0)+parseFloat(u||0)}function sK(e){return e&&e.attributes?sU(e.attributes).reduce(function(e,t){var i=a8[t.name]||a8.DEFAULT;return e[t.name]=i(t.value),e},{}):{}}function sY(e,t){return t.length?s4(e.map(function(e){return t.map(function(t){return rx(e,sG(t))})})):e}function s9(e){var t=sz(e,"SegmentTemplate")[0],i=sz(e,"SegmentList")[0],n=i&&sz(i,"SegmentURL").map(function(e){return s3({tag:"SegmentURL"},sK(e))}),r=sz(e,"SegmentBase")[0],s=i||t,a=s&&sz(s,"SegmentTimeline")[0],o=i||r||t,u=o&&sz(o,"Initialization")[0],l=t&&sK(t);l&&u?l.initialization=u&&sK(u):l&&l.initialization&&(l.initialization={sourceURL:l.initialization});var c={template:l,timeline:a&&sz(a,"S").map(function(e){return sK(e)}),list:i&&s3(sK(i),{segmentUrls:n,initialization:sK(u)}),base:r&&s3(sK(r),{initialization:sK(u)})};return Object.keys(c).forEach(function(e){c[e]||delete c[e]}),c}function sQ(e){if(""===e)throw Error(ap);var t=(new ah).parseFromString(e,"application/xml"),i=t&&"MPD"===t.documentElement.tagName?t.documentElement:null;if(!i||i&&0<i.getElementsByTagName("parsererror").length)throw Error(af);return i}function sJ(t,i){var n;return void 0===i&&(i={}),function e(t,i){if(void 0===i&&(i={}),!t.length)return{};var n,r,s,a,o,u,l,c=t[0].attributes,h=c.sourceDuration,d=c.type,p=c.suggestedPresentationDelay,f=c.minimumUpdatePeriod,m=sj(t.filter(function(e){var t=e.attributes;return"video/mp4"===t.mimeType||"video"===t.contentType})).map(s7),g=sj(t.filter(function(e){var t=e.attributes;return"audio/mp4"===t.mimeType||"audio"===t.contentType})),v=t.filter(function(e){var t=e.attributes;return"text/vtt"===t.mimeType||"text"===t.contentType}),y={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:((n={AUDIO:{},VIDEO:{}})["CLOSED-CAPTIONS"]={},n.SUBTITLES={},n),uri:"",duration:h,playlists:sF(m,i),minimumUpdatePeriod:1e3*(void 0===f?0:f)};return"dynamic"===(void 0===d?"static":d)&&(y.suggestedPresentationDelay=p),g.length&&(y.mediaGroups.AUDIO.audio=(r=g,void 0===(s=i)&&(s={}),o=r.reduce(function(e,t){var i,n,r,o,u,l,c=t.attributes.role&&t.attributes.role.value||"",h=t.attributes.lang||"",d="main";return h&&(d=t.attributes.lang+(c?" ("+c+")":"")),e[d]&&e[d].playlists[0].attributes.BANDWIDTH>t.attributes.bandwidth||(e[d]={language:h,autoselect:!0,default:"main"===c,playlists:sF([(r=(i=t).attributes,o=i.segments,u=i.sidx,l={attributes:((n={NAME:r.id,BANDWIDTH:r.bandwidth,CODECS:r.codecs})["PROGRAM-ID"]=1,n),uri:"",endList:"static"===(r.type||"static"),timeline:r.periodIndex,resolvedUri:"",targetDuration:r.duration,segments:o,mediaSequence:o.length?o[0].number:1},r.contentProtection&&(l.contentProtection=r.contentProtection),u&&(l.sidx=u),l)],s),uri:""},void 0===a&&"main"===c&&((a=t).default=!0)),e},{}),a||(o[Object.keys(o)[0]].default=!0),o)),v.length&&(y.mediaGroups.SUBTITLES.subs=(u=v,void 0===(l=i)&&(l={}),u.reduce(function(e,t){var i,n,r,s,a=t.attributes.lang||"text";return e[a]||(e[a]={language:a,default:!1,autoselect:!1,playlists:sF([(r=(i=t).attributes,void 0===(s=i.segments)&&(s=[{uri:r.baseUrl,timeline:r.periodIndex,resolvedUri:r.baseUrl||"",duration:r.sourceDuration,number:0}],r.duration=r.sourceDuration),{attributes:((n={NAME:r.id,BANDWIDTH:r.bandwidth})["PROGRAM-ID"]=1,n),uri:"",endList:"static"===(r.type||"static"),timeline:r.periodIndex,resolvedUri:r.baseUrl||"",targetDuration:r.duration,segments:s,mediaSequence:s.length?s[0].number:1})],l),uri:""}),e},{}))),y}((n=function t(i,n){void 0===n&&(n={});var r=n,s=r.manifestUri,a=r.NOW,o=void 0===a?Date.now():a,u=r.clientOffset,l=sz(i,"Period");if(!l.length)throw Error(ad);var c,h,d=sK(i),p=sY([void 0===s?"":s],sz(i,"BaseURL"));return d.sourceDuration=d.mediaPresentationDuration||0,d.NOW=o,d.clientOffset=void 0===u?0:u,s4(l.map((c=d,h=p,function(t,i){var n,r,s,a=sY(h,sz(t,"BaseURL")),o=sK(t),u=parseInt(o.id,10),l=e.isNaN(u)?i:u,d=s3(c,{periodIndex:l}),p=sz(t,"AdaptationSet"),f=s9(t);return s4(p.map((n=d,r=a,s=f,function(e){var t,i=sK(e),a=sY(r,sz(e,"BaseURL")),o=sz(e,"Role")[0],u={role:sK(o)},l=s3(n,i,u),c=(t=sz(e,"ContentProtection")).reduce(function(e,t){var i=sK(t),n=a$[i.schemeIdUri];if(n){e[n]={attributes:i};var r=sz(t,"cenc:pssh")[0];if(r){var s=sG(r),a=s&&rL(s);e[n].pssh=a}}return e},{});Object.keys(c).length&&(l=s3(l,{contentProtection:c}));var h,d,p,f=s9(e),m=sz(e,"Representation"),g=s3(s,f);return s4(m.map((h=l,d=a,p=g,function(e){var t=sz(e,"BaseURL"),i=sY(d,t),n=s3(h,sK(e)),r=s9(e);return i.map(function(e){return{segmentInfo:s3(p,r),attributes:s3(n,{baseUrl:e})}})})))})))})))}(sQ(t),i)).map(sW),i.sidxMapping)}function sZ(e){return new Date(1e3*e-20828448e5)}function ae(e){return{isLeading:(12&e[0])>>>2,dependsOn:3&e[0],isDependedOn:(192&e[1])>>>6,hasRedundancy:(48&e[1])>>>4,paddingValue:(14&e[1])>>>1,isNonSyncSample:1&e[1],degradationPriority:e[2]<<8|e[3]}}function at(e){var t="";return t+=String.fromCharCode(e[0]),t+=String.fromCharCode(e[1]),t+=String.fromCharCode(e[2]),t+=String.fromCharCode(e[3])}var ai,an,ar,as,aa,ao,au,al={DOMImplementation:sd,XMLSerializer:sI},ac=a(function(e,t){function i(e){this.options=e||{locator:{}}}function n(){this.cdata=!1}function r(e,t){t.lineNumber=e.lineNumber,t.columnNumber=e.columnNumber}function s(e,t,i){return"string"==typeof e?e.substr(t,i):e.length>=t+i||t?new java.lang.String(e,t,i)+"":e}function a(e,t){e.currentElement?e.currentElement.appendChild(t):e.doc.appendChild(t)}i.prototype.parseFromString=function(e,t){var i=this.options,r=new o,s=i.domBuilder||new n,a=i.errorHandler,u=i.locator,l=i.xmlns||{},c={lt:"<",gt:">",amp:"&",quot:'"',apos:"'"};return u&&s.setDocumentLocator(u),r.errorHandler=function(e,t,i){if(!e){if(t instanceof n)return t;e=t}var r={},s=e instanceof Function;function a(t){var n=e[t];!n&&s&&(n=2==e.length?function(i){e(t,i)}:e),r[t]=n&&function(e){n("[xmldom "+t+"]	"+e+function e(t){if(t)return"\n@"+(t.systemId||"")+"#[line:"+t.lineNumber+",col:"+t.columnNumber+"]"}(i))}||function(){}}return i=i||{},a("warning"),a("error"),a("fatalError"),r}(a,s,u),r.domBuilder=i.domBuilder||s,/\/x?html?$/.test(t)&&(c.nbsp=" ",c.copy="\xa9",l[""]="http://www.w3.org/1999/xhtml"),l.xml=l.xml||"http://www.w3.org/XML/1998/namespace",e?r.parse(e,l,c):r.errorHandler.error("invalid doc source"),s.doc},n.prototype={startDocument:function(){this.doc=(new u).createDocument(null,null,null),this.locator&&(this.doc.documentURI=this.locator.systemId)},startElement:function(e,t,i,n){var s=this.doc,o=s.createElementNS(e,i||t),u=n.length;a(this,o),this.currentElement=o,this.locator&&r(this.locator,o);for(var l=0;l<u;l++){e=n.getURI(l);var c=n.getValue(l),h=(i=n.getQName(l),s.createAttributeNS(e,i));this.locator&&r(n.getLocator(l),h),h.value=h.nodeValue=c,o.setAttributeNode(h)}},endElement:function(e,t,i){var n=this.currentElement;n.tagName,this.currentElement=n.parentNode},startPrefixMapping:function(e,t){},endPrefixMapping:function(e){},processingInstruction:function(e,t){var i=this.doc.createProcessingInstruction(e,t);this.locator&&r(this.locator,i),a(this,i)},ignorableWhitespace:function(e,t,i){},characters:function(e,t,i){if(e=s.apply(this,arguments)){if(this.cdata)var n=this.doc.createCDATASection(e);else n=this.doc.createTextNode(e);this.currentElement?this.currentElement.appendChild(n):/^\s*$/.test(e)&&this.doc.appendChild(n),this.locator&&r(this.locator,n)}},skippedEntity:function(e){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(e){(this.locator=e)&&(e.lineNumber=0)},comment:function(e,t,i){e=s.apply(this,arguments);var n=this.doc.createComment(e);this.locator&&r(this.locator,n),a(this,n)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(e,t,i){var n=this.doc.implementation;if(n&&n.createDocumentType){var s=n.createDocumentType(e,t,i);this.locator&&r(this.locator,s),a(this,s)}},warning:function(e){},error:function(e){},fatalError:function(e){throw e}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(e){n.prototype[e]=function(){return null}});var o=rj.XMLReader,u=t.DOMImplementation=al.DOMImplementation;t.XMLSerializer=al.XMLSerializer,t.DOMParser=i}),ah=(ac.DOMImplementation,ac.XMLSerializer,ac.DOMParser),ad="INVALID_NUMBER_OF_PERIOD",ap="DASH_EMPTY_MANIFEST",af="DASH_INVALID_XML",am="NO_BASE_URL",ag="SEGMENT_TIME_UNSPECIFIED",av={static:function(e){var t=e.duration,i=e.timescale;return{start:0,end:Math.ceil(e.sourceDuration/(t/(void 0===i?1:i)))}},dynamic:function(e){var t=e.NOW,i=e.clientOffset,n=e.availabilityStartTime,r=e.timescale,s=void 0===r?1:r,a=e.duration,o=e.start,u=e.minimumUpdatePeriod,l=e.timeShiftBufferDepth,c=(t+i)/1e3,h=n+(void 0===o?0:o);return{start:Math.max(0,Math.floor((c-h-(void 0===l?1/0:l))*s/a)),end:Math.min(Math.ceil((c+(void 0===u?0:u)-h)*s/a),Math.floor((c-h)*s/a))}}},ay=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,a8={mediaPresentationDuration:function(e){return sX(e)},availabilityStartTime:function(e){var t;return t=e,/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(t)&&(t+="Z"),Date.parse(t)/1e3},minimumUpdatePeriod:function(e){return sX(e)},suggestedPresentationDelay:function(e){return sX(e)},type:function(e){return e},timeShiftBufferDepth:function(e){return sX(e)},start:function(e){return sX(e)},width:function(e){return parseInt(e,10)},height:function(e){return parseInt(e,10)},bandwidth:function(e){return parseInt(e,10)},startNumber:function(e){return parseInt(e,10)},timescale:function(e){return parseInt(e,10)},duration:function(e){var t=parseInt(e,10);return isNaN(t)?sX(e):t},d:function(e){return parseInt(e,10)},t:function(e){return parseInt(e,10)},r:function(e){return parseInt(e,10)},DEFAULT:function(e){return e}},a$={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime"},ab=function(e){return e>>>0},aT=function(e){return("00"+e.toString(16)).slice(-2)},a_=ab,aS={avc1:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{dataReferenceIndex:t.getUint16(6),width:t.getUint16(24),height:t.getUint16(26),horizresolution:t.getUint16(28)+t.getUint16(30)/16,vertresolution:t.getUint16(32)+t.getUint16(34)/16,frameCount:t.getUint16(40),depth:t.getUint16(74),config:ai(e.subarray(78,e.byteLength))}},avcC:function(e){var t,i,n,r,s=new DataView(e.buffer,e.byteOffset,e.byteLength),a={configurationVersion:e[0],avcProfileIndication:e[1],profileCompatibility:e[2],avcLevelIndication:e[3],lengthSizeMinusOne:3&e[4],sps:[],pps:[]},o=31&e[5];for(n=6,r=0;r<o;r++)i=s.getUint16(n),n+=2,a.sps.push(new Uint8Array(e.subarray(n,n+i))),n+=i;for(t=e[n],n++,r=0;r<t;r++)i=s.getUint16(n),n+=2,a.pps.push(new Uint8Array(e.subarray(n,n+i))),n+=i;return a},btrt:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{bufferSizeDB:t.getUint32(0),maxBitrate:t.getUint32(4),avgBitrate:t.getUint32(8)}},esds:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),esId:e[6]<<8|e[7],streamPriority:31&e[8],decoderConfig:{objectProfileIndication:e[11],streamType:e[12]>>>2&63,bufferSize:e[13]<<16|e[14]<<8|e[15],maxBitrate:e[16]<<24|e[17]<<16|e[18]<<8|e[19],avgBitrate:e[20]<<24|e[21]<<16|e[22]<<8|e[23],decoderConfigDescriptor:{tag:e[24],length:e[25],audioObjectType:e[26]>>>3&31,samplingFrequencyIndex:(7&e[26])<<1|e[27]>>>7&1,channelConfiguration:e[27]>>>3&15}}}},ftyp:function(e){for(var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i={majorBrand:at(e.subarray(0,4)),minorVersion:t.getUint32(4),compatibleBrands:[]},n=8;n<e.byteLength;)i.compatibleBrands.push(at(e.subarray(n,n+4))),n+=4;return i},dinf:function(e){return{boxes:ai(e)}},dref:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),dataReferences:ai(e.subarray(8))}},hdlr:function(e){var t={version:new DataView(e.buffer,e.byteOffset,e.byteLength).getUint8(0),flags:new Uint8Array(e.subarray(1,4)),handlerType:at(e.subarray(8,12)),name:""},i=8;for(i=24;i<e.byteLength;i++){if(0===e[i]){i++;break}t.name+=String.fromCharCode(e[i])}return t.name=decodeURIComponent(escape(t.name)),t},mdat:function(e){return{byteLength:e.byteLength,nals:function(e){var t,i,n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=[];for(t=0;t+4<e.length;t+=i)if(i=n.getUint32(t),t+=4,i<=0)r.push("<span style='color:red;'>MALFORMED DATA</span>");else switch(31&e[t]){case 1:r.push("slice_layer_without_partitioning_rbsp");break;case 5:r.push("slice_layer_without_partitioning_rbsp_idr");break;case 6:r.push("sei_rbsp");break;case 7:r.push("seq_parameter_set_rbsp");break;case 8:r.push("pic_parameter_set_rbsp");break;case 9:r.push("access_unit_delimiter_rbsp");break;default:r.push("UNKNOWN NAL - "+e[t]&31)}return r}(e)}},mdhd:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n=4,r={version:i.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),language:""};return 1===r.version?(n+=4,r.creationTime=sZ(i.getUint32(n)),n+=8,r.modificationTime=sZ(i.getUint32(n)),n+=4,r.timescale=i.getUint32(n),n+=8):(r.creationTime=sZ(i.getUint32(n)),n+=4,r.modificationTime=sZ(i.getUint32(n)),n+=4,r.timescale=i.getUint32(n),n+=4),r.duration=i.getUint32(n),n+=4,t=i.getUint16(n),r.language+=String.fromCharCode(96+(t>>10)),r.language+=String.fromCharCode(96+((992&t)>>5)),r.language+=String.fromCharCode(96+(31&t)),r},mdia:function(e){return{boxes:ai(e)}},mfhd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),sequenceNumber:e[4]<<24|e[5]<<16|e[6]<<8|e[7]}},minf:function(e){return{boxes:ai(e)}},mp4a:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i={dataReferenceIndex:t.getUint16(6),channelcount:t.getUint16(16),samplesize:t.getUint16(18),samplerate:t.getUint16(24)+t.getUint16(26)/65536};return 28<e.byteLength&&(i.streamDescriptor=ai(e.subarray(28))[0]),i},moof:function(e){return{boxes:ai(e)}},moov:function(e){return{boxes:ai(e)}},mvex:function(e){return{boxes:ai(e)}},mvhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i=4,n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4))};return 1===n.version?(i+=4,n.creationTime=sZ(t.getUint32(i)),i+=8,n.modificationTime=sZ(t.getUint32(i)),i+=4,n.timescale=t.getUint32(i),i+=8):(n.creationTime=sZ(t.getUint32(i)),i+=4,n.modificationTime=sZ(t.getUint32(i)),i+=4,n.timescale=t.getUint32(i),i+=4),n.duration=t.getUint32(i),i+=4,n.rate=t.getUint16(i)+t.getUint16(i+2)/16,i+=4,n.volume=t.getUint8(i)+t.getUint8(i+1)/8,i+=2,i+=2,i+=8,n.matrix=new Uint32Array(e.subarray(i,i+36)),i+=36,i+=24,n.nextTrackId=t.getUint32(i),n},pdin:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4)),rate:t.getUint32(4),initialDelay:t.getUint32(8)}},sdtp:function(e){var t,i={version:e[0],flags:new Uint8Array(e.subarray(1,4)),samples:[]};for(t=4;t<e.byteLength;t++)i.samples.push({dependsOn:(48&e[t])>>4,isDependedOn:(12&e[t])>>2,hasRedundancy:3&e[t]});return i},sidx:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),references:[],referenceId:i.getUint32(4),timescale:i.getUint32(8),earliestPresentationTime:i.getUint32(12),firstOffset:i.getUint32(16)},r=i.getUint16(22);for(t=24;r;t+=12,r--)n.references.push({referenceType:(128&e[t])>>>7,referencedSize:2147483647&i.getUint32(t),subsegmentDuration:i.getUint32(t+4),startsWithSap:!!(128&e[t+8]),sapType:(112&e[t+8])>>>4,sapDeltaTime:268435455&i.getUint32(t+8)});return n},smhd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),balance:e[4]+e[5]/256}},stbl:function(e){return{boxes:ai(e)}},stco:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),chunkOffsets:[]},r=i.getUint32(4);for(t=8;r;t+=4,r--)n.chunkOffsets.push(i.getUint32(t));return n},stsc:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n=i.getUint32(4),r={version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleToChunks:[]};for(t=8;n;t+=12,n--)r.sampleToChunks.push({firstChunk:i.getUint32(t),samplesPerChunk:i.getUint32(t+4),sampleDescriptionIndex:i.getUint32(t+8)});return r},stsd:function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleDescriptions:ai(e.subarray(8))}},stsz:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),sampleSize:i.getUint32(4),entries:[]};for(t=12;t<e.byteLength;t+=4)n.entries.push(i.getUint32(t));return n},stts:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),timeToSamples:[]},r=i.getUint32(4);for(t=8;r;t+=8,r--)n.timeToSamples.push({sampleCount:i.getUint32(t),sampleDelta:i.getUint32(t+4)});return n},styp:function(e){return aS.ftyp(e)},tfdt:function(e){var t={version:e[0],flags:new Uint8Array(e.subarray(1,4)),baseMediaDecodeTime:a_(e[4]<<24|e[5]<<16|e[6]<<8|e[7])};return 1===t.version&&(t.baseMediaDecodeTime*=4294967296,t.baseMediaDecodeTime+=a_(e[8]<<24|e[9]<<16|e[10]<<8|e[11])),t},tfhd:function(e){var t,i=new DataView(e.buffer,e.byteOffset,e.byteLength),n={version:e[0],flags:new Uint8Array(e.subarray(1,4)),trackId:i.getUint32(4)},r=1&n.flags[2],s=2&n.flags[2],a=8&n.flags[2],o=16&n.flags[2],u=32&n.flags[2],l=65536&n.flags[0],c=131072&n.flags[0];return t=8,r&&(t+=4,n.baseDataOffset=i.getUint32(12),t+=4),s&&(n.sampleDescriptionIndex=i.getUint32(t),t+=4),a&&(n.defaultSampleDuration=i.getUint32(t),t+=4),o&&(n.defaultSampleSize=i.getUint32(t),t+=4),u&&(n.defaultSampleFlags=i.getUint32(t)),l&&(n.durationIsEmpty=!0),!r&&c&&(n.baseDataOffsetIsMoof=!0),n},tkhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength),i=4,n={version:t.getUint8(0),flags:new Uint8Array(e.subarray(1,4))};return 1===n.version?(i+=4,n.creationTime=sZ(t.getUint32(i)),i+=8,n.modificationTime=sZ(t.getUint32(i)),i+=4,n.trackId=t.getUint32(i),i+=4,i+=8):(n.creationTime=sZ(t.getUint32(i)),i+=4,n.modificationTime=sZ(t.getUint32(i)),i+=4,n.trackId=t.getUint32(i),i+=4,i+=4),n.duration=t.getUint32(i),i+=4,i+=8,n.layer=t.getUint16(i),i+=2,n.alternateGroup=t.getUint16(i),i+=2,n.volume=t.getUint8(i)+t.getUint8(i+1)/8,i+=2,i+=2,n.matrix=new Uint32Array(e.subarray(i,i+36)),i+=36,n.width=t.getUint16(i)+t.getUint16(i+2)/16,i+=4,n.height=t.getUint16(i)+t.getUint16(i+2)/16,n},traf:function(e){return{boxes:ai(e)}},trak:function(e){return{boxes:ai(e)}},trex:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),trackId:t.getUint32(4),defaultSampleDescriptionIndex:t.getUint32(8),defaultSampleDuration:t.getUint32(12),defaultSampleSize:t.getUint32(16),sampleDependsOn:3&e[20],sampleIsDependedOn:(192&e[21])>>6,sampleHasRedundancy:(48&e[21])>>4,samplePaddingValue:(14&e[21])>>1,sampleIsDifferenceSample:!!(1&e[21]),sampleDegradationPriority:t.getUint16(22)}},trun:function(e){var t,i={version:e[0],flags:new Uint8Array(e.subarray(1,4)),samples:[]},n=new DataView(e.buffer,e.byteOffset,e.byteLength),r=1&i.flags[2],s=4&i.flags[2],a=1&i.flags[1],o=2&i.flags[1],u=4&i.flags[1],l=8&i.flags[1],c=n.getUint32(4),h=8;for(r&&(i.dataOffset=n.getInt32(h),h+=4),s&&c&&(t={flags:ae(e.subarray(h,h+4))},h+=4,a&&(t.duration=n.getUint32(h),h+=4),o&&(t.size=n.getUint32(h),h+=4),l&&(t.compositionTimeOffset=n.getUint32(h),h+=4),i.samples.push(t),c--);c--;)t={},a&&(t.duration=n.getUint32(h),h+=4),o&&(t.size=n.getUint32(h),h+=4),u&&(t.flags=ae(e.subarray(h,h+4)),h+=4),l&&(t.compositionTimeOffset=n.getUint32(h),h+=4),i.samples.push(t);return i},"url ":function(e){return{version:e[0],flags:new Uint8Array(e.subarray(1,4))}},vmhd:function(e){var t=new DataView(e.buffer,e.byteOffset,e.byteLength);return{version:e[0],flags:new Uint8Array(e.subarray(1,4)),graphicsmode:t.getUint16(4),opcolor:new Uint16Array([t.getUint16(6),t.getUint16(8),t.getUint16(10)])}}},ak={inspect:ai=function(e){for(var t,i,n,r,s,a=0,o=[],u=new ArrayBuffer(e.length),l=new Uint8Array(u),c=0;c<e.length;++c)l[c]=e[c];for(t=new DataView(u);a<e.byteLength;)i=t.getUint32(a),n=at(e.subarray(a+4,a+8)),r=1<i?a+i:e.byteLength,(s=(aS[n]||function(e){return{data:e}})(e.subarray(a+8,r))).size=i,s.type=n,o.push(s),a=r;return o},textify:an=function(e,t){var i;return i=Array(2*(t=t||0)+1).join(" "),e.map(function(e,n){return i+e.type+"\n"+Object.keys(e).filter(function(e){return"type"!==e&&"boxes"!==e}).map(function(t){var n=i+"  "+t+": ",r=e[t];if(r instanceof Uint8Array||r instanceof Uint32Array){var s=Array.prototype.slice.call(new Uint8Array(r.buffer,r.byteOffset,r.byteLength)).map(function(e){return" "+("00"+e.toString(16)).slice(-2)}).join("").match(/.{1,24}/g);return s?1===s.length?n+"<"+s.join("").slice(1)+">":n+"<\n"+s.map(function(e){return i+"  "+e}).join("\n")+"\n"+i+"  >":n+"<>"}return n+JSON.stringify(r,null,2).split("\n").map(function(e,t){return 0===t?e:i+"  "+e}).join("\n")}).join("\n")+(e.boxes?"\n"+an(e.boxes,t+1):"")}).join("\n")},parseType:at,findBox:function e(t,i){var n,r,s,a,o,u=[];if(!i.length)return null;for(n=0;n<t.byteLength;)r=a_(t[n]<<24|t[n+1]<<16|t[n+2]<<8|t[n+3]),s=at(t.subarray(n+4,n+8)),a=1<r?n+r:t.byteLength,s===i[0]&&(1===i.length?u.push(t.subarray(n+8,a)):(o=e(t.subarray(n+8,a),i.slice(1))).length&&(u=u.concat(o))),n=a;return u},parseTraf:aS.traf,parseTfdt:aS.tfdt,parseHdlr:aS.hdlr,parseTfhd:aS.tfhd,parseTrun:aS.trun,parseSidx:aS.sidx},aC=ab,aE=aT;function aw(){this.init=function(){var e={};this.on=function(t,i){e[t]||(e[t]=[]),e[t]=e[t].concat(i)},this.off=function(t,i){var n;return!!e[t]&&(n=e[t].indexOf(i),e[t]=e[t].slice(),e[t].splice(n,1),-1<n)},this.trigger=function(t){var i,n,r,s;if(i=e[t]){if(2===arguments.length)for(r=i.length,n=0;n<r;++n)i[n].call(this,arguments[1]);else{for(s=[],n=arguments.length,n=1;n<arguments.length;++n)s.push(arguments[n]);for(r=i.length,n=0;n<r;++n)i[n].apply(this,s)}}},this.dispose=function(){e={}}}}ar=function(e){return ak.findBox(e,["moov","trak"]).reduce(function(e,t){var i,n,r,s,a;return(i=ak.findBox(t,["tkhd"])[0])?(s=aC(i[r=0===(n=i[0])?12:20]<<24|i[r+1]<<16|i[r+2]<<8|i[r+3]),(a=ak.findBox(t,["mdia","mdhd"])[0])?(r=0===(n=a[0])?12:20,e[s]=aC(a[r]<<24|a[r+1]<<16|a[r+2]<<8|a[r+3]),e):null):null},{})},as=function(e,t){var i,n,r;return i=ak.findBox(t,["moof","traf"]),n=[].concat.apply([],i.map(function(t){return ak.findBox(t,["tfhd"]).map(function(i){var n,r;return r=e[n=aC(i[4]<<24|i[5]<<16|i[6]<<8|i[7])]||9e4,(ak.findBox(t,["tfdt"]).map(function(e){var t,i;return t=e[0],i=aC(e[4]<<24|e[5]<<16|e[6]<<8|e[7]),1===t&&(i*=4294967296,i+=aC(e[8]<<24|e[9]<<16|e[10]<<8|e[11])),i})[0]||1/0)/r})})),isFinite(r=Math.min.apply(null,n))?r:0},aa=function(e,t){var i,n=ak.findBox(t,["moof","traf"]),r=0,s=0;if(n&&n.length)for(var a=ak.parseTraf(n[0]),o=0;o<a.boxes.length;o++)"tfhd"===a.boxes[o].type?i=a.boxes[o].trackId:"tfdt"===a.boxes[o].type?r=a.boxes[o].baseMediaDecodeTime:"trun"===a.boxes[o].type&&a.boxes[o].samples.length&&(s=a.boxes[o].samples[0].compositionTimeOffset||0);return(r+s)/(e[i]||9e4)},ao=function(e){var t=ak.findBox(e,["moov","trak"]),i=[];return t.forEach(function(e){var t=ak.findBox(e,["mdia","hdlr"]),n=ak.findBox(e,["tkhd"]);t.forEach(function(e,t){var r,s,a=ak.parseType(e.subarray(8,12)),o=n[t];"vide"===a&&(s=0===(r=new DataView(o.buffer,o.byteOffset,o.byteLength)).getUint8(0)?r.getUint32(12):r.getUint32(20),i.push(s))})}),i},au=function(e){var t=ak.findBox(e,["moov","trak"]),i=[];return t.forEach(function(e){var t,n,r={},s=ak.findBox(e,["tkhd"])[0];s&&(n=(t=new DataView(s.buffer,s.byteOffset,s.byteLength)).getUint8(0),r.id=0===n?t.getUint32(12):t.getUint32(20));var a=ak.findBox(e,["mdia","hdlr"])[0];if(a){var o=ak.parseType(a.subarray(8,12));r.type="vide"===o?"video":"soun"===o?"audio":o}var u=ak.findBox(e,["mdia","minf","stbl","stsd"])[0];if(u){var l=u.subarray(8);r.codec=ak.parseType(l.subarray(4,8));var c,h=ak.findBox(l,[r.codec])[0];h&&(/^[a-z]vc[1-9]$/i.test(r.codec)?(c=h.subarray(78),"avcC"===ak.parseType(c.subarray(4,8))&&11<c.length?(r.codec+=".",r.codec+=aE(c[9]),r.codec+=aE(c[10]),r.codec+=aE(c[11])):r.codec="avc1.4d400d"):/^mp4[a,v]$/i.test(r.codec)&&(c=h.subarray(28),"esds"===ak.parseType(c.subarray(4,8))&&20<c.length&&0!==c[19]?(r.codec+="."+aE(c[19]),r.codec+="."+aE(c[20]>>>2&63).replace(/^0/,"")):r.codec="mp4a.40.2"))}var d=ak.findBox(e,["mdia","mdhd"])[0];if(d&&s){var p=0===n?12:20;r.timescale=aC(d[p]<<24|d[1+p]<<16|d[2+p]<<8|d[3+p])}i.push(r)}),i};var a0={findBox:ak.findBox,parseType:ak.parseType,timescale:ar,startTime:as,compositionStartTime:aa,videoTrackIds:ao,tracks:au},ax=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},n=0,r=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)n+=255,t++;for(n+=e[t++];255===e[t];)r+=255,t++;if(r+=e[t++],!i.payload&&4===n){i.payloadType=n,i.payloadSize=r,i.payload=e.subarray(t,t+r);break}t+=r,r=n=0}return i},aP=function(e,t){var i,n,r,s,a=[];if(!(64&t[0]))return a;for(n=31&t[0],i=0;i<n;i++)s={type:3&t[2+(r=3*i)],pts:e},4&t[2+r]&&(s.ccData=t[3+r]<<8|t[4+r],a.push(s));return a},aL=function(e){for(var t,i,n=e.byteLength,r=[],s=1;s<n-2;)0===e[s]&&0===e[s+1]&&3===e[s+2]?(r.push(s+2),s+=2):s++;if(0===r.length)return e;t=n-r.length,i=new Uint8Array(t);var a=0;for(s=0;s<t;a++,s++)a===r[0]&&(a++,r.shift()),i[s]=e[a];return i};function aI(){aI.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new aN(0,0),new aN(0,1),new aN(1,0),new aN(1,1)],this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("partialdone",this.trigger.bind(this,"partialdone")),e.on("done",this.trigger.bind(this,"done"))},this)}aw.prototype.pipe=function(e){return this.on("data",function(t){e.push(t)}),this.on("done",function(t){e.flush(t)}),this.on("partialdone",function(t){e.partialFlush(t)}),this.on("endedtimeline",function(t){e.endTimeline(t)}),this.on("reset",function(t){e.reset(t)}),e},aw.prototype.push=function(e){this.trigger("data",e)},aw.prototype.flush=function(e){this.trigger("done",e)},aw.prototype.partialFlush=function(e){this.trigger("partialdone",e)},aw.prototype.endTimeline=function(e){this.trigger("endedtimeline",e)},aw.prototype.reset=function(e){this.trigger("reset",e)};var aD=aw;function aA(e){return null===e?"":String.fromCharCode(e=a2[e]||e)}function aO(){for(var e=[],t=15;t--;)e.push("");return e}(aI.prototype=new aD).push=function(e){var t,i,n,r;if("sei_rbsp"===e.nalUnitType&&4===(t=ax(e.escapedRBSP)).payloadType&&(i=181!==(r=t).payload[0]?null:49!=(r.payload[1]<<8|r.payload[2])?null:"GA94"!==String.fromCharCode(r.payload[3],r.payload[4],r.payload[5],r.payload[6])?null:3!==r.payload[7]?null:r.payload.subarray(8,r.payload.length-1))){if(e.dts<this.latestDts_)this.ignoreNextEqualDts_=!0;else{if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return this.numSameDts_--,void(this.numSameDts_||(this.ignoreNextEqualDts_=!1));n=aP(e.pts,i),this.captionPackets_=this.captionPackets_.concat(n),this.latestDts_!==e.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=e.dts}}},aI.prototype.flushCCStreams=function(e){this.ccStreams_.forEach(function(t){return"flush"===e?t.flush():t.partialFlush()},this)},aI.prototype.flushStream=function(e){this.captionPackets_.length&&(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2&&this.dispatchCea608Packet(e)},this),this.captionPackets_.length=0),this.flushCCStreams(e)},aI.prototype.flush=function(){return this.flushStream("flush")},aI.prototype.partialFlush=function(){return this.flushStream("partialFlush")},aI.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},aI.prototype.dispatchCea608Packet=function(e){this.setsTextOrXDSActive(e)?this.activeCea608Channel_[e.type]=null:this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},aI.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},aI.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)},aI.prototype.setsTextOrXDSActive=function(e){return 256==(28928&e.ccData)||4138==(30974&e.ccData)||6186==(30974&e.ccData)};var a2={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},aR=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],aN=function e(t,i){e.prototype.init.call(this),this.field_=t||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,i,n,r,s;if((t=32639&e.ccData)!==this.lastControlCode_){if(4096==(61440&t)?this.lastControlCode_=t:t!==this.PADDING_&&(this.lastControlCode_=null),n=t>>>8,r=255&t,t!==this.PADDING_){if(t===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(t===this.END_OF_CAPTION_)this.mode_="popOn",this.clearFormatting(e.pts),this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;else if(t===this.ROLL_UP_2_ROWS_)this.rollUpRows_=2,this.setRollUp(e.pts);else if(t===this.ROLL_UP_3_ROWS_)this.rollUpRows_=3,this.setRollUp(e.pts);else if(t===this.ROLL_UP_4_ROWS_)this.rollUpRows_=4,this.setRollUp(e.pts);else if(t===this.CARRIAGE_RETURN_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;else if(t===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1);else if(t===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(e.pts),this.displayed_=aO();else if(t===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=aO();else if(t===this.RESUME_DIRECT_CAPTIONING_)"paintOn"!==this.mode_&&(this.flushDisplayed(e.pts),this.displayed_=aO()),this.mode_="paintOn",this.startPts_=e.pts;else if(this.isSpecialCharacter(n,r))s=aA((n=(3&n)<<8)|r),this[this.mode_](e.pts,s),this.column_++;else if(this.isExtCharacter(n,r))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1),s=aA((n=(3&n)<<8)|r),this[this.mode_](e.pts,s),this.column_++;else if(this.isMidRowCode(n,r))this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&r)&&this.addFormatting(e.pts,["i"]),1==(1&r)&&this.addFormatting(e.pts,["u"]);else if(this.isOffsetControlCode(n,r))this.column_+=3&r;else if(this.isPAC(n,r)){var a=aR.indexOf(7968&t);"rollUp"===this.mode_&&(a-this.rollUpRows_+1<0&&(a=this.rollUpRows_-1),this.setRollUp(e.pts,a)),a!==this.row_&&(this.clearFormatting(e.pts),this.row_=a),1&r&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&t)&&(this.column_=4*((14&t)>>1)),this.isColorPAC(r)&&14==(14&r)&&this.addFormatting(e.pts,["i"])}else this.isNormalChar(n)&&(0===r&&(r=null),s=aA(n),s+=aA(r),this[this.mode_](e.pts,s),this.column_+=s.length)}}else this.lastControlCode_=null}};function a3(e,t){for(var i=e,n=0;n<t.length;n++){var r=t[n];if(i<r.size)return r;i-=r.size}return null}aN.prototype=new aD,aN.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){try{return e.trim()}catch(t){return""}}).join("\n").replace(/^\n+|\n+$/g,"");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t,stream:this.name_})},aN.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=aO(),this.nonDisplayed_=aO(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.rollUpRows_=2,this.formatting_=[]},aN.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},aN.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&48<=t&&t<=63},aN.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&32<=t&&t<=63},aN.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&32<=t&&t<=47},aN.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&33<=t&&t<=35},aN.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&64<=t&&t<=127},aN.prototype.isColorPAC=function(e){return 64<=e&&e<=79||96<=e&&e<=127},aN.prototype.isNormalChar=function(e){return 32<=e&&e<=127},aN.prototype.setRollUp=function(e,t){if("rollUp"!==this.mode_&&(this.row_=14,this.mode_="rollUp",this.flushDisplayed(e),this.nonDisplayed_=aO(),this.displayed_=aO()),void 0!==t&&t!==this.row_)for(var i=0;i<this.rollUpRows_;i++)this.displayed_[t-i]=this.displayed_[this.row_-i],this.displayed_[this.row_-i]="";void 0===t&&(t=this.row_),this.topRow_=t-this.rollUpRows_+1},aN.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);var i=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,i)},aN.prototype.clearFormatting=function(e){if(this.formatting_.length){var t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},"");this.formatting_=[],this[this.mode_](e,t)}},aN.prototype.popOn=function(e,t){var i=this.nonDisplayed_[this.row_];i+=t,this.nonDisplayed_[this.row_]=i},aN.prototype.rollUp=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i},aN.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.row_+1;e<15;e++)this.displayed_[e]="";for(e=this.topRow_;e<this.row_;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[this.row_]=""},aN.prototype.paintOn=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i};var a4=aL,aU=aI,a1=function(){var e,t,i,n,r,s,a=!1;this.isInitialized=function(){return a},this.init=function(t){e=new aU,a=!0,s=!!t&&t.isPartial,e.on("data",function(e){e.startTime=e.startPts/n,e.endTime=e.endPts/n,r.captions.push(e),r.captionStreams[e.stream]=!0})},this.isNewInit=function(e,t){return!(e&&0===e.length||t&&"object"==typeof t&&0===Object.keys(t).length)&&(i!==e[0]||n!==t[i])},this.parse=function(e,s,a){if(!this.isInitialized()||!s||!a)return null;if(this.isNewInit(s,a))n=a[i=s[0]];else if(null===i||!n)return t.push(e),null;for(;0<t.length;){var o,u,l,c,h,d,p,f,m,g,v=t.shift();this.parse(v,s,a)}return null!==(o=(u=e,l=i,c=n,null===l?null:{seiNals:(h=u,d=l,p=a0.findBox(h,["moof","traf"]),f=a0.findBox(h,["mdat"]),m={},g=[],f.forEach(function(e,t){var i=p[t];g.push({mdat:e,traf:i})}),g.forEach(function(e){var t,i,n,r,s,a,o,u,l,c=e.mdat,h=e.traf,p=a0.findBox(h,["tfhd"]),f=ak.parseTfhd(p[0]),g=f.trackId,v=a0.findBox(h,["tfdt"]),y=0<v.length?ak.parseTfdt(v[0]).baseMediaDecodeTime:0,$=a0.findBox(h,["trun"]);d===g&&0<$.length&&(t=function(e,t,i){var n,r,s,a,o=new DataView(e.buffer,e.byteOffset,e.byteLength),u=[];for(r=0;r+4<e.length;r+=s)if(s=o.getUint32(r),r+=4,!(s<=0)&&(31&e[r])==6){var l=e.subarray(r+1,r+1+s),c=a3(r,t);n={nalUnitType:"sei_rbsp",size:s,data:l,escapedRBSP:a4(l),trackId:i},c?(n.pts=c.pts,n.dts=c.dts,a=c):(n.pts=a.pts,n.dts=a.dts),u.push(n)}return u}(c,(i=$,n=y,r=f,s=n,a=r.defaultSampleDuration||0,o=r.defaultSampleSize||0,u=r.trackId,l=[],i.forEach(function(e){var t=ak.parseTrun(e).samples;t.forEach(function(e){void 0===e.duration&&(e.duration=a),void 0===e.size&&(e.size=o),e.trackId=u,e.dts=s,void 0===e.compositionTimeOffset&&(e.compositionTimeOffset=0),e.pts=s+e.compositionTimeOffset,s+=e.duration}),l=l.concat(t)}),l),g),m[g]||(m[g]=[]),m[g]=m[g].concat(t))}),m)[l],timescale:c}))&&o.seiNals?(this.pushNals(o.seiNals),this.flushStream(),r):null},this.pushNals=function(t){if(!this.isInitialized()||!t||0===t.length)return null;t.forEach(function(t){e.push(t)})},this.flushStream=function(){if(!this.isInitialized())return null;s?e.partialFlush():e.flush()},this.clearParsedCaptions=function(){r.captions=[],r.captionStreams={}},this.resetCaptionStream=function(){if(!this.isInitialized())return null;e.reset()},this.clearAllCaptions=function(){this.clearParsedCaptions(),this.resetCaptionStream()},this.reset=function(){t=[],n=i=null,r?this.clearParsedCaptions():r={captions:[],captionStreams:{}},this.resetCaptionStream()},this.reset()};function aM(e){var t=31&e[1];return t<<=8,t|=e[2]}function a6(e){return!!(64&e[1])}function aB(e){var t=0;return 1<(48&e[3])>>>4&&(t+=e[4]+1),t}function aj(e){switch(e){case 5:return"slice_layer_without_partitioning_rbsp_idr";case 6:return"sei_rbsp";case 7:return"seq_parameter_set_rbsp";case 8:return"pic_parameter_set_rbsp";case 9:return"access_unit_delimiter_rbsp";default:return null}}function aF(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]}new aD;var a7,a5,aH,aq,aV=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],aW=(a7=function(e){return 9e4*e},a5=function(e,t){return e*t},aH=function(e){return e/9e4},aq=function(e,t){return e/t},function e(t,i){var n=1;for(i<t&&(n=-1);4294967296<Math.abs(i-t);)t+=8589934592*n;return t}),az={};function aG(e,t,i){for(var n,r,s,a,o=0,u=aK,l=!1;u<=e.byteLength;)if(71!==e[o]||71!==e[u]&&u!==e.byteLength)o++,u++;else{if(n=e.subarray(o,u),"pes"===az.ts.parseType(n,t.pid)&&(r=az.ts.parsePesType(n,t.table),s=az.ts.parsePayloadUnitStartIndicator(n),"audio"===r&&s&&(a=az.ts.parsePesTime(n))&&(a.type="audio",i.audio.push(a),l=!0)),l)break;o+=aK,u+=aK}for(o=(u=e.byteLength)-aK,l=!1;0<=o;)if(71!==e[o]||71!==e[u]&&u!==e.byteLength)o--,u--;else{if(n=e.subarray(o,u),"pes"===az.ts.parseType(n,t.pid)&&(r=az.ts.parsePesType(n,t.table),s=az.ts.parsePayloadUnitStartIndicator(n),"audio"===r&&s&&(a=az.ts.parsePesTime(n))&&(a.type="audio",i.audio.push(a),l=!0)),l)break;o-=aK,u-=aK}}function aX(e,t,i){for(var n,r,s,a,o,u,l,c=0,h=aK,d=!1,p={data:[],size:0};h<e.byteLength;)if(71!==e[c]||71!==e[h])c++,h++;else{if(n=e.subarray(c,h),"pes"===az.ts.parseType(n,t.pid)&&(r=az.ts.parsePesType(n,t.table),s=az.ts.parsePayloadUnitStartIndicator(n),"video"===r&&(s&&!d&&(a=az.ts.parsePesTime(n))&&(a.type="video",i.video.push(a),d=!0),!i.firstKeyFrame))){if(s&&0!==p.size){for(o=new Uint8Array(p.size),u=0;p.data.length;)l=p.data.shift(),o.set(l,u),u+=l.byteLength;if(az.ts.videoPacketContainsKeyFrame(o)){var f=az.ts.parsePesTime(o);f&&(i.firstKeyFrame=f,i.firstKeyFrame.type="video")}p.size=0}p.data.push(n),p.size+=n.byteLength}if(d&&i.firstKeyFrame)break;c+=aK,h+=aK}for(c=(h=e.byteLength)-aK,d=!1;0<=c;)if(71!==e[c]||71!==e[h])c--,h--;else{if(n=e.subarray(c,h),"pes"===az.ts.parseType(n,t.pid)&&(r=az.ts.parsePesType(n,t.table),s=az.ts.parsePayloadUnitStartIndicator(n),"video"===r&&s&&(a=az.ts.parsePesTime(n))&&(a.type="video",i.video.push(a),d=!0)),d)break;c-=aK,h-=aK}}az.ts={parseType:function(e,t){var i=aM(e);return 0===i?"pat":i===t?"pmt":t?"pes":null},parsePat:function(e){var t=a6(e),i=4+aB(e);return t&&(i+=e[i]+1),(31&e[i+10])<<8|e[i+11]},parsePmt:function(e){var t,i={},n=a6(e),r=4+aB(e);if(n&&(r+=e[r]+1),1&e[r+5]){t=3+((15&e[r+1])<<8|e[r+2])-4;for(var s=12+((15&e[r+10])<<8|e[r+11]);s<t;){var a=r+s;i[(31&e[a+1])<<8|e[a+2]]=e[a],s+=5+((15&e[a+3])<<8|e[a+4])}return i}},parsePayloadUnitStartIndicator:a6,parsePesType:function(e,t){switch(t[aM(e)]){case 27:return"video";case 15:return"audio";case 21:return"timed-metadata";default:return null}},parsePesTime:function(e){if(!a6(e))return null;var t=4+aB(e);if(t>=e.byteLength)return null;var i,n=null;return 192&(i=e[t+7])&&((n={}).pts=(14&e[t+9])<<27|(255&e[t+10])<<20|(254&e[t+11])<<12|(255&e[t+12])<<5|(254&e[t+13])>>>3,n.pts*=4,n.pts+=(6&e[t+13])>>>1,n.dts=n.pts,64&i&&(n.dts=(14&e[t+14])<<27|(255&e[t+15])<<20|(254&e[t+16])<<12|(255&e[t+17])<<5|(254&e[t+18])>>>3,n.dts*=4,n.dts+=(6&e[t+18])>>>1)),n},videoPacketContainsKeyFrame:function(e){for(var t=4+aB(e),i=e.subarray(t),n=0,r=0,s=!1;r<i.byteLength-3;r++)if(1===i[r+2]){n=r+5;break}for(;n<i.byteLength;)switch(i[n]){case 0:if(0!==i[n-1]){n+=2;break}if(0!==i[n-2]){n++;break}for(r+3!==n-2&&"slice_layer_without_partitioning_rbsp_idr"===aj(31&i[r+3])&&(s=!0);1!==i[++n]&&n<i.length;);r=n-2,n+=3;break;case 1:if(0!==i[n-1]||0!==i[n-2]){n+=3;break}"slice_layer_without_partitioning_rbsp_idr"===aj(31&i[r+3])&&(s=!0),r=n-2,n+=3;break;default:n+=3}return i=i.subarray(r),n-=r,r=0,i&&3<i.byteLength&&"slice_layer_without_partitioning_rbsp_idr"===aj(31&i[r+3])&&(s=!0),s}},az.aac={isLikelyAacData:function(e){return 73===e[0]&&68===e[1]&&51===e[2]},parseId3TagSize:function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?20+i:10+i},parseAdtsSize:function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3;return 6144&e[t+3]|n|i},parseType:function(e,t){return 73===e[t]&&68===e[t+1]&&51===e[t+2]?"timed-metadata":!0&e[t]&&240==(240&e[t+1])?"audio":null},parseSampleRate:function(e){for(var t=0;t+5<e.length;){if(255===e[t]&&240==(246&e[t+1]))return aV[(60&e[t+2])>>>2];t++}return null},parseAacTimestamp:function(e){var t,i,n;t=10,64&e[5]&&(t+=4,t+=aF(e.subarray(10,14)));do{if((i=aF(e.subarray(t+4,t+8)))<1)break;if("PRIV"===String.fromCharCode(e[t],e[t+1],e[t+2],e[t+3])){n=e.subarray(t+10,t+i+10);for(var r=0;r<n.byteLength;r++)if(0===n[r]){if("com.apple.streaming.transportStreamTimestamp"!==unescape(function(e,t,i){var n,r="";for(n=0;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r}(n,0,r)))break;var s=n.subarray(r+1),a=(1&s[3])<<30|s[4]<<22|s[5]<<14|s[6]<<6|s[7]>>>2;return a*=4,a+=3&s[7]}}t+=10,t+=i}while(t<e.byteLength);return null}};var aK=188,aY=function(e,t){var i;return(i=az.aac.isLikelyAacData(e)?function(e){for(var t,i=!1,n=0,r=null,s=null,a=0,o=0;3<=e.length-o;){switch(az.aac.parseType(e,o)){case"timed-metadata":if(e.length-o<10||(a=az.aac.parseId3TagSize(e,o))>e.length){i=!0;break}null===s&&(t=e.subarray(o,o+a),s=az.aac.parseAacTimestamp(t)),o+=a;break;case"audio":if(e.length-o<7||(a=az.aac.parseAdtsSize(e,o))>e.length){i=!0;break}null===r&&(t=e.subarray(o,o+a),r=az.aac.parseSampleRate(t)),n++,o+=a;break;default:o++}if(i)return null}if(null===r||null===s)return null;var u=9e4/r;return{audio:[{type:"audio",dts:s,pts:s},{type:"audio",dts:s+1024*n*u,pts:s+1024*n*u}]}}(e):function e(t){var i={pid:null,table:null},n={};for(var r in function(e,t){for(var i,n=0,r=aK;r<e.byteLength;)if(71!==e[n]||71!==e[r])n++,r++;else{switch(i=e.subarray(n,r),az.ts.parseType(i,t.pid)){case"pat":t.pid||(t.pid=az.ts.parsePat(i));break;case"pmt":t.table||(t.table=az.ts.parsePmt(i))}if(t.pid&&t.table)return;n+=aK,r+=aK}}(t,i),i.table)if(i.table.hasOwnProperty(r))switch(i.table[r]){case 27:n.video=[],aX(t,i,n),0===n.video.length&&delete n.video;break;case 15:n.audio=[],aG(t,i,n),0===n.audio.length&&delete n.audio}return n}(e))&&(i.audio||i.video)?(function(e,t){if(e.audio&&e.audio.length){var i=t;void 0===i&&(i=e.audio[0].dts),e.audio.forEach(function(e){e.dts=aW(e.dts,i),e.pts=aW(e.pts,i),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4})}if(e.video&&e.video.length){var n=t;if(void 0===n&&(n=e.video[0].dts),e.video.forEach(function(e){e.dts=aW(e.dts,n),e.pts=aW(e.pts,n),e.dtsTime=e.dts/9e4,e.ptsTime=e.pts/9e4}),e.firstKeyFrame){var r=e.firstKeyFrame;r.dts=aW(r.dts,n),r.pts=aW(r.pts,n),r.dtsTime=r.dts/9e4,r.ptsTime=r.dts/9e4}}}(i,t),i):null};function a9(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function aQ(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var aJ=null,aZ=(oe.prototype.decrypt=function(e,t,i,n,r,s){var a=this._key[1],o=e^a[0],u=n^a[1],l=i^a[2],c=t^a[3],h=void 0,d=void 0,p=void 0,f=a.length/4-2,m=void 0,g=4,v=this._tables[1],y=v[0],$=v[1],b=v[2],T=v[3],_=v[4];for(m=0;m<f;m++)h=y[o>>>24]^$[u>>16&255]^b[l>>8&255]^T[255&c]^a[g],d=y[u>>>24]^$[l>>16&255]^b[c>>8&255]^T[255&o]^a[g+1],p=y[l>>>24]^$[c>>16&255]^b[o>>8&255]^T[255&u]^a[g+2],c=y[c>>>24]^$[o>>16&255]^b[u>>8&255]^T[255&l]^a[g+3],g+=4,o=h,u=d,l=p;for(m=0;m<4;m++)r[(3&-m)+s]=_[o>>>24]<<24^_[u>>16&255]<<16^_[l>>8&255]<<8^_[255&c]^a[g++],h=o,o=u,u=l,l=c,c=h},oe);function oe(e){a9(this,oe),aJ=aJ||function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],n=t[4],r=i[4],s=void 0,a=void 0,o=void 0,u=[],l=[],c=void 0,h=void 0,d=void 0,p=void 0,f=void 0;for(s=0;s<256;s++)l[(u[s]=s<<1^283*(s>>7))^s]=s;for(a=o=0;!n[a];a^=c||1,o=l[o]||1)for(d=(d=o^o<<1^o<<2^o<<3^o<<4)>>8^255&d^99,f=16843009*u[h=u[c=u[r[n[a]=d]=a]]]^65537*h^257*c^16843008*a,p=257*u[d]^16843008*d,s=0;s<4;s++)t[s][a]=p=p<<24^p>>>8,i[s][d]=f=f<<24^f>>>8;for(s=0;s<5;s++)t[s]=t[s].slice(0),i[s]=i[s].slice(0);return e}(),this._tables=[[aJ[0][0].slice(),aJ[0][1].slice(),aJ[0][2].slice(),aJ[0][3].slice(),aJ[0][4].slice()],[aJ[1][0].slice(),aJ[1][1].slice(),aJ[1][2].slice(),aJ[1][3].slice(),aJ[1][4].slice()]];var t=void 0,i=void 0,n=void 0,r=void 0,s=void 0,a=this._tables[0][4],o=this._tables[1],u=e.length,l=1;if(4!==u&&6!==u&&8!==u)throw Error("Invalid aes key size");for(r=e.slice(0),s=[],this._key=[r,s],t=u;t<4*u+28;t++)n=r[t-1],(t%u==0||8===u&&t%u==4)&&(n=a[n>>>24]<<24^a[n>>16&255]<<16^a[n>>8&255]<<8^a[255&n],t%u==0&&(n=n<<8^n>>>24^l<<24,l=l<<1^283*(l>>7))),r[t]=r[t-u]^n;for(i=0;t;i++,t--)n=r[3&i?t:t-4],s[i]=t<=4||i<4?n:o[0][a[n>>>24]]^o[1][a[n>>16&255]]^o[2][a[n>>8&255]]^o[3][a[255&n]]}var ot=(oi.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},oi.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},oi.prototype.trigger=function(e,t){var i=this.listeners[e];if(i){if(2===arguments.length)for(var n=i.length,r=0;r<n;++r)i[r].call(this,t);else for(var s=Array.prototype.slice.call(arguments,1),a=i.length,o=0;o<a;++o)i[o].apply(this,s)}},oi.prototype.dispose=function(){this.listeners={}},oi.prototype.pipe=function(e){this.on("data",function(t){e.push(t)})},oi);function oi(){a9(this,oi),this.listeners={}}var on,or=(function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(os,on=ot),os.prototype.processJob_=function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null},os.prototype.push=function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))},os);function os(){a9(this,os);var e=function(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e}(this,on.call(this,ot));return e.jobs=[],e.delay=1,e.timeout_=null,e}function oa(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24}function oo(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),r=new aZ(Array.prototype.slice.call(t)),s=new Uint8Array(e.byteLength),a=new Int32Array(s.buffer),o=void 0,u=void 0,l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,f=void 0,m=void 0;for(o=i[0],u=i[1],l=i[2],c=i[3],m=0;m<n.length;m+=4)h=oa(n[m]),d=oa(n[m+1]),p=oa(n[m+2]),f=oa(n[m+3]),r.decrypt(h,d,p,f,a,m),a[m]=oa(a[m]^o),a[m+1]=oa(a[m+1]^u),a[m+2]=oa(a[m+2]^l),a[m+3]=oa(a[m+3]^c),o=h,u=d,l=p,c=f;return s}var ou,ol,oc=(oh.prototype.decryptChunk_=function(e,t,i,n){return function(){var r=oo(e,t,i);n.set(r,e.byteOffset)}},ou=oh,ol=[{key:"STEP",get:function(){return 32e3}}],ol&&aQ(ou,ol),oh);function oh(e,t,i,n){a9(this,oh);var r=oh.STEP,s=new Int32Array(e.buffer),a=new Uint8Array(e.byteLength),o=0;for(this.asyncStream_=new or,this.asyncStream_.push(this.decryptChunk_(s.subarray(o,o+r),t,i,a)),o=r;o<s.length;o+=r)i=new Uint32Array([oa(s[o-4]),oa(s[o-3]),oa(s[o-2]),oa(s[o-1])]),this.asyncStream_.push(this.decryptChunk_(s.subarray(o,o+r),t,i,a));this.asyncStream_.push(function(){var e;n(null,(e=a).subarray(0,e.byteLength-e[e.byteLength-1]))})}function od(t,i){return/^[a-z]+:/i.test(i)?i:(/\/\//i.test(t)||(t=ry.buildAbsoluteURL(e.location.href,t)),ry.buildAbsoluteURL(t,i))}function op(e,t,i){return e&&i.responseURL&&t!==i.responseURL?i.responseURL:t}function of(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}/**
 * @videojs/http-streaming
 * @version 1.13.2
 * @copyright 2020 Brightcove, Inc
 * @license Apache-2.0
 */ var om=function(e,t,i){return t&&og(e.prototype,t),i&&og(e,i),e};function og(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function ov(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function oy(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e}function o8(e,t){["AUDIO","SUBTITLES"].forEach(function(i){for(var n in e.mediaGroups[i])for(var r in e.mediaGroups[i][n])t(e.mediaGroups[i][n][r],i,n,r)})}function o$(e,t){var i=oC(e,{}),n=i.playlists[t.id];if(!n||n.segments&&t.segments&&n.segments.length===t.segments.length&&n.endList===t.endList&&n.mediaSequence===t.mediaSequence)return null;var r=oC(n,t);n.segments&&(r.segments=function(e,t,i){var n=t.slice();i=i||0;for(var r=Math.min(e.length,t.length+i),s=i;s<r;s++)n[s-i]=oC(e[s],n[s-i]);return n}(n.segments,t.segments,t.mediaSequence-n.mediaSequence)),r.segments.forEach(function(e){var t,i;t=e,i=r.resolvedUri,t.resolvedUri||(t.resolvedUri=od(i,t.uri)),t.key&&!t.key.resolvedUri&&(t.key.resolvedUri=od(i,t.key.uri)),t.map&&!t.map.resolvedUri&&(t.map.resolvedUri=od(i,t.map.uri))});for(var s=0;s<i.playlists.length;s++)i.playlists[s].id===t.id&&(i.playlists[s]=r);return i.playlists[t.id]=r,i.playlists[t.uri]=r,i}function ob(e,t){return e+"-"+t}function oT(e){for(var t=e.playlists.length;t--;){var i=e.playlists[t];i.resolvedUri=od(e.uri,i.uri),i.id=ob(t,i.uri),e.playlists[i.id]=i,(e.playlists[i.uri]=i).attributes||(i.attributes={},oE.warn("Invalid playlist STREAM-INF detected. Missing BANDWIDTH attribute."))}}function o_(e){o8(e,function(t){t.uri&&(t.resolvedUri=od(e.uri,t.uri))})}function oS(e,t){var i=e.segments[e.segments.length-1];return t&&i&&i.duration?1e3*i.duration:500*(e.targetDuration||10)}var ok=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var i=[],n=!0,r=!1,s=void 0;try{for(var a,o=e[Symbol.iterator]();!(n=(a=o.next()).done)&&(i.push(a.value),!t||i.length!==t);n=!0);}catch(u){r=!0,s=u}finally{try{!n&&o.return&&o.return()}finally{if(r)throw s}}return i}(e,t);throw TypeError("Invalid attempt to destructure non-iterable instance")},oC=rm.mergeOptions,oE=rm.log,ow=(ov(o0,rm.EventTarget),om(o0,[{key:"playlistRequestError",value:function(e,t,i){var n=t.uri,r=t.id;this.request=null,i&&(this.state=i),this.error={playlist:this.master.playlists[r],status:e.status,message:"HLS playlist request error at URL: "+n+".",responseText:e.responseText,code:500<=e.status?4:2},this.trigger("error")}},{key:"haveMetadata",value:function(t,i,n){var r=this;this.request=null,this.state="HAVE_METADATA";var s=new rk;this.customTagParsers.forEach(function(e){return s.addParser(e)}),this.customTagMappers.forEach(function(e){return s.addTagMapper(e)}),s.push(t.responseText),s.end(),s.manifest.uri=i,s.manifest.id=n,s.manifest.attributes=s.manifest.attributes||{};var a=o$(this.master,s.manifest);this.targetDuration=s.manifest.targetDuration,a?(this.master=a,this.media_=this.master.playlists[n]):this.trigger("playlistunchanged"),this.media().endList||(e.clearTimeout(this.mediaUpdateTimeout),this.mediaUpdateTimeout=e.setTimeout(function(){r.trigger("mediaupdatetimeout")},oS(this.media(),!!a))),this.trigger("loadedplaylist")}},{key:"dispose",value:function(){this.trigger("dispose"),this.stopRequest(),e.clearTimeout(this.mediaUpdateTimeout),e.clearTimeout(this.finalRenditionTimeout),this.off()}},{key:"stopRequest",value:function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}}},{key:"media",value:function(t,i){var n=this;if(!t)return this.media_;if("HAVE_NOTHING"===this.state)throw Error("Cannot switch media playlist from "+this.state);if("string"==typeof t){if(!this.master.playlists[t])throw Error("Unknown playlist URI: "+t);t=this.master.playlists[t]}if(e.clearTimeout(this.finalRenditionTimeout),i){var r=t.targetDuration/2*1e3||5e3;this.finalRenditionTimeout=e.setTimeout(this.media.bind(this,t,!1),r)}else{var s=this.state,a=!this.media_||t.id!==this.media_.id;if(this.master.playlists[t.id].endList)return this.request&&(this.request.onreadystatechange=null,this.request.abort(),this.request=null),this.state="HAVE_METADATA",this.media_=t,void(a&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(a){if(this.state="SWITCHING_MEDIA",this.request){if(t.resolvedUri===this.request.url)return;this.request.onreadystatechange=null,this.request.abort(),this.request=null}this.media_&&this.trigger("mediachanging"),this.request=this.hls_.xhr({uri:t.resolvedUri,withCredentials:this.withCredentials},function(e,i){if(n.request){if(t.resolvedUri=op(n.handleManifestRedirects,t.resolvedUri,i),e)return n.playlistRequestError(n.request,t,s);n.haveMetadata(i,t.uri,t.id),"HAVE_MASTER"===s?n.trigger("loadedmetadata"):n.trigger("mediachange")}})}}}},{key:"pause",value:function(){this.stopRequest(),e.clearTimeout(this.mediaUpdateTimeout),"HAVE_NOTHING"===this.state&&(this.started=!1),"SWITCHING_MEDIA"===this.state?this.media_?this.state="HAVE_METADATA":this.state="HAVE_MASTER":"HAVE_CURRENT_METADATA"===this.state&&(this.state="HAVE_METADATA")}},{key:"load",value:function(t){var i=this;e.clearTimeout(this.mediaUpdateTimeout);var n=this.media();if(t){var r=n?n.targetDuration/2*1e3:5e3;this.mediaUpdateTimeout=e.setTimeout(function(){return i.load()},r)}else this.started?n&&!n.endList?this.trigger("mediaupdatetimeout"):this.trigger("loadedplaylist"):this.start()}},{key:"start",value:function(){var t=this;this.started=!0,this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(i,n){if(t.request){if(t.request=null,i)return t.error={status:n.status,message:"HLS playlist request error at URL: "+t.srcUrl+".",responseText:n.responseText,code:2},"HAVE_NOTHING"===t.state&&(t.started=!1),t.trigger("error");var r=new rk;if(t.customTagParsers.forEach(function(e){return r.addParser(e)}),t.customTagMappers.forEach(function(e){return r.addTagMapper(e)}),r.push(n.responseText),r.end(),t.state="HAVE_MASTER",t.srcUrl=op(t.handleManifestRedirects,t.srcUrl,n),r.manifest.uri=t.srcUrl,r.manifest.playlists)return t.master=r.manifest,oT(t.master),o_(t.master),t.trigger("loadedplaylist"),void(t.request||t.media(r.manifest.playlists[0]));var s=ob(0,t.srcUrl);return t.master={mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:e.location.href,playlists:[{uri:t.srcUrl,id:s,resolvedUri:t.srcUrl,attributes:{}}]},t.master.playlists[s]=t.master.playlists[0],t.master.playlists[t.srcUrl]=t.master.playlists[0],t.haveMetadata(n,t.srcUrl,s),t.trigger("loadedmetadata")}})}}]),o0);function o0(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};of(this,o0);var n=oy(this,(o0.__proto__||Object.getPrototypeOf(o0)).call(this)),r=i.withCredentials,s=i.handleManifestRedirects;n.srcUrl=e,n.hls_=t,n.withCredentials=void 0!==r&&r,n.handleManifestRedirects=void 0!==s&&s;var a=t.options_;if(n.customTagParsers=a&&a.customTagParsers||[],n.customTagMappers=a&&a.customTagMappers||[],!n.srcUrl)throw Error("A non-empty playlist URL is required");return n.state="HAVE_NOTHING",n.on("mediaupdatetimeout",function(){"HAVE_METADATA"===n.state&&(n.state="HAVE_CURRENT_METADATA",n.request=n.hls_.xhr({uri:od(n.master.uri,n.media().uri),withCredentials:n.withCredentials},function(e,t){if(n.request)return e?n.playlistRequestError(n.request,n.media(),"HAVE_METADATA"):void n.haveMetadata(n.request,n.media().uri,n.media().id)}))}),n}function ox(e,t,i){var n,r;return void 0===t&&(t=e.mediaSequence+e.segments.length),t<e.mediaSequence?0:(n=function(e,t){var i=0,n=t-e.mediaSequence,r=e.segments[n];if(r){if(void 0!==r.start)return{result:r.start,precise:!0};if(void 0!==r.end)return{result:r.end-r.duration,precise:!0}}for(;n--;){if(void 0!==(r=e.segments[n]).end)return{result:i+r.end,precise:!0};if(i+=r.duration,void 0!==r.start)return{result:i+r.start,precise:!0}}return{result:i,precise:!1}}(e,t)).precise?n.result:(r=function(e,t){for(var i=0,n=void 0,r=t-e.mediaSequence;r<e.segments.length;r++){if(void 0!==(n=e.segments[r]).start)return{result:n.start-i,precise:!0};if(i+=n.duration,void 0!==n.end)return{result:n.end-i,precise:!0}}return{result:-1,precise:!1}}(e,t)).precise?r.result:n.result+i}function oP(t,i,n){if(!t)return 0;if("number"!=typeof n&&(n=0),void 0===i){if(t.totalDuration)return t.totalDuration;if(!t.endList)return e.Infinity}return ox(t,i,n)}function oL(e,t,i){var n=0;if(i<t){var r=[i,t];t=r[0],i=r[1]}if(t<0){for(var s=t;s<Math.min(0,i);s++)n+=e.targetDuration;t=0}for(var a=t;a<i;a++)n+=e.segments[a].duration;return n}function oI(e,t){if(!e.segments.length)return 0;var i=e.segments.length,n=e.segments[i-1].duration||e.targetDuration,r="number"==typeof t?t:n+2*e.targetDuration;if(0===r)return i;for(var s=0;i--&&!(r<=(s+=e.segments[i].duration)););return Math.max(0,i)}function oD(e,t,i,n){if(!e||!e.segments)return null;if(e.endList)return oP(e);if(null===t)return null;t=t||0;var r=i?oI(e,n):e.segments.length;return ox(e,e.mediaSequence+r,t)}function oA(e){return e-Math.floor(e)==0}function oO(e,t){if(oA(t))return t+.1*e;for(var i=t.toString().split(".")[1].length,n=1;n<=i;n++){var r=Math.pow(10,n),s=t*r;if(oA(s)||n===i)return(s+e)/r}}function o2(e){return e.excludeUntil&&e.excludeUntil>Date.now()}function oR(e){return e.excludeUntil&&e.excludeUntil===1/0}function oN(e){var t=o2(e);return!e.disabled&&!t}function o3(e,t){return t.attributes&&t.attributes[e]}function o4(e,t){if(1===e.playlists.length)return!0;var i=t.attributes.BANDWIDTH||Number.MAX_VALUE;return 0===e.playlists.filter(function(e){return!!oN(e)&&(e.attributes.BANDWIDTH||0)<i}).length}function oU(){return function e(t,i){t=oJ({timeout:45e3},t);var n=e.beforeRequest||rm.Hls.xhr.beforeRequest;if(n&&"function"==typeof n){var r=n(t);r&&(t=r)}var s=oQ(t,function(e,t){var n=s.response;!e&&n&&(s.responseTime=Date.now(),s.roundTripTime=s.responseTime-s.requestTime,s.bytesReceived=n.byteLength||n.length,s.bandwidth||(s.bandwidth=Math.floor(s.bytesReceived/s.roundTripTime*8e3))),t.headers&&(s.responseHeaders=t.headers),e&&"ETIMEDOUT"===e.code&&(s.timedout=!0),e||s.aborted||200===t.statusCode||206===t.statusCode||0===t.statusCode||(e=Error("XHR Failed with a response of: "+(s&&(n||s.responseText)))),i(e,s)}),a=s.abort;return s.abort=function(){return s.aborted=!0,a.apply(s,arguments)},s.uri=t.uri,s.requestTime=Date.now(),s}}function o1(e){var t,i,n={};return e.byterange&&(n.Range=(i=(t=e.byterange).offset+t.length-1,"bytes="+t.offset+"-"+i)),n}function oM(e,t){var i=e.toString(16);return"00".substring(0,2-i.length)+i+(t%2?" ":"")}function o6(e){return 32<=e&&e<126?String.fromCharCode(e):"."}function oB(e){var t={};return Object.keys(e).forEach(function(i){var n=e[i];ArrayBuffer.isView(n)?t[i]={bytes:n.buffer,byteOffset:n.byteOffset,byteLength:n.byteLength}:t[i]=n}),t}function oj(e){var t=e.byterange||{length:1/0,offset:0};return[t.length,t.offset,e.resolvedUri].join(",")}function oF(e){return e.resolvedUri}function o7(e){for(var t=Array.prototype.slice.call(e),i="",n=0;n<t.length/16;n++)i+=t.slice(16*n,16*n+16).map(oM).join("")+" "+t.slice(16*n,16*n+16).map(o6).join("")+"\n";return i}function o5(e,t){var i=[],n=void 0;if(e&&e.length)for(n=0;n<e.length;n++)t(e.start(n),e.end(n))&&i.push([e.start(n),e.end(n)]);return rm.createTimeRanges(i)}function oH(e,t){return o5(e,function(e,i){return e-.1<=t&&t<=i+.1})}function oq(e,t){return o5(e,function(e){return t<=e-1/30})}function oV(e){var t=[];if(!e||!e.length)return"";for(var i=0;i<e.length;i++)t.push(e.start(i)+" => "+e.end(i));return t.join(", ")}function oW(e){for(var t=[],i=0;i<e.length;i++)t.push({start:e.start(i),end:e.end(i)});return t}function oz(e,t,i){var n=void 0,r=void 0;if(i&&i.cues)for(n=i.cues.length;n--;)(r=i.cues[n]).startTime<=t&&r.endTime>=e&&i.removeCue(r)}function oG(e){return isNaN(e)||Math.abs(e)===1/0?Number.MAX_VALUE:e}var oX=rm.createTimeRange,oK=oO.bind(null,1),oY=oO.bind(null,-1),o9={duration:oP,seekable:function(e,t,i){var n=oD(e,t,!0,i);return null===n?oX():oX(t||0,n)},safeLiveIndex:oI,getMediaInfoForTime:function(e,t,i,n){var r=void 0,s=void 0,a=e.segments.length,o=t-n;if(o<0){if(0<i){for(r=i-1;0<=r;r--)if(0<(o+=oY((s=e.segments[r]).duration)))return{mediaIndex:r,startTime:n-oL(e,i,r)}}return{mediaIndex:0,startTime:t}}if(i<0){for(r=i;r<0;r++)if((o-=e.targetDuration)<0)return{mediaIndex:0,startTime:t};i=0}for(r=i;r<a;r++)if((o-=oK((s=e.segments[r]).duration))<0)return{mediaIndex:r,startTime:n+oL(e,i,r)};return{mediaIndex:a-1,startTime:t}},isEnabled:oN,isDisabled:function(e){return e.disabled},isBlacklisted:o2,isIncompatible:oR,playlistEnd:oD,isAes:function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].key)return!0;return!1},isFmp4:function(e){for(var t=0;t<e.segments.length;t++)if(e.segments[t].map)return!0;return!1},hasAttribute:o3,estimateSegmentRequestTime:function(e,t,i,n){var r=3<arguments.length&&void 0!==n?n:0;return o3("BANDWIDTH",i)?(e*i.attributes.BANDWIDTH-8*r)/t:NaN},isLowestEnabledRendition:o4},oQ=rm.xhr,oJ=rm.mergeOptions,oZ=Object.freeze({createTransferableMessage:oB,initSegmentId:oj,segmentKeyId:oF,hexDump:o7,tagDump:function(e){return o7(e.bytes)},textRanges:function(e){var t,i,n="",r=void 0;for(r=0;r<e.length;r++)n+=(i=r,(t=e).start(i)+"-"+t.end(i)+" ");return n}}),ue="undefined"!=typeof window?window:{},ut="undefined"==typeof Symbol?"__target":Symbol(),ui=ue.BlobBuilder||ue.WebKitBlobBuilder||ue.MozBlobBuilder||ue.MSBlobBuilder,un=ue.URL||ue.webkitURL||un&&un.msURL,ur=ue.Worker;function us(e,t){return function(i){var n=this;if(!t)return new ur(e);if(ur&&!i){var r=uc(t.toString().replace(/^function.+?{/,"").slice(0,-1));return this[ut]=new ur(r),function(e,t){if(e&&t){var i=e.terminate;e.objURL=t,e.terminate=function(){e.objURL&&un.revokeObjectURL(e.objURL),i.call(e)}}}(this[ut],r),this[ut]}var s={postMessage:function(e){n.onmessage&&setTimeout(function(){n.onmessage({data:e,target:s})})}};t.call(s),this.postMessage=function(e){setTimeout(function(){s.onmessage({data:e,target:n})})},this.isThisThread=!0}}if(ur){var ua,uo=uc("self.onmessage = function () {}"),uu=new Uint8Array(1);try{(ua=new ur(uo)).postMessage(uu,[uu.buffer])}catch(ul){ur=null}finally{un.revokeObjectURL(uo),ua&&ua.terminate()}}function uc(e){try{return un.createObjectURL(new Blob([e],{type:"application/javascript"}))}catch(t){var i=new ui;return i.append(e),un.createObjectURL(i.getBlob(type))}}function uh(e){return e.map(function(e){return e.replace(/avc1\.(\d+)\.(\d+)/i,function(e,t,i){return"avc1."+("00"+Number(t).toString(16)).slice(-2)+"00"+("00"+Number(i).toString(16)).slice(-2)})})}function ud(e){var t,i=0<arguments.length&&void 0!==e?e:"",n={codecCount:0};return n.codecCount=i.split(",").length,n.codecCount=n.codecCount||2,(t=/(^|\s|,)+(avc[13])([^ ,]*)/i.exec(i))&&(n.videoCodec=t[2],n.videoObjectTypeIndicator=t[3]),n.audioProfile=/(^|\s|,)+mp4a.[0-9A-Fa-f]+\.([0-9A-Fa-f]+)/i.exec(i),n.audioProfile=n.audioProfile&&n.audioProfile[2],n}function up(e,t,i){return e+"/"+t+'; codecs="'+i.filter(function(e){return!!e}).join(", ")+'"'}function uf(e){var t={type:"",parameters:{}},i=e.trim().split(";");return t.type=i.shift().trim(),i.forEach(function(e){var i=e.trim().split("=");if(1<i.length){var n=i[0].replace(/"/g,"").trim(),r=i[1].replace(/"/g,"").trim();t.parameters[n]=r}}),t}function um(e){return/mp4a\.\d+.\d+/i.test(e)}function ug(e){return/avc1\.[\da-f]+/i.test(e)}var uv=new us("./transmuxer-worker.worker.js",function(e,t){var i=this;!function(){function e(){this.init=function(){var e={};this.on=function(t,i){e[t]||(e[t]=[]),e[t]=e[t].concat(i)},this.off=function(t,i){var n;return!!e[t]&&(n=e[t].indexOf(i),e[t]=e[t].slice(),e[t].splice(n,1),-1<n)},this.trigger=function(t){var i,n,r,s;if(i=e[t]){if(2===arguments.length)for(r=i.length,n=0;n<r;++n)i[n].call(this,arguments[1]);else{for(s=[],n=arguments.length,n=1;n<arguments.length;++n)s.push(arguments[n]);for(r=i.length,n=0;n<r;++n)i[n].apply(this,s)}}},this.dispose=function(){e={}}}}e.prototype.pipe=function(e){return this.on("data",function(t){e.push(t)}),this.on("done",function(t){e.flush(t)}),this.on("partialdone",function(t){e.partialFlush(t)}),this.on("endedtimeline",function(t){e.endTimeline(t)}),this.on("reset",function(t){e.reset(t)}),e},e.prototype.push=function(e){this.trigger("data",e)},e.prototype.flush=function(e){this.trigger("done",e)},e.prototype.partialFlush=function(e){this.trigger("partialdone",e)},e.prototype.endTimeline=function(e){this.trigger("endedtimeline",e)},e.prototype.reset=function(e){this.trigger("reset",e)};var t,n,r,s,a,o,u,l,c,h,d,p,f,m,g,v,y,$,b,T,_,S,k,C,E,w,x,P,L,I,D,A,O,R,N,U,M,B,j,F,H=e;function q(e,t){var i={size:0,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0,degradationPriority:0,isNonSyncSample:1}};return i.dataOffset=t,i.compositionTimeOffset=e.pts-e.dts,i.duration=e.duration,i.size=4*e.length,i.size+=e.byteLength,e.keyFrame&&(i.flags.dependsOn=2,i.flags.isNonSyncSample=0),i}function V(e){for(var t=[];e--;)t.push(0);return t}function W(){W.prototype.init.call(this),this.captionPackets_=[],this.ccStreams_=[new eT(0,0),new eT(0,1),new eT(1,0),new eT(1,1)],this.reset(),this.ccStreams_.forEach(function(e){e.on("data",this.trigger.bind(this,"data")),e.on("partialdone",this.trigger.bind(this,"partialdone")),e.on("done",this.trigger.bind(this,"done"))},this)}!function(){var e;if(S={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],pasp:[],sdtp:[],smhd:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],styp:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[]},"undefined"!=typeof Uint8Array){for(e in S)S.hasOwnProperty(e)&&(S[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);k=new Uint8Array([105,115,111,109]),E=new Uint8Array([97,118,99,49]),C=new Uint8Array([0,0,0,1]),w=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),x=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),P={video:w,audio:x},D=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),I=new Uint8Array([0,0,0,0,0,0,0,0]),O=A=new Uint8Array([0,0,0,0,0,0,0,0]),R=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),N=A,L=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}}(),t=function(e){var t,i,n=[],r=0;for(t=1;t<arguments.length;t++)n.push(arguments[t]);for(t=n.length;t--;)r+=n[t].byteLength;for(i=new Uint8Array(r+8),new DataView(i.buffer,i.byteOffset,i.byteLength).setUint32(0,i.byteLength),i.set(e,4),t=0,r=8;t<n.length;t++)i.set(n[t],r),r+=n[t].byteLength;return i},n=function(){return t(S.dinf,t(S.dref,D))},r=function(e){return t(S.esds,new Uint8Array([0,0,0,0,3,25,0,0,0,4,17,64,21,0,6,0,0,0,218,192,0,0,218,192,5,2,e.audioobjecttype<<3|e.samplingfrequencyindex>>>1,e.samplingfrequencyindex<<7|e.channelcount<<3,6,1,2]))},g=function(e){return t(S.hdlr,P[e])},m=function(e){var i=new Uint8Array([0,0,0,0,0,0,0,2,0,0,0,3,0,1,95,144,e.duration>>>24&255,e.duration>>>16&255,e.duration>>>8&255,255&e.duration,85,196,0,0]);return e.samplerate&&(i[12]=e.samplerate>>>24&255,i[13]=e.samplerate>>>16&255,i[14]=e.samplerate>>>8&255,i[15]=255&e.samplerate),t(S.mdhd,i)},f=function(e){return t(S.mdia,m(e),g(e.type),o(e))},a=function(e){return t(S.mfhd,new Uint8Array([0,0,0,0,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e]))},o=function(e){return t(S.minf,"video"===e.type?t(S.vmhd,L):t(S.smhd,I),n(),y(e))},u=function(e,i){for(var n=[],r=i.length;r--;)n[r]=b(i[r]);return t.apply(null,[S.moof,a(e)].concat(n))},l=function(e){for(var i=e.length,n=[];i--;)n[i]=d(e[i]);return t.apply(null,[S.moov,h(4294967295)].concat(n).concat(c(e)))},c=function(e){for(var i=e.length,n=[];i--;)n[i]=T(e[i]);return t.apply(null,[S.mvex].concat(n))},h=function(e){var i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,2,0,1,95,144,(4278190080&e)>>24,(16711680&e)>>16,(65280&e)>>8,255&e,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return t(S.mvhd,i)},v=function(e){var i,n,r=e.samples||[],s=new Uint8Array(4+r.length);for(n=0;n<r.length;n++)i=r[n].flags,s[n+4]=i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy;return t(S.sdtp,s)},y=function(e){return t(S.stbl,$(e),t(S.stts,N),t(S.stsc,O),t(S.stsz,R),t(S.stco,A))},$=function(e){return t(S.stsd,new Uint8Array([0,0,0,0,0,0,0,1]),"video"===e.type?U(e):M(e))},U=function(e){var i,n,r=e.sps||[],s=e.pps||[],a=[],o=[];for(i=0;i<r.length;i++)a.push((65280&r[i].byteLength)>>>8),a.push(255&r[i].byteLength),a=a.concat(Array.prototype.slice.call(r[i]));for(i=0;i<s.length;i++)o.push((65280&s[i].byteLength)>>>8),o.push(255&s[i].byteLength),o=o.concat(Array.prototype.slice.call(s[i]));if(n=[S.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,118,105,100,101,111,106,115,45,99,111,110,116,114,105,98,45,104,108,115,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),t(S.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([r.length],a,[s.length],o))),t(S.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192]))],e.sarRatio){var u=e.sarRatio[0],l=e.sarRatio[1];n.push(t(S.pasp,new Uint8Array([(4278190080&u)>>24,(16711680&u)>>16,(65280&u)>>8,255&u,(4278190080&l)>>24,(16711680&l)>>16,(65280&l)>>8,255&l])))}return t.apply(null,n)},M=function(e){return t(S.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),r(e))},p=function(e){var i=new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,0,(4278190080&e.duration)>>24,(16711680&e.duration)>>16,(65280&e.duration)>>8,255&e.duration,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,(65280&e.width)>>8,255&e.width,0,0,(65280&e.height)>>8,255&e.height,0,0]);return t(S.tkhd,i)},b=function(e){var i,n,r,s,a,o;return i=t(S.tfhd,new Uint8Array([0,0,0,58,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0])),a=Math.floor(e.baseMediaDecodeTime/4294967296),o=Math.floor(e.baseMediaDecodeTime%4294967296),n=t(S.tfdt,new Uint8Array([1,0,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a,o>>>24&255,o>>>16&255,o>>>8&255,255&o])),"audio"===e.type?(r=_(e,92),t(S.traf,i,n,r)):(s=v(e),r=_(e,s.length+92),t(S.traf,i,n,r,s))},d=function(e){return e.duration=e.duration||4294967295,t(S.trak,p(e),f(e))},T=function(e){var i=new Uint8Array([0,0,0,0,(4278190080&e.id)>>24,(16711680&e.id)>>16,(65280&e.id)>>8,255&e.id,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return"video"!==e.type&&(i[i.length-1]=0),t(S.trex,i)},F=function(e,t){var i=0,n=0,r=0,s=0;return e.length&&(void 0!==e[0].duration&&(i=1),void 0!==e[0].size&&(n=2),void 0!==e[0].flags&&(r=4),void 0!==e[0].compositionTimeOffset&&(s=8)),[0,0,i|n|r|s,1,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t]},j=function(e,i){var n,r,s,a;for(i+=20+16*(r=e.samples||[]).length,n=F(r,i),a=0;a<r.length;a++)s=r[a],n=n.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size,s.flags.isLeading<<2|s.flags.dependsOn,s.flags.isDependedOn<<6|s.flags.hasRedundancy<<4|s.flags.paddingValue<<1|s.flags.isNonSyncSample,61440&s.flags.degradationPriority,15&s.flags.degradationPriority,(4278190080&s.compositionTimeOffset)>>>24,(16711680&s.compositionTimeOffset)>>>16,(65280&s.compositionTimeOffset)>>>8,255&s.compositionTimeOffset]);return t(S.trun,new Uint8Array(n))},B=function(e,i){var n,r,s,a;for(i+=20+8*(r=e.samples||[]).length,n=F(r,i),a=0;a<r.length;a++)s=r[a],n=n.concat([(4278190080&s.duration)>>>24,(16711680&s.duration)>>>16,(65280&s.duration)>>>8,255&s.duration,(4278190080&s.size)>>>24,(16711680&s.size)>>>16,(65280&s.size)>>>8,255&s.size]);return t(S.trun,new Uint8Array(n))},_=function(e,t){return"audio"===e.type?B(e,t):j(e,t)},s=function(){return t(S.ftyp,k,C,k,E)};var z,G,X,K,Y,Q=function(e){return t(S.mdat,e)},J=u,Z=function(e){var t,i=s(),n=l(e);return(t=new Uint8Array(i.byteLength+n.byteLength)).set(i),t.set(n,i.byteLength),t},ee=function(e){var t,i,n=[],r=[];for(r.byteLength=0,r.nalCount=0,r.duration=0,t=n.byteLength=0;t<e.length;t++)"access_unit_delimiter_rbsp"===(i=e[t]).nalUnitType?(n.length&&(n.duration=i.dts-n.dts,r.byteLength+=n.byteLength,r.nalCount+=n.length,r.duration+=n.duration,r.push(n)),(n=[i]).byteLength=i.data.byteLength,n.pts=i.pts,n.dts=i.dts):("slice_layer_without_partitioning_rbsp_idr"===i.nalUnitType&&(n.keyFrame=!0),n.duration=i.dts-n.dts,n.byteLength+=i.data.byteLength,n.push(i));return r.length&&(!n.duration||n.duration<=0)&&(n.duration=r[r.length-1].duration),r.byteLength+=n.byteLength,r.nalCount+=n.length,r.duration+=n.duration,r.push(n),r},et=function(e){var t,i,n=[],r=[];for(n.byteLength=0,n.nalCount=0,n.duration=0,n.pts=e[0].pts,n.dts=e[0].dts,r.byteLength=0,r.nalCount=0,r.duration=0,r.pts=e[0].pts,r.dts=e[0].dts,t=0;t<e.length;t++)(i=e[t]).keyFrame?(n.length&&(r.push(n),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration),(n=[i]).nalCount=i.length,n.byteLength=i.byteLength,n.pts=i.pts,n.dts=i.dts,n.duration=i.duration):(n.duration+=i.duration,n.nalCount+=i.length,n.byteLength+=i.byteLength,n.push(i));return r.length&&n.duration<=0&&(n.duration=r[r.length-1].duration),r.byteLength+=n.byteLength,r.nalCount+=n.nalCount,r.duration+=n.duration,r.push(n),r},ei=function(e){var t;return!e[0][0].keyFrame&&1<e.length&&(t=e.shift(),e.byteLength-=t.byteLength,e.nalCount-=t.nalCount,e[0][0].dts=t.dts,e[0][0].pts=t.pts,e[0][0].duration+=t.duration),e},en=function(e,t){var i,n,r,s,a,o=t||0,u=[];for(i=0;i<e.length;i++)for(s=e[i],n=0;n<s.length;n++)a=s[n],o+=(r=q(a,o)).size,u.push(r);return u},er=function(e){var t,i,n,r,s,a,o=0,u=e.byteLength,l=e.nalCount,c=new Uint8Array(u+4*l),h=new DataView(c.buffer);for(t=0;t<e.length;t++)for(r=e[t],i=0;i<r.length;i++)for(s=r[i],n=0;n<s.length;n++)a=s[n],h.setUint32(o,a.data.byteLength),o+=4,c.set(a.data,o),o+=a.data.byteLength;return c},es=[33,16,5,32,164,27],ea=[33,65,108,84,1,2,4,8,168,2,4,8,17,191,252],eo=Object.keys(z={96e3:[es,[227,64],V(154),[56]],88200:[es,[231],V(170),[56]],64e3:[es,[248,192],V(240),[56]],48e3:[es,[255,192],V(268),[55,148,128],V(54),[112]],44100:[es,[255,192],V(268),[55,163,128],V(84),[112]],32e3:[es,[255,192],V(268),[55,234],V(226),[112]],24e3:[es,[255,192],V(268),[55,255,128],V(268),[111,112],V(126),[224]],16e3:[es,[255,192],V(268),[55,255,128],V(268),[111,255],V(269),[223,108],V(195),[1,192]],12e3:[ea,V(268),[3,127,248],V(268),[6,255,240],V(268),[13,255,224],V(268),[27,253,128],V(259),[56]],11025:[ea,V(268),[3,127,248],V(268),[6,255,240],V(268),[13,255,224],V(268),[27,255,192],V(268),[55,175,128],V(108),[112]],8e3:[ea,V(268),[3,121,16],V(47),[7]]}).reduce(function(e,t){return e[t]=new Uint8Array(z[t].reduce(function(e,t){return e.concat(t)},[])),e},{}),eu=(G=function(e){return 9e4*e},X=function(e,t){return e*t},K=function(e){return e/9e4},Y=function(e,t){return e/t},function(e,t){return G(Y(e,t))}),el=function(e,t,i){return K(i?e:e-t)},ec=function(e,t,i,n){var r,s,a,o,u,l,c,h=0,d=0,p=0;if(t.length&&(a=eu(e.baseMediaDecodeTime,e.samplerate),o=Math.ceil(9e4/(e.samplerate/1024)),i&&n&&(p=(d=Math.floor((h=a-Math.max(i,n))/o))*o),!(d<1||45e3<p))){for(u=(u=eo[e.samplerate])||t[0].data,l=0;l<d;l++)c=t[0],t.splice(0,0,{data:u,dts:c.dts-o,pts:c.pts-o});e.baseMediaDecodeTime-=Math.floor((r=p,s=e.samplerate,X(K(r),s)))}},eh=function(e){var t,i,n=[];for(t=0;t<e.length;t++)i=e[t],n.push({size:i.data.byteLength,duration:1024});return n},ed=function(e){var t,i,n=0,r=new Uint8Array(function(e){var t,i=0;for(t=0;t<e.length;t++)i+=e[t].data.byteLength;return i}(e));for(t=0;t<e.length;t++)i=e[t],r.set(i.data,n),n+=i.data.byteLength;return r},ep=function(e){delete e.minSegmentDts,delete e.maxSegmentDts,delete e.minSegmentPts,delete e.maxSegmentPts},ef=function(e,t){var i,n=e.minSegmentDts;return t||(n-=e.timelineStartInfo.dts),i=e.timelineStartInfo.baseMediaDecodeTime,i+=n,i=Math.max(0,i),"audio"===e.type&&(i*=e.samplerate/9e4,i=Math.floor(i)),i},em=function(e,t){"number"==typeof t.pts&&(void 0===e.timelineStartInfo.pts&&(e.timelineStartInfo.pts=t.pts),void 0===e.minSegmentPts?e.minSegmentPts=t.pts:e.minSegmentPts=Math.min(e.minSegmentPts,t.pts),void 0===e.maxSegmentPts?e.maxSegmentPts=t.pts:e.maxSegmentPts=Math.max(e.maxSegmentPts,t.pts)),"number"==typeof t.dts&&(void 0===e.timelineStartInfo.dts&&(e.timelineStartInfo.dts=t.dts),void 0===e.minSegmentDts?e.minSegmentDts=t.dts:e.minSegmentDts=Math.min(e.minSegmentDts,t.dts),void 0===e.maxSegmentDts?e.maxSegmentDts=t.dts:e.maxSegmentDts=Math.max(e.maxSegmentDts,t.dts))},eg=function(e){for(var t=0,i={payloadType:-1,payloadSize:0},n=0,r=0;t<e.byteLength&&128!==e[t];){for(;255===e[t];)n+=255,t++;for(n+=e[t++];255===e[t];)r+=255,t++;if(r+=e[t++],!i.payload&&4===n){i.payloadType=n,i.payloadSize=r,i.payload=e.subarray(t,t+r);break}t+=r,r=n=0}return i},ev=function(e,t){var i,n,r,s,a=[];if(!(64&t[0]))return a;for(n=31&t[0],i=0;i<n;i++)s={type:3&t[2+(r=3*i)],pts:e},4&t[2+r]&&(s.ccData=t[3+r]<<8|t[4+r],a.push(s));return a};function ey(e){return null===e?"":String.fromCharCode(e=e$[e]||e)}function e8(){for(var e=[],t=15;t--;)e.push("");return e}(W.prototype=new H).push=function(e){var t,i,n,r;if("sei_rbsp"===e.nalUnitType&&4===(t=eg(e.escapedRBSP)).payloadType&&(i=181!==(r=t).payload[0]?null:49!=(r.payload[1]<<8|r.payload[2])?null:"GA94"!==String.fromCharCode(r.payload[3],r.payload[4],r.payload[5],r.payload[6])?null:3!==r.payload[7]?null:r.payload.subarray(8,r.payload.length-1))){if(e.dts<this.latestDts_)this.ignoreNextEqualDts_=!0;else{if(e.dts===this.latestDts_&&this.ignoreNextEqualDts_)return this.numSameDts_--,void(this.numSameDts_||(this.ignoreNextEqualDts_=!1));n=ev(e.pts,i),this.captionPackets_=this.captionPackets_.concat(n),this.latestDts_!==e.dts&&(this.numSameDts_=0),this.numSameDts_++,this.latestDts_=e.dts}}},W.prototype.flushCCStreams=function(e){this.ccStreams_.forEach(function(t){return"flush"===e?t.flush():t.partialFlush()},this)},W.prototype.flushStream=function(e){this.captionPackets_.length&&(this.captionPackets_.forEach(function(e,t){e.presortIndex=t}),this.captionPackets_.sort(function(e,t){return e.pts===t.pts?e.presortIndex-t.presortIndex:e.pts-t.pts}),this.captionPackets_.forEach(function(e){e.type<2&&this.dispatchCea608Packet(e)},this),this.captionPackets_.length=0),this.flushCCStreams(e)},W.prototype.flush=function(){return this.flushStream("flush")},W.prototype.partialFlush=function(){return this.flushStream("partialFlush")},W.prototype.reset=function(){this.latestDts_=null,this.ignoreNextEqualDts_=!1,this.numSameDts_=0,this.activeCea608Channel_=[null,null],this.ccStreams_.forEach(function(e){e.reset()})},W.prototype.dispatchCea608Packet=function(e){this.setsTextOrXDSActive(e)?this.activeCea608Channel_[e.type]=null:this.setsChannel1Active(e)?this.activeCea608Channel_[e.type]=0:this.setsChannel2Active(e)&&(this.activeCea608Channel_[e.type]=1),null!==this.activeCea608Channel_[e.type]&&this.ccStreams_[(e.type<<1)+this.activeCea608Channel_[e.type]].push(e)},W.prototype.setsChannel1Active=function(e){return 4096==(30720&e.ccData)},W.prototype.setsChannel2Active=function(e){return 6144==(30720&e.ccData)},W.prototype.setsTextOrXDSActive=function(e){return 256==(28928&e.ccData)||4138==(30974&e.ccData)||6186==(30974&e.ccData)};var e$={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,304:174,305:176,306:189,307:191,308:8482,309:162,310:163,311:9834,312:224,313:160,314:232,315:226,316:234,317:238,318:244,319:251,544:193,545:201,546:211,547:218,548:220,549:252,550:8216,551:161,552:42,553:39,554:8212,555:169,556:8480,557:8226,558:8220,559:8221,560:192,561:194,562:199,563:200,564:202,565:203,566:235,567:206,568:207,569:239,570:212,571:217,572:249,573:219,574:171,575:187,800:195,801:227,802:205,803:204,804:236,805:210,806:242,807:213,808:245,809:123,810:125,811:92,812:94,813:95,814:124,815:126,816:196,817:228,818:214,819:246,820:223,821:165,822:164,823:9474,824:197,825:229,826:216,827:248,828:9484,829:9488,830:9492,831:9496},eb=[4352,4384,4608,4640,5376,5408,5632,5664,5888,5920,4096,4864,4896,5120,5152],eT=function e(t,i){e.prototype.init.call(this),this.field_=t||0,this.dataChannel_=i||0,this.name_="CC"+(1+(this.field_<<1|this.dataChannel_)),this.setConstants(),this.reset(),this.push=function(e){var t,i,n,r,s;if((t=32639&e.ccData)!==this.lastControlCode_){if(4096==(61440&t)?this.lastControlCode_=t:t!==this.PADDING_&&(this.lastControlCode_=null),n=t>>>8,r=255&t,t!==this.PADDING_){if(t===this.RESUME_CAPTION_LOADING_)this.mode_="popOn";else if(t===this.END_OF_CAPTION_)this.mode_="popOn",this.clearFormatting(e.pts),this.flushDisplayed(e.pts),i=this.displayed_,this.displayed_=this.nonDisplayed_,this.nonDisplayed_=i,this.startPts_=e.pts;else if(t===this.ROLL_UP_2_ROWS_)this.rollUpRows_=2,this.setRollUp(e.pts);else if(t===this.ROLL_UP_3_ROWS_)this.rollUpRows_=3,this.setRollUp(e.pts);else if(t===this.ROLL_UP_4_ROWS_)this.rollUpRows_=4,this.setRollUp(e.pts);else if(t===this.CARRIAGE_RETURN_)this.clearFormatting(e.pts),this.flushDisplayed(e.pts),this.shiftRowsUp_(),this.startPts_=e.pts;else if(t===this.BACKSPACE_)"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1);else if(t===this.ERASE_DISPLAYED_MEMORY_)this.flushDisplayed(e.pts),this.displayed_=e8();else if(t===this.ERASE_NON_DISPLAYED_MEMORY_)this.nonDisplayed_=e8();else if(t===this.RESUME_DIRECT_CAPTIONING_)"paintOn"!==this.mode_&&(this.flushDisplayed(e.pts),this.displayed_=e8()),this.mode_="paintOn",this.startPts_=e.pts;else if(this.isSpecialCharacter(n,r))s=ey((n=(3&n)<<8)|r),this[this.mode_](e.pts,s),this.column_++;else if(this.isExtCharacter(n,r))"popOn"===this.mode_?this.nonDisplayed_[this.row_]=this.nonDisplayed_[this.row_].slice(0,-1):this.displayed_[this.row_]=this.displayed_[this.row_].slice(0,-1),s=ey((n=(3&n)<<8)|r),this[this.mode_](e.pts,s),this.column_++;else if(this.isMidRowCode(n,r))this.clearFormatting(e.pts),this[this.mode_](e.pts," "),this.column_++,14==(14&r)&&this.addFormatting(e.pts,["i"]),1==(1&r)&&this.addFormatting(e.pts,["u"]);else if(this.isOffsetControlCode(n,r))this.column_+=3&r;else if(this.isPAC(n,r)){var a=eb.indexOf(7968&t);"rollUp"===this.mode_&&(a-this.rollUpRows_+1<0&&(a=this.rollUpRows_-1),this.setRollUp(e.pts,a)),a!==this.row_&&(this.clearFormatting(e.pts),this.row_=a),1&r&&-1===this.formatting_.indexOf("u")&&this.addFormatting(e.pts,["u"]),16==(16&t)&&(this.column_=4*((14&t)>>1)),this.isColorPAC(r)&&14==(14&r)&&this.addFormatting(e.pts,["i"])}else this.isNormalChar(n)&&(0===r&&(r=null),s=ey(n),s+=ey(r),this[this.mode_](e.pts,s),this.column_+=s.length)}}else this.lastControlCode_=null}};function e_(e,t){var i=1;for(t<e&&(i=-1);4294967296<Math.abs(t-e);)e+=8589934592*i;return e}function eS(e){var t,i;eS.prototype.init.call(this),this.type_=e||"shared",this.push=function(e){"shared"!==this.type_&&e.type!==this.type_||(void 0===i&&(i=e.dts),e.dts=e_(e.dts,i),e.pts=e_(e.pts,i),t=e.dts,this.trigger("data",e))},this.flush=function(){i=t,this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")},this.discontinuity=function(){t=i=void 0},this.reset=function(){this.discontinuity(),this.trigger("reset")}}eT.prototype=new H,eT.prototype.flushDisplayed=function(e){var t=this.displayed_.map(function(e){try{return e.trim()}catch(t){return""}}).join("\n").replace(/^\n+|\n+$/g,"");t.length&&this.trigger("data",{startPts:this.startPts_,endPts:e,text:t,stream:this.name_})},eT.prototype.reset=function(){this.mode_="popOn",this.topRow_=0,this.startPts_=0,this.displayed_=e8(),this.nonDisplayed_=e8(),this.lastControlCode_=null,this.column_=0,this.row_=14,this.rollUpRows_=2,this.formatting_=[]},eT.prototype.setConstants=function(){0===this.dataChannel_?(this.BASE_=16,this.EXT_=17,this.CONTROL_=(20|this.field_)<<8,this.OFFSET_=23):1===this.dataChannel_&&(this.BASE_=24,this.EXT_=25,this.CONTROL_=(28|this.field_)<<8,this.OFFSET_=31),this.PADDING_=0,this.RESUME_CAPTION_LOADING_=32|this.CONTROL_,this.END_OF_CAPTION_=47|this.CONTROL_,this.ROLL_UP_2_ROWS_=37|this.CONTROL_,this.ROLL_UP_3_ROWS_=38|this.CONTROL_,this.ROLL_UP_4_ROWS_=39|this.CONTROL_,this.CARRIAGE_RETURN_=45|this.CONTROL_,this.RESUME_DIRECT_CAPTIONING_=41|this.CONTROL_,this.BACKSPACE_=33|this.CONTROL_,this.ERASE_DISPLAYED_MEMORY_=44|this.CONTROL_,this.ERASE_NON_DISPLAYED_MEMORY_=46|this.CONTROL_},eT.prototype.isSpecialCharacter=function(e,t){return e===this.EXT_&&48<=t&&t<=63},eT.prototype.isExtCharacter=function(e,t){return(e===this.EXT_+1||e===this.EXT_+2)&&32<=t&&t<=63},eT.prototype.isMidRowCode=function(e,t){return e===this.EXT_&&32<=t&&t<=47},eT.prototype.isOffsetControlCode=function(e,t){return e===this.OFFSET_&&33<=t&&t<=35},eT.prototype.isPAC=function(e,t){return e>=this.BASE_&&e<this.BASE_+8&&64<=t&&t<=127},eT.prototype.isColorPAC=function(e){return 64<=e&&e<=79||96<=e&&e<=127},eT.prototype.isNormalChar=function(e){return 32<=e&&e<=127},eT.prototype.setRollUp=function(e,t){if("rollUp"!==this.mode_&&(this.row_=14,this.mode_="rollUp",this.flushDisplayed(e),this.nonDisplayed_=e8(),this.displayed_=e8()),void 0!==t&&t!==this.row_)for(var i=0;i<this.rollUpRows_;i++)this.displayed_[t-i]=this.displayed_[this.row_-i],this.displayed_[this.row_-i]="";void 0===t&&(t=this.row_),this.topRow_=t-this.rollUpRows_+1},eT.prototype.addFormatting=function(e,t){this.formatting_=this.formatting_.concat(t);var i=t.reduce(function(e,t){return e+"<"+t+">"},"");this[this.mode_](e,i)},eT.prototype.clearFormatting=function(e){if(this.formatting_.length){var t=this.formatting_.reverse().reduce(function(e,t){return e+"</"+t+">"},"");this.formatting_=[],this[this.mode_](e,t)}},eT.prototype.popOn=function(e,t){var i=this.nonDisplayed_[this.row_];i+=t,this.nonDisplayed_[this.row_]=i},eT.prototype.rollUp=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i},eT.prototype.shiftRowsUp_=function(){var e;for(e=0;e<this.topRow_;e++)this.displayed_[e]="";for(e=this.row_+1;e<15;e++)this.displayed_[e]="";for(e=this.topRow_;e<this.row_;e++)this.displayed_[e]=this.displayed_[e+1];this.displayed_[this.row_]=""},eT.prototype.paintOn=function(e,t){var i=this.displayed_[this.row_];i+=t,this.displayed_[this.row_]=i};var ek={CaptionStream:W,Cea608Stream:eT},eC={H264_STREAM_TYPE:27,ADTS_STREAM_TYPE:15,METADATA_STREAM_TYPE:21};function eE(e,t,i){var n,r="";for(n=t;n<i;n++)r+="%"+("00"+e[n].toString(16)).slice(-2);return r}function ew(e,t,i){return decodeURIComponent(eE(e,t,i))}function e0(e){return e[0]<<21|e[1]<<14|e[2]<<7|e[3]}eS.prototype=new H;var ex,eP={TXXX:function(e){var t;if(3===e.data[0]){for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=ew(e.data,1,t),e.value=ew(e.data,t+1,e.data.length).replace(/\0*$/,"");break}e.data=e.value}},WXXX:function(e){var t;if(3===e.data[0]){for(t=1;t<e.data.length;t++)if(0===e.data[t]){e.description=ew(e.data,1,t),e.url=ew(e.data,t+1,e.data.length);break}}},PRIV:function(e){var t,i;for(t=0;t<e.data.length;t++)if(0===e.data[t]){e.owner=unescape(eE(i=e.data,0,t));break}e.privateData=e.data.subarray(t+1),e.data=e.privateData}};(ex=function(e){var t,i={debug:!(!e||!e.debug),descriptor:e&&e.descriptor},n=0,r=[],s=0;if(ex.prototype.init.call(this),this.dispatchType=eC.METADATA_STREAM_TYPE.toString(16),i.descriptor)for(t=0;t<i.descriptor.length;t++)this.dispatchType+=("00"+i.descriptor[t].toString(16)).slice(-2);this.push=function(e){var t,a,o,u,l;if("timed-metadata"===e.type){if(e.dataAlignmentIndicator&&(s=0,r.length=0),0===r.length&&(e.data.length<10||e.data[0]!=="I".charCodeAt(0)||e.data[1]!=="D".charCodeAt(0)||e.data[2]!=="3".charCodeAt(0)))i.debug;else if(r.push(e),s+=e.data.byteLength,1===r.length&&(n=e0(e.data.subarray(6,10)),n+=10),!(s<n)){for(t={data:new Uint8Array(n),frames:[],pts:r[0].pts,dts:r[0].dts},l=0;l<n;)t.data.set(r[0].data.subarray(0,n-l),l),l+=r[0].data.byteLength,s-=r[0].data.byteLength,r.shift();a=10,64&t.data[5]&&(a+=4,a+=e0(t.data.subarray(10,14)),n-=e0(t.data.subarray(16,20)));do{if((o=e0(t.data.subarray(a+4,a+8)))<1)return;if((u={id:String.fromCharCode(t.data[a],t.data[a+1],t.data[a+2],t.data[a+3]),data:t.data.subarray(a+10,a+o+10)}).key=u.id,eP[u.id]&&(eP[u.id](u),"com.apple.streaming.transportStreamTimestamp"===u.owner)){var c=u.data,h=(1&c[3])<<30|c[4]<<22|c[5]<<14|c[6]<<6|c[7]>>>2;h*=4,h+=3&c[7],u.timeStamp=h,void 0===t.pts&&void 0===t.dts&&(t.pts=u.timeStamp,t.dts=u.timeStamp),this.trigger("timestamp",u)}t.frames.push(u),a+=10,a+=o}while(a<n);this.trigger("data",t)}}}}).prototype=new H;var eL,eI,eD,eA=ex;(eL=function(){var e=new Uint8Array(188),t=0;eL.prototype.init.call(this),this.push=function(i){var n,r=0,s=188;for(t?((n=new Uint8Array(i.byteLength+t)).set(e.subarray(0,t)),n.set(i,t),t=0):n=i;s<n.byteLength;)71!==n[r]||71!==n[s]?(r++,s++):(this.trigger("data",n.subarray(r,s)),r+=188,s+=188);r<n.byteLength&&(e.set(n.subarray(r),0),t=n.byteLength-r)},this.flush=function(){188===t&&71===e[0]&&(this.trigger("data",e),t=0),this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")},this.reset=function(){t=0,this.trigger("reset")}}).prototype=new H,(eI=function(){var e,t,i,n;eI.prototype.init.call(this),(n=this).packetsWaitingForPmt=[],this.programMapTable=void 0,e=function(e,n){var r=0;n.payloadUnitStartIndicator&&(r+=e[r]+1),"pat"===n.type?t(e.subarray(r),n):i(e.subarray(r),n)},t=function(e,t){t.section_number=e[7],t.last_section_number=e[8],n.pmtPid=(31&e[10])<<8|e[11],t.pmtPid=n.pmtPid},i=function(e,t){var i,r;if(1&e[5]){for(n.programMapTable={video:null,audio:null,"timed-metadata":{}},i=3+((15&e[1])<<8|e[2])-4,r=12+((15&e[10])<<8|e[11]);r<i;){var s=e[r],a=(31&e[r+1])<<8|e[r+2];s===eC.H264_STREAM_TYPE&&null===n.programMapTable.video?n.programMapTable.video=a:s===eC.ADTS_STREAM_TYPE&&null===n.programMapTable.audio?n.programMapTable.audio=a:s===eC.METADATA_STREAM_TYPE&&(n.programMapTable["timed-metadata"][a]=s),r+=5+((15&e[r+3])<<8|e[r+4])}t.programMapTable=n.programMapTable}},this.push=function(t){var i={},n=4;if(i.payloadUnitStartIndicator=!!(64&t[1]),i.pid=31&t[1],i.pid<<=8,i.pid|=t[2],1<(48&t[3])>>>4&&(n+=t[n]+1),0===i.pid)i.type="pat",e(t.subarray(n),i),this.trigger("data",i);else if(i.pid===this.pmtPid)for(i.type="pmt",e(t.subarray(n),i),this.trigger("data",i);this.packetsWaitingForPmt.length;)this.processPes_.apply(this,this.packetsWaitingForPmt.shift());else void 0===this.programMapTable?this.packetsWaitingForPmt.push([t,n,i]):this.processPes_(t,n,i)},this.processPes_=function(e,t,i){i.pid===this.programMapTable.video?i.streamType=eC.H264_STREAM_TYPE:i.pid===this.programMapTable.audio?i.streamType=eC.ADTS_STREAM_TYPE:i.streamType=this.programMapTable["timed-metadata"][i.pid],i.type="pes",i.data=e.subarray(t),this.trigger("data",i)}}).prototype=new H,eI.STREAM_TYPES={h264:27,adts:15},(eD=function(){function e(e,t,n){var r,s,a,o,u,l=new Uint8Array(e.size),c={type:t},h=0,d=0;if(e.data.length&&!(e.size<9)){for(c.trackId=e.data[0].pid,h=0;h<e.data.length;h++)u=e.data[h],l.set(u.data,d),d+=u.data.byteLength;r=l,(s=c).packetLength=6+(r[4]<<8|r[5]),s.dataAlignmentIndicator=0!=(4&r[6]),192&(a=r[7])&&(s.pts=(14&r[9])<<27|(255&r[10])<<20|(254&r[11])<<12|(255&r[12])<<5|(254&r[13])>>>3,s.pts*=4,s.pts+=(6&r[13])>>>1,s.dts=s.pts,64&a&&(s.dts=(14&r[14])<<27|(255&r[15])<<20|(254&r[16])<<12|(255&r[17])<<5|(254&r[18])>>>3,s.dts*=4,s.dts+=(6&r[18])>>>1)),s.data=r.subarray(9+r[8]),o="video"===t||c.packetLength<=e.size,(n||o)&&(e.size=0,e.data.length=0),o&&i.trigger("data",c)}}var t,i=this,n={data:[],size:0},r={data:[],size:0},s={data:[],size:0};eD.prototype.init.call(this),this.push=function(a){({pat:function(){},pes:function(){var t,i;switch(a.streamType){case eC.H264_STREAM_TYPE:case eC.H264_STREAM_TYPE:t=n,i="video";break;case eC.ADTS_STREAM_TYPE:t=r,i="audio";break;case eC.METADATA_STREAM_TYPE:t=s,i="timed-metadata";break;default:return}a.payloadUnitStartIndicator&&e(t,i,!0),t.data.push(a),t.size+=a.data.byteLength},pmt:function(){var e={type:"metadata",tracks:[]};null!==(t=a.programMapTable).video&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.video,codec:"avc",type:"video"}),null!==t.audio&&e.tracks.push({timelineStartInfo:{baseMediaDecodeTime:0},id:+t.audio,codec:"adts",type:"audio"}),i.trigger("data",e)}})[a.type]()},this.reset=function(){n.size=0,n.data.length=0,r.size=0,r.data.length=0,this.trigger("reset")},this.flushStreams_=function(){e(n,"video"),e(r,"audio"),e(s,"timed-metadata")},this.flush=function(){this.flushStreams_(),this.trigger("done")}}).prototype=new H;var eO={PAT_PID:0,MP2T_PACKET_LENGTH:188,TransportPacketStream:eL,TransportParseStream:eI,ElementaryStream:eD,TimestampRolloverStream:eS,CaptionStream:ek.CaptionStream,Cea608Stream:ek.Cea608Stream,MetadataStream:eA};for(var e2 in eC)eC.hasOwnProperty(e2)&&(eO[e2]=eC[e2]);var eR,eN=eO,e3=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];(eR=function(e){var t,i=0;eR.prototype.init.call(this),this.push=function(n){var r,s,a,o,u,l,c=0;if(e||(i=0),"audio"===n.type)for(t?(o=t,(t=new Uint8Array(o.byteLength+n.data.byteLength)).set(o),t.set(n.data,o.byteLength)):t=n.data;c+5<t.length;)if(255===t[c]&&240==(246&t[c+1])){if(s=2*(1&~t[c+1]),r=(3&t[c+3])<<11|t[c+4]<<3|(224&t[c+5])>>5,l=9e4*(u=1024*(1+(3&t[c+6])))/e3[(60&t[c+2])>>>2],a=c+r,t.byteLength<a)return;if(this.trigger("data",{pts:n.pts+i*l,dts:n.dts+i*l,sampleCount:u,audioobjecttype:1+(t[c+2]>>>6&3),channelcount:(1&t[c+2])<<2|(192&t[c+3])>>>6,samplerate:e3[(60&t[c+2])>>>2],samplingfrequencyindex:(60&t[c+2])>>>2,samplesize:16,data:t.subarray(c+7+s,a)}),i++,t.byteLength===a)return void(t=void 0);t=t.subarray(a)}else c++},this.flush=function(){i=0,this.trigger("done")},this.reset=function(){t=void 0,this.trigger("reset")},this.endTimeline=function(){t=void 0,this.trigger("endedtimeline")}}).prototype=new H;var e4,eU,e1,eM=eR,e6=function(e){var t=e.byteLength,i=0,n=0;this.length=function(){return 8*t},this.bitsAvailable=function(){return 8*t+n},this.loadWord=function(){var r=e.byteLength-t,s=new Uint8Array(4),a=Math.min(4,t);if(0===a)throw Error("no bytes available");s.set(e.subarray(r,r+a)),i=new DataView(s.buffer).getUint32(0),n=8*a,t-=a},this.skipBits=function(e){var r;e<n||(e-=n,e-=8*(r=Math.floor(e/8)),t-=r,this.loadWord()),i<<=e,n-=e},this.readBits=function(e){var r=Math.min(n,e),s=i>>>32-r;return 0<(n-=r)?i<<=r:0<t&&this.loadWord(),0<(r=e-r)?s<<r|this.readBits(r):s},this.skipLeadingZeros=function(){var e;for(e=0;e<n;++e)if(0!=(i&2147483648>>>e))return i<<=e,n-=e,e;return this.loadWord(),e+this.skipLeadingZeros()},this.skipUnsignedExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.skipExpGolomb=function(){this.skipBits(1+this.skipLeadingZeros())},this.readUnsignedExpGolomb=function(){var e=this.skipLeadingZeros();return this.readBits(e+1)-1},this.readExpGolomb=function(){var e=this.readUnsignedExpGolomb();return 1&e?1+e>>>1:-1*(e>>>1)},this.readBoolean=function(){return 1===this.readBits(1)},this.readUnsignedByte=function(){return this.readBits(8)},this.loadWord()};(eU=function(){var e,t,i=0;eU.prototype.init.call(this),this.push=function(n){for(var r,s=(t=t?((r=new Uint8Array(t.byteLength+n.data.byteLength)).set(t),r.set(n.data,t.byteLength),r):n.data).byteLength;i<s-3;i++)if(1===t[i+2]){e=i+5;break}for(;e<s;)switch(t[e]){case 0:if(0!==t[e-1]){e+=2;break}if(0!==t[e-2]){e++;break}for(i+3!==e-2&&this.trigger("data",t.subarray(i+3,e-2));1!==t[++e]&&e<s;);i=e-2,e+=3;break;case 1:if(0!==t[e-1]||0!==t[e-2]){e+=3;break}this.trigger("data",t.subarray(i+3,e-2)),i=e-2,e+=3;break;default:e+=3}t=t.subarray(i),e-=i,i=0},this.reset=function(){t=null,i=0,this.trigger("reset")},this.flush=function(){t&&3<t.byteLength&&this.trigger("data",t.subarray(i+3)),t=null,i=0,this.trigger("done")},this.endTimeline=function(){this.flush(),this.trigger("endedtimeline")}}).prototype=new H,e1={100:!0,110:!0,122:!0,244:!0,44:!0,83:!0,86:!0,118:!0,128:!0,138:!0,139:!0,134:!0},(e4=function(){var e,t,i,n,r,s,a,o=new eU;e4.prototype.init.call(this),(e=this).push=function(e){"video"===e.type&&(t=e.trackId,i=e.pts,n=e.dts,o.push(e))},o.on("data",function(a){var o={trackId:t,pts:i,dts:n,data:a};switch(31&a[0]){case 5:o.nalUnitType="slice_layer_without_partitioning_rbsp_idr";break;case 6:o.nalUnitType="sei_rbsp",o.escapedRBSP=r(a.subarray(1));break;case 7:o.nalUnitType="seq_parameter_set_rbsp",o.escapedRBSP=r(a.subarray(1)),o.config=s(o.escapedRBSP);break;case 8:o.nalUnitType="pic_parameter_set_rbsp";break;case 9:o.nalUnitType="access_unit_delimiter_rbsp"}e.trigger("data",o)}),o.on("done",function(){e.trigger("done")}),o.on("partialdone",function(){e.trigger("partialdone")}),o.on("reset",function(){e.trigger("reset")}),o.on("endedtimeline",function(){e.trigger("endedtimeline")}),this.flush=function(){o.flush()},this.partialFlush=function(){o.partialFlush()},this.reset=function(){o.reset()},this.endTimeline=function(){o.endTimeline()},a=function(e,t){var i,n=8,r=8;for(i=0;i<e;i++)0!==r&&(r=(n+t.readExpGolomb()+256)%256),n=0===r?n:r},r=function(e){for(var t,i,n=e.byteLength,r=[],s=1;s<n-2;)0===e[s]&&0===e[s+1]&&3===e[s+2]?(r.push(s+2),s+=2):s++;if(0===r.length)return e;t=n-r.length,i=new Uint8Array(t);var a=0;for(s=0;s<t;a++,s++)a===r[0]&&(a++,r.shift()),i[s]=e[a];return i},s=function(e){var t,i,n,r,s,o,u,l,c,h,d,p,f,m=0,g=0,v=0,y=0,$=1;if(i=(t=new e6(e)).readUnsignedByte(),r=t.readUnsignedByte(),n=t.readUnsignedByte(),t.skipUnsignedExpGolomb(),e1[i]&&(3===(s=t.readUnsignedExpGolomb())&&t.skipBits(1),t.skipUnsignedExpGolomb(),t.skipUnsignedExpGolomb(),t.skipBits(1),t.readBoolean()))for(d=3!==s?8:12,f=0;f<d;f++)t.readBoolean()&&a(f<6?16:64,t);if(t.skipUnsignedExpGolomb(),0===(o=t.readUnsignedExpGolomb()))t.readUnsignedExpGolomb();else if(1===o)for(t.skipBits(1),t.skipExpGolomb(),t.skipExpGolomb(),u=t.readUnsignedExpGolomb(),f=0;f<u;f++)t.skipExpGolomb();if(t.skipUnsignedExpGolomb(),t.skipBits(1),l=t.readUnsignedExpGolomb(),c=t.readUnsignedExpGolomb(),0===(h=t.readBits(1))&&t.skipBits(1),t.skipBits(1),t.readBoolean()&&(m=t.readUnsignedExpGolomb(),g=t.readUnsignedExpGolomb(),v=t.readUnsignedExpGolomb(),y=t.readUnsignedExpGolomb()),t.readBoolean()&&t.readBoolean()){switch(t.readUnsignedByte()){case 1:p=[1,1];break;case 2:p=[12,11];break;case 3:p=[10,11];break;case 4:p=[16,11];break;case 5:p=[40,33];break;case 6:p=[24,11];break;case 7:p=[20,11];break;case 8:p=[32,11];break;case 9:p=[80,33];break;case 10:p=[18,11];break;case 11:p=[15,11];break;case 12:p=[64,33];break;case 13:p=[160,99];break;case 14:p=[4,3];break;case 15:p=[3,2];break;case 16:p=[2,1];break;case 255:p=[t.readUnsignedByte()<<8|t.readUnsignedByte(),t.readUnsignedByte()<<8|t.readUnsignedByte()]}p&&($=p[0]/p[1])}return{profileIdc:i,levelIdc:n,profileCompatibility:r,width:Math.ceil((16*(l+1)-2*m-2*g)*$),height:(2-h)*(c+1)*16-2*v-2*y,sarRatio:p}}}).prototype=new H;var eB,ej={H264Stream:e4,NalByteStream:eU},eF=function(e){return 73===e[0]&&68===e[1]&&51===e[2]},e7=function(e,t){var i=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&e[t+5])>>4?20+i:10+i},e5=function(e,t){var i=(224&e[t+5])>>5,n=e[t+4]<<3;return 6144&e[t+3]|n|i};function eH(e,t){var i;if(e.length!==t.length)return!1;for(i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}function eq(e,t,i,n,r,s){return{start:{dts:e,pts:e+(i-t)},end:{dts:e+(n-t),pts:e+(r-i)},prependedContentDuration:s,baseMediaDecodeTime:e}}(eB=function(){var e=new Uint8Array,t=0;eB.prototype.init.call(this),this.setTimestamp=function(e){t=e},this.push=function(i){var n,r,s,a,o=0,u=0;for(e.length?(a=e.length,(e=new Uint8Array(i.byteLength+a)).set(e.subarray(0,a)),e.set(i,a)):e=i;3<=e.length-u;)if(e[u]!=="I".charCodeAt(0)||e[u+1]!=="D".charCodeAt(0)||e[u+2]!=="3".charCodeAt(0)){if(255!=(255&e[u])||240!=(240&e[u+1]))u++;else{if(e.length-u<7||u+(o=e5(e,u))>e.length)break;s={type:"audio",data:e.subarray(u,u+o),pts:t,dts:t},this.trigger("data",s),u+=o}}else{if(e.length-u<10||u+(o=e7(e,u))>e.length)break;r={type:"timed-metadata",data:e.subarray(u,u+o)},this.trigger("data",r),u+=o}e=0<(n=e.length-u)?e.subarray(u):new Uint8Array},this.reset=function(){e=new Uint8Array,this.trigger("reset")},this.endTimeline=function(){e=new Uint8Array,this.trigger("endedtimeline")}}).prototype=new H;var eV,eW,ez,eG,eX=eB,eK=ej.H264Stream,eY=eF,e9=["audioobjecttype","channelcount","samplerate","samplingfrequencyindex","samplesize"],eQ=["width","height","profileIdc","levelIdc","profileCompatibility","sarRatio"];(eW=function(e,t){var i=[],n=0,r=0,s=0,a=1/0;t=t||{},eW.prototype.init.call(this),this.push=function(t){em(e,t),e&&e9.forEach(function(i){e[i]=t[i]}),i.push(t)},this.setEarliestDts=function(t){r=t-e.timelineStartInfo.baseMediaDecodeTime},this.setVideoBaseMediaDecodeTime=function(e){a=e},this.setAudioAppendStart=function(e){s=e},this.flush=function(){var o,u,l,c,h,d,p,f;0!==i.length&&(o=(d=i,p=e,f=r,p.minSegmentDts>=f?d:(p.minSegmentDts=1/0,d.filter(function(e){return e.dts>=f&&(p.minSegmentDts=Math.min(p.minSegmentDts,e.dts),p.minSegmentPts=p.minSegmentDts,!0)}))),e.baseMediaDecodeTime=ef(e,t.keepOriginalTimestamps),ec(e,o,s,a),e.samples=eh(o),l=Q(ed(o)),i=[],u=J(n,[e]),c=new Uint8Array(u.byteLength+l.byteLength),n++,c.set(u),c.set(l,u.byteLength),ep(e),h=Math.ceil(9216e4/e.samplerate),o.length&&this.trigger("timingInfo",{start:o[0].pts,end:o[0].pts+o.length*h}),this.trigger("data",{track:e,boxes:c})),this.trigger("done","AudioSegmentStream")},this.reset=function(){ep(e),i=[],this.trigger("reset")}}).prototype=new H,(eV=function(e,t){var i,n,r=0,s=[],a=[];t=t||{},eV.prototype.init.call(this),delete e.minPTS,this.gopCache_=[],this.push=function(t){em(e,t),"seq_parameter_set_rbsp"!==t.nalUnitType||i||(i=t.config,e.sps=[t.data],eQ.forEach(function(t){e[t]=i[t]},this)),"pic_parameter_set_rbsp"!==t.nalUnitType||n||(n=t.data,e.pps=[t.data]),s.push(t)},this.flush=function(){for(var i,n,o,u,l,c,h,d,p,f=0;s.length&&"access_unit_delimiter_rbsp"!==s[0].nalUnitType;)s.shift();if(0===s.length)return this.resetStream_(),void this.trigger("done","VideoSegmentStream");if((u=et(n=ee(s)))[0][0].keyFrame||((o=this.getGopForFusion_(s[0],e))?(f=o.duration,u.unshift(o),u.byteLength+=o.byteLength,u.nalCount+=o.nalCount,u.pts=o.pts,u.dts=o.dts,u.duration+=o.duration):u=ei(u)),a.length){if(!(i=t.alignGopsAtEnd?this.alignGopsAtEnd_(u):this.alignGopsAtStart_(u)))return this.gopCache_.unshift({gop:u.pop(),pps:e.pps,sps:e.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.resetStream_(),void this.trigger("done","VideoSegmentStream");ep(e),u=i}em(e,u),e.samples=en(u),c=Q(er(u)),e.baseMediaDecodeTime=ef(e,t.keepOriginalTimestamps),this.trigger("processedGopsInfo",u.map(function(e){return{pts:e.pts,dts:e.dts,byteLength:e.byteLength}})),d=u[0],p=u[u.length-1],this.trigger("segmentTimingInfo",eq(e.baseMediaDecodeTime,d.dts,d.pts,p.dts+p.duration,p.pts+p.duration,f)),this.trigger("timingInfo",{start:u[0].pts,end:u[u.length-1].pts+u[u.length-1].duration}),this.gopCache_.unshift({gop:u.pop(),pps:e.pps,sps:e.sps}),this.gopCache_.length=Math.min(6,this.gopCache_.length),s=[],this.trigger("baseMediaDecodeTime",e.baseMediaDecodeTime),this.trigger("timelineStartInfo",e.timelineStartInfo),l=J(r,[e]),h=new Uint8Array(l.byteLength+c.byteLength),r++,h.set(l),h.set(c,l.byteLength),this.trigger("data",{track:e,boxes:h}),this.resetStream_(),this.trigger("done","VideoSegmentStream")},this.reset=function(){this.resetStream_(),s=[],this.gopCache_.length=0,a.length=0,this.trigger("reset")},this.resetStream_=function(){ep(e),n=i=void 0},this.getGopForFusion_=function(t){var i,n,r,s,a,o=1/0;for(a=0;a<this.gopCache_.length;a++)r=(s=this.gopCache_[a]).gop,e.pps&&eH(e.pps[0],s.pps[0])&&e.sps&&eH(e.sps[0],s.sps[0])&&(r.dts<e.timelineStartInfo.dts||-1e4<=(i=t.dts-r.dts-r.duration)&&i<=45e3&&(!n||i<o)&&(n=s,o=i));return n?n.gop:null},this.alignGopsAtStart_=function(e){var t,i,n,r,s,o,u,l;for(s=e.byteLength,o=e.nalCount,u=e.duration,t=i=0;t<a.length&&i<e.length&&(n=a[t],r=e[i],n.pts!==r.pts);)r.pts>n.pts?t++:(i++,s-=r.byteLength,o-=r.nalCount,u-=r.duration);return 0===i?e:i===e.length?null:((l=e.slice(i)).byteLength=s,l.duration=u,l.nalCount=o,l.pts=l[0].pts,l.dts=l[0].dts,l)},this.alignGopsAtEnd_=function(e){for(t=a.length-1,i=e.length-1,s=null,o=!1;0<=t&&0<=i;){if(n=a[t],r=e[i],n.pts===r.pts){o=!0;break}n.pts>r.pts?t--:(t===a.length-1&&(s=i),i--)}if(!o&&null===s)return null;if(0===(u=o?i:s))return e;var t,i,n,r,s,o,u,l=e.slice(u),c=l.reduce(function(e,t){return e.byteLength+=t.byteLength,e.duration+=t.duration,e.nalCount+=t.nalCount,e},{byteLength:0,duration:0,nalCount:0});return l.byteLength=c.byteLength,l.duration=c.duration,l.nalCount=c.nalCount,l.pts=l[0].pts,l.dts=l[0].dts,l},this.alignGopsWith=function(e){a=e}}).prototype=new H,(eG=function(e,t){this.numberOfTracks=0,this.metadataStream=t,void 0!==(e=e||{}).remux?this.remuxTracks=!!e.remux:this.remuxTracks=!0,"boolean"==typeof e.keepOriginalTimestamps?this.keepOriginalTimestamps=e.keepOriginalTimestamps:this.keepOriginalTimestamps=!1,this.pendingTracks=[],this.videoTrack=null,this.pendingBoxes=[],this.pendingCaptions=[],this.pendingMetadata=[],this.pendingBytes=0,this.emittedTracks=0,eG.prototype.init.call(this),this.push=function(e){return e.text?this.pendingCaptions.push(e):e.frames?this.pendingMetadata.push(e):(this.pendingTracks.push(e.track),this.pendingBytes+=e.boxes.byteLength,"video"===e.track.type&&(this.videoTrack=e.track,this.pendingBoxes.push(e.boxes)),void("audio"===e.track.type&&(this.audioTrack=e.track,this.pendingBoxes.unshift(e.boxes))))}}).prototype=new H,eG.prototype.flush=function(e){var t,i,n,r,s=0,a={captions:[],captionStreams:{},metadata:[],info:{}},o=0;if(this.pendingTracks.length<this.numberOfTracks){if("VideoSegmentStream"!==e&&"AudioSegmentStream"!==e||this.remuxTracks)return;if(0===this.pendingTracks.length)return this.emittedTracks++,void(this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0))}if(this.videoTrack?(o=this.videoTrack.timelineStartInfo.pts,eQ.forEach(function(e){a.info[e]=this.videoTrack[e]},this)):this.audioTrack&&(o=this.audioTrack.timelineStartInfo.pts,e9.forEach(function(e){a.info[e]=this.audioTrack[e]},this)),this.videoTrack||this.audioTrack){for(1===this.pendingTracks.length?a.type=this.pendingTracks[0].type:a.type="combined",this.emittedTracks+=this.pendingTracks.length,n=Z(this.pendingTracks),a.initSegment=new Uint8Array(n.byteLength),a.initSegment.set(n),a.data=new Uint8Array(this.pendingBytes),r=0;r<this.pendingBoxes.length;r++)a.data.set(this.pendingBoxes[r],s),s+=this.pendingBoxes[r].byteLength;for(r=0;r<this.pendingCaptions.length;r++)(t=this.pendingCaptions[r]).startTime=el(t.startPts,o,this.keepOriginalTimestamps),t.endTime=el(t.endPts,o,this.keepOriginalTimestamps),a.captionStreams[t.stream]=!0,a.captions.push(t);for(r=0;r<this.pendingMetadata.length;r++)(i=this.pendingMetadata[r]).cueTime=el(i.pts,o,this.keepOriginalTimestamps),a.metadata.push(i);for(a.metadata.dispatchType=this.metadataStream.dispatchType,this.pendingTracks.length=0,this.videoTrack=null,this.pendingBoxes.length=0,this.pendingCaptions.length=0,this.pendingBytes=0,this.pendingMetadata.length=0,this.trigger("data",a),r=0;r<a.captions.length;r++)t=a.captions[r],this.trigger("caption",t);for(r=0;r<a.metadata.length;r++)i=a.metadata[r],this.trigger("id3Frame",i)}this.emittedTracks>=this.numberOfTracks&&(this.trigger("done"),this.emittedTracks=0)},eG.prototype.setRemux=function(e){this.remuxTracks=e},(ez=function(e){var t,i,n=this,r=!0;ez.prototype.init.call(this),e=e||{},this.baseMediaDecodeTime=e.baseMediaDecodeTime||0,this.transmuxPipeline_={},this.setupAacPipeline=function(){var r={};(this.transmuxPipeline_=r).type="aac",r.metadataStream=new eN.MetadataStream,r.aacStream=new eX,r.audioTimestampRolloverStream=new eN.TimestampRolloverStream("audio"),r.timedMetadataTimestampRolloverStream=new eN.TimestampRolloverStream("timed-metadata"),r.adtsStream=new eM,r.coalesceStream=new eG(e,r.metadataStream),r.headOfPipeline=r.aacStream,r.aacStream.pipe(r.audioTimestampRolloverStream).pipe(r.adtsStream),r.aacStream.pipe(r.timedMetadataTimestampRolloverStream).pipe(r.metadataStream).pipe(r.coalesceStream),r.metadataStream.on("timestamp",function(e){r.aacStream.setTimestamp(e.timeStamp)}),r.aacStream.on("data",function(s){"timed-metadata"!==s.type||r.audioSegmentStream||(i=i||{timelineStartInfo:{baseMediaDecodeTime:n.baseMediaDecodeTime},codec:"adts",type:"audio"},r.coalesceStream.numberOfTracks++,r.audioSegmentStream=new eW(i,e),r.audioSegmentStream.on("timingInfo",n.trigger.bind(n,"audioTimingInfo")),r.adtsStream.pipe(r.audioSegmentStream).pipe(r.coalesceStream)),n.trigger("trackinfo",{hasAudio:!!i,hasVideo:!!t})}),r.coalesceStream.on("data",this.trigger.bind(this,"data")),r.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setupTsPipeline=function(){var r={};(this.transmuxPipeline_=r).type="ts",r.metadataStream=new eN.MetadataStream,r.packetStream=new eN.TransportPacketStream,r.parseStream=new eN.TransportParseStream,r.elementaryStream=new eN.ElementaryStream,r.timestampRolloverStream=new eN.TimestampRolloverStream,r.adtsStream=new eM,r.h264Stream=new eK,r.captionStream=new eN.CaptionStream,r.coalesceStream=new eG(e,r.metadataStream),r.headOfPipeline=r.packetStream,r.packetStream.pipe(r.parseStream).pipe(r.elementaryStream).pipe(r.timestampRolloverStream),r.timestampRolloverStream.pipe(r.h264Stream),r.timestampRolloverStream.pipe(r.adtsStream),r.timestampRolloverStream.pipe(r.metadataStream).pipe(r.coalesceStream),r.h264Stream.pipe(r.captionStream).pipe(r.coalesceStream),r.elementaryStream.on("data",function(s){var a;if("metadata"===s.type){for(a=s.tracks.length;a--;)t||"video"!==s.tracks[a].type?i||"audio"!==s.tracks[a].type||((i=s.tracks[a]).timelineStartInfo.baseMediaDecodeTime=n.baseMediaDecodeTime):(t=s.tracks[a]).timelineStartInfo.baseMediaDecodeTime=n.baseMediaDecodeTime;t&&!r.videoSegmentStream&&(r.coalesceStream.numberOfTracks++,r.videoSegmentStream=new eV(t,e),r.videoSegmentStream.on("timelineStartInfo",function(e){i&&(i.timelineStartInfo=e,r.audioSegmentStream.setEarliestDts(e.dts))}),r.videoSegmentStream.on("processedGopsInfo",n.trigger.bind(n,"gopInfo")),r.videoSegmentStream.on("segmentTimingInfo",n.trigger.bind(n,"videoSegmentTimingInfo")),r.videoSegmentStream.on("baseMediaDecodeTime",function(e){i&&r.audioSegmentStream.setVideoBaseMediaDecodeTime(e)}),r.videoSegmentStream.on("timingInfo",n.trigger.bind(n,"videoTimingInfo")),r.h264Stream.pipe(r.videoSegmentStream).pipe(r.coalesceStream)),i&&!r.audioSegmentStream&&(r.coalesceStream.numberOfTracks++,r.audioSegmentStream=new eW(i,e),r.audioSegmentStream.on("timingInfo",n.trigger.bind(n,"audioTimingInfo")),r.adtsStream.pipe(r.audioSegmentStream).pipe(r.coalesceStream)),n.trigger("trackinfo",{hasAudio:!!i,hasVideo:!!t})}}),r.coalesceStream.on("data",this.trigger.bind(this,"data")),r.coalesceStream.on("id3Frame",function(e){e.dispatchType=r.metadataStream.dispatchType,n.trigger("id3Frame",e)}),r.coalesceStream.on("caption",this.trigger.bind(this,"caption")),r.coalesceStream.on("done",this.trigger.bind(this,"done"))},this.setBaseMediaDecodeTime=function(n){var r=this.transmuxPipeline_;e.keepOriginalTimestamps||(this.baseMediaDecodeTime=n),i&&(i.timelineStartInfo.dts=void 0,i.timelineStartInfo.pts=void 0,ep(i),e.keepOriginalTimestamps||(i.timelineStartInfo.baseMediaDecodeTime=n),r.audioTimestampRolloverStream&&r.audioTimestampRolloverStream.discontinuity()),t&&(r.videoSegmentStream&&(r.videoSegmentStream.gopCache_=[]),t.timelineStartInfo.dts=void 0,t.timelineStartInfo.pts=void 0,ep(t),r.captionStream.reset(),e.keepOriginalTimestamps||(t.timelineStartInfo.baseMediaDecodeTime=n)),r.timestampRolloverStream&&r.timestampRolloverStream.discontinuity()},this.setAudioAppendStart=function(e){i&&this.transmuxPipeline_.audioSegmentStream.setAudioAppendStart(e)},this.setRemux=function(t){var i=this.transmuxPipeline_;e.remux=t,i&&i.coalesceStream&&i.coalesceStream.setRemux(t)},this.alignGopsWith=function(e){t&&this.transmuxPipeline_.videoSegmentStream&&this.transmuxPipeline_.videoSegmentStream.alignGopsWith(e)},this.push=function(e){if(r){var t=eY(e);t&&"aac"!==this.transmuxPipeline_.type?this.setupAacPipeline():t||"ts"===this.transmuxPipeline_.type||this.setupTsPipeline(),r=!1}this.transmuxPipeline_.headOfPipeline.push(e)},this.flush=function(){r=!0,this.transmuxPipeline_.headOfPipeline.flush()},this.endTimeline=function(){this.transmuxPipeline_.headOfPipeline.endTimeline()},this.reset=function(){this.transmuxPipeline_.headOfPipeline&&this.transmuxPipeline_.headOfPipeline.reset()},this.resetCaptions=function(){this.transmuxPipeline_.captionStream&&this.transmuxPipeline_.captionStream.reset()}}).prototype=new H;var eJ={Transmuxer:ez,VideoSegmentStream:eV,AudioSegmentStream:eW,AUDIO_PROPERTIES:e9,VIDEO_PROPERTIES:eQ,generateVideoSegmentTimingInfo:eq};function eZ(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var te,tt,ti,tn=(te=tr,tt=[{key:"init",value:function(){var e,t;this.transmuxer&&this.transmuxer.dispose(),this.transmuxer=new eJ.Transmuxer(this.options),e=this.self,(t=this.transmuxer).on("data",function(t){var i=t.initSegment;t.initSegment={data:i.buffer,byteOffset:i.byteOffset,byteLength:i.byteLength};var n=t.data;t.data=n.buffer,e.postMessage({action:"data",segment:t,byteOffset:n.byteOffset,byteLength:n.byteLength},[t.data])}),t.captionStream&&t.captionStream.on("data",function(t){e.postMessage({action:"caption",data:t})}),t.on("done",function(t){e.postMessage({action:"done"})}),t.on("gopInfo",function(t){e.postMessage({action:"gopInfo",gopInfo:t})}),t.on("videoSegmentTimingInfo",function(t){e.postMessage({action:"videoSegmentTimingInfo",videoSegmentTimingInfo:t})})}},{key:"push",value:function(e){var t=new Uint8Array(e.data,e.byteOffset,e.byteLength);this.transmuxer.push(t)}},{key:"reset",value:function(){this.init()}},{key:"setTimestampOffset",value:function(e){var t=e.timestampOffset||0;this.transmuxer.setBaseMediaDecodeTime(Math.round(9e4*t))}},{key:"setAudioAppendStart",value:function(e){this.transmuxer.setAudioAppendStart(Math.ceil(9e4*e.appendStart))}},{key:"flush",value:function(e){this.transmuxer.flush()}},{key:"resetCaptions",value:function(){this.transmuxer.resetCaptions()}},{key:"alignGopsWith",value:function(e){this.transmuxer.alignGopsWith(e.gopsToAlignWith.slice())}}],eZ(te.prototype,tt),ti&&eZ(te,ti),tr);function tr(e,t){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,tr),this.options=t||{},this.self=e,this.init()}new function(e){e.onmessage=function(t){"init"===t.data.action&&t.data.options?this.messageHandlers=new tn(e,t.data.options):(this.messageHandlers||(this.messageHandlers=new tn(e)),t.data&&t.data.action&&"init"!==t.data.action&&this.messageHandlers[t.data.action]&&this.messageHandlers[t.data.action](t.data))}}(i)}()}),uy={videoCodec:"avc1",videoObjectTypeIndicator:".4d400d",audioProfile:"2"},u8=(ov(u$,rm.EventTarget),om(u$,[{key:"data_",value:function(e){var t=e.data.segment;t.data=new Uint8Array(t.data,e.data.byteOffset,e.data.byteLength),t.initSegment=new Uint8Array(t.initSegment.data,t.initSegment.byteOffset,t.initSegment.byteLength),function(e,t,i){var n=t.player_;if(i.captions&&i.captions.length){for(var r in e.inbandTextTracks_||(e.inbandTextTracks_={}),i.captionStreams)if(!e.inbandTextTracks_[r]){n.tech_.trigger({type:"usage",name:"hls-608"});var s=n.textTracks().getTrackById(r);e.inbandTextTracks_[r]=s||n.addRemoteTextTrack({kind:"captions",id:r,label:r},!1).track}}i.metadata&&i.metadata.length&&!e.metadataTrack_&&(e.metadataTrack_=n.addRemoteTextTrack({kind:"metadata",label:"Timed Metadata"},!1).track,e.metadataTrack_.inBandMetadataTrackDispatchType=i.metadata.dispatchType)}(this,this.mediaSource_,t),this.pendingBuffers_.push(t)}},{key:"done_",value:function(e){"closed"!==this.mediaSource_.readyState?this.processPendingSegments_():this.pendingBuffers_.length=0}},{key:"videoSegmentTimingInfo_",value:function(e){var t={start:{decode:e.start.dts/9e4,presentation:e.start.pts/9e4},end:{decode:e.end.dts/9e4,presentation:e.end.pts/9e4},baseMediaDecodeTime:e.baseMediaDecodeTime/9e4};e.prependedContentDuration&&(t.prependedContentDuration=e.prependedContentDuration/9e4),this.trigger({type:"videoSegmentTimingInfo",videoSegmentTimingInfo:t})}},{key:"createRealSourceBuffers_",value:function(){var e=this,t=["audio","video"];t.forEach(function(i){if(e[i+"Codec_"]&&!e[i+"Buffer_"]){var n=null;if(e.mediaSource_[i+"Buffer_"])(n=e.mediaSource_[i+"Buffer_"]).updating=!1;else{var r=i+'/mp4;codecs="'+e[i+"Codec_"]+'"';n=function(e,t){var i=e.addSourceBuffer(t),n=Object.create(null);function r(e){"function"==typeof i[e]?n[e]=function(){return i[e].apply(i,arguments)}:void 0===n[e]&&Object.defineProperty(n,e,{get:function(){return i[e]},set:function(t){return i[e]=t}})}for(var s in n.updating=!1,n.realBuffer_=i)r(s);return n}(e.mediaSource_.nativeMediaSource_,r),e.mediaSource_[i+"Buffer_"]=n}e[i+"Buffer_"]=n,["update","updatestart","updateend"].forEach(function(r){n.addEventListener(r,function(){if("audio"!==i||!e.audioDisabled_)return"updateend"===r&&(e[i+"Buffer_"].updating=!1),t.every(function(t){return!("audio"!==t||!e.audioDisabled_)||i===t||!e[t+"Buffer_"]||!e[t+"Buffer_"].updating})?e.trigger(r):void 0})})}})}},{key:"appendBuffer",value:function(e){if(this.bufferUpdating_=!0,this.audioBuffer_&&this.audioBuffer_.buffered.length){var t=this.audioBuffer_.buffered;this.transmuxer_.postMessage({action:"setAudioAppendStart",appendStart:t.end(t.length-1)})}this.videoBuffer_&&this.transmuxer_.postMessage({action:"alignGopsWith",gopsToAlignWith:function(e,t,i){if(null==t||!e.length)return[];var n=Math.ceil(9e4*(t-i+3)),r=void 0;for(r=0;r<e.length&&!(e[r].pts>n);r++);return e.slice(r)}(this.gopBuffer_,this.mediaSource_.player_?this.mediaSource_.player_.currentTime():null,this.timeMapping_)}),this.transmuxer_.postMessage({action:"push",data:e.buffer,byteOffset:e.byteOffset,byteLength:e.byteLength},[e.buffer]),this.transmuxer_.postMessage({action:"flush"})}},{key:"appendGopInfo_",value:function(e){this.gopBuffer_=function(e,t,i){if(!t.length)return e;if(i)return t.slice();for(var n=t[0].pts,r=0;r<e.length&&!(e[r].pts>=n);r++);return e.slice(0,r).concat(t)}(this.gopBuffer_,e.data.gopInfo,this.safeAppend_)}},{key:"remove",value:function(e,t){if(this.videoBuffer_&&(this.videoBuffer_.updating=!0,this.videoBuffer_.remove(e,t),this.gopBuffer_=function(e,t,i,n){for(var r=Math.ceil(9e4*(t-n)),s=Math.ceil(9e4*(i-n)),a=e.slice(),o=e.length;o--&&!(e[o].pts<=s););if(-1===o)return a;for(var u=o+1;u--&&!(e[u].pts<=r););return u=Math.max(u,0),a.splice(u,o-u+1),a}(this.gopBuffer_,e,t,this.timeMapping_)),!this.audioDisabled_&&this.audioBuffer_&&(this.audioBuffer_.updating=!0,this.audioBuffer_.remove(e,t)),oz(e,t,this.metadataTrack_),this.inbandTextTracks_)for(var i in this.inbandTextTracks_)oz(e,t,this.inbandTextTracks_[i])}},{key:"processPendingSegments_",value:function(){var t={video:{segments:[],bytes:0},audio:{segments:[],bytes:0},captions:[],metadata:[]};if(!this.pendingBuffers_.length)return this.trigger("updateend"),void(this.bufferUpdating_=!1);t=this.pendingBuffers_.reduce(function(e,t){var i=t.type,n=t.data,r=t.initSegment;return e[i].segments.push(n),e[i].bytes+=n.byteLength,e[i].initSegment=r,t.captions&&(e.captions=e.captions.concat(t.captions)),t.info&&(e[i].info=t.info),t.metadata&&(e.metadata=e.metadata.concat(t.metadata)),e},t),this.videoBuffer_||this.audioBuffer_||(0===t.video.bytes&&(this.videoCodec_=null),0===t.audio.bytes&&(this.audioCodec_=null),this.createRealSourceBuffers_()),t.audio.info&&this.mediaSource_.trigger({type:"audioinfo",info:t.audio.info}),t.video.info&&this.mediaSource_.trigger({type:"videoinfo",info:t.video.info}),this.appendAudioInitSegment_&&(!this.audioDisabled_&&this.audioBuffer_&&(t.audio.segments.unshift(t.audio.initSegment),t.audio.bytes+=t.audio.initSegment.byteLength),this.appendAudioInitSegment_=!1);var i=!1;this.videoBuffer_&&t.video.bytes?(t.video.segments.unshift(t.video.initSegment),t.video.bytes+=t.video.initSegment.byteLength,this.concatAndAppendSegments_(t.video,this.videoBuffer_)):this.videoBuffer_&&(this.audioDisabled_||!this.audioBuffer_)&&(i=!0),function t(i,n,r){var s=e.WebKitDataCue||e.VTTCue;if(n&&n.forEach(function(e){var t=e.stream;this.inbandTextTracks_[t].addCue(new s(e.startTime+this.timestampOffset,e.endTime+this.timestampOffset,e.text))},i),r){var a=oG(i.mediaSource_.duration);if(r.forEach(function(t){var i=t.cueTime+this.timestampOffset;!("number"!=typeof i||e.isNaN(i)||i<0)&&i<1/0&&t.frames.forEach(function(e){var t,n=new s(i,i,e.value||e.url||e.data||"");n.frame=e,n.value=e,Object.defineProperties((t=n).frame,{id:{get:function(){return rm.log.warn("cue.frame.id is deprecated. Use cue.value.key instead."),t.value.key}},value:{get:function(){return rm.log.warn("cue.frame.value is deprecated. Use cue.value.data instead."),t.value.data}},privateData:{get:function(){return rm.log.warn("cue.frame.privateData is deprecated. Use cue.value.data instead."),t.value.data}}}),this.metadataTrack_.addCue(n)},this)},i),i.metadataTrack_&&i.metadataTrack_.cues&&i.metadataTrack_.cues.length){for(var o=i.metadataTrack_.cues,u=[],l=0;l<o.length;l++)o[l]&&u.push(o[l]);var c=u.reduce(function(e,t){var i=e[t.startTime]||[];return i.push(t),e[t.startTime]=i,e},{}),h=Object.keys(c).sort(function(e,t){return Number(e)-Number(t)});h.forEach(function(e,t){var i=c[e],n=Number(h[t+1])||a;i.forEach(function(e){e.endTime=n})})}}}(this,t.captions,t.metadata),!this.audioDisabled_&&this.audioBuffer_&&this.concatAndAppendSegments_(t.audio,this.audioBuffer_),this.pendingBuffers_.length=0,i&&this.trigger("updateend"),this.bufferUpdating_=!1}},{key:"concatAndAppendSegments_",value:function(e,t){var i=0,n=void 0;if(e.bytes){n=new Uint8Array(e.bytes),e.segments.forEach(function(e){n.set(e,i),i+=e.byteLength});try{t.updating=!0,t.appendBuffer(n)}catch(r){this.mediaSource_.player_&&this.mediaSource_.player_.error({code:-3,type:"APPEND_BUFFER_ERR",message:r.message,originalError:r})}}}},{key:"abort",value:function(){this.videoBuffer_&&this.videoBuffer_.abort(),!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.abort(),this.transmuxer_&&this.transmuxer_.postMessage({action:"reset"}),this.pendingBuffers_.length=0,this.bufferUpdating_=!1}},{key:"dispose",value:function(){this.transmuxer_&&this.transmuxer_.terminate(),this.trigger("dispose"),this.off()}}]),u$);function u$(e,t){of(this,u$);var i=oy(this,(u$.__proto__||Object.getPrototypeOf(u$)).call(this,rm.EventTarget));i.timestampOffset_=0,i.pendingBuffers_=[],i.bufferUpdating_=!1,i.mediaSource_=e,i.codecs_=t,i.audioCodec_=null,i.videoCodec_=null,i.audioDisabled_=!1,i.appendAudioInitSegment_=!0,i.gopBuffer_=[],i.timeMapping_=0,i.safeAppend_=11<=rm.browser.IE_VERSION;var n={remux:!1,alignGopsAtEnd:i.safeAppend_};return i.codecs_.forEach(function(e){um(e)?i.audioCodec_=e:ug(e)&&(i.videoCodec_=e)}),i.transmuxer_=new uv,i.transmuxer_.postMessage({action:"init",options:n}),i.transmuxer_.onmessage=function(e){return"data"===e.data.action?i.data_(e):"done"===e.data.action?i.done_(e):"gopInfo"===e.data.action?i.appendGopInfo_(e):"videoSegmentTimingInfo"===e.data.action?i.videoSegmentTimingInfo_(e.data.videoSegmentTimingInfo):void 0},Object.defineProperty(i,"timestampOffset",{get:function(){return this.timestampOffset_},set:function(e){"number"==typeof e&&0<=e&&(this.timestampOffset_=e,this.appendAudioInitSegment_=!0,this.gopBuffer_.length=0,this.timeMapping_=0,this.transmuxer_.postMessage({action:"setTimestampOffset",timestampOffset:e}))}}),Object.defineProperty(i,"appendWindowStart",{get:function(){return(this.videoBuffer_||this.audioBuffer_).appendWindowStart},set:function(e){this.videoBuffer_&&(this.videoBuffer_.appendWindowStart=e),this.audioBuffer_&&(this.audioBuffer_.appendWindowStart=e)}}),Object.defineProperty(i,"updating",{get:function(){return!!(this.bufferUpdating_||!this.audioDisabled_&&this.audioBuffer_&&this.audioBuffer_.updating||this.videoBuffer_&&this.videoBuffer_.updating)}}),Object.defineProperty(i,"buffered",{get:function(){return function(e,t,i){var n=null,r=null,s=0,a=[],o=[];if(!e&&!t)return rm.createTimeRange();if(!e)return t.buffered;if(!t||i)return e.buffered;if(0===e.buffered.length&&0===t.buffered.length)return rm.createTimeRange();for(var u=e.buffered,l=t.buffered,c=u.length;c--;)a.push({time:u.start(c),type:"start"}),a.push({time:u.end(c),type:"end"});for(c=l.length;c--;)a.push({time:l.start(c),type:"start"}),a.push({time:l.end(c),type:"end"});for(a.sort(function(e,t){return e.time-t.time}),c=0;c<a.length;c++)"start"===a[c].type?2==++s&&(n=a[c].time):"end"===a[c].type&&1==--s&&(r=a[c].time),null!==n&&null!==r&&(o.push([n,r]),r=n=null);return rm.createTimeRanges(o)}(this.videoBuffer_,this.audioBuffer_,this.audioDisabled_)}}),i}var ub=(ov(uT,rm.EventTarget),om(uT,[{key:"addSeekableRange_",value:function(e,t){var i=void 0;if(this.duration!==1/0)throw(i=Error("MediaSource.addSeekableRange() can only be invoked when the duration is Infinity")).name="InvalidStateError",i.code=11,i;(t>this.nativeMediaSource_.duration||isNaN(this.nativeMediaSource_.duration))&&(this.nativeMediaSource_.duration=t)}},{key:"addSourceBuffer",value:function(e){var t=void 0,i=uf(e);if(/^(video|audio)\/mp2t$/i.test(i.type)){var n=[];i.parameters&&i.parameters.codecs&&(n=(n=uh(n=i.parameters.codecs.split(","))).filter(function(e){return um(e)||ug(e)})),0===n.length&&(n=["avc1.4d400d","mp4a.40.2"]),t=new u8(this,n),0!==this.sourceBuffers.length&&(this.sourceBuffers[0].createRealSourceBuffers_(),t.createRealSourceBuffers_(),this.sourceBuffers[0].audioDisabled_=!0)}else t=this.nativeMediaSource_.addSourceBuffer(e);return this.sourceBuffers.push(t),t}},{key:"dispose",value:function(){this.trigger("dispose"),this.off(),this.sourceBuffers.forEach(function(e){e.dispose&&e.dispose()}),this.sourceBuffers.length=0}}]),uT);function uT(){of(this,uT);var i=oy(this,(uT.__proto__||Object.getPrototypeOf(uT)).call(this)),n=void 0;for(n in i.nativeMediaSource_=new e.MediaSource,i.nativeMediaSource_)n in uT.prototype||"function"!=typeof i.nativeMediaSource_[n]||(i[n]=i.nativeMediaSource_[n].bind(i.nativeMediaSource_));return i.duration_=NaN,Object.defineProperty(i,"duration",{get:function(){return this.duration_===1/0?this.duration_:this.nativeMediaSource_.duration},set:function(e){(this.duration_=e)===1/0||(this.nativeMediaSource_.duration=e)}}),Object.defineProperty(i,"seekable",{get:function(){return this.duration_===1/0?rm.createTimeRanges([[0,this.nativeMediaSource_.duration]]):this.nativeMediaSource_.seekable}}),Object.defineProperty(i,"readyState",{get:function(){return this.nativeMediaSource_.readyState}}),Object.defineProperty(i,"activeSourceBuffers",{get:function(){return this.activeSourceBuffers_}}),i.sourceBuffers=[],i.activeSourceBuffers_=[],i.updateActiveSourceBuffers_=function(){if(i.activeSourceBuffers_.length=0,1===i.sourceBuffers.length){var e=i.sourceBuffers[0];return e.appendAudioInitSegment_=!0,e.audioDisabled_=!e.audioCodec_,void i.activeSourceBuffers_.push(e)}for(var t=!1,n=!0,r=0;r<i.player_.audioTracks().length;r++){var s=i.player_.audioTracks()[r];if(s.enabled&&"main"!==s.kind){n=(t=!0,!1);break}}i.sourceBuffers.forEach(function(e,r){if(e.appendAudioInitSegment_=!0,e.videoCodec_&&e.audioCodec_)e.audioDisabled_=t;else if(e.videoCodec_&&!e.audioCodec_)e.audioDisabled_=!0,n=!1;else if(!e.videoCodec_&&e.audioCodec_&&(e.audioDisabled_=r?n:!n,e.audioDisabled_))return;i.activeSourceBuffers_.push(e)})},i.onPlayerMediachange_=function(){i.sourceBuffers.forEach(function(e){e.appendAudioInitSegment_=!0})},i.onHlsReset_=function(){i.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.postMessage({action:"resetCaptions"})})},i.onHlsSegmentTimeMapping_=function(e){i.sourceBuffers.forEach(function(t){return t.timeMapping_=e.mapping})},["sourceopen","sourceclose","sourceended"].forEach(function(e){this.nativeMediaSource_.addEventListener(e,this.trigger.bind(this))},i),i.on("sourceopen",function(e){var n=t.querySelector('[src="'+i.url_+'"]');n&&(i.player_=rm(n.parentNode),i.player_&&(i.player_.tech_.on("hls-reset",i.onHlsReset_),i.player_.tech_.on("hls-segment-time-mapping",i.onHlsSegmentTimeMapping_),i.player_.audioTracks&&i.player_.audioTracks()&&(i.player_.audioTracks().on("change",i.updateActiveSourceBuffers_),i.player_.audioTracks().on("addtrack",i.updateActiveSourceBuffers_),i.player_.audioTracks().on("removetrack",i.updateActiveSourceBuffers_)),i.player_.on("mediachange",i.onPlayerMediachange_)))}),i.on("sourceended",function(e){for(var t=oG(i.duration),n=0;n<i.sourceBuffers.length;n++){var r=i.sourceBuffers[n],s=r.metadataTrack_&&r.metadataTrack_.cues;s&&s.length&&(s[s.length-1].endTime=t)}}),i.on("sourceclose",function(e){this.sourceBuffers.forEach(function(e){e.transmuxer_&&e.transmuxer_.terminate()}),this.sourceBuffers.length=0,this.player_&&(this.player_.audioTracks&&this.player_.audioTracks()&&(this.player_.audioTracks().off("change",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("addtrack",this.updateActiveSourceBuffers_),this.player_.audioTracks().off("removetrack",this.updateActiveSourceBuffers_)),this.player_.el_&&this.player_.off("mediachange",this.onPlayerMediachange_),this.player_.tech_&&this.player_.tech_.el_&&(this.player_.tech_.off("hls-reset",this.onHlsReset_),this.player_.tech_.off("hls-segment-time-mapping",this.onHlsSegmentTimeMapping_)))}),i}var u_=0;function uS(e,t){var i=rm.mediaSources[e];if(!i)throw Error("Media Source not found (Video.js)");i.trigger({type:"sourceopen",swfId:t})}function uk(){return!!e.MediaSource&&!!e.MediaSource.isTypeSupported&&e.MediaSource.isTypeSupported('video/mp4;codecs="avc1.4d400d,mp4a.40.2"')}function uC(){if(this.MediaSource={open:uS,supportsNativeMediaSources:uk},uk())return new ub;throw Error("Cannot use create a virtual MediaSource for this video")}rm.mediaSources={},uC.open=uS,uC.supportsNativeMediaSources=uk;var uE={createObjectURL:function(t){var i=void 0;return t instanceof ub?(i=e.URL.createObjectURL(t.nativeMediaSource_),t.url_=i):t instanceof ub?(i="blob:vjs-media-source/"+u_,u_++,rm.mediaSources[i]=t,i):(i=e.URL.createObjectURL(t),t.url_=i)}};function uw(e,t){for(var i=void 0,n=uL(e,{duration:t.duration,minimumUpdatePeriod:t.minimumUpdatePeriod}),r=0;r<t.playlists.length;r++){var s=o$(n,t.playlists[r]);s?n=s:i=!0}return o8(t,function(e,t,r,s){if(e.playlists&&e.playlists.length){var a=e.playlists[0].id,o=o$(n,e.playlists[0]);o&&((n=o).mediaGroups[t][r][s].playlists[0]=n.playlists[a],i=!1)}}),i?null:n}function u0(e){var t=e.byterange.offset+e.byterange.length-1;return e.uri+"-"+e.byterange.offset+"-"+t}function ux(e,t){var i,n,r={};for(var s in e){var a=e[s].sidx;if(a){var o=u0(a);if(!t[o])break;i=t[o].sidxInfo,n=a,(Boolean(!i.map&&!n.map)||Boolean(i.map&&n.map&&i.map.byterange.offset===n.map.byterange.offset&&i.map.byterange.length===n.map.byterange.length))&&i.uri===n.uri&&i.byterange.offset===n.byterange.offset&&i.byterange.length===n.byterange.length&&(r[o]=t[o])}}return r}function uP(e,t,i,n,r){var s={uri:op(n.handleManifestRedirects,e.resolvedUri),byterange:e.byterange,playlist:t};return i(rm.mergeOptions(s,{responseType:"arraybuffer",headers:o1(s)}),r)}rm.MediaSource=uC,rm.URL=uE;var uL=rm.mergeOptions,uI=(ov(uD,rm.EventTarget),om(uD,[{key:"setupChildLoader",value:function(e,t){this.masterPlaylistLoader_=e,this.childPlaylist_=t}},{key:"dispose",value:function(){this.trigger("dispose"),this.stopRequest(),this.loadedPlaylists_={},e.clearTimeout(this.minimumUpdatePeriodTimeout_),e.clearTimeout(this.mediaRequest_),e.clearTimeout(this.mediaUpdateTimeout),this.off()}},{key:"hasPendingRequest",value:function(){return this.request||this.mediaRequest_}},{key:"stopRequest",value:function(){if(this.request){var e=this.request;this.request=null,e.onreadystatechange=null,e.abort()}}},{key:"sidxRequestFinished_",value:function(e,t,i,n){var r=this;return function(s,a){if(r.request){if(r.request=null,s)return r.error={status:a.status,message:"DASH playlist request error at URL: "+e.uri,response:a.response,code:2},i&&(r.state=i),r.trigger("error"),n(t,null);var o=new Uint8Array(a.response);return n(t,ak.parseSidx(o.subarray(8)))}}}},{key:"media",value:function(t){var i=this;if(!t)return this.media_;if("HAVE_NOTHING"===this.state)throw Error("Cannot switch media playlist from "+this.state);var n=this.state;if("string"==typeof t){if(!this.master.playlists[t])throw Error("Unknown playlist URI: "+t);t=this.master.playlists[t]}var r=!this.media_||t.id!==this.media_.id;if(r&&this.loadedPlaylists_[t.id]&&this.loadedPlaylists_[t.id].endList)return this.state="HAVE_METADATA",this.media_=t,void(r&&(this.trigger("mediachanging"),this.trigger("mediachange")));if(r){if(this.media_&&this.trigger("mediachanging"),t.sidx){var s=void 0,a=void 0;a=this.masterPlaylistLoader_?(s=this.masterPlaylistLoader_.master,this.masterPlaylistLoader_.sidxMapping_):(s=this.master,this.sidxMapping_);var o=u0(t.sidx);a[o]={sidxInfo:t.sidx},this.request=uP(t.sidx,t,this.hls_.xhr,{handleManifestRedirects:this.handleManifestRedirects},this.sidxRequestFinished_(t,s,n,function(e,r){if(!e||!r)throw Error("failed to request sidx");a[o].sidx=r,i.haveMetadata({startingState:n,playlist:e.playlists[t.id]})}))}else this.mediaRequest_=e.setTimeout(this.haveMetadata.bind(this,{startingState:n,playlist:t}),0)}}},{key:"haveMetadata",value:function(e){var t=e.startingState,i=e.playlist;this.state="HAVE_METADATA",this.loadedPlaylists_[i.id]=i,this.mediaRequest_=null,this.refreshMedia_(i.id),"HAVE_MASTER"===t?this.trigger("loadedmetadata"):this.trigger("mediachange")}},{key:"pause",value:function(){this.stopRequest(),e.clearTimeout(this.mediaUpdateTimeout),e.clearTimeout(this.minimumUpdatePeriodTimeout_),"HAVE_NOTHING"===this.state&&(this.started=!1)}},{key:"load",value:function(t){var i=this;e.clearTimeout(this.mediaUpdateTimeout),e.clearTimeout(this.minimumUpdatePeriodTimeout_);var n=this.media();if(t){var r=n?n.targetDuration/2*1e3:5e3;this.mediaUpdateTimeout=e.setTimeout(function(){return i.load()},r)}else this.started?n&&!n.endList?this.trigger("mediaupdatetimeout"):this.trigger("loadedplaylist"):this.start()}},{key:"parseMasterXml",value:function(){var e=sJ(this.masterXml_,{manifestUri:this.srcUrl,clientOffset:this.clientOffset_,sidxMapping:this.sidxMapping_});e.uri=this.srcUrl;for(var t=0;t<e.playlists.length;t++){var i="placeholder-uri-"+t;e.playlists[t].uri=i}return o8(e,function(t,i,n,r){if(t.playlists&&t.playlists.length){var s="placeholder-uri-"+i+"-"+n+"-"+r,a=ob(0,s);t.playlists[0].uri=s,t.playlists[0].id=a,e.playlists[a]=t.playlists[0],e.playlists[s]=t.playlists[0]}}),oT(e),o_(e),e}},{key:"start",value:function(){var t=this;this.started=!0,this.masterPlaylistLoader_?this.mediaRequest_=e.setTimeout(this.haveMaster_.bind(this),0):this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(e,i){if(t.request){if(t.request=null,e)return t.error={status:i.status,message:"DASH playlist request error at URL: "+t.srcUrl,responseText:i.responseText,code:2},"HAVE_NOTHING"===t.state&&(t.started=!1),t.trigger("error");t.masterXml_=i.responseText,i.responseHeaders&&i.responseHeaders.date?t.masterLoaded_=Date.parse(i.responseHeaders.date):t.masterLoaded_=Date.now(),t.srcUrl=op(t.handleManifestRedirects,t.srcUrl,i),t.syncClientServerClock_(t.onClientServerClockSync_.bind(t))}})}},{key:"syncClientServerClock_",value:function(e){var t,i=this,n=function(e){var t=sz(e,"UTCTiming")[0];if(!t)return null;var i=sK(t);switch(i.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":i.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":i.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":i.method="DIRECT",i.value=Date.parse(i.value);break;default:throw Error("UNSUPPORTED_UTC_TIMING_SCHEME")}return i}(sQ(t=this.masterXml_));return null===n?(this.clientOffset_=this.masterLoaded_-Date.now(),e()):"DIRECT"===n.method?(this.clientOffset_=n.value-Date.now(),e()):void(this.request=this.hls_.xhr({uri:od(this.srcUrl,n.value),method:n.method,withCredentials:this.withCredentials},function(t,r){if(i.request){if(t)return i.clientOffset_=i.masterLoaded_-Date.now(),e();var s=void 0;s="HEAD"===n.method?r.responseHeaders&&r.responseHeaders.date?Date.parse(r.responseHeaders.date):i.masterLoaded_:Date.parse(r.responseText),i.clientOffset_=s-Date.now(),e()}}))}},{key:"haveMaster_",value:function(){this.state="HAVE_MASTER",this.mediaRequest_=null,this.masterPlaylistLoader_?this.media_||this.media(this.childPlaylist_):(this.master=this.parseMasterXml(),this.trigger("loadedplaylist"))}},{key:"onClientServerClockSync_",value:function(){var t=this;this.haveMaster_(),this.hasPendingRequest()||this.media_||this.media(this.master.playlists[0]),this.master&&this.master.minimumUpdatePeriod&&(this.minimumUpdatePeriodTimeout_=e.setTimeout(function(){t.trigger("minimumUpdatePeriod")},this.master.minimumUpdatePeriod))}},{key:"refreshXml_",value:function(){var t=this;this.request=this.hls_.xhr({uri:this.srcUrl,withCredentials:this.withCredentials},function(i,n){if(t.request){if(t.request=null,i)return t.error={status:n.status,message:"DASH playlist request error at URL: "+t.srcUrl,responseText:n.responseText,code:2},"HAVE_NOTHING"===t.state&&(t.started=!1),t.trigger("error");t.masterXml_=n.responseText,t.sidxMapping_=(r=t.masterXml_,s=t.srcUrl,a=t.clientOffset_,o=t.sidxMapping_,u=sJ(r,{manifestUri:s,clientOffset:a}),l=ux(u.playlists,o),o8(u,function(e,t,i,n){e.playlists&&e.playlists.length&&(l=uL(l,ux(e.playlists,o)))}),l);var r,s,a,o,u,l,c=t.parseMasterXml(),h=uw(t.master,c),d=t.media().sidx;if(h){if(d){var p=u0(d);if(!t.sidxMapping_[p]){var f=t.media();t.request=uP(f.sidx,f,t.hls_.xhr,{handleManifestRedirects:t.handleManifestRedirects},t.sidxRequestFinished_(f,c,t.state,function(i,n){if(!i||!n)throw Error("failed to request sidx on minimumUpdatePeriod");t.sidxMapping_[p].sidx=n,t.minimumUpdatePeriodTimeout_=e.setTimeout(function(){t.trigger("minimumUpdatePeriod")},t.master.minimumUpdatePeriod),t.refreshMedia_(t.media().id)}))}}else t.master=h}t.minimumUpdatePeriodTimeout_=e.setTimeout(function(){t.trigger("minimumUpdatePeriod")},t.master.minimumUpdatePeriod)}})}},{key:"refreshMedia_",value:function(t){var i=this;if(!t)throw Error("refreshMedia_ must take a media id");var n=void 0,r=void 0;r=this.masterPlaylistLoader_?(n=this.masterPlaylistLoader_.master,this.masterPlaylistLoader_.parseMasterXml()):(n=this.master,this.parseMasterXml());var s=uw(n,r);s?(this.masterPlaylistLoader_?this.masterPlaylistLoader_.master=s:this.master=s,this.media_=s.playlists[t]):(this.media_=r.playlists[t],this.trigger("playlistunchanged")),this.media().endList||(this.mediaUpdateTimeout=e.setTimeout(function(){i.trigger("mediaupdatetimeout")},oS(this.media(),!!s))),this.trigger("loadedplaylist")}}]),uD);function uD(e,t){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},n=arguments[3];of(this,uD);var r=oy(this,(uD.__proto__||Object.getPrototypeOf(uD)).call(this)),s=i.withCredentials,a=i.handleManifestRedirects;if(r.hls_=t,r.withCredentials=void 0!==s&&s,r.handleManifestRedirects=void 0!==a&&a,!e)throw Error("A non-empty playlist URL or playlist is required");return r.on("minimumUpdatePeriod",function(){r.refreshXml_()}),r.on("mediaupdatetimeout",function(){r.refreshMedia_(r.media().id)}),r.state="HAVE_NOTHING",r.loadedPlaylists_={},"string"==typeof e?(r.srcUrl=e,r.sidxMapping_={},oy(r)):(r.setupChildLoader(n,e),r)}function uA(e){return rm.log.debug?rm.log.debug.bind(rm,"VHS:",e+" >"):function(){}}function uO(){}var u2=(om(uR,[{key:"createSourceBuffer_",value:function(e,t){var i=this;this.sourceBuffer_=this.mediaSource.addSourceBuffer(e),this.logger_("created SourceBuffer"),t&&(t.trigger("sourcebufferadded"),this.mediaSource.sourceBuffers.length<2)?t.on("sourcebufferadded",function(){i.start_()}):this.start_()}},{key:"start_",value:function(){var e=this;this.started_=!0,this.onUpdateendCallback_=function(){var t=e.pendingCallback_;e.pendingCallback_=null,e.sourceBuffer_.removing=!1,e.logger_("buffered ["+oV(e.buffered())+"]"),t&&t(),e.runCallback_()},this.sourceBuffer_.addEventListener("updateend",this.onUpdateendCallback_),this.runCallback_()}},{key:"abort",value:function(e){var t=this;this.processedAppend_&&this.queueCallback_(function(){t.sourceBuffer_.abort()},e)}},{key:"appendBuffer",value:function(e,t){var i=this;this.processedAppend_=!0,this.queueCallback_(function(){e.videoSegmentTimingInfoCallback&&i.sourceBuffer_.addEventListener("videoSegmentTimingInfo",e.videoSegmentTimingInfoCallback),i.sourceBuffer_.appendBuffer(e.bytes)},function(){e.videoSegmentTimingInfoCallback&&i.sourceBuffer_.removeEventListener("videoSegmentTimingInfo",e.videoSegmentTimingInfoCallback),t()})}},{key:"buffered",value:function(){return this.sourceBuffer_?this.sourceBuffer_.buffered:rm.createTimeRanges()}},{key:"remove",value:function(e,t,i){var n=this,r=2<arguments.length&&void 0!==i?i:uO;this.processedAppend_&&this.queueCallback_(function(){n.logger_("remove ["+e+" => "+t+"]"),n.sourceBuffer_.removing=!0,n.sourceBuffer_.remove(e,t)},r)}},{key:"updating",value:function(){return!this.sourceBuffer_||this.sourceBuffer_.updating||!!this.pendingCallback_&&this.pendingCallback_!==uO}},{key:"timestampOffset",value:function(e){var t=this;return void 0!==e&&(this.queueCallback_(function(){t.sourceBuffer_.timestampOffset=e,t.runCallback_()}),this.timestampOffset_=e),this.timestampOffset_}},{key:"queueCallback_",value:function(e,t){this.callbacks_.push([e.bind(this),t]),this.runCallback_()}},{key:"runCallback_",value:function(){var e=void 0;!this.updating()&&this.callbacks_.length&&this.started_&&(e=this.callbacks_.shift(),this.pendingCallback_=e[1],e[0]())}},{key:"dispose",value:function(){function e(){t.sourceBuffer_&&"open"===t.mediaSource.readyState&&t.sourceBuffer_.abort(),t.sourceBuffer_.removeEventListener("updateend",e)}var t=this;this.sourceBuffer_.removeEventListener("updateend",this.onUpdateendCallback_),this.sourceBuffer_.removing?this.sourceBuffer_.addEventListener("updateend",e):e()}}]),uR);function uR(e,t,i,n){of(this,uR),this.callbacks_=[],this.pendingCallback_=null,this.timestampOffset_=0,this.mediaSource=e,this.processedAppend_=!1,this.type_=i,this.mimeType_=t,this.logger_=uA("SourceUpdater["+i+"]["+t+"]"),"closed"===e.readyState?e.addEventListener("sourceopen",this.createSourceBuffer_.bind(this,t,n)):this.createSourceBuffer_(t,n)}function uN(e){e.forEach(function(e){e.abort()})}function u3(e,t){return t.timedout?{status:t.status,message:"HLS request timed-out at URL: "+t.uri,code:uj,xhr:t}:t.aborted?{status:t.status,message:"HLS request aborted at URL: "+t.uri,code:uF,xhr:t}:e?{status:t.status,message:"HLS request errored at URL: "+t.uri,code:uB,xhr:t}:null}function u4(t,i){var n;return t&&(n=e.getComputedStyle(t))?n[i]:""}function uU(e,t){var i=e.slice();e.sort(function(e,n){var r=t(e,n);return 0===r?i.indexOf(e)-i.indexOf(n):r})}function u1(t,i){var n=void 0,r=void 0;return t.attributes.BANDWIDTH&&(n=t.attributes.BANDWIDTH),n=n||e.Number.MAX_VALUE,i.attributes.BANDWIDTH&&(r=i.attributes.BANDWIDTH),n-(r=r||e.Number.MAX_VALUE)}function uM(e){return"number"==typeof e&&isFinite(e)}var u6={GOAL_BUFFER_LENGTH:30,MAX_GOAL_BUFFER_LENGTH:60,GOAL_BUFFER_LENGTH_RATE:1,INITIAL_BANDWIDTH:4194304,BANDWIDTH_VARIANCE:1.2,BUFFER_LOW_WATER_LINE:0,MAX_BUFFER_LOW_WATER_LINE:30,BUFFER_LOW_WATER_LINE_RATE:1},uB=2,uj=-101,uF=-102,u7=(ov(u5,rm.EventTarget),om(u5,[{key:"resetStats_",value:function(){this.mediaBytesTransferred=0,this.mediaRequests=0,this.mediaRequestsAborted=0,this.mediaRequestsTimedout=0,this.mediaRequestsErrored=0,this.mediaTransferDuration=0,this.mediaSecondsLoaded=0}},{key:"dispose",value:function(){this.trigger("dispose"),this.state="DISPOSED",this.pause(),this.abort_(),this.sourceUpdater_&&this.sourceUpdater_.dispose(),this.resetStats_(),this.captionParser_&&this.captionParser_.reset(),this.checkBufferTimeout_&&e.clearTimeout(this.checkBufferTimeout_),this.syncController_&&this.triggerSyncInfoUpdate_&&this.syncController_.off("syncinfoupdate",this.triggerSyncInfoUpdate_),this.off()}},{key:"abort",value:function(){"WAITING"===this.state?(this.abort_(),this.state="READY",this.paused()||this.monitorBuffer_()):this.pendingSegment_&&(this.pendingSegment_=null)}},{key:"abort_",value:function(){this.pendingSegment_&&this.pendingSegment_.abortRequests(),this.pendingSegment_=null}},{key:"error",value:function(e){return void 0!==e&&(this.error_=e),this.pendingSegment_=null,this.error_}},{key:"endOfStream",value:function(){this.ended_=!0,this.pause(),this.trigger("ended")}},{key:"buffered_",value:function(){return this.sourceUpdater_?this.sourceUpdater_.buffered():rm.createTimeRanges()}},{key:"initSegment",value:function(e,t){var i=1<arguments.length&&void 0!==t&&t;if(!e)return null;var n=oj(e),r=this.initSegments_[n];return i&&!r&&e.bytes&&(this.initSegments_[n]=r={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:e.bytes,timescales:e.timescales,videoTrackIds:e.videoTrackIds}),r||e}},{key:"segmentKey",value:function(e,t){var i=1<arguments.length&&void 0!==t&&t;if(!e)return null;var n=oF(e),r=this.keyCache_[n];this.cacheEncryptionKeys_&&i&&!r&&e.bytes&&(this.keyCache_[n]=r={resolvedUri:e.resolvedUri,bytes:e.bytes});var s={resolvedUri:(r||e).resolvedUri};return r&&(s.bytes=r.bytes),s}},{key:"couldBeginLoading_",value:function(){return this.playlist_&&(this.sourceUpdater_||this.mimeType_&&"INIT"===this.state)&&!this.paused()}},{key:"load",value:function(){if(this.monitorBuffer_(),this.playlist_){if(this.syncController_.setDateTimeMapping(this.playlist_),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();this.couldBeginLoading_()&&("READY"===this.state||"INIT"===this.state)&&(this.state="READY")}}},{key:"init_",value:function(){return this.state="READY",this.sourceUpdater_=new u2(this.mediaSource_,this.mimeType_,this.loaderType_,this.sourceBufferEmitter_),this.resetEverything(),this.monitorBuffer_()}},{key:"playlist",value:function(e,t){var i=1<arguments.length&&void 0!==t?t:{};if(e){var n=this.playlist_,r=this.pendingSegment_;this.playlist_=e,this.xhrOptions_=i,"INIT"===this.state&&(e.syncInfo={mediaSequence:e.mediaSequence,time:0});var s=null;if(n&&(n.id?s=n.id:n.uri&&(s=n.uri)),this.logger_("playlist update ["+s+" => "+(e.id||e.uri)+"]"),this.trigger("syncinfoupdate"),"INIT"===this.state&&this.couldBeginLoading_())return this.init_();if(n&&n.uri===e.uri){var a=e.mediaSequence-n.mediaSequence;this.logger_("live window shift ["+a+"]"),null!==this.mediaIndex&&(this.mediaIndex-=a),r&&(r.mediaIndex-=a,0<=r.mediaIndex&&(r.segment=e.segments[r.mediaIndex])),this.syncController_.saveExpiredSegmentInfo(n,e)}else null!==this.mediaIndex&&this.resyncLoader()}}},{key:"pause",value:function(){this.checkBufferTimeout_&&(e.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=null)}},{key:"paused",value:function(){return null===this.checkBufferTimeout_}},{key:"mimeType",value:function(e,t){this.mimeType_||(this.mimeType_=e,this.sourceBufferEmitter_=t,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_())}},{key:"resetEverything",value:function(e){this.ended_=!1,this.resetLoader(),this.remove(0,1/0,e),this.captionParser_&&this.captionParser_.clearAllCaptions(),this.trigger("reseteverything")}},{key:"resetLoader",value:function(){this.fetchAtBuffer_=!1,this.resyncLoader()}},{key:"resyncLoader",value:function(){this.mediaIndex=null,this.syncPoint_=null,this.abort()}},{key:"remove",value:function(e,t,i){if(t===1/0&&(t=this.duration_()),this.sourceUpdater_&&this.sourceUpdater_.remove(e,t,i),oz(e,t,this.segmentMetadataTrack_),this.inbandTextTracks_)for(var n in this.inbandTextTracks_)oz(e,t,this.inbandTextTracks_[n])}},{key:"monitorBuffer_",value:function(){this.checkBufferTimeout_&&e.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=e.setTimeout(this.monitorBufferTick_.bind(this),1)}},{key:"monitorBufferTick_",value:function(){"READY"===this.state&&this.fillBuffer_(),this.checkBufferTimeout_&&e.clearTimeout(this.checkBufferTimeout_),this.checkBufferTimeout_=e.setTimeout(this.monitorBufferTick_.bind(this),500)}},{key:"fillBuffer_",value:function(){if(!this.sourceUpdater_.updating()){this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var e=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);e&&(this.isEndOfStream_(e.mediaIndex)?this.endOfStream():(e.mediaIndex!==this.playlist_.segments.length-1||"ended"!==this.mediaSource_.readyState||this.seeking_())&&(e.timeline!==this.currentTimeline_&&(this.syncController_.reset(),e.timestampOffset=e.startOfSegment,this.captionParser_&&this.captionParser_.clearAllCaptions()),this.loadSegment_(e)))}}},{key:"isEndOfStream_",value:function(e,t){return function(e,t,i){if(!e||!t)return!1;var n=i===e.segments.length;return e.endList&&"open"===t.readyState&&n}(1<arguments.length&&void 0!==t?t:this.playlist_,this.mediaSource_,e)&&!this.sourceUpdater_.updating()}},{key:"checkBuffer_",value:function(e,t,i,n,r,s){var a=0,o=void 0;e.length&&(a=e.end(e.length-1));var u=Math.max(0,a-r);if(!t.segments.length||u>=this.goalBufferLength_()||!n&&1<=u)return null;if(null===s)return i=this.getSyncSegmentCandidate_(t),this.generateSegmentInfo_(t,i,null,!0);if(null!==i)return t.segments[i],o=a,this.generateSegmentInfo_(t,i+1,o,!1);if(this.fetchAtBuffer_){var l=o9.getMediaInfoForTime(t,a,s.segmentIndex,s.time);i=l.mediaIndex,o=l.startTime}else{var c=o9.getMediaInfoForTime(t,r,s.segmentIndex,s.time);i=c.mediaIndex,o=c.startTime}return this.generateSegmentInfo_(t,i,o,!1)}},{key:"getSyncSegmentCandidate_",value:function(e){var t=this;if(-1===this.currentTimeline_)return 0;var i=e.segments.map(function(e,t){return{timeline:e.timeline,segmentIndex:t}}).filter(function(e){return e.timeline===t.currentTimeline_});return i.length?i[Math.min(i.length-1,1)].segmentIndex:Math.max(e.segments.length-1,0)}},{key:"generateSegmentInfo_",value:function(e,t,i,n){if(t<0||t>=e.segments.length)return null;var r=e.segments[t];return{requestId:"segment-loader-"+Math.random(),uri:r.resolvedUri,mediaIndex:t,isSyncRequest:n,startOfSegment:i,playlist:e,bytes:null,encryptedBytes:null,timestampOffset:null,timeline:r.timeline,duration:r.duration,segment:r}}},{key:"abortRequestEarly_",value:function(e){if(this.hls_.tech_.paused()||!this.xhrOptions_.timeout||!this.playlist_.attributes.BANDWIDTH||Date.now()-(e.firstBytesReceivedAt||Date.now())<1e3)return!1;var t=this.currentTime_(),i=e.bandwidth,n=this.pendingSegment_.duration,r=o9.estimateSegmentRequestTime(n,i,this.playlist_,e.bytesReceived),s=function(e,t,i){var n=2<arguments.length&&void 0!==i?i:1;return((e.length?e.end(e.length-1):0)-t)/n}(this.buffered_(),t,this.hls_.tech_.playbackRate())-1;if(r<=s)return!1;var a,o,u,l,c,h,d,p,f,m,g,v,y,$=(o=(a={master:this.hls_.playlists.master,currentTime:t,bandwidth:i,duration:this.duration_(),segmentDuration:n,timeUntilRebuffer:s,currentTimeline:this.currentTimeline_,syncController:this.syncController_}).master,u=a.currentTime,l=a.bandwidth,c=a.duration,h=a.segmentDuration,d=a.timeUntilRebuffer,p=a.currentTimeline,f=a.syncController,(g=(m=o.playlists.filter(function(e){return!o9.isIncompatible(e)})).filter(o9.isEnabled)).length||(g=m.filter(function(e){return!o9.isDisabled(e)})),y=(v=g.filter(o9.hasAttribute.bind(null,"BANDWIDTH")).map(function(e){var t=f.getSyncPoint(e,c,p,u)?1:2;return{playlist:e,rebufferingImpact:o9.estimateSegmentRequestTime(h,l,e)*t-d}})).filter(function(e){return e.rebufferingImpact<=0}),uU(y,function(e,t){return u1(t.playlist,e.playlist)}),y.length?y[0]:(uU(v,function(e,t){return e.rebufferingImpact-t.rebufferingImpact}),v[0]||null));if($){var b=r-s-$.rebufferingImpact,T=.5;return s<=1/30&&(T=1),!(!$.playlist||$.playlist.uri===this.playlist_.uri||b<T)&&(this.bandwidth=$.playlist.attributes.BANDWIDTH*u6.BANDWIDTH_VARIANCE+1,this.abort(),this.trigger("earlyabort"),!0)}}},{key:"handleProgress_",value:function(e,t){this.pendingSegment_&&t.requestId===this.pendingSegment_.requestId&&!this.abortRequestEarly_(t.stats)&&this.trigger("progress")}},{key:"loadSegment_",value:function(e){this.state="WAITING",this.pendingSegment_=e,this.trimBackBuffer_(e),e.abortRequests=function e(t,i,n,r,s,a,o){var u,l,c,h,d,p=[],f=(u=p,l=n,c=o,h=0,d=!1,function(e,t){if(!d)return e?(d=!0,uN(u),c(e,t)):(h+=1)===u.length?(t.endOfAllRequests=Date.now(),t.encryptedBytes?function(e,t,i){e.addEventListener("message",function n(r){if(r.data.source===t.requestId){e.removeEventListener("message",n);var s=r.data.decrypted;return t.bytes=new Uint8Array(s.bytes,s.byteOffset,s.byteLength),i(null,t)}});var n=void 0;n=t.key.bytes.slice?t.key.bytes.slice():new Uint32Array(Array.prototype.slice.call(t.key.bytes)),e.postMessage(oB({source:t.requestId,encrypted:t.encryptedBytes,key:n,iv:t.key.iv}),[t.encryptedBytes.buffer,n.buffer])}(l,t,c):c(null,t)):void 0});if(s.key&&!s.key.bytes){var m,g,v=t(rm.mergeOptions(i,{uri:s.key.resolvedUri,responseType:"arraybuffer"}),(m=s,g=f,function(e,t){var i=t.response,n=u3(e,t);if(n)return g(n,m);if(16!==i.byteLength)return g({status:t.status,message:"Invalid HLS key at URL: "+t.uri,code:uB,xhr:t},m);var r=new DataView(i);return m.key.bytes=new Uint32Array([r.getUint32(0),r.getUint32(4),r.getUint32(8),r.getUint32(12)]),g(null,m)}));p.push(v)}if(s.map&&!s.map.bytes){var y,$,b,T=t(rm.mergeOptions(i,{uri:s.map.resolvedUri,responseType:"arraybuffer",headers:o1(s.map)}),(y=s,$=r,b=f,function(e,t){var i=t.response,n=u3(e,t);return n?b(n,y):0===i.byteLength?b({status:t.status,message:"Empty HLS segment content at URL: "+t.uri,code:uB,xhr:t},y):(y.map.bytes=new Uint8Array(t.response),$&&!$.isInitialized()&&$.init(),y.map.timescales=a0.timescale(y.map.bytes),y.map.videoTrackIds=a0.videoTrackIds(y.map.bytes),b(null,y))}));p.push(T)}var _,S,k,C,E,w=t(rm.mergeOptions(i,{uri:s.resolvedUri,responseType:"arraybuffer",headers:o1(s)}),(_=s,S=r,k=f,function(e,t){var i,n=t.response,r=u3(e,t),s=void 0;return r?k(r,_):0===n.byteLength?k({status:t.status,message:"Empty HLS segment content at URL: "+t.uri,code:uB,xhr:t},_):(_.stats={bandwidth:(i=t).bandwidth,bytesReceived:i.bytesReceived||0,roundTripTime:i.roundTripTime||0},_.key?_.encryptedBytes=new Uint8Array(t.response):_.bytes=new Uint8Array(t.response),S&&_.map&&_.map.bytes&&(S.isInitialized()||S.init(),(s=S.parse(_.bytes,_.map.videoTrackIds,_.map.timescales))&&s.captions&&(_.captionStreams=s.captionStreams,_.fmp4Captions=s.captions)),k(null,_))}));return w.addEventListener("progress",(C=s,E=a,function(e){var t,i,n;return C.stats=rm.mergeOptions(C.stats,(i=(t=e).target,(n={bandwidth:1/0,bytesReceived:0,roundTripTime:Date.now()-i.requestTime||0}).bytesReceived=t.loaded,n.bandwidth=Math.floor(n.bytesReceived/n.roundTripTime*8e3),n)),!C.stats.firstBytesReceivedAt&&C.stats.bytesReceived&&(C.stats.firstBytesReceivedAt=Date.now()),E(e,C)})),p.push(w),function(){return uN(p)}}(this.hls_.xhr,this.xhrOptions_,this.decrypter_,this.captionParser_,this.createSimplifiedSegmentObj_(e),this.handleProgress_.bind(this),this.segmentRequestFinished_.bind(this))}},{key:"trimBackBuffer_",value:function(e){var t,i,n,r,s=(t=this.seekable_(),i=this.currentTime_(),n=this.playlist_.targetDuration||10,r=i-30,t.length&&(r=Math.max(r,t.start(0))),Math.min(i-n,r));0<s&&this.remove(0,s)}},{key:"createSimplifiedSegmentObj_",value:function(e){var t=e.segment,i={resolvedUri:t.resolvedUri,byterange:t.byterange,requestId:e.requestId};if(t.key){var n=t.key.iv||new Uint32Array([0,0,0,e.mediaIndex+e.playlist.mediaSequence]);i.key=this.segmentKey(t.key),i.key.iv=n}return t.map&&(i.map=this.initSegment(t.map)),i}},{key:"segmentRequestFinished_",value:function(e,t){if(this.mediaRequests+=1,t.stats&&(this.mediaBytesTransferred+=t.stats.bytesReceived,this.mediaTransferDuration+=t.stats.roundTripTime),this.pendingSegment_){if(t.requestId===this.pendingSegment_.requestId){if(e)return this.pendingSegment_=null,this.state="READY",e.code===uF?void(this.mediaRequestsAborted+=1):(this.pause(),e.code===uj?(this.mediaRequestsTimedout+=1,this.bandwidth=1,this.roundTrip=NaN,void this.trigger("bandwidthupdate")):(this.mediaRequestsErrored+=1,this.error(e),void this.trigger("error")));this.bandwidth=t.stats.bandwidth,this.roundTrip=t.stats.roundTripTime,t.map&&(t.map=this.initSegment(t.map,!0)),t.key&&this.segmentKey(t.key,!0),this.processSegmentResponse_(t)}}else this.mediaRequestsAborted+=1}},{key:"processSegmentResponse_",value:function(e){var t=this.pendingSegment_;t.bytes=e.bytes,e.map&&(t.segment.map.bytes=e.map.bytes),t.endOfAllRequests=e.endOfAllRequests,e.fmp4Captions&&(function(e,t,i){for(var n in i)if(!e[n]){t.trigger({type:"usage",name:"hls-608"});var r=t.textTracks().getTrackById(n);e[n]=r||t.addRemoteTextTrack({kind:"captions",id:n,label:n},!1).track}}(this.inbandTextTracks_,this.hls_.tech_,e.captionStreams),function(e){var t=e.inbandTextTracks,i=e.captionArray,n=e.timestampOffset;if(i){var r=window.WebKitDataCue||window.VTTCue;i.forEach(function(e){var i=e.stream,s=e.startTime,a=e.endTime;t[i]&&(s+=n,a+=n,t[i].addCue(new r(s,a,e.text)))})}}({inbandTextTracks:this.inbandTextTracks_,captionArray:e.fmp4Captions,timestampOffset:0}),this.captionParser_&&this.captionParser_.clearParsedCaptions()),this.handleSegment_()}},{key:"handleSegment_",value:function(){var e=this;if(this.pendingSegment_){var t,i,n,r,s,a,o,u,l,c,h,d,p,f=this.pendingSegment_,m=f.segment,g=this.syncController_.probeSegmentInfo(f);void 0===this.startingMedia_&&g&&(g.containsAudio||g.containsVideo)&&(this.startingMedia_={containsAudio:g.containsAudio,containsVideo:g.containsVideo});var v=(h=this.loaderType_,d=this.startingMedia_,p=g,"main"===h&&d&&p?p.containsAudio||p.containsVideo?d.containsVideo&&!p.containsVideo?"Only audio found in segment when we expected video. We can't switch to audio only from a stream that had video. To get rid of this message, please add codec information to the manifest.":!d.containsVideo&&p.containsVideo?"Video found in segment when we expected only audio. We can't switch to a stream with video from an audio only stream. To get rid of this message, please add codec information to the manifest.":null:"Neither audio nor video found in segment.":null);if(v)return this.error({message:v,blacklistDuration:1/0}),void this.trigger("error");if(f.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");if(null!==f.timestampOffset&&f.timestampOffset!==this.sourceUpdater_.timestampOffset()){if(g&&g.segmentTimestampInfo){var y=g.segmentTimestampInfo[0].ptsTime,$=g.segmentTimestampInfo[0].dtsTime;f.timestampOffset-=y-$}this.sourceUpdater_.timestampOffset(f.timestampOffset),this.trigger("timestampoffset")}var b=this.syncController_.mappingForTimeline(f.timeline);if(null!==b&&this.trigger({type:"segmenttimemapping",mapping:b}),this.state="APPENDING",m.map){var T=oj(m.map);if(!this.activeInitSegmentId_||this.activeInitSegmentId_!==T){var _=this.initSegment(m.map);this.sourceUpdater_.appendBuffer({bytes:_.bytes},function(){e.activeInitSegmentId_=T})}}f.byteLength=f.bytes.byteLength,"number"==typeof m.start&&"number"==typeof m.end?this.mediaSecondsLoaded+=m.end-m.start:this.mediaSecondsLoaded+=m.duration,this.logger_((n=(i=(t=f).segment).start,r=i.end,a=(s=t.playlist).mediaSequence,o=s.id,u=s.segments,l=t.mediaIndex,c=t.timeline,["appending ["+l+"] of ["+a+", "+(a+(void 0===u?[]:u).length)+"] from playlist ["+o+"]","["+n+" => "+r+"] in timeline ["+c+"]"].join(" "))),this.sourceUpdater_.appendBuffer({bytes:f.bytes,videoSegmentTimingInfoCallback:this.handleVideoSegmentTimingInfo_.bind(this,f.requestId)},this.handleUpdateEnd_.bind(this))}else this.state="READY"}},{key:"handleVideoSegmentTimingInfo_",value:function(e,t){if(this.pendingSegment_&&e===this.pendingSegment_.requestId){var i=this.pendingSegment_.segment;i.videoTimingInfo||(i.videoTimingInfo={}),i.videoTimingInfo.transmuxerPrependedSeconds=t.videoSegmentTimingInfo.prependedContentDuration||0,i.videoTimingInfo.transmuxedPresentationStart=t.videoSegmentTimingInfo.start.presentation,i.videoTimingInfo.transmuxedPresentationEnd=t.videoSegmentTimingInfo.end.presentation,i.videoTimingInfo.baseMediaDecodeTime=t.videoSegmentTimingInfo.baseMediaDecodeTime}}},{key:"handleUpdateEnd_",value:function(){if(!this.pendingSegment_)return this.state="READY",void(this.paused()||this.monitorBuffer_());var e=this.pendingSegment_,t=e.segment,i=null!==this.mediaIndex;this.pendingSegment_=null,this.recordThroughput_(e),this.addSegmentMetadataCue_(e),this.state="READY",this.mediaIndex=e.mediaIndex,this.fetchAtBuffer_=!0,this.currentTimeline_=e.timeline,this.trigger("syncinfoupdate"),t.end&&this.currentTime_()-t.end>3*e.playlist.targetDuration?this.resetEverything():(i&&this.trigger("bandwidthupdate"),this.trigger("progress"),this.isEndOfStream_(e.mediaIndex+1,e.playlist)&&this.endOfStream(),this.paused()||this.monitorBuffer_())}},{key:"recordThroughput_",value:function(e){var t=this.throughput.rate,i=Date.now()-e.endOfAllRequests+1,n=Math.floor(e.byteLength/i*8e3);this.throughput.rate+=(n-t)/++this.throughput.count}},{key:"addSegmentMetadataCue_",value:function(t){if(this.segmentMetadataTrack_){var i=t.segment,n=i.start,r=i.end;if(uM(n)&&uM(r)){oz(n,r,this.segmentMetadataTrack_);var s=e.WebKitDataCue||e.VTTCue,a={custom:i.custom,dateTimeObject:i.dateTimeObject,dateTimeString:i.dateTimeString,bandwidth:t.playlist.attributes.BANDWIDTH,resolution:t.playlist.attributes.RESOLUTION,codecs:t.playlist.attributes.CODECS,byteLength:t.byteLength,uri:t.uri,timeline:t.timeline,playlist:t.playlist.id,start:n,end:r},o=new s(n,r,JSON.stringify(a));o.value=a,this.segmentMetadataTrack_.addCue(o)}}}}]),u5);function u5(e){of(this,u5);var t=oy(this,(u5.__proto__||Object.getPrototypeOf(u5)).call(this));if(!e)throw TypeError("Initialization settings are required");if("function"!=typeof e.currentTime)throw TypeError("No currentTime getter specified");if(!e.mediaSource)throw TypeError("No MediaSource specified");return t.bandwidth=e.bandwidth,t.throughput={rate:0,count:0},t.roundTrip=NaN,t.resetStats_(),t.mediaIndex=null,t.hasPlayed_=e.hasPlayed,t.currentTime_=e.currentTime,t.seekable_=e.seekable,t.seeking_=e.seeking,t.duration_=e.duration,t.mediaSource_=e.mediaSource,t.hls_=e.hls,t.loaderType_=e.loaderType,t.startingMedia_=void 0,t.segmentMetadataTrack_=e.segmentMetadataTrack,t.goalBufferLength_=e.goalBufferLength,t.sourceType_=e.sourceType,t.inbandTextTracks_=e.inbandTextTracks,t.state_="INIT",t.checkBufferTimeout_=null,t.error_=void 0,t.currentTimeline_=-1,t.pendingSegment_=null,t.mimeType_=null,t.sourceUpdater_=null,t.xhrOptions_=null,t.activeInitSegmentId_=null,t.initSegments_={},t.cacheEncryptionKeys_=e.cacheEncryptionKeys,t.keyCache_={},"main"===t.loaderType_?t.captionParser_=new a1:t.captionParser_=null,t.decrypter_=e.decrypter,t.syncController_=e.syncController,t.syncPoint_={segmentIndex:0,time:0},t.triggerSyncInfoUpdate_=function(){return t.trigger("syncinfoupdate")},t.syncController_.on("syncinfoupdate",t.triggerSyncInfoUpdate_),t.mediaSource_.addEventListener("sourceopen",function(){return t.ended_=!1}),t.fetchAtBuffer_=!1,t.logger_=uA("SegmentLoader["+t.loaderType_+"]"),Object.defineProperty(t,"state",{get:function(){return this.state_},set:function(e){e!==this.state_&&(this.logger_(this.state_+" -> "+e),this.state_=e)}}),t}function uH(e){return decodeURIComponent(escape(String.fromCharCode.apply(null,e)))}var uq=new Uint8Array("\n\n".split("").map(function(e){return e.charCodeAt(0)})),uV=(ov(uW,u7),om(uW,[{key:"buffered_",value:function(){if(!this.subtitlesTrack_||!this.subtitlesTrack_.cues.length)return rm.createTimeRanges();var e=this.subtitlesTrack_.cues,t=e[0].startTime,i=e[e.length-1].startTime;return rm.createTimeRanges([[t,i]])}},{key:"initSegment",value:function(e,t){var i=1<arguments.length&&void 0!==t&&t;if(!e)return null;var n=oj(e),r=this.initSegments_[n];if(i&&!r&&e.bytes){var s=uq.byteLength+e.bytes.byteLength,a=new Uint8Array(s);a.set(e.bytes),a.set(uq,e.bytes.byteLength),this.initSegments_[n]=r={resolvedUri:e.resolvedUri,byterange:e.byterange,bytes:a}}return r||e}},{key:"couldBeginLoading_",value:function(){return this.playlist_&&this.subtitlesTrack_&&!this.paused()}},{key:"init_",value:function(){return this.state="READY",this.resetEverything(),this.monitorBuffer_()}},{key:"track",value:function(e){return void 0===e||(this.subtitlesTrack_=e,"INIT"===this.state&&this.couldBeginLoading_()&&this.init_()),this.subtitlesTrack_}},{key:"remove",value:function(e,t){oz(e,t,this.subtitlesTrack_)}},{key:"fillBuffer_",value:function(){var e=this;this.syncPoint_||(this.syncPoint_=this.syncController_.getSyncPoint(this.playlist_,this.duration_(),this.currentTimeline_,this.currentTime_()));var t=this.checkBuffer_(this.buffered_(),this.playlist_,this.mediaIndex,this.hasPlayed_(),this.currentTime_(),this.syncPoint_);if(t=this.skipEmptySegments_(t)){if(null===this.syncController_.timestampOffsetForTimeline(t.timeline))return this.syncController_.one("timestampoffset",function(){e.state="READY",e.paused()||e.monitorBuffer_()}),void(this.state="WAITING_ON_TIMELINE");this.loadSegment_(t)}}},{key:"skipEmptySegments_",value:function(e){for(;e&&e.segment.empty;)e=this.generateSegmentInfo_(e.playlist,e.mediaIndex+1,e.startOfSegment+e.duration,e.isSyncRequest);return e}},{key:"handleSegment_",value:function(){var t=this;if(this.pendingSegment_&&this.subtitlesTrack_){this.state="APPENDING";var i=this.pendingSegment_,n=i.segment;if("function"!=typeof e.WebVTT&&this.subtitlesTrack_&&this.subtitlesTrack_.tech_){var r=void 0,s=function(){t.subtitlesTrack_.tech_.off("vttjsloaded",r),t.error({message:"Error loading vtt.js"}),t.state="READY",t.pause(),t.trigger("error")};return r=function(){t.subtitlesTrack_.tech_.off("vttjserror",s),t.handleSegment_()},this.state="WAITING_ON_VTTJS",this.subtitlesTrack_.tech_.one("vttjsloaded",r),void this.subtitlesTrack_.tech_.one("vttjserror",s)}n.requested=!0;try{this.parseVTTCues_(i)}catch(a){return this.error({message:a.message}),this.state="READY",this.pause(),this.trigger("error")}if(this.updateTimeMapping_(i,this.syncController_.timelines[i.timeline],this.playlist_),i.isSyncRequest)return this.trigger("syncinfoupdate"),this.pendingSegment_=null,void(this.state="READY");i.byteLength=i.bytes.byteLength,this.mediaSecondsLoaded+=n.duration,i.cues.length&&this.remove(i.cues[0].endTime,i.cues[i.cues.length-1].endTime),i.cues.forEach(function(i){t.subtitlesTrack_.addCue(t.featuresNativeTextTracks_?new e.VTTCue(i.startTime,i.endTime,i.text):i)}),this.handleUpdateEnd_()}else this.state="READY"}},{key:"parseVTTCues_",value:function(t){var i=void 0,n=!1;"function"==typeof e.TextDecoder?i=new e.TextDecoder("utf8"):(i=e.WebVTT.StringDecoder(),n=!0);var r=new e.WebVTT.Parser(e,e.vttjs,i);if(t.cues=[],t.timestampmap={MPEGTS:0,LOCAL:0},r.oncue=t.cues.push.bind(t.cues),r.ontimestampmap=function(e){return t.timestampmap=e},r.onparsingerror=function(e){rm.log.warn("Error encountered when parsing cues: "+e.message)},t.segment.map){var s=t.segment.map.bytes;n&&(s=uH(s)),r.parse(s)}var a=t.bytes;n&&(a=uH(a)),r.parse(a),r.flush()}},{key:"updateTimeMapping_",value:function(e,t,i){var n=e.segment;if(t){if(e.cues.length){var r=e.timestampmap,s=r.MPEGTS/9e4-r.LOCAL+t.mapping;if(e.cues.forEach(function(e){e.startTime+=s,e.endTime+=s}),!i.syncInfo){var a=e.cues[0].startTime,o=e.cues[e.cues.length-1].startTime;i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:Math.min(a,o-n.duration)}}}else n.empty=!0}}}]),uW);function uW(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};of(this,uW);var i=oy(this,(uW.__proto__||Object.getPrototypeOf(uW)).call(this,e,t));return i.mediaSource_=null,i.subtitlesTrack_=null,i.featuresNativeTextTracks_=e.featuresNativeTextTracks,i}function uz(e,t){for(var i=e.cues,n=0;n<i.length;n++){var r=i[n];if(t>=r.adStartTime&&t<=r.adEndTime)return r}return null}var uG=aY,uX=[{name:"VOD",run:function(e,t,i,n,r){return i===1/0?null:{time:0,segmentIndex:0}}},{name:"ProgramDateTime",run:function(e,t,i,n,r){if(!e.datetimeToDisplayTime)return null;var s=t.segments||[],a=null,o=null;r=r||0;for(var u=0;u<s.length;u++){var l=s[u];if(l.dateTimeObject){var c=l.dateTimeObject.getTime()/1e3+e.datetimeToDisplayTime,h=Math.abs(r-c);if(null!==o&&(0===h||o<h))break;o=h,a={time:c,segmentIndex:u}}}return a}},{name:"Segment",run:function(e,t,i,n,r){var s=t.segments||[],a=null,o=null;r=r||0;for(var u=0;u<s.length;u++){var l=s[u];if(l.timeline===n&&void 0!==l.start){var c=Math.abs(r-l.start);if(null!==o&&o<c)break;(!a||null===o||c<=o)&&(o=c,a={time:l.start,segmentIndex:u})}}return a}},{name:"Discontinuity",run:function(e,t,i,n,r){var s=null;if(r=r||0,t.discontinuityStarts&&t.discontinuityStarts.length)for(var a=null,o=0;o<t.discontinuityStarts.length;o++){var u=t.discontinuityStarts[o],l=t.discontinuitySequence+o+1,c=e.discontinuities[l];if(c){var h=Math.abs(r-c.time);if(null!==a&&a<h)break;(!s||null===a||h<=a)&&(a=h,s={time:c.time,segmentIndex:u})}}return s}},{name:"Playlist",run:function(e,t,i,n,r){return t.syncInfo?{time:t.syncInfo.time,segmentIndex:t.syncInfo.mediaSequence-t.mediaSequence}:null}}],uK=(ov(uY,rm.EventTarget),om(uY,[{key:"getSyncPoint",value:function(e,t,i,n){var r=this.runStrategies_(e,t,i,n);return r.length?this.selectSyncPoint_(r,{key:"time",value:n}):null}},{key:"getExpiredTime",value:function(e,t){if(!e||!e.segments)return null;var i=this.runStrategies_(e,t,e.discontinuitySequence,0);if(!i.length)return null;var n=this.selectSyncPoint_(i,{key:"segmentIndex",value:0});return 0<n.segmentIndex&&(n.time*=-1),Math.abs(n.time+oL(e,n.segmentIndex,0))}},{key:"runStrategies_",value:function(e,t,i,n){for(var r=[],s=0;s<uX.length;s++){var a=uX[s],o=a.run(this,e,t,i,n);o&&(o.strategy=a.name,r.push({strategy:a.name,syncPoint:o}))}return r}},{key:"selectSyncPoint_",value:function(e,t){for(var i=e[0].syncPoint,n=Math.abs(e[0].syncPoint[t.key]-t.value),r=e[0].strategy,s=1;s<e.length;s++){var a=Math.abs(e[s].syncPoint[t.key]-t.value);a<n&&(n=a,i=e[s].syncPoint,r=e[s].strategy)}return this.logger_("syncPoint for ["+t.key+": "+t.value+"] chosen with strategy ["+r+"]: [time:"+i.time+", segmentIndex:"+i.segmentIndex+"]"),i}},{key:"saveExpiredSegmentInfo",value:function(e,t){for(var i=t.mediaSequence-e.mediaSequence-1;0<=i;i--){var n=e.segments[i];if(n&&void 0!==n.start){t.syncInfo={mediaSequence:e.mediaSequence+i,time:n.start},this.logger_("playlist refresh sync: [time:"+t.syncInfo.time+", mediaSequence: "+t.syncInfo.mediaSequence+"]"),this.trigger("syncinfoupdate");break}}}},{key:"setDateTimeMapping",value:function(e){if(!this.datetimeToDisplayTime&&e.segments&&e.segments.length&&e.segments[0].dateTimeObject){var t=e.segments[0].dateTimeObject.getTime()/1e3;this.datetimeToDisplayTime=-t}}},{key:"reset",value:function(){this.inspectCache_=void 0}},{key:"probeSegmentInfo",value:function(e){var t=e.segment,i=e.playlist,n=void 0;return(n=t.map?this.probeMp4Segment_(e):this.probeTsSegment_(e))&&this.calculateSegmentTimeMapping_(e,n)&&(this.saveDiscontinuitySyncInfo_(e),i.syncInfo||(i.syncInfo={mediaSequence:i.mediaSequence+e.mediaIndex,time:t.start})),n}},{key:"probeMp4Segment_",value:function(e){var t=e.segment,i=a0.timescale(t.map.bytes),n=a0.compositionStartTime(i,e.bytes);return null!==e.timestampOffset&&(e.timestampOffset-=n),{start:n,end:n+t.duration}}},{key:"probeTsSegment_",value:function(e){var t=uG(e.bytes,this.inspectCache_),i=void 0,n=void 0,r=void 0;return t?(t.video&&2===t.video.length?(this.inspectCache_=t.video[1].dts,i=t.video[0].dtsTime,n=t.video[1].dtsTime,r=t.video):t.audio&&2===t.audio.length&&(this.inspectCache_=t.audio[1].dts,i=t.audio[0].dtsTime,n=t.audio[1].dtsTime,r=t.audio),{segmentTimestampInfo:r,start:i,end:n,containsVideo:t.video&&2===t.video.length,containsAudio:t.audio&&2===t.audio.length}):null}},{key:"timestampOffsetForTimeline",value:function(e){return void 0===this.timelines[e]?null:this.timelines[e].time}},{key:"mappingForTimeline",value:function(e){return void 0===this.timelines[e]?null:this.timelines[e].mapping}},{key:"calculateSegmentTimeMapping_",value:function(e,t){var i=e.segment,n=this.timelines[e.timeline];if(null!==e.timestampOffset)n={time:e.startOfSegment,mapping:e.startOfSegment-t.start},this.timelines[e.timeline]=n,this.trigger("timestampoffset"),this.logger_("time mapping for timeline "+e.timeline+": [time: "+n.time+"] [mapping: "+n.mapping+"]"),i.start=e.startOfSegment,i.end=t.end+n.mapping;else{if(!n)return!1;i.start=t.start+n.mapping,i.end=t.end+n.mapping}return!0}},{key:"saveDiscontinuitySyncInfo_",value:function(e){var t=e.playlist,i=e.segment;if(i.discontinuity)this.discontinuities[i.timeline]={time:i.start,accuracy:0};else if(t.discontinuityStarts&&t.discontinuityStarts.length)for(var n=0;n<t.discontinuityStarts.length;n++){var r=t.discontinuityStarts[n],s=t.discontinuitySequence+n+1,a=r-e.mediaIndex,o=Math.abs(a);if(!this.discontinuities[s]||this.discontinuities[s].accuracy>o){var u=void 0;u=a<0?i.start-oL(t,e.mediaIndex,r):i.end+oL(t,e.mediaIndex+1,r),this.discontinuities[s]={time:u,accuracy:o}}}}},{key:"dispose",value:function(){this.trigger("dispose"),this.off()}}]),uY);function uY(){of(this,uY);var e=oy(this,(uY.__proto__||Object.getPrototypeOf(uY)).call(this));return e.inspectCache_=void 0,e.timelines=[],e.discontinuities=[],e.datetimeToDisplayTime=null,e.logger_=uA("SyncController"),e}function u9(e,t){e.abort(),e.pause(),t&&t.activePlaylistLoader&&(t.activePlaylistLoader.pause(),t.activePlaylistLoader=null)}function uQ(e,t){(t.activePlaylistLoader=e).load()}var uJ=new us("./decrypter-worker.worker.js",function(e,t){var i,n,r,s,a,o,u;function l(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function c(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function h(e){l(this,h),n=n||function(){var e=[[[],[],[],[],[]],[[],[],[],[],[]]],t=e[0],i=e[1],n=t[4],r=i[4],s=void 0,a=void 0,o=void 0,u=[],l=[],c=void 0,h=void 0,d=void 0,p=void 0,f=void 0;for(s=0;s<256;s++)l[(u[s]=s<<1^283*(s>>7))^s]=s;for(a=o=0;!n[a];a^=c||1,o=l[o]||1)for(d=(d=o^o<<1^o<<2^o<<3^o<<4)>>8^255&d^99,f=16843009*u[h=u[c=u[r[n[a]=d]=a]]]^65537*h^257*c^16843008*a,p=257*u[d]^16843008*d,s=0;s<4;s++)t[s][a]=p=p<<24^p>>>8,i[s][d]=f=f<<24^f>>>8;for(s=0;s<5;s++)t[s]=t[s].slice(0),i[s]=i[s].slice(0);return e}(),this._tables=[[n[0][0].slice(),n[0][1].slice(),n[0][2].slice(),n[0][3].slice(),n[0][4].slice()],[n[1][0].slice(),n[1][1].slice(),n[1][2].slice(),n[1][3].slice(),n[1][4].slice()]];var t=void 0,i=void 0,r=void 0,s=void 0,a=void 0,o=this._tables[0][4],u=this._tables[1],c=e.length,d=1;if(4!==c&&6!==c&&8!==c)throw Error("Invalid aes key size");for(s=e.slice(0),a=[],this._key=[s,a],t=c;t<4*c+28;t++)r=s[t-1],(t%c==0||8===c&&t%c==4)&&(r=o[r>>>24]<<24^o[r>>16&255]<<16^o[r>>8&255]<<8^o[255&r],t%c==0&&(r=r<<8^r>>>24^d<<24,d=d<<1^283*(d>>7))),s[t]=s[t-c]^r;for(i=0;t;i++,t--)r=s[3&i?t:t-4],a[i]=t<=4||i<4?r:u[0][o[r>>>24]]^u[1][o[r>>16&255]]^u[2][o[r>>8&255]]^u[3][o[255&r]]}function d(){l(this,d),this.listeners={}}function p(){l(this,p);var e=function(e,t){if(!e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&("object"==typeof t||"function"==typeof t)?t:e}(this,a.call(this,s));return e.jobs=[],e.delay=1,e.timeout_=null,e}function f(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24}function m(e,t,i,n){l(this,m);var r=m.STEP,s=new Int32Array(e.buffer),a=new Uint8Array(e.byteLength),u=0;for(this.asyncStream_=new o,this.asyncStream_.push(this.decryptChunk_(s.subarray(u,u+r),t,i,a)),u=r;u<s.length;u+=r)i=new Uint32Array([f(s[u-4]),f(s[u-3]),f(s[u-2]),f(s[u-1])]),this.asyncStream_.push(this.decryptChunk_(s.subarray(u,u+r),t,i,a));this.asyncStream_.push(function(){var e;n(null,(e=a).subarray(0,e.byteLength-e[e.byteLength-1]))})}i=function(e,t,i){return t&&c(e.prototype,t),i&&c(e,i),e},n=null,h.prototype.decrypt=function(e,t,i,n,r,s){var a=this._key[1],o=e^a[0],u=n^a[1],l=i^a[2],c=t^a[3],h=void 0,d=void 0,p=void 0,f=a.length/4-2,m=void 0,g=4,v=this._tables[1],y=v[0],$=v[1],b=v[2],T=v[3],_=v[4];for(m=0;m<f;m++)h=y[o>>>24]^$[u>>16&255]^b[l>>8&255]^T[255&c]^a[g],d=y[u>>>24]^$[l>>16&255]^b[c>>8&255]^T[255&o]^a[g+1],p=y[l>>>24]^$[c>>16&255]^b[o>>8&255]^T[255&u]^a[g+2],c=y[c>>>24]^$[o>>16&255]^b[u>>8&255]^T[255&l]^a[g+3],g+=4,o=h,u=d,l=p;for(m=0;m<4;m++)r[(3&-m)+s]=_[o>>>24]<<24^_[u>>16&255]<<16^_[l>>8&255]<<8^_[255&c]^a[g++],h=o,o=u,u=l,l=c,c=h},r=h,d.prototype.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},d.prototype.off=function(e,t){if(!this.listeners[e])return!1;var i=this.listeners[e].indexOf(t);return this.listeners[e].splice(i,1),-1<i},d.prototype.trigger=function(e,t){var i=this.listeners[e];if(i){if(2===arguments.length)for(var n=i.length,r=0;r<n;++r)i[r].call(this,t);else for(var s=Array.prototype.slice.call(arguments,1),a=i.length,o=0;o<a;++o)i[o].apply(this,s)}},d.prototype.dispose=function(){this.listeners={}},d.prototype.pipe=function(e){this.on("data",function(t){e.push(t)})},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(p,a=s=d),p.prototype.processJob_=function(){this.jobs.shift()(),this.jobs.length?this.timeout_=setTimeout(this.processJob_.bind(this),this.delay):this.timeout_=null},p.prototype.push=function(e){this.jobs.push(e),this.timeout_||(this.timeout_=setTimeout(this.processJob_.bind(this),this.delay))},o=p,m.prototype.decryptChunk_=function(e,t,i,n){return function(){var s=function(e,t,i){var n=new Int32Array(e.buffer,e.byteOffset,e.byteLength>>2),s=new r(Array.prototype.slice.call(t)),a=new Uint8Array(e.byteLength),o=new Int32Array(a.buffer),u=void 0,l=void 0,c=void 0,h=void 0,d=void 0,p=void 0,m=void 0,g=void 0,v=void 0;for(u=i[0],l=i[1],c=i[2],h=i[3],v=0;v<n.length;v+=4)d=f(n[v]),p=f(n[v+1]),m=f(n[v+2]),g=f(n[v+3]),s.decrypt(d,p,m,g,o,v),o[v]=f(o[v]^u),o[v+1]=f(o[v+1]^l),o[v+2]=f(o[v+2]^c),o[v+3]=f(o[v+3]^h),u=d,l=p,c=m,h=g;return a}(e,t,i);n.set(s,e.byteOffset)}},i(m,null,[{key:"STEP",get:function(){return 32e3}}]),u=m,new function(e){e.onmessage=function(t){var i=t.data,n=new Uint8Array(i.encrypted.bytes,i.encrypted.byteOffset,i.encrypted.byteLength),r=new Uint32Array(i.key.bytes,i.key.byteOffset,i.key.byteLength/4),s=new Uint32Array(i.iv.bytes,i.iv.byteOffset,i.iv.byteLength/4);new u(n,r,s,function(t,n){var r,s;e.postMessage((r={source:i.source,decrypted:n},s={},Object.keys(r).forEach(function(e){var t=r[e];ArrayBuffer.isView(t)?s[e]={bytes:t.buffer,byteOffset:t.byteOffset,byteLength:t.byteLength}:s[e]=t}),s),[n.buffer])})}}(this)}),uZ={AUDIO:function(e,t){return function(){var i=t.segmentLoaders[e],n=t.mediaTypes[e],r=t.blacklistCurrentPlaylist;u9(i,n);var s=n.activeTrack(),a=n.activeGroup(),o=(a.filter(function(e){return e.default})[0]||a[0]).id,u=n.tracks[o];if(s!==u){for(var l in rm.log.warn("Problem encountered loading the alternate audio track.Switching back to default."),n.tracks)n.tracks[l].enabled=n.tracks[l]===u;n.onTrackChanged()}else r({message:"Problem encountered loading the default audio track."})}},SUBTITLES:function(e,t){return function(){var i=t.segmentLoaders[e],n=t.mediaTypes[e];rm.log.warn("Problem encountered loading the subtitle track.Disabling subtitle track."),u9(i,n);var r=n.activeTrack();r&&(r.mode="disabled"),n.onTrackChanged()}}},le={AUDIO:function(e,t,i){if(t){var n=i.tech,r=i.requestOptions,s=i.segmentLoaders[e];t.on("loadedmetadata",function(){var e=t.media();s.playlist(e,r),(!n.paused()||e.endList&&"none"!==n.preload())&&s.load()}),t.on("loadedplaylist",function(){s.playlist(t.media(),r),n.paused()||s.load()}),t.on("error",uZ[e](e,i))}},SUBTITLES:function(e,t,i){var n=i.tech,r=i.requestOptions,s=i.segmentLoaders[e],a=i.mediaTypes[e];t.on("loadedmetadata",function(){var e=t.media();s.playlist(e,r),s.track(a.activeTrack()),(!n.paused()||e.endList&&"none"!==n.preload())&&s.load()}),t.on("loadedplaylist",function(){s.playlist(t.media(),r),n.paused()||s.load()}),t.on("error",uZ[e](e,i))}},lt={AUDIO:function(e,t){var i,n,r=t.hls,s=t.sourceType,a=t.segmentLoaders[e],o=t.requestOptions,u=t.master.mediaGroups,l=t.mediaTypes[e],c=l.groups,h=l.tracks,d=t.masterPlaylistLoader;for(var p in u[e]&&0!==Object.keys(u[e]).length||(u[e]={main:{default:{default:!0}}}),u[e])for(var f in c[p]||(c[p]=[]),u[e][p]){var m=u[e][p][f],g=void 0;if(g=m.resolvedUri?new ow(m.resolvedUri,r,o):m.playlists&&"dash"===s?new uI(m.playlists[0],r,o,d):null,m=rm.mergeOptions({id:f,playlistLoader:g},m),le[e](e,m.playlistLoader,t),c[p].push(m),void 0===h[f]){var v=new rm.AudioTrack({id:f,kind:(i=m,n=void 0,n=i.default?"main":"alternative",i.characteristics&&0<=i.characteristics.indexOf("public.accessibility.describes-video")&&(n="main-desc"),n),enabled:!1,language:m.language,default:m.default,label:f});h[f]=v}}a.on("error",uZ[e](e,t))},SUBTITLES:function(e,t){var i=t.tech,n=t.hls,r=t.sourceType,s=t.segmentLoaders[e],a=t.requestOptions,o=t.master.mediaGroups,u=t.mediaTypes[e],l=u.groups,c=u.tracks,h=t.masterPlaylistLoader;for(var d in o[e])for(var p in l[d]||(l[d]=[]),o[e][d])if(!o[e][d][p].forced){var f=o[e][d][p],m=void 0;if("hls"===r?m=new ow(f.resolvedUri,n,a):"dash"===r&&(m=new uI(f.playlists[0],n,a,h)),f=rm.mergeOptions({id:p,playlistLoader:m},f),le[e](e,f.playlistLoader,t),l[d].push(f),void 0===c[p]){var g=i.addRemoteTextTrack({id:p,kind:"subtitles",default:f.default&&f.autoselect,language:f.language,label:p},!1).track;c[p]=g}}s.on("error",uZ[e](e,t))},"CLOSED-CAPTIONS":function(e,t){var i=t.tech,n=t.master.mediaGroups,r=t.mediaTypes[e],s=r.groups,a=r.tracks;for(var o in n[e])for(var u in s[o]||(s[o]=[]),n[e][o]){var l=n[e][o][u];if(l.instreamId.match(/CC\d/)&&(s[o].push(rm.mergeOptions({id:u},l)),void 0===a[u])){var c=i.addRemoteTextTrack({id:l.instreamId,kind:"captions",default:l.default&&l.autoselect,language:l.language,label:u},!1).track;a[u]=c}}}},li={AUDIO:function(e,t){return function(){var i=t.mediaTypes[e].tracks;for(var n in i)if(i[n].enabled)return i[n];return null}},SUBTITLES:function(e,t){return function(){var i=t.mediaTypes[e].tracks;for(var n in i)if("showing"===i[n].mode||"hidden"===i[n].mode)return i[n];return null}}},ln=void 0,lr=["mediaRequests","mediaRequestsAborted","mediaRequestsTimedout","mediaRequestsErrored","mediaTransferDuration","mediaBytesTransferred"],ls=(ov(la,rm.EventTarget),om(la,[{key:"setupMasterPlaylistLoaderListeners_",value:function(){var e=this;this.masterPlaylistLoader_.on("loadedmetadata",function(){var t=e.masterPlaylistLoader_.media(),i=1.5*t.targetDuration*1e3;o4(e.masterPlaylistLoader_.master,e.masterPlaylistLoader_.media())?e.requestOptions_.timeout=0:e.requestOptions_.timeout=i,t.endList&&"none"!==e.tech_.preload()&&(e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.mainSegmentLoader_.load()),function e(t){["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){lt[e](e,t)});var i=t.mediaTypes,n=t.masterPlaylistLoader,r=t.tech,s=t.hls;["AUDIO","SUBTITLES"].forEach(function(e){var n,r,s,a,o,u;i[e].activeGroup=(n=e,r=t,function(e){var t=r.masterPlaylistLoader,i=r.mediaTypes[n].groups,s=t.media();if(!s)return null;var a=null;return s.attributes[n]&&(a=i[s.attributes[n]]),a=a||i.main,void 0===e?a:null===e?null:a.filter(function(t){return t.id===e.id})[0]||null}),i[e].activeTrack=li[e](e,t),i[e].onGroupChanged=(s=e,a=t,function(){var e=a.segmentLoaders,t=e[s],i=e.main,n=a.mediaTypes[s],r=n.activeTrack(),o=n.activeGroup(r),u=n.activePlaylistLoader;u9(t,n),o&&(o.playlistLoader?(t.resyncLoader(),uQ(o.playlistLoader,n)):u&&i.resetEverything())}),i[e].onTrackChanged=(o=e,u=t,function(){var e=u.segmentLoaders,t=e[o],i=e.main,n=u.mediaTypes[o],r=n.activeTrack(),s=n.activeGroup(r),a=n.activePlaylistLoader;u9(t,n),s&&(s.playlistLoader?(a!==s.playlistLoader&&(t.track&&t.track(r),t.resetEverything()),uQ(s.playlistLoader,n)):i.resetEverything())})});var a=i.AUDIO.activeGroup(),o=(a.filter(function(e){return e.default})[0]||a[0]).id;function u(){i.AUDIO.onTrackChanged(),r.trigger({type:"usage",name:"hls-audio-change"})}for(var l in i.AUDIO.tracks[o].enabled=!0,i.AUDIO.onTrackChanged(),n.on("mediachange",function(){["AUDIO","SUBTITLES"].forEach(function(e){return i[e].onGroupChanged()})}),r.audioTracks().addEventListener("change",u),r.remoteTextTracks().addEventListener("change",i.SUBTITLES.onTrackChanged),s.on("dispose",function(){r.audioTracks().removeEventListener("change",u),r.remoteTextTracks().removeEventListener("change",i.SUBTITLES.onTrackChanged)}),r.clearTracks("audio"),i.AUDIO.tracks)r.audioTracks().addTrack(i.AUDIO.tracks[l])}({sourceType:e.sourceType_,segmentLoaders:{AUDIO:e.audioSegmentLoader_,SUBTITLES:e.subtitleSegmentLoader_,main:e.mainSegmentLoader_},tech:e.tech_,requestOptions:e.requestOptions_,masterPlaylistLoader:e.masterPlaylistLoader_,hls:e.hls_,master:e.master(),mediaTypes:e.mediaTypes_,blacklistCurrentPlaylist:e.blacklistCurrentPlaylist.bind(e)}),e.triggerPresenceUsage_(e.master(),t);try{e.setupSourceBuffers_()}catch(n){return rm.log.warn("Failed to create SourceBuffers",n),e.mediaSource.endOfStream("decode")}e.setupFirstPlay(),!e.mediaTypes_.AUDIO.activePlaylistLoader||e.mediaTypes_.AUDIO.activePlaylistLoader.media()?e.trigger("selectedinitialmedia"):e.mediaTypes_.AUDIO.activePlaylistLoader.one("loadedmetadata",function(){e.trigger("selectedinitialmedia")})}),this.masterPlaylistLoader_.on("loadedplaylist",function(){var t=e.masterPlaylistLoader_.media();if(!t){e.excludeUnsupportedVariants_();var i=void 0;return e.enableLowInitialPlaylist&&(i=e.selectInitialPlaylist()),i=i||e.selectPlaylist(),e.initialMedia_=i,void e.masterPlaylistLoader_.media(e.initialMedia_)}if(e.useCueTags_&&e.updateAdCues_(t),e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.updateDuration(),e.tech_.paused()||(e.mainSegmentLoader_.load(),e.audioSegmentLoader_&&e.audioSegmentLoader_.load()),!t.endList){var n=function(){var t=e.seekable();0!==t.length&&e.mediaSource.addSeekableRange_(t.start(0),t.end(0))};e.duration()!==1/0?e.tech_.one("durationchange",function t(){e.duration()===1/0?n():e.tech_.one("durationchange",t)}):n()}}),this.masterPlaylistLoader_.on("error",function(){e.blacklistCurrentPlaylist(e.masterPlaylistLoader_.error)}),this.masterPlaylistLoader_.on("mediachanging",function(){e.mainSegmentLoader_.abort(),e.mainSegmentLoader_.pause()}),this.masterPlaylistLoader_.on("mediachange",function(){var t=e.masterPlaylistLoader_.media(),i=1.5*t.targetDuration*1e3;o4(e.masterPlaylistLoader_.master,e.masterPlaylistLoader_.media())?e.requestOptions_.timeout=0:e.requestOptions_.timeout=i,e.mainSegmentLoader_.playlist(t,e.requestOptions_),e.mainSegmentLoader_.load(),e.tech_.trigger({type:"mediachange",bubbles:!0})}),this.masterPlaylistLoader_.on("playlistunchanged",function(){var t=e.masterPlaylistLoader_.media();e.stuckAtPlaylistEnd_(t)&&(e.blacklistCurrentPlaylist({message:"Playlist no longer updating."}),e.tech_.trigger("playliststuck"))}),this.masterPlaylistLoader_.on("renditiondisabled",function(){e.tech_.trigger({type:"usage",name:"hls-rendition-disabled"})}),this.masterPlaylistLoader_.on("renditionenabled",function(){e.tech_.trigger({type:"usage",name:"hls-rendition-enabled"})})}},{key:"triggerPresenceUsage_",value:function(e,t){var i=e.mediaGroups||{},n=!0,r=Object.keys(i.AUDIO);for(var s in i.AUDIO)for(var a in i.AUDIO[s])i.AUDIO[s][a].uri||(n=!1);n&&this.tech_.trigger({type:"usage",name:"hls-demuxed"}),Object.keys(i.SUBTITLES).length&&this.tech_.trigger({type:"usage",name:"hls-webvtt"}),ln.Playlist.isAes(t)&&this.tech_.trigger({type:"usage",name:"hls-aes"}),ln.Playlist.isFmp4(t)&&this.tech_.trigger({type:"usage",name:"hls-fmp4"}),r.length&&1<Object.keys(i.AUDIO[r[0]]).length&&this.tech_.trigger({type:"usage",name:"hls-alternate-audio"}),this.useCueTags_&&this.tech_.trigger({type:"usage",name:"hls-playlist-cue-tags"})}},{key:"setupSegmentLoaderListeners_",value:function(){var e=this;this.mainSegmentLoader_.on("bandwidthupdate",function(){var t,i,n,r,s,a,o=e.selectPlaylist(),u=e.masterPlaylistLoader_.media(),l=e.tech_.buffered();i=(t={currentPlaylist:u,nextPlaylist:o,forwardBuffer:l.length?l.end(l.length-1)-e.tech_.currentTime():0,bufferLowWaterLine:e.bufferLowWaterLine(),duration:e.duration(),log:e.logger_}).currentPlaylist,n=t.nextPlaylist,r=t.forwardBuffer,s=t.bufferLowWaterLine,a=t.duration,t.log,(n?!i.endList||a<u6.MAX_BUFFER_LOW_WATER_LINE||n.attributes.BANDWIDTH<i.attributes.BANDWIDTH||s<=r:(rm.log.warn("We received no playlist to switch to. Please check your stream."),!1))&&e.masterPlaylistLoader_.media(o),e.tech_.trigger("bandwidthupdate")}),this.mainSegmentLoader_.on("progress",function(){e.trigger("progress")}),this.mainSegmentLoader_.on("error",function(){e.blacklistCurrentPlaylist(e.mainSegmentLoader_.error())}),this.mainSegmentLoader_.on("syncinfoupdate",function(){e.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("timestampoffset",function(){e.tech_.trigger({type:"usage",name:"hls-timestamp-offset"})}),this.audioSegmentLoader_.on("syncinfoupdate",function(){e.onSyncInfoUpdate_()}),this.mainSegmentLoader_.on("ended",function(){e.onEndOfStream()}),this.mainSegmentLoader_.on("earlyabort",function(){e.blacklistCurrentPlaylist({message:"Aborted early because there isn't enough bandwidth to complete the request without rebuffering."},120)}),this.mainSegmentLoader_.on("reseteverything",function(){e.tech_.trigger("hls-reset")}),this.mainSegmentLoader_.on("segmenttimemapping",function(t){e.tech_.trigger({type:"hls-segment-time-mapping",mapping:t.mapping})}),this.audioSegmentLoader_.on("ended",function(){e.onEndOfStream()})}},{key:"mediaSecondsLoaded_",value:function(){return Math.max(this.audioSegmentLoader_.mediaSecondsLoaded+this.mainSegmentLoader_.mediaSecondsLoaded)}},{key:"load",value:function(){this.mainSegmentLoader_.load(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.load(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.load()}},{key:"smoothQualityChange_",value:function(){var e=this.selectPlaylist();e!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(e),this.mainSegmentLoader_.resetLoader())}},{key:"fastQualityChange_",value:function(){var e=this,t=this.selectPlaylist();t!==this.masterPlaylistLoader_.media()&&(this.masterPlaylistLoader_.media(t),this.mainSegmentLoader_.resetEverything(function(){rm.browser.IE_VERSION||rm.browser.IS_EDGE?e.tech_.setCurrentTime(e.tech_.currentTime()+.04):e.tech_.setCurrentTime(e.tech_.currentTime())}))}},{key:"play",value:function(){if(!this.setupFirstPlay()){this.tech_.ended()&&this.tech_.setCurrentTime(0),this.hasPlayed_&&this.load();var e=this.tech_.seekable();return this.tech_.duration()===1/0&&this.tech_.currentTime()<e.start(0)?this.tech_.setCurrentTime(e.end(e.length-1)):void 0}}},{key:"setupFirstPlay",value:function(){var e=this,t=this.masterPlaylistLoader_.media();if(!t||this.tech_.paused()||this.hasPlayed_)return!1;if(!t.endList){var i=this.seekable();if(!i.length)return!1;if(rm.browser.IE_VERSION&&0===this.tech_.readyState())return this.tech_.one("loadedmetadata",function(){e.trigger("firstplay"),e.tech_.setCurrentTime(i.end(0)),e.hasPlayed_=!0}),!1;this.trigger("firstplay"),this.tech_.setCurrentTime(i.end(0))}return this.hasPlayed_=!0,this.load(),!0}},{key:"handleSourceOpen_",value:function(){try{this.setupSourceBuffers_()}catch(e){return rm.log.warn("Failed to create Source Buffers",e),this.mediaSource.endOfStream("decode")}if(this.tech_.autoplay()){var t=this.tech_.play();void 0!==t&&"function"==typeof t.then&&t.then(null,function(e){})}this.trigger("sourceopen")}},{key:"onEndOfStream",value:function(){var e=this.mainSegmentLoader_.ended_;if(this.mediaTypes_.AUDIO.activePlaylistLoader&&(e=!this.mainSegmentLoader_.startingMedia_||this.mainSegmentLoader_.startingMedia_.containsVideo?e&&this.audioSegmentLoader_.ended_:this.audioSegmentLoader_.ended_),e){this.logger_("calling mediaSource.endOfStream()");try{this.mediaSource.endOfStream()}catch(t){rm.log.warn("Failed to call media source endOfStream",t)}}}},{key:"stuckAtPlaylistEnd_",value:function(e){if(!this.seekable().length)return!1;var t=this.syncController_.getExpiredTime(e,this.mediaSource.duration);if(null===t)return!1;var i=ln.Playlist.playlistEnd(e,t),n=this.tech_.currentTime(),r=this.tech_.buffered();if(!r.length)return i-n<=.1;var s=r.end(r.length-1);return s-n<=.1&&i-s<=.1}},{key:"blacklistCurrentPlaylist",value:function(e,t){var i,n=0<arguments.length&&void 0!==e?e:{},r=t,s=void 0;if(s=n.playlist||this.masterPlaylistLoader_.media(),r=r||n.blacklistDuration||this.blacklistDuration,!s){this.error=n;try{return this.mediaSource.endOfStream("network")}catch(a){return this.trigger("error")}}var o=1===this.masterPlaylistLoader_.master.playlists.filter(oN).length,u=this.masterPlaylistLoader_.master.playlists;return 1===u.length?(rm.log.warn("Problem encountered with the current HLS playlist. Trying again since it is the only playlist."),this.tech_.trigger("retryplaylist"),this.masterPlaylistLoader_.load(o)):(o&&(rm.log.warn("Removing all playlists from the blacklist because the last rendition is about to be blacklisted."),u.forEach(function(e){e.excludeUntil!==1/0&&delete e.excludeUntil}),this.tech_.trigger("retryplaylist")),s.excludeUntil=Date.now()+1e3*r,this.tech_.trigger("blacklistplaylist"),this.tech_.trigger({type:"usage",name:"hls-rendition-blacklisted"}),i=this.selectPlaylist(),rm.log.warn("Problem encountered with the current HLS playlist."+(n.message?" "+n.message:"")+" Switching to another playlist."),this.masterPlaylistLoader_.media(i,o))}},{key:"pauseLoading",value:function(){this.mainSegmentLoader_.pause(),this.mediaTypes_.AUDIO.activePlaylistLoader&&this.audioSegmentLoader_.pause(),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&this.subtitleSegmentLoader_.pause()}},{key:"setCurrentTime",value:function(e){var t=oH(this.tech_.buffered(),e);return this.masterPlaylistLoader_&&this.masterPlaylistLoader_.media()&&this.masterPlaylistLoader_.media().segments?t&&t.length?e:(this.mainSegmentLoader_.resetEverything(),this.mainSegmentLoader_.abort(),this.mediaTypes_.AUDIO.activePlaylistLoader&&(this.audioSegmentLoader_.resetEverything(),this.audioSegmentLoader_.abort()),this.mediaTypes_.SUBTITLES.activePlaylistLoader&&(this.subtitleSegmentLoader_.resetEverything(),this.subtitleSegmentLoader_.abort()),void this.load()):0}},{key:"duration",value:function(){return this.masterPlaylistLoader_?this.mediaSource?this.mediaSource.duration:ln.Playlist.duration(this.masterPlaylistLoader_.media()):0}},{key:"seekable",value:function(){return this.seekable_}},{key:"onSyncInfoUpdate_",value:function(){var e=void 0;if(this.masterPlaylistLoader_){var t=this.masterPlaylistLoader_.media();if(t){var i=this.syncController_.getExpiredTime(t,this.mediaSource.duration);if(null!==i){var n=this.masterPlaylistLoader_.master.suggestedPresentationDelay,r=ln.Playlist.seekable(t,i,n);if(0!==r.length){if(this.mediaTypes_.AUDIO.activePlaylistLoader&&(t=this.mediaTypes_.AUDIO.activePlaylistLoader.media(),null===(i=this.syncController_.getExpiredTime(t,this.mediaSource.duration))||0===(e=ln.Playlist.seekable(t,i,n)).length))return;var s=void 0,a=void 0;this.seekable_&&this.seekable_.length&&(s=this.seekable_.end(0),a=this.seekable_.start(0)),e?e.start(0)>r.end(0)||r.start(0)>e.end(0)?this.seekable_=r:this.seekable_=rm.createTimeRanges([[e.start(0)>r.start(0)?e.start(0):r.start(0),e.end(0)<r.end(0)?e.end(0):r.end(0)]]):this.seekable_=r,this.seekable_&&this.seekable_.length&&this.seekable_.end(0)===s&&this.seekable_.start(0)===a||(this.logger_("seekable updated ["+oV(this.seekable_)+"]"),this.tech_.trigger("seekablechanged"))}}}}}},{key:"updateDuration",value:function(){function e(){t.logger_("Setting duration from "+t.mediaSource.duration+" => "+n);try{t.mediaSource.duration=n}catch(i){rm.log.warn("Failed to set media source duration",i)}t.tech_.trigger("durationchange"),t.mediaSource.removeEventListener("sourceopen",e)}var t=this,i=this.mediaSource.duration,n=ln.Playlist.duration(this.masterPlaylistLoader_.media()),r=this.tech_.buffered();0<r.length&&(n=Math.max(n,r.end(r.length-1))),i!==n&&("open"!==this.mediaSource.readyState?this.mediaSource.addEventListener("sourceopen",e):e())}},{key:"dispose",value:function(){var e=this;this.trigger("dispose"),this.decrypter_&&this.decrypter_.terminate(),this.masterPlaylistLoader_.dispose(),this.mainSegmentLoader_.dispose(),["AUDIO","SUBTITLES"].forEach(function(t){var i=e.mediaTypes_[t].groups;for(var n in i)i[n].forEach(function(e){e.playlistLoader&&e.playlistLoader.dispose()})}),this.audioSegmentLoader_.dispose(),this.subtitleSegmentLoader_.dispose(),this.off(),this.mediaSource.dispose&&this.mediaSource.dispose()}},{key:"master",value:function(){return this.masterPlaylistLoader_.master}},{key:"media",value:function(){return this.masterPlaylistLoader_.media()||this.initialMedia_}},{key:"setupSourceBuffers_",value:function(){var e,t=this.masterPlaylistLoader_.media();if(t&&"open"===this.mediaSource.readyState){if((e=function e(t,i){var n,r,s,a=(n=i).segments&&n.segments.length&&n.segments[0].map?"mp4":"mp2t",o=(s=(r=i).attributes||{}).CODECS?ud(s.CODECS):uy,u=i.attributes||{},l=!0,c=!1;if(!i)return[];if(t.mediaGroups.AUDIO&&u.AUDIO){var h=t.mediaGroups.AUDIO[u.AUDIO];if(h){for(var d in l=(c=!0,!1),h)if(!h[d].uri&&!h[d].playlists){l=!0;break}}}c&&!o.audioProfile&&(l||(o.audioProfile=function(e,t){if(!e.mediaGroups.AUDIO||!t)return null;var i=e.mediaGroups.AUDIO[t];if(!i)return null;for(var n in i){var r=i[n];if(r.default&&r.playlists)return ud(r.playlists[0].attributes.CODECS).audioProfile}return null}(t,u.AUDIO)),o.audioProfile||(rm.log.warn("Multiple audio tracks present but no audio codec string is specified. Attempting to use the default audio codec (mp4a.40.2)"),o.audioProfile=uy.audioProfile));var p={};o.videoCodec&&(p.video=""+o.videoCodec+o.videoObjectTypeIndicator),o.audioProfile&&(p.audio="mp4a.40."+o.audioProfile);var f=up("audio",a,[p.audio]),m=up("video",a,[p.video]),g=up("video",a,[p.video,p.audio]);return c?!l&&p.video?[m,f]:l||p.video?[g,f]:[f,f]:p.video?[g]:[f]}(this.masterPlaylistLoader_.master,t)).length<1)return this.error="No compatible SourceBuffer configuration for the variant stream:"+t.resolvedUri,this.mediaSource.endOfStream("decode");this.configureLoaderMimeTypes_(e),this.excludeIncompatibleVariants_(t)}}},{key:"configureLoaderMimeTypes_",value:function(e){var t=1<e.length&&-1===e[0].indexOf(",")&&e[0]!==e[1]?new rm.EventTarget:null;this.mainSegmentLoader_.mimeType(e[0],t),e[1]&&this.audioSegmentLoader_.mimeType(e[1],t)}},{key:"excludeUnsupportedVariants_",value:function(){this.master().playlists.forEach(function(t){var i;t.attributes.CODECS&&e.MediaSource&&e.MediaSource.isTypeSupported&&!e.MediaSource.isTypeSupported('video/mp4; codecs="'+(i=t.attributes.CODECS).replace(/avc1\.(\d+)\.(\d+)/i,function(e){return uh([e])[0]})+'"')&&(t.excludeUntil=1/0)})}},{key:"excludeIncompatibleVariants_",value:function(e){var t=2,i=null,n=void 0;e.attributes.CODECS&&(i=(n=ud(e.attributes.CODECS)).videoCodec,t=n.codecCount),this.master().playlists.forEach(function(e){var n={codecCount:2,videoCodec:null};e.attributes.CODECS&&(n=ud(e.attributes.CODECS)),n.codecCount!==t&&(e.excludeUntil=1/0),n.videoCodec!==i&&(e.excludeUntil=1/0)})}},{key:"updateAdCues_",value:function(t){var i=0,n=this.seekable();n.length&&(i=n.start(0)),function(t,i,n){var r=2<arguments.length&&void 0!==n?n:0;if(t.segments)for(var s=r,a=void 0,o=0;o<t.segments.length;o++){var u=t.segments[o];if(a=a||uz(i,s+u.duration/2)){if("cueIn"in u){a.endTime=s,a.adEndTime=s,s+=u.duration,a=null;continue}if(s<a.endTime){s+=u.duration;continue}a.endTime+=u.duration}else if("cueOut"in u&&((a=new e.VTTCue(s,s+u.duration,u.cueOut)).adStartTime=s,a.adEndTime=s+parseFloat(u.cueOut),i.addCue(a)),"cueOutCont"in u){var l,c,h=u.cueOutCont.split("/").map(parseFloat),d=ok(h,2);l=d[0],c=d[1],(a=new e.VTTCue(s,s+u.duration,"")).adStartTime=s-l,a.adEndTime=a.adStartTime+c,i.addCue(a)}s+=u.duration}}(t,this.cueTagsTrack_,i)}},{key:"goalBufferLength",value:function(){var e=this.tech_.currentTime(),t=u6.GOAL_BUFFER_LENGTH,i=u6.GOAL_BUFFER_LENGTH_RATE,n=Math.max(t,u6.MAX_GOAL_BUFFER_LENGTH);return Math.min(t+e*i,n)}},{key:"bufferLowWaterLine",value:function(){var e=this.tech_.currentTime(),t=u6.BUFFER_LOW_WATER_LINE,i=u6.BUFFER_LOW_WATER_LINE_RATE,n=Math.max(t,u6.MAX_BUFFER_LOW_WATER_LINE);return Math.min(t+e*i,n)}}]),la);function la(e){of(this,la);var t,i=oy(this,(la.__proto__||Object.getPrototypeOf(la)).call(this)),n=e.url,r=e.handleManifestRedirects,s=e.withCredentials,a=e.tech,o=e.bandwidth,u=e.externHls,l=e.useCueTags,c=e.blacklistDuration,h=e.enableLowInitialPlaylist,d=e.cacheEncryptionKeys,p=e.sourceType;if(!n)throw Error("A non-empty playlist URL is required");ln=u,i.withCredentials=s,i.tech_=a,i.hls_=a.hls,i.sourceType_=p,i.useCueTags_=l,i.blacklistDuration=c,i.enableLowInitialPlaylist=h,i.useCueTags_&&(i.cueTagsTrack_=i.tech_.addTextTrack("metadata","ad-cues"),i.cueTagsTrack_.inBandMetadataTrackDispatchType=""),i.requestOptions_={withCredentials:s,handleManifestRedirects:r,timeout:null},i.mediaTypes_=(t={},["AUDIO","SUBTITLES","CLOSED-CAPTIONS"].forEach(function(e){t[e]={groups:{},tracks:{},activePlaylistLoader:null,activeGroup:uO,activeTrack:uO,onGroupChanged:uO,onTrackChanged:uO}}),t),i.mediaSource=new rm.MediaSource,i.mediaSource.addEventListener("sourceopen",i.handleSourceOpen_.bind(i)),i.seekable_=rm.createTimeRanges(),i.hasPlayed_=!1,i.syncController_=new uK(e),i.segmentMetadataTrack_=a.addRemoteTextTrack({kind:"metadata",label:"segment-metadata"},!1).track,i.decrypter_=new uJ,i.inbandTextTracks_={};var f={hls:i.hls_,mediaSource:i.mediaSource,currentTime:i.tech_.currentTime.bind(i.tech_),seekable:function(){return i.seekable()},seeking:function(){return i.tech_.seeking()},duration:function(){return i.mediaSource.duration},hasPlayed:function(){return i.hasPlayed_},goalBufferLength:function(){return i.goalBufferLength()},bandwidth:o,syncController:i.syncController_,decrypter:i.decrypter_,sourceType:i.sourceType_,inbandTextTracks:i.inbandTextTracks_,cacheEncryptionKeys:d};return i.masterPlaylistLoader_="dash"===i.sourceType_?new uI(n,i.hls_,i.requestOptions_):new ow(n,i.hls_,i.requestOptions_),i.setupMasterPlaylistLoaderListeners_(),i.mainSegmentLoader_=new u7(rm.mergeOptions(f,{segmentMetadataTrack:i.segmentMetadataTrack_,loaderType:"main"}),e),i.audioSegmentLoader_=new u7(rm.mergeOptions(f,{loaderType:"audio"}),e),i.subtitleSegmentLoader_=new uV(rm.mergeOptions(f,{loaderType:"vtt",featuresNativeTextTracks:i.tech_.featuresNativeTextTracks}),e),i.setupSegmentLoaderListeners_(),lr.forEach(function(e){i[e+"_"]=(function(e){return this.audioSegmentLoader_[e]+this.mainSegmentLoader_[e]}).bind(i,e)}),i.logger_=uA("MPC"),i.masterPlaylistLoader_.load(),i}function lo(e,t,i){of(this,lo);var n,r,s,a=e.masterPlaylistController_,o=a[(e.options_.smoothQualityChange?"smooth":"fast")+"QualityChange_"].bind(a);if(t.attributes.RESOLUTION){var u=t.attributes.RESOLUTION;this.width=u.width,this.height=u.height}this.bandwidth=t.attributes.BANDWIDTH,this.id=i,this.enabled=(n=e.playlists,r=t.id,s=o,function(e){var t=n.master.playlists[r],i=oR(t),a=oN(t);return void 0===e?a:(e?delete t.disabled:t.disabled=!0,e===a||i||(s(),e?n.trigger("renditionenabled"):n.trigger("renditiondisabled")),e)})}var lu=["seeking","seeked","pause","playing","error"],ll=(om(lc,[{key:"monitorCurrentTime_",value:function(){this.checkCurrentTime_(),this.checkCurrentTimeTimeout_&&e.clearTimeout(this.checkCurrentTimeTimeout_),this.checkCurrentTimeTimeout_=e.setTimeout(this.monitorCurrentTime_.bind(this),250)}},{key:"checkCurrentTime_",value:function(){if(this.tech_.seeking()&&this.fixesBadSeeks_())return this.consecutiveUpdates=0,void(this.lastRecordedTime=this.tech_.currentTime());if(!this.tech_.paused()&&!this.tech_.seeking()){var e=this.tech_.currentTime(),t=this.tech_.buffered();if(this.lastRecordedTime===e&&(!t.length||e+.1>=t.end(t.length-1)))return this.techWaiting_();5<=this.consecutiveUpdates&&e===this.lastRecordedTime?(this.consecutiveUpdates++,this.waiting_()):e===this.lastRecordedTime?this.consecutiveUpdates++:(this.consecutiveUpdates=0,this.lastRecordedTime=e)}}},{key:"cancelTimer_",value:function(){this.consecutiveUpdates=0,this.timer_&&(this.logger_("cancelTimer_"),clearTimeout(this.timer_)),this.timer_=null}},{key:"fixesBadSeeks_",value:function(){if(!this.tech_.seeking())return!1;var e=this.seekable(),t=this.tech_.currentTime(),i=void 0;return this.afterSeekableWindow_(e,t,this.media(),this.allowSeeksWithinUnsafeLiveWindow)&&(i=e.end(e.length-1)),this.beforeSeekableWindow_(e,t)&&(i=e.start(0)+.1),void 0!==i&&(this.logger_("Trying to seek outside of seekable at time "+t+" with seekable range "+oV(e)+". Seeking to "+i+"."),this.tech_.setCurrentTime(i),!0)}},{key:"waiting_",value:function(){if(!this.techWaiting_()){var e=this.tech_.currentTime(),t=oH(this.tech_.buffered(),e);return t.length&&e+3<=t.end(0)?(this.cancelTimer_(),this.tech_.setCurrentTime(e),this.logger_("Stopped at "+e+" while inside a buffered region ["+t.start(0)+" -> "+t.end(0)+"]. Attempting to resume playback by seeking to the current time."),void this.tech_.trigger({type:"usage",name:"hls-unknown-waiting"})):void 0}}},{key:"techWaiting_",value:function(){var e=this.seekable(),t=this.tech_.currentTime();if(this.tech_.seeking()&&this.fixesBadSeeks_()||this.tech_.seeking()||null!==this.timer_)return!0;if(this.beforeSeekableWindow_(e,t)){var i=e.end(e.length-1);return this.logger_("Fell out of live window at time "+t+". Seeking to live point (seekable end) "+i),this.cancelTimer_(),this.tech_.setCurrentTime(i),this.tech_.trigger({type:"usage",name:"hls-live-resync"}),!0}var n=this.tech_.buffered(),r=oq(n,t);if(this.videoUnderflow_(r,n,t))return this.cancelTimer_(),this.tech_.setCurrentTime(t),this.tech_.trigger({type:"usage",name:"hls-video-underflow"}),!0;if(0<r.length){var s=r.start(0)-t;return this.logger_("Stopped at "+t+", setting timer for "+s+", seeking to "+r.start(0)),this.timer_=setTimeout(this.skipTheGap_.bind(this),1e3*s,t),!0}return!1}},{key:"afterSeekableWindow_",value:function(e,t,i,n){var r=3<arguments.length&&void 0!==n&&n;if(!e.length)return!1;var s=e.end(e.length-1)+.1;return!i.endList&&r&&(s=e.end(e.length-1)+3*i.targetDuration),s<t}},{key:"beforeSeekableWindow_",value:function(e,t){return!!(e.length&&0<e.start(0)&&t<e.start(0)-.1)}},{key:"videoUnderflow_",value:function(e,t,i){if(0===e.length){var n=this.gapFromVideoUnderflow_(t,i);if(n)return this.logger_("Encountered a gap in video from "+n.start+" to "+n.end+". Seeking to current time "+i),!0}return!1}},{key:"skipTheGap_",value:function(e){var t=this.tech_.buffered(),i=this.tech_.currentTime(),n=oq(t,i);this.cancelTimer_(),0!==n.length&&i===e&&(this.logger_("skipTheGap_:","currentTime:",i,"scheduled currentTime:",e,"nextRange start:",n.start(0)),this.tech_.setCurrentTime(n.start(0)+1/30),this.tech_.trigger({type:"usage",name:"hls-gap-skip"}))}},{key:"gapFromVideoUnderflow_",value:function(e,t){for(var i=function(e){if(e.length<2)return rm.createTimeRanges();for(var t=[],i=1;i<e.length;i++){var n=e.end(i-1),r=e.start(i);t.push([n,r])}return rm.createTimeRanges(t)}(e),n=0;n<i.length;n++){var r=i.start(n),s=i.end(n);if(t-r<4&&2<t-r)return{start:r,end:s}}return null}}]),lc);function lc(t){var i=this;function n(){return i.monitorCurrentTime_()}function r(){return i.techWaiting_()}function s(){return i.cancelTimer_()}function a(){return i.fixesBadSeeks_()}of(this,lc),this.tech_=t.tech,this.seekable=t.seekable,this.allowSeeksWithinUnsafeLiveWindow=t.allowSeeksWithinUnsafeLiveWindow,this.media=t.media,this.consecutiveUpdates=0,this.lastRecordedTime=null,this.timer_=null,this.checkCurrentTimeTimeout_=null,this.logger_=uA("PlaybackWatcher"),this.logger_("initialize"),this.tech_.on("seekablechanged",a),this.tech_.on("waiting",r),this.tech_.on(lu,s),this.tech_.on("canplay",n),this.dispose=function(){i.logger_("dispose"),i.tech_.off("seekablechanged",a),i.tech_.off("waiting",r),i.tech_.off(lu,s),i.tech_.off("canplay",n),i.checkCurrentTimeTimeout_&&e.clearTimeout(i.checkCurrentTimeTimeout_),i.cancelTimer_()}}function lh(e){!function e(t,i){var n=0,r=0,s=rm.mergeOptions(ld,i);function a(){r&&t.currentTime(r)}function o(e){null!=e&&(r=t.duration()!==1/0&&t.currentTime()||0,t.one("loadedmetadata",a),t.src(e),t.trigger({type:"usage",name:"hls-error-reload"}),t.play())}function u(){if(Date.now()-n<1e3*s.errorInterval)t.trigger({type:"usage",name:"hls-error-reload-canceled"});else{if(s.getSource&&"function"==typeof s.getSource)return n=Date.now(),s.getSource.call(t,o);rm.log.error("ERROR: reloadSourceOnError - The option getSource must be a function!")}}function l(){t.off("loadedmetadata",a),t.off("error",u),t.off("dispose",l)}t.ready(function(){t.trigger({type:"usage",name:"hls-error-reload-initialized"})}),t.on("error",u),t.on("dispose",l),t.reloadSourceOnError=function(i){l(),e(t,i)}}(this,e)}var ld={errorInterval:30,getSource:function(e){return e(this.tech({IWillNotUseThisInPlugins:!0}).currentSource_)}},lp={PlaylistLoader:ow,Playlist:o9,Decrypter:oc,AsyncStream:or,decrypt:oo,utils:oZ,STANDARD_PLAYLIST_SELECTOR:function(){var t=this.useDevicePixelRatio&&e.devicePixelRatio||1;return function(t,i,n,r,s){var a=t.playlists.map(function(t){var i,n;return i=t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width,n=t.attributes.RESOLUTION&&t.attributes.RESOLUTION.height,{bandwidth:t.attributes.BANDWIDTH||e.Number.MAX_VALUE,width:i,height:n,playlist:t}});uU(a,function(e,t){return e.bandwidth-t.bandwidth});var o=(a=a.filter(function(e){return!o9.isIncompatible(e.playlist)})).filter(function(e){return o9.isEnabled(e.playlist)});o.length||(o=a.filter(function(e){return!o9.isDisabled(e.playlist)}));var u=o.filter(function(e){return e.bandwidth*u6.BANDWIDTH_VARIANCE<i}),l=u[u.length-1],c=u.filter(function(e){return e.bandwidth===l.bandwidth})[0];if(!1===s){var h=c||o[0]||a[0];return h?h.playlist:null}var d=u.filter(function(e){return e.width&&e.height});uU(d,function(e,t){return e.width-t.width});var p=d.filter(function(e){return e.width===n&&e.height===r});l=p[p.length-1];var f=p.filter(function(e){return e.bandwidth===l.bandwidth})[0],m=void 0,g=void 0,v=void 0;f||(l=(g=(m=d.filter(function(e){return e.width>n||e.height>r})).filter(function(e){return e.width===m[0].width&&e.height===m[0].height}))[g.length-1],v=g.filter(function(e){return e.bandwidth===l.bandwidth})[0]);var y=v||f||c||o[0]||a[0];return y?y.playlist:null}(this.playlists.master,this.systemBandwidth,parseInt(u4(this.tech_.el(),"width"),10)*t,parseInt(u4(this.tech_.el(),"height"),10)*t,this.limitRenditionByPlayerDimensions)},INITIAL_PLAYLIST_SELECTOR:function(){var e=this.playlists.master.playlists.filter(o9.isEnabled);return uU(e,function(e,t){return u1(e,t)}),e.filter(function(e){return ud(e.attributes.CODECS).videoCodec})[0]||null},comparePlaylistBandwidth:u1,comparePlaylistResolution:function(t,i){var n=void 0,r=void 0;return t.attributes.RESOLUTION&&t.attributes.RESOLUTION.width&&(n=t.attributes.RESOLUTION.width),n=n||e.Number.MAX_VALUE,i.attributes.RESOLUTION&&i.attributes.RESOLUTION.width&&(r=i.attributes.RESOLUTION.width),n===(r=r||e.Number.MAX_VALUE)&&t.attributes.BANDWIDTH&&i.attributes.BANDWIDTH?t.attributes.BANDWIDTH-i.attributes.BANDWIDTH:n-r},xhr:oU()};function lf(e){return/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i.test(e)?"hls":/^application\/dash\+xml/i.test(e)?"dash":null}function lm(e,t){for(var i=t.media(),n=-1,r=0;r<e.length;r++)if(e[r].id===i.id){n=r;break}e.selectedIndex_=n,e.trigger({selectedIndex:n,type:"change"})}["GOAL_BUFFER_LENGTH","MAX_GOAL_BUFFER_LENGTH","GOAL_BUFFER_LENGTH_RATE","BUFFER_LOW_WATER_LINE","MAX_BUFFER_LOW_WATER_LINE","BUFFER_LOW_WATER_LINE_RATE","BANDWIDTH_VARIANCE"].forEach(function(e){Object.defineProperty(lp,e,{get:function(){return rm.log.warn("using Hls."+e+" is UNSAFE be sure you know what you are doing"),u6[e]},set:function(t){rm.log.warn("using Hls."+e+" is UNSAFE be sure you know what you are doing"),"number"!=typeof t||t<0?rm.log.warn("value of Hls."+e+" must be greater than or equal to 0"):u6[e]=t}})});var lg="videojs-vhs";function lv(){if(!window.localStorage)return null;var e=window.localStorage.getItem(lg);if(!e)return null;try{return JSON.parse(e)}catch(t){return null}}lp.canPlaySource=function(){return rm.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")},lp.supportsNativeHls=(ly=t.createElement("video"),!!rm.getTech("Html5").isSupported()&&["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"].some(function(e){return/maybe|probably/i.test(ly.canPlayType(e))})),lp.supportsNativeDash=!!rm.getTech("Html5").isSupported()&&/maybe|probably/i.test(t.createElement("video").canPlayType("application/dash+xml")),lp.supportsTypeNatively=function(e){return"hls"===e?lp.supportsNativeHls:"dash"===e&&lp.supportsNativeDash},lp.isSupported=function(){return rm.log.warn("HLS is no longer a tech. Please remove it from your player's techOrder.")};var ly,l8=(ov(l$,rm.getComponent("Component")),om(l$,[{key:"setOptions_",value:function(){var e=this;if(this.options_.withCredentials=this.options_.withCredentials||!1,this.options_.handleManifestRedirects=this.options_.handleManifestRedirects||!1,this.options_.limitRenditionByPlayerDimensions=!1!==this.options_.limitRenditionByPlayerDimensions,this.options_.useDevicePixelRatio=this.options_.useDevicePixelRatio||!1,this.options_.smoothQualityChange=this.options_.smoothQualityChange||!1,this.options_.useBandwidthFromLocalStorage=void 0!==this.source_.useBandwidthFromLocalStorage?this.source_.useBandwidthFromLocalStorage:this.options_.useBandwidthFromLocalStorage||!1,this.options_.customTagParsers=this.options_.customTagParsers||[],this.options_.customTagMappers=this.options_.customTagMappers||[],this.options_.cacheEncryptionKeys=this.options_.cacheEncryptionKeys||!1,"number"!=typeof this.options_.blacklistDuration&&(this.options_.blacklistDuration=300),"number"!=typeof this.options_.bandwidth&&this.options_.useBandwidthFromLocalStorage){var t=lv();t&&t.bandwidth&&(this.options_.bandwidth=t.bandwidth,this.tech_.trigger({type:"usage",name:"hls-bandwidth-from-local-storage"})),t&&t.throughput&&(this.options_.throughput=t.throughput,this.tech_.trigger({type:"usage",name:"hls-throughput-from-local-storage"}))}"number"!=typeof this.options_.bandwidth&&(this.options_.bandwidth=u6.INITIAL_BANDWIDTH),this.options_.enableLowInitialPlaylist=this.options_.enableLowInitialPlaylist&&this.options_.bandwidth===u6.INITIAL_BANDWIDTH,["withCredentials","useDevicePixelRatio","limitRenditionByPlayerDimensions","bandwidth","smoothQualityChange","customTagParsers","customTagMappers","handleManifestRedirects","cacheEncryptionKeys"].forEach(function(t){void 0!==e.source_[t]&&(e.options_[t]=e.source_[t])}),this.limitRenditionByPlayerDimensions=this.options_.limitRenditionByPlayerDimensions,this.useDevicePixelRatio=this.options_.useDevicePixelRatio}},{key:"src",value:function(e,t){var i=this;e&&(this.setOptions_(),this.options_.url=this.source_.src,this.options_.tech=this.tech_,this.options_.externHls=lp,this.options_.sourceType=lf(t),this.options_.seekTo=function(e){i.tech_.setCurrentTime(e)},this.masterPlaylistController_=new ls(this.options_),this.playbackWatcher_=new ll(rm.mergeOptions(this.options_,{seekable:function(){return i.seekable()},media:function(){return i.masterPlaylistController_.media()}})),this.masterPlaylistController_.on("error",function(){rm.players[i.tech_.options_.playerId].error(i.masterPlaylistController_.error)}),this.masterPlaylistController_.selectPlaylist=this.selectPlaylist?this.selectPlaylist.bind(this):lp.STANDARD_PLAYLIST_SELECTOR.bind(this),this.masterPlaylistController_.selectInitialPlaylist=lp.INITIAL_PLAYLIST_SELECTOR.bind(this),this.playlists=this.masterPlaylistController_.masterPlaylistLoader_,this.mediaSource=this.masterPlaylistController_.mediaSource,Object.defineProperties(this,{selectPlaylist:{get:function(){return this.masterPlaylistController_.selectPlaylist},set:function(e){this.masterPlaylistController_.selectPlaylist=e.bind(this)}},throughput:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.throughput.rate},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.throughput.rate=e,this.masterPlaylistController_.mainSegmentLoader_.throughput.count=1}},bandwidth:{get:function(){return this.masterPlaylistController_.mainSegmentLoader_.bandwidth},set:function(e){this.masterPlaylistController_.mainSegmentLoader_.bandwidth=e,this.masterPlaylistController_.mainSegmentLoader_.throughput={rate:0,count:0}}},systemBandwidth:{get:function(){var e=1/(this.bandwidth||1),t=void 0;return Math.floor(1/(e+(t=0<this.throughput?1/this.throughput:0)))},set:function(){rm.log.error('The "systemBandwidth" property is read-only')}}}),this.options_.bandwidth&&(this.bandwidth=this.options_.bandwidth),this.options_.throughput&&(this.throughput=this.options_.throughput),Object.defineProperties(this.stats,{bandwidth:{get:function(){return i.bandwidth||0},enumerable:!0},mediaRequests:{get:function(){return i.masterPlaylistController_.mediaRequests_()||0},enumerable:!0},mediaRequestsAborted:{get:function(){return i.masterPlaylistController_.mediaRequestsAborted_()||0},enumerable:!0},mediaRequestsTimedout:{get:function(){return i.masterPlaylistController_.mediaRequestsTimedout_()||0},enumerable:!0},mediaRequestsErrored:{get:function(){return i.masterPlaylistController_.mediaRequestsErrored_()||0},enumerable:!0},mediaTransferDuration:{get:function(){return i.masterPlaylistController_.mediaTransferDuration_()||0},enumerable:!0},mediaBytesTransferred:{get:function(){return i.masterPlaylistController_.mediaBytesTransferred_()||0},enumerable:!0},mediaSecondsLoaded:{get:function(){return i.masterPlaylistController_.mediaSecondsLoaded_()||0},enumerable:!0},buffered:{get:function(){return oW(i.tech_.buffered())},enumerable:!0},currentTime:{get:function(){return i.tech_.currentTime()},enumerable:!0},currentSource:{get:function(){return i.tech_.currentSource_},enumerable:!0},currentTech:{get:function(){return i.tech_.name_},enumerable:!0},duration:{get:function(){return i.tech_.duration()},enumerable:!0},master:{get:function(){return i.playlists.master},enumerable:!0},playerDimensions:{get:function(){return i.tech_.currentDimensions()},enumerable:!0},seekable:{get:function(){return oW(i.tech_.seekable())},enumerable:!0},timestamp:{get:function(){return Date.now()},enumerable:!0},videoPlaybackQuality:{get:function(){return i.tech_.getVideoPlaybackQuality()},enumerable:!0}}),this.tech_.one("canplay",this.masterPlaylistController_.setupFirstPlay.bind(this.masterPlaylistController_)),this.tech_.on("bandwidthupdate",function(){i.options_.useBandwidthFromLocalStorage&&function(e){if(window.localStorage){var t=lv();t=t?rm.mergeOptions(t,e):e;try{window.localStorage.setItem(lg,JSON.stringify(t))}catch(i){return}}}({bandwidth:i.bandwidth,throughput:Math.round(i.throughput)})}),this.masterPlaylistController_.on("selectedinitialmedia",function(){var e,t;t=(e=i).playlists,e.representations=function(){return t&&t.master&&t.master.playlists?t.master.playlists.filter(function(e){return!oR(e)}).map(function(t,i){return new lo(e,t,t.id)}):[]},function e(t){var i=t.masterPlaylistController_.mainSegmentLoader_,n=t.masterPlaylistController_.audioSegmentLoader_,r=rm.players[t.tech_.options_.playerId];if(r.eme){var s=function(e,t,i){if(!e)return e;var n=void 0,r=void 0;if(i.mimeType_)n=t.mimeType_,r=i.mimeType_;else{var s=uf(t.mimeType_),a=s.parameters.codecs.split(","),o=void 0,u=void 0;a.forEach(function(e){um(e=e.trim())?o=e:ug(e)&&(u=e)}),n=s.type+'; codecs="'+u+'"',r=s.type.replace("video","audio")+'; codecs="'+o+'"'}var l={},c=t.playlist_;for(var h in e)l[h]={audioContentType:r,videoContentType:n},c.contentProtection&&c.contentProtection[h]&&c.contentProtection[h].pssh&&(l[h].pssh=c.contentProtection[h].pssh),"string"==typeof e[h]&&(l[h].url=e[h]);return rm.mergeOptions(e,l)}(t.source_.keySystems,i,n);s&&(r.currentSource().keySystems=s,11!==rm.browser.IE_VERSION&&r.eme.initializeMediaKeys&&r.eme.initializeMediaKeys())}}(i)}),this.on(this.masterPlaylistController_,"progress",function(){this.tech_.trigger("progress")}),this.on(this.masterPlaylistController_,"firstplay",function(){this.ignoreNextSeekingEvent_=!0}),this.setupQualityLevels_(),this.tech_.el()&&this.tech_.src(rm.URL.createObjectURL(this.masterPlaylistController_.mediaSource)))}},{key:"setupQualityLevels_",value:function(){var e=this,t=rm.players[this.tech_.options_.playerId];t&&t.qualityLevels&&!this.qualityLevels_&&(this.qualityLevels_=t.qualityLevels(),this.masterPlaylistController_.on("selectedinitialmedia",function(){var t,i;t=e.qualityLevels_,(i=e).representations().forEach(function(e){t.addQualityLevel(e)}),lm(t,i.playlists)}),this.playlists.on("mediachange",function(){lm(e.qualityLevels_,e.playlists)}))}},{key:"play",value:function(){this.masterPlaylistController_.play()}},{key:"setCurrentTime",value:function(e){this.masterPlaylistController_.setCurrentTime(e)}},{key:"duration",value:function(){return this.masterPlaylistController_.duration()}},{key:"seekable",value:function(){return this.masterPlaylistController_.seekable()}},{key:"dispose",value:function(){this.playbackWatcher_&&this.playbackWatcher_.dispose(),this.masterPlaylistController_&&this.masterPlaylistController_.dispose(),this.qualityLevels_&&this.qualityLevels_.dispose(),this.player_&&(delete this.player_.vhs,delete this.player_.dash,delete this.player_.hls),this.tech_&&this.tech_.hls&&delete this.tech_.hls,(function e(t,i,n){null===t&&(t=Function.prototype);var r=Object.getOwnPropertyDescriptor(t,i);if(void 0===r){var s=Object.getPrototypeOf(t);return null===s?void 0:e(s,i,n)}if("value"in r)return r.value;var a=r.get;return void 0!==a?a.call(n):void 0})(l$.prototype.__proto__||Object.getPrototypeOf(l$.prototype),"dispose",this).call(this)}},{key:"convertToProgramTime",value:function(e,t){return function e(t){var i=t.playlist,n=t.time,r=void 0===n?void 0:n,s=t.callback;if(!s)throw Error("getProgramTime: callback must be provided");if(!i||void 0===r)return s({message:"getProgramTime: playlist and time must be provided"});var a=function(e,t){if(!t||!t.segments||0===t.segments.length)return null;for(var i=0,n=void 0,r=0;r<t.segments.length&&!(e<=(i=(n=t.segments[r]).videoTimingInfo?n.videoTimingInfo.transmuxedPresentationEnd:i+n.duration));r++);var s=t.segments[t.segments.length-1];if(s.videoTimingInfo&&s.videoTimingInfo.transmuxedPresentationEnd<e)return null;if(i<e){if(e>i+.25*s.duration)return null;n=s}return{segment:n,estimatedStart:n.videoTimingInfo?n.videoTimingInfo.transmuxedPresentationStart:i-n.duration,type:n.videoTimingInfo?"accurate":"estimate"}}(r,i);if(!a)return s({message:"valid programTime was not found"});if("estimate"===a.type)return s({message:"Accurate programTime could not be determined. Please seek to e.seekTime and try again",seekTime:a.estimatedStart});var o={mediaSeconds:r},u=function(e,t){if(!t.dateTimeObject)return null;var i=t.videoTimingInfo.transmuxerPrependedSeconds,n=e-(t.videoTimingInfo.transmuxedPresentationStart+i);return new Date(t.dateTimeObject.getTime()+1e3*n)}(r,a.segment);return u&&(o.programDateTime=u.toISOString()),s(null,o)}({playlist:this.masterPlaylistController_.media(),time:e,callback:t})}},{key:"seekToProgramTime",value:function(e,t,i,n){var r=!(2<arguments.length&&void 0!==i)||i,s=3<arguments.length&&void 0!==n?n:2;return function e(t){var i=t.programTime,n=t.playlist,r=t.retryCount,s=void 0===r?2:r,a=t.seekTo,o=t.pauseAfterSeek,u=void 0===o||o,l=t.tech,c=t.callback;if(!c)throw Error("seekToProgramTime: callback must be provided");if(void 0===i||!n||!a)return c({message:"seekToProgramTime: programTime, seekTo and playlist must be provided"});if(!n.endList&&!l.hasStarted_)return c({message:"player must be playing a live stream to start buffering"});if(!function(e){if(!e.segments||0===e.segments.length)return!1;for(var t=0;t<e.segments.length;t++)if(!e.segments[t].dateTimeObject)return!1;return!0}(n))return c({message:"programDateTime tags must be provided in the manifest "+n.resolvedUri});var h=function(e,t){var i=void 0;try{i=new Date(e)}catch(n){return null}if(!t||!t.segments||0===t.segments.length)return null;var r=t.segments[0];if(i<r.dateTimeObject)return null;for(var s=0;s<t.segments.length-1&&(r=t.segments[s],!(i<t.segments[s+1].dateTimeObject));s++);var a,o=t.segments[t.segments.length-1],u=o.dateTimeObject,l=o.videoTimingInfo?(a=o.videoTimingInfo).transmuxedPresentationEnd-a.transmuxedPresentationStart-a.transmuxerPrependedSeconds:o.duration+.25*o.duration;return new Date(u.getTime()+1e3*l)<i?null:(u<i&&(r=o),{segment:r,estimatedStart:r.videoTimingInfo?r.videoTimingInfo.transmuxedPresentationStart:o9.duration(t,t.mediaSequence+t.segments.indexOf(r)),type:r.videoTimingInfo?"accurate":"estimate"})}(i,n);if(!h)return c({message:i+" was not found in the stream"});var d=h.segment,p=function(e,t){var i=void 0,n=void 0;try{i=new Date(e),n=new Date(t)}catch(r){}var s=i.getTime();return(n.getTime()-s)/1e3}(d.dateTimeObject,i);if("estimate"===h.type)return 0===s?c({message:i+" is not buffered yet. Try again"}):(a(h.estimatedStart+p),void l.one("seeked",function(){e({programTime:i,playlist:n,retryCount:s-1,seekTo:a,pauseAfterSeek:u,tech:l,callback:c})}));var f=d.start+p;l.one("seeked",function(){return c(null,l.currentTime())}),u&&l.pause(),a(f)}({programTime:e,playlist:this.masterPlaylistController_.media(),retryCount:s,pauseAfterSeek:r,seekTo:this.options_.seekTo,tech:this.options_.tech,callback:t})}}]),l$);function l$(e,i,n){of(this,l$);var r=oy(this,(l$.__proto__||Object.getPrototypeOf(l$)).call(this,i,n.hls));if(i.options_&&i.options_.playerId){var s=rm(i.options_.playerId);s.hasOwnProperty("hls")||Object.defineProperty(s,"hls",{get:function(){return rm.log.warn("player.hls is deprecated. Use player.tech().hls instead."),i.trigger({type:"usage",name:"hls-player-access"}),r},configurable:!0}),s.vhs=r,(s.dash=r).player_=s}if(r.tech_=i,r.source_=e,r.stats={},r.ignoreNextSeekingEvent_=!1,r.setOptions_(),r.options_.overrideNative&&i.overrideNativeAudioTracks&&i.overrideNativeVideoTracks)i.overrideNativeAudioTracks(!0),i.overrideNativeVideoTracks(!0);else if(r.options_.overrideNative&&(i.featuresNativeVideoTracks||i.featuresNativeAudioTracks))throw Error("Overriding native HLS requires emulated tracks. See https://git.io/vMpjB");return r.on(t,["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"],function(e){var i=t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement;i&&i.contains(r.tech_.el())&&r.masterPlaylistController_.smoothQualityChange_()}),r.on(r.tech_,"seeking",function(){this.ignoreNextSeekingEvent_?this.ignoreNextSeekingEvent_=!1:this.setCurrentTime(this.tech_.currentTime())}),r.on(r.tech_,"error",function(){this.masterPlaylistController_&&this.masterPlaylistController_.pauseLoading()}),r.on(r.tech_,"play",r.play),r}var lb={name:"videojs-http-streaming",VERSION:"1.13.2",canHandleSource:function(e,t){var i=rm.mergeOptions(rm.options,1<arguments.length&&void 0!==t?t:{});return lb.canPlayType(e.type,i)},handleSource:function(e,t,i){var n=rm.mergeOptions(rm.options,2<arguments.length&&void 0!==i?i:{});return t.hls=new l8(e,t,n),t.hls.xhr=oU(),t.hls.src(e.src,e.type),t.hls},canPlayType:function(e,t){var i=rm.mergeOptions(rm.options,1<arguments.length&&void 0!==t?t:{}).hls.overrideNative,n=lf(e);return n&&(!lp.supportsTypeNatively(n)||i)?"maybe":""}};return void 0!==rm.MediaSource&&void 0!==rm.URL||(rm.MediaSource=uC,rm.URL=uE),uC.supportsNativeMediaSources()&&rm.getTech("Html5").registerSourceHandler(lb,0),rm.HlsHandler=l8,rm.HlsSourceHandler=lb,rm.Hls=lp,rm.use||rm.registerComponent("Hls",lp),rm.options.hls=rm.options.hls||{},rm.registerPlugin?rm.registerPlugin("reloadSourceOnError",lh):rm.plugin("reloadSourceOnError",lh),rm});
