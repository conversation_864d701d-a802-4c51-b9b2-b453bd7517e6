<?php
/**
 * Russian PHPMailer language file: refer to English translation for definitive list
 * @package PHPMailer
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */

$PHPMAILER_LANG['authenticate']         = 'Ошибка SMTP: ошибка авторизации.';
$PHPMAILER_LANG['connect_host']         = 'Ошибка SMTP: не удается подключиться к серверу SMTP.';
$PHPMAILER_LANG['data_not_accepted']    = 'Ошибка SMTP: данные не приняты.';
$PHPMAILER_LANG['encoding']             = 'Неизвестный вид кодировки: ';
$PHPMAILER_LANG['execute']              = 'Невозможно выполнить команду: ';
$PHPMAILER_LANG['file_access']          = 'Нет доступа к файлу: ';
$PHPMAILER_LANG['file_open']            = 'Файловая ошибка: не удается открыть файл: ';
$PHPMAILER_LANG['from_failed']          = 'Неверный адрес отправителя: ';
$PHPMAILER_LANG['instantiate']          = 'Невозможно запустить функцию mail.';
$PHPMAILER_LANG['provide_address']      = 'Пожалуйста, введите хотя бы один адрес e-mail получателя.';
$PHPMAILER_LANG['mailer_not_supported'] = ' — почтовый сервер не поддерживается.';
$PHPMAILER_LANG['recipients_failed']    = 'Ошибка SMTP: отправка по следующим адресам получателей не удалась: ';
$PHPMAILER_LANG['empty_message']        = 'Пустое сообщение';
$PHPMAILER_LANG['invalid_address']      = 'Не отослано, неправильный формат email адреса: ';
$PHPMAILER_LANG['signing']              = 'Ошибка подписи: ';
$PHPMAILER_LANG['smtp_connect_failed']  = 'Ошибка соединения с SMTP-сервером';
$PHPMAILER_LANG['smtp_error']           = 'Ошибка SMTP-сервера: ';
$PHPMAILER_LANG['variable_set']         = 'Невозможно установить или переустановить переменную: ';
$PHPMAILER_LANG['extension_missing']    = 'Расширение отсутствует: ';
