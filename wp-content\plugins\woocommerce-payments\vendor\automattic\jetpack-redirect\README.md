# Jetpack Redirect package

A package containing functionality to generate URLs to the jetpack.com/redirect/ service

### Usage TODO

See `Automattic\Jetpack\Redirect::get_url()` documentation.

### Testing

```bash
$ composer run phpunit
```
## Using this package in your WordPress plugin

If you plan on using this package in your WordPress plugin, we would recommend that you use [Jetpack Autoloader](https://packagist.org/packages/automattic/jetpack-autoloader) as your autoloader. This will allow for maximum interoperability with other plugins that use this package as well.

## Security

Need to report a security vulnerability? Go to [https://automattic.com/security/](https://automattic.com/security/) or directly to our security bug bounty site [https://hackerone.com/automattic](https://hackerone.com/automattic).

## License

jetpack-redirect is licensed under [GNU General Public License v2 (or later)](./LICENSE.txt)
