/* inverted Theme file for fontIconPicker {@link https://github.com/micc83/fontIconPicker} */
.fip-inverted.icons-selector{font-size:16px;color:#aaa}.fip-inverted.icons-selector .selector{border:1px solid #111;background-color:#333}.fip-inverted.icons-selector .selector-button{background-color:#222;border-left:1px solid #111}.fip-inverted.icons-selector .selector-button:hover{background-color:#000}.fip-inverted.icons-selector .selector-button:hover i{color:#fff}.fip-inverted.icons-selector .selector-button i{color:#eee}.fip-inverted.icons-selector .selected-icon i{color:#ccc;text-shadow:0 0 1px #000}.fip-inverted.icons-selector .selector-popup{-moz-box-shadow:0 1px 1px rgba(255,255,255,.04);-webkit-box-shadow:0 1px 1px rgba(255,255,255,.04);box-shadow:0 1px 1px rgba(255,255,255,.04);border:1px solid #111;background-color:#101010}.fip-inverted.icons-selector .selector-category select,.fip-inverted.icons-selector .selector-search input[type=text]{border:1px solid #111;background:#333;color:#aaa;-moz-box-shadow:none;-webkit-box-shadow:none;box-shadow:none;outline:0}.fip-inverted.icons-selector input::-webkit-input-placeholder{color:#aaa}.fip-inverted.icons-selector input:-moz-placeholder{color:#aaa}.fip-inverted.icons-selector input::-moz-placeholder{color:#aaa}.fip-inverted.icons-selector input:-ms-input-placeholder{color:#aaa!important}.fip-inverted.icons-selector .selector-search i{color:#aaa}.fip-inverted.icons-selector .fip-icons-container{background-color:#333;border:1px solid #111}.fip-inverted.icons-selector .fip-icons-container .loading{color:#aaa}.fip-inverted.icons-selector .fip-box{border:1px solid #111}.fip-inverted.icons-selector .fip-box:hover{background-color:#000;color:#eee}.fip-inverted.icons-selector .selector-footer,.fip-inverted.icons-selector .selector-footer i{color:#aaa}.fip-inverted.icons-selector .selector-arrows i:hover{color:#000}.fip-inverted.icons-selector span.current-icon,.fip-inverted.icons-selector span.current-icon:hover{background-color:#000;color:#fff}.fip-inverted.icons-selector .fip-icon-block:before,.fip-inverted.icons-selector .icons-picker-error i:before{color:#633;text-shadow:none}
