/* Cart/Checkout Pages */
.shipping.recurring-total ul {
	list-style: none outside;
	margin: 0;
	padding: 0;
}
.shipping.recurring-total ul li {
	margin: 0;
	padding: 0.25em 0 0.25em 22px;
	text-indent: -22px;
	list-style: none outside;
}
.shipping.recurring-total ul li input {
	margin: 3px 0.5ex;
}
.shipping.recurring-total ul li label {
	display: inline;
}
.shipping.recurring-total ul .amount {
	font-weight: 700;
}
.woocommerce-page table.shop_table_responsive tbody .recurring-totals th {
	display: table-cell;
}
.woocommerce-page
	table.shop_table_responsive
	tr.recurring-total
	td:not( [data-title] )::before {
	content: '';
}
