@font-face {
	font-family: 'Linearicons';
	src: url('Linearicons.ttf') format('truetype');
	font-weight: normal;
	font-style: normal;
}
[class^="lnricons-"], [class*=" lnricons-"] {
	font-family: 'Linearicons';
	speak: none;
	font-style: normal;
	font-weight: normal;
	font-variant: normal;
	text-transform: none;
	line-height: 1;

	/* Enable Ligatures ================ */
	-webkit-font-feature-settings: "liga";
	-moz-font-feature-settings: "liga=1";
	-moz-font-feature-settings: "liga";
	-ms-font-feature-settings: "liga" 1;
	-o-font-feature-settings: "liga";
	font-feature-settings: "liga";

	/* Better Font Rendering =========== */
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.lnricons-home:before {
	content: "\e600";
}
.lnricons-home2:before {
	content: "\e601";
}
.lnricons-home3:before {
	content: "\e602";
}
.lnricons-home4:before {
	content: "\e603";
}
.lnricons-home5:before {
	content: "\e604";
}
.lnricons-home6:before {
	content: "\e605";
}
.lnricons-bathtub:before {
	content: "\e606";
}
.lnricons-toothbrush:before {
	content: "\e607";
}
.lnricons-bed:before {
	content: "\e608";
}
.lnricons-couch:before {
	content: "\e609";
}
.lnricons-chair:before {
	content: "\e60a";
}
.lnricons-city:before {
	content: "\e60b";
}
.lnricons-apartment:before {
	content: "\e60c";
}
.lnricons-pencil:before {
	content: "\e60d";
}
.lnricons-pencil2:before {
	content: "\e60e";
}
.lnricons-pen:before {
	content: "\e60f";
}
.lnricons-pencil3:before {
	content: "\e610";
}
.lnricons-eraser:before {
	content: "\e611";
}
.lnricons-pencil4:before {
	content: "\e612";
}
.lnricons-pencil5:before {
	content: "\e613";
}
.lnricons-feather:before {
	content: "\e614";
}
.lnricons-feather2:before {
	content: "\e615";
}
.lnricons-feather3:before {
	content: "\e616";
}
.lnricons-pen2:before {
	content: "\e617";
}
.lnricons-pen-add:before {
	content: "\e618";
}
.lnricons-pen-remove:before {
	content: "\e619";
}
.lnricons-vector:before {
	content: "\e61a";
}
.lnricons-pen3:before {
	content: "\e61b";
}
.lnricons-blog:before {
	content: "\e61c";
}
.lnricons-brush:before {
	content: "\e61d";
}
.lnricons-brush2:before {
	content: "\e61e";
}
.lnricons-spray:before {
	content: "\e61f";
}
.lnricons-paint-roller:before {
	content: "\e620";
}
.lnricons-stamp:before {
	content: "\e621";
}
.lnricons-tape:before {
	content: "\e622";
}
.lnricons-desk-tape:before {
	content: "\e623";
}
.lnricons-texture:before {
	content: "\e624";
}
.lnricons-eye-dropper:before {
	content: "\e625";
}
.lnricons-palette:before {
	content: "\e626";
}
.lnricons-color-sampler:before {
	content: "\e627";
}
.lnricons-bucket:before {
	content: "\e628";
}
.lnricons-gradient:before {
	content: "\e629";
}
.lnricons-gradient2:before {
	content: "\e62a";
}
.lnricons-magic-wand:before {
	content: "\e62b";
}
.lnricons-magnet:before {
	content: "\e62c";
}
.lnricons-pencil-ruler:before {
	content: "\e62d";
}
.lnricons-pencil-ruler2:before {
	content: "\e62e";
}
.lnricons-compass:before {
	content: "\e62f";
}
.lnricons-aim:before {
	content: "\e630";
}
.lnricons-gun:before {
	content: "\e631";
}
.lnricons-bottle:before {
	content: "\e632";
}
.lnricons-drop:before {
	content: "\e633";
}
.lnricons-drop-crossed:before {
	content: "\e634";
}
.lnricons-drop2:before {
	content: "\e635";
}
.lnricons-snow:before {
	content: "\e636";
}
.lnricons-snow2:before {
	content: "\e637";
}
.lnricons-fire:before {
	content: "\e638";
}
.lnricons-lighter:before {
	content: "\e639";
}
.lnricons-knife:before {
	content: "\e63a";
}
.lnricons-dagger:before {
	content: "\e63b";
}
.lnricons-tissue:before {
	content: "\e63c";
}
.lnricons-toilet-paper:before {
	content: "\e63d";
}
.lnricons-poop:before {
	content: "\e63e";
}
.lnricons-umbrella:before {
	content: "\e63f";
}
.lnricons-umbrella2:before {
	content: "\e640";
}
.lnricons-rain:before {
	content: "\e641";
}
.lnricons-tornado:before {
	content: "\e642";
}
.lnricons-wind:before {
	content: "\e643";
}
.lnricons-fan:before {
	content: "\e644";
}
.lnricons-contrast:before {
	content: "\e645";
}
.lnricons-sun-small:before {
	content: "\e646";
}
.lnricons-sun:before {
	content: "\e647";
}
.lnricons-sun2:before {
	content: "\e648";
}
.lnricons-moon:before {
	content: "\e649";
}
.lnricons-cloud:before {
	content: "\e64a";
}
.lnricons-cloud-upload:before {
	content: "\e64b";
}
.lnricons-cloud-download:before {
	content: "\e64c";
}
.lnricons-cloud-rain:before {
	content: "\e64d";
}
.lnricons-cloud-hailstones:before {
	content: "\e64e";
}
.lnricons-cloud-snow:before {
	content: "\e64f";
}
.lnricons-cloud-windy:before {
	content: "\e650";
}
.lnricons-sun-wind:before {
	content: "\e651";
}
.lnricons-cloud-fog:before {
	content: "\e652";
}
.lnricons-cloud-sun:before {
	content: "\e653";
}
.lnricons-cloud-lightning:before {
	content: "\e654";
}
.lnricons-cloud-sync:before {
	content: "\e655";
}
.lnricons-cloud-lock:before {
	content: "\e656";
}
.lnricons-cloud-gear:before {
	content: "\e657";
}
.lnricons-cloud-alert:before {
	content: "\e658";
}
.lnricons-cloud-check:before {
	content: "\e659";
}
.lnricons-cloud-cross:before {
	content: "\e65a";
}
.lnricons-cloud-crossed:before {
	content: "\e65b";
}
.lnricons-cloud-database:before {
	content: "\e65c";
}
.lnricons-database:before {
	content: "\e65d";
}
.lnricons-database-add:before {
	content: "\e65e";
}
.lnricons-database-remove:before {
	content: "\e65f";
}
.lnricons-database-lock:before {
	content: "\e660";
}
.lnricons-database-refresh:before {
	content: "\e661";
}
.lnricons-database-check:before {
	content: "\e662";
}
.lnricons-database-history:before {
	content: "\e663";
}
.lnricons-database-upload:before {
	content: "\e664";
}
.lnricons-database-download:before {
	content: "\e665";
}
.lnricons-server:before {
	content: "\e666";
}
.lnricons-shield:before {
	content: "\e667";
}
.lnricons-shield-check:before {
	content: "\e668";
}
.lnricons-shield-alert:before {
	content: "\e669";
}
.lnricons-shield-cross:before {
	content: "\e66a";
}
.lnricons-lock:before {
	content: "\e66b";
}
.lnricons-rotation-lock:before {
	content: "\e66c";
}
.lnricons-unlock:before {
	content: "\e66d";
}
.lnricons-key:before {
	content: "\e66e";
}
.lnricons-key-hole:before {
	content: "\e66f";
}
.lnricons-toggle-off:before {
	content: "\e670";
}
.lnricons-toggle-on:before {
	content: "\e671";
}
.lnricons-cog:before {
	content: "\e672";
}
.lnricons-cog2:before {
	content: "\e673";
}
.lnricons-wrench:before {
	content: "\e674";
}
.lnricons-screwdriver:before {
	content: "\e675";
}
.lnricons-hammer-wrench:before {
	content: "\e676";
}
.lnricons-hammer:before {
	content: "\e677";
}
.lnricons-saw:before {
	content: "\e678";
}
.lnricons-axe:before {
	content: "\e679";
}
.lnricons-axe2:before {
	content: "\e67a";
}
.lnricons-shovel:before {
	content: "\e67b";
}
.lnricons-pickaxe:before {
	content: "\e67c";
}
.lnricons-factory:before {
	content: "\e67d";
}
.lnricons-factory2:before {
	content: "\e67e";
}
.lnricons-recycle:before {
	content: "\e67f";
}
.lnricons-trash:before {
	content: "\e680";
}
.lnricons-trash2:before {
	content: "\e681";
}
.lnricons-trash3:before {
	content: "\e682";
}
.lnricons-broom:before {
	content: "\e683";
}
.lnricons-game:before {
	content: "\e684";
}
.lnricons-gamepad:before {
	content: "\e685";
}
.lnricons-joystick:before {
	content: "\e686";
}
.lnricons-dice:before {
	content: "\e687";
}
.lnricons-spades:before {
	content: "\e688";
}
.lnricons-diamonds:before {
	content: "\e689";
}
.lnricons-clubs:before {
	content: "\e68a";
}
.lnricons-hearts:before {
	content: "\e68b";
}
.lnricons-heart:before {
	content: "\e68c";
}
.lnricons-star:before {
	content: "\e68d";
}
.lnricons-star-half:before {
	content: "\e68e";
}
.lnricons-star-empty:before {
	content: "\e68f";
}
.lnricons-flag:before {
	content: "\e690";
}
.lnricons-flag2:before {
	content: "\e691";
}
.lnricons-flag3:before {
	content: "\e692";
}
.lnricons-mailbox-full:before {
	content: "\e693";
}
.lnricons-mailbox-empty:before {
	content: "\e694";
}
.lnricons-at-sign:before {
	content: "\e695";
}
.lnricons-envelope:before {
	content: "\e696";
}
.lnricons-envelope-open:before {
	content: "\e697";
}
.lnricons-paperclip:before {
	content: "\e698";
}
.lnricons-paper-plane:before {
	content: "\e699";
}
.lnricons-reply:before {
	content: "\e69a";
}
.lnricons-reply-all:before {
	content: "\e69b";
}
.lnricons-inbox:before {
	content: "\e69c";
}
.lnricons-inbox2:before {
	content: "\e69d";
}
.lnricons-outbox:before {
	content: "\e69e";
}
.lnricons-box:before {
	content: "\e69f";
}
.lnricons-archive:before {
	content: "\e6a0";
}
.lnricons-archive2:before {
	content: "\e6a1";
}
.lnricons-drawers:before {
	content: "\e6a2";
}
.lnricons-drawers2:before {
	content: "\e6a3";
}
.lnricons-drawers3:before {
	content: "\e6a4";
}
.lnricons-eye:before {
	content: "\e6a5";
}
.lnricons-eye-crossed:before {
	content: "\e6a6";
}
.lnricons-eye-plus:before {
	content: "\e6a7";
}
.lnricons-eye-minus:before {
	content: "\e6a8";
}
.lnricons-binoculars:before {
	content: "\e6a9";
}
.lnricons-binoculars2:before {
	content: "\e6aa";
}
.lnricons-hdd:before {
	content: "\e6ab";
}
.lnricons-hdd-down:before {
	content: "\e6ac";
}
.lnricons-hdd-up:before {
	content: "\e6ad";
}
.lnricons-floppy-disk:before {
	content: "\e6ae";
}
.lnricons-disc:before {
	content: "\e6af";
}
.lnricons-tape2:before {
	content: "\e6b0";
}
.lnricons-printer:before {
	content: "\e6b1";
}
.lnricons-shredder:before {
	content: "\e6b2";
}
.lnricons-file-empty:before {
	content: "\e6b3";
}
.lnricons-file-add:before {
	content: "\e6b4";
}
.lnricons-file-check:before {
	content: "\e6b5";
}
.lnricons-file-lock:before {
	content: "\e6b6";
}
.lnricons-files:before {
	content: "\e6b7";
}
.lnricons-copy:before {
	content: "\e6b8";
}
.lnricons-compare:before {
	content: "\e6b9";
}
.lnricons-folder:before {
	content: "\e6ba";
}
.lnricons-folder-search:before {
	content: "\e6bb";
}
.lnricons-folder-plus:before {
	content: "\e6bc";
}
.lnricons-folder-minus:before {
	content: "\e6bd";
}
.lnricons-folder-download:before {
	content: "\e6be";
}
.lnricons-folder-upload:before {
	content: "\e6bf";
}
.lnricons-folder-star:before {
	content: "\e6c0";
}
.lnricons-folder-heart:before {
	content: "\e6c1";
}
.lnricons-folder-user:before {
	content: "\e6c2";
}
.lnricons-folder-shared:before {
	content: "\e6c3";
}
.lnricons-folder-music:before {
	content: "\e6c4";
}
.lnricons-folder-picture:before {
	content: "\e6c5";
}
.lnricons-folder-film:before {
	content: "\e6c6";
}
.lnricons-scissors:before {
	content: "\e6c7";
}
.lnricons-paste:before {
	content: "\e6c8";
}
.lnricons-clipboard-empty:before {
	content: "\e6c9";
}
.lnricons-clipboard-pencil:before {
	content: "\e6ca";
}
.lnricons-clipboard-text:before {
	content: "\e6cb";
}
.lnricons-clipboard-check:before {
	content: "\e6cc";
}
.lnricons-clipboard-down:before {
	content: "\e6cd";
}
.lnricons-clipboard-left:before {
	content: "\e6ce";
}
.lnricons-clipboard-alert:before {
	content: "\e6cf";
}
.lnricons-clipboard-user:before {
	content: "\e6d0";
}
.lnricons-register:before {
	content: "\e6d1";
}
.lnricons-enter:before {
	content: "\e6d2";
}
.lnricons-exit:before {
	content: "\e6d3";
}
.lnricons-papers:before {
	content: "\e6d4";
}
.lnricons-news:before {
	content: "\e6d5";
}
.lnricons-reading:before {
	content: "\e6d6";
}
.lnricons-typewriter:before {
	content: "\e6d7";
}
.lnricons-document:before {
	content: "\e6d8";
}
.lnricons-document2:before {
	content: "\e6d9";
}
.lnricons-graduation-hat:before {
	content: "\e6da";
}
.lnricons-license:before {
	content: "\e6db";
}
.lnricons-license2:before {
	content: "\e6dc";
}
.lnricons-medal-empty:before {
	content: "\e6dd";
}
.lnricons-medal-first:before {
	content: "\e6de";
}
.lnricons-medal-second:before {
	content: "\e6df";
}
.lnricons-medal-third:before {
	content: "\e6e0";
}
.lnricons-podium:before {
	content: "\e6e1";
}
.lnricons-trophy:before {
	content: "\e6e2";
}
.lnricons-trophy2:before {
	content: "\e6e3";
}
.lnricons-music-note:before {
	content: "\e6e4";
}
.lnricons-music-note2:before {
	content: "\e6e5";
}
.lnricons-music-note3:before {
	content: "\e6e6";
}
.lnricons-playlist:before {
	content: "\e6e7";
}
.lnricons-playlist-add:before {
	content: "\e6e8";
}
.lnricons-guitar:before {
	content: "\e6e9";
}
.lnricons-trumpet:before {
	content: "\e6ea";
}
.lnricons-album:before {
	content: "\e6eb";
}
.lnricons-shuffle:before {
	content: "\e6ec";
}
.lnricons-repeat-one:before {
	content: "\e6ed";
}
.lnricons-repeat:before {
	content: "\e6ee";
}
.lnricons-headphones:before {
	content: "\e6ef";
}
.lnricons-headset:before {
	content: "\e6f0";
}
.lnricons-loudspeaker:before {
	content: "\e6f1";
}
.lnricons-equalizer:before {
	content: "\e6f2";
}
.lnricons-theater:before {
	content: "\e6f3";
}
.lnricons-3d-glasses:before {
	content: "\e6f4";
}
.lnricons-ticket:before {
	content: "\e6f5";
}
.lnricons-presentation:before {
	content: "\e6f6";
}
.lnricons-play:before {
	content: "\e6f7";
}
.lnricons-film-play:before {
	content: "\e6f8";
}
.lnricons-clapboard-play:before {
	content: "\e6f9";
}
.lnricons-media:before {
	content: "\e6fa";
}
.lnricons-film:before {
	content: "\e6fb";
}
.lnricons-film2:before {
	content: "\e6fc";
}
.lnricons-surveillance:before {
	content: "\e6fd";
}
.lnricons-surveillance2:before {
	content: "\e6fe";
}
.lnricons-camera:before {
	content: "\e6ff";
}
.lnricons-camera-crossed:before {
	content: "\e700";
}
.lnricons-camera-play:before {
	content: "\e701";
}
.lnricons-time-lapse:before {
	content: "\e702";
}
.lnricons-record:before {
	content: "\e703";
}
.lnricons-camera2:before {
	content: "\e704";
}
.lnricons-camera-flip:before {
	content: "\e705";
}
.lnricons-panorama:before {
	content: "\e706";
}
.lnricons-time-lapse2:before {
	content: "\e707";
}
.lnricons-shutter:before {
	content: "\e708";
}
.lnricons-shutter2:before {
	content: "\e709";
}
.lnricons-face-detection:before {
	content: "\e70a";
}
.lnricons-flare:before {
	content: "\e70b";
}
.lnricons-convex:before {
	content: "\e70c";
}
.lnricons-concave:before {
	content: "\e70d";
}
.lnricons-picture:before {
	content: "\e70e";
}
.lnricons-picture2:before {
	content: "\e70f";
}
.lnricons-picture3:before {
	content: "\e710";
}
.lnricons-pictures:before {
	content: "\e711";
}
.lnricons-book:before {
	content: "\e712";
}
.lnricons-audio-book:before {
	content: "\e713";
}
.lnricons-book2:before {
	content: "\e714";
}
.lnricons-bookmark:before {
	content: "\e715";
}
.lnricons-bookmark2:before {
	content: "\e716";
}
.lnricons-label:before {
	content: "\e717";
}
.lnricons-library:before {
	content: "\e718";
}
.lnricons-library2:before {
	content: "\e719";
}
.lnricons-contacts:before {
	content: "\e71a";
}
.lnricons-profile:before {
	content: "\e71b";
}
.lnricons-portrait:before {
	content: "\e71c";
}
.lnricons-portrait2:before {
	content: "\e71d";
}
.lnricons-user:before {
	content: "\e71e";
}
.lnricons-user-plus:before {
	content: "\e71f";
}
.lnricons-user-minus:before {
	content: "\e720";
}
.lnricons-user-lock:before {
	content: "\e721";
}
.lnricons-users:before {
	content: "\e722";
}
.lnricons-users2:before {
	content: "\e723";
}
.lnricons-users-plus:before {
	content: "\e724";
}
.lnricons-users-minus:before {
	content: "\e725";
}
.lnricons-group-work:before {
	content: "\e726";
}
.lnricons-woman:before {
	content: "\e727";
}
.lnricons-man:before {
	content: "\e728";
}
.lnricons-baby:before {
	content: "\e729";
}
.lnricons-baby2:before {
	content: "\e72a";
}
.lnricons-baby3:before {
	content: "\e72b";
}
.lnricons-baby-bottle:before {
	content: "\e72c";
}
.lnricons-walk:before {
	content: "\e72d";
}
.lnricons-hand-waving:before {
	content: "\e72e";
}
.lnricons-jump:before {
	content: "\e72f";
}
.lnricons-run:before {
	content: "\e730";
}
.lnricons-woman2:before {
	content: "\e731";
}
.lnricons-man2:before {
	content: "\e732";
}
.lnricons-man-woman:before {
	content: "\e733";
}
.lnricons-height:before {
	content: "\e734";
}
.lnricons-weight:before {
	content: "\e735";
}
.lnricons-scale:before {
	content: "\e736";
}
.lnricons-button:before {
	content: "\e737";
}
.lnricons-bow-tie:before {
	content: "\e738";
}
.lnricons-tie:before {
	content: "\e739";
}
.lnricons-socks:before {
	content: "\e73a";
}
.lnricons-shoe:before {
	content: "\e73b";
}
.lnricons-shoes:before {
	content: "\e73c";
}
.lnricons-hat:before {
	content: "\e73d";
}
.lnricons-pants:before {
	content: "\e73e";
}
.lnricons-shorts:before {
	content: "\e73f";
}
.lnricons-flip-flops:before {
	content: "\e740";
}
.lnricons-shirt:before {
	content: "\e741";
}
.lnricons-hanger:before {
	content: "\e742";
}
.lnricons-laundry:before {
	content: "\e743";
}
.lnricons-store:before {
	content: "\e744";
}
.lnricons-haircut:before {
	content: "\e745";
}
.lnricons-store-24:before {
	content: "\e746";
}
.lnricons-barcode:before {
	content: "\e747";
}
.lnricons-barcode2:before {
	content: "\e748";
}
.lnricons-barcode3:before {
	content: "\e749";
}
.lnricons-cashier:before {
	content: "\e74a";
}
.lnricons-bag:before {
	content: "\e74b";
}
.lnricons-bag2:before {
	content: "\e74c";
}
.lnricons-cart:before {
	content: "\e74d";
}
.lnricons-cart-empty:before {
	content: "\e74e";
}
.lnricons-cart-full:before {
	content: "\e74f";
}
.lnricons-cart-plus:before {
	content: "\e750";
}
.lnricons-cart-plus2:before {
	content: "\e751";
}
.lnricons-cart-add:before {
	content: "\e752";
}
.lnricons-cart-remove:before {
	content: "\e753";
}
.lnricons-cart-exchange:before {
	content: "\e754";
}
.lnricons-tag:before {
	content: "\e755";
}
.lnricons-tags:before {
	content: "\e756";
}
.lnricons-receipt:before {
	content: "\e757";
}
.lnricons-wallet:before {
	content: "\e758";
}
.lnricons-credit-card:before {
	content: "\e759";
}
.lnricons-cash-dollar:before {
	content: "\e75a";
}
.lnricons-cash-euro:before {
	content: "\e75b";
}
.lnricons-cash-pound:before {
	content: "\e75c";
}
.lnricons-cash-yen:before {
	content: "\e75d";
}
.lnricons-bag-dollar:before {
	content: "\e75e";
}
.lnricons-bag-euro:before {
	content: "\e75f";
}
.lnricons-bag-pound:before {
	content: "\e760";
}
.lnricons-bag-yen:before {
	content: "\e761";
}
.lnricons-coin-dollar:before {
	content: "\e762";
}
.lnricons-coin-euro:before {
	content: "\e763";
}
.lnricons-coin-pound:before {
	content: "\e764";
}
.lnricons-coin-yen:before {
	content: "\e765";
}
.lnricons-calculator:before {
	content: "\e766";
}
.lnricons-calculator2:before {
	content: "\e767";
}
.lnricons-abacus:before {
	content: "\e768";
}
.lnricons-vault:before {
	content: "\e769";
}
.lnricons-telephone:before {
	content: "\e76a";
}
.lnricons-phone-lock:before {
	content: "\e76b";
}
.lnricons-phone-wave:before {
	content: "\e76c";
}
.lnricons-phone-pause:before {
	content: "\e76d";
}
.lnricons-phone-outgoing:before {
	content: "\e76e";
}
.lnricons-phone-incoming:before {
	content: "\e76f";
}
.lnricons-phone-in-out:before {
	content: "\e770";
}
.lnricons-phone-error:before {
	content: "\e771";
}
.lnricons-phone-sip:before {
	content: "\e772";
}
.lnricons-phone-plus:before {
	content: "\e773";
}
.lnricons-phone-minus:before {
	content: "\e774";
}
.lnricons-voicemail:before {
	content: "\e775";
}
.lnricons-dial:before {
	content: "\e776";
}
.lnricons-telephone2:before {
	content: "\e777";
}
.lnricons-pushpin:before {
	content: "\e778";
}
.lnricons-pushpin2:before {
	content: "\e779";
}
.lnricons-map-marker:before {
	content: "\e77a";
}
.lnricons-map-marker-user:before {
	content: "\e77b";
}
.lnricons-map-marker-down:before {
	content: "\e77c";
}
.lnricons-map-marker-check:before {
	content: "\e77d";
}
.lnricons-map-marker-crossed:before {
	content: "\e77e";
}
.lnricons-radar:before {
	content: "\e77f";
}
.lnricons-compass2:before {
	content: "\e780";
}
.lnricons-map:before {
	content: "\e781";
}
.lnricons-map2:before {
	content: "\e782";
}
.lnricons-location:before {
	content: "\e783";
}
.lnricons-road-sign:before {
	content: "\e784";
}
.lnricons-calendar-empty:before {
	content: "\e785";
}
.lnricons-calendar-check:before {
	content: "\e786";
}
.lnricons-calendar-cross:before {
	content: "\e787";
}
.lnricons-calendar-31:before {
	content: "\e788";
}
.lnricons-calendar-full:before {
	content: "\e789";
}
.lnricons-calendar-insert:before {
	content: "\e78a";
}
.lnricons-calendar-text:before {
	content: "\e78b";
}
.lnricons-calendar-user:before {
	content: "\e78c";
}
.lnricons-mouse:before {
	content: "\e78d";
}
.lnricons-mouse-left:before {
	content: "\e78e";
}
.lnricons-mouse-right:before {
	content: "\e78f";
}
.lnricons-mouse-both:before {
	content: "\e790";
}
.lnricons-keyboard:before {
	content: "\e791";
}
.lnricons-keyboard-up:before {
	content: "\e792";
}
.lnricons-keyboard-down:before {
	content: "\e793";
}
.lnricons-delete:before {
	content: "\e794";
}
.lnricons-spell-check:before {
	content: "\e795";
}
.lnricons-escape:before {
	content: "\e796";
}
.lnricons-enter2:before {
	content: "\e797";
}
.lnricons-screen:before {
	content: "\e798";
}
.lnricons-aspect-ratio:before {
	content: "\e799";
}
.lnricons-signal:before {
	content: "\e79a";
}
.lnricons-signal-lock:before {
	content: "\e79b";
}
.lnricons-signal-80:before {
	content: "\e79c";
}
.lnricons-signal-60:before {
	content: "\e79d";
}
.lnricons-signal-40:before {
	content: "\e79e";
}
.lnricons-signal-20:before {
	content: "\e79f";
}
.lnricons-signal-0:before {
	content: "\e7a0";
}
.lnricons-signal-blocked:before {
	content: "\e7a1";
}
.lnricons-sim:before {
	content: "\e7a2";
}
.lnricons-flash-memory:before {
	content: "\e7a3";
}
.lnricons-usb-drive:before {
	content: "\e7a4";
}
.lnricons-phone:before {
	content: "\e7a5";
}
.lnricons-smartphone:before {
	content: "\e7a6";
}
.lnricons-smartphone-notification:before {
	content: "\e7a7";
}
.lnricons-smartphone-vibration:before {
	content: "\e7a8";
}
.lnricons-smartphone-embed:before {
	content: "\e7a9";
}
.lnricons-smartphone-waves:before {
	content: "\e7aa";
}
.lnricons-tablet:before {
	content: "\e7ab";
}
.lnricons-tablet2:before {
	content: "\e7ac";
}
.lnricons-laptop:before {
	content: "\e7ad";
}
.lnricons-laptop-phone:before {
	content: "\e7ae";
}
.lnricons-desktop:before {
	content: "\e7af";
}
.lnricons-launch:before {
	content: "\e7b0";
}
.lnricons-new-tab:before {
	content: "\e7b1";
}
.lnricons-window:before {
	content: "\e7b2";
}
.lnricons-cable:before {
	content: "\e7b3";
}
.lnricons-cable2:before {
	content: "\e7b4";
}
.lnricons-tv:before {
	content: "\e7b5";
}
.lnricons-radio:before {
	content: "\e7b6";
}
.lnricons-remote-control:before {
	content: "\e7b7";
}
.lnricons-power-switch:before {
	content: "\e7b8";
}
.lnricons-power:before {
	content: "\e7b9";
}
.lnricons-power-crossed:before {
	content: "\e7ba";
}
.lnricons-flash-auto:before {
	content: "\e7bb";
}
.lnricons-lamp:before {
	content: "\e7bc";
}
.lnricons-flashlight:before {
	content: "\e7bd";
}
.lnricons-lampshade:before {
	content: "\e7be";
}
.lnricons-cord:before {
	content: "\e7bf";
}
.lnricons-outlet:before {
	content: "\e7c0";
}
.lnricons-battery-power:before {
	content: "\e7c1";
}
.lnricons-battery-empty:before {
	content: "\e7c2";
}
.lnricons-battery-alert:before {
	content: "\e7c3";
}
.lnricons-battery-error:before {
	content: "\e7c4";
}
.lnricons-battery-low1:before {
	content: "\e7c5";
}
.lnricons-battery-low2:before {
	content: "\e7c6";
}
.lnricons-battery-low3:before {
	content: "\e7c7";
}
.lnricons-battery-mid1:before {
	content: "\e7c8";
}
.lnricons-battery-mid2:before {
	content: "\e7c9";
}
.lnricons-battery-mid3:before {
	content: "\e7ca";
}
.lnricons-battery-full:before {
	content: "\e7cb";
}
.lnricons-battery-charging:before {
	content: "\e7cc";
}
.lnricons-battery-charging2:before {
	content: "\e7cd";
}
.lnricons-battery-charging3:before {
	content: "\e7ce";
}
.lnricons-battery-charging4:before {
	content: "\e7cf";
}
.lnricons-battery-charging5:before {
	content: "\e7d0";
}
.lnricons-battery-charging6:before {
	content: "\e7d1";
}
.lnricons-battery-charging7:before {
	content: "\e7d2";
}
.lnricons-chip:before {
	content: "\e7d3";
}
.lnricons-chip-x64:before {
	content: "\e7d4";
}
.lnricons-chip-x86:before {
	content: "\e7d5";
}
.lnricons-bubble:before {
	content: "\e7d6";
}
.lnricons-bubbles:before {
	content: "\e7d7";
}
.lnricons-bubble-dots:before {
	content: "\e7d8";
}
.lnricons-bubble-alert:before {
	content: "\e7d9";
}
.lnricons-bubble-question:before {
	content: "\e7da";
}
.lnricons-bubble-text:before {
	content: "\e7db";
}
.lnricons-bubble-pencil:before {
	content: "\e7dc";
}
.lnricons-bubble-picture:before {
	content: "\e7dd";
}
.lnricons-bubble-video:before {
	content: "\e7de";
}
.lnricons-bubble-user:before {
	content: "\e7df";
}
.lnricons-bubble-quote:before {
	content: "\e7e0";
}
.lnricons-bubble-heart:before {
	content: "\e7e1";
}
.lnricons-bubble-emoticon:before {
	content: "\e7e2";
}
.lnricons-bubble-attachment:before {
	content: "\e7e3";
}
.lnricons-phone-bubble:before {
	content: "\e7e4";
}
.lnricons-quote-open:before {
	content: "\e7e5";
}
.lnricons-quote-close:before {
	content: "\e7e6";
}
.lnricons-dna:before {
	content: "\e7e7";
}
.lnricons-heart-pulse:before {
	content: "\e7e8";
}
.lnricons-pulse:before {
	content: "\e7e9";
}
.lnricons-syringe:before {
	content: "\e7ea";
}
.lnricons-pills:before {
	content: "\e7eb";
}
.lnricons-first-aid:before {
	content: "\e7ec";
}
.lnricons-lifebuoy:before {
	content: "\e7ed";
}
.lnricons-bandage:before {
	content: "\e7ee";
}
.lnricons-bandages:before {
	content: "\e7ef";
}
.lnricons-thermometer:before {
	content: "\e7f0";
}
.lnricons-microscope:before {
	content: "\e7f1";
}
.lnricons-brain:before {
	content: "\e7f2";
}
.lnricons-beaker:before {
	content: "\e7f3";
}
.lnricons-skull:before {
	content: "\e7f4";
}
.lnricons-bone:before {
	content: "\e7f5";
}
.lnricons-construction:before {
	content: "\e7f6";
}
.lnricons-construction-cone:before {
	content: "\e7f7";
}
.lnricons-pie-chart:before {
	content: "\e7f8";
}
.lnricons-pie-chart2:before {
	content: "\e7f9";
}
.lnricons-graph:before {
	content: "\e7fa";
}
.lnricons-chart-growth:before {
	content: "\e7fb";
}
.lnricons-chart-bars:before {
	content: "\e7fc";
}
.lnricons-chart-settings:before {
	content: "\e7fd";
}
.lnricons-cake:before {
	content: "\e7fe";
}
.lnricons-gift:before {
	content: "\e7ff";
}
.lnricons-balloon:before {
	content: "\e800";
}
.lnricons-rank:before {
	content: "\e801";
}
.lnricons-rank2:before {
	content: "\e802";
}
.lnricons-rank3:before {
	content: "\e803";
}
.lnricons-crown:before {
	content: "\e804";
}
.lnricons-lotus:before {
	content: "\e805";
}
.lnricons-diamond:before {
	content: "\e806";
}
.lnricons-diamond2:before {
	content: "\e807";
}
.lnricons-diamond3:before {
	content: "\e808";
}
.lnricons-diamond4:before {
	content: "\e809";
}
.lnricons-linearicons:before {
	content: "\e80a";
}
.lnricons-teacup:before {
	content: "\e80b";
}
.lnricons-teapot:before {
	content: "\e80c";
}
.lnricons-glass:before {
	content: "\e80d";
}
.lnricons-bottle2:before {
	content: "\e80e";
}
.lnricons-glass-cocktail:before {
	content: "\e80f";
}
.lnricons-glass2:before {
	content: "\e810";
}
.lnricons-dinner:before {
	content: "\e811";
}
.lnricons-dinner2:before {
	content: "\e812";
}
.lnricons-chef:before {
	content: "\e813";
}
.lnricons-scale2:before {
	content: "\e814";
}
.lnricons-egg:before {
	content: "\e815";
}
.lnricons-egg2:before {
	content: "\e816";
}
.lnricons-eggs:before {
	content: "\e817";
}
.lnricons-platter:before {
	content: "\e818";
}
.lnricons-steak:before {
	content: "\e819";
}
.lnricons-hamburger:before {
	content: "\e81a";
}
.lnricons-hotdog:before {
	content: "\e81b";
}
.lnricons-pizza:before {
	content: "\e81c";
}
.lnricons-sausage:before {
	content: "\e81d";
}
.lnricons-chicken:before {
	content: "\e81e";
}
.lnricons-fish:before {
	content: "\e81f";
}
.lnricons-carrot:before {
	content: "\e820";
}
.lnricons-cheese:before {
	content: "\e821";
}
.lnricons-bread:before {
	content: "\e822";
}
.lnricons-ice-cream:before {
	content: "\e823";
}
.lnricons-ice-cream2:before {
	content: "\e824";
}
.lnricons-candy:before {
	content: "\e825";
}
.lnricons-lollipop:before {
	content: "\e826";
}
.lnricons-coffee-bean:before {
	content: "\e827";
}
.lnricons-coffee-cup:before {
	content: "\e828";
}
.lnricons-cherry:before {
	content: "\e829";
}
.lnricons-grapes:before {
	content: "\e82a";
}
.lnricons-citrus:before {
	content: "\e82b";
}
.lnricons-apple:before {
	content: "\e82c";
}
.lnricons-leaf:before {
	content: "\e82d";
}
.lnricons-landscape:before {
	content: "\e82e";
}
.lnricons-pine-tree:before {
	content: "\e82f";
}
.lnricons-tree:before {
	content: "\e830";
}
.lnricons-cactus:before {
	content: "\e831";
}
.lnricons-paw:before {
	content: "\e832";
}
.lnricons-footprint:before {
	content: "\e833";
}
.lnricons-speed-slow:before {
	content: "\e834";
}
.lnricons-speed-medium:before {
	content: "\e835";
}
.lnricons-speed-fast:before {
	content: "\e836";
}
.lnricons-rocket:before {
	content: "\e837";
}
.lnricons-hammer2:before {
	content: "\e838";
}
.lnricons-balance:before {
	content: "\e839";
}
.lnricons-briefcase:before {
	content: "\e83a";
}
.lnricons-luggage-weight:before {
	content: "\e83b";
}
.lnricons-dolly:before {
	content: "\e83c";
}
.lnricons-plane:before {
	content: "\e83d";
}
.lnricons-plane-crossed:before {
	content: "\e83e";
}
.lnricons-helicopter:before {
	content: "\e83f";
}
.lnricons-traffic-lights:before {
	content: "\e840";
}
.lnricons-siren:before {
	content: "\e841";
}
.lnricons-road:before {
	content: "\e842";
}
.lnricons-engine:before {
	content: "\e843";
}
.lnricons-oil-pressure:before {
	content: "\e844";
}
.lnricons-coolant-temperature:before {
	content: "\e845";
}
.lnricons-car-battery:before {
	content: "\e846";
}
.lnricons-gas:before {
	content: "\e847";
}
.lnricons-gallon:before {
	content: "\e848";
}
.lnricons-transmission:before {
	content: "\e849";
}
.lnricons-car:before {
	content: "\e84a";
}
.lnricons-car-wash:before {
	content: "\e84b";
}
.lnricons-car-wash2:before {
	content: "\e84c";
}
.lnricons-bus:before {
	content: "\e84d";
}
.lnricons-bus2:before {
	content: "\e84e";
}
.lnricons-car2:before {
	content: "\e84f";
}
.lnricons-parking:before {
	content: "\e850";
}
.lnricons-car-lock:before {
	content: "\e851";
}
.lnricons-taxi:before {
	content: "\e852";
}
.lnricons-car-siren:before {
	content: "\e853";
}
.lnricons-car-wash3:before {
	content: "\e854";
}
.lnricons-car-wash4:before {
	content: "\e855";
}
.lnricons-ambulance:before {
	content: "\e856";
}
.lnricons-truck:before {
	content: "\e857";
}
.lnricons-trailer:before {
	content: "\e858";
}
.lnricons-scale-truck:before {
	content: "\e859";
}
.lnricons-train:before {
	content: "\e85a";
}
.lnricons-ship:before {
	content: "\e85b";
}
.lnricons-ship2:before {
	content: "\e85c";
}
.lnricons-anchor:before {
	content: "\e85d";
}
.lnricons-boat:before {
	content: "\e85e";
}
.lnricons-bicycle:before {
	content: "\e85f";
}
.lnricons-bicycle2:before {
	content: "\e860";
}
.lnricons-dumbbell:before {
	content: "\e861";
}
.lnricons-bench-press:before {
	content: "\e862";
}
.lnricons-swim:before {
	content: "\e863";
}
.lnricons-football:before {
	content: "\e864";
}
.lnricons-baseball-bat:before {
	content: "\e865";
}
.lnricons-baseball:before {
	content: "\e866";
}
.lnricons-tennis:before {
	content: "\e867";
}
.lnricons-tennis2:before {
	content: "\e868";
}
.lnricons-ping-pong:before {
	content: "\e869";
}
.lnricons-hockey:before {
	content: "\e86a";
}
.lnricons-8ball:before {
	content: "\e86b";
}
.lnricons-bowling:before {
	content: "\e86c";
}
.lnricons-bowling-pins:before {
	content: "\e86d";
}
.lnricons-golf:before {
	content: "\e86e";
}
.lnricons-golf2:before {
	content: "\e86f";
}
.lnricons-archery:before {
	content: "\e870";
}
.lnricons-slingshot:before {
	content: "\e871";
}
.lnricons-soccer:before {
	content: "\e872";
}
.lnricons-basketball:before {
	content: "\e873";
}
.lnricons-cube:before {
	content: "\e874";
}
.lnricons-3d-rotate:before {
	content: "\e875";
}
.lnricons-puzzle:before {
	content: "\e876";
}
.lnricons-glasses:before {
	content: "\e877";
}
.lnricons-glasses2:before {
	content: "\e878";
}
.lnricons-accessibility:before {
	content: "\e879";
}
.lnricons-wheelchair:before {
	content: "\e87a";
}
.lnricons-wall:before {
	content: "\e87b";
}
.lnricons-fence:before {
	content: "\e87c";
}
.lnricons-wall2:before {
	content: "\e87d";
}
.lnricons-icons:before {
	content: "\e87e";
}
.lnricons-resize-handle:before {
	content: "\e87f";
}
.lnricons-icons2:before {
	content: "\e880";
}
.lnricons-select:before {
	content: "\e881";
}
.lnricons-select2:before {
	content: "\e882";
}
.lnricons-site-map:before {
	content: "\e883";
}
.lnricons-earth:before {
	content: "\e884";
}
.lnricons-earth-lock:before {
	content: "\e885";
}
.lnricons-network:before {
	content: "\e886";
}
.lnricons-network-lock:before {
	content: "\e887";
}
.lnricons-planet:before {
	content: "\e888";
}
.lnricons-happy:before {
	content: "\e889";
}
.lnricons-smile:before {
	content: "\e88a";
}
.lnricons-grin:before {
	content: "\e88b";
}
.lnricons-tongue:before {
	content: "\e88c";
}
.lnricons-sad:before {
	content: "\e88d";
}
.lnricons-wink:before {
	content: "\e88e";
}
.lnricons-dream:before {
	content: "\e88f";
}
.lnricons-shocked:before {
	content: "\e890";
}
.lnricons-shocked2:before {
	content: "\e891";
}
.lnricons-tongue2:before {
	content: "\e892";
}
.lnricons-neutral:before {
	content: "\e893";
}
.lnricons-happy-grin:before {
	content: "\e894";
}
.lnricons-cool:before {
	content: "\e895";
}
.lnricons-mad:before {
	content: "\e896";
}
.lnricons-grin-evil:before {
	content: "\e897";
}
.lnricons-evil:before {
	content: "\e898";
}
.lnricons-wow:before {
	content: "\e899";
}
.lnricons-annoyed:before {
	content: "\e89a";
}
.lnricons-wondering:before {
	content: "\e89b";
}
.lnricons-confused:before {
	content: "\e89c";
}
.lnricons-zipped:before {
	content: "\e89d";
}
.lnricons-grumpy:before {
	content: "\e89e";
}
.lnricons-mustache:before {
	content: "\e89f";
}
.lnricons-tombstone-hipster:before {
	content: "\e8a0";
}
.lnricons-tombstone:before {
	content: "\e8a1";
}
.lnricons-ghost:before {
	content: "\e8a2";
}
.lnricons-ghost-hipster:before {
	content: "\e8a3";
}
.lnricons-halloween:before {
	content: "\e8a4";
}
.lnricons-christmas:before {
	content: "\e8a5";
}
.lnricons-easter-egg:before {
	content: "\e8a6";
}
.lnricons-mustache2:before {
	content: "\e8a7";
}
.lnricons-mustache-glasses:before {
	content: "\e8a8";
}
.lnricons-pipe:before {
	content: "\e8a9";
}
.lnricons-alarm:before {
	content: "\e8aa";
}
.lnricons-alarm-add:before {
	content: "\e8ab";
}
.lnricons-alarm-snooze:before {
	content: "\e8ac";
}
.lnricons-alarm-ringing:before {
	content: "\e8ad";
}
.lnricons-bullhorn:before {
	content: "\e8ae";
}
.lnricons-hearing:before {
	content: "\e8af";
}
.lnricons-volume-high:before {
	content: "\e8b0";
}
.lnricons-volume-medium:before {
	content: "\e8b1";
}
.lnricons-volume-low:before {
	content: "\e8b2";
}
.lnricons-volume:before {
	content: "\e8b3";
}
.lnricons-mute:before {
	content: "\e8b4";
}
.lnricons-lan:before {
	content: "\e8b5";
}
.lnricons-lan2:before {
	content: "\e8b6";
}
.lnricons-wifi:before {
	content: "\e8b7";
}
.lnricons-wifi-lock:before {
	content: "\e8b8";
}
.lnricons-wifi-blocked:before {
	content: "\e8b9";
}
.lnricons-wifi-mid:before {
	content: "\e8ba";
}
.lnricons-wifi-low:before {
	content: "\e8bb";
}
.lnricons-wifi-low2:before {
	content: "\e8bc";
}
.lnricons-wifi-alert:before {
	content: "\e8bd";
}
.lnricons-wifi-alert-mid:before {
	content: "\e8be";
}
.lnricons-wifi-alert-low:before {
	content: "\e8bf";
}
.lnricons-wifi-alert-low2:before {
	content: "\e8c0";
}
.lnricons-stream:before {
	content: "\e8c1";
}
.lnricons-stream-check:before {
	content: "\e8c2";
}
.lnricons-stream-error:before {
	content: "\e8c3";
}
.lnricons-stream-alert:before {
	content: "\e8c4";
}
.lnricons-communication:before {
	content: "\e8c5";
}
.lnricons-communication-crossed:before {
	content: "\e8c6";
}
.lnricons-broadcast:before {
	content: "\e8c7";
}
.lnricons-antenna:before {
	content: "\e8c8";
}
.lnricons-satellite:before {
	content: "\e8c9";
}
.lnricons-satellite2:before {
	content: "\e8ca";
}
.lnricons-mic:before {
	content: "\e8cb";
}
.lnricons-mic-mute:before {
	content: "\e8cc";
}
.lnricons-mic2:before {
	content: "\e8cd";
}
.lnricons-spotlights:before {
	content: "\e8ce";
}
.lnricons-hourglass:before {
	content: "\e8cf";
}
.lnricons-loading:before {
	content: "\e8d0";
}
.lnricons-loading2:before {
	content: "\e8d1";
}
.lnricons-loading3:before {
	content: "\e8d2";
}
.lnricons-refresh:before {
	content: "\e8d3";
}
.lnricons-refresh2:before {
	content: "\e8d4";
}
.lnricons-undo:before {
	content: "\e8d5";
}
.lnricons-redo:before {
	content: "\e8d6";
}
.lnricons-jump2:before {
	content: "\e8d7";
}
.lnricons-undo2:before {
	content: "\e8d8";
}
.lnricons-redo2:before {
	content: "\e8d9";
}
.lnricons-sync:before {
	content: "\e8da";
}
.lnricons-repeat-one2:before {
	content: "\e8db";
}
.lnricons-sync-crossed:before {
	content: "\e8dc";
}
.lnricons-sync2:before {
	content: "\e8dd";
}
.lnricons-repeat-one3:before {
	content: "\e8de";
}
.lnricons-sync-crossed2:before {
	content: "\e8df";
}
.lnricons-return:before {
	content: "\e8e0";
}
.lnricons-return2:before {
	content: "\e8e1";
}
.lnricons-refund:before {
	content: "\e8e2";
}
.lnricons-history:before {
	content: "\e8e3";
}
.lnricons-history2:before {
	content: "\e8e4";
}
.lnricons-self-timer:before {
	content: "\e8e5";
}
.lnricons-clock:before {
	content: "\e8e6";
}
.lnricons-clock2:before {
	content: "\e8e7";
}
.lnricons-clock3:before {
	content: "\e8e8";
}
.lnricons-watch:before {
	content: "\e8e9";
}
.lnricons-alarm2:before {
	content: "\e8ea";
}
.lnricons-alarm-add2:before {
	content: "\e8eb";
}
.lnricons-alarm-remove:before {
	content: "\e8ec";
}
.lnricons-alarm-check:before {
	content: "\e8ed";
}
.lnricons-alarm-error:before {
	content: "\e8ee";
}
.lnricons-timer:before {
	content: "\e8ef";
}
.lnricons-timer-crossed:before {
	content: "\e8f0";
}
.lnricons-timer2:before {
	content: "\e8f1";
}
.lnricons-timer-crossed2:before {
	content: "\e8f2";
}
.lnricons-download:before {
	content: "\e8f3";
}
.lnricons-upload:before {
	content: "\e8f4";
}
.lnricons-download2:before {
	content: "\e8f5";
}
.lnricons-upload2:before {
	content: "\e8f6";
}
.lnricons-enter-up:before {
	content: "\e8f7";
}
.lnricons-enter-down:before {
	content: "\e8f8";
}
.lnricons-enter-left:before {
	content: "\e8f9";
}
.lnricons-enter-right:before {
	content: "\e8fa";
}
.lnricons-exit-up:before {
	content: "\e8fb";
}
.lnricons-exit-down:before {
	content: "\e8fc";
}
.lnricons-exit-left:before {
	content: "\e8fd";
}
.lnricons-exit-right:before {
	content: "\e8fe";
}
.lnricons-enter-up2:before {
	content: "\e8ff";
}
.lnricons-enter-down2:before {
	content: "\e900";
}
.lnricons-enter-vertical:before {
	content: "\e901";
}
.lnricons-enter-left2:before {
	content: "\e902";
}
.lnricons-enter-right2:before {
	content: "\e903";
}
.lnricons-enter-horizontal:before {
	content: "\e904";
}
.lnricons-exit-up2:before {
	content: "\e905";
}
.lnricons-exit-down2:before {
	content: "\e906";
}
.lnricons-exit-left2:before {
	content: "\e907";
}
.lnricons-exit-right2:before {
	content: "\e908";
}
.lnricons-cli:before {
	content: "\e909";
}
.lnricons-bug:before {
	content: "\e90a";
}
.lnricons-code:before {
	content: "\e90b";
}
.lnricons-file-code:before {
	content: "\e90c";
}
.lnricons-file-image:before {
	content: "\e90d";
}
.lnricons-file-zip:before {
	content: "\e90e";
}
.lnricons-file-audio:before {
	content: "\e90f";
}
.lnricons-file-video:before {
	content: "\e910";
}
.lnricons-file-preview:before {
	content: "\e911";
}
.lnricons-file-charts:before {
	content: "\e912";
}
.lnricons-file-stats:before {
	content: "\e913";
}
.lnricons-file-spreadsheet:before {
	content: "\e914";
}
.lnricons-link:before {
	content: "\e915";
}
.lnricons-unlink:before {
	content: "\e916";
}
.lnricons-link2:before {
	content: "\e917";
}
.lnricons-unlink2:before {
	content: "\e918";
}
.lnricons-thumbs-up:before {
	content: "\e919";
}
.lnricons-thumbs-down:before {
	content: "\e91a";
}
.lnricons-thumbs-up2:before {
	content: "\e91b";
}
.lnricons-thumbs-down2:before {
	content: "\e91c";
}
.lnricons-thumbs-up3:before {
	content: "\e91d";
}
.lnricons-thumbs-down3:before {
	content: "\e91e";
}
.lnricons-share:before {
	content: "\e91f";
}
.lnricons-share2:before {
	content: "\e920";
}
.lnricons-share3:before {
	content: "\e921";
}
.lnricons-magnifier:before {
	content: "\e922";
}
.lnricons-file-search:before {
	content: "\e923";
}
.lnricons-find-replace:before {
	content: "\e924";
}
.lnricons-zoom-in:before {
	content: "\e925";
}
.lnricons-zoom-out:before {
	content: "\e926";
}
.lnricons-loupe:before {
	content: "\e927";
}
.lnricons-loupe-zoom-in:before {
	content: "\e928";
}
.lnricons-loupe-zoom-out:before {
	content: "\e929";
}
.lnricons-cross:before {
	content: "\e92a";
}
.lnricons-menu:before {
	content: "\e92b";
}
.lnricons-list:before {
	content: "\e92c";
}
.lnricons-list2:before {
	content: "\e92d";
}
.lnricons-list3:before {
	content: "\e92e";
}
.lnricons-menu2:before {
	content: "\e92f";
}
.lnricons-list4:before {
	content: "\e930";
}
.lnricons-menu3:before {
	content: "\e931";
}
.lnricons-exclamation:before {
	content: "\e932";
}
.lnricons-question:before {
	content: "\e933";
}
.lnricons-check:before {
	content: "\e934";
}
.lnricons-cross2:before {
	content: "\e935";
}
.lnricons-plus:before {
	content: "\e936";
}
.lnricons-minus:before {
	content: "\e937";
}
.lnricons-percent:before {
	content: "\e938";
}
.lnricons-chevron-up:before {
	content: "\e939";
}
.lnricons-chevron-down:before {
	content: "\e93a";
}
.lnricons-chevron-left:before {
	content: "\e93b";
}
.lnricons-chevron-right:before {
	content: "\e93c";
}
.lnricons-chevrons-expand-vertical:before {
	content: "\e93d";
}
.lnricons-chevrons-expand-horizontal:before {
	content: "\e93e";
}
.lnricons-chevrons-contract-vertical:before {
	content: "\e93f";
}
.lnricons-chevrons-contract-horizontal:before {
	content: "\e940";
}
.lnricons-arrow-up:before {
	content: "\e941";
}
.lnricons-arrow-down:before {
	content: "\e942";
}
.lnricons-arrow-left:before {
	content: "\e943";
}
.lnricons-arrow-right:before {
	content: "\e944";
}
.lnricons-arrow-up-right:before {
	content: "\e945";
}
.lnricons-arrows-merge:before {
	content: "\e946";
}
.lnricons-arrows-split:before {
	content: "\e947";
}
.lnricons-arrow-divert:before {
	content: "\e948";
}
.lnricons-arrow-return:before {
	content: "\e949";
}
.lnricons-expand:before {
	content: "\e94a";
}
.lnricons-contract:before {
	content: "\e94b";
}
.lnricons-expand2:before {
	content: "\e94c";
}
.lnricons-contract2:before {
	content: "\e94d";
}
.lnricons-move:before {
	content: "\e94e";
}
.lnricons-tab:before {
	content: "\e94f";
}
.lnricons-arrow-wave:before {
	content: "\e950";
}
.lnricons-expand3:before {
	content: "\e951";
}
.lnricons-expand4:before {
	content: "\e952";
}
.lnricons-contract3:before {
	content: "\e953";
}
.lnricons-notification:before {
	content: "\e954";
}
.lnricons-warning:before {
	content: "\e955";
}
.lnricons-notification-circle:before {
	content: "\e956";
}
.lnricons-question-circle:before {
	content: "\e957";
}
.lnricons-menu-circle:before {
	content: "\e958";
}
.lnricons-checkmark-circle:before {
	content: "\e959";
}
.lnricons-cross-circle:before {
	content: "\e95a";
}
.lnricons-plus-circle:before {
	content: "\e95b";
}
.lnricons-circle-minus:before {
	content: "\e95c";
}
.lnricons-percent-circle:before {
	content: "\e95d";
}
.lnricons-arrow-up-circle:before {
	content: "\e95e";
}
.lnricons-arrow-down-circle:before {
	content: "\e95f";
}
.lnricons-arrow-left-circle:before {
	content: "\e960";
}
.lnricons-arrow-right-circle:before {
	content: "\e961";
}
.lnricons-chevron-up-circle:before {
	content: "\e962";
}
.lnricons-chevron-down-circle:before {
	content: "\e963";
}
.lnricons-chevron-left-circle:before {
	content: "\e964";
}
.lnricons-chevron-right-circle:before {
	content: "\e965";
}
.lnricons-backward-circle:before {
	content: "\e966";
}
.lnricons-first-circle:before {
	content: "\e967";
}
.lnricons-previous-circle:before {
	content: "\e968";
}
.lnricons-stop-circle:before {
	content: "\e969";
}
.lnricons-play-circle:before {
	content: "\e96a";
}
.lnricons-pause-circle:before {
	content: "\e96b";
}
.lnricons-next-circle:before {
	content: "\e96c";
}
.lnricons-last-circle:before {
	content: "\e96d";
}
.lnricons-forward-circle:before {
	content: "\e96e";
}
.lnricons-eject-circle:before {
	content: "\e96f";
}
.lnricons-crop:before {
	content: "\e970";
}
.lnricons-frame-expand:before {
	content: "\e971";
}
.lnricons-frame-contract:before {
	content: "\e972";
}
.lnricons-focus:before {
	content: "\e973";
}
.lnricons-transform:before {
	content: "\e974";
}
.lnricons-grid:before {
	content: "\e975";
}
.lnricons-grid-crossed:before {
	content: "\e976";
}
.lnricons-layers:before {
	content: "\e977";
}
.lnricons-layers-crossed:before {
	content: "\e978";
}
.lnricons-toggle:before {
	content: "\e979";
}
.lnricons-rulers:before {
	content: "\e97a";
}
.lnricons-ruler:before {
	content: "\e97b";
}
.lnricons-funnel:before {
	content: "\e97c";
}
.lnricons-flip-horizontal:before {
	content: "\e97d";
}
.lnricons-flip-vertical:before {
	content: "\e97e";
}
.lnricons-flip-horizontal2:before {
	content: "\e97f";
}
.lnricons-flip-vertical2:before {
	content: "\e980";
}
.lnricons-angle:before {
	content: "\e981";
}
.lnricons-angle2:before {
	content: "\e982";
}
.lnricons-subtract:before {
	content: "\e983";
}
.lnricons-combine:before {
	content: "\e984";
}
.lnricons-intersect:before {
	content: "\e985";
}
.lnricons-exclude:before {
	content: "\e986";
}
.lnricons-align-center-vertical:before {
	content: "\e987";
}
.lnricons-align-right:before {
	content: "\e988";
}
.lnricons-align-bottom:before {
	content: "\e989";
}
.lnricons-align-left:before {
	content: "\e98a";
}
.lnricons-align-center-horizontal:before {
	content: "\e98b";
}
.lnricons-align-top:before {
	content: "\e98c";
}
.lnricons-square:before {
	content: "\e98d";
}
.lnricons-plus-square:before {
	content: "\e98e";
}
.lnricons-minus-square:before {
	content: "\e98f";
}
.lnricons-percent-square:before {
	content: "\e990";
}
.lnricons-arrow-up-square:before {
	content: "\e991";
}
.lnricons-arrow-down-square:before {
	content: "\e992";
}
.lnricons-arrow-left-square:before {
	content: "\e993";
}
.lnricons-arrow-right-square:before {
	content: "\e994";
}
.lnricons-chevron-up-square:before {
	content: "\e995";
}
.lnricons-chevron-down-square:before {
	content: "\e996";
}
.lnricons-chevron-left-square:before {
	content: "\e997";
}
.lnricons-chevron-right-square:before {
	content: "\e998";
}
.lnricons-check-square:before {
	content: "\e999";
}
.lnricons-cross-square:before {
	content: "\e99a";
}
.lnricons-menu-square:before {
	content: "\e99b";
}
.lnricons-prohibited:before {
	content: "\e99c";
}
.lnricons-circle:before {
	content: "\e99d";
}
.lnricons-radio-button:before {
	content: "\e99e";
}
.lnricons-ligature:before {
	content: "\e99f";
}
.lnricons-text-format:before {
	content: "\e9a0";
}
.lnricons-text-format-remove:before {
	content: "\e9a1";
}
.lnricons-text-size:before {
	content: "\e9a2";
}
.lnricons-bold:before {
	content: "\e9a3";
}
.lnricons-italic:before {
	content: "\e9a4";
}
.lnricons-underline:before {
	content: "\e9a5";
}
.lnricons-strikethrough:before {
	content: "\e9a6";
}
.lnricons-highlight:before {
	content: "\e9a7";
}
.lnricons-text-align-left:before {
	content: "\e9a8";
}
.lnricons-text-align-center:before {
	content: "\e9a9";
}
.lnricons-text-align-right:before {
	content: "\e9aa";
}
.lnricons-text-align-justify:before {
	content: "\e9ab";
}
.lnricons-line-spacing:before {
	content: "\e9ac";
}
.lnricons-indent-increase:before {
	content: "\e9ad";
}
.lnricons-indent-decrease:before {
	content: "\e9ae";
}
.lnricons-text-wrap:before {
	content: "\e9af";
}
.lnricons-pilcrow:before {
	content: "\e9b0";
}
.lnricons-direction-ltr:before {
	content: "\e9b1";
}
.lnricons-direction-rtl:before {
	content: "\e9b2";
}
.lnricons-page-break:before {
	content: "\e9b3";
}
.lnricons-page-break2:before {
	content: "\e9b4";
}
.lnricons-sort-alpha-asc:before {
	content: "\e9b5";
}
.lnricons-sort-alpha-desc:before {
	content: "\e9b6";
}
.lnricons-sort-numeric-asc:before {
	content: "\e9b7";
}
.lnricons-sort-numeric-desc:before {
	content: "\e9b8";
}
.lnricons-sort-amount-asc:before {
	content: "\e9b9";
}
.lnricons-sort-amount-desc:before {
	content: "\e9ba";
}
.lnricons-sort-time-asc:before {
	content: "\e9bb";
}
.lnricons-sort-time-desc:before {
	content: "\e9bc";
}
.lnricons-sigma:before {
	content: "\e9bd";
}
.lnricons-pencil-line:before {
	content: "\e9be";
}
.lnricons-hand:before {
	content: "\e9bf";
}
.lnricons-pointer-up:before {
	content: "\e9c0";
}
.lnricons-pointer-right:before {
	content: "\e9c1";
}
.lnricons-pointer-down:before {
	content: "\e9c2";
}
.lnricons-pointer-left:before {
	content: "\e9c3";
}
.lnricons-finger-tap:before {
	content: "\e9c4";
}
.lnricons-fingers-tap:before {
	content: "\e9c5";
}
.lnricons-reminder:before {
	content: "\e9c6";
}
.lnricons-fingers-crossed:before {
	content: "\e9c7";
}
.lnricons-fingers-victory:before {
	content: "\e9c8";
}
.lnricons-gesture-zoom:before {
	content: "\e9c9";
}
.lnricons-gesture-pinch:before {
	content: "\e9ca";
}
.lnricons-fingers-scroll-horizontal:before {
	content: "\e9cb";
}
.lnricons-fingers-scroll-vertical:before {
	content: "\e9cc";
}
.lnricons-fingers-scroll-left:before {
	content: "\e9cd";
}
.lnricons-fingers-scroll-right:before {
	content: "\e9ce";
}
.lnricons-hand2:before {
	content: "\e9cf";
}
.lnricons-pointer-up2:before {
	content: "\e9d0";
}
.lnricons-pointer-right2:before {
	content: "\e9d1";
}
.lnricons-pointer-down2:before {
	content: "\e9d2";
}
.lnricons-pointer-left2:before {
	content: "\e9d3";
}
.lnricons-finger-tap2:before {
	content: "\e9d4";
}
.lnricons-fingers-tap2:before {
	content: "\e9d5";
}
.lnricons-reminder2:before {
	content: "\e9d6";
}
.lnricons-gesture-zoom2:before {
	content: "\e9d7";
}
.lnricons-gesture-pinch2:before {
	content: "\e9d8";
}
.lnricons-fingers-scroll-horizontal2:before {
	content: "\e9d9";
}
.lnricons-fingers-scroll-vertical2:before {
	content: "\e9da";
}
.lnricons-fingers-scroll-left2:before {
	content: "\e9db";
}
.lnricons-fingers-scroll-right2:before {
	content: "\e9dc";
}
.lnricons-fingers-scroll-vertical3:before {
	content: "\e9dd";
}
.lnricons-border-style:before {
	content: "\e9de";
}
.lnricons-border-all:before {
	content: "\e9df";
}
.lnricons-border-outer:before {
	content: "\e9e0";
}
.lnricons-border-inner:before {
	content: "\e9e1";
}
.lnricons-border-top:before {
	content: "\e9e2";
}
.lnricons-border-horizontal:before {
	content: "\e9e3";
}
.lnricons-border-bottom:before {
	content: "\e9e4";
}
.lnricons-border-left:before {
	content: "\e9e5";
}
.lnricons-border-vertical:before {
	content: "\e9e6";
}
.lnricons-border-right:before {
	content: "\e9e7";
}
.lnricons-border-none:before {
	content: "\e9e8";
}
.lnricons-ellipsis:before {
	content: "\e9e9";
}
.lnricons-uni21:before {
	content: "\21";
}
.lnricons-uni22:before {
	content: "\22";
}
.lnricons-uni23:before {
	content: "\23";
}
.lnricons-uni24:before {
	content: "\24";
}
.lnricons-uni25:before {
	content: "\25";
}
.lnricons-uni26:before {
	content: "\26";
}
.lnricons-uni27:before {
	content: "\27";
}
.lnricons-uni28:before {
	content: "\28";
}
.lnricons-uni29:before {
	content: "\29";
}
.lnricons-uni2a:before {
	content: "\2a";
}
.lnricons-uni2b:before {
	content: "\2b";
}
.lnricons-uni2c:before {
	content: "\2c";
}
.lnricons-uni2d:before {
	content: "\2d";
}
.lnricons-uni2e:before {
	content: "\2e";
}
.lnricons-uni2f:before {
	content: "\2f";
}
.lnricons-uni30:before {
	content: "\30";
}
.lnricons-uni31:before {
	content: "\31";
}
.lnricons-uni32:before {
	content: "\32";
}
.lnricons-uni33:before {
	content: "\33";
}
.lnricons-uni34:before {
	content: "\34";
}
.lnricons-uni35:before {
	content: "\35";
}
.lnricons-uni36<div><br></div>:before {
	content: "\36";
}
.lnricons-uni37:before {
	content: "\37";
}
.lnricons-uni38:before {
	content: "\38";
}
.lnricons-uni39:before {
	content: "\39";
}
.lnricons-uni3a:before {
	content: "\3a";
}
.lnricons-uni3b:before {
	content: "\3b";
}
.lnricons-uni3c:before {
	content: "\3c";
}
.lnricons-uni3d:before {
	content: "\3d";
}
.lnricons-uni3e:before {
	content: "\3e";
}
.lnricons-uni3f:before {
	content: "\3f";
}
.lnricons-uni40:before {
	content: "\40";
}
.lnricons-uni41:before {
	content: "\41";
}
.lnricons-uni42:before {
	content: "\42";
}
.lnricons-uni43:before {
	content: "\43";
}
.lnricons-uni44:before {
	content: "\44";
}
.lnricons-uni45:before {
	content: "\45";
}
.lnricons-uni46:before {
	content: "\46";
}
.lnricons-uni47:before {
	content: "\47";
}
.lnricons-uni48:before {
	content: "\48";
}
.lnricons-uni49:before {
	content: "\49";
}
.lnricons-uni4a:before {
	content: "\4a";
}
.lnricons-uni4b:before {
	content: "\4b";
}
.lnricons-uni4c:before {
	content: "\4c";
}
.lnricons-uni4d:before {
	content: "\4d";
}
.lnricons-uni4e:before {
	content: "\4e";
}
.lnricons-uni4f:before {
	content: "\4f";
}
.lnricons-uni50:before {
	content: "\50";
}
.lnricons-uni51:before {
	content: "\51";
}
.lnricons-uni52:before {
	content: "\52";
}
.lnricons-uni53:before {
	content: "\53";
}
.lnricons-uni54:before {
	content: "\54";
}
.lnricons-uni55:before {
	content: "\55";
}
.lnricons-uni56:before {
	content: "\56";
}
.lnricons-uni57:before {
	content: "\57";
}
.lnricons-uni58:before {
	content: "\58";
}
.lnricons-uni59:before {
	content: "\59";
}
.lnricons-uni5a:before {
	content: "\5a";
}
.lnricons-uni5b:before {
	content: "\5b";
}
.lnricons-uni5c:before {
	content: "\5c";
}
.lnricons-uni5d:before {
	content: "\5d";
}
.lnricons-uni5e:before {
	content: "\5e";
}
.lnricons-uni5f:before {
	content: "\5f";
}
.lnricons-uni60:before {
	content: "\60";
}
.lnricons-uni61:before {
	content: "\61";
}
.lnricons-uni62:before {
	content: "\62";
}
.lnricons-uni63:before {
	content: "\63";
}
.lnricons-uni64:before {
	content: "\64";
}
.lnricons-uni65:before {
	content: "\65";
}
.lnricons-uni66:before {
	content: "\66";
}
.lnricons-uni67:before {
	content: "\67";
}
.lnricons-uni68:before {
	content: "\68";
}
.lnricons-uni69:before {
	content: "\69";
}
.lnricons-uni6a:before {
	content: "\6a";
}
.lnricons-uni6b:before {
	content: "\6b";
}
.lnricons-uni6c:before {
	content: "\6c";
}
.lnricons-uni6d:before {
	content: "\6d";
}
.lnricons-uni6e:before {
	content: "\6e";
}
.lnricons-uni6f:before {
	content: "\6f";
}
.lnricons-uni70:before {
	content: "\70";
}
.lnricons-uni71:before {
	content: "\71";
}
.lnricons-uni72:before {
	content: "\72";
}
.lnricons-uni73:before {
	content: "\73";
}
.lnricons-uni74:before {
	content: "\74";
}
.lnricons-uni75:before {
	content: "\75";
}
.lnricons-uni76:before {
	content: "\76";
}
.lnricons-uni77:before {
	content: "\77";
}
.lnricons-uni78:before {
	content: "\78";
}
.lnricons-uni79:before {
	content: "\79";
}
.lnricons-uni7a:before {
	content: "\7a";
}
.lnricons-uni7b:before {
	content: "\7b";
}
.lnricons-uni7c:before {
	content: "\7c";
}
.lnricons-uni7d:before {
	content: "\7d";
}
.lnricons-uni7e:before {
	content: "\7e";
}
.lnricons-copyright:before {
	content: "\a9";
}
