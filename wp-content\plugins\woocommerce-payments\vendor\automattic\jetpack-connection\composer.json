{"name": "automattic/jetpack-connection", "description": "Everything needed to connect to the Jetpack infrastructure", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"automattic/jetpack-a8c-mc-stats": "^1.4.20", "automattic/jetpack-admin-ui": "^0.2.19", "automattic/jetpack-constants": "^1.6.22", "automattic/jetpack-roles": "^1.4.23", "automattic/jetpack-status": "^1.16.4", "automattic/jetpack-redirect": "^1.7.25"}, "require-dev": {"automattic/wordbless": "@dev", "yoast/phpunit-polyfills": "1.0.4", "brain/monkey": "2.6.1", "automattic/jetpack-changelogger": "^3.3.2"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["legacy", "src/", "src/webhooks"]}, "scripts": {"build-production": ["pnpm run build-production"], "build-development": ["pnpm run build"], "phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "post-install-cmd": "WorDBless\\Composer\\InstallDropin::copy", "post-update-cmd": "WorDBless\\Composer\\InstallDropin::copy", "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-connection", "textdomain": "jetpack-connection", "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-connection/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.51.x-dev"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}