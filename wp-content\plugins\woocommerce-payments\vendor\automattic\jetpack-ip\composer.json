{"name": "automattic/jetpack-ip", "description": "Utilities for working with IP addresses.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {}, "require-dev": {"brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4", "automattic/jetpack-changelogger": "^3.3.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-ip", "changelogger": {"link-template": "https://github.com/automattic/jetpack-ip/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.1.x-dev"}, "textdomain": "jetpack-ip", "version-constants": {"::PACKAGE_VERSION": "src/class-utils.php"}}}