msgid ""
msgstr ""
"Project-Id-Version: NUXY\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-23 06:46+0000\n"
"PO-Revision-Date: 2024-12-23 13:21+0000\n"
"Last-Translator: \n"
"Language-Team: Portuguese (Portugal)\n"
"Language: pt_PT\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.1; wp-5.9.3\n"
"X-Domain: nuxy"

#: taxonomy_meta/fields/image.php:10
msgid "Add image"
msgstr "Adicionar imagem"

#: metaboxes/metabox.php:248
msgid "Backup Font Family"
msgstr "Família de fontes de reserva"

#: metaboxes/google_fonts.php:22
msgid "Black 900"
msgstr "Preto 900"

#: metaboxes/google_fonts.php:23
msgid "Black 900 italic"
msgstr "Preto 900 itálico"

#: metaboxes/google_fonts.php:20
msgid "Bold 700"
msgstr "Negrito 700"

#: metaboxes/google_fonts.php:21
msgid "Bold 700 italic"
msgstr "Negrito 700 itálico"

#: metaboxes/google_fonts.php:53
msgid "Capitalize"
msgstr "Capitalizar"

#: metaboxes/google_fonts.php:43
msgid "Center"
msgstr "Centro"

#: settings/settings.php:94
msgid "Choose Page"
msgstr "Escolha página"

#: metaboxes/metabox.php:42
msgid "Choose User"
msgstr "Escolha o Utilizador"

#: metaboxes/fields/text.php:28
msgid "Copied"
msgstr "Copiado"

#: metaboxes/metabox.php:254
msgid "Copy settings"
msgstr "Copiar definições"

#: metaboxes/metabox.php:258
msgid "Couldn't copy settings"
msgstr "Não foi possível copiar configurações"

#: metaboxes/google_fonts.php:29
msgid "Cyrillic"
msgstr "Cirílico"

#: metaboxes/google_fonts.php:30
msgid "Cyrillic ext"
msgstr "Extensão cirílica"

#: metaboxes/fields/duration.php:35
msgid "Days"
msgstr "Dias"

#: metaboxes/google_fonts.php:41
msgid "Default"
msgstr "Predefinição"

#: metaboxes/metabox.php:301
msgid "Delete"
msgstr "Eliminar"

#: taxonomy_meta/fields/image.php:13
msgid "Delete image"
msgstr "Apagar imagem"

#: metaboxes/metabox.php:266
msgid ""
"Download and store Google Fonts locally. Set the fonts in the typography."
msgstr ""
"Transfira e armazene Google Fonts localmente. Defina as fontes na tipografia."

#: metaboxes/metabox.php:265
msgid "Download Google Fonts"
msgstr "Descarregue fontes do Google"

#: metaboxes/fields/duration.php:24
msgid "duration"
msgstr "duração"

#: metaboxes/fields/repeater.php:25 metaboxes/fields/textarea.php:25
#: metaboxes/fields/text.php:27
msgid "Enter"
msgstr "Inserir"

#: metaboxes/fields/number.php:22
msgid "Enter numbers..."
msgstr "Inserir números..."

#: helpers/file_upload.php:21
msgid "Error occurred, please try again"
msgstr "Occoreu um erro, por favor tente novamente"

#: metaboxes/metabox.php:259
msgid "Export options"
msgstr "Opções de exportação"

#: metaboxes/metabox.php:252
msgid "Font Color"
msgstr "Cor da fonte"

#: metaboxes/metabox.php:247
msgid "Font Family"
msgstr "Família de Fonte"

#: metaboxes/metabox.php:243
msgid "Font size"
msgstr "Tamanho da fonte"

#: metaboxes/metabox.php:250
msgid "Font Subsets"
msgstr "Subconjuntos de fonte"

#: metaboxes/metabox.php:263
msgid "Font Synchronize"
msgstr "Sincronizar fontes"

#: metaboxes/metabox.php:249
msgid "Font Weignt & Style"
msgstr "Peso & Estilo da Fonte"

#: metaboxes/google_fonts.php:31
msgid "Greek"
msgstr "Grego"

#: metaboxes/google_fonts.php:32
msgid "Greek ext"
msgstr "Extensão grega"

#: metaboxes/fields/duration.php:34
msgid "Hours"
msgstr "Horas"

#. Author URI of the plugin
msgid "https://stylemixthemes.com"
msgstr "https://stylemixthemes.com"

#: metaboxes/fields/image.php:24
msgid "Image URL"
msgstr "URL da imagem"

#: metaboxes/metabox.php:260
msgid "Import options"
msgstr "Opções de importação"

#: metaboxes/metabox.php:255
msgid "Import settings"
msgstr "Definições de importação"

#: metaboxes/metabox-display.php:27
msgid "Import/Export"
msgstr "Importação/Exportação"

#: helpers/file_upload.php:65
msgid "Invalid file extension"
msgstr "Extensão de ficheiro inválida"

#: metaboxes/google_fonts.php:33
msgid "Latin"
msgstr "Latim"

#: metaboxes/google_fonts.php:34
msgid "Latin ext"
msgstr "Extensão latina"

#: metaboxes/google_fonts.php:42
msgid "Left"
msgstr "Esquerdo"

#: metaboxes/metabox.php:246
msgid "Letter spacing"
msgstr "Espaçamento de letras"

#: metaboxes/google_fonts.php:14
msgid "Light 300"
msgstr "Luz 300"

#: metaboxes/google_fonts.php:15
msgid "Light 300 italic"
msgstr "Claro 300 itálico"

#: metaboxes/metabox.php:244
msgid "Line height"
msgstr "Altura da linha"

#: metaboxes/google_fonts.php:52
msgid "Lowercase"
msgstr "Minúsculos"

#: metaboxes/google_fonts.php:18
msgid "Medium 500"
msgstr "Médio 500"

#: metaboxes/google_fonts.php:19
msgid "Medium 500 italic"
msgstr "Médio 500 itálico"

#: metaboxes/fields/duration.php:33
msgid "Minutes"
msgstr "Minutos"

#: metaboxes/google_fonts.php:50
msgid "Normal"
msgstr "Normal"

#. Name of the plugin
msgid "NUXY"
msgstr "NUXY"

#: settings/settings.php:162
msgid "Oops, something went wrong"
msgstr "Ups, algo correu mal"

#: helpers/file_upload.php:53
msgid "Please, select file"
msgstr "Por favor, selecione ficheiro"

#: metaboxes/metabox.php:302
msgid "Preview"
msgstr "Visualização"

#: metaboxes/google_fonts.php:16
msgid "Regular 400"
msgstr "400 normais"

#: metaboxes/google_fonts.php:17
msgid "Regular 400 italic"
msgstr "Normal 400 itálico"

#: metaboxes/fields/image.php:27
msgid "Remove"
msgstr "Remover"

#: metaboxes/fields/image.php:26
msgid "Replace"
msgstr "Substituir"

#: metaboxes/google_fonts.php:44
msgid "Right"
msgstr "Certo"

#: settings/view/header.php:82
msgid "Save Settings"
msgstr "Guardar Definições"

#: settings/settings.php:158
msgid "Saved!"
msgstr "Salvo!"

#: settings/view/header.php:47
msgid "Search"
msgstr "Pesquisar"

#: taxonomy_meta/fields/image.php:45
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr "Selecione ou Carregue Mídia da sua Persuasão Escolhida"

#: settings/settings.php:159
msgid "Settings are changed"
msgstr "As definições foram alteradas"

#: settings/settings.php:163
msgid "Settings are not changed"
msgstr "As definições não são alteradas"

#: metaboxes/metabox.php:257
msgid "Settings copied to buffer"
msgstr "Configurações copiadas para buffer"

#: metaboxes/metabox.php:261
msgid "Sorry, no matching options."
msgstr "Desculpa, sem opções iguais."

#. Author of the plugin
msgid "StylemixThemes"
msgstr "StylemixThemes"

#: metaboxes/metabox.php:264
msgid ""
"Sync and update your fonts if they are displayed incorrectly on your website."
msgstr ""
"Sincronize e atualize as suas fontes se forem apresentadas incorretamente no "
"seu site."

#: metaboxes/metabox.php:262
msgid "Synchronize"
msgstr "Sincronizar"

#: metaboxes/metabox.php:251
msgid "Text Align"
msgstr "Alinhamento de texto"

#: metaboxes/metabox.php:253
msgid "Text transform"
msgstr "Transformação de texto"

#: metaboxes/google_fonts.php:12
msgid "Thin 100"
msgstr "Fino 100"

#: metaboxes/google_fonts.php:13
msgid "Thin 100 italic"
msgstr "Fino 100 itálico"

#: metaboxes/fields/image.php:25
msgid "Upload"
msgstr "Carregar"

#: metaboxes/google_fonts.php:51
msgid "Uppercase"
msgstr "Maiúsculos"

#: taxonomy_meta/fields/image.php:47
msgid "Use this media"
msgstr "Use este meio de comunicação"

#: metaboxes/google_fonts.php:35
msgid "Vietnamese"
msgstr "Vietnamita"

#: metaboxes/metabox.php:256
msgid ""
"WARNING! This will overwrite all existing option values, please proceed with "
"caution!"
msgstr ""
"AVISO! Isto vai sobrepor todos os valores existentes, por favor proceda "

#: metaboxes/metabox.php:245
msgid "Word spacing"
msgstr "Espaçamento de palavras"

#. Description of the plugin
msgid "WordPress Custom Fields & Theme Options with Vue.js."
msgstr "Campos personalizados e opções de tema WordPress com Vue.js."
