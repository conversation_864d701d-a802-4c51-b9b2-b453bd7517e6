{"name": "automattic/jetpack-status", "description": "Used to retrieve information about the current status of Jetpack and the site overall.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"automattic/jetpack-constants": "^1.6.22"}, "require-dev": {"brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4", "automattic/jetpack-changelogger": "^3.3.2", "automattic/jetpack-ip": "^0.1.3"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-status", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-status/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.17.x-dev"}}}