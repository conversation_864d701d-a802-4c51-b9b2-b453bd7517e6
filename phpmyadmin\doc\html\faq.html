
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>FAQ - Frequently Asked Questions &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="Developers Information" href="developers.html" />
    <link rel="prev" title="Other sources of information" href="other.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="developers.html" title="Developers Information"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="other.html" title="Other sources of information"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FAQ - Frequently Asked Questions</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="faq-frequently-asked-questions">
<span id="faq"></span><h1>FAQ - Frequently Asked Questions<a class="headerlink" href="#faq-frequently-asked-questions" title="Permalink to this headline">¶</a></h1>
<p>Please have a look at our <a class="reference external" href="https://www.phpmyadmin.net/docs/">Link section</a> on the official
phpMyAdmin homepage for in-depth coverage of phpMyAdmin’s features and
or interface.</p>
<div class="section" id="server">
<span id="faqserver"></span><h2>Server<a class="headerlink" href="#server" title="Permalink to this headline">¶</a></h2>
<div class="section" id="my-server-is-crashing-each-time-a-specific-action-is-required-or-phpmyadmin-sends-a-blank-page-or-a-page-full-of-cryptic-characters-to-my-browser-what-can-i-do">
<span id="faq1-1"></span><h3>1.1 My server is crashing each time a specific action is required or phpMyAdmin sends a blank page or a page full of cryptic characters to my browser, what can I do?<a class="headerlink" href="#my-server-is-crashing-each-time-a-specific-action-is-required-or-phpmyadmin-sends-a-blank-page-or-a-page-full-of-cryptic-characters-to-my-browser-what-can-i-do" title="Permalink to this headline">¶</a></h3>
<p>Try to set the <span class="target" id="index-0"></span><a class="reference internal" href="config.html#cfg_OBGzip"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['OBGzip']</span></code></a> directive to <code class="docutils literal notranslate"><span class="pre">false</span></code> in your
<code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> file and the <code class="docutils literal notranslate"><span class="pre">zlib.output_compression</span></code> directive to
<code class="docutils literal notranslate"><span class="pre">Off</span></code> in your php configuration file.</p>
</div>
<div class="section" id="my-apache-server-crashes-when-using-phpmyadmin">
<span id="faq1-2"></span><h3>1.2 My Apache server crashes when using phpMyAdmin.<a class="headerlink" href="#my-apache-server-crashes-when-using-phpmyadmin" title="Permalink to this headline">¶</a></h3>
<p>You should first try the latest versions of Apache (and possibly MySQL). If
your server keeps crashing, please ask for help in the various Apache support
groups.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#faq1-1"><span class="std std-ref">1.1 My server is crashing each time a specific action is required or phpMyAdmin sends a blank page or a page full of cryptic characters to my browser, what can I do?</span></a></p>
</div>
</div>
<div class="section" id="withdrawn">
<span id="faq1-3"></span><h3>1.3 (withdrawn).<a class="headerlink" href="#withdrawn" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="using-phpmyadmin-on-iis-i-m-displayed-the-error-message-the-specified-cgi-application-misbehaved-by-not-returning-a-complete-set-of-http-headers">
<span id="faq1-4"></span><h3>1.4 Using phpMyAdmin on IIS, I’m displayed the error message: “The specified CGI application misbehaved by not returning a complete set of HTTP headers …”.<a class="headerlink" href="#using-phpmyadmin-on-iis-i-m-displayed-the-error-message-the-specified-cgi-application-misbehaved-by-not-returning-a-complete-set-of-http-headers" title="Permalink to this headline">¶</a></h3>
<p>You just forgot to read the <em>install.txt</em> file from the PHP
distribution. Have a look at the last message in this <a class="reference external" href="https://bugs.php.net/bug.php?id=12061">PHP bug report #12061</a> from the official PHP bug
database.</p>
</div>
<div class="section" id="using-phpmyadmin-on-iis-i-m-facing-crashes-and-or-many-error-messages-with-the-http">
<span id="faq1-5"></span><h3>1.5 Using phpMyAdmin on IIS, I’m facing crashes and/or many error messages with the HTTP.<a class="headerlink" href="#using-phpmyadmin-on-iis-i-m-facing-crashes-and-or-many-error-messages-with-the-http" title="Permalink to this headline">¶</a></h3>
<p>This is a known problem with the PHP <a class="reference internal" href="glossary.html#term-ISAPI"><span class="xref std std-term">ISAPI</span></a> filter: it’s not so stable.
Please use instead the cookie authentication mode.</p>
</div>
<div class="section" id="i-can-t-use-phpmyadmin-on-pws-nothing-is-displayed">
<span id="faq1-6"></span><h3>1.6 I can’t use phpMyAdmin on PWS: nothing is displayed!<a class="headerlink" href="#i-can-t-use-phpmyadmin-on-pws-nothing-is-displayed" title="Permalink to this headline">¶</a></h3>
<p>This seems to be a PWS bug. Filippo Simoncini found a workaround (at
this time there is no better fix): remove or comment the <code class="docutils literal notranslate"><span class="pre">DOCTYPE</span></code>
declarations (2 lines) from the scripts <code class="file docutils literal notranslate"><span class="pre">libraries/classes/Header.php</span></code>
and <code class="file docutils literal notranslate"><span class="pre">index.php</span></code>.</p>
</div>
<div class="section" id="how-can-i-gzip-a-dump-or-a-csv-export-it-does-not-seem-to-work">
<span id="faq1-7"></span><h3>1.7 How can I gzip a dump or a CSV export? It does not seem to work.<a class="headerlink" href="#how-can-i-gzip-a-dump-or-a-csv-export-it-does-not-seem-to-work" title="Permalink to this headline">¶</a></h3>
<p>This feature is based on the <code class="docutils literal notranslate"><span class="pre">gzencode()</span></code>
PHP function to be more independent of the platform (Unix/Windows,
Safe Mode or not, and so on). So, you must have Zlib support
(<code class="docutils literal notranslate"><span class="pre">--with-zlib</span></code>).</p>
</div>
<div class="section" id="i-cannot-insert-a-text-file-in-a-table-and-i-get-an-error-about-safe-mode-being-in-effect">
<span id="faq1-8"></span><h3>1.8 I cannot insert a text file in a table, and I get an error about safe mode being in effect.<a class="headerlink" href="#i-cannot-insert-a-text-file-in-a-table-and-i-get-an-error-about-safe-mode-being-in-effect" title="Permalink to this headline">¶</a></h3>
<p>Your uploaded file is saved by PHP in the “upload dir”, as defined in
<code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> by the variable <code class="docutils literal notranslate"><span class="pre">upload_tmp_dir</span></code> (usually the system
default is <em>/tmp</em>). We recommend the following setup for Apache
servers running in safe mode, to enable uploads of files while being
reasonably secure:</p>
<ul class="simple">
<li><p>create a separate directory for uploads: <strong class="command">mkdir /tmp/php</strong></p></li>
<li><p>give ownership to the Apache server’s user.group: <strong class="command">chown
apache.apache /tmp/php</strong></p></li>
<li><p>give proper permission: <strong class="command">chmod 600 /tmp/php</strong></p></li>
<li><p>put <code class="docutils literal notranslate"><span class="pre">upload_tmp_dir</span> <span class="pre">=</span> <span class="pre">/tmp/php</span></code> in <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code></p></li>
<li><p>restart Apache</p></li>
</ul>
</div>
<div class="section" id="faq1-9">
<span id="id1"></span><h3>1.9 (withdrawn).<a class="headerlink" href="#faq1-9" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-m-having-troubles-when-uploading-files-with-phpmyadmin-running-on-a-secure-server-my-browser-is-internet-explorer-and-i-m-using-the-apache-server">
<span id="faq1-10"></span><h3>1.10 I’m having troubles when uploading files with phpMyAdmin running on a secure server. My browser is Internet Explorer and I’m using the Apache server.<a class="headerlink" href="#i-m-having-troubles-when-uploading-files-with-phpmyadmin-running-on-a-secure-server-my-browser-is-internet-explorer-and-i-m-using-the-apache-server" title="Permalink to this headline">¶</a></h3>
<p>As suggested by “Rob M” in the phpWizard forum, add this line to your
<em>httpd.conf</em>:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">SetEnvIf</span> <span class="k">User</span>-Agent <span class="s2">&quot;.*MSIE.*&quot;</span> nokeepalive ssl-unclean-shutdown
</pre></div>
</div>
<p>It seems to clear up many problems between Internet Explorer and SSL.</p>
</div>
<div class="section" id="i-get-an-open-basedir-restriction-while-uploading-a-file-from-the-import-tab">
<span id="faq1-11"></span><h3>1.11 I get an ‘open_basedir restriction’ while uploading a file from the import tab.<a class="headerlink" href="#i-get-an-open-basedir-restriction-while-uploading-a-file-from-the-import-tab" title="Permalink to this headline">¶</a></h3>
<p>Since version 2.2.4, phpMyAdmin supports servers with open_basedir
restrictions. However you need to create temporary directory and configure it
as <span class="target" id="index-1"></span><a class="reference internal" href="config.html#cfg_TempDir"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['TempDir']</span></code></a>. The uploaded files will be moved there,
and after execution of your <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> commands, removed.</p>
</div>
<div class="section" id="i-have-lost-my-mysql-root-password-what-can-i-do">
<span id="faq1-12"></span><h3>1.12 I have lost my MySQL root password, what can I do?<a class="headerlink" href="#i-have-lost-my-mysql-root-password-what-can-i-do" title="Permalink to this headline">¶</a></h3>
<p>phpMyAdmin does authenticate against MySQL server you’re using, so to recover
from phpMyAdmin password loss, you need to recover at MySQL level.</p>
<p>The MySQL manual explains how to <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/resetting-permissions.html">reset the permissions</a>.</p>
<p>If you are using MySQL server installed by your hosting provider, please
contact their support to recover the password for you.</p>
</div>
<div class="section" id="faq1-13">
<span id="id2"></span><h3>1.13 (withdrawn).<a class="headerlink" href="#faq1-13" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq1-14">
<span id="id3"></span><h3>1.14 (withdrawn).<a class="headerlink" href="#faq1-14" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-have-problems-with-mysql-user-column-names">
<span id="faq1-15"></span><h3>1.15 I have problems with <em>mysql.user</em> column names.<a class="headerlink" href="#i-have-problems-with-mysql-user-column-names" title="Permalink to this headline">¶</a></h3>
<p>In previous MySQL versions, the <code class="docutils literal notranslate"><span class="pre">User</span></code> and <code class="docutils literal notranslate"><span class="pre">Password</span></code> columns were
named <code class="docutils literal notranslate"><span class="pre">user</span></code> and <code class="docutils literal notranslate"><span class="pre">password</span></code>. Please modify your column names to
align with current standards.</p>
</div>
<div class="section" id="i-cannot-upload-big-dump-files-memory-http-or-timeout-problems">
<span id="faq1-16"></span><h3>1.16 I cannot upload big dump files (memory, HTTP or timeout problems).<a class="headerlink" href="#i-cannot-upload-big-dump-files-memory-http-or-timeout-problems" title="Permalink to this headline">¶</a></h3>
<p>Starting with version 2.7.0, the import engine has been re–written and
these problems should not occur. If possible, upgrade your phpMyAdmin
to the latest version to take advantage of the new import features.</p>
<p>The first things to check (or ask your host provider to check) are the values
of <code class="docutils literal notranslate"><span class="pre">max_execution_time</span></code>, <code class="docutils literal notranslate"><span class="pre">upload_max_filesize</span></code>, <code class="docutils literal notranslate"><span class="pre">memory_limit</span></code> and
<code class="docutils literal notranslate"><span class="pre">post_max_size</span></code> in the <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> configuration file. All of these
settings limit the maximum size of data that can be submitted and handled by
PHP. Please note that <code class="docutils literal notranslate"><span class="pre">post_max_size</span></code> needs to be larger than
<code class="docutils literal notranslate"><span class="pre">upload_max_filesize</span></code>. There exist several workarounds if your upload is too
big or your hosting provider is unwilling to change the settings:</p>
<ul>
<li><p>Look at the <span class="target" id="index-2"></span><a class="reference internal" href="config.html#cfg_UploadDir"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['UploadDir']</span></code></a> feature. This allows one to upload a file to the server
via scp, FTP, or your favorite file transfer method. PhpMyAdmin is
then able to import the files from the temporary directory. More
information is available in the <a class="reference internal" href="config.html#config"><span class="std std-ref">Configuration</span></a>  of this document.</p></li>
<li><p>Using a utility (such as <a class="reference external" href="https://www.ozerov.de/bigdump/">BigDump</a>) to split the files before
uploading. We cannot support this or any third party applications, but
are aware of users having success with it.</p></li>
<li><p>If you have shell (command line) access, use MySQL to import the files
directly. You can do this by issuing the “source” command from within
MySQL:</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">source</span> <span class="n">filename</span><span class="p">.</span><span class="k">sql</span><span class="p">;</span>
</pre></div>
</div>
</li>
</ul>
</div>
<div class="section" id="which-database-versions-does-phpmyadmin-support">
<span id="faq1-17"></span><h3>1.17 Which Database versions does phpMyAdmin support?<a class="headerlink" href="#which-database-versions-does-phpmyadmin-support" title="Permalink to this headline">¶</a></h3>
<p>For <a class="reference external" href="https://www.mysql.com/">MySQL</a>, versions 5.5 and newer are supported.
For older MySQL versions, our <a class="reference external" href="https://www.phpmyadmin.net/downloads/">Downloads</a> page offers older phpMyAdmin versions
(which may have become unsupported).</p>
<p>For <a class="reference external" href="https://mariadb.org/">MariaDB</a>, versions 5.5 and newer are supported.</p>
</div>
<div class="section" id="a-i-cannot-connect-to-the-mysql-server-it-always-returns-the-error-message-client-does-not-support-authentication-protocol-requested-by-server-consider-upgrading-mysql-client">
<span id="faq1-17a"></span><h3>1.17a I cannot connect to the MySQL server. It always returns the error message, “Client does not support authentication protocol requested by server; consider upgrading MySQL client”<a class="headerlink" href="#a-i-cannot-connect-to-the-mysql-server-it-always-returns-the-error-message-client-does-not-support-authentication-protocol-requested-by-server-consider-upgrading-mysql-client" title="Permalink to this headline">¶</a></h3>
<p>You tried to access MySQL with an old MySQL client library. The
version of your MySQL client library can be checked in your phpinfo()
output. In general, it should have at least the same minor version as
your server - as mentioned in <a class="reference internal" href="#faq1-17"><span class="std std-ref">1.17 Which Database versions does phpMyAdmin support?</span></a>. This problem is
generally caused by using MySQL version 4.1 or newer. MySQL changed
the authentication hash and your PHP is trying to use the old method.
The proper solution is to use the <a class="reference external" href="https://www.php.net/mysqli">mysqli extension</a> with the proper client library to match
your MySQL installation. More
information (and several workarounds) are located in the <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/common-errors.html">MySQL
Documentation</a>.</p>
</div>
<div class="section" id="faq1-18">
<span id="id4"></span><h3>1.18 (withdrawn).<a class="headerlink" href="#faq1-18" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-can-t-run-the-display-relations-feature-because-the-script-seems-not-to-know-the-font-face-i-m-using">
<span id="faq1-19"></span><h3>1.19 I can’t run the “display relations” feature because the script seems not to know the font face I’m using!<a class="headerlink" href="#i-can-t-run-the-display-relations-feature-because-the-script-seems-not-to-know-the-font-face-i-m-using" title="Permalink to this headline">¶</a></h3>
<p>The <a class="reference internal" href="glossary.html#term-TCPDF"><span class="xref std std-term">TCPDF</span></a> library we’re using for this feature requires some special
files to use font faces. Please refers to the <a class="reference external" href="https://tcpdf.org/">TCPDF manual</a> to build these files.</p>
</div>
<div class="section" id="i-receive-an-error-about-missing-mysqli-and-mysql-extensions">
<span id="faqmysql"></span><h3>1.20 I receive an error about missing mysqli and mysql extensions.<a class="headerlink" href="#i-receive-an-error-about-missing-mysqli-and-mysql-extensions" title="Permalink to this headline">¶</a></h3>
<p>To connect to a MySQL server, PHP needs a set of MySQL functions
called “MySQL extension”. This extension may be part of the PHP
distribution (compiled-in), otherwise it needs to be loaded
dynamically. Its name is probably <em>mysqli.so</em> or <em>php_mysqli.dll</em>.
phpMyAdmin tried to load the extension but failed. Usually, the
problem is solved by installing a software package called “PHP-MySQL”
or something similar.</p>
<p>There was two interfaces PHP provided as MySQL extensions - <code class="docutils literal notranslate"><span class="pre">mysql</span></code>
and <code class="docutils literal notranslate"><span class="pre">mysqli</span></code>. The <code class="docutils literal notranslate"><span class="pre">mysql</span></code> interface was removed in PHP 7.0.</p>
<p>This problem can be also caused by wrong paths in the <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> or using
wrong <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>.</p>
<p>Make sure that the extension files do exist in the folder which the
<code class="docutils literal notranslate"><span class="pre">extension_dir</span></code> points to and that the corresponding lines in your
<code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> are not commented out (you can use <code class="docutils literal notranslate"><span class="pre">phpinfo()</span></code> to check
current setup):</p>
<div class="highlight-ini notranslate"><div class="highlight"><pre><span></span><span class="k">[PHP]</span>

<span class="c1">; Directory in which the loadable extensions (modules) reside.</span>
<span class="na">extension_dir</span> <span class="o">=</span> <span class="s">&quot;C:/Apache2/modules/php/ext&quot;</span>
</pre></div>
</div>
<p>The <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> can be loaded from several locations (especially on
Windows), so please check you’re updating the correct one. If using Apache, you
can tell it to use specific path for this file using <code class="docutils literal notranslate"><span class="pre">PHPIniDir</span></code> directive:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">LoadModule</span> php7_module <span class="s2">&quot;C:/php7/php7apache2_4.dll&quot;</span>
<span class="nt">&lt;IfModule</span> <span class="s">php7_module</span><span class="nt">&gt;</span>
    <span class="nb">PHPIniDir</span> <span class="s2">&quot;C:/php7&quot;</span>
    <span class="nt">&lt;Location&gt;</span>
       <span class="nb">AddType</span> text/html .php
       <span class="nb">AddHandler</span> application/x-httpd-php .php
    <span class="nt">&lt;/Location&gt;</span>
<span class="nt">&lt;/IfModule&gt;</span>
</pre></div>
</div>
<p>In some rare cases this problem can be also caused by other extensions loaded
in PHP which prevent MySQL extensions to be loaded. If anything else fails, you
can try commenting out extensions for other databases from <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>.</p>
</div>
<div class="section" id="i-am-running-the-cgi-version-of-php-under-unix-and-i-cannot-log-in-using-cookie-auth">
<span id="faq1-21"></span><h3>1.21 I am running the CGI version of PHP under Unix, and I cannot log in using cookie auth.<a class="headerlink" href="#i-am-running-the-cgi-version-of-php-under-unix-and-i-cannot-log-in-using-cookie-auth" title="Permalink to this headline">¶</a></h3>
<p>In <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>, set <code class="docutils literal notranslate"><span class="pre">mysql.max_links</span></code> higher than 1.</p>
</div>
<div class="section" id="i-don-t-see-the-location-of-text-file-field-so-i-cannot-upload">
<span id="faq1-22"></span><h3>1.22 I don’t see the “Location of text file” field, so I cannot upload.<a class="headerlink" href="#i-don-t-see-the-location-of-text-file-field-so-i-cannot-upload" title="Permalink to this headline">¶</a></h3>
<p>This is most likely because in <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>, your <code class="docutils literal notranslate"><span class="pre">file_uploads</span></code>
parameter is not set to “on”.</p>
</div>
<div class="section" id="i-m-running-mysql-on-a-win32-machine-each-time-i-create-a-new-table-the-table-and-column-names-are-changed-to-lowercase">
<span id="faq1-23"></span><h3>1.23 I’m running MySQL on a Win32 machine. Each time I create a new table the table and column names are changed to lowercase!<a class="headerlink" href="#i-m-running-mysql-on-a-win32-machine-each-time-i-create-a-new-table-the-table-and-column-names-are-changed-to-lowercase" title="Permalink to this headline">¶</a></h3>
<p>This happens because the MySQL directive <code class="docutils literal notranslate"><span class="pre">lower_case_table_names</span></code>
defaults to 1 (<code class="docutils literal notranslate"><span class="pre">ON</span></code>) in the Win32 version of MySQL. You can change
this behavior by simply changing the directive to 0 (<code class="docutils literal notranslate"><span class="pre">OFF</span></code>): Just
edit your <code class="docutils literal notranslate"><span class="pre">my.ini</span></code> file that should be located in your Windows
directory and add the following line to the group [mysqld]:</p>
<div class="highlight-ini notranslate"><div class="highlight"><pre><span></span><span class="na">set-variable</span> <span class="o">=</span> <span class="s">lower_case_table_names=0</span>
</pre></div>
</div>
<div class="admonition note">
<p class="admonition-title">Note</p>
<p>Forcing this variable to 0 with –lower-case-table-names=0 on a
case-insensitive filesystem and access MyISAM tablenames using different
lettercases, index corruption may result.</p>
</div>
<p>Next, save the file and restart the MySQL service. You can always
check the value of this directive using the query</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">SHOW</span> <span class="k">VARIABLES</span> <span class="k">LIKE</span> <span class="s1">&#39;lower_case_table_names&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/identifier-case-sensitivity.html">Identifier Case Sensitivity in the MySQL Reference Manual</a></p>
</div>
</div>
<div class="section" id="faq1-24">
<span id="id5"></span><h3>1.24 (withdrawn).<a class="headerlink" href="#faq1-24" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-am-running-apache-with-mod-gzip-1-3-26-1a-on-windows-xp-and-i-get-problems-such-as-undefined-variables-when-i-run-a-sql-query">
<span id="faq1-25"></span><h3>1.25 I am running Apache with mod_gzip-1.3.26.1a on Windows XP, and I get problems, such as undefined variables when I run a SQL query.<a class="headerlink" href="#i-am-running-apache-with-mod-gzip-1-3-26-1a-on-windows-xp-and-i-get-problems-such-as-undefined-variables-when-i-run-a-sql-query" title="Permalink to this headline">¶</a></h3>
<p>A tip from Jose Fandos: put a comment on the following two lines in
httpd.conf, like this:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="c"># mod_gzip_item_include file \.php$</span>
<span class="c"># mod_gzip_item_include mime &quot;application/x-httpd-php.*&quot;</span>
</pre></div>
</div>
<p>as this version of mod_gzip on Apache (Windows) has problems handling
PHP scripts. Of course you have to restart Apache.</p>
</div>
<div class="section" id="i-just-installed-phpmyadmin-in-my-document-root-of-iis-but-i-get-the-error-no-input-file-specified-when-trying-to-run-phpmyadmin">
<span id="faq1-26"></span><h3>1.26 I just installed phpMyAdmin in my document root of IIS but I get the error “No input file specified” when trying to run phpMyAdmin.<a class="headerlink" href="#i-just-installed-phpmyadmin-in-my-document-root-of-iis-but-i-get-the-error-no-input-file-specified-when-trying-to-run-phpmyadmin" title="Permalink to this headline">¶</a></h3>
<p>This is a permission problem. Right-click on the phpmyadmin folder and
choose properties. Under the tab Security, click on “Add” and select
the user “IUSR_machine” from the list. Now set their permissions and it
should work.</p>
</div>
<div class="section" id="i-get-empty-page-when-i-want-to-view-huge-page-eg-db-structure-php-with-plenty-of-tables">
<span id="faq1-27"></span><h3>1.27 I get empty page when I want to view huge page (eg. db_structure.php with plenty of tables).<a class="headerlink" href="#i-get-empty-page-when-i-want-to-view-huge-page-eg-db-structure-php-with-plenty-of-tables" title="Permalink to this headline">¶</a></h3>
<p>This was caused by a <a class="reference external" href="https://bugs.php.net/bug.php?id=21079">PHP bug</a> that occur when
GZIP output buffering is enabled. If you turn off it (by
<span class="target" id="index-3"></span><a class="reference internal" href="config.html#cfg_OBGzip"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['OBGzip']</span></code></a> in <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>), it should work.
This bug will has been fixed in PHP 5.0.0.</p>
</div>
<div class="section" id="my-mysql-server-sometimes-refuses-queries-and-returns-the-message-errorcode-13-what-does-this-mean">
<span id="faq1-28"></span><h3>1.28 My MySQL server sometimes refuses queries and returns the message ‘Errorcode: 13’. What does this mean?<a class="headerlink" href="#my-mysql-server-sometimes-refuses-queries-and-returns-the-message-errorcode-13-what-does-this-mean" title="Permalink to this headline">¶</a></h3>
<p>This can happen due to a MySQL bug when having database / table names
with upper case characters although <code class="docutils literal notranslate"><span class="pre">lower_case_table_names</span></code> is
set to 1. To fix this, turn off this directive, convert all database
and table names to lower case and turn it on again. Alternatively,
there’s a bug-fix available starting with MySQL 3.23.56 /
4.0.11-gamma.</p>
</div>
<div class="section" id="when-i-create-a-table-or-modify-a-column-i-get-an-error-and-the-columns-are-duplicated">
<span id="faq1-29"></span><h3>1.29 When I create a table or modify a column, I get an error and the columns are duplicated.<a class="headerlink" href="#when-i-create-a-table-or-modify-a-column-i-get-an-error-and-the-columns-are-duplicated" title="Permalink to this headline">¶</a></h3>
<p>It is possible to configure Apache in such a way that PHP has problems
interpreting .php files.</p>
<p>The problems occur when two different (and conflicting) set of
directives are used:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">SetOutputFilter</span> PHP
<span class="nb">SetInputFilter</span> PHP
</pre></div>
</div>
<p>and</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">AddType</span> application/x-httpd-php .php
</pre></div>
</div>
<p>In the case we saw, one set of directives was in
<code class="docutils literal notranslate"><span class="pre">/etc/httpd/conf/httpd.conf</span></code>, while the other set was in
<code class="docutils literal notranslate"><span class="pre">/etc/httpd/conf/addon-modules/php.conf</span></code>. The recommended way is
with <code class="docutils literal notranslate"><span class="pre">AddType</span></code>, so just comment out the first set of lines and
restart Apache:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="c">#SetOutputFilter PHP</span>
<span class="c">#SetInputFilter PHP</span>
</pre></div>
</div>
</div>
<div class="section" id="i-get-the-error-navigation-php-missing-hash">
<span id="faq1-30"></span><h3>1.30 I get the error “navigation.php: Missing hash”.<a class="headerlink" href="#i-get-the-error-navigation-php-missing-hash" title="Permalink to this headline">¶</a></h3>
<p>This problem is known to happen when the server is running Turck
MMCache but upgrading MMCache to version 2.3.21 solves the problem.</p>
</div>
<div class="section" id="which-php-versions-does-phpmyadmin-support">
<span id="faq1-31"></span><h3>1.31 Which PHP versions does phpMyAdmin support?<a class="headerlink" href="#which-php-versions-does-phpmyadmin-support" title="Permalink to this headline">¶</a></h3>
<p>Since release 4.5, phpMyAdmin supports only PHP 5.5 and newer. Since release
4.1 phpMyAdmin supports only PHP 5.3 and newer. For PHP 5.2 you can use 4.0.x
releases.</p>
<p>PHP 7 is supported since phpMyAdmin 4.6, PHP 7.1 is supported since 4.6.5,
PHP 7.2 is supported since 4.7.4.</p>
<p>HHVM is supported up to phpMyAdmin 4.8.</p>
<p>Since release 5.0, phpMyAdmin supports only PHP 7.1 and newer.
Since release 5.2, phpMyAdmin supports only PHP 7.2 and newer.</p>
</div>
<div class="section" id="can-i-use-http-authentication-with-iis">
<span id="faq1-32"></span><h3>1.32 Can I use HTTP authentication with IIS?<a class="headerlink" href="#can-i-use-http-authentication-with-iis" title="Permalink to this headline">¶</a></h3>
<p>Yes. This procedure was tested with phpMyAdmin 2.6.1, PHP 4.3.9 in
<a class="reference internal" href="glossary.html#term-ISAPI"><span class="xref std std-term">ISAPI</span></a> mode under <a class="reference internal" href="glossary.html#term-IIS"><span class="xref std std-term">IIS</span></a> 5.1.</p>
<ol class="arabic simple">
<li><p>In your <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> file, set <code class="docutils literal notranslate"><span class="pre">cgi.rfc2616_headers</span> <span class="pre">=</span> <span class="pre">0</span></code></p></li>
<li><p>In <code class="docutils literal notranslate"><span class="pre">Web</span> <span class="pre">Site</span> <span class="pre">Properties</span> <span class="pre">-&gt;</span> <span class="pre">File/Directory</span> <span class="pre">Security</span> <span class="pre">-&gt;</span> <span class="pre">Anonymous</span>
<span class="pre">Access</span></code> dialog box, check the <code class="docutils literal notranslate"><span class="pre">Anonymous</span> <span class="pre">access</span></code> checkbox and
uncheck any other checkboxes (i.e. uncheck <code class="docutils literal notranslate"><span class="pre">Basic</span> <span class="pre">authentication</span></code>,
<code class="docutils literal notranslate"><span class="pre">Integrated</span> <span class="pre">Windows</span> <span class="pre">authentication</span></code>, and <code class="docutils literal notranslate"><span class="pre">Digest</span></code> if it’s
enabled.) Click <code class="docutils literal notranslate"><span class="pre">OK</span></code>.</p></li>
<li><p>In <code class="docutils literal notranslate"><span class="pre">Custom</span> <span class="pre">Errors</span></code>, select the range of <code class="docutils literal notranslate"><span class="pre">401;1</span></code> through <code class="docutils literal notranslate"><span class="pre">401;5</span></code>
and click the <code class="docutils literal notranslate"><span class="pre">Set</span> <span class="pre">to</span> <span class="pre">Default</span></code> button.</p></li>
</ol>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-4"></span><a class="rfc reference external" href="https://tools.ietf.org/html/rfc2616.html"><strong>RFC 2616</strong></a></p>
</div>
</div>
<div class="section" id="faq1-33">
<span id="id6"></span><h3>1.33 (withdrawn).<a class="headerlink" href="#faq1-33" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="can-i-directly-access-a-database-or-table-pages">
<span id="faq1-34"></span><h3>1.34 Can I directly access a database or table pages?<a class="headerlink" href="#can-i-directly-access-a-database-or-table-pages" title="Permalink to this headline">¶</a></h3>
<p>Yes. Out of the box, you can use a <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> like
<code class="docutils literal notranslate"><span class="pre">http://server/phpMyAdmin/index.php?server=X&amp;db=database&amp;table=table&amp;target=script</span></code>.
For <code class="docutils literal notranslate"><span class="pre">server</span></code> you can use the server number
which refers to the numeric host index (from <code class="docutils literal notranslate"><span class="pre">$i</span></code>) in
<code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>. The table and script parts are optional.</p>
<p>If you want a URL like
<code class="docutils literal notranslate"><span class="pre">http://server/phpMyAdmin/database[/table][/script]</span></code>, you need to do some additional configuration. The following
lines apply only for the <a class="reference external" href="https://httpd.apache.org">Apache</a> web server.
First, make sure that you have enabled some features within the Apache global
configuration. You need <code class="docutils literal notranslate"><span class="pre">Options</span> <span class="pre">SymLinksIfOwnerMatch</span></code> and <code class="docutils literal notranslate"><span class="pre">AllowOverride</span>
<span class="pre">FileInfo</span></code> enabled for directory where phpMyAdmin is installed and you
need mod_rewrite to be enabled. Then you just need to create the
following <a class="reference internal" href="glossary.html#term-.htaccess"><span class="xref std std-term">.htaccess</span></a> file in root folder of phpMyAdmin installation (don’t
forget to change directory name inside of it):</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">RewriteEngine</span> <span class="k">On</span>
<span class="nb">RewriteBase</span> <span class="sx">/path_to_phpMyAdmin</span>
<span class="nb">RewriteRule</span> ^([a-zA-Z0-9_]+)/([a-zA-Z0-9_]+)/([a-z_]+\.php)$ index.php?db=$1&amp;table=$2&amp;target=$3 [R]
<span class="nb">RewriteRule</span> ^([a-zA-Z0-9_]+)/([a-z_]+\.php)$ index.php?db=$1&amp;target=$2 [R]
<span class="nb">RewriteRule</span> ^([a-zA-Z0-9_]+)/([a-zA-Z0-9_]+)$ index.php?db=$1&amp;table=$2 [R]
<span class="nb">RewriteRule</span> ^([a-zA-Z0-9_]+)$ index.php?db=$1 [R]
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#faq4-8"><span class="std std-ref">4.8 Which parameters can I use in the URL that starts phpMyAdmin?</span></a></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 5.1.0: </span>Support for using the <code class="docutils literal notranslate"><span class="pre">target</span></code> parameter was removed in phpMyAdmin 5.1.0.
Use the <code class="docutils literal notranslate"><span class="pre">route</span></code> parameter instead.</p>
</div>
</div>
<div class="section" id="can-i-use-http-authentication-with-apache-cgi">
<span id="faq1-35"></span><h3>1.35 Can I use HTTP authentication with Apache CGI?<a class="headerlink" href="#can-i-use-http-authentication-with-apache-cgi" title="Permalink to this headline">¶</a></h3>
<p>Yes. However you need to pass authentication variable to <a class="reference internal" href="glossary.html#term-CGI"><span class="xref std std-term">CGI</span></a> using
following rewrite rule:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">RewriteEngine</span> <span class="k">On</span>
<span class="nb">RewriteRule</span> .* - [E=REMOTE_USER:%{HTTP:Authorization},L]
</pre></div>
</div>
</div>
<div class="section" id="i-get-an-error-500-internal-server-error">
<span id="faq1-36"></span><h3>1.36 I get an error “500 Internal Server Error”.<a class="headerlink" href="#i-get-an-error-500-internal-server-error" title="Permalink to this headline">¶</a></h3>
<p>There can be many explanations to this and a look at your server’s
error log file might give a clue.</p>
</div>
<div class="section" id="i-run-phpmyadmin-on-cluster-of-different-machines-and-password-encryption-in-cookie-auth-doesn-t-work">
<span id="faq1-37"></span><h3>1.37 I run phpMyAdmin on cluster of different machines and password encryption in cookie auth doesn’t work.<a class="headerlink" href="#i-run-phpmyadmin-on-cluster-of-different-machines-and-password-encryption-in-cookie-auth-doesn-t-work" title="Permalink to this headline">¶</a></h3>
<p>If your cluster consist of different architectures, PHP code used for
encryption/decryption won’t work correctly. This is caused by use of
pack/unpack functions in code. Only solution is to use openssl
extension which works fine in this case.</p>
</div>
<div class="section" id="can-i-use-phpmyadmin-on-a-server-on-which-suhosin-is-enabled">
<span id="faq1-38"></span><h3>1.38 Can I use phpMyAdmin on a server on which Suhosin is enabled?<a class="headerlink" href="#can-i-use-phpmyadmin-on-a-server-on-which-suhosin-is-enabled" title="Permalink to this headline">¶</a></h3>
<p>Yes but the default configuration values of Suhosin are known to cause
problems with some operations, for example editing a table with many
columns and no <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a> or with textual <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a>.</p>
<p>Suhosin configuration might lead to malfunction in some cases and it
can not be fully avoided as phpMyAdmin is kind of application which
needs to transfer big amounts of columns in single HTTP request, what
is something what Suhosin tries to prevent. Generally all
<code class="docutils literal notranslate"><span class="pre">suhosin.request.*</span></code>, <code class="docutils literal notranslate"><span class="pre">suhosin.post.*</span></code> and <code class="docutils literal notranslate"><span class="pre">suhosin.get.*</span></code>
directives can have negative effect on phpMyAdmin usability. You can
always find in your error logs which limit did cause dropping of
variable, so you can diagnose the problem and adjust matching
configuration variable.</p>
<p>The default values for most Suhosin configuration options will work in
most scenarios, however you might want to adjust at least following
parameters:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-request-max-vars">suhosin.request.max_vars</a> should
be increased (eg. 2048)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-post-max-vars">suhosin.post.max_vars</a> should be
increased (eg. 2048)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-request-max-array-index-length">suhosin.request.max_array_index_length</a>
should be increased (eg. 256)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-post-max-array-index-length">suhosin.post.max_array_index_length</a>
should be increased (eg. 256)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-request-max-totalname-length">suhosin.request.max_totalname_length</a>
should be increased (eg. 8192)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-post-max-totalname-length">suhosin.post.max_totalname_length</a> should be
increased (eg. 8192)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-get-max-value-length">suhosin.get.max_value_length</a>
should be increased (eg. 1024)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-sql-bailout-on-error">suhosin.sql.bailout_on_error</a>
needs to be disabled (the default)</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#logging-configuration">suhosin.log.*</a> should not
include <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a>, otherwise you get big
slowdown</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-sql-union">suhosin.sql.union</a> must be disabled (which is the default).</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-sql-multiselect">suhosin.sql.multiselect</a> must be disabled (which is the default).</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-sql-comment">suhosin.sql.comment</a> must be disabled (which is the default).</p></li>
</ul>
<p>To further improve security, we also recommend these modifications:</p>
<ul class="simple">
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-executor-include-max-traversal">suhosin.executor.include.max_traversal</a> should be
enabled as a mitigation against local file inclusion attacks. We suggest
setting this to 2 as <code class="docutils literal notranslate"><span class="pre">../</span></code> is used with the ReCaptcha library.</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-cookie-encrypt">suhosin.cookie.encrypt</a> should be enabled.</p></li>
<li><p><a class="reference external" href="https://suhosin.org/stories/configuration.html#suhosin-executor-disable-emodifier">suhosin.executor.disable_emodifier</a> should be enabled.</p></li>
</ul>
<p>You can also disable the warning using the <span class="target" id="index-5"></span><a class="reference internal" href="config.html#cfg_SuhosinDisableWarning"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['SuhosinDisableWarning']</span></code></a>.</p>
</div>
<div class="section" id="when-i-try-to-connect-via-https-i-can-log-in-but-then-my-connection-is-redirected-back-to-http-what-can-cause-this-behavior">
<span id="faq1-39"></span><h3>1.39 When I try to connect via https, I can log in, but then my connection is redirected back to http. What can cause this behavior?<a class="headerlink" href="#when-i-try-to-connect-via-https-i-can-log-in-but-then-my-connection-is-redirected-back-to-http-what-can-cause-this-behavior" title="Permalink to this headline">¶</a></h3>
<p>This is caused by the fact that PHP scripts have no knowledge that the site is
using https. Depending on used webserver, you should configure it to let PHP
know about URL and scheme used to access it.</p>
<p>For example in Apache ensure that you have enabled <code class="docutils literal notranslate"><span class="pre">SSLOptions</span></code> and
<code class="docutils literal notranslate"><span class="pre">StdEnvVars</span></code> in the configuration.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://httpd.apache.org/docs/2.4/mod/mod_ssl.html">https://httpd.apache.org/docs/2.4/mod/mod_ssl.html</a>&gt;</p>
</div>
</div>
<div class="section" id="when-accessing-phpmyadmin-via-an-apache-reverse-proxy-cookie-login-does-not-work">
<span id="faq1-40"></span><h3>1.40 When accessing phpMyAdmin via an Apache reverse proxy, cookie login does not work.<a class="headerlink" href="#when-accessing-phpmyadmin-via-an-apache-reverse-proxy-cookie-login-does-not-work" title="Permalink to this headline">¶</a></h3>
<p>To be able to use cookie auth Apache must know that it has to rewrite
the set-cookie headers. Example from the Apache 2.2 documentation:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">ProxyPass</span> <span class="sx">/mirror/foo/</span> http://backend.example.com/
<span class="nb">ProxyPassReverse</span> <span class="sx">/mirror/foo/</span> http://backend.example.com/
<span class="nb">ProxyPassReverseCookieDomain</span> backend.example.com public.example.com
<span class="nb">ProxyPassReverseCookiePath</span> / <span class="sx">/mirror/foo/</span>
</pre></div>
</div>
<p>Note: if the backend url looks like <code class="docutils literal notranslate"><span class="pre">http://server/~user/phpmyadmin</span></code>, the
tilde (~) must be url encoded as %7E in the ProxyPassReverse* lines.
This is not specific to phpmyadmin, it’s just the behavior of Apache.</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">ProxyPass</span> <span class="sx">/mirror/foo/</span> http://backend.example.com/~user/phpmyadmin
<span class="nb">ProxyPassReverse</span> <span class="sx">/mirror/foo/</span> http://backend.example.com/%7Euser/phpmyadmin
<span class="nb">ProxyPassReverseCookiePath</span> /%7Euser/phpmyadmin <span class="sx">/mirror/foo</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://httpd.apache.org/docs/2.2/mod/mod_proxy.html">https://httpd.apache.org/docs/2.2/mod/mod_proxy.html</a>&gt;, <span class="target" id="index-6"></span><a class="reference internal" href="config.html#cfg_PmaAbsoluteUri"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PmaAbsoluteUri']</span></code></a></p>
</div>
</div>
<div class="section" id="when-i-view-a-database-and-ask-to-see-its-privileges-i-get-an-error-about-an-unknown-column">
<span id="faq1-41"></span><h3>1.41 When I view a database and ask to see its privileges, I get an error about an unknown column.<a class="headerlink" href="#when-i-view-a-database-and-ask-to-see-its-privileges-i-get-an-error-about-an-unknown-column" title="Permalink to this headline">¶</a></h3>
<p>The MySQL server’s privilege tables are not up to date, you need to
run the <strong class="command">mysql_upgrade</strong> command on the server.</p>
</div>
<div class="section" id="how-can-i-prevent-robots-from-accessing-phpmyadmin">
<span id="faq1-42"></span><h3>1.42 How can I prevent robots from accessing phpMyAdmin?<a class="headerlink" href="#how-can-i-prevent-robots-from-accessing-phpmyadmin" title="Permalink to this headline">¶</a></h3>
<p>You can add various rules to <a class="reference internal" href="glossary.html#term-.htaccess"><span class="xref std std-term">.htaccess</span></a> to filter access based on user agent
field. This is quite easy to circumvent, but could prevent at least
some robots accessing your installation.</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">RewriteEngine</span> <span class="k">on</span>

<span class="c"># Allow only GET and POST verbs</span>
<span class="nb">RewriteCond</span> %{REQUEST_METHOD} !^(GET|POST)$ [NC,OR]

<span class="c"># Ban Typical Vulnerability Scanners and others</span>
<span class="c"># Kick out Script Kiddies</span>
<span class="nb">RewriteCond</span> %{HTTP_USER_AGENT} ^(java|curl|wget).* [NC,OR]
<span class="nb">RewriteCond</span> %{HTTP_USER_AGENT} ^.*(libwww-perl|curl|wget|python|nikto|wkito|pikto|scan|acunetix).* [NC,OR]
<span class="nb">RewriteCond</span> %{HTTP_USER_AGENT} ^.*(winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner).* [NC,OR]

<span class="c"># Ban Search Engines, Crawlers to your administrative panel</span>
<span class="c"># No reasons to access from bots</span>
<span class="c"># Ultimately Better than the useless robots.txt</span>
<span class="c"># Did google respect robots.txt?</span>
<span class="c"># Try google: intitle:phpMyAdmin intext:&quot;Welcome to phpMyAdmin *.*.*&quot; intext:&quot;Log in&quot; -wiki -forum -forums -questions intext:&quot;Cookies must be enabled&quot;</span>
<span class="nb">RewriteCond</span> %{HTTP_USER_AGENT} ^.*(AdsBot-Google|ia_archiver|Scooter|Ask.Jeeves|Baiduspider|Exabot|FAST.Enterprise.Crawler|FAST-WebCrawler|www\.neomo\.de|Gigabot|Mediapartners-Google|Google.Desktop|Feedfetcher-Google|Googlebot|heise-IT-Markt-Crawler|heritrix|ibm.com\cs/crawler|ICCrawler|ichiro|MJ12bot|MetagerBot|msnbot-NewsBlogs|msnbot|msnbot-media|NG-Search|lucene.apache.org|NutchCVS|OmniExplorer_Bot|online.link.validator|psbot0|Seekbot|Sensis.Web.Crawler|SEO.search.Crawler|Seoma.\[SEO.Crawler\]|SEOsearch|Snappy|www.urltrends.com|www.tkl.iis.u-tokyo.ac.jp/~crawler|SynooBot|<EMAIL>|TurnitinBot|voyager|W3.SiteSearch.Crawler|W3C-checklink|W3C_Validator|www.WISEnutbot.com|yacybot|Yahoo-MMCrawler|Yahoo\!.DE.Slurp|Yahoo\!.Slurp|YahooSeeker).* [NC]
<span class="nb">RewriteRule</span> .* - [F]
</pre></div>
</div>
</div>
<div class="section" id="why-can-t-i-display-the-structure-of-my-table-containing-hundreds-of-columns">
<span id="faq1-43"></span><h3>1.43 Why can’t I display the structure of my table containing hundreds of columns?<a class="headerlink" href="#why-can-t-i-display-the-structure-of-my-table-containing-hundreds-of-columns" title="Permalink to this headline">¶</a></h3>
<p>Because your PHP’s <code class="docutils literal notranslate"><span class="pre">memory_limit</span></code> is too low; adjust it in <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>.</p>
</div>
<div class="section" id="how-can-i-reduce-the-installed-size-of-phpmyadmin-on-disk">
<span id="faq1-44"></span><h3>1.44 How can I reduce the installed size of phpMyAdmin on disk?<a class="headerlink" href="#how-can-i-reduce-the-installed-size-of-phpmyadmin-on-disk" title="Permalink to this headline">¶</a></h3>
<p>Some users have requested to be able to reduce the size of the phpMyAdmin installation.
This is not recommended and could lead to confusion over missing features, but can be done.
A list of files and corresponding functionality which degrade gracefully when removed include:</p>
<ul class="simple">
<li><p><code class="file docutils literal notranslate"><span class="pre">./locale/</span></code> folder, or unused subfolders (interface translations)</p></li>
<li><p>Any unused themes in <code class="file docutils literal notranslate"><span class="pre">./themes/</span></code> except the default theme <cite>pmahomme</cite>.</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./libraries/language_stats.inc.php</span></code> (translation statistics)</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./doc/</span></code> (documentation)</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./setup/</span></code> (setup script)</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./examples/</span></code> (configuration examples)</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./sql/</span></code> (SQL scripts to configure advanced functionalities)</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./js/src/</span></code> (Source files to re-build <cite>./js/dist/</cite>)</p></li>
<li><p><code class="file docutils literal notranslate"><span class="pre">./js/config/</span></code> (Configuration files to re-build <cite>./js/dist/</cite>)</p></li>
<li><p>Run <cite>rm -rv vendor/tecnickcom/tcpdf &amp;&amp; composer dump-autoload –no-interaction –optimize –dev</cite> (exporting to PDF)</p></li>
<li><p>Run <cite>rm -rv vendor/williamdes/mariadb-mysql-kbs &amp;&amp; composer dump-autoload –no-interaction –optimize –dev</cite> (external links to MariaDB and MySQL documentations)</p></li>
<li><p>Run <cite>rm -rv vendor/code-lts/u2f-php-server &amp;&amp; composer dump-autoload –no-interaction –optimize –dev</cite> (U2F second factor authentication)</p></li>
<li><p>Run <cite>rm -rv vendor/pragmarx/* &amp;&amp; composer dump-autoload –no-interaction –optimize –dev</cite> (2FA second factor authentication)</p></li>
<li><p>Run <cite>rm -rv vendor/bacon/bacon-qr-code &amp;&amp; composer dump-autoload –no-interaction –optimize –dev</cite> (QRcode generation for 2FA second factor authentication)</p></li>
</ul>
</div>
<div class="section" id="i-get-an-error-message-about-unknown-authentication-method-caching-sha2-password-when-trying-to-log-in">
<span id="faq1-45"></span><h3>1.45 I get an error message about unknown authentication method caching_sha2_password when trying to log in<a class="headerlink" href="#i-get-an-error-message-about-unknown-authentication-method-caching-sha2-password-when-trying-to-log-in" title="Permalink to this headline">¶</a></h3>
<p>When logging in using MySQL version 8 or newer, you may encounter an error message like this:</p>
<blockquote>
<div><p>mysqli_real_connect(): The server requested authentication method unknown to the client [caching_sha2_password]</p>
<p>mysqli_real_connect(): (HY000/2054): The server requested authentication method unknown to the client</p>
</div></blockquote>
<p>This error is because of a version compatibility problem between PHP and MySQL. The MySQL project introduced a new authentication
method (our tests show this began with version 8.0.11) however PHP did not include the ability to use that authentication method.
PHP reports that this was fixed in PHP version 7.4.</p>
<p>Users experiencing this are encouraged to upgrade their PHP installation, however a workaround exists. Your MySQL user account
can be set to use the older authentication with a command such as</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">ALTER</span> <span class="k">USER</span> <span class="s1">&#39;root&#39;</span><span class="nv">@&#39;localhost&#39;</span> <span class="k">IDENTIFIED</span> <span class="k">WITH</span> <span class="n">mysql_native_password</span> <span class="k">BY</span> <span class="s1">&#39;PASSWORD&#39;</span><span class="p">;</span>
</pre></div>
</div>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://github.com/phpmyadmin/phpmyadmin/issues/14220">https://github.com/phpmyadmin/phpmyadmin/issues/14220</a>&gt;, &lt;<a class="reference external" href="https://stackoverflow.com/questions/********/phpmyadmin-on-mysql-8-0">https://stackoverflow.com/questions/********/phpmyadmin-on-mysql-8-0</a>&gt;, &lt;<a class="reference external" href="https://bugs.php.net/bug.php?id=76243">https://bugs.php.net/bug.php?id=76243</a>&gt;</p>
</div>
</div>
</div>
<div class="section" id="configuration">
<span id="faqconfig"></span><h2>Configuration<a class="headerlink" href="#configuration" title="Permalink to this headline">¶</a></h2>
<div class="section" id="the-error-message-warning-cannot-add-header-information-headers-already-sent-by-is-displayed-what-s-the-problem">
<span id="faq2-1"></span><h3>2.1 The error message “Warning: Cannot add header information - headers already sent by …” is displayed, what’s the problem?<a class="headerlink" href="#the-error-message-warning-cannot-add-header-information-headers-already-sent-by-is-displayed-what-s-the-problem" title="Permalink to this headline">¶</a></h3>
<p>Edit your <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> file and ensure there is nothing (I.E. no
blank lines, no spaces, no characters…) neither before the <code class="docutils literal notranslate"><span class="pre">&lt;?php</span></code> tag at
the beginning, neither after the <code class="docutils literal notranslate"><span class="pre">?&gt;</span></code> tag at the end.</p>
</div>
<div class="section" id="phpmyadmin-can-t-connect-to-mysql-what-s-wrong">
<span id="faq2-2"></span><h3>2.2 phpMyAdmin can’t connect to MySQL. What’s wrong?<a class="headerlink" href="#phpmyadmin-can-t-connect-to-mysql-what-s-wrong" title="Permalink to this headline">¶</a></h3>
<p>Either there is an error with your PHP setup or your username/password
is wrong. Try to make a small script which uses mysql_connect and see
if it works. If it doesn’t, it may be you haven’t even compiled MySQL
support into PHP.</p>
</div>
<div class="section" id="the-error-message-warning-mysql-connection-failed-can-t-connect-to-local-mysql-server-through-socket-tmp-mysql-sock-111-is-displayed-what-can-i-do">
<span id="faq2-3"></span><h3>2.3 The error message “Warning: MySQL Connection Failed: Can’t connect to local MySQL server through socket ‘/tmp/mysql.sock’ (111) …” is displayed. What can I do?<a class="headerlink" href="#the-error-message-warning-mysql-connection-failed-can-t-connect-to-local-mysql-server-through-socket-tmp-mysql-sock-111-is-displayed-what-can-i-do" title="Permalink to this headline">¶</a></h3>
<p>The error message can also be: <span class="guilabel">Error #2002 - The server is not
responding (or the local MySQL server’s socket is not correctly configured)</span>.</p>
<p>First, you need to determine what socket is being used by MySQL. To do this,
connect to your server and go to the MySQL bin directory. In this directory
there should be a file named <em>mysqladmin</em>. Type <code class="docutils literal notranslate"><span class="pre">./mysqladmin</span> <span class="pre">variables</span></code>, and
this should give you a bunch of info about your MySQL server, including the
socket (<em>/tmp/mysql.sock</em>, for example). You can also ask your ISP for the
connection info or, if you’re hosting your own, connect from the ‘mysql’
command-line client and type ‘status’ to get the connection type and socket or
port number.</p>
<p>Then, you need to tell PHP to use this socket. You can do this for all PHP in
the <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> or for phpMyAdmin only in the <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>. For
example: <span class="target" id="index-7"></span><a class="reference internal" href="config.html#cfg_Servers_socket"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['socket']</span></code></a>  Please also make sure
that the permissions of this file allow to be readable by your webserver.</p>
<p>On my RedHat-Box the socket of MySQL is <em>/var/lib/mysql/mysql.sock</em>.
In your <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> you will find a line</p>
<div class="highlight-ini notranslate"><div class="highlight"><pre><span></span><span class="na">mysql.default_socket</span> <span class="o">=</span> <span class="s">/tmp/mysql.sock</span>
</pre></div>
</div>
<p>change it to</p>
<div class="highlight-ini notranslate"><div class="highlight"><pre><span></span><span class="na">mysql.default_socket</span> <span class="o">=</span> <span class="s">/var/lib/mysql/mysql.sock</span>
</pre></div>
</div>
<p>Then restart apache and it will work.</p>
<p>Have also a look at the <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/can-not-connect-to-server.html">corresponding section of the MySQL
documentation</a>.</p>
</div>
<div class="section" id="nothing-is-displayed-by-my-browser-when-i-try-to-run-phpmyadmin-what-can-i-do">
<span id="faq2-4"></span><h3>2.4 Nothing is displayed by my browser when I try to run phpMyAdmin, what can I do?<a class="headerlink" href="#nothing-is-displayed-by-my-browser-when-i-try-to-run-phpmyadmin-what-can-i-do" title="Permalink to this headline">¶</a></h3>
<p>Try to set the <span class="target" id="index-8"></span><a class="reference internal" href="config.html#cfg_OBGzip"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['OBGzip']</span></code></a> directive to <code class="docutils literal notranslate"><span class="pre">false</span></code> in the phpMyAdmin configuration
file. It helps sometime. Also have a look at your PHP version number:
if it contains “b” or “alpha” it means you’re running a testing
version of PHP. That’s not a so good idea, please upgrade to a plain
revision.</p>
</div>
<div class="section" id="each-time-i-want-to-insert-or-change-a-row-or-drop-a-database-or-a-table-an-error-404-page-not-found-is-displayed-or-with-http-or-cookie-authentication-i-m-asked-to-log-in-again-what-s-wrong">
<span id="faq2-5"></span><h3>2.5 Each time I want to insert or change a row or drop a database or a table, an error 404 (page not found) is displayed or, with HTTP or cookie authentication, I’m asked to log in again. What’s wrong?<a class="headerlink" href="#each-time-i-want-to-insert-or-change-a-row-or-drop-a-database-or-a-table-an-error-404-page-not-found-is-displayed-or-with-http-or-cookie-authentication-i-m-asked-to-log-in-again-what-s-wrong" title="Permalink to this headline">¶</a></h3>
<p>Check your webserver setup if it correctly fills in either PHP_SELF or REQUEST_URI variables.</p>
<p>If you are running phpMyAdmin behind reverse proxy, please set the
<span class="target" id="index-9"></span><a class="reference internal" href="config.html#cfg_PmaAbsoluteUri"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PmaAbsoluteUri']</span></code></a> directive in the phpMyAdmin
configuration file to match your setup.</p>
</div>
<div class="section" id="i-get-an-access-denied-for-user-root-localhost-using-password-yes-error-when-trying-to-access-a-mysql-server-on-a-host-which-is-port-forwarded-for-my-localhost">
<span id="faq2-6"></span><h3>2.6 I get an “Access denied for user: <a class="reference external" href="mailto:'root&#37;&#52;&#48;localhost">‘root<span>&#64;</span>localhost</a>’ (Using password: YES)”-error when trying to access a MySQL-Server on a host which is port-forwarded for my localhost.<a class="headerlink" href="#i-get-an-access-denied-for-user-root-localhost-using-password-yes-error-when-trying-to-access-a-mysql-server-on-a-host-which-is-port-forwarded-for-my-localhost" title="Permalink to this headline">¶</a></h3>
<p>When you are using a port on your localhost, which you redirect via
port-forwarding to another host, MySQL is not resolving the localhost
as expected. Erik Wasser explains: The solution is: if your host is
“localhost” MySQL (the command line tool <strong class="command">mysql</strong> as well) always
tries to use the socket connection for speeding up things. And that
doesn’t work in this configuration with port forwarding. If you enter
“127.0.0.1” as hostname, everything is right and MySQL uses the
<a class="reference internal" href="glossary.html#term-TCP"><span class="xref std std-term">TCP</span></a> connection.</p>
</div>
<div class="section" id="using-and-creating-themes">
<span id="faqthemes"></span><h3>2.7 Using and creating themes<a class="headerlink" href="#using-and-creating-themes" title="Permalink to this headline">¶</a></h3>
<p>See <a class="reference internal" href="themes.html#themes"><span class="std std-ref">Custom Themes</span></a>.</p>
</div>
<div class="section" id="i-get-missing-parameters-errors-what-can-i-do">
<span id="faqmissingparameters"></span><h3>2.8 I get “Missing parameters” errors, what can I do?<a class="headerlink" href="#i-get-missing-parameters-errors-what-can-i-do" title="Permalink to this headline">¶</a></h3>
<p>Here are a few points to check:</p>
<ul class="simple">
<li><p>In <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>, try to leave the <span class="target" id="index-10"></span><a class="reference internal" href="config.html#cfg_PmaAbsoluteUri"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PmaAbsoluteUri']</span></code></a> directive empty. See also
<a class="reference internal" href="#faq4-7"><span class="std std-ref">4.7 Authentication window is displayed more than once, why?</span></a>.</p></li>
<li><p>Maybe you have a broken PHP installation or you need to upgrade your
Zend Optimizer. See &lt;<a class="reference external" href="https://bugs.php.net/bug.php?id=31134">https://bugs.php.net/bug.php?id=31134</a>&gt;.</p></li>
<li><p>If you are using Hardened PHP with the ini directive
<code class="docutils literal notranslate"><span class="pre">varfilter.max_request_variables</span></code> set to the default (200) or
another low value, you could get this error if your table has a high
number of columns. Adjust this setting accordingly. (Thanks to Klaus
Dorninger for the hint).</p></li>
<li><p>In the <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> directive <code class="docutils literal notranslate"><span class="pre">arg_separator.input</span></code>, a value of “;”
will cause this error. Replace it with “&amp;;”.</p></li>
<li><p>If you are using <a class="reference external" href="https://suhosin.org/stories/index.html">Suhosin</a>, you
might want to increase <a class="reference external" href="https://suhosin.org/stories/faq.html">request limits</a>.</p></li>
<li><p>The directory specified in the <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code> directive
<code class="docutils literal notranslate"><span class="pre">session.save_path</span></code> does not exist or is read-only (this can be caused
by <a class="reference external" href="https://bugs.php.net/bug.php?id=39842">bug in the PHP installer</a>).</p></li>
</ul>
</div>
<div class="section" id="seeing-an-upload-progress-bar">
<span id="faq2-9"></span><h3>2.9 Seeing an upload progress bar<a class="headerlink" href="#seeing-an-upload-progress-bar" title="Permalink to this headline">¶</a></h3>
<p>To be able to see a progress bar during your uploads, your server must
have the <a class="reference external" href="https://pecl.php.net/package/uploadprogress">uploadprogress</a> extension, and
you must be running PHP 5.4.0 or higher. Moreover, the JSON extension
has to be enabled in your PHP.</p>
<p>If using PHP 5.4.0 or higher, you must set
<code class="docutils literal notranslate"><span class="pre">session.upload_progress.enabled</span></code> to <code class="docutils literal notranslate"><span class="pre">1</span></code> in your <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>. However,
starting from phpMyAdmin version 4.0.4, session-based upload progress has
been temporarily deactivated due to its problematic behavior.</p>
</div>
<div class="section" id="how-to-generate-a-string-of-random-bytes">
<span id="faq2-10"></span><h3>2.10 How to generate a string of random bytes<a class="headerlink" href="#how-to-generate-a-string-of-random-bytes" title="Permalink to this headline">¶</a></h3>
<p>One way to generate a string of random bytes suitable for cryptographic use is using the
<a class="reference external" href="https://www.php.net/random_bytes">random_bytes</a> <a class="reference internal" href="glossary.html#term-PHP"><span class="xref std std-term">PHP</span></a> function. Since this function returns a binary string,
the returned value should be converted to printable format before being able to copy it.</p>
<p>For example, the <span class="target" id="index-11"></span><a class="reference internal" href="config.html#cfg_blowfish_secret"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['blowfish_secret']</span></code></a> configuration directive requires a 32-bytes long string. The
following command can be used to generate a hexadecimal representation of this string.</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span>php -r <span class="s1">&#39;echo bin2hex(random_bytes(32)) . PHP_EOL;&#39;</span>
</pre></div>
</div>
<p>The above example will output something similar to:</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span>f16ce59f45714194371b48fe362072dc3b019da7861558cd4ad29e4d6fb13851
</pre></div>
</div>
<p>And then this hexadecimal value can be used in the configuration file.</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;blowfish_secret&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="nx">sodium_hex2bin</span><span class="p">(</span><span class="s1">&#39;f16ce59f45714194371b48fe362072dc3b019da7861558cd4ad29e4d6fb13851&#39;</span><span class="p">);</span>
</pre></div>
</div>
<p>The <a class="reference external" href="https://www.php.net/sodium_hex2bin">sodium_hex2bin</a> is used here to convert the hexadecimal value back to the
binary format.</p>
</div>
</div>
<div class="section" id="known-limitations">
<span id="faqlimitations"></span><h2>Known limitations<a class="headerlink" href="#known-limitations" title="Permalink to this headline">¶</a></h2>
<div class="section" id="when-using-http-authentication-a-user-who-logged-out-can-not-log-in-again-in-with-the-same-nick">
<span id="login-bug"></span><h3>3.1 When using HTTP authentication, a user who logged out can not log in again in with the same nick.<a class="headerlink" href="#when-using-http-authentication-a-user-who-logged-out-can-not-log-in-again-in-with-the-same-nick" title="Permalink to this headline">¶</a></h3>
<p>This is related to the authentication mechanism (protocol) used by
phpMyAdmin. To bypass this problem: just close all the opened browser
windows and then go back to phpMyAdmin. You should be able to log in
again.</p>
</div>
<div class="section" id="when-dumping-a-large-table-in-compressed-mode-i-get-a-memory-limit-error-or-a-time-limit-error">
<span id="faq3-2"></span><h3>3.2 When dumping a large table in compressed mode, I get a memory limit error or a time limit error.<a class="headerlink" href="#when-dumping-a-large-table-in-compressed-mode-i-get-a-memory-limit-error-or-a-time-limit-error" title="Permalink to this headline">¶</a></h3>
<p>Compressed dumps are built in memory and because of this are limited
to php’s memory limit. For gzip/bzip2 exports this can be overcome
since 2.5.4 using <span class="target" id="index-12"></span><a class="reference internal" href="config.html#cfg_CompressOnFly"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['CompressOnFly']</span></code></a> (enabled by default).
zip exports can not be handled this way, so if you need zip files for larger
dump, you have to use another way.</p>
</div>
<div class="section" id="with-innodb-tables-i-lose-foreign-key-relationships-when-i-rename-a-table-or-a-column">
<span id="faq3-3"></span><h3>3.3 With InnoDB tables, I lose foreign key relationships when I rename a table or a column.<a class="headerlink" href="#with-innodb-tables-i-lose-foreign-key-relationships-when-i-rename-a-table-or-a-column" title="Permalink to this headline">¶</a></h3>
<p>This is an InnoDB bug, see &lt;<a class="reference external" href="https://bugs.mysql.com/bug.php?id=21704">https://bugs.mysql.com/bug.php?id=21704</a>&gt;.</p>
</div>
<div class="section" id="i-am-unable-to-import-dumps-i-created-with-the-mysqldump-tool-bundled-with-the-mysql-server-distribution">
<span id="faq3-4"></span><h3>3.4 I am unable to import dumps I created with the mysqldump tool bundled with the MySQL server distribution.<a class="headerlink" href="#i-am-unable-to-import-dumps-i-created-with-the-mysqldump-tool-bundled-with-the-mysql-server-distribution" title="Permalink to this headline">¶</a></h3>
<p>The problem is that older versions of <code class="docutils literal notranslate"><span class="pre">mysqldump</span></code> created invalid
comments like this:</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="c1">-- MySQL dump 8.22</span>
<span class="c1">--</span>
<span class="c1">-- Host: localhost Database: database</span>
<span class="o">---------------------------------------------------------</span>
<span class="c1">-- Server version 3.23.54</span>
</pre></div>
</div>
<p>The invalid part of the code is the horizontal line made of dashes
that appears once in every dump created with mysqldump. If you want to
run your dump you have to turn it into valid MySQL. This means, you
have to add a whitespace after the first two dashes of the line or add
a # before it:  <code class="docutils literal notranslate"><span class="pre">--</span> <span class="pre">-------------------------------------------------------</span></code> or
<code class="docutils literal notranslate"><span class="pre">#---------------------------------------------------------</span></code></p>
</div>
<div class="section" id="when-using-nested-folders-multiple-hierarchies-are-displayed-in-a-wrong-manner">
<span id="faq3-5"></span><h3>3.5 When using nested folders, multiple hierarchies are displayed in a wrong manner.<a class="headerlink" href="#when-using-nested-folders-multiple-hierarchies-are-displayed-in-a-wrong-manner" title="Permalink to this headline">¶</a></h3>
<p>Please note that you should not use the separating string multiple
times without any characters between them, or at the beginning/end of
your table name. If you have to, think about using another
TableSeparator or disabling that feature.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-13"></span><a class="reference internal" href="config.html#cfg_NavigationTreeTableSeparator"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NavigationTreeTableSeparator']</span></code></a></p>
</div>
</div>
<div class="section" id="faq3-6">
<span id="id7"></span><h3>3.6 (withdrawn).<a class="headerlink" href="#faq3-6" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-have-table-with-many-100-columns-and-when-i-try-to-browse-table-i-get-series-of-errors-like-warning-unable-to-parse-url-how-can-this-be-fixed">
<span id="faq3-7"></span><h3>3.7 I have table with many (100+) columns and when I try to browse table I get series of errors like “Warning: unable to parse url”. How can this be fixed?<a class="headerlink" href="#i-have-table-with-many-100-columns-and-when-i-try-to-browse-table-i-get-series-of-errors-like-warning-unable-to-parse-url-how-can-this-be-fixed" title="Permalink to this headline">¶</a></h3>
<p>Your table neither have a <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a> nor an <a class="reference internal" href="glossary.html#term-unique-key"><span class="xref std std-term">unique key</span></a>, so we must
use a long expression to identify this row. This causes problems to
parse_url function. The workaround is to create a <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a>
or <a class="reference internal" href="glossary.html#term-unique-key"><span class="xref std std-term">unique key</span></a>.</p>
</div>
<div class="section" id="i-cannot-use-clickable-html-forms-in-columns-where-i-put-a-mime-transformation-onto">
<span id="faq3-8"></span><h3>3.8 I cannot use (clickable) HTML-forms in columns where I put a MIME-Transformation onto!<a class="headerlink" href="#i-cannot-use-clickable-html-forms-in-columns-where-i-put-a-mime-transformation-onto" title="Permalink to this headline">¶</a></h3>
<p>Due to a surrounding form-container (for multi-row delete checkboxes),
no nested forms can be put inside the table where phpMyAdmin displays
the results. You can, however, use any form inside of a table if keep
the parent form-container with the target to tbl_row_delete.php and
just put your own input-elements inside. If you use a custom submit
input field, the form will submit itself to the displaying page again,
where you can validate the $HTTP_POST_VARS in a transformation. For
a tutorial on how to effectively use transformations, see our <a class="reference external" href="https://www.phpmyadmin.net/docs/">Link
section</a> on the
official phpMyAdmin-homepage.</p>
</div>
<div class="section" id="i-get-error-messages-when-using-sql-mode-ansi-for-the-mysql-server">
<span id="faq3-9"></span><h3>3.9 I get error messages when using “–sql_mode=ANSI” for the MySQL server.<a class="headerlink" href="#i-get-error-messages-when-using-sql-mode-ansi-for-the-mysql-server" title="Permalink to this headline">¶</a></h3>
<p>When MySQL is running in ANSI-compatibility mode, there are some major
differences in how <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> is structured (see
&lt;<a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/sql-mode.html">https://dev.mysql.com/doc/refman/5.7/en/sql-mode.html</a>&gt;). Most important of all, the
quote-character (“) is interpreted as an identifier quote character and not as
a string quote character, which makes many internal phpMyAdmin operations into
invalid <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> statements. There is no
workaround to this behaviour.  News to this item will be posted in <a class="reference external" href="https://github.com/phpmyadmin/phpmyadmin/issues/7383">issue
#7383</a>.</p>
</div>
<div class="section" id="homonyms-and-no-primary-key-when-the-results-of-a-select-display-more-that-one-column-with-the-same-value-for-example-select-lastname-from-employees-where-firstname-like-a-and-two-smith-values-are-displayed-if-i-click-edit-i-cannot-be-sure-that-i-am-editing-the-intended-row">
<span id="faq3-10"></span><h3>3.10 Homonyms and no primary key: When the results of a SELECT display more that one column with the same value (for example <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">lastname</span> <span class="pre">from</span> <span class="pre">employees</span> <span class="pre">where</span> <span class="pre">firstname</span> <span class="pre">like</span> <span class="pre">'A%'</span></code> and two “Smith” values are displayed), if I click Edit I cannot be sure that I am editing the intended row.<a class="headerlink" href="#homonyms-and-no-primary-key-when-the-results-of-a-select-display-more-that-one-column-with-the-same-value-for-example-select-lastname-from-employees-where-firstname-like-a-and-two-smith-values-are-displayed-if-i-click-edit-i-cannot-be-sure-that-i-am-editing-the-intended-row" title="Permalink to this headline">¶</a></h3>
<p>Please make sure that your table has a <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a>, so that phpMyAdmin
can use it for the Edit and Delete links.</p>
</div>
<div class="section" id="the-number-of-rows-for-innodb-tables-is-not-correct">
<span id="faq3-11"></span><h3>3.11 The number of rows for InnoDB tables is not correct.<a class="headerlink" href="#the-number-of-rows-for-innodb-tables-is-not-correct" title="Permalink to this headline">¶</a></h3>
<p>phpMyAdmin uses a quick method to get the row count, and this method only
returns an approximate count in the case of InnoDB tables. See
<span class="target" id="index-14"></span><a class="reference internal" href="config.html#cfg_MaxExactCount"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['MaxExactCount']</span></code></a> for a way to modify those results, but
this could have a serious impact on performance.
However, one can easily replace the approximate row count with exact count by
simply clicking on the approximate count. This can also be done for all tables
at once by clicking on the rows sum displayed at the bottom.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><span class="target" id="index-15"></span><a class="reference internal" href="config.html#cfg_MaxExactCount"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['MaxExactCount']</span></code></a></p>
</div>
</div>
<div class="section" id="faq3-12">
<span id="id9"></span><h3>3.12 (withdrawn).<a class="headerlink" href="#faq3-12" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-get-an-error-when-entering-use-followed-by-a-db-name-containing-an-hyphen">
<span id="faq3-13"></span><h3>3.13 I get an error when entering <code class="docutils literal notranslate"><span class="pre">USE</span></code> followed by a db name containing an hyphen.<a class="headerlink" href="#i-get-an-error-when-entering-use-followed-by-a-db-name-containing-an-hyphen" title="Permalink to this headline">¶</a></h3>
<p>The tests I have made with MySQL 5.1.49 shows that the API does not
accept this syntax for the USE command.</p>
</div>
<div class="section" id="i-am-not-able-to-browse-a-table-when-i-don-t-have-the-right-to-select-one-of-the-columns">
<span id="faq3-14"></span><h3>3.14 I am not able to browse a table when I don’t have the right to SELECT one of the columns.<a class="headerlink" href="#i-am-not-able-to-browse-a-table-when-i-don-t-have-the-right-to-select-one-of-the-columns" title="Permalink to this headline">¶</a></h3>
<p>This has been a known limitation of phpMyAdmin since the beginning and
it’s not likely to be solved in the future.</p>
</div>
<div class="section" id="faq3-15">
<span id="id10"></span><h3>3.15 (withdrawn).<a class="headerlink" href="#faq3-15" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq3-16">
<span id="id11"></span><h3>3.16 (withdrawn).<a class="headerlink" href="#faq3-16" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq3-17">
<span id="id12"></span><h3>3.17 (withdrawn).<a class="headerlink" href="#faq3-17" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="when-i-import-a-csv-file-that-contains-multiple-tables-they-are-lumped-together-into-a-single-table">
<span id="faq3-18"></span><h3>3.18 When I import a CSV file that contains multiple tables, they are lumped together into a single table.<a class="headerlink" href="#when-i-import-a-csv-file-that-contains-multiple-tables-they-are-lumped-together-into-a-single-table" title="Permalink to this headline">¶</a></h3>
<p>There is no reliable way to differentiate tables in <a class="reference internal" href="glossary.html#term-CSV"><span class="xref std std-term">CSV</span></a> format. For the
time being, you will have to break apart <a class="reference internal" href="glossary.html#term-CSV"><span class="xref std std-term">CSV</span></a> files containing multiple
tables.</p>
</div>
<div class="section" id="when-i-import-a-file-and-have-phpmyadmin-determine-the-appropriate-data-structure-it-only-uses-int-decimal-and-varchar-types">
<span id="faq3-19"></span><h3>3.19 When I import a file and have phpMyAdmin determine the appropriate data structure it only uses int, decimal, and varchar types.<a class="headerlink" href="#when-i-import-a-file-and-have-phpmyadmin-determine-the-appropriate-data-structure-it-only-uses-int-decimal-and-varchar-types" title="Permalink to this headline">¶</a></h3>
<p>Currently, the import type-detection system can only assign these
MySQL types to columns. In future, more will likely be added but for
the time being you will have to edit the structure to your liking
post-import.  Also, you should note the fact that phpMyAdmin will use
the size of the largest item in any given column as the column size
for the appropriate type. If you know you will be adding larger items
to that column then you should manually adjust the column sizes
accordingly. This is done for the sake of efficiency.</p>
</div>
<div class="section" id="after-upgrading-some-bookmarks-are-gone-or-their-content-cannot-be-shown">
<span id="faq3-20"></span><h3>3.20 After upgrading, some bookmarks are gone or their content cannot be shown.<a class="headerlink" href="#after-upgrading-some-bookmarks-are-gone-or-their-content-cannot-be-shown" title="Permalink to this headline">¶</a></h3>
<p>At some point, the character set used to store bookmark content has changed.
It’s better to recreate your bookmark from the newer phpMyAdmin version.</p>
</div>
<div class="section" id="i-am-unable-to-log-in-with-a-username-containing-unicode-characters-such-as-a">
<span id="faq3-21"></span><h3>3.21 I am unable to log in with a username containing unicode characters such as á.<a class="headerlink" href="#i-am-unable-to-log-in-with-a-username-containing-unicode-characters-such-as-a" title="Permalink to this headline">¶</a></h3>
<p>This can happen if MySQL server is not configured to use utf-8 as default
charset. This is a limitation of how PHP and the MySQL server interact; there
is no way for PHP to set the charset before authenticating.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference external" href="https://github.com/phpmyadmin/phpmyadmin/issues/12232">phpMyAdmin issue 12232</a>,
<a class="reference external" href="https://www.php.net/manual/en/mysqli.real-connect.php#refsect1-mysqli.real-connect-notes">MySQL documentation note</a></p>
</div>
</div>
</div>
<div class="section" id="isps-multi-user-installations">
<span id="faqmultiuser"></span><h2>ISPs, multi-user installations<a class="headerlink" href="#isps-multi-user-installations" title="Permalink to this headline">¶</a></h2>
<div class="section" id="i-m-an-isp-can-i-setup-one-central-copy-of-phpmyadmin-or-do-i-need-to-install-it-for-each-customer">
<span id="faq4-1"></span><h3>4.1 I’m an ISP. Can I setup one central copy of phpMyAdmin or do I need to install it for each customer?<a class="headerlink" href="#i-m-an-isp-can-i-setup-one-central-copy-of-phpmyadmin-or-do-i-need-to-install-it-for-each-customer" title="Permalink to this headline">¶</a></h3>
<p>Since version 2.0.3, you can setup a central copy of phpMyAdmin for all your
users. The development of this feature was kindly sponsored by NetCologne GmbH.
This requires a properly setup MySQL user management and phpMyAdmin
<a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> or cookie authentication.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="setup.html#authentication-modes"><span class="std std-ref">Using authentication modes</span></a></p>
</div>
</div>
<div class="section" id="what-s-the-preferred-way-of-making-phpmyadmin-secure-against-evil-access">
<span id="faq4-2"></span><h3>4.2 What’s the preferred way of making phpMyAdmin secure against evil access?<a class="headerlink" href="#what-s-the-preferred-way-of-making-phpmyadmin-secure-against-evil-access" title="Permalink to this headline">¶</a></h3>
<p>This depends on your system. If you’re running a server which cannot be
accessed by other people, it’s sufficient to use the directory protection
bundled with your webserver (with Apache you can use <a class="reference internal" href="glossary.html#term-.htaccess"><span class="xref std std-term">.htaccess</span></a> files,
for example). If other people have telnet access to your server, you should use
phpMyAdmin’s <a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> or cookie authentication features.</p>
<p>Suggestions:</p>
<ul class="simple">
<li><p>Your <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> file should be <code class="docutils literal notranslate"><span class="pre">chmod</span> <span class="pre">660</span></code>.</p></li>
<li><p>All your phpMyAdmin files should be chown -R phpmy.apache, where phpmy
is a user whose password is only known to you, and apache is the group
under which Apache runs.</p></li>
<li><p>Follow security recommendations for PHP and your webserver.</p></li>
</ul>
</div>
<div class="section" id="i-get-errors-about-not-being-able-to-include-a-file-in-lang-or-in-libraries">
<span id="faq4-3"></span><h3>4.3 I get errors about not being able to include a file in <em>/lang</em> or in <em>/libraries</em>.<a class="headerlink" href="#i-get-errors-about-not-being-able-to-include-a-file-in-lang-or-in-libraries" title="Permalink to this headline">¶</a></h3>
<p>Check <code class="file docutils literal notranslate"><span class="pre">php.ini</span></code>, or ask your sysadmin to check it. The
<code class="docutils literal notranslate"><span class="pre">include_path</span></code> must contain “.” somewhere in it, and
<code class="docutils literal notranslate"><span class="pre">open_basedir</span></code>, if used, must contain “.” and “./lang” to allow
normal operation of phpMyAdmin.</p>
</div>
<div class="section" id="phpmyadmin-always-gives-access-denied-when-using-http-authentication">
<span id="faq4-4"></span><h3>4.4 phpMyAdmin always gives “Access denied” when using HTTP authentication.<a class="headerlink" href="#phpmyadmin-always-gives-access-denied-when-using-http-authentication" title="Permalink to this headline">¶</a></h3>
<p>This could happen for several reasons:</p>
<ul class="simple">
<li><p><span class="target" id="index-16"></span><a class="reference internal" href="config.html#cfg_Servers_controluser"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['controluser']</span></code></a> and/or <span class="target" id="index-17"></span><a class="reference internal" href="config.html#cfg_Servers_controlpass"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['controlpass']</span></code></a>  are wrong.</p></li>
<li><p>The username/password you specify in the login dialog are invalid.</p></li>
<li><p>You have already setup a security mechanism for the phpMyAdmin-
directory, eg. a <a class="reference internal" href="glossary.html#term-.htaccess"><span class="xref std std-term">.htaccess</span></a> file. This would interfere with phpMyAdmin’s
authentication, so remove it.</p></li>
</ul>
</div>
<div class="section" id="is-it-possible-to-let-users-create-their-own-databases">
<span id="faq4-5"></span><h3>4.5 Is it possible to let users create their own databases?<a class="headerlink" href="#is-it-possible-to-let-users-create-their-own-databases" title="Permalink to this headline">¶</a></h3>
<p>Starting with 2.2.5, in the user management page, you can enter a
wildcard database name for a user (for example “joe%”), and put the
privileges you want. For example, adding <code class="docutils literal notranslate"><span class="pre">SELECT,</span> <span class="pre">INSERT,</span> <span class="pre">UPDATE,</span>
<span class="pre">DELETE,</span> <span class="pre">CREATE,</span> <span class="pre">DROP,</span> <span class="pre">INDEX,</span> <span class="pre">ALTER</span></code> would let a user create/manage
their database(s).</p>
</div>
<div class="section" id="how-can-i-use-the-host-based-authentication-additions">
<span id="faq4-6"></span><h3>4.6 How can I use the Host-based authentication additions?<a class="headerlink" href="#how-can-i-use-the-host-based-authentication-additions" title="Permalink to this headline">¶</a></h3>
<p>If you have existing rules from an old <a class="reference internal" href="glossary.html#term-.htaccess"><span class="xref std std-term">.htaccess</span></a> file, you can take them and
add a username between the <code class="docutils literal notranslate"><span class="pre">'deny'</span></code>/<code class="docutils literal notranslate"><span class="pre">'allow'</span></code> and <code class="docutils literal notranslate"><span class="pre">'from'</span></code>
strings. Using the username wildcard of <code class="docutils literal notranslate"><span class="pre">'%'</span></code> would be a major
benefit here if your installation is suited to using it. Then you can
just add those updated lines into the
<span class="target" id="index-18"></span><a class="reference internal" href="config.html#cfg_Servers_AllowDeny_rules"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['AllowDeny']['rules']</span></code></a> array.</p>
<p>If you want a pre-made sample, you can try this fragment. It stops the
‘root’ user from logging in from any networks other than the private
network <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> blocks.</p>
<div class="highlight-php notranslate"><div class="highlight"><pre><span></span><span class="c1">//block root from logging in except from the private networks</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;AllowDeny&#39;</span><span class="p">][</span><span class="s1">&#39;order&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;deny,allow&#39;</span><span class="p">;</span>
<span class="nv">$cfg</span><span class="p">[</span><span class="s1">&#39;Servers&#39;</span><span class="p">][</span><span class="nv">$i</span><span class="p">][</span><span class="s1">&#39;AllowDeny&#39;</span><span class="p">][</span><span class="s1">&#39;rules&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span>
    <span class="s1">&#39;deny root from all&#39;</span><span class="p">,</span>
    <span class="s1">&#39;allow root from localhost&#39;</span><span class="p">,</span>
    <span class="s1">&#39;allow root from 10.0.0.0/8&#39;</span><span class="p">,</span>
    <span class="s1">&#39;allow root from ***********/16&#39;</span><span class="p">,</span>
    <span class="s1">&#39;allow root from **********/12&#39;</span><span class="p">,</span>
<span class="p">];</span>
</pre></div>
</div>
</div>
<div class="section" id="authentication-window-is-displayed-more-than-once-why">
<span id="faq4-7"></span><h3>4.7 Authentication window is displayed more than once, why?<a class="headerlink" href="#authentication-window-is-displayed-more-than-once-why" title="Permalink to this headline">¶</a></h3>
<p>This happens if you are using a <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> to start phpMyAdmin which is
different than the one set in your <span class="target" id="index-19"></span><a class="reference internal" href="config.html#cfg_PmaAbsoluteUri"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PmaAbsoluteUri']</span></code></a>. For
example, a missing “www”, or entering with an <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> address while a domain
name is defined in the config file.</p>
</div>
<div class="section" id="which-parameters-can-i-use-in-the-url-that-starts-phpmyadmin">
<span id="faq4-8"></span><h3>4.8 Which parameters can I use in the URL that starts phpMyAdmin?<a class="headerlink" href="#which-parameters-can-i-use-in-the-url-that-starts-phpmyadmin" title="Permalink to this headline">¶</a></h3>
<p>When starting phpMyAdmin, you can use the <code class="docutils literal notranslate"><span class="pre">db</span></code>
and <code class="docutils literal notranslate"><span class="pre">server</span></code> parameters. This last one can contain
either the numeric host index (from <code class="docutils literal notranslate"><span class="pre">$i</span></code> of the configuration file)
or one of the host names present in the configuration file.</p>
<p>For example, to jump directly to a particular database, a URL can be constructed as
<code class="docutils literal notranslate"><span class="pre">https://example.com/phpmyadmin/?db=sakila</span></code>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="#faq1-34"><span class="std std-ref">1.34 Can I directly access a database or table pages?</span></a></p>
</div>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 4.9.0: </span>Support for using the <code class="docutils literal notranslate"><span class="pre">pma_username</span></code> and <code class="docutils literal notranslate"><span class="pre">pma_password</span></code> parameters was removed
in phpMyAdmin 4.9.0 (see <a class="reference external" href="https://www.phpmyadmin.net/security/PMASA-2019-4/">PMASA-2019-4</a>).</p>
</div>
</div>
</div>
<div class="section" id="browsers-or-client-os">
<span id="faqbrowsers"></span><h2>Browsers or client OS<a class="headerlink" href="#browsers-or-client-os" title="Permalink to this headline">¶</a></h2>
<div class="section" id="i-get-an-out-of-memory-error-and-my-controls-are-non-functional-when-trying-to-create-a-table-with-more-than-14-columns">
<span id="faq5-1"></span><h3>5.1 I get an out of memory error, and my controls are non-functional, when trying to create a table with more than 14 columns.<a class="headerlink" href="#i-get-an-out-of-memory-error-and-my-controls-are-non-functional-when-trying-to-create-a-table-with-more-than-14-columns" title="Permalink to this headline">¶</a></h3>
<p>We could reproduce this problem only under Win98/98SE. Testing under
WinNT4 or Win2K, we could easily create more than 60 columns.  A
workaround is to create a smaller number of columns, then come back to
your table properties and add the other columns.</p>
</div>
<div class="section" id="with-xitami-2-5b4-phpmyadmin-won-t-process-form-fields">
<span id="faq5-2"></span><h3>5.2 With Xitami 2.5b4, phpMyAdmin won’t process form fields.<a class="headerlink" href="#with-xitami-2-5b4-phpmyadmin-won-t-process-form-fields" title="Permalink to this headline">¶</a></h3>
<p>This is not a phpMyAdmin problem but a Xitami known bug: you’ll face
it with each script/website that use forms. Upgrade or downgrade your
Xitami server.</p>
</div>
<div class="section" id="i-have-problems-dumping-tables-with-konqueror-phpmyadmin-2-2-2">
<span id="faq5-3"></span><h3>5.3 I have problems dumping tables with Konqueror (phpMyAdmin 2.2.2).<a class="headerlink" href="#i-have-problems-dumping-tables-with-konqueror-phpmyadmin-2-2-2" title="Permalink to this headline">¶</a></h3>
<p>With Konqueror 2.1.1: plain dumps, zip and gzip dumps work ok, except
that the proposed file name for the dump is always ‘tbl_dump.php’.
The bzip2 dumps don’t seem to work. With Konqueror 2.2.1: plain dumps
work; zip dumps are placed into the user’s temporary directory, so
they must be moved before closing Konqueror, or else they disappear.
gzip dumps give an error message. Testing needs to be done for
Konqueror 2.2.2.</p>
</div>
<div class="section" id="i-can-t-use-the-cookie-authentication-mode-because-internet-explorer-never-stores-the-cookies">
<span id="faq5-4"></span><h3>5.4 I can’t use the cookie authentication mode because Internet Explorer never stores the cookies.<a class="headerlink" href="#i-can-t-use-the-cookie-authentication-mode-because-internet-explorer-never-stores-the-cookies" title="Permalink to this headline">¶</a></h3>
<p>MS Internet Explorer seems to be really buggy about cookies, at least
till version 6.</p>
</div>
<div class="section" id="faq5-5">
<span id="id13"></span><h3>5.5 (withdrawn).<a class="headerlink" href="#faq5-5" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq5-6">
<span id="id14"></span><h3>5.6 (withdrawn).<a class="headerlink" href="#faq5-6" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-refresh-reload-my-browser-and-come-back-to-the-welcome-page">
<span id="faq5-7"></span><h3>5.7 I refresh (reload) my browser, and come back to the welcome page.<a class="headerlink" href="#i-refresh-reload-my-browser-and-come-back-to-the-welcome-page" title="Permalink to this headline">¶</a></h3>
<p>Some browsers support right-clicking into the frame you want to
refresh, just do this in the right frame.</p>
</div>
<div class="section" id="with-mozilla-0-9-7-i-have-problems-sending-a-query-modified-in-the-query-box">
<span id="faq5-8"></span><h3>5.8 With Mozilla 0.9.7 I have problems sending a query modified in the query box.<a class="headerlink" href="#with-mozilla-0-9-7-i-have-problems-sending-a-query-modified-in-the-query-box" title="Permalink to this headline">¶</a></h3>
<p>Looks like a Mozilla bug: 0.9.6 was OK. We will keep an eye on future
Mozilla versions.</p>
</div>
<div class="section" id="with-mozilla-0-9-to-1-0-and-netscape-7-0-pr1-i-can-t-type-a-whitespace-in-the-sql-query-edit-area-the-page-scrolls-down">
<span id="faq5-9"></span><h3>5.9 With Mozilla 0.9.? to 1.0 and Netscape 7.0-PR1 I can’t type a whitespace in the SQL-Query edit area: the page scrolls down.<a class="headerlink" href="#with-mozilla-0-9-to-1-0-and-netscape-7-0-pr1-i-can-t-type-a-whitespace-in-the-sql-query-edit-area-the-page-scrolls-down" title="Permalink to this headline">¶</a></h3>
<p>This is a Mozilla bug (see bug #26882 at <a class="reference external" href="https://bugzilla.mozilla.org/">BugZilla</a>).</p>
</div>
<div class="section" id="faq5-10">
<span id="id15"></span><h3>5.10 (withdrawn).<a class="headerlink" href="#faq5-10" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="extended-ascii-characters-like-german-umlauts-are-displayed-wrong">
<span id="faq5-11"></span><h3>5.11 Extended-ASCII characters like German umlauts are displayed wrong.<a class="headerlink" href="#extended-ascii-characters-like-german-umlauts-are-displayed-wrong" title="Permalink to this headline">¶</a></h3>
<p>Please ensure that you have set your browser’s character set to the
one of the language file you have selected on phpMyAdmin’s start page.
Alternatively, you can try the auto detection mode that is supported
by the recent versions of the most browsers.</p>
</div>
<div class="section" id="mac-os-x-safari-browser-changes-special-characters-to">
<span id="faq5-12"></span><h3>5.12 Mac OS X Safari browser changes special characters to “?”.<a class="headerlink" href="#mac-os-x-safari-browser-changes-special-characters-to" title="Permalink to this headline">¶</a></h3>
<p>This issue has been reported by a <a class="reference internal" href="glossary.html#term-macOS"><span class="xref std std-term">macOS</span></a> user, who adds that Chimera,
Netscape and Mozilla do not have this problem.</p>
</div>
<div class="section" id="faq5-13">
<span id="id16"></span><h3>5.13 (withdrawn)<a class="headerlink" href="#faq5-13" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq5-14">
<span id="id17"></span><h3>5.14 (withdrawn)<a class="headerlink" href="#faq5-14" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq5-15">
<span id="id18"></span><h3>5.15 (withdrawn)<a class="headerlink" href="#faq5-15" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="with-internet-explorer-i-get-access-is-denied-javascript-errors-or-i-cannot-make-phpmyadmin-work-under-windows">
<span id="faq5-16"></span><h3>5.16 With Internet Explorer, I get “Access is denied” Javascript errors. Or I cannot make phpMyAdmin work under Windows.<a class="headerlink" href="#with-internet-explorer-i-get-access-is-denied-javascript-errors-or-i-cannot-make-phpmyadmin-work-under-windows" title="Permalink to this headline">¶</a></h3>
<p>Please check the following points:</p>
<ul class="simple">
<li><p>Maybe you have defined your <span class="target" id="index-20"></span><a class="reference internal" href="config.html#cfg_PmaAbsoluteUri"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['PmaAbsoluteUri']</span></code></a> setting in
<code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code> to an <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> address and you are starting phpMyAdmin
with a <a class="reference internal" href="glossary.html#term-URL"><span class="xref std std-term">URL</span></a> containing a domain name, or the reverse situation.</p></li>
<li><p>Security settings in IE and/or Microsoft Security Center are too high,
thus blocking scripts execution.</p></li>
<li><p>The Windows Firewall is blocking Apache and MySQL. You must allow
<a class="reference internal" href="glossary.html#term-HTTP"><span class="xref std std-term">HTTP</span></a> ports (80 or 443) and MySQL
port (usually 3306) in the “in” and “out” directions.</p></li>
</ul>
</div>
<div class="section" id="with-firefox-i-cannot-delete-rows-of-data-or-drop-a-database">
<span id="faq5-17"></span><h3>5.17 With Firefox, I cannot delete rows of data or drop a database.<a class="headerlink" href="#with-firefox-i-cannot-delete-rows-of-data-or-drop-a-database" title="Permalink to this headline">¶</a></h3>
<p>Many users have confirmed that the Tabbrowser Extensions plugin they
installed in their Firefox is causing the problem.</p>
</div>
<div class="section" id="faq5-18">
<span id="id19"></span><h3>5.18 (withdrawn)<a class="headerlink" href="#faq5-18" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-get-javascript-errors-in-my-browser">
<span id="faq5-19"></span><h3>5.19 I get JavaScript errors in my browser.<a class="headerlink" href="#i-get-javascript-errors-in-my-browser" title="Permalink to this headline">¶</a></h3>
<p>Issues have been reported with some combinations of browser
extensions. To troubleshoot, disable all extensions then clear your
browser cache to see if the problem goes away.</p>
</div>
<div class="section" id="i-get-errors-about-violating-content-security-policy">
<span id="faq5-20"></span><h3>5.20 I get errors about violating Content Security Policy.<a class="headerlink" href="#i-get-errors-about-violating-content-security-policy" title="Permalink to this headline">¶</a></h3>
<p>If you see errors like:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>Refused to apply inline style because it violates the following Content Security Policy directive
</pre></div>
</div>
<p>This is usually caused by some software, which wrongly rewrites
<em class="mailheader">Content Security Policy</em> headers. Usually this is caused by
antivirus proxy or browser addons which are causing such errors.</p>
<p>If you see these errors, try disabling the HTTP proxy in antivirus or disable
the <em class="mailheader">Content Security Policy</em> rewriting in it. If that doesn’t
help, try disabling browser extensions.</p>
<p>Alternatively it can be also server configuration issue (if the webserver is
configured to emit <em class="mailheader">Content Security Policy</em> headers, they can
override the ones from phpMyAdmin).</p>
<p>Programs known to cause these kind of errors:</p>
<ul class="simple">
<li><p>Kaspersky Internet Security</p></li>
</ul>
</div>
<div class="section" id="i-get-errors-about-potentially-unsafe-operation-when-browsing-table-or-executing-sql-query">
<span id="faq5-21"></span><h3>5.21 I get errors about potentially unsafe operation when browsing table or executing SQL query.<a class="headerlink" href="#i-get-errors-about-potentially-unsafe-operation-when-browsing-table-or-executing-sql-query" title="Permalink to this headline">¶</a></h3>
<p>If you see errors like:</p>
<div class="highlight-text notranslate"><div class="highlight"><pre><span></span>A potentially unsafe operation has been detected in your request to this site.
</pre></div>
</div>
<p>This is usually caused by web application firewall doing requests filtering. It
tries to prevent SQL injection, however phpMyAdmin is tool designed to execute
SQL queries, thus it makes it unusable.</p>
<p>Please allow phpMyAdmin scripts from the web application firewall settings
or disable it completely for phpMyAdmin path.</p>
<p>Programs known to cause these kind of errors:</p>
<ul class="simple">
<li><p>Wordfence Web Application Firewall</p></li>
</ul>
</div>
</div>
<div class="section" id="using-phpmyadmin">
<span id="faqusing"></span><h2>Using phpMyAdmin<a class="headerlink" href="#using-phpmyadmin" title="Permalink to this headline">¶</a></h2>
<div class="section" id="i-can-t-insert-new-rows-into-a-table-i-can-t-create-a-table-mysql-brings-up-a-sql-error">
<span id="faq6-1"></span><h3>6.1 I can’t insert new rows into a table / I can’t create a table - MySQL brings up a SQL error.<a class="headerlink" href="#i-can-t-insert-new-rows-into-a-table-i-can-t-create-a-table-mysql-brings-up-a-sql-error" title="Permalink to this headline">¶</a></h3>
<p>Examine the <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> error with care.
Often the problem is caused by specifying a wrong column-type. Common
errors include:</p>
<ul class="simple">
<li><p>Using <code class="docutils literal notranslate"><span class="pre">VARCHAR</span></code> without a size argument</p></li>
<li><p>Using <code class="docutils literal notranslate"><span class="pre">TEXT</span></code> or <code class="docutils literal notranslate"><span class="pre">BLOB</span></code> with a size argument</p></li>
</ul>
<p>Also, look at the syntax chapter in the MySQL manual to confirm that
your syntax is correct.</p>
</div>
<div class="section" id="when-i-create-a-table-i-set-an-index-for-two-columns-and-phpmyadmin-generates-only-one-index-with-those-two-columns">
<span id="faq6-2"></span><h3>6.2 When I create a table, I set an index for two columns and phpMyAdmin generates only one index with those two columns.<a class="headerlink" href="#when-i-create-a-table-i-set-an-index-for-two-columns-and-phpmyadmin-generates-only-one-index-with-those-two-columns" title="Permalink to this headline">¶</a></h3>
<p>This is the way to create a multi-columns index. If you want two
indexes, create the first one when creating the table, save, then
display the table properties and click the Index link to create the
other index.</p>
</div>
<div class="section" id="how-can-i-insert-a-null-value-into-my-table">
<span id="faq6-3"></span><h3>6.3 How can I insert a null value into my table?<a class="headerlink" href="#how-can-i-insert-a-null-value-into-my-table" title="Permalink to this headline">¶</a></h3>
<p>Since version 2.2.3, you have a checkbox for each column that can be
null. Before 2.2.3, you had to enter “null”, without the quotes, as
the column’s value. Since version 2.5.5, you have to use the checkbox
to get a real NULL value, so if you enter “NULL” this means you want a
literal NULL in the column, and not a NULL value (this works in PHP4).</p>
</div>
<div class="section" id="how-can-i-backup-my-database-or-table">
<span id="faq6-4"></span><h3>6.4 How can I backup my database or table?<a class="headerlink" href="#how-can-i-backup-my-database-or-table" title="Permalink to this headline">¶</a></h3>
<p>Click on a database or table name in the navigation panel, the properties will
be displayed. Then on the menu, click “Export”, you can dump the structure, the
data, or both. This will generate standard <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> statements that can be
used to recreate your database/table.  You will need to choose “Save as file”,
so that phpMyAdmin can transmit the resulting dump to your station.  Depending
on your PHP configuration, you will see options to compress the dump. See also
the <span class="target" id="index-21"></span><a class="reference internal" href="config.html#cfg_ExecTimeLimit"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['ExecTimeLimit']</span></code></a> configuration variable. For
additional help on this subject, look for the word “dump” in this document.</p>
</div>
<div class="section" id="how-can-i-restore-upload-my-database-or-table-using-a-dump-how-can-i-run-a-sql-file">
<span id="faq6-5"></span><h3>6.5 How can I restore (upload) my database or table using a dump? How can I run a “.sql” file?<a class="headerlink" href="#how-can-i-restore-upload-my-database-or-table-using-a-dump-how-can-i-run-a-sql-file" title="Permalink to this headline">¶</a></h3>
<p>Click on a database name in the navigation panel, the properties will
be displayed. Select “Import” from the list of tabs in the right–hand
frame (or “<a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a>” if your phpMyAdmin
version is previous to 2.7.0). In the “Location of the text file”
section, type in the path to your dump filename, or use the Browse
button. Then click Go.  With version 2.7.0, the import engine has been
re–written, if possible it is suggested that you upgrade to take
advantage of the new features.  For additional help on this subject,
look for the word “upload” in this document.</p>
<p>Note: For errors while importing of dumps exported from older MySQL versions to newer MySQL versions,
please check <a class="reference internal" href="#faq6-41"><span class="std std-ref">6.41 I get import errors while importing the dumps exported from older MySQL versions (pre-5.7.6) into newer MySQL versions (5.7.7+), but they work fine when imported back on same older versions ?</span></a>.</p>
</div>
<div class="section" id="how-can-i-use-the-relation-table-in-query-by-example">
<span id="faq6-6"></span><h3>6.6 How can I use the relation table in Query-by-example?<a class="headerlink" href="#how-can-i-use-the-relation-table-in-query-by-example" title="Permalink to this headline">¶</a></h3>
<p>Here is an example with the tables persons, towns and countries, all
located in the database “mydb”. If you don’t have a <code class="docutils literal notranslate"><span class="pre">pma__relation</span></code>
table, create it as explained in the configuration section. Then
create the example tables:</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">REL_countries</span> <span class="p">(</span>
<span class="n">country_code</span> <span class="kt">char</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">default</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
<span class="k">description</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">default</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
<span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="p">(</span><span class="n">country_code</span><span class="p">)</span>
<span class="p">)</span> <span class="k">ENGINE</span><span class="o">=</span><span class="n">MyISAM</span><span class="p">;</span>

<span class="k">INSERT</span> <span class="k">INTO</span> <span class="n">REL_countries</span> <span class="k">VALUES</span> <span class="p">(</span><span class="s1">&#39;C&#39;</span><span class="p">,</span> <span class="s1">&#39;Canada&#39;</span><span class="p">);</span>

<span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">REL_persons</span> <span class="p">(</span>
<span class="n">id</span> <span class="kt">tinyint</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">auto_increment</span><span class="p">,</span>
<span class="n">person_name</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">32</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">default</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
<span class="n">town_code</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span> <span class="k">default</span> <span class="s1">&#39;0&#39;</span><span class="p">,</span>
<span class="n">country_code</span> <span class="kt">char</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">default</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
<span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="p">(</span><span class="n">id</span><span class="p">)</span>
<span class="p">)</span> <span class="k">ENGINE</span><span class="o">=</span><span class="n">MyISAM</span><span class="p">;</span>

<span class="k">INSERT</span> <span class="k">INTO</span> <span class="n">REL_persons</span> <span class="k">VALUES</span> <span class="p">(</span><span class="mi">11</span><span class="p">,</span> <span class="s1">&#39;Marc&#39;</span><span class="p">,</span> <span class="s1">&#39;S&#39;</span><span class="p">,</span> <span class="s1">&#39;C&#39;</span><span class="p">);</span>
<span class="k">INSERT</span> <span class="k">INTO</span> <span class="n">REL_persons</span> <span class="k">VALUES</span> <span class="p">(</span><span class="mi">15</span><span class="p">,</span> <span class="s1">&#39;Paul&#39;</span><span class="p">,</span> <span class="s1">&#39;S&#39;</span><span class="p">,</span> <span class="s1">&#39;C&#39;</span><span class="p">);</span>

<span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">REL_towns</span> <span class="p">(</span>
<span class="n">town_code</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">default</span> <span class="s1">&#39;0&#39;</span><span class="p">,</span>
<span class="k">description</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">30</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span> <span class="k">default</span> <span class="s1">&#39;&#39;</span><span class="p">,</span>
<span class="k">PRIMARY</span> <span class="k">KEY</span> <span class="p">(</span><span class="n">town_code</span><span class="p">)</span>
<span class="p">)</span> <span class="k">ENGINE</span><span class="o">=</span><span class="n">MyISAM</span><span class="p">;</span>

<span class="k">INSERT</span> <span class="k">INTO</span> <span class="n">REL_towns</span> <span class="k">VALUES</span> <span class="p">(</span><span class="s1">&#39;S&#39;</span><span class="p">,</span> <span class="s1">&#39;Sherbrooke&#39;</span><span class="p">);</span>
<span class="k">INSERT</span> <span class="k">INTO</span> <span class="n">REL_towns</span> <span class="k">VALUES</span> <span class="p">(</span><span class="s1">&#39;M&#39;</span><span class="p">,</span> <span class="s1">&#39;Montréal&#39;</span><span class="p">);</span>
</pre></div>
</div>
<p>To setup appropriate links and display information:</p>
<ul class="simple">
<li><p>on table “REL_persons” click Structure, then Relation view</p></li>
<li><p>for “town_code”, choose from dropdowns, “mydb”, “REL_towns”, “town_code”
for foreign database, table and column respectively</p></li>
<li><p>for “country_code”, choose  from dropdowns, “mydb”, “REL_countries”,
“country_code” for foreign database, table and column respectively</p></li>
<li><p>on table “REL_towns” click Structure, then Relation view</p></li>
<li><p>in “Choose column to display”, choose “description”</p></li>
<li><p>repeat the two previous steps for table “REL_countries”</p></li>
</ul>
<p>Then test like this:</p>
<ul class="simple">
<li><p>Click on your db name in the navigation panel</p></li>
<li><p>Choose “Query”</p></li>
<li><p>Use tables: persons, towns, countries</p></li>
<li><p>Click “Update query”</p></li>
<li><p>In the columns row, choose persons.person_name and click the “Show”
tickbox</p></li>
<li><p>Do the same for towns.description and countries.descriptions in the
other 2 columns</p></li>
<li><p>Click “Update query” and you will see in the query box that the
correct joins have been generated</p></li>
<li><p>Click “Submit query”</p></li>
</ul>
</div>
<div class="section" id="how-can-i-use-the-display-column-feature">
<span id="faqdisplay"></span><h3>6.7 How can I use the “display column” feature?<a class="headerlink" href="#how-can-i-use-the-display-column-feature" title="Permalink to this headline">¶</a></h3>
<p>Starting from the previous example, create the <code class="docutils literal notranslate"><span class="pre">pma__table_info</span></code> as
explained in the configuration section, then browse your persons
table, and move the mouse over a town code or country code.  See also
<a class="reference internal" href="#faq6-21"><span class="std std-ref">6.21 In edit/insert mode, how can I see a list of possible values for a column, based on some foreign table?</span></a> for an additional feature that “display column”
enables: drop-down list of possible values.</p>
</div>
<div class="section" id="how-can-i-produce-a-pdf-schema-of-my-database">
<span id="faqpdf"></span><h3>6.8 How can I produce a PDF schema of my database?<a class="headerlink" href="#how-can-i-produce-a-pdf-schema-of-my-database" title="Permalink to this headline">¶</a></h3>
<p>First the configuration variables “relation”, “table_coords” and
“pdf_pages” have to be filled in.</p>
<ul class="simple">
<li><p>Select your database in the navigation panel.</p></li>
<li><p>Choose “<span class="guilabel">Designer</span>” in the navigation bar at the top.</p></li>
<li><p>Move the tables the way you want them.</p></li>
<li><p>Choose “<span class="guilabel">Export schema</span>” in the left menu.</p></li>
<li><p>The export modal will open.</p></li>
<li><p>Select the type of export to <a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a>, you may adjust the other settings.</p></li>
<li><p>Submit the form and the file will start downloading.</p></li>
</ul>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="relations.html#relations"><span class="std std-ref">Relations</span></a></p>
</div>
</div>
<div class="section" id="phpmyadmin-is-changing-the-type-of-one-of-my-columns">
<span id="faq6-9"></span><h3>6.9 phpMyAdmin is changing the type of one of my columns!<a class="headerlink" href="#phpmyadmin-is-changing-the-type-of-one-of-my-columns" title="Permalink to this headline">¶</a></h3>
<p>No, it’s MySQL that is doing <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/silent-column-changes.html">silent column type changing</a>.</p>
</div>
<div class="section" id="when-creating-a-privilege-what-happens-with-underscores-in-the-database-name">
<span id="underscore"></span><h3>6.10 When creating a privilege, what happens with underscores in the database name?<a class="headerlink" href="#when-creating-a-privilege-what-happens-with-underscores-in-the-database-name" title="Permalink to this headline">¶</a></h3>
<p>If you do not put a backslash before the underscore, this is a
wildcard grant, and the underscore means “any character”. So, if the
database name is “john_db”, the user would get rights to john1db,
john2db … If you put a backslash before the underscore, it means
that the database name will have a real underscore.</p>
</div>
<div class="section" id="what-is-the-curious-symbol-o-in-the-statistics-pages">
<span id="faq6-11"></span><h3>6.11 What is the curious symbol ø in the statistics pages?<a class="headerlink" href="#what-is-the-curious-symbol-o-in-the-statistics-pages" title="Permalink to this headline">¶</a></h3>
<p>It means “average”.</p>
</div>
<div class="section" id="i-want-to-understand-some-export-options">
<span id="faqexport"></span><h3>6.12 I want to understand some Export options.<a class="headerlink" href="#i-want-to-understand-some-export-options" title="Permalink to this headline">¶</a></h3>
<p><strong>Structure:</strong></p>
<ul class="simple">
<li><p>“Add DROP TABLE” will add a line telling MySQL to <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/drop-table.html">drop the table</a>, if it already
exists during the import. It does NOT drop the table after your
export, it only affects the import file.</p></li>
<li><p>“If Not Exists” will only create the table if it doesn’t exist.
Otherwise, you may get an error if the table name exists but has a
different structure.</p></li>
<li><p>“Add AUTO_INCREMENT value” ensures that AUTO_INCREMENT value (if
any) will be included in backup.</p></li>
<li><p>“Enclose table and column names with backquotes” ensures that column
and table names formed with special characters are protected.</p></li>
<li><p>“Add into comments” includes column comments, relations, and media
types set in the pmadb in the dump as <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> comments
(<em>/* xxx */</em>).</p></li>
</ul>
<p><strong>Data:</strong></p>
<ul class="simple">
<li><p>“Complete inserts” adds the column names on every INSERT command, for
better documentation (but resulting file is bigger).</p></li>
<li><p>“Extended inserts” provides a shorter dump file by using only once the
INSERT verb and the table name.</p></li>
<li><p>“Delayed inserts” are best explained in the <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/insert-delayed.html">MySQL manual - INSERT DELAYED Syntax</a>.</p></li>
<li><p>“Ignore inserts” treats errors as a warning instead. Again, more info
is provided in the <a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/insert.html">MySQL manual - INSERT Syntax</a>, but basically with
this selected, invalid values are adjusted and inserted rather than
causing the entire statement to fail.</p></li>
</ul>
</div>
<div class="section" id="i-would-like-to-create-a-database-with-a-dot-in-its-name">
<span id="faq6-13"></span><h3>6.13 I would like to create a database with a dot in its name.<a class="headerlink" href="#i-would-like-to-create-a-database-with-a-dot-in-its-name" title="Permalink to this headline">¶</a></h3>
<p>This is a bad idea, because in MySQL the syntax “database.table” is
the normal way to reference a database and table name. Worse, MySQL
will usually let you create a database with a dot, but then you cannot
work with it, nor delete it.</p>
</div>
<div class="section" id="faqsqlvalidator">
<span id="id20"></span><h3>6.14 (withdrawn).<a class="headerlink" href="#faqsqlvalidator" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="i-want-to-add-a-blob-column-and-put-an-index-on-it-but-mysql-says-blob-column-used-in-key-specification-without-a-key-length">
<span id="faq6-15"></span><h3>6.15 I want to add a BLOB column and put an index on it, but MySQL says “BLOB column ‘…’ used in key specification without a key length”.<a class="headerlink" href="#i-want-to-add-a-blob-column-and-put-an-index-on-it-but-mysql-says-blob-column-used-in-key-specification-without-a-key-length" title="Permalink to this headline">¶</a></h3>
<p>The right way to do this, is to create the column without any indexes,
then display the table structure and use the “Create an index” dialog.
On this page, you will be able to choose your BLOB column, and set a
size to the index, which is the condition to create an index on a BLOB
column.</p>
</div>
<div class="section" id="how-can-i-simply-move-in-page-with-plenty-editing-fields">
<span id="faq6-16"></span><h3>6.16 How can I simply move in page with plenty editing fields?<a class="headerlink" href="#how-can-i-simply-move-in-page-with-plenty-editing-fields" title="Permalink to this headline">¶</a></h3>
<p>You can use <kbd class="kbd docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Ctrl</kbd>+<kbd class="kbd docutils literal notranslate">arrows</kbd></kbd> (<kbd class="kbd docutils literal notranslate"><kbd class="kbd docutils literal notranslate">Option</kbd>+<kbd class="kbd docutils literal notranslate">Arrows</kbd></kbd> in Safari) for moving on
most pages with many editing fields (table structure changes, row editing,
etc.).</p>
</div>
<div class="section" id="transformations-i-can-t-enter-my-own-mimetype-what-is-this-feature-then-useful-for">
<span id="faq6-17"></span><h3>6.17 Transformations: I can’t enter my own mimetype! What is this feature then useful for?<a class="headerlink" href="#transformations-i-can-t-enter-my-own-mimetype-what-is-this-feature-then-useful-for" title="Permalink to this headline">¶</a></h3>
<p>Defining mimetypes is of no use if you can’t put
transformations on them. Otherwise you could just put a comment on the
column. Because entering your own mimetype will cause serious syntax
checking issues and validation, this introduces a high-risk false-
user-input situation. Instead you have to initialize mimetypes using
functions or empty mimetype definitions.</p>
<p>Plus, you have a whole overview of available mimetypes. Who knows all those
mimetypes by heart so they can enter it at will?</p>
</div>
<div class="section" id="bookmarks-where-can-i-store-bookmarks-why-can-t-i-see-any-bookmarks-below-the-query-box-what-are-these-variables-for">
<span id="faqbookmark"></span><h3>6.18 Bookmarks: Where can I store bookmarks? Why can’t I see any bookmarks below the query box? What are these variables for?<a class="headerlink" href="#bookmarks-where-can-i-store-bookmarks-why-can-t-i-see-any-bookmarks-below-the-query-box-what-are-these-variables-for" title="Permalink to this headline">¶</a></h3>
<p>You need to have configured the <a class="reference internal" href="setup.html#linked-tables"><span class="std std-ref">phpMyAdmin configuration storage</span></a> for using bookmarks
feature. Once you have done that, you can use bookmarks in the <span class="guilabel">SQL</span> tab.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="bookmarks.html#bookmarks"><span class="std std-ref">Bookmarks</span></a></p>
</div>
</div>
<div class="section" id="how-can-i-create-simple-latex-document-to-include-exported-table">
<span id="faq6-19"></span><h3>6.19 How can I create simple LATEX document to include exported table?<a class="headerlink" href="#how-can-i-create-simple-latex-document-to-include-exported-table" title="Permalink to this headline">¶</a></h3>
<p>You can simply include table in your LATEX documents,
minimal sample document should look like following one (assuming you
have table exported in file <code class="file docutils literal notranslate"><span class="pre">table.tex</span></code>):</p>
<div class="highlight-latex notranslate"><div class="highlight"><pre><span></span><span class="k">\documentclass</span><span class="nb">{</span>article<span class="nb">}</span> <span class="c">% or any class you want</span>
<span class="k">\usepackage</span><span class="nb">{</span>longtable<span class="nb">}</span>  <span class="c">% for displaying table</span>
<span class="k">\begin</span><span class="nb">{</span>document<span class="nb">}</span>        <span class="c">% start of document</span>
<span class="k">\include</span><span class="nb">{</span>table<span class="nb">}</span>         <span class="c">% including exported table</span>
<span class="k">\end</span><span class="nb">{</span>document<span class="nb">}</span>          <span class="c">% end of document</span>
</pre></div>
</div>
</div>
<div class="section" id="i-see-a-lot-of-databases-which-are-not-mine-and-cannot-access-them">
<span id="faq6-20"></span><h3>6.20 I see a lot of databases which are not mine, and cannot access them.<a class="headerlink" href="#i-see-a-lot-of-databases-which-are-not-mine-and-cannot-access-them" title="Permalink to this headline">¶</a></h3>
<p>You have one of these global privileges: CREATE TEMPORARY TABLES, SHOW
DATABASES, LOCK TABLES. Those privileges also enable users to see all the
database names. So if your users do not need those privileges, you can remove
them and their databases list will shorten.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p>&lt;<a class="reference external" href="https://bugs.mysql.com/bug.php?id=179">https://bugs.mysql.com/bug.php?id=179</a>&gt;</p>
</div>
</div>
<div class="section" id="in-edit-insert-mode-how-can-i-see-a-list-of-possible-values-for-a-column-based-on-some-foreign-table">
<span id="faq6-21"></span><h3>6.21 In edit/insert mode, how can I see a list of possible values for a column, based on some foreign table?<a class="headerlink" href="#in-edit-insert-mode-how-can-i-see-a-list-of-possible-values-for-a-column-based-on-some-foreign-table" title="Permalink to this headline">¶</a></h3>
<p>You have to setup appropriate links between the tables, and also setup
the “display column” in the foreign table. See <a class="reference internal" href="#faq6-6"><span class="std std-ref">6.6 How can I use the relation table in Query-by-example?</span></a> for an
example. Then, if there are 100 values or less in the foreign table, a
drop-down list of values will be available. You will see two lists of
values, the first list containing the key and the display column, the
second list containing the display column and the key. The reason for
this is to be able to type the first letter of either the key or the
display column. For 100 values or more, a distinct window will appear,
to browse foreign key values and choose one. To change the default
limit of 100, see <span class="target" id="index-22"></span><a class="reference internal" href="config.html#cfg_ForeignKeyMaxLimit"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['ForeignKeyMaxLimit']</span></code></a>.</p>
</div>
<div class="section" id="bookmarks-can-i-execute-a-default-bookmark-automatically-when-entering-browse-mode-for-a-table">
<span id="faq6-22"></span><h3>6.22 Bookmarks: Can I execute a default bookmark automatically when entering Browse mode for a table?<a class="headerlink" href="#bookmarks-can-i-execute-a-default-bookmark-automatically-when-entering-browse-mode-for-a-table" title="Permalink to this headline">¶</a></h3>
<p>Yes. If a bookmark has the same label as a table name and it’s not a
public bookmark, it will be executed.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="bookmarks.html#bookmarks"><span class="std std-ref">Bookmarks</span></a></p>
</div>
</div>
<div class="section" id="export-i-heard-phpmyadmin-can-export-microsoft-excel-files">
<span id="faq6-23"></span><h3>6.23 Export: I heard phpMyAdmin can export Microsoft Excel files?<a class="headerlink" href="#export-i-heard-phpmyadmin-can-export-microsoft-excel-files" title="Permalink to this headline">¶</a></h3>
<p>You can use <a class="reference internal" href="glossary.html#term-CSV"><span class="xref std std-term">CSV</span></a> for Microsoft Excel,
which works out of the box.</p>
<div class="versionchanged">
<p><span class="versionmodified changed">Changed in version 3.4.5: </span>Since phpMyAdmin 3.4.5 support for direct export to Microsoft Excel version
97 and newer was dropped.</p>
</div>
</div>
<div class="section" id="now-that-phpmyadmin-supports-native-mysql-4-1-x-column-comments-what-happens-to-my-column-comments-stored-in-pmadb">
<span id="faq6-24"></span><h3>6.24 Now that phpMyAdmin supports native MySQL 4.1.x column comments, what happens to my column comments stored in pmadb?<a class="headerlink" href="#now-that-phpmyadmin-supports-native-mysql-4-1-x-column-comments-what-happens-to-my-column-comments-stored-in-pmadb" title="Permalink to this headline">¶</a></h3>
<p>Automatic migration of a table’s pmadb-style column comments to the
native ones is done whenever you enter Structure page for this table.</p>
</div>
<div class="section" id="faq6-25">
<span id="id21"></span><h3>6.25 (withdrawn).<a class="headerlink" href="#faq6-25" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="how-can-i-select-a-range-of-rows">
<span id="faq6-26"></span><h3>6.26 How can I select a range of rows?<a class="headerlink" href="#how-can-i-select-a-range-of-rows" title="Permalink to this headline">¶</a></h3>
<p>Click the first row of the range, hold the shift key and click the
last row of the range. This works everywhere you see rows, for example
in Browse mode or on the Structure page.</p>
</div>
<div class="section" id="what-format-strings-can-i-use">
<span id="faq6-27"></span><h3>6.27 What format strings can I use?<a class="headerlink" href="#what-format-strings-can-i-use" title="Permalink to this headline">¶</a></h3>
<p>In all places where phpMyAdmin accepts format strings, you can use
<code class="docutils literal notranslate"><span class="pre">&#64;VARIABLE&#64;</span></code> expansion and <a class="reference external" href="https://www.php.net/strftime">strftime</a>
format strings. The expanded variables depend on a context (for
example, if you haven’t chosen a table, you can not get the table
name), but the following variables can be used:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">&#64;HTTP_HOST&#64;</span></code></dt><dd><p>HTTP host that runs phpMyAdmin</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;SERVER&#64;</span></code></dt><dd><p>MySQL server name</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;VERBOSE&#64;</span></code></dt><dd><p>Verbose MySQL server name as defined in <span class="target" id="index-23"></span><a class="reference internal" href="config.html#cfg_Servers_verbose"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['Servers'][$i]['verbose']</span></code></a></p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;VSERVER&#64;</span></code></dt><dd><p>Verbose MySQL server name if set, otherwise normal</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;DATABASE&#64;</span></code></dt><dd><p>Currently opened database</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;TABLE&#64;</span></code></dt><dd><p>Currently opened table</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;COLUMNS&#64;</span></code></dt><dd><p>Columns of the currently opened table</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">&#64;PHPMYADMIN&#64;</span></code></dt><dd><p>phpMyAdmin with version</p>
</dd>
</dl>
</div>
<div class="section" id="faq6-28">
<span id="id22"></span><h3>6.28 (withdrawn).<a class="headerlink" href="#faq6-28" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="why-can-t-i-get-a-chart-from-my-query-result-table">
<span id="faq6-29"></span><h3>6.29 Why can’t I get a chart from my query result table?<a class="headerlink" href="#why-can-t-i-get-a-chart-from-my-query-result-table" title="Permalink to this headline">¶</a></h3>
<p>Not every table can be put to the chart. Only tables with one, two or
three columns can be visualised as a chart. Moreover the table must be
in a special format for chart script to understand it. Currently
supported formats can be found in <a class="reference internal" href="charts.html#charts"><span class="std std-ref">Charts</span></a>.</p>
</div>
<div class="section" id="import-how-can-i-import-esri-shapefiles">
<span id="faq6-30"></span><h3>6.30 Import: How can I import ESRI Shapefiles?<a class="headerlink" href="#import-how-can-i-import-esri-shapefiles" title="Permalink to this headline">¶</a></h3>
<p>An ESRI Shapefile is actually a set of several files, where .shp file
contains geometry data and .dbf file contains data related to those
geometry data. To read data from .dbf file you need to have PHP
compiled with the dBase extension (–enable-dbase). Otherwise only
geometry data will be imported.</p>
<p>To upload these set of files you can use either of the following
methods:</p>
<p>Configure upload directory with <span class="target" id="index-24"></span><a class="reference internal" href="config.html#cfg_UploadDir"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['UploadDir']</span></code></a>, upload both .shp and .dbf files with
the same filename and chose the .shp file from the import page.</p>
<p>Create a zip archive with .shp and .dbf files and import it. For this
to work, you need to set <span class="target" id="index-25"></span><a class="reference internal" href="config.html#cfg_TempDir"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['TempDir']</span></code></a> to a place where the web server user can
write (for example <code class="docutils literal notranslate"><span class="pre">'./tmp'</span></code>).</p>
<p>To create the temporary directory on a UNIX-based system, you can do:</p>
<div class="highlight-sh notranslate"><div class="highlight"><pre><span></span><span class="nb">cd</span> phpMyAdmin
mkdir tmp
chmod o+rwx tmp
</pre></div>
</div>
</div>
<div class="section" id="how-do-i-create-a-relation-in-designer">
<span id="faq6-31"></span><h3>6.31 How do I create a relation in designer?<a class="headerlink" href="#how-do-i-create-a-relation-in-designer" title="Permalink to this headline">¶</a></h3>
<p>To select relation, click:  The display column is shown in pink. To
set/unset a column as the display column, click the “Choose column to
display” icon, then click on the appropriate column name.</p>
</div>
<div class="section" id="how-can-i-use-the-zoom-search-feature">
<span id="faq6-32"></span><h3>6.32 How can I use the zoom search feature?<a class="headerlink" href="#how-can-i-use-the-zoom-search-feature" title="Permalink to this headline">¶</a></h3>
<p>The Zoom search feature is an alternative to table search feature. It allows
you to explore a table by representing its data in a scatter plot. You can
locate this feature by selecting a table and clicking the <span class="guilabel">Search</span>
tab. One of the sub-tabs in the <span class="guilabel">Table Search</span> page is
<span class="guilabel">Zoom Search</span>.</p>
<p>Consider the table REL_persons in <a class="reference internal" href="#faq6-6"><span class="std std-ref">6.6 How can I use the relation table in Query-by-example?</span></a> for
an example. To use zoom search, two columns need to be selected, for
example, id and town_code. The id values will be represented on one
axis and town_code values on the other axis. Each row will be
represented as a point in a scatter plot based on its id and
town_code. You can include two additional search criteria apart from
the two fields to display.</p>
<p>You can choose which field should be
displayed as label for each point. If a display column has been set
for the table (see <a class="reference internal" href="#faqdisplay"><span class="std std-ref">6.7 How can I use the “display column” feature?</span></a>), it is taken as the label unless
you specify otherwise. You can also select the maximum number of rows
you want to be displayed in the plot by specifing it in the ‘Max rows
to plot’ field. Once you have decided over your criteria, click ‘Go’
to display the plot.</p>
<p>After the plot is generated, you can use the
mouse wheel to zoom in and out of the plot. In addition, panning
feature is enabled to navigate through the plot. You can zoom-in to a
certain level of detail and use panning to locate your area of
interest. Clicking on a point opens a dialogue box, displaying field
values of the data row represented by the point. You can edit the
values if required and click on submit to issue an update query. Basic
instructions on how to use can be viewed by clicking the ‘How to use?’
link located just above the plot.</p>
</div>
<div class="section" id="when-browsing-a-table-how-can-i-copy-a-column-name">
<span id="faq6-33"></span><h3>6.33 When browsing a table, how can I copy a column name?<a class="headerlink" href="#when-browsing-a-table-how-can-i-copy-a-column-name" title="Permalink to this headline">¶</a></h3>
<p>Selecting the name of the column within the browse table header cell
for copying is difficult, as the columns support reordering by
dragging the header cells as well as sorting by clicking on the linked
column name. To copy a column name, double-click on the empty area
next to the column name, when the tooltip tells you to do so. This
will show you an input box with the column name. You may right-click
the column name within this input box to copy it to your clipboard.</p>
</div>
<div class="section" id="how-can-i-use-the-favorite-tables-feature">
<span id="faq6-34"></span><h3>6.34 How can I use the Favorite Tables feature?<a class="headerlink" href="#how-can-i-use-the-favorite-tables-feature" title="Permalink to this headline">¶</a></h3>
<p>Favorite Tables feature is very much similar to Recent Tables feature.
It allows you to add a shortcut for the frequently used tables of any
database in the navigation panel . You can easily navigate to any table
in the list by simply choosing it from the list. These tables are stored
in your browser’s local storage if you have not configured your
<cite>phpMyAdmin Configuration Storage</cite>. Otherwise these entries are stored in
<cite>phpMyAdmin Configuration Storage</cite>.</p>
<p>IMPORTANT: In absence of <cite>phpMyAdmin Configuration Storage</cite>, your Favorite
tables may be different in different browsers based on your different
selections in them.</p>
<p>To add a table to Favorite list simply click on the <cite>Gray</cite> star in front
of a table name in the list of tables of a Database and wait until it
turns to <cite>Yellow</cite>.
To remove a table from list, simply click on the <cite>Yellow</cite> star and
wait until it turns <cite>Gray</cite> again.</p>
<p>Using <span class="target" id="index-26"></span><a class="reference internal" href="config.html#cfg_NumFavoriteTables"><code class="xref config config-option docutils literal notranslate"><span class="pre">$cfg['NumFavoriteTables']</span></code></a> in your <code class="file docutils literal notranslate"><span class="pre">config.inc.php</span></code>
file, you can define the  maximum number of favorite tables shown in the
navigation panel. Its default value is <cite>10</cite>.</p>
</div>
<div class="section" id="how-can-i-use-the-range-search-feature">
<span id="faq6-35"></span><h3>6.35 How can I use the Range search feature?<a class="headerlink" href="#how-can-i-use-the-range-search-feature" title="Permalink to this headline">¶</a></h3>
<p>With the help of range search feature, one can specify a range of values for
particular column(s) while performing search operation on a table from the <cite>Search</cite>
tab.</p>
<p>To use this feature simply click on the <cite>BETWEEN</cite> or <cite>NOT BETWEEN</cite> operators
from the operator select list in front of the column name. On choosing one of the
above options, a dialog box will show up asking for the <cite>Minimum</cite> and <cite>Maximum</cite>
value for that column. Only the specified range of values will be included
in case of <cite>BETWEEN</cite> and excluded in case of <cite>NOT BETWEEN</cite> from the final results.</p>
<p>Note: The Range search feature will work only <cite>Numeric</cite> and <cite>Date</cite> data type columns.</p>
</div>
<div class="section" id="what-is-central-columns-and-how-can-i-use-this-feature">
<span id="faq6-36"></span><h3>6.36 What is Central columns and how can I use this feature?<a class="headerlink" href="#what-is-central-columns-and-how-can-i-use-this-feature" title="Permalink to this headline">¶</a></h3>
<p>As the name suggests, the Central columns feature enables to maintain a central list of
columns per database to avoid similar name for the same data element and bring consistency
of data type for the same data element. You can use the central list of columns to
add an element to any table structure in that database which will save from writing
similar column name and column definition.</p>
<p>To add a column to central list, go to table structure page, check the columns you want
to include and then simply click on “Add to central columns”. If you want to add all
unique columns from more than one table from a database then go to database structure page,
check the tables you want to include and then select “Add columns to central list”.</p>
<p>To remove a column from central list, go to Table structure page, check the columns you want
to remove and then simply click on “Remove from central columns”. If you want to remove all
columns from more than one tables from a database then go to database structure page,
check the tables you want to include and then select “Remove columns from central list”.</p>
<p>To view and manage the central list, select the database you want to manage central columns
for then from the top menu click on “Central columns”. You will be taken to a page where
you will have options to edit, delete or add new columns to central list.</p>
</div>
<div class="section" id="how-can-i-use-improve-table-structure-feature">
<span id="faq6-37"></span><h3>6.37 How can I use Improve Table structure feature?<a class="headerlink" href="#how-can-i-use-improve-table-structure-feature" title="Permalink to this headline">¶</a></h3>
<p>Improve table structure feature helps to bring the table structure upto
Third Normal Form. A wizard is presented to user which asks questions about the
elements during the various steps for normalization and a new structure is proposed
accordingly to bring the table into the First/Second/Third Normal form.
On startup of the wizard, user gets to select upto what normal form they want to
normalize the table structure.</p>
<p>Here is an example table which you can use to test all of the three First, Second and
Third Normal Form.</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="k">CREATE</span> <span class="k">TABLE</span> <span class="n">`VetOffice`</span> <span class="p">(</span>
 <span class="n">`petName`</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">64</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`petBreed`</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">64</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`petType`</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">64</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`petDOB`</span> <span class="kt">date</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`ownerLastName`</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">64</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`ownerFirstName`</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">64</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`ownerPhone1`</span> <span class="kt">int</span><span class="p">(</span><span class="mi">12</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`ownerPhone2`</span> <span class="kt">int</span><span class="p">(</span><span class="mi">12</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
 <span class="n">`ownerEmail`</span> <span class="kt">varchar</span><span class="p">(</span><span class="mi">64</span><span class="p">)</span> <span class="k">NOT</span> <span class="no">NULL</span><span class="p">,</span>
<span class="p">);</span>
</pre></div>
</div>
<p>The above table is not in First normal Form as no <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a> exists. Primary key
is supposed to be (<cite>petName</cite>,`ownerLastName`,`ownerFirstName`) . If the <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a>
is chosen as suggested the resultant table won’t be in Second as well as Third Normal
form as the following dependencies exists.</p>
<div class="highlight-mysql notranslate"><div class="highlight"><pre><span></span><span class="p">(</span><span class="n">OwnerLastName</span><span class="p">,</span> <span class="n">OwnerFirstName</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">OwnerEmail</span>
<span class="p">(</span><span class="n">OwnerLastName</span><span class="p">,</span> <span class="n">OwnerFirstName</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">OwnerPhone</span>
<span class="n">PetBreed</span> <span class="o">-&gt;</span> <span class="n">PetType</span>
</pre></div>
</div>
<p>Which says, OwnerEmail depends on OwnerLastName and OwnerFirstName.
OwnerPhone depends on OwnerLastName and OwnerFirstName.
PetType depends on PetBreed.</p>
</div>
<div class="section" id="how-can-i-reassign-auto-incremented-values">
<span id="faq6-38"></span><h3>6.38 How can I reassign auto-incremented values?<a class="headerlink" href="#how-can-i-reassign-auto-incremented-values" title="Permalink to this headline">¶</a></h3>
<p>Some users prefer their AUTO_INCREMENT values to be consecutive; this is not
always the case after row deletion.</p>
<p>Here are the steps to accomplish this. These are manual steps because they
involve a manual verification at one point.</p>
<ul class="simple">
<li><p>Ensure that you have exclusive access to the table to rearrange</p></li>
<li><p>On your <a class="reference internal" href="glossary.html#term-primary-key"><span class="xref std std-term">primary key</span></a> column (i.e. id), remove the AUTO_INCREMENT setting</p></li>
<li><p>Delete your primary key in Structure &gt; indexes</p></li>
<li><p>Create a new column future_id as primary key, AUTO_INCREMENT</p></li>
<li><p>Browse your table and verify that the new increments correspond to what
you’re expecting</p></li>
<li><p>Drop your old id column</p></li>
<li><p>Rename the future_id column to id</p></li>
<li><p>Move the new id column via Structure &gt; Move columns</p></li>
</ul>
</div>
<div class="section" id="what-is-the-adjust-privileges-option-when-renaming-copying-or-moving-a-database-table-column-or-procedure">
<span id="faq6-39"></span><h3>6.39 What is the “Adjust privileges” option when renaming, copying, or moving a database, table, column, or procedure?<a class="headerlink" href="#what-is-the-adjust-privileges-option-when-renaming-copying-or-moving-a-database-table-column-or-procedure" title="Permalink to this headline">¶</a></h3>
<p>When renaming/copying/moving a database/table/column/procedure,
MySQL does not adjust the original privileges relating to these objects
on its own. By selecting this option, phpMyAdmin will adjust the privilege
table so that users have the same privileges on the new items.</p>
<p>For example: A user <a class="reference external" href="mailto:'bob'&#37;&#52;&#48;'localhost">‘bob’<span>&#64;</span>’localhost</a>’ has a ‘SELECT’ privilege on a
column named ‘id’. Now, if this column is renamed to ‘id_new’, MySQL,
on its own, would <strong>not</strong> adjust the column privileges to the new column name.
phpMyAdmin can make this adjustment for you automatically.</p>
<p>Notes:</p>
<ul class="simple">
<li><p>While adjusting privileges for a database, the privileges of all
database-related elements (tables, columns and procedures) are also adjusted
to the database’s new name.</p></li>
<li><p>Similarly, while adjusting privileges for a table, the privileges of all
the columns inside the new table are also adjusted.</p></li>
<li><p>While adjusting privileges, the user performing the operation <strong>must</strong> have the following
privileges:</p>
<ul>
<li><p>SELECT, INSERT, UPDATE, DELETE privileges on following tables:
<cite>mysql</cite>.`db`, <cite>mysql</cite>.`columns_priv`, <cite>mysql</cite>.`tables_priv`, <cite>mysql</cite>.`procs_priv`</p></li>
<li><p>FLUSH privilege (GLOBAL)</p></li>
</ul>
</li>
</ul>
<p>Thus, if you want to replicate the database/table/column/procedure as it is
while renaming/copying/moving these objects, make sure you have checked this option.</p>
</div>
<div class="section" id="i-see-bind-parameters-checkbox-in-the-sql-page-how-do-i-write-parameterized-sql-queries">
<span id="faq6-40"></span><h3>6.40 I see “Bind parameters” checkbox in the “SQL” page. How do I write parameterized SQL queries?<a class="headerlink" href="#i-see-bind-parameters-checkbox-in-the-sql-page-how-do-i-write-parameterized-sql-queries" title="Permalink to this headline">¶</a></h3>
<p>From version 4.5, phpMyAdmin allows users to execute parameterized queries in the “SQL” page.
Parameters should be prefixed with a colon(:) and when the “Bind parameters” checkbox is checked
these parameters will be identified and input fields for these parameters will be presented.
Values entered in these field will be substituted in the query before being executed.</p>
</div>
<div class="section" id="i-get-import-errors-while-importing-the-dumps-exported-from-older-mysql-versions-pre-5-7-6-into-newer-mysql-versions-5-7-7-but-they-work-fine-when-imported-back-on-same-older-versions">
<span id="faq6-41"></span><h3>6.41 I get import errors while importing the dumps exported from older MySQL versions (pre-5.7.6) into newer MySQL versions (5.7.7+), but they work fine when imported back on same older versions ?<a class="headerlink" href="#i-get-import-errors-while-importing-the-dumps-exported-from-older-mysql-versions-pre-5-7-6-into-newer-mysql-versions-5-7-7-but-they-work-fine-when-imported-back-on-same-older-versions" title="Permalink to this headline">¶</a></h3>
<p>If you get errors like <em>#1031 - Table storage engine for ‘table_name’ doesn’t have this option</em>
while importing the dumps exported from pre-5.7.7 MySQL servers into new MySQL server versions 5.7.7+,
it might be because ROW_FORMAT=FIXED is not supported with InnoDB tables. Moreover, the value of
<a class="reference external" href="https://dev.mysql.com/doc/refman/5.7/en/innodb-parameters.html#sysvar_innodb_strict_mode">innodb_strict_mode</a> would define if this would be reported as a warning or as an error.</p>
<p>Since MySQL version 5.7.9, the default value for <cite>innodb_strict_mode</cite> is <cite>ON</cite> and thus would generate
an error when such a CREATE TABLE or ALTER TABLE statement is encountered.</p>
<p>There are two ways of preventing such errors while importing:</p>
<ul class="simple">
<li><p>Change the value of <cite>innodb_strict_mode</cite> to <cite>OFF</cite> before starting the import and turn it <cite>ON</cite> after
the import is successfully completed.</p></li>
<li><p>This can be achieved in two ways:</p>
<ul>
<li><p>Go to ‘Variables’ page and edit the value of <cite>innodb_strict_mode</cite></p></li>
<li><p>Run the query : <cite>SET GLOBAL `innodb_strict_mode</cite> = ‘[value]’`</p></li>
</ul>
</li>
</ul>
<p>After the import is done, it is suggested that the value of <cite>innodb_strict_mode</cite> should be reset to the
original value.</p>
</div>
</div>
<div class="section" id="phpmyadmin-project">
<span id="faqproject"></span><h2>phpMyAdmin project<a class="headerlink" href="#phpmyadmin-project" title="Permalink to this headline">¶</a></h2>
<div class="section" id="i-have-found-a-bug-how-do-i-inform-developers">
<span id="faq7-1"></span><h3>7.1 I have found a bug. How do I inform developers?<a class="headerlink" href="#i-have-found-a-bug-how-do-i-inform-developers" title="Permalink to this headline">¶</a></h3>
<p>Our issues tracker is located at &lt;<a class="reference external" href="https://github.com/phpmyadmin/phpmyadmin/issues">https://github.com/phpmyadmin/phpmyadmin/issues</a>&gt;.
For security issues, please refer to the instructions at &lt;<a class="reference external" href="https://www.phpmyadmin.net/security">https://www.phpmyadmin.net/security</a>&gt; to email
the developers directly.</p>
</div>
<div class="section" id="i-want-to-translate-the-messages-to-a-new-language-or-upgrade-an-existing-language-where-do-i-start">
<span id="faq7-2"></span><h3>7.2 I want to translate the messages to a new language or upgrade an existing language, where do I start?<a class="headerlink" href="#i-want-to-translate-the-messages-to-a-new-language-or-upgrade-an-existing-language-where-do-i-start" title="Permalink to this headline">¶</a></h3>
<p>Translations are very welcome and all you need to have are the
language skills. The easiest way is to use our <a class="reference external" href="https://hosted.weblate.org/projects/phpmyadmin/">online translation
service</a>. You can check
out all the possibilities to translate in the <a class="reference external" href="https://www.phpmyadmin.net/translate/">translate section on
our website</a>.</p>
</div>
<div class="section" id="i-would-like-to-help-out-with-the-development-of-phpmyadmin-how-should-i-proceed">
<span id="faq7-3"></span><h3>7.3 I would like to help out with the development of phpMyAdmin. How should I proceed?<a class="headerlink" href="#i-would-like-to-help-out-with-the-development-of-phpmyadmin-how-should-i-proceed" title="Permalink to this headline">¶</a></h3>
<p>We welcome every contribution to the development of phpMyAdmin. You
can check out all the possibilities to contribute in the <a class="reference external" href="https://www.phpmyadmin.net/contribute/">contribute
section on our website</a>.</p>
<div class="admonition seealso">
<p class="admonition-title">See also</p>
<p><a class="reference internal" href="developers.html#developers"><span class="std std-ref">Developers Information</span></a></p>
</div>
</div>
</div>
<div class="section" id="security">
<span id="faqsecurity"></span><h2>Security<a class="headerlink" href="#security" title="Permalink to this headline">¶</a></h2>
<div class="section" id="where-can-i-get-information-about-the-security-alerts-issued-for-phpmyadmin">
<span id="faq8-1"></span><h3>8.1 Where can I get information about the security alerts issued for phpMyAdmin?<a class="headerlink" href="#where-can-i-get-information-about-the-security-alerts-issued-for-phpmyadmin" title="Permalink to this headline">¶</a></h3>
<p>Please refer to &lt;<a class="reference external" href="https://www.phpmyadmin.net/security/">https://www.phpmyadmin.net/security/</a>&gt;.</p>
</div>
<div class="section" id="how-can-i-protect-phpmyadmin-against-brute-force-attacks">
<span id="faq8-2"></span><h3>8.2 How can I protect phpMyAdmin against brute force attacks?<a class="headerlink" href="#how-can-i-protect-phpmyadmin-against-brute-force-attacks" title="Permalink to this headline">¶</a></h3>
<p>If you use Apache web server, phpMyAdmin exports information about
authentication to the Apache environment and it can be used in Apache
logs. Currently there are two variables available:</p>
<dl class="simple">
<dt><code class="docutils literal notranslate"><span class="pre">userID</span></code></dt><dd><p>User name of currently active user (they do not have to be logged in).</p>
</dd>
<dt><code class="docutils literal notranslate"><span class="pre">userStatus</span></code></dt><dd><p>Status of currently active user, one of <code class="docutils literal notranslate"><span class="pre">ok</span></code> (user is logged in),
<code class="docutils literal notranslate"><span class="pre">mysql-denied</span></code> (MySQL denied user login), <code class="docutils literal notranslate"><span class="pre">allow-denied</span></code> (user denied
by allow/deny rules), <code class="docutils literal notranslate"><span class="pre">root-denied</span></code> (root is denied in configuration),
<code class="docutils literal notranslate"><span class="pre">empty-denied</span></code> (empty password is denied).</p>
</dd>
</dl>
<p><code class="docutils literal notranslate"><span class="pre">LogFormat</span></code> directive for Apache can look like following:</p>
<div class="highlight-apache notranslate"><div class="highlight"><pre><span></span><span class="nb">LogFormat</span> <span class="s2">&quot;%h %l %u %t \&quot;%r\&quot; %&gt;s %b \&quot;%{Referer}i\&quot; \&quot;%{User-Agent}i\&quot; %{userID}n %{userStatus}n&quot;</span>   pma_combined
</pre></div>
</div>
<p>You can then use any log analyzing tools to detect possible break-in
attempts.</p>
</div>
<div class="section" id="why-are-there-path-disclosures-when-directly-loading-certain-files">
<span id="faq8-3"></span><h3>8.3 Why are there path disclosures when directly loading certain files?<a class="headerlink" href="#why-are-there-path-disclosures-when-directly-loading-certain-files" title="Permalink to this headline">¶</a></h3>
<p>This is a server configuration problem. Never enable <code class="docutils literal notranslate"><span class="pre">display_errors</span></code> on a production site.</p>
</div>
<div class="section" id="csv-files-exported-from-phpmyadmin-could-allow-a-formula-injection-attack">
<span id="faq8-4"></span><h3>8.4 CSV files exported from phpMyAdmin could allow a formula injection attack.<a class="headerlink" href="#csv-files-exported-from-phpmyadmin-could-allow-a-formula-injection-attack" title="Permalink to this headline">¶</a></h3>
<p>It is possible to generate a <a class="reference internal" href="glossary.html#term-CSV"><span class="xref std std-term">CSV</span></a> file that, when imported to a spreadsheet program such as Microsoft Excel,
could potentially allow the execution of arbitrary commands.</p>
<p>The CSV files generated by phpMyAdmin could potentially contain text that would be interpreted by a spreadsheet program as
a formula, but we do not believe escaping those fields is the proper behavior. There is no means to properly escape and
differentiate between a desired text output and a formula that should be escaped, and CSV is a text format where function
definitions should not be interpreted anyway. We have discussed this at length and feel it is the responsibility of the
spreadsheet program to properly parse and sanitize such data on input instead.</p>
<p>Google also has a <a class="reference external" href="https://sites.google.com/site/bughunteruniversity/nonvuln/csv-excel-formula-injection">similar view</a>.</p>
</div>
</div>
<div class="section" id="synchronization">
<span id="faqsynchronization"></span><h2>Synchronization<a class="headerlink" href="#synchronization" title="Permalink to this headline">¶</a></h2>
<div class="section" id="faq9-1">
<span id="id23"></span><h3>9.1 (withdrawn).<a class="headerlink" href="#faq9-1" title="Permalink to this headline">¶</a></h3>
</div>
<div class="section" id="faq9-2">
<span id="id24"></span><h3>9.2 (withdrawn).<a class="headerlink" href="#faq9-2" title="Permalink to this headline">¶</a></h3>
</div>
</div>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">FAQ - Frequently Asked Questions</a><ul>
<li><a class="reference internal" href="#server">Server</a><ul>
<li><a class="reference internal" href="#my-server-is-crashing-each-time-a-specific-action-is-required-or-phpmyadmin-sends-a-blank-page-or-a-page-full-of-cryptic-characters-to-my-browser-what-can-i-do">1.1 My server is crashing each time a specific action is required or phpMyAdmin sends a blank page or a page full of cryptic characters to my browser, what can I do?</a></li>
<li><a class="reference internal" href="#my-apache-server-crashes-when-using-phpmyadmin">1.2 My Apache server crashes when using phpMyAdmin.</a></li>
<li><a class="reference internal" href="#withdrawn">1.3 (withdrawn).</a></li>
<li><a class="reference internal" href="#using-phpmyadmin-on-iis-i-m-displayed-the-error-message-the-specified-cgi-application-misbehaved-by-not-returning-a-complete-set-of-http-headers">1.4 Using phpMyAdmin on IIS, I’m displayed the error message: “The specified CGI application misbehaved by not returning a complete set of HTTP headers …”.</a></li>
<li><a class="reference internal" href="#using-phpmyadmin-on-iis-i-m-facing-crashes-and-or-many-error-messages-with-the-http">1.5 Using phpMyAdmin on IIS, I’m facing crashes and/or many error messages with the HTTP.</a></li>
<li><a class="reference internal" href="#i-can-t-use-phpmyadmin-on-pws-nothing-is-displayed">1.6 I can’t use phpMyAdmin on PWS: nothing is displayed!</a></li>
<li><a class="reference internal" href="#how-can-i-gzip-a-dump-or-a-csv-export-it-does-not-seem-to-work">1.7 How can I gzip a dump or a CSV export? It does not seem to work.</a></li>
<li><a class="reference internal" href="#i-cannot-insert-a-text-file-in-a-table-and-i-get-an-error-about-safe-mode-being-in-effect">1.8 I cannot insert a text file in a table, and I get an error about safe mode being in effect.</a></li>
<li><a class="reference internal" href="#faq1-9">1.9 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-m-having-troubles-when-uploading-files-with-phpmyadmin-running-on-a-secure-server-my-browser-is-internet-explorer-and-i-m-using-the-apache-server">1.10 I’m having troubles when uploading files with phpMyAdmin running on a secure server. My browser is Internet Explorer and I’m using the Apache server.</a></li>
<li><a class="reference internal" href="#i-get-an-open-basedir-restriction-while-uploading-a-file-from-the-import-tab">1.11 I get an ‘open_basedir restriction’ while uploading a file from the import tab.</a></li>
<li><a class="reference internal" href="#i-have-lost-my-mysql-root-password-what-can-i-do">1.12 I have lost my MySQL root password, what can I do?</a></li>
<li><a class="reference internal" href="#faq1-13">1.13 (withdrawn).</a></li>
<li><a class="reference internal" href="#faq1-14">1.14 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-have-problems-with-mysql-user-column-names">1.15 I have problems with <em>mysql.user</em> column names.</a></li>
<li><a class="reference internal" href="#i-cannot-upload-big-dump-files-memory-http-or-timeout-problems">1.16 I cannot upload big dump files (memory, HTTP or timeout problems).</a></li>
<li><a class="reference internal" href="#which-database-versions-does-phpmyadmin-support">1.17 Which Database versions does phpMyAdmin support?</a></li>
<li><a class="reference internal" href="#a-i-cannot-connect-to-the-mysql-server-it-always-returns-the-error-message-client-does-not-support-authentication-protocol-requested-by-server-consider-upgrading-mysql-client">1.17a I cannot connect to the MySQL server. It always returns the error message, “Client does not support authentication protocol requested by server; consider upgrading MySQL client”</a></li>
<li><a class="reference internal" href="#faq1-18">1.18 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-can-t-run-the-display-relations-feature-because-the-script-seems-not-to-know-the-font-face-i-m-using">1.19 I can’t run the “display relations” feature because the script seems not to know the font face I’m using!</a></li>
<li><a class="reference internal" href="#i-receive-an-error-about-missing-mysqli-and-mysql-extensions">1.20 I receive an error about missing mysqli and mysql extensions.</a></li>
<li><a class="reference internal" href="#i-am-running-the-cgi-version-of-php-under-unix-and-i-cannot-log-in-using-cookie-auth">1.21 I am running the CGI version of PHP under Unix, and I cannot log in using cookie auth.</a></li>
<li><a class="reference internal" href="#i-don-t-see-the-location-of-text-file-field-so-i-cannot-upload">1.22 I don’t see the “Location of text file” field, so I cannot upload.</a></li>
<li><a class="reference internal" href="#i-m-running-mysql-on-a-win32-machine-each-time-i-create-a-new-table-the-table-and-column-names-are-changed-to-lowercase">1.23 I’m running MySQL on a Win32 machine. Each time I create a new table the table and column names are changed to lowercase!</a></li>
<li><a class="reference internal" href="#faq1-24">1.24 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-am-running-apache-with-mod-gzip-1-3-26-1a-on-windows-xp-and-i-get-problems-such-as-undefined-variables-when-i-run-a-sql-query">1.25 I am running Apache with mod_gzip-1.3.26.1a on Windows XP, and I get problems, such as undefined variables when I run a SQL query.</a></li>
<li><a class="reference internal" href="#i-just-installed-phpmyadmin-in-my-document-root-of-iis-but-i-get-the-error-no-input-file-specified-when-trying-to-run-phpmyadmin">1.26 I just installed phpMyAdmin in my document root of IIS but I get the error “No input file specified” when trying to run phpMyAdmin.</a></li>
<li><a class="reference internal" href="#i-get-empty-page-when-i-want-to-view-huge-page-eg-db-structure-php-with-plenty-of-tables">1.27 I get empty page when I want to view huge page (eg. db_structure.php with plenty of tables).</a></li>
<li><a class="reference internal" href="#my-mysql-server-sometimes-refuses-queries-and-returns-the-message-errorcode-13-what-does-this-mean">1.28 My MySQL server sometimes refuses queries and returns the message ‘Errorcode: 13’. What does this mean?</a></li>
<li><a class="reference internal" href="#when-i-create-a-table-or-modify-a-column-i-get-an-error-and-the-columns-are-duplicated">1.29 When I create a table or modify a column, I get an error and the columns are duplicated.</a></li>
<li><a class="reference internal" href="#i-get-the-error-navigation-php-missing-hash">1.30 I get the error “navigation.php: Missing hash”.</a></li>
<li><a class="reference internal" href="#which-php-versions-does-phpmyadmin-support">1.31 Which PHP versions does phpMyAdmin support?</a></li>
<li><a class="reference internal" href="#can-i-use-http-authentication-with-iis">1.32 Can I use HTTP authentication with IIS?</a></li>
<li><a class="reference internal" href="#faq1-33">1.33 (withdrawn).</a></li>
<li><a class="reference internal" href="#can-i-directly-access-a-database-or-table-pages">1.34 Can I directly access a database or table pages?</a></li>
<li><a class="reference internal" href="#can-i-use-http-authentication-with-apache-cgi">1.35 Can I use HTTP authentication with Apache CGI?</a></li>
<li><a class="reference internal" href="#i-get-an-error-500-internal-server-error">1.36 I get an error “500 Internal Server Error”.</a></li>
<li><a class="reference internal" href="#i-run-phpmyadmin-on-cluster-of-different-machines-and-password-encryption-in-cookie-auth-doesn-t-work">1.37 I run phpMyAdmin on cluster of different machines and password encryption in cookie auth doesn’t work.</a></li>
<li><a class="reference internal" href="#can-i-use-phpmyadmin-on-a-server-on-which-suhosin-is-enabled">1.38 Can I use phpMyAdmin on a server on which Suhosin is enabled?</a></li>
<li><a class="reference internal" href="#when-i-try-to-connect-via-https-i-can-log-in-but-then-my-connection-is-redirected-back-to-http-what-can-cause-this-behavior">1.39 When I try to connect via https, I can log in, but then my connection is redirected back to http. What can cause this behavior?</a></li>
<li><a class="reference internal" href="#when-accessing-phpmyadmin-via-an-apache-reverse-proxy-cookie-login-does-not-work">1.40 When accessing phpMyAdmin via an Apache reverse proxy, cookie login does not work.</a></li>
<li><a class="reference internal" href="#when-i-view-a-database-and-ask-to-see-its-privileges-i-get-an-error-about-an-unknown-column">1.41 When I view a database and ask to see its privileges, I get an error about an unknown column.</a></li>
<li><a class="reference internal" href="#how-can-i-prevent-robots-from-accessing-phpmyadmin">1.42 How can I prevent robots from accessing phpMyAdmin?</a></li>
<li><a class="reference internal" href="#why-can-t-i-display-the-structure-of-my-table-containing-hundreds-of-columns">1.43 Why can’t I display the structure of my table containing hundreds of columns?</a></li>
<li><a class="reference internal" href="#how-can-i-reduce-the-installed-size-of-phpmyadmin-on-disk">1.44 How can I reduce the installed size of phpMyAdmin on disk?</a></li>
<li><a class="reference internal" href="#i-get-an-error-message-about-unknown-authentication-method-caching-sha2-password-when-trying-to-log-in">1.45 I get an error message about unknown authentication method caching_sha2_password when trying to log in</a></li>
</ul>
</li>
<li><a class="reference internal" href="#configuration">Configuration</a><ul>
<li><a class="reference internal" href="#the-error-message-warning-cannot-add-header-information-headers-already-sent-by-is-displayed-what-s-the-problem">2.1 The error message “Warning: Cannot add header information - headers already sent by …” is displayed, what’s the problem?</a></li>
<li><a class="reference internal" href="#phpmyadmin-can-t-connect-to-mysql-what-s-wrong">2.2 phpMyAdmin can’t connect to MySQL. What’s wrong?</a></li>
<li><a class="reference internal" href="#the-error-message-warning-mysql-connection-failed-can-t-connect-to-local-mysql-server-through-socket-tmp-mysql-sock-111-is-displayed-what-can-i-do">2.3 The error message “Warning: MySQL Connection Failed: Can’t connect to local MySQL server through socket ‘/tmp/mysql.sock’ (111) …” is displayed. What can I do?</a></li>
<li><a class="reference internal" href="#nothing-is-displayed-by-my-browser-when-i-try-to-run-phpmyadmin-what-can-i-do">2.4 Nothing is displayed by my browser when I try to run phpMyAdmin, what can I do?</a></li>
<li><a class="reference internal" href="#each-time-i-want-to-insert-or-change-a-row-or-drop-a-database-or-a-table-an-error-404-page-not-found-is-displayed-or-with-http-or-cookie-authentication-i-m-asked-to-log-in-again-what-s-wrong">2.5 Each time I want to insert or change a row or drop a database or a table, an error 404 (page not found) is displayed or, with HTTP or cookie authentication, I’m asked to log in again. What’s wrong?</a></li>
<li><a class="reference internal" href="#i-get-an-access-denied-for-user-root-localhost-using-password-yes-error-when-trying-to-access-a-mysql-server-on-a-host-which-is-port-forwarded-for-my-localhost">2.6 I get an “Access denied for user: ‘root&#64;localhost’ (Using password: YES)”-error when trying to access a MySQL-Server on a host which is port-forwarded for my localhost.</a></li>
<li><a class="reference internal" href="#using-and-creating-themes">2.7 Using and creating themes</a></li>
<li><a class="reference internal" href="#i-get-missing-parameters-errors-what-can-i-do">2.8 I get “Missing parameters” errors, what can I do?</a></li>
<li><a class="reference internal" href="#seeing-an-upload-progress-bar">2.9 Seeing an upload progress bar</a></li>
<li><a class="reference internal" href="#how-to-generate-a-string-of-random-bytes">2.10 How to generate a string of random bytes</a></li>
</ul>
</li>
<li><a class="reference internal" href="#known-limitations">Known limitations</a><ul>
<li><a class="reference internal" href="#when-using-http-authentication-a-user-who-logged-out-can-not-log-in-again-in-with-the-same-nick">3.1 When using HTTP authentication, a user who logged out can not log in again in with the same nick.</a></li>
<li><a class="reference internal" href="#when-dumping-a-large-table-in-compressed-mode-i-get-a-memory-limit-error-or-a-time-limit-error">3.2 When dumping a large table in compressed mode, I get a memory limit error or a time limit error.</a></li>
<li><a class="reference internal" href="#with-innodb-tables-i-lose-foreign-key-relationships-when-i-rename-a-table-or-a-column">3.3 With InnoDB tables, I lose foreign key relationships when I rename a table or a column.</a></li>
<li><a class="reference internal" href="#i-am-unable-to-import-dumps-i-created-with-the-mysqldump-tool-bundled-with-the-mysql-server-distribution">3.4 I am unable to import dumps I created with the mysqldump tool bundled with the MySQL server distribution.</a></li>
<li><a class="reference internal" href="#when-using-nested-folders-multiple-hierarchies-are-displayed-in-a-wrong-manner">3.5 When using nested folders, multiple hierarchies are displayed in a wrong manner.</a></li>
<li><a class="reference internal" href="#faq3-6">3.6 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-have-table-with-many-100-columns-and-when-i-try-to-browse-table-i-get-series-of-errors-like-warning-unable-to-parse-url-how-can-this-be-fixed">3.7 I have table with many (100+) columns and when I try to browse table I get series of errors like “Warning: unable to parse url”. How can this be fixed?</a></li>
<li><a class="reference internal" href="#i-cannot-use-clickable-html-forms-in-columns-where-i-put-a-mime-transformation-onto">3.8 I cannot use (clickable) HTML-forms in columns where I put a MIME-Transformation onto!</a></li>
<li><a class="reference internal" href="#i-get-error-messages-when-using-sql-mode-ansi-for-the-mysql-server">3.9 I get error messages when using “–sql_mode=ANSI” for the MySQL server.</a></li>
<li><a class="reference internal" href="#homonyms-and-no-primary-key-when-the-results-of-a-select-display-more-that-one-column-with-the-same-value-for-example-select-lastname-from-employees-where-firstname-like-a-and-two-smith-values-are-displayed-if-i-click-edit-i-cannot-be-sure-that-i-am-editing-the-intended-row">3.10 Homonyms and no primary key: When the results of a SELECT display more that one column with the same value (for example <code class="docutils literal notranslate"><span class="pre">SELECT</span> <span class="pre">lastname</span> <span class="pre">from</span> <span class="pre">employees</span> <span class="pre">where</span> <span class="pre">firstname</span> <span class="pre">like</span> <span class="pre">'A%'</span></code> and two “Smith” values are displayed), if I click Edit I cannot be sure that I am editing the intended row.</a></li>
<li><a class="reference internal" href="#the-number-of-rows-for-innodb-tables-is-not-correct">3.11 The number of rows for InnoDB tables is not correct.</a></li>
<li><a class="reference internal" href="#faq3-12">3.12 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-get-an-error-when-entering-use-followed-by-a-db-name-containing-an-hyphen">3.13 I get an error when entering <code class="docutils literal notranslate"><span class="pre">USE</span></code> followed by a db name containing an hyphen.</a></li>
<li><a class="reference internal" href="#i-am-not-able-to-browse-a-table-when-i-don-t-have-the-right-to-select-one-of-the-columns">3.14 I am not able to browse a table when I don’t have the right to SELECT one of the columns.</a></li>
<li><a class="reference internal" href="#faq3-15">3.15 (withdrawn).</a></li>
<li><a class="reference internal" href="#faq3-16">3.16 (withdrawn).</a></li>
<li><a class="reference internal" href="#faq3-17">3.17 (withdrawn).</a></li>
<li><a class="reference internal" href="#when-i-import-a-csv-file-that-contains-multiple-tables-they-are-lumped-together-into-a-single-table">3.18 When I import a CSV file that contains multiple tables, they are lumped together into a single table.</a></li>
<li><a class="reference internal" href="#when-i-import-a-file-and-have-phpmyadmin-determine-the-appropriate-data-structure-it-only-uses-int-decimal-and-varchar-types">3.19 When I import a file and have phpMyAdmin determine the appropriate data structure it only uses int, decimal, and varchar types.</a></li>
<li><a class="reference internal" href="#after-upgrading-some-bookmarks-are-gone-or-their-content-cannot-be-shown">3.20 After upgrading, some bookmarks are gone or their content cannot be shown.</a></li>
<li><a class="reference internal" href="#i-am-unable-to-log-in-with-a-username-containing-unicode-characters-such-as-a">3.21 I am unable to log in with a username containing unicode characters such as á.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#isps-multi-user-installations">ISPs, multi-user installations</a><ul>
<li><a class="reference internal" href="#i-m-an-isp-can-i-setup-one-central-copy-of-phpmyadmin-or-do-i-need-to-install-it-for-each-customer">4.1 I’m an ISP. Can I setup one central copy of phpMyAdmin or do I need to install it for each customer?</a></li>
<li><a class="reference internal" href="#what-s-the-preferred-way-of-making-phpmyadmin-secure-against-evil-access">4.2 What’s the preferred way of making phpMyAdmin secure against evil access?</a></li>
<li><a class="reference internal" href="#i-get-errors-about-not-being-able-to-include-a-file-in-lang-or-in-libraries">4.3 I get errors about not being able to include a file in <em>/lang</em> or in <em>/libraries</em>.</a></li>
<li><a class="reference internal" href="#phpmyadmin-always-gives-access-denied-when-using-http-authentication">4.4 phpMyAdmin always gives “Access denied” when using HTTP authentication.</a></li>
<li><a class="reference internal" href="#is-it-possible-to-let-users-create-their-own-databases">4.5 Is it possible to let users create their own databases?</a></li>
<li><a class="reference internal" href="#how-can-i-use-the-host-based-authentication-additions">4.6 How can I use the Host-based authentication additions?</a></li>
<li><a class="reference internal" href="#authentication-window-is-displayed-more-than-once-why">4.7 Authentication window is displayed more than once, why?</a></li>
<li><a class="reference internal" href="#which-parameters-can-i-use-in-the-url-that-starts-phpmyadmin">4.8 Which parameters can I use in the URL that starts phpMyAdmin?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#browsers-or-client-os">Browsers or client OS</a><ul>
<li><a class="reference internal" href="#i-get-an-out-of-memory-error-and-my-controls-are-non-functional-when-trying-to-create-a-table-with-more-than-14-columns">5.1 I get an out of memory error, and my controls are non-functional, when trying to create a table with more than 14 columns.</a></li>
<li><a class="reference internal" href="#with-xitami-2-5b4-phpmyadmin-won-t-process-form-fields">5.2 With Xitami 2.5b4, phpMyAdmin won’t process form fields.</a></li>
<li><a class="reference internal" href="#i-have-problems-dumping-tables-with-konqueror-phpmyadmin-2-2-2">5.3 I have problems dumping tables with Konqueror (phpMyAdmin 2.2.2).</a></li>
<li><a class="reference internal" href="#i-can-t-use-the-cookie-authentication-mode-because-internet-explorer-never-stores-the-cookies">5.4 I can’t use the cookie authentication mode because Internet Explorer never stores the cookies.</a></li>
<li><a class="reference internal" href="#faq5-5">5.5 (withdrawn).</a></li>
<li><a class="reference internal" href="#faq5-6">5.6 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-refresh-reload-my-browser-and-come-back-to-the-welcome-page">5.7 I refresh (reload) my browser, and come back to the welcome page.</a></li>
<li><a class="reference internal" href="#with-mozilla-0-9-7-i-have-problems-sending-a-query-modified-in-the-query-box">5.8 With Mozilla 0.9.7 I have problems sending a query modified in the query box.</a></li>
<li><a class="reference internal" href="#with-mozilla-0-9-to-1-0-and-netscape-7-0-pr1-i-can-t-type-a-whitespace-in-the-sql-query-edit-area-the-page-scrolls-down">5.9 With Mozilla 0.9.? to 1.0 and Netscape 7.0-PR1 I can’t type a whitespace in the SQL-Query edit area: the page scrolls down.</a></li>
<li><a class="reference internal" href="#faq5-10">5.10 (withdrawn).</a></li>
<li><a class="reference internal" href="#extended-ascii-characters-like-german-umlauts-are-displayed-wrong">5.11 Extended-ASCII characters like German umlauts are displayed wrong.</a></li>
<li><a class="reference internal" href="#mac-os-x-safari-browser-changes-special-characters-to">5.12 Mac OS X Safari browser changes special characters to “?”.</a></li>
<li><a class="reference internal" href="#faq5-13">5.13 (withdrawn)</a></li>
<li><a class="reference internal" href="#faq5-14">5.14 (withdrawn)</a></li>
<li><a class="reference internal" href="#faq5-15">5.15 (withdrawn)</a></li>
<li><a class="reference internal" href="#with-internet-explorer-i-get-access-is-denied-javascript-errors-or-i-cannot-make-phpmyadmin-work-under-windows">5.16 With Internet Explorer, I get “Access is denied” Javascript errors. Or I cannot make phpMyAdmin work under Windows.</a></li>
<li><a class="reference internal" href="#with-firefox-i-cannot-delete-rows-of-data-or-drop-a-database">5.17 With Firefox, I cannot delete rows of data or drop a database.</a></li>
<li><a class="reference internal" href="#faq5-18">5.18 (withdrawn)</a></li>
<li><a class="reference internal" href="#i-get-javascript-errors-in-my-browser">5.19 I get JavaScript errors in my browser.</a></li>
<li><a class="reference internal" href="#i-get-errors-about-violating-content-security-policy">5.20 I get errors about violating Content Security Policy.</a></li>
<li><a class="reference internal" href="#i-get-errors-about-potentially-unsafe-operation-when-browsing-table-or-executing-sql-query">5.21 I get errors about potentially unsafe operation when browsing table or executing SQL query.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#using-phpmyadmin">Using phpMyAdmin</a><ul>
<li><a class="reference internal" href="#i-can-t-insert-new-rows-into-a-table-i-can-t-create-a-table-mysql-brings-up-a-sql-error">6.1 I can’t insert new rows into a table / I can’t create a table - MySQL brings up a SQL error.</a></li>
<li><a class="reference internal" href="#when-i-create-a-table-i-set-an-index-for-two-columns-and-phpmyadmin-generates-only-one-index-with-those-two-columns">6.2 When I create a table, I set an index for two columns and phpMyAdmin generates only one index with those two columns.</a></li>
<li><a class="reference internal" href="#how-can-i-insert-a-null-value-into-my-table">6.3 How can I insert a null value into my table?</a></li>
<li><a class="reference internal" href="#how-can-i-backup-my-database-or-table">6.4 How can I backup my database or table?</a></li>
<li><a class="reference internal" href="#how-can-i-restore-upload-my-database-or-table-using-a-dump-how-can-i-run-a-sql-file">6.5 How can I restore (upload) my database or table using a dump? How can I run a “.sql” file?</a></li>
<li><a class="reference internal" href="#how-can-i-use-the-relation-table-in-query-by-example">6.6 How can I use the relation table in Query-by-example?</a></li>
<li><a class="reference internal" href="#how-can-i-use-the-display-column-feature">6.7 How can I use the “display column” feature?</a></li>
<li><a class="reference internal" href="#how-can-i-produce-a-pdf-schema-of-my-database">6.8 How can I produce a PDF schema of my database?</a></li>
<li><a class="reference internal" href="#phpmyadmin-is-changing-the-type-of-one-of-my-columns">6.9 phpMyAdmin is changing the type of one of my columns!</a></li>
<li><a class="reference internal" href="#when-creating-a-privilege-what-happens-with-underscores-in-the-database-name">6.10 When creating a privilege, what happens with underscores in the database name?</a></li>
<li><a class="reference internal" href="#what-is-the-curious-symbol-o-in-the-statistics-pages">6.11 What is the curious symbol ø in the statistics pages?</a></li>
<li><a class="reference internal" href="#i-want-to-understand-some-export-options">6.12 I want to understand some Export options.</a></li>
<li><a class="reference internal" href="#i-would-like-to-create-a-database-with-a-dot-in-its-name">6.13 I would like to create a database with a dot in its name.</a></li>
<li><a class="reference internal" href="#faqsqlvalidator">6.14 (withdrawn).</a></li>
<li><a class="reference internal" href="#i-want-to-add-a-blob-column-and-put-an-index-on-it-but-mysql-says-blob-column-used-in-key-specification-without-a-key-length">6.15 I want to add a BLOB column and put an index on it, but MySQL says “BLOB column ‘…’ used in key specification without a key length”.</a></li>
<li><a class="reference internal" href="#how-can-i-simply-move-in-page-with-plenty-editing-fields">6.16 How can I simply move in page with plenty editing fields?</a></li>
<li><a class="reference internal" href="#transformations-i-can-t-enter-my-own-mimetype-what-is-this-feature-then-useful-for">6.17 Transformations: I can’t enter my own mimetype! What is this feature then useful for?</a></li>
<li><a class="reference internal" href="#bookmarks-where-can-i-store-bookmarks-why-can-t-i-see-any-bookmarks-below-the-query-box-what-are-these-variables-for">6.18 Bookmarks: Where can I store bookmarks? Why can’t I see any bookmarks below the query box? What are these variables for?</a></li>
<li><a class="reference internal" href="#how-can-i-create-simple-latex-document-to-include-exported-table">6.19 How can I create simple LATEX document to include exported table?</a></li>
<li><a class="reference internal" href="#i-see-a-lot-of-databases-which-are-not-mine-and-cannot-access-them">6.20 I see a lot of databases which are not mine, and cannot access them.</a></li>
<li><a class="reference internal" href="#in-edit-insert-mode-how-can-i-see-a-list-of-possible-values-for-a-column-based-on-some-foreign-table">6.21 In edit/insert mode, how can I see a list of possible values for a column, based on some foreign table?</a></li>
<li><a class="reference internal" href="#bookmarks-can-i-execute-a-default-bookmark-automatically-when-entering-browse-mode-for-a-table">6.22 Bookmarks: Can I execute a default bookmark automatically when entering Browse mode for a table?</a></li>
<li><a class="reference internal" href="#export-i-heard-phpmyadmin-can-export-microsoft-excel-files">6.23 Export: I heard phpMyAdmin can export Microsoft Excel files?</a></li>
<li><a class="reference internal" href="#now-that-phpmyadmin-supports-native-mysql-4-1-x-column-comments-what-happens-to-my-column-comments-stored-in-pmadb">6.24 Now that phpMyAdmin supports native MySQL 4.1.x column comments, what happens to my column comments stored in pmadb?</a></li>
<li><a class="reference internal" href="#faq6-25">6.25 (withdrawn).</a></li>
<li><a class="reference internal" href="#how-can-i-select-a-range-of-rows">6.26 How can I select a range of rows?</a></li>
<li><a class="reference internal" href="#what-format-strings-can-i-use">6.27 What format strings can I use?</a></li>
<li><a class="reference internal" href="#faq6-28">6.28 (withdrawn).</a></li>
<li><a class="reference internal" href="#why-can-t-i-get-a-chart-from-my-query-result-table">6.29 Why can’t I get a chart from my query result table?</a></li>
<li><a class="reference internal" href="#import-how-can-i-import-esri-shapefiles">6.30 Import: How can I import ESRI Shapefiles?</a></li>
<li><a class="reference internal" href="#how-do-i-create-a-relation-in-designer">6.31 How do I create a relation in designer?</a></li>
<li><a class="reference internal" href="#how-can-i-use-the-zoom-search-feature">6.32 How can I use the zoom search feature?</a></li>
<li><a class="reference internal" href="#when-browsing-a-table-how-can-i-copy-a-column-name">6.33 When browsing a table, how can I copy a column name?</a></li>
<li><a class="reference internal" href="#how-can-i-use-the-favorite-tables-feature">6.34 How can I use the Favorite Tables feature?</a></li>
<li><a class="reference internal" href="#how-can-i-use-the-range-search-feature">6.35 How can I use the Range search feature?</a></li>
<li><a class="reference internal" href="#what-is-central-columns-and-how-can-i-use-this-feature">6.36 What is Central columns and how can I use this feature?</a></li>
<li><a class="reference internal" href="#how-can-i-use-improve-table-structure-feature">6.37 How can I use Improve Table structure feature?</a></li>
<li><a class="reference internal" href="#how-can-i-reassign-auto-incremented-values">6.38 How can I reassign auto-incremented values?</a></li>
<li><a class="reference internal" href="#what-is-the-adjust-privileges-option-when-renaming-copying-or-moving-a-database-table-column-or-procedure">6.39 What is the “Adjust privileges” option when renaming, copying, or moving a database, table, column, or procedure?</a></li>
<li><a class="reference internal" href="#i-see-bind-parameters-checkbox-in-the-sql-page-how-do-i-write-parameterized-sql-queries">6.40 I see “Bind parameters” checkbox in the “SQL” page. How do I write parameterized SQL queries?</a></li>
<li><a class="reference internal" href="#i-get-import-errors-while-importing-the-dumps-exported-from-older-mysql-versions-pre-5-7-6-into-newer-mysql-versions-5-7-7-but-they-work-fine-when-imported-back-on-same-older-versions">6.41 I get import errors while importing the dumps exported from older MySQL versions (pre-5.7.6) into newer MySQL versions (5.7.7+), but they work fine when imported back on same older versions ?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#phpmyadmin-project">phpMyAdmin project</a><ul>
<li><a class="reference internal" href="#i-have-found-a-bug-how-do-i-inform-developers">7.1 I have found a bug. How do I inform developers?</a></li>
<li><a class="reference internal" href="#i-want-to-translate-the-messages-to-a-new-language-or-upgrade-an-existing-language-where-do-i-start">7.2 I want to translate the messages to a new language or upgrade an existing language, where do I start?</a></li>
<li><a class="reference internal" href="#i-would-like-to-help-out-with-the-development-of-phpmyadmin-how-should-i-proceed">7.3 I would like to help out with the development of phpMyAdmin. How should I proceed?</a></li>
</ul>
</li>
<li><a class="reference internal" href="#security">Security</a><ul>
<li><a class="reference internal" href="#where-can-i-get-information-about-the-security-alerts-issued-for-phpmyadmin">8.1 Where can I get information about the security alerts issued for phpMyAdmin?</a></li>
<li><a class="reference internal" href="#how-can-i-protect-phpmyadmin-against-brute-force-attacks">8.2 How can I protect phpMyAdmin against brute force attacks?</a></li>
<li><a class="reference internal" href="#why-are-there-path-disclosures-when-directly-loading-certain-files">8.3 Why are there path disclosures when directly loading certain files?</a></li>
<li><a class="reference internal" href="#csv-files-exported-from-phpmyadmin-could-allow-a-formula-injection-attack">8.4 CSV files exported from phpMyAdmin could allow a formula injection attack.</a></li>
</ul>
</li>
<li><a class="reference internal" href="#synchronization">Synchronization</a><ul>
<li><a class="reference internal" href="#faq9-1">9.1 (withdrawn).</a></li>
<li><a class="reference internal" href="#faq9-2">9.2 (withdrawn).</a></li>
</ul>
</li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="other.html"
                        title="previous chapter">Other sources of information</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="developers.html"
                        title="next chapter">Developers Information</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/faq.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="developers.html" title="Developers Information"
             >next</a> |</li>
        <li class="right" >
          <a href="other.html" title="Other sources of information"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">FAQ - Frequently Asked Questions</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>