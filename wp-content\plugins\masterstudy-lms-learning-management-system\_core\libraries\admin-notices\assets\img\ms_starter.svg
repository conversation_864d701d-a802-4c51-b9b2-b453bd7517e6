<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="512" height="512.001" viewBox="0 0 512 512.001">
  <defs>
    <linearGradient id="linear-gradient" x1="0.857" y1="0.1" x2="0.11" y2="0.881" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#57b6fc"/>
      <stop offset="1" stop-color="#174dff"/>
    </linearGradient>
    <filter id="Union_6">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="35" result="blur"/>
      <feFlood flood-color="#fff" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_8834" data-name="Group 8834" transform="translate(-568 -1923)">
    <g id="Group_119" data-name="Group 119" transform="translate(568 1923)">
      <g id="Group_130" data-name="Group 130" transform="translate(0 0)">
        <g data-type="innerShadowGroup">
          <path id="Union_6-2" data-name="Union 6" d="M60.121,451.881C24.6,417.316,0,342.6,0,256S24.6,94.685,60.12,60.12C94.685,24.6,169.4,0,256,0S417.316,24.6,451.881,60.121C487.405,94.686,512,169.4,512,256s-24.6,161.315-60.12,195.881C417.315,487.4,342.6,512,256,512S94.686,487.4,60.121,451.881Z" transform="translate(0 0)" fill="url(#linear-gradient)"/>
          <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Union_6)">
            <path id="Union_6-3" data-name="Union 6" d="M60.121,451.881C24.6,417.316,0,342.6,0,256S24.6,94.685,60.12,60.12C94.685,24.6,169.4,0,256,0S417.316,24.6,451.881,60.121C487.405,94.686,512,169.4,512,256s-24.6,161.315-60.12,195.881C417.315,487.4,342.6,512,256,512S94.686,487.4,60.121,451.881Z" transform="translate(0 0)" fill="#fff"/>
          </g>
        </g>
      </g>
      <g id="Group_8831" data-name="Group 8831" transform="translate(110.933 136.534)">
        <path id="Op_component_2" data-name="Op component 2" d="M553.026,210.352H503.678c-1.7,0-2.566-.769-2.566-2.311V198.21c0-1.542.863-2.319,2.566-2.319h14.315V99.909h-.209l-40.6,90.979q-2.554,5.784-9.394,5.78t-9.4-5.78L417.8,99.909h-.425v95.982h14.309c1.851,0,2.781.777,2.781,2.319v9.831c0,1.542-.93,2.311-2.781,2.311H382.558q-2.778,0-2.776-2.311V198.21c0-1.542.924-2.319,2.776-2.319H398.8V86.416H382.558q-2.778,0-2.776-2.311V74.287c0-1.553.924-2.315,2.776-2.315h32.9c4.84,0,8.187,2.172,10.038,6.555l31.827,69.381,10.039,24.286h.86q5.983-15.042,9.829-23.907l32.036-69.76c1.854-4.382,5.272-6.555,10.25-6.555h32.689c1.715,0,2.566.762,2.566,2.315V84.1c0,1.542-.851,2.311-2.566,2.311H536.577V195.892h16.449c1.715,0,2.566.777,2.566,2.319v9.831c0,1.542-.851,2.311-2.566,2.311" transform="translate(-379.782 -71.971)" fill="#fff"/>
        <path id="Op_component_3" data-name="Op component 3" d="M410.52,220.145H398.557c-1.852,0-2.777-.777-2.777-2.314v-45.1c0-1.662.924-2.508,2.777-2.508h13.455a2.247,2.247,0,0,1,2.566,2.508v4.042q0,3.658,4.916,9.73a62.028,62.028,0,0,0,9.821,9.931,44.25,44.25,0,0,0,13.142,6.65,55,55,0,0,0,17.832,2.794q16.458,0,25.745-6.167a19.485,19.485,0,0,0,9.289-17.158,13.914,13.914,0,0,0-5.231-10.881,38.546,38.546,0,0,0-13.563-7.23q-8.337-2.7-18.484-5.015t-20.285-5.294A114.7,114.7,0,0,1,419.274,147a36.11,36.11,0,0,1-13.565-11.562,29.568,29.568,0,0,1-5.229-17.451q0-16.2,13.564-26.794t36.418-10.6q28.836,0,42.3,15.217l-.424-11.947c0-1.542.924-2.3,2.777-2.3h11.96c1.7,0,2.564.758,2.564,2.3V125.89c0,1.549-.86,2.326-2.564,2.326H493.611q-2.561,0-2.558-2.326v-5.4q0-7.9-9.827-16T452.386,96.4q-14.1,0-22.752,6.066t-8.648,15.522a15.02,15.02,0,0,0,5.342,11.562,39.271,39.271,0,0,0,13.883,7.712q8.54,2.888,18.794,5.306,10.26,2.4,20.507,5.294a118.508,118.508,0,0,1,18.805,6.944A36.854,36.854,0,0,1,512.2,165.885a26.8,26.8,0,0,1,5.34,16.665q0,16.966-13.463,27.954T463.925,221.49q-19.656,0-32.469-7.142-12.816-7.112-18.583-15.406l.209,18.888c0,1.538-.851,2.314-2.562,2.314" transform="translate(-227.408 10.226)" fill="#fff"/>
      </g>
    </g>
  </g>
</svg>
