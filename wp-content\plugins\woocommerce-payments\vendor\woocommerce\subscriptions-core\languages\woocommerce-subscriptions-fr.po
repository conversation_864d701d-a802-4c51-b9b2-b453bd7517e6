# Translation of WooCommerce - WooCommerce Subscriptions in French (France)
# This file is distributed under the same license as the WooCommerce - WooCommerce Subscriptions package.
msgid ""
msgstr ""
"PO-Revision-Date: 2020-11-06 17:52:37+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n > 1;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: fr\n"
"Project-Id-Version: WooCommerce - WooCommerce Subscriptions\n"

#: includes/class-wc-subscriptions-order.php:517
msgid "Payment completed on order after subscription was cancelled."
msgstr ""

#. Translators: Placeholder is the subscription ID number.
#: includes/class-wc-subscriptions-manager.php:917
msgid ""
"The related subscription #%s has been deleted after the customer was deleted."
msgstr ""

#. Translators: 1: The subscription ID number. 2: The current user's username.
#: includes/class-wc-subscriptions-manager.php:914
msgid ""
"The related subscription #%1$s has been deleted after the customer was "
"deleted by %2$s."
msgstr ""

#: templates/checkout/recurring-totals.php:19
msgid "Recurring totals"
msgstr ""

#. Translators: Placeholders are opening and closing strong and link tags.
#: includes/class-wc-subscriptions-checkout.php:569
msgid ""
"Purchasing a subscription product requires an account. Please go to the %sMy "
"Account%s page to login or contact us if you need assistance."
msgstr ""

#. Translators: Placeholders are opening and closing strong and link tags.
#: includes/class-wc-subscriptions-checkout.php:566
msgid ""
"Purchasing a subscription product requires an account. Please go to the %sMy "
"Account%s page to login or register."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:384
msgid "Lock manual price increases"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:380
msgid ""
"This order contains line items with prices above the current product price. "
"To override the product's live price when the customer pays for this order, "
"lock in the manual price increases."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:124
msgid "Please enter a date at least 2 minutes into the future."
msgstr ""

#: templates/myaccount/subscription-details.php:62
msgid "Using the auto-renewal toggle is disabled while in staging mode."
msgstr ""
"L’utilisation de la bascule de renouvellement automatique est désactivée en "
"mode préproduction."

#: templates/myaccount/subscription-details.php:24
msgctxt "customer subscription table header"
msgid "Start date"
msgstr "Date de début"

#: templates/myaccount/related-orders.php:15
msgid "Related orders"
msgstr "Commandes similaires"

#: templates/myaccount/my-subscriptions.php:77
msgid "Browse products"
msgstr "Parcourir les produits"

#: templates/myaccount/my-subscriptions.php:74
msgid "You have no active subscriptions."
msgstr "Vous n’avez pas d’abonnement actif."

#: templates/html-early-renewal-modal-content.php:34
msgid "Want to renew early via the checkout? Click %shere.%s"
msgstr ""
"Vous voulez renouveler tôt via la validation de commande ? Cliquez %sici.%s"

#: templates/html-early-renewal-modal-content.php:28
msgid ""
"By renewing your subscription early, your scheduled next payment on %s will "
"be cancelled."
msgstr ""
"En renouvelant votre abonnement tôt, votre prochain paiement planifié le %s "
"sera annulé."

#: templates/html-early-renewal-modal-content.php:23
msgid "By renewing your subscription early your next payment will be %s."
msgstr "En renouvelant votre abonnement tôt, votre prochain paiement sera %s."

#. Translators: Placeholders are opening and closing My Account link tags.
#: templates/emails/subscription-info.php:57
msgid ""
"This subscription is set to renew automatically using your payment method on "
"file. You can manage or cancel this subscription from your %smy account "
"page%s."
msgid_plural ""
"These subscriptions are set to renew automatically using your payment method "
"on file. You can manage or cancel your subscriptions from your %smy account "
"page%s."
msgstr[0] ""
"Cet abonnement est configuré pour se renouveler automatiquement en utilisant "
"votre moyen de paiement enregistré. Vous pouvez gérer ou annuler cet "
"abonnement sur votre %spage Mon compte%s."
msgstr[1] ""
"Ces abonnements sont configurés pour se renouveler automatiquement en "
"utilisant votre moyen de paiement enregistré. Vous pouvez gérer ou annuler "
"vos abonnements sur votre %spage Mon compte%s."

#: templates/emails/subscription-info.php:37
msgctxt "Used as end date for an indefinite subscription"
msgid "When cancelled"
msgstr "En cas d’annulation"

#: templates/emails/subscription-info.php:28
msgctxt "table heading"
msgid "Recurring total"
msgstr "Total récurrent"

#: templates/emails/subscription-info.php:27
msgctxt "table heading"
msgid "End date"
msgstr "Date de fin"

#: templates/emails/subscription-info.php:26
msgctxt "table heading"
msgid "Start date"
msgstr "Date de début"

#: templates/emails/subscription-info.php:25
msgctxt "subscription ID table heading"
msgid "ID"
msgstr "ID"

#. Translators: Placeholder is the My Account URL.
#: templates/emails/plain/subscription-info.php:51
msgid ""
"This subscription is set to renew automatically using your payment method on "
"file. You can manage or cancel this subscription from your my account page. "
"%s"
msgid_plural ""
"These subscriptions are set to renew automatically using your payment method "
"on file. You can manage or cancel your subscriptions from your my account "
"page. %s"
msgstr[0] ""
"Cet abonnement est configuré pour se renouveler automatiquement en utilisant "
"votre moyen de paiement enregistré. Vous pouvez gérer ou annuler cet "
"abonnement sur votre page Mon compte. %s"
msgstr[1] ""
"Ces abonnements sont configurés pour se renouveler automatiquement en "
"utilisant votre moyen de paiement enregistré. Vous pouvez gérer ou annuler "
"vos abonnements sur votre page Mon compte. %s"

#: templates/emails/plain/subscription-info.php:38
#: templates/emails/subscription-info.php:42
msgid "Next payment: %s"
msgstr "Paiement suivant : %s"

#. translators: placeholder is either view or edit url for the subscription
#: templates/emails/plain/subscription-info.php:27
msgctxt "in plain emails for subscription information"
msgid "View subscription: %s"
msgstr "Afficher l’abonnement : %s"

#: templates/emails/email-order-details.php:61
msgid "Note:"
msgstr "Remarque :"

#. translators: %s: Order number
#: templates/emails/customer-processing-renewal-order.php:19
#: templates/emails/plain/customer-processing-renewal-order.php:18
msgid ""
"Just to let you know &mdash; we've received your subscription renewal order "
"#%s, and it is now being processed:"
msgstr ""
"Pour information – nous avons reçu votre commande de renouvellement "
"d’abonnement n° %s, elle est maintenant en cours de traitement :"

#: templates/emails/customer-on-hold-renewal-order.php:18
#: templates/emails/plain/customer-on-hold-renewal-order.php:17
msgid ""
"Thanks for your renewal order. It’s on-hold until we confirm that payment "
"has been received. In the meantime, here’s a reminder of your order:"
msgstr ""
"Merci pour votre commande de renouvellement. Elle est en attente jusqu’à ce "
"que nous confirmions que le paiement ait bien été reçu. En attendant, voici "
"un rappel de votre commande :"

#: templates/emails/customer-completed-renewal-order.php:18
#: templates/emails/plain/customer-completed-renewal-order.php:17
msgid "We have finished processing your subscription renewal order."
msgstr ""
"Nous avons terminé de traiter votre commande de renouvellement d’abonnement."

#. translators: %s: Customer first name
#: templates/emails/customer-completed-renewal-order.php:17
#: templates/emails/customer-completed-switch-order.php:17
#: templates/emails/customer-on-hold-renewal-order.php:17
#: templates/emails/customer-payment-retry.php:16
#: templates/emails/customer-processing-renewal-order.php:17
#: templates/emails/customer-renewal-invoice.php:16
#: templates/emails/plain/customer-completed-renewal-order.php:16
#: templates/emails/plain/customer-completed-switch-order.php:16
#: templates/emails/plain/customer-on-hold-renewal-order.php:16
#: templates/emails/plain/customer-payment-retry.php:16
#: templates/emails/plain/customer-processing-renewal-order.php:16
#: templates/emails/plain/customer-renewal-invoice.php:16
msgid "Hi %s,"
msgstr "Salut %s,"

#. translators: 1) passed sort order type argument, 2) 'ascending', 3)
#. 'descending'.
#: includes/wcs-helper-functions.php:266
msgid ""
"Invalid sort order type: %1$s. The $sort_order argument must be %2$s or %3$s."
msgstr ""
"Type d’ordre de tri non valide : %1$s. L’argument $sort_order doit être %2$s "
"ou %3$s."

#. translators: placeholder is Subscription version string ('2.3')
#: includes/upgrades/templates/update-welcome-notice.php:16
msgid "Want to know more about Subscriptions %s?"
msgstr "Vous désirez en savoir plus sur Subscriptions %s ?"

#. translators: placeholder is Subscription version string ('2.3')
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:99
msgid "Welcome to WooCommerce Subscriptions %s!"
msgstr "Bienvenue dans WooCommerce Subscriptions %s !"

#. translators: 1-2: opening/closing <a> tags - link to documentation.
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:92
msgid ""
"Previous versions of Subscriptions relied on %1$sWP Cron%2$s to process "
"subscription payments and other scheduled events. In 3.0, these events will "
"now run on admin request with loopback support. This will significantly "
"increase the throughput of payment processing."
msgstr ""
"Les versions précédentes de Subscriptions reposaient sur %1$sWP Cron%2$s "
"pour traiter les paiements d’abonnements et d’autres événements planifiés. "
"Dans la version 3.0, ces événements s’exécuteront désormais à la demande de "
"l’administrateur avec prise en charge du bouclage. Cela augmentera "
"considérablement le débit du traitement des paiements."

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:89
msgid "Increased processing rate for scheduled payments"
msgstr "Augmentation du taux de traitement des paiements planifiés"

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:86
msgid ""
"Scheduled action data, which was previously stored in the WordPress post "
"tables, has been moved to a custom database table. Amongst other benefits, "
"this will greatly improve the performance of processing scheduled actions "
"such as subscription payments."
msgstr ""
"Les données des actions planifiées, qui étaient auparavant stockées dans les "
"tables d’articles WordPress, ont été déplacées dans une table de base de "
"données personnalisée. Entre autres avantages, cela améliorera "
"considérablement les performances du traitement des actions planifiées "
"telles que les paiements d’abonnement."

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:85
msgid "Improved scheduled action data storage"
msgstr "Stockage amélioré des données des actions planifiées"

#. translators: $1 and $2 are opening link tags, $3 is a closing link tag.
#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:29
msgid ""
"To resolve this as quickly as possible, please create a %1$stemporary "
"administrator account%3$s with the <NAME_EMAIL> and "
"share the credentials with us via %2$sQuickForget.com%3$s."
msgstr ""
"Pour résoudre ceci le plus rapidement possible, veuillez créer un %1$scompte "
"d’administrateur temporaire%3$s avec l’adresse e-mail d’utilisateur "
"<EMAIL> et partager les identifiants de connexion avec nous "
"via %2$sQuickForget.com%3$s."

#. translators: opening/closing tags - links to documentation.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:126
msgid ""
"%1$sPayPal Reference Transactions are not enabled on your account%2$s. If "
"you wish to use PayPal Reference Transactions with Subscriptions, please "
"contact PayPal and request they %3$senable PayPal Reference Transactions%4$s "
"on your account. %5$sCheck PayPal Account%6$s  %3$sLearn more %7$s"
msgstr ""
"%1$sPayPal Reference Transactions n’est pas activé sur votre compte%2$s. Si "
"vous souhaitez utiliser PayPal Reference Transactions avec Subscriptions, "
"veuillez contacter PayPal et leur demander d’%3$sactiver PayPal Reference "
"Transactions%4$s sur votre compte. %5$sVérifier le compte PayPal%6$s %3$sEn "
"savoir plus%7$s"

#. translators: 1-2: opening/closing tags - link to documentation.
#: includes/gateways/class-wc-subscriptions-payment-gateways.php:135
msgid ""
"Sorry, it seems there are no available payment methods which support "
"subscriptions. Please see %1$sEnabling Payment Gateways for "
"Subscriptions%2$s if you require assistance."
msgstr ""
"Désolé, il semble qu’aucun mode de paiement ne soit disponible pour les "
"abonnements. Veuillez consulter %1$sActiver des passerelles de paiement pour "
"Subscriptions%2$s si vous avez besoin d’aide."

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:26
msgid "Thank you for your renewal order"
msgstr "Merci pour votre commande de renouvellement"

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:25
msgid "Your {blogname} renewal order has been received!"
msgstr "Votre commande de renouvellement de {blogname} a été reçue !"

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:24
msgid ""
"This is an order notification sent to customers containing order details "
"after a renewal order is placed on-hold."
msgstr ""
"Ceci est une notification de commande envoyée aux clients après qu’une "
"commande de renouvellement est placée en attente et contenant les détails de "
"la commande."

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:23
msgid "On-hold Renewal Order"
msgstr "Commande de renouvellement en attente"

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:153
msgid "Your early renewal order was successful."
msgstr "Votre commande de renouvellement anticipé a réussi."

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:145
msgid ""
"Payment for the renewal order was unsuccessful with your payment method on "
"file, please try again."
msgstr ""
"Le paiement de la commande de renouvellement a échoué avec votre moyen de "
"paiement enregistré, veuillez réessayer."

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:128
msgid ""
"We couldn't create a renewal order for your subscription, please try again."
msgstr ""
"Nous n’avons pas pu créer de commande de renouvellement pour votre "
"abonnement, veuillez réessayer."

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:118
msgid "You can't renew the subscription at this time. Please try again."
msgstr ""
"Vous ne pouvez pas renouveler l’abonnement pour le moment. Veuillez "
"réessayer."

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:113
msgid "We were unable to locate that subscription, please try again."
msgstr "Nous n’avons pas pu localiser cet abonnement, veuillez réessayer."

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:56
msgid "Renew early"
msgstr "Renouveler tôt"

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:39
msgid "Pay now"
msgstr "Payer maintenant"

#. translators: 1-2: opening/closing <strong> tags , 2-3: opening/closing tags
#. for a link to docs on early renewal.
#: includes/early-renewal/class-wcs-early-renewal-manager.php:66
msgid ""
"Allow customers to bypass the checkout and renew their subscription early "
"from their %1$sMy Account > View Subscription%2$s page. %3$sLearn more.%4$s"
msgstr ""
"Autorisez les clients à contourner la validation de commande et à renouveler "
"leur abonnement tôt sur la page %1$sMon compte > Afficher l’abonnement%2$s. "
"%3$sEn savoir plus.%4$s"

#: includes/early-renewal/class-wcs-early-renewal-manager.php:63
msgid "Accept Early Renewal Payments via a Modal"
msgstr "Accepter les paiements de renouvellement anticipé via un modal"

#: includes/class-wcs-switch-totals-calculator.php:196
msgid ""
"Your cart contained an invalid subscription switch request. It has been "
"removed from your cart."
msgstr ""
"Votre panier contenait une demande de changement d’abonnement non valide. "
"Elle a été supprimée de votre panier."

#. translators: placeholder is a payment method title.
#: includes/class-wcs-staging.php:97
msgid ""
"Subscription locked to Manual Renewal while the store is in staging mode. "
"Live payment method: %s"
msgstr ""
"Abonnement verrouillé sur Renouvellement manuel pendant que la boutique est "
"en mode préproduction. Moyen de paiement en direct : %s"

#: includes/class-wcs-staging.php:83
msgid ""
"Subscription locked to Manual Renewal while the store is in staging mode. "
"Payment method changes will take effect in live mode."
msgstr ""
"Abonnement verrouillé sur Renouvellement manuel pendant que la boutique est "
"en mode préproduction. Les changements de moyen de paiement prendront effet "
"en mode en direct."

#. translators: %s is order's number
#: includes/class-wcs-cart-renewal.php:330
msgid "Order #%s has not been added to the cart."
msgstr "La commande n°%s n’a pas été ajoutée au panier."

#. translators: %s: new item name.
#: includes/class-wc-subscriptions-switcher.php:1988
msgctxt "used in order notes"
msgid "Customer added %s."
msgstr "Le client a ajouté %s."

#: includes/class-wc-subscriptions-manager.php:134
msgid "Manual renewal order awaiting customer payment."
msgstr "Commande de renouvellement manuel en attente de paiement client."

#. translators: %1$s is the coupon code, %2$d is the number of payment usages
#: includes/class-wc-subscriptions-coupon.php:1047
msgid ""
"Limited use coupon \"%1$s\" removed from subscription. It has been used %2$d "
"time."
msgid_plural ""
"Limited use coupon \"%1$s\" removed from subscription. It has been used %2$d "
"times."
msgstr[0] ""
"Le code de promotion à usage limité « %1$s » a été supprimé de l’abonnement. "
"Il a été utilisé %2$d fois."
msgstr[1] ""
"Le code de promotion à usage limité « %1$s » a été supprimé de l’abonnement. "
"Il a été utilisé %2$d fois."

#: includes/class-wc-subscriptions-cart-validator.php:111
msgid ""
"Your cart has been emptied of subscription products. Only one subscription "
"product can be purchased at a time."
msgstr ""
"Des produits d’abonnement ont été retirés de votre panier. Un seul produit "
"d’abonnement peut être acheté à la fois."

#: includes/class-wc-product-subscription.php:73
msgid "Read more"
msgstr "Lire plus"

#: includes/api/class-wc-rest-subscriptions-controller.php:691
msgid "Meta value."
msgstr "Valeur de la méta."

#: includes/api/class-wc-rest-subscriptions-controller.php:685
msgid "Meta label."
msgstr "Etiquette de la méta."

#: includes/api/class-wc-rest-subscriptions-controller.php:679
msgid "Meta key."
msgstr "Clé de la méta."

#: includes/api/class-wc-rest-subscriptions-controller.php:671
msgid "Removed line item meta data."
msgstr "Métadonnées de l'article de la ligne supprimées."

#: includes/api/class-wc-rest-subscriptions-controller.php:662
msgid "Tax subtotal."
msgstr "Sous-total de la TVA."

#: includes/api/class-wc-rest-subscriptions-controller.php:656
msgid "Tax total."
msgstr "Total de la TVA."

#: includes/api/class-wc-rest-subscriptions-controller.php:650
msgid "Tax rate ID."
msgstr "ID du taux de TVA."

#: includes/api/class-wc-rest-subscriptions-controller.php:642
msgid "Line taxes."
msgstr "Ligne de TVA."

#: includes/api/class-wc-rest-subscriptions-controller.php:637
msgid "Line total tax (after discounts)."
msgstr "Ligne de total de TVA (après remises)."

#: includes/api/class-wc-rest-subscriptions-controller.php:632
msgid "Line total (after discounts)."
msgstr "Ligne de total (après remises)."

#: includes/api/class-wc-rest-subscriptions-controller.php:627
msgid "Line subtotal tax (before discounts)."
msgstr "Ligne de sous-total de TVA (avant remises)."

#: includes/api/class-wc-rest-subscriptions-controller.php:622
msgid "Line subtotal (before discounts)."
msgstr "Ligne de sous-total (avant remises)."

#: includes/api/class-wc-rest-subscriptions-controller.php:616
msgid "Product price."
msgstr "Tarif du produit."

#: includes/api/class-wc-rest-subscriptions-controller.php:610
msgid "Tax class of product."
msgstr "Classe de TVA de produit."

#: includes/api/class-wc-rest-subscriptions-controller.php:605
msgid "Quantity ordered."
msgstr "Quantité commandé."

#: includes/api/class-wc-rest-subscriptions-controller.php:600
msgid "Variation ID, if applicable."
msgstr "ID de la variation, si applicable."

#: includes/api/class-wc-rest-subscriptions-controller.php:595
msgid "Product ID."
msgstr "ID produit."

#: includes/api/class-wc-rest-subscriptions-controller.php:589
msgid "Product SKU."
msgstr "UGS du produit."

#: includes/api/class-wc-rest-subscriptions-controller.php:583
msgid "Product name."
msgstr "Nom du produit"

#: includes/api/class-wc-rest-subscriptions-controller.php:577
msgid "Item ID."
msgstr "ID de l&rsquo;article."

#: includes/api/class-wc-rest-subscriptions-controller.php:570
msgid "Removed line items data."
msgstr "Données articles de la ligne supprimées."

#: includes/api/class-wc-rest-subscriptions-controller.php:497
msgid ""
"The status to transition the subscription to. Unlike the \"status\" param, "
"this will calculate and update the subscription dates."
msgstr ""
"État vers lequel effectuer la transition de l’abonnement. Contrairement au "
"paramètre « état », cela calculera et mettra à jour les dates d’abonnement."

#. translators: %s: renewal count.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:97
msgid ""
"The number of upcoming renewal orders, for currently active subscriptions."
msgstr ""
"Nombre de commandes de renouvellement à venir, pour les abonnements "
"actuellement actifs."

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:90
msgid ""
"The sum of all the upcoming renewal orders, including items, fees, tax and "
"shipping, for currently active subscriptions."
msgstr ""
"Somme de toutes les commandes de renouvellement à venir, incluant les "
"articles, les frais, les frais d’expédition et de taxe, pour les abonnements "
"actuellement actifs."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:1024
msgid "Switch Totals"
msgstr "Totaux de changements"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:678
msgid "%2$s %1$s current subscriptions"
msgstr "%2$s %1$s abonnements actuels"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:653
msgid "%2$s %1$s ended subscriptions"
msgstr "%2$s %1$s abonnements terminés"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:631
msgid "%2$s %1$s subscription cancellations"
msgstr "%2$s %1$s annulations d’abonnement"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:609
msgid "%2$s %1$s subscription switches"
msgstr "%2$s %1$s changements d’abonnement"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:587
msgid "%2$s %1$s subscription renewals"
msgstr "%2$s %1$s renouvellements d’abonnement"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:565
msgid "%2$s %1$s subscription resubscribes"
msgstr "%2$s %1$s réabonnements d’abonnement"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:543
msgid "%2$s %1$s subscription signups"
msgstr "%2$s %1$s inscriptions d’abonnement"

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:521
msgid "%2$s %1$s new subscriptions"
msgstr "%2$s %1$s nouveaux abonnements"

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:513
msgid "The sum of all switch orders including tax and shipping."
msgstr ""
"Somme de toutes les commandes de changement, incluant les frais d’expédition "
"et de taxe."

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:512
msgid "%s switch revenue in this period"
msgstr "%s revenu de changement sur cette période"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:232
msgid "Copy billing address"
msgstr "Copier l’adresse de facturation"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:137
msgid "Billing"
msgstr "Facturation"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:69
msgid "Profile &rarr;"
msgstr "Profil &rarr;"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:64
msgid "View other subscriptions &rarr;"
msgstr "Afficher les autres abonnements &rarr;"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:52
msgid "General"
msgstr "Général"

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:102
msgctxt "relation to order"
msgid "Unknown Order Type"
msgstr "Type de commande inconnu"

#: includes/admin/class-wcs-wc-admin-manager.php:59
msgid "Edit Subscription"
msgstr "Modifier l’abonnement"

#: includes/admin/class-wcs-wc-admin-manager.php:49
msgid "Add New"
msgstr "Ajouter"

#: includes/admin/class-wcs-admin-meta-boxes.php:284
msgid "Retry renewal payment action requested by admin."
msgstr ""
"Réessayez l’action de paiement de renouvellement demandée par "
"l’administrateur."

#. translators: Placeholders are opening and closing link tags.
#: includes/admin/class-wc-subscriptions-admin.php:1541
msgid ""
"We weren't able to locate the set of report results you requested. Please "
"regenerate the link from the %1$sSubscription Reports screen%2$s."
msgstr ""
"Nous n’avons pas pu localiser l’ensemble de résultats de rapport que vous "
"avez demandé. Veuillez régénérer le lien sur l’%1$sécran Rapports "
"d’abonnement%2$s."

#: wcs-functions.php:130
msgctxt "Error message while creating a subscription"
msgid ""
"Invalid created date. The date must be a string and of the format: \"Y-m-d H:"
"i:s\"."
msgstr ""

#: templates/checkout/form-change-payment-method.php:49
msgctxt "text on button on checkout page"
msgid "Add payment method"
msgstr ""

#. translators: placeholder is a localized date and time (e.g. "February 1,
#. 2018 10:20 PM")
#: includes/wcs-formatting-functions.php:251
msgctxt "wcs_get_human_time_diff"
msgid "%s"
msgstr ""

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:80
msgctxt "plugin version number used in admin notice"
msgid "3.0"
msgstr ""

#: includes/class-wcs-retry-manager.php:268
msgctxt "used in order note as reason for why status changed"
msgid "Retry rule reapplied:"
msgstr ""

#. translators: 1: previous token, 2: new token.
#: includes/class-wcs-my-account-payment-methods.php:199
msgctxt "used in subscription note"
msgid ""
"Payment method meta updated after customer changed their default token and "
"opted to update their subscriptions. Payment meta changed from %1$s to %2$s"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:234
msgctxt "when to prorate first payment / subscription length"
msgid "Never (charge the full recurring amount at sign-up)"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:233
msgctxt "when to prorate first payment / subscription length"
msgid "Never (do not charge any recurring amount)"
msgstr ""

#: includes/early-renewal/class-wcs-cart-early-renewal.php:150
#: includes/early-renewal/class-wcs-cart-early-renewal.php:198
msgctxt "used in order note as reason for why subscription status changed"
msgid "Customer requested to renew early:"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:711
#: includes/class-wc-subscriptions-change-payment-gateway.php:752
msgctxt "the page title of the add payment method form"
msgid "Add payment method"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:279
msgctxt "label on button, imperative"
msgid "Add payment"
msgstr ""

#. translators: %s: address type (eg. 'billing' or 'shipping').
#: includes/class-wc-subscriptions-addresses.php:213
msgctxt "change billing or shipping address"
msgid "Change %s address"
msgstr ""

#. translators: %s: parent order number (linked to its details screen).
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:387
msgctxt "subscription note after linking to a parent order"
msgid "Subscription linked to parent order %s via admin."
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:99
msgctxt "relation to order"
msgid "Resubscribe Order"
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:204
msgctxt "label for the system status page"
msgid "Retries Migration Status"
msgstr "État des nouvelles tentatives de migration"

#: includes/payment-retry/class-wcs-retry-admin.php:196
msgctxt "label for the system status page"
msgid "Custom Retry Rule"
msgstr "Règle de nouvelle tentative personnalisée"

#: includes/payment-retry/class-wcs-retry-admin.php:188
msgctxt "label for the system status page"
msgid "Custom Raw Retry Rule"
msgstr "Règle de nouvelle tentative brute personnalisée"

#: includes/payment-retry/class-wcs-retry-admin.php:180
msgctxt "label for the system status page"
msgid "Custom Retry Rule Class"
msgstr "Classe de règle de nouvelle tentative personnalisée"

#: includes/payment-retry/class-wcs-retry-admin.php:172
msgctxt "label for the system status page"
msgid "Custom Retry Rules"
msgstr "Règles de nouvelle tentative personnalisées"

#: includes/admin/class-wcs-admin-system-status.php:357
msgctxt "label for the system status page"
msgid "Country / State"
msgstr "Pays / État"

#: includes/admin/class-wcs-admin-system-status.php:329
msgctxt "label for the system status page"
msgid "PayPal Reference Transactions Enabled"
msgstr "PayPal Reference Transactions activé"

#: includes/admin/class-wcs-admin-system-status.php:295
msgctxt "label for the system status page"
msgid "Other"
msgstr "Autre"

#: includes/admin/class-wcs-admin-system-status.php:265
msgctxt "label for the system status page"
msgid "Active Product Key"
msgstr "Clé de produit active"

#: includes/admin/class-wcs-admin-system-status.php:242
msgctxt "label for the system status page"
msgid "WooCommerce Account Connected"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:221
msgctxt "label for the system status page"
msgid "Subscription Statuses"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:119
msgctxt "Live URL, Label on WooCommerce -> System Status page"
msgid "Subscriptions Live URL"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:75
msgctxt "meta box title"
msgid "Schedule"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:243
msgctxt "there's a number immediately in front of this text"
msgid "days prior to Renewal Day"
msgstr ""

#. Author URI of the plugin
msgid "https://woocommerce.com/"
msgstr "https://woocommerce.com/"

#. translators: 1$-2$: opening and closing <strong> tags. 3$-4$: opening and
#. closing link tags for learn more. Leads to duplicate site article on docs.
#. 5$-6$: Opening and closing link to production URL. 7$: Production URL .
#: woocommerce-subscriptions.php:930
msgid ""
"It looks like this site has moved or is a duplicate site. %1$sWooCommerce "
"Subscriptions%2$s has disabled automatic payments and subscription related "
"emails on this site to prevent duplicate payments from a staging or test "
"environment. %1$sWooCommerce Subscriptions%2$s considers %5$s%7$s%6$s to be "
"the site's URL. %3$sLearn more &raquo;%4$s."
msgstr ""
"Il semble que ce site a été déplacé ou est un site dupliqué. %1$sWooCommerce "
"Subscriptions%2$s a désactivé des paiements automatiques et les e-mails liés "
"aux abonnements sur ce site pour éviter les paiements en double à partir "
"d’un environnement de préproduction ou de test. %1$sWooCommerce "
"Subscriptions%2$s pense que %5$s%7$s%6$s est l’URL du site. %3$sEn savoir "
"plus &raquo;%4$s."

#: woocommerce-subscriptions.php:861
msgid "Installed Plugins"
msgstr "Extensions installées"

#. translators: 1-2: opening/closing <b> tags, 3: Subscriptions version.
#: woocommerce-subscriptions.php:858
msgid ""
"%1$sWarning!%2$s We can see the %1$sWooCommerce Subscriptions Early "
"Renewal%2$s plugin is active. Version %3$s of %1$sWooCommerce "
"Subscriptions%2$s comes with that plugin's functionality packaged into the "
"core plugin. Please deactivate WooCommerce Subscriptions Early Renewal to "
"avoid any conflicts."
msgstr ""
"%1$sAttention !%2$sNous pouvons voir que l’extension %1$sWooCommerce "
"Subscriptions Early Renewal%2$s est active. La version %3$s de "
"%1$sWooCommerce Subscriptions%2$s est fournie avec la fonctionnalité de "
"cette extension intégrée à l’extension principale. Veuillez désactiver "
"WooCommerce Subscriptions Early Renewal pour éviter tout conflit."

#: woocommerce-subscriptions.php:393
msgid "Would you like to add a payment method now?"
msgstr "Voulez-vous ajouter un moyen de paiement maintenant ?"

#: woocommerce-subscriptions.php:393
msgid ""
"To enable automatic renewals for this subscription, you will first need to "
"add a payment method."
msgstr ""
"Pour activer les renouvellements automatiques pour cet abonnement, vous "
"devez d’abord ajouter un moyen de paiement."

#: templates/single-product/add-to-cart/variable-subscription.php:34
msgid "You have added a variation of this product to the cart already."
msgstr "Vous avez déjà ajouté une variante de ce produit au panier."

#: templates/myaccount/subscription-details.php:71
msgid "Payment"
msgstr "Paiement"

#: templates/myaccount/subscription-details.php:57
msgid "Disable auto renew"
msgstr "Désactiver le renouvellement automatique"

#: templates/myaccount/subscription-details.php:50
msgid "Enable auto renew"
msgstr "Activer le renouvellement automatique"

#: templates/myaccount/subscription-details.php:42
msgid "Auto renew"
msgstr "Renouvellement automatique"

#: templates/myaccount/my-subscriptions.php:72
msgid "You have reached the end of subscriptions. Go to the %sfirst page%s."
msgstr ""
"Vous avez atteint la fin des abonnements. Allez à la %spremière page%s."

#: templates/myaccount/my-subscriptions.php:65
msgid "Next"
msgstr "Suivant"

#: templates/myaccount/my-subscriptions.php:61
msgid "Previous"
msgstr "Précédent"

#. translators: $1: opening <strong> tag, $2: closing </strong> tag
#: templates/checkout/form-change-payment-method.php:91
msgid ""
"Update the payment method used for %1$sall%2$s of my current subscriptions"
msgstr ""
"Mettre à jour le moyen de paiement utilisé pour %1$stous%2$s mes abonnements "
"actuels"

#. translators: $1 the log file name $2 and $3 are opening and closing link
#. tags, respectively.
#: templates/admin/html-failed-scheduled-action-notice.php:38
msgid ""
"To see further details about these errors, view the %1$s log file from the "
"%2$sWooCommerce logs screen.%2$s"
msgstr ""
"Pour voir plus de détails sur ces erreurs, consultez le fichier journal %1$s "
"sur l’%2$sécran des journaux de WooCommerce.%2$s"

#: templates/admin/html-failed-scheduled-action-notice.php:31
msgid "Affected event:"
msgid_plural "Affected events:"
msgstr[0] "Événement affecté :"
msgstr[1] "Événements affectés :"

#: templates/admin/html-failed-scheduled-action-notice.php:21
msgid ""
"An error has occurred while processing a recent subscription related event. "
"For steps on how to fix the affected subscription and to learn more about "
"the possible causes of this error, please read our guide %1$shere%2$s."
msgid_plural ""
"An error has occurred while processing recent subscription related events. "
"For steps on how to fix the affected subscriptions and to learn more about "
"the possible causes of this error, please read our guide %1$shere%2$s."
msgstr[0] ""
"Une erreur est survenue lors du traitement d’un événement récent lié à un "
"abonnement. Pour savoir comment résoudre l’abonnement affecté et en savoir "
"plus sur les causes possibles de cette erreur, veuillez lire notre guide "
"%1$sici%2$s."
msgstr[1] ""
"Une erreur est survenue lors du traitement d’événements récents liés à un "
"abonnement. Pour savoir comment résoudre les abonnements affectés et en "
"savoir plus sur les causes possibles de cette erreur, veuillez lire notre "
"guide %1$sici%2$s."

#. translators: placeholder is human time diff (e.g. "3 weeks")
#: includes/wcs-formatting-functions.php:243
msgid "in %s"
msgstr "dans %s"

#: includes/wcs-cart-functions.php:252
msgid "[Remove]"
msgstr "[Enlever]"

#: includes/upgrades/templates/update-welcome-notice.php:8
msgid "What's new?"
msgstr "Qu’est-ce qui change ?"

#. translators: placeholder $1 is the Subscription version string ('2.3'), $2-3
#. are opening and closing <em> tags
#: includes/upgrades/templates/update-welcome-notice.php:5
msgid ""
"Version %1$s brings some new improvements requested by store managers just "
"like you (and possibly even by %2$syou%3$s)."
msgstr ""
"La version %1$s apporte de nouvelles améliorations demandées par des gérants "
"de boutique comme vous (et peut-être même par %2$svous%3$s)."

#: includes/upgrades/class-wcs-repair-suspended-paypal-subscriptions.php:51
msgid ""
"Subscription suspended by Database repair script. This subscription was "
"suspended via PayPal."
msgstr ""
"Abonnement suspendu par le script de réparation de base de données. Cet "
"abonnement a été suspendu via PayPal."

#. translators: 1-2: opening/closing <strong> tags, 3-4: opening/closing tags
#. linked to ticket form.
#: includes/upgrades/class-wc-subscriptions-upgrader.php:895
msgid ""
"%1$sWarning!%2$s We discovered an issue in %1$sWooCommerce Subscriptions 2.3."
"0 - 2.3.2%2$s that may cause your subscription renewal order and customer "
"subscription caches to contain invalid data. For information about how to "
"update the cached data, please %3$sopen a new support ticket%4$s."
msgstr ""
"%1$sAttention !%2$s Nous avons découvert un problème dans %1$sWooCommerce "
"Subscriptions 2.3.0 - 2.3.2%2$s qui peut entraîner la présence de données "
"non valides dans votre commande de renouvellement d’abonnement et les caches "
"d’abonnements client. Pour plus d’informations sur la mise à jour des "
"données mises en cache, veuillez %3$souvrir un nouveau ticket "
"d’assistance%4$s."

#. translators: 1-2: opening/closing <strong> tags, 3: active version of
#. Subscriptions, 4: current version of Subscriptions, 5-6: opening/closing
#. tags linked to ticket form, 7-8: opening/closing tags linked to
#. documentation.
#: includes/upgrades/class-wc-subscriptions-upgrader.php:822
msgid ""
"%1$sWarning!%2$s It appears that you have downgraded %1$sWooCommerce "
"Subscriptions%2$s from %3$s to %4$s. Downgrading the plugin in this way may "
"cause issues. Please update to %3$s or higher, or %5$sopen a new support "
"ticket%6$s for further assistance. %7$sLearn more &raquo;%8$s"
msgstr ""
"%1$sAttention !%2$s Il semble que vous avez rétrogradé %1$sWooCommerce "
"Subscriptions%2$s de %3$s à %4$s. Rétrograder l’extension de cette manière "
"peut causer des problèmes. Veuillez mettre à jour vers %3$s ou supérieur ou "
"bien %5$souvrir un nouveau ticket d’assistance%6$s pour obtenir une "
"assistance supplémentaire. %7$sEn savoir plus &raquo;%8$s"

#: includes/privacy/class-wcs-privacy.php:277
msgid "Customers with a subscription are excluded from this setting."
msgstr "Les clients avec un abonnement sont exclus de ce paramètre."

#: includes/privacy/class-wcs-privacy.php:233
msgid ""
"Retain ended subscriptions and their related orders for a specified duration "
"before anonymizing the personal data within them."
msgstr ""
"Conservez les abonnements terminés et leurs commandes liées pour la durée "
"spécifiée avant l’anonymisation des données personnelles."

#: includes/privacy/class-wcs-privacy.php:232
msgid "Retain ended subscriptions"
msgstr "Conserver les abonnements terminés"

#. Translators: %s URL to erasure request screen.
#: includes/privacy/class-wcs-privacy.php:223
msgid ""
"When handling an %s, should personal data within subscriptions be retained "
"or removed?"
msgstr ""
"Lors du traitement d’une %s, les données personnelles dans les abonnements "
"doivent-elles être conservées ou supprimées ?"

#: includes/privacy/class-wcs-privacy.php:221
msgid "Remove personal data from subscriptions"
msgstr "Supprimer les données personnelles des abonnements"

#: includes/privacy/class-wcs-privacy.php:215
msgid "account erasure request"
msgstr "demande d’effacement de données"

#. translators: placeholders are opening and closing tags.
#: includes/privacy/class-wcs-privacy.php:195
msgid ""
"%1$sNote:%2$s Orders which are related to subscriptions will not be included "
"in the orders affected by these settings."
msgstr ""
"%1$sRemarque :%2$s les commandes liées à des abonnements ne seront pas "
"incluses dans les commandes affectées par ces paramètres."

#. translators: %d: number of subscriptions affected.
#: includes/privacy/class-wcs-privacy.php:176
msgid "Removed personal data from %d subscription."
msgid_plural "Removed personal data from %d subscriptions."
msgstr[0] "Données personnelles supprimées de %d abonnement."
msgstr[1] "Données personnelles supprimées de %d abonnements."

#: includes/privacy/class-wcs-privacy.php:109
msgid "Cancel and remove personal data"
msgstr "Annuler et supprimer les données personnelles"

#. translators: placeholders are opening and closing link tags, linking to
#. additional privacy policy documentation.
#: includes/privacy/class-wcs-privacy.php:99
msgid ""
"If you are using PayPal Standard or PayPal Reference transactions please see "
"the %1$sPayPal Privacy Policy%2$s for more details."
msgstr ""
"Si vous utilisez PayPal Standard ou PayPal Reference Transactions, veuillez "
"consulter la %1$sPolitique de confidentialité de PayPal%2$s pour plus de "
"détails."

#: includes/privacy/class-wcs-privacy.php:97
msgid ""
"What personal information your store shares with external sources depends on "
"which third-party payment processor plugins you are using to collect "
"subscription payments. We recommend that you consult with their privacy "
"policies to inform this section of your privacy policy."
msgstr ""
"Les informations personnelles que votre boutique partage avec des sources "
"externes dépendent des extensions de plateforme de paiement tierces que vous "
"utilisez pour collecter les paiements d’abonnement. Nous vous recommandons "
"de consulter leurs politiques de confidentialité pour informer cette section "
"de votre politique de confidentialité."

#: includes/privacy/class-wcs-privacy.php:96
msgid "What we share with others"
msgstr "Ce que nous partageons avec d’autres"

#. translators: placeholders are opening and closing link tags, linking to
#. additional privacy policy documentation.
#: includes/privacy/class-wcs-privacy.php:95
msgid ""
"For the purposes of processing recurring subscription payments, we store the "
"customer's name, billing address, shipping address, email address, phone "
"number and credit card/payment details."
msgstr ""
"À des fins de traitement des paiements d’abonnement récurrents, nous "
"stockons le nom du client, son adresse de facturation, son adresse de "
"livraison, son adresse e-mail, son numéro de téléphone et ses détails de "
"carte de crédit/paiement."

#. translators: placeholders are opening and closing link tags, linking to
#. additional privacy policy documentation.
#: includes/privacy/class-wcs-privacy.php:94
msgid "What we collect and store"
msgstr "Ce que nous collectons et stockons"

#: includes/privacy/class-wcs-privacy.php:92
msgid ""
"By using WooCommerce Subscriptions, you may be storing personal data and "
"depending on which third-party payment processors you’re using to take "
"subscription payments, you may be sharing personal data with external "
"sources."
msgstr ""
"En utilisant WooCommerce Subscriptions, il est possible que vous stockiez "
"des données personnelles et, en fonction des plateformes de paiement tierces "
"que vous utilisez pour accepter les paiements d’abonnement, il est possible "
"que vous partagiez des données personnelles avec des sources externes."

#: includes/privacy/class-wcs-privacy.php:43
#: includes/privacy/class-wcs-privacy.php:44
msgid "Subscriptions Data"
msgstr "Données des abonnements"

#: includes/privacy/class-wcs-privacy-exporters.php:86
msgid "Email Address"
msgstr "Adresse e-mail"

#: includes/privacy/class-wcs-privacy-exporters.php:85
msgid "Phone Number"
msgstr "Numéro de téléphone"

#: includes/privacy/class-wcs-privacy-exporters.php:82
msgid "Browser User Agent"
msgstr "Agent utilisateur du navigateur"

#: includes/privacy/class-wcs-privacy-exporters.php:81
msgid "IP Address"
msgstr "Addresse IP"

#: includes/privacy/class-wcs-privacy-exporters.php:80
msgid "Subscription Items"
msgstr "Articles d’abonnement"

#: includes/privacy/class-wcs-privacy-exporters.php:78
msgid "Created Date"
msgstr "Date de création"

#: includes/privacy/class-wcs-privacy-erasers.php:189
msgid "Personal data removed."
msgstr "Données personnelles supprimées."

#. Translators: %s subscription number.
#: includes/privacy/class-wcs-privacy-erasers.php:75
msgid "Personal data within subscription %s has been retained."
msgstr "Les données personnelles de l’abonnement %s ont été conservées."

#. Translators: %s subscription number.
#: includes/privacy/class-wcs-privacy-erasers.php:71
msgid "Removed personal data from subscription %s."
msgstr "Données personnelles supprimées de l’abonnement %s."

#. translators: $1 is the log file name. $2 and $3 are opening and closing link
#. tags, respectively.
#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:46
msgid ""
"To see the full error, view the %1$s log file from the %2$sWooCommerce logs "
"screen.%3$s."
msgstr ""
"Pour voir l’erreur complète, consultez le fichier journal %1$s sur "
"l’%2$sécran des journaux de WooCommerce%3$s."

#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:36
msgid "Last recorded error:"
msgstr "Dernière erreur enregistrée :"

#. translators: $1 and $2 are opening link tags, $3 is a closing link tag.
#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:18
msgid ""
"A fatal error has occurred while processing a recent subscription payment "
"with PayPal. Please %1$sopen a new ticket at WooCommerce Support%3$s "
"immediately to get this resolved. %2$sLearn more &raquo;%3$s"
msgstr ""
"Une erreur fatale est survenue lors du traitement d’un paiement d’abonnement "
"récent avec PayPal. Veuillez immédiatement %1$souvrir un nouveau ticket sur "
"WooCommerce Support%3$s pour résoudre cette erreur. %2$sEn savoir plus "
"&raquo;%3$s"

#. translators: Placeholders are the opening and closing link tags.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:320
msgid ""
"Before enabling PayPal Standard for Subscriptions, please note, when using "
"PayPal Standard, customers are locked into using PayPal Standard for the "
"life of their subscription, and PayPal Standard has a number of limitations. "
"Please read the guide on %1$swhy we don't recommend PayPal Standard%2$s for "
"Subscriptions before choosing to enable this option."
msgstr ""
"Avant d’activer PayPal Standard pour Subscriptions, veuillez noter que, "
"lorsque PayPal Standard est utilisé, les clients sont obligés d’utiliser "
"PayPal Standard pendant toute la durée de leur abonnement, et PayPal "
"Standard présente un certain nombre de limites. Veuillez lire le guide sur "
"%1$sla raison pour laquelle nous ne recommandons pas PayPal Standard%2$s "
"pour Subscriptions avant de choisir d’activer cette option."

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:312
msgid "Enable PayPal Standard for Subscriptions"
msgstr "Activer PayPal Standard pour Subscriptions"

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:199
msgid "Open a ticket"
msgstr "Ouvrir un ticket"

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:279
msgid "Change payment features:"
msgstr "Modifier des fonctionnalités de paiement :"

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:275
msgid "Subscription features:"
msgstr "Fonctionnalités d’abonnement :"

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:272
msgid "Supported features:"
msgstr "Fonctionnalités prises en charge :"

#. translators: %s: default e-mail heading.
#: includes/emails/class-wcs-email-cancelled-subscription.php:168
msgid ""
"This controls the main heading contained within the email notification. "
"Leave blank to use the default heading: %s."
msgstr ""
"Cela contrôle l’en-tête principal contenu dans la notification par e-mail. "
"Laisser vide pour utiliser l’en-tête par défaut : %s."

#: includes/early-renewal/class-wcs-early-renewal-manager.php:55
msgid ""
"With early renewals enabled, customers can renew their subscriptions before "
"the next payment date."
msgstr ""
"Lorsque les renouvellements anticipés sont activés, les clients peuvent "
"renouveler leurs abonnements avant la date du prochain paiement."

#: includes/early-renewal/class-wcs-early-renewal-manager.php:54
msgid "Accept Early Renewal Payments"
msgstr "Accepter les paiements de renouvellement anticipé"

#: includes/early-renewal/class-wcs-early-renewal-manager.php:53
msgid "Early Renewal"
msgstr "Renouvellement anticipé"

#. translators: %s: order ID (linked to details page).
#: includes/early-renewal/class-wcs-cart-early-renewal.php:312
msgid "Order %s created to record early renewal."
msgstr "Commande %s créée pour enregistrer le renouvellement anticipé."

#. translators: placeholder contains a link to the order's edit screen.
#: includes/early-renewal/wcs-early-renewal-functions.php:172
msgid ""
"Failed to update subscription dates after customer renewed early with order "
"%s."
msgstr ""
"Échec de la mise à jour des dates d’abonnement après le renouvellement "
"anticipé du client avec la commande %s."

#. translators: placeholder contains a link to the order's edit screen.
#: includes/early-renewal/wcs-early-renewal-functions.php:169
msgid "Customer successfully renewed early with order %s."
msgstr "Le client a réussi le renouvellement anticipé avec la commande %s."

#: includes/early-renewal/class-wcs-cart-early-renewal.php:105
msgid "Complete checkout to renew now."
msgstr "Terminez la validation de commande pour renouveler maintenant."

#: includes/early-renewal/class-wcs-cart-early-renewal.php:99
msgid ""
"You can not renew this subscription early. Please contact us if you need "
"assistance."
msgstr ""
"Vous ne pouvez pas renouveler cet abonnement de manière anticipée. Contactez-"
"nous si vous avez besoin d’aide."

#: includes/early-renewal/class-wcs-cart-early-renewal.php:71
msgid "Renew now"
msgstr "Renouveler maintenant"

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:70
msgid ""
"This will clear the persistent cache of all renewal, switch, resubscribe and "
"other order types for all subscriptions in your store. Expect slower "
"performance of checkout, renewal and other subscription related functions "
"after taking this action. The caches will be regenerated overtime as related "
"order queries are run."
msgstr ""
"Cela effacera le cache persistant des commandes de renouvellement, de "
"changement, de réabonnement etc. pour tous les abonnements dans votre "
"boutique. Attendez-vous à une performance plus lente de la validation de "
"commande, du renouvellement et des autres fonctions liées à l’abonnement "
"après avoir effectué cette action. Les caches sont régénérés au fur et à "
"mesure que les requêtes de commande liées sont exécutées."

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:70
msgid "Delete Related Order Cache"
msgstr "Supprimer le cache de commandes liées"

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:69
msgid ""
"This will generate the persistent cache of all renewal, switch, resubscribe "
"and other order types for all subscriptions in your store. The caches will "
"be generated overtime in the background (via Action Scheduler)."
msgstr ""
"Cela générera le cache persistant des commandes de renouvellement, de "
"changement, de réabonnement etc. pour tous les abonnements dans votre "
"boutique. Les caches sont générés au fur et à mesure en arrière-plan (via le "
"Planificateur d’actions)."

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:69
msgid "Generate Related Order Cache"
msgstr "Générer le cache de commandes liées"

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:55
msgid ""
"This will clear the persistent cache of all of subscriptions stored against "
"users in your store. Expect slower performance of checkout, renewal and "
"other subscription related functions after taking this action. The caches "
"will be regenerated overtime as queries to find a given user's subscriptions "
"are run."
msgstr ""
"Cela effacera le cache persistant de tous les abonnements stockés avec les "
"utilisateurs dans votre boutique. Attendez-vous à une performance plus lente "
"de la validation de commande, du renouvellement et des autres fonctions "
"liées à l’abonnement après avoir effectué cette action. Les caches sont "
"régénérés au fur et à mesure que les requêtes pour trouver les abonnements "
"d’un utilisateur donné sont exécutées."

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:55
msgid "Delete Customer Subscription Cache"
msgstr "Supprimer le cache d’abonnement client"

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:54
msgid ""
"This will generate the persistent cache for linking users with subscriptions."
" The caches will be generated overtime in the background (via Action "
"Scheduler)."
msgstr ""
"Cela générera le cache persistant pour lier les utilisateurs avec des "
"abonnements. Les caches sont générés au fur et à mesure en arrière-plan (via "
"le Planificateur d’actions)."

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:54
msgid "Generate Customer Subscription Cache"
msgstr "Générer le cache d’abonnement client"

#: includes/class-wcs-staging.php:55
msgid "staging"
msgstr "préproduction"

#. translators: 1-2: opening/closing <a> tags - linked to staging site, 3: link
#. to live site.
#: includes/class-wcs-staging.php:40
msgid ""
"Payment processing skipped - renewal order created on %1$sstaging site%2$s "
"under staging site lock. Live site is at %3$s"
msgstr ""
"Traitement du paiement ignoré - commande de renouvellement créée sur le "
"%1$ssite de préproduction%2$s sous verrouillage du site de préproduction. Le "
"site en direct se trouve sur %3$s"

#: includes/class-wcs-retry-manager.php:344
msgid ""
"Payment retry attempted on renewal order with multiple related subscriptions "
"with no payment method in common."
msgstr ""
"Nouvelle tentative de paiement sur une commande de renouvellement avec "
"plusieurs abonnements liés sans moyen de paiement en commun."

#: includes/class-wcs-query.php:306
msgid ""
"Endpoint for the My Account &rarr; Change Subscription Payment Method page"
msgstr ""
"Point de terminaison pour la page Mon compte &rarr; Modifier le moyen de "
"paiement d’abonnement"

#: includes/class-wcs-query.php:305
msgid "Subscription payment method"
msgstr "Moyen de paiement d’abonnement"

#. translators: placeholder is a page number.
#: includes/class-wcs-query.php:106
msgid "Subscriptions (page %d)"
msgstr "Abonnements (page %d)"

#. translators: %s: invalid type of update argument.
#: includes/class-wcs-post-meta-cache-manager.php:199
msgid ""
"Invalid update type: %s. Post update types supported are \"add\" or "
"\"delete\". Updates are done on post meta directly."
msgstr ""
"Type de mise à jour non valide : %s. Les types de mise à jour d’article sont "
"« ajouter » ou « supprimer ». Les mises à jour sont effectuées sur les "
"métadonnées d’article directement."

#. translators: 1$-2$: opening and closing <strong> tags.
#: includes/class-wcs-permalink-manager.php:91
msgid ""
"Error saving Subscriptions endpoints: %1$sSubscriptions%2$s, %1$sView "
"subscription%2$s and %1$sSubscription payment method%2$s cannot be the same. "
"The changes have been reverted."
msgstr ""
"Erreur lors de l’enregistrement des points de terminaisons des abonnements : "
"%1$sAbonnements%2$s, %1$sAfficher l’abonnement%2$s et %1$sMoyen de paiement "
"d’abonnement%2$s ne peuvent pas être identiques. Les modifications ont été "
"annulées."

#. translators: 1: token display name, 2: opening link tag, 4: closing link
#. tag, 3: opening link tag.
#: includes/class-wcs-my-account-payment-methods.php:158
msgid ""
"Would you like to update your subscriptions to use this new payment method - "
"%1$s?%2$sYes%4$s | %3$sNo%4$s"
msgstr ""
"Voulez-vous mettre à jour vos abonnements pour utiliser ce nouveau moyen de "
"paiement - %1$s ?%2$sOui%4$s | %3$sNon%4$s"

#: includes/class-wcs-my-account-auto-renew-toggle.php:155
msgid ""
"Allow customers to turn on and off automatic renewals from their View "
"Subscription page."
msgstr ""
"Autorisez les clients à activer et désactiver les renouvellements "
"automatiques sur leur page Afficher l’abonnement."

#: includes/class-wcs-my-account-auto-renew-toggle.php:154
msgid "Display the auto renewal toggle"
msgstr "Afficher la bascule de renouvellement automatique"

#: includes/class-wcs-my-account-auto-renew-toggle.php:153
msgid "Auto Renewal Toggle"
msgstr "Bascule de renouvellement automatique"

#: includes/class-wcs-failed-scheduled-action-manager.php:163
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:106
msgid "Learn more"
msgstr "En savoir plus"

#: includes/class-wcs-failed-scheduled-action-manager.php:158
msgid "Ignore this error"
msgstr "Ignorer cette erreur"

#: includes/class-wcs-cart-renewal.php:210
msgid ""
"This order can no longer be paid because the corresponding subscription does "
"not require payment at this time."
msgstr ""
"Cette commande ne peut plus être payée car l’abonnement correspondant ne "
"nécessite pas de paiement pour le moment."

#: includes/class-wcs-cached-data-manager.php:127
msgid "new related order methods in WCS_Related_Order_Store"
msgstr "nouvelles méthodes de commande liée dans WCS_Related_Order_Store"

#: includes/class-wcs-cached-data-manager.php:110
#: includes/class-wcs-cached-data-manager.php:240
msgid "Customer subscription caching is now handled by %1$s and %2$s."
msgstr ""
"La mise en cache d’abonnement client est maintenant gérée par %1$s et %2$s."

#: includes/class-wcs-cached-data-manager.php:86
msgid "Customer subscription caching is now handled by %1$s."
msgstr "La mise en cache d’abonnement client est maintenant gérée par %1$s."

#: includes/class-wcs-cached-data-manager.php:79
msgid "Related order caching is now handled by %1$s."
msgstr "La mise en cache de commande liée est maintenant gérée par %1$s."

#: includes/class-wc-subscriptions-synchroniser.php:247
msgid ""
"Subscriptions created within this many days prior to the Renewal Day will "
"not be charged at sign-up. Set to zero for all new Subscriptions to be "
"charged the full recurring amount. Must be a positive number."
msgstr ""
"Les abonnements créés avant la date de renouvellement ne seront pas facturés "
"lors de l’inscription. Définissez sur zéro pour que tous les nouveaux "
"abonnements soient facturés du montant récurrent complet. Doit être un "
"nombre positif."

#: includes/class-wc-subscriptions-synchroniser.php:242
msgid "Sign-up grace period"
msgstr "Période de grâce d’inscription"

#: includes/class-wc-subscriptions-synchroniser.php:226
msgid "Prorate First Renewal"
msgstr "Premier renouvellement au prorata"

#. translators: placeholder is a switch type.
#: includes/class-wcs-switch-cart-item.php:309
msgid ""
"Invalid switch type \"%s\". Switch must be one of: \"upgrade\", "
"\"downgrade\" or \"crossgrade\"."
msgstr ""
"Type de changement non valide « %s ». Le changement doit être : « mise à "
"niveau », « rétrogradation » ou « reclassement »."

#: includes/class-wc-subscriptions-product.php:969
msgid ""
"This variation can not be removed because it is associated with active "
"subscriptions. To remove this variation, please cancel and delete the "
"subscriptions for it."
msgstr ""
"Cette variante ne peut pas être supprimée car elle est associée à des "
"abonnements actifs. Pour supprimer cette variante, veuillez annuler et "
"supprimer les abonnements correspondants."

#. translators: %1$s refers to the price. This string is meant to prefix
#. another string below, e.g. "$5 now, and $5 on March 15th each year"
#: includes/class-wc-subscriptions-product.php:291
msgid "%1$s now, and "
msgstr "%1$s maintenant, et"

#: includes/class-wc-subscriptions-coupon.php:1086
msgid "Active for unlimited payments"
msgstr "Actif pour les paiements illimités"

#. translators: %d refers to the number of payments the coupon can be used for.
#: includes/class-wc-subscriptions-coupon.php:1082
msgid "Active for %d payment"
msgid_plural "Active for %d payments"
msgstr[0] "Actif pour %d paiement"
msgstr[1] "Actif pour %d paiements"

#: includes/class-wc-subscriptions-coupon.php:950
msgid ""
"Coupon will be limited to the given number of payments. It will then be "
"automatically removed from the subscription. \"Payments\" also includes the "
"initial subscription payment."
msgstr ""
"Le code de promotion sera limité au nombre donné de paiements. Il sera "
"ensuite automatiquement supprimé de l’abonnement. « Paiements » inclut "
"également le paiement d’abonnement initial."

#: includes/class-wc-subscriptions-coupon.php:949
msgid "Unlimited payments"
msgstr "Paiements illimités"

#: includes/class-wc-subscriptions-coupon.php:948
msgid "Active for x payments"
msgstr "Actif pour x paiements"

#: includes/class-wc-subscriptions-coupon.php:933
msgid ""
"Sorry, it seems there are no available payment methods which support the "
"recurring coupon you are using. Please contact us if you require assistance "
"or wish to make alternate arrangements."
msgstr ""
"Désolé, il semble qu’aucun moyen de paiement ne soit disponible pour le code "
"de promotion récurrent que vous utilisez. Veuillez nous contacter si vous "
"avez besoin d’aide ou si vous désirez mettre en place une alternative."

#: includes/class-wc-subscriptions-coupon.php:717
msgid "Discount"
msgstr "Remise"

#: includes/class-wc-subscriptions-coupon.php:697
msgid "Initial payment discount"
msgstr "Remise sur le paiement initial"

#: includes/class-wc-subscriptions-change-payment-gateway.php:797
msgid ""
"Please log in to your account below to choose a new payment method for your "
"subscription."
msgstr ""
"Veuillez vous connecter à votre compte ci-dessous pour choisir un nouveau "
"moyen de paiement pour votre abonnement."

#: includes/class-wc-subscriptions-change-payment-gateway.php:380
#: includes/class-wc-subscriptions-change-payment-gateway.php:382
msgid "Payment method updated for all your current subscriptions."
msgstr "Moyen de paiement mis à jour pour tous vos abonnements actuels."

#: includes/class-wc-subscriptions-change-payment-gateway.php:340
msgid "Payment method added."
msgstr "Moyen de paiement ajouté."

#: includes/class-wc-subscription.php:1900
msgid ""
"The \"all\" value for $order_type parameter is deprecated. It was a misnomer,"
" as it did not return resubscribe orders. It was also inconsistent with "
"order type values accepted by wcs_get_subscription_orders(). Use array( "
"\"parent\", \"renewal\", \"switch\" ) to maintain previous behaviour, or "
"\"any\" to receive all order types, including switch and resubscribe."
msgstr ""
"La valeur « tous » du paramètre $order_type est obsolète. Elle était "
"inappropriée, car elle ne renvoyait pas les commandes de réabonnement. Elle "
"était également incohérente avec les valeurs de type de commande acceptées "
"par wcs_get_subscription_orders(). Utilisez un tableau (« parent », "
"« renouvellement », « changement ») pour conserver le comportement précédent "
"ou « n’importe lequel » pour recevoir tous les types de commande, notamment "
"changement et réabonnement."

#: includes/class-wc-subscription.php:1757
msgid "Payment status marked complete."
msgstr "État de paiement marqué comme terminé."

#: includes/class-wc-subscription.php:1326
msgid "The creation date of a subscription can not be deleted, only updated."
msgstr ""
"La date de création d’un abonnement ne peut pas être supprimée, uniquement "
"mise à jour."

#: includes/class-wc-subscription.php:604
msgid "Error during subscription status transition."
msgstr "Une erreur est survenue pendant le changement d’état de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:564
msgid "The date the subscription's latest order was paid, in GMT."
msgstr ""
"Date à laquelle la dernière commande de l’abonnement a été payée, au format "
"GMT."

#: includes/api/class-wc-rest-subscriptions-controller.php:558
msgid "The date the subscription's latest order was completed, in GMT."
msgstr ""
"Date à laquelle la dernière commande de l’abonnement a été terminée, au "
"format GMT."

#: includes/api/class-wc-rest-subscriptions-controller.php:552
msgid "The subscription's resubscribed subscription ID."
msgstr "ID d’abonnement réabonné de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:546
msgid ""
"The subscription's original subscription ID if this is a resubscribed "
"subscription."
msgstr ""
"ID d’abonnement original de l’abonnement s’il s’agit d’abonnement réabonné."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:855
msgid "New Subscriptions"
msgstr "Nouveaux abonnements"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:535
msgid ""
"The number of subscriptions created during this period, either by being "
"manually created, imported or a customer placing an order. This includes "
"orders pending payment."
msgstr ""
"Nombre d’abonnements créés pendant cette période, soit créés manuellement, "
"soit importés, soit via la commande d’un client. Cela inclut les commandes "
"en attente de paiement."

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:20
msgid "Product"
msgstr "Produit"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:50
msgid "The average value of all customers' sign-up, switch and renewal orders."
msgstr ""
"Valeur moyenne de toutes les commandes d’inscription, de changement et de "
"renouvellement des clients."

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:47
msgid ""
"The total number of sign-up, switch and renewal orders placed with your "
"store with a paid status (i.e. processing or complete)."
msgstr ""
"Nombre total des commandes d’inscription, de changement et de renouvellement "
"placées avec votre boutique avec un état payé (c.-à-d. traitement ou terminé)"
"."

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:46
msgid ""
"The total number of subscriptions with a status other than pending or "
"trashed."
msgstr ""
"Nombre total d’abonnements avec un état autre que en attente ou déplacé vers "
"la corbeille."

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:45
msgid ""
"The total number of subscriptions with a status of active or pending "
"cancellation."
msgstr ""
"Nombre total d’abonnements avec un état d’annulation active ou en attente."

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:44
msgid ""
"The number of unique customers with a subscription of any status other than "
"pending or trashed."
msgstr ""
"Nombre de clients uniques avec un abonnement avec un état autre que en "
"attente ou déplacé vers la corbeille."

#. translators: 1$: count, 2$ and 3$ are opening and closing strong tags,
#. respectively.
#: includes/admin/reports/class-wcs-report-dashboard.php:248
msgid "%2$s%1$s cancellation%3$s subscription cancellations this month"
msgid_plural "%2$s%1$s cancellations%3$s subscription cancellations this month"
msgstr[0] "%2$s%1$s annulation%3$s annulations d’abonnement ce mois-ci"
msgstr[1] "%2$s%1$s annulations%3$s annulations d’abonnement ce mois-ci"

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-dashboard.php:240
msgid "%s renewal revenue this month"
msgstr "%s revenu de renouvellement ce mois-ci"

#. translators: 1$: count, 2$ and 3$ are opening and closing strong tags,
#. respectively.
#: includes/admin/reports/class-wcs-report-dashboard.php:232
msgid "%2$s%1$s renewal%3$s subscription renewals this month"
msgid_plural "%2$s%1$s renewals%3$s subscription renewals this month"
msgstr[0] "%2$s%1$s renouvellement%3$s renouvellements d’abonnement ce mois-ci"
msgstr[1] ""
"%2$s%1$s renouvellements%3$s renouvellements d’abonnement ce mois-ci"

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-dashboard.php:224
msgid "%s signup revenue this month"
msgstr "%s revenu d’inscription ce mois-ci"

#. translators: 1$: count, 2$ and 3$ are opening and closing strong tags,
#. respectively.
#: includes/admin/reports/class-wcs-report-dashboard.php:216
msgid "%2$s%1$s signup%3$s subscription signups this month"
msgid_plural "%2$s%1$s signups%3$s subscription signups this month"
msgstr[0] "%2$s%1$s inscription%3$s inscriptions d’abonnement ce mois-ci"
msgstr[1] "%2$s%1$s inscriptions%3$s inscriptions d’abonnement ce mois-ci"

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:23
#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:72
msgid "Payment:"
msgstr "Paiement"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:214
msgid "Customer add payment method page &rarr;"
msgstr "Page d’ajout de moyen de paiement du client &rarr;"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:212
msgid "Customer change payment method page &rarr;"
msgstr "Page de changement de moyen de paiement du client &rarr;"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:127
msgid "Select an order&hellip;"
msgstr "Sélectionner une commande&hellip;"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:121
msgid "Parent order:"
msgstr "Commande parente :"

#. translators: placeholder is an order number.
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:114
msgid "#%1$s"
msgstr "n°%1$s"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:110
msgid "Parent order: "
msgstr "Commande parente : "

#: includes/admin/class-wcs-admin-system-status.php:72
msgid "This section shows information about payment gateway feature support."
msgstr ""
"Cette section contient des informations sur la prise en charge de la "
"fonctionnalité de passerelle de paiement."

#: includes/admin/class-wcs-admin-system-status.php:71
msgid "Payment Gateway Support"
msgstr "Prise en charge de la passerelle de paiement"

#: includes/admin/class-wcs-admin-system-status.php:67
msgid "This section shows information about Subscription payment methods."
msgstr ""
"Cette section contient des informations sur les moyens de paiement "
"d’abonnement."

#: includes/admin/class-wcs-admin-system-status.php:66
msgid "Subscriptions by Payment Gateway"
msgstr "Abonnements par passerelle de paiement"

#: includes/admin/class-wcs-admin-system-status.php:62
msgid "This section shows general information about the store."
msgstr "Cette section contient des informations générales sur la boutique."

#: includes/admin/class-wcs-admin-system-status.php:61
msgid "Store Setup"
msgstr "Configuration de la boutique"

#: includes/admin/class-wcs-admin-meta-boxes.php:253
msgid "Create pending parent order requested by admin action."
msgstr ""
"Créez une commande parente en attente demandée par une action de "
"l’administrateur."

#: includes/admin/class-wcs-admin-meta-boxes.php:183
msgid "Create pending parent order"
msgstr "Créer une commande parente en attente"

#: includes/admin/class-wc-subscriptions-admin.php:2036
msgid "Note that purchasing a subscription still requires an account."
msgstr "Notez que l’achat d’un abonnement nécessite toujours un compte."

#: includes/admin/class-wc-subscriptions-admin.php:1595
msgid "We can't find a paid subscription order for this user."
msgstr ""
"Nous ne trouvons pas de commande d’abonnement payée pour cet utilisateur."

#: includes/admin/class-wc-subscriptions-admin.php:1344
msgid ""
"Allow a subscription product with a $0 initial payment to be purchased "
"without providing a payment method. The customer will be required to provide "
"a payment method at the end of the initial period to keep the subscription "
"active."
msgstr ""
"Autorisez l’achat d’un produit d’abonnement avec un paiement initial de 0 $ "
"sans fournir de moyen de paiement. Le client devra fournir un moyen de "
"paiement à la fin de la période initiale pour maintenir l’abonnement actif."

#: includes/admin/class-wc-subscriptions-admin.php:1340
msgid "Allow $0 initial checkout without a payment method."
msgstr ""
"Autorisez une validation de commande initiale de 0 $ sans moyen de paiement."

#: includes/admin/class-wc-subscriptions-admin.php:1339
msgid "$0 Initial Checkout"
msgstr "Validation de commande initiale de 0 $"

#: includes/admin/class-wc-subscriptions-admin.php:868
msgid ""
"Because of this, it is not recommended as a payment method for Subscriptions "
"unless it is the only available option for your country."
msgstr ""
"Pour cette raison, il n’est pas recommandé comme moyen de paiement pour les "
"abonnements, sauf s’il s’agit de la seule option disponible pour votre pays."

#: includes/admin/class-wc-subscriptions-admin.php:868
msgid ""
"PayPal Standard has a number of limitations and does not support all "
"subscription features."
msgstr ""
"PayPal Standard présente un certain nombre de limites et ne prend pas en "
"charge toutes les fonctionnalités d’abonnement."

#: includes/admin/class-wc-subscriptions-admin.php:2050
msgid ""
"The product type can not be changed because this product is associated with "
"subscriptions."
msgstr ""
"Le type de produit ne peut pas être modifié car ce produit est associé à des "
"abonnements."

#: includes/admin/class-wc-subscriptions-admin.php:836
msgid "Delete all variations without a subscription"
msgstr "Supprimer toutes les variantes sans abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:447
msgid "Subscription pricing"
msgstr "Tarifs d’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:221
msgid "Virtual"
msgstr "Virtuel"

#: includes/admin/class-wc-subscriptions-admin.php:220
msgid "Downloadable"
msgstr "Téléchargeable"

#. translators: 1: relation type, 2: list of valid relation types.
#: includes/abstracts/abstract-wcs-related-order-store.php:148
msgid ""
"Invalid relation type: %1$s. Order relationship type must be one of: %2$s."
msgstr ""
"Type de relation non valide : %1$s. Le type de relation de commande doit "
"être : %2$s."

#: templates/emails/subscription-info.php:35
msgctxt "subscription number in email table. (eg: #106)"
msgid "#%s"
msgstr "n°%s"

#. translators: $1-$3: opening and closing <a> tags $2: subscription's order
#. number
#: templates/emails/email-order-details.php:27
msgctxt "Used in email notification"
msgid "Subscription %1$s#%2$s%3$s"
msgstr "Abonnement %1$sn°%2$s%3$s"

#. translators: $1-$2: opening and closing <a> tags $3: order's order number
#. $4: date of order in <time> element
#: templates/emails/email-order-details.php:24
msgctxt "Used in email notification"
msgid "%1$sOrder #%3$s%2$s (%4$s)"
msgstr "%1$sCommande n°%3$s%2$s (%4$s)"

#. translators: %1$s: link to checkout payment url, note: no full stop due to
#. url at the end
#: templates/emails/plain/customer-payment-retry.php:21
msgctxt "In customer renewal invoice email"
msgid ""
"To reactivate the subscription now, you can also log in and pay for the "
"renewal from your account page: %1$s"
msgstr ""
"Pour réactiver l’abonnement maintenant, vous pouvez également vous connecter "
"et payer le renouvellement sur la page de votre compte : %1$s"

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s:
#. lowercase human time diff in the form returned by wcs_get_human_time_diff(),
#. e.g. 'in 12 hours'
#: templates/emails/plain/admin-payment-retry.php:20
msgctxt "In customer renewal invoice email"
msgid ""
"The automatic recurring payment for order #%1$s from %2$s has failed. The "
"payment will be retried %3$s."
msgstr ""
"Le paiement récurrent automatique pour la commande n°%1$s de %2$s a échoué. "
"Le paiement sera retenté %3$s."

#. translators: %1$s %2$s: link markup to checkout payment url, note: no full
#. stop due to url at the end
#: templates/emails/customer-payment-retry.php:21
msgctxt "In customer renewal invoice email"
msgid ""
"To reactivate the subscription now, you can also log in and pay for the "
"renewal from your account page: %1$sPay Now &raquo;%2$s"
msgstr ""
"Pour réactiver l’abonnement maintenant, vous pouvez également vous connecter "
"et payer le renouvellement sur la page de votre compte : %1$sPayer "
"maintenant &raquo;%2$s"

#. translators: %s: lowercase human time diff in the form returned by
#. wcs_get_human_time_diff(), e.g. 'in 12 hours'
#: templates/emails/customer-payment-retry.php:18
#: templates/emails/plain/customer-payment-retry.php:18
msgctxt "In customer renewal invoice email"
msgid ""
"The automatic payment to renew your subscription has failed. We will retry "
"the payment %s."
msgstr ""
"Le paiement automatique pour renouveler votre abonnement a échoué. Nous "
"retenterons le paiement %s."

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s:
#. lowercase human time diff in the form returned by wcs_get_human_time_diff(),
#. e.g. 'in 12 hours'
#: templates/emails/admin-payment-retry.php:23
msgctxt "In customer renewal invoice email"
msgid ""
"The automatic recurring payment for order #%d from %s has failed. The "
"payment will be retried %3$s."
msgstr ""
"Le paiement récurrent automatique pour la commande n°%d de %s a échoué. Le "
"paiement sera retenté %3$s."

#: templates/emails/on-hold-subscription.php:24
msgctxt "table headings in notification email"
msgid "Date Suspended"
msgstr "Date de la suspension"

#: templates/emails/expired-subscription.php:24
msgctxt "table headings in notification email"
msgid "End Date"
msgstr "Date de fin"

#: templates/admin/html-variation-price.php:67
msgctxt "Subscription Length dropdown's description in pricing fields"
msgid ""
"Automatically expire the subscription after this length of time. This length "
"is in addition to any free trial or amount of time provided before a "
"synchronised first renewal date."
msgstr ""
"Expire automatiquement l’abonnement après cette durée. Cette durée s’ajoute "
"à tout essai gratuit ou à toute durée accordée avant une première date de "
"renouvellement synchronisée."

#: includes/wcs-time-functions.php:88
msgctxt "Subscription length"
msgid "Never expire"
msgstr "N’expire jamais"

#: includes/upgrades/templates/wcs-about.php:100
msgctxt "learn more link to subscription emails documentation"
msgid "Learn More"
msgstr "En savoir plus"

#: includes/upgrades/templates/wcs-about.php:79
msgctxt "learn more link to failed payment retry documentation"
msgid "Learn More"
msgstr "En savoir plus"

#: includes/upgrades/templates/wcs-about.php:55
msgctxt "learn more link to subscription reports documentation"
msgid "Learn More"
msgstr "En savoir plus"

#: includes/upgrades/templates/wcs-about.php:126
msgctxt "h3 on the About Subscriptions page for this new feature"
msgid "Import/Export Subscriptions"
msgstr "Importer/exporter des abonnements"

#: includes/upgrades/templates/wcs-about.php:35
msgctxt "short for documents"
msgid "Documentation"
msgstr "Documentation"

#: includes/payment-retry/class-wcs-retry-post-store.php:37
msgctxt "Admin menu name"
msgid "Renewal Payment Retries"
msgstr "Nouvelles tentatives de paiement de renouvellement"

#: includes/payment-retry/class-wcs-retry-post-store.php:35
msgctxt "Post type name"
msgid "Renewal Payment Retries"
msgstr "Nouvelles tentatives de paiement de renouvellement"

#. translators: 1$: subscription ID, 2$: names of items, comma separated
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:75
msgctxt "item name sent to paypal"
msgid "Subscription %1$s - %2$s"
msgstr "Abonnement %1$s - %2$s"

#. translators: placeholder is {blogname}, a variable that will be substituted
#. when email is sent out
#: includes/emails/class-wcs-email-on-hold-subscription.php:31
msgctxt "default email subject for suspended emails sent to the admin"
msgid "[%s] Subscription Suspended"
msgstr "[%s] Abonnement suspendu"

#. translators: placeholder is {blogname}, a variable that will be substituted
#. when email is sent out
#: includes/emails/class-wcs-email-expired-subscription.php:31
msgctxt "default email subject for expired emails sent to the admin"
msgid "[%s] Subscription Expired"
msgstr "[%s] Abonnement expiré"

#: includes/class-wcs-retry-manager.php:324
msgctxt "used in order note as reason for why order status changed"
msgid "Subscription renewal payment retry:"
msgstr "Nouvelle tentative de paiement de renouvellement d’abonnement :"

#: includes/class-wcs-retry-manager.php:230
msgctxt "used in order note as reason for why status changed"
msgid "Retry rule applied:"
msgstr "Règle de nouvelle tentative appliquée :"

#. translators: 1: payment status (e.g. "completed"), 2: pending reason.
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:435
msgctxt "used in order note"
msgid "IPN subscription payment %1$s for reason: %2$s."
msgstr "Paiement d’abonnement IPN %1$s pour la raison : %2$s."

#. translators: 1: deleted token, 2: new token.
#: includes/class-wcs-my-account-payment-methods.php:98
msgctxt "used in subscription note"
msgid ""
"Payment method meta updated after customer deleted a token from their My "
"Account page. Payment meta changed from %1$s to %2$s"
msgstr ""
"Métadonnées de moyen de paiement mises à jour après la suppression par le "
"client d’un jeton sur sa page Mon compte. Les métadonnées de paiement sont "
"passées de %1$s à %2$s"

#: includes/class-wc-subscriptions-order.php:753
msgctxt "An order type"
msgid "Non-subscription"
msgstr "Non-abonnement"

#: includes/class-wc-subscriptions-order.php:752
msgctxt "An order type"
msgid "Subscription Switch"
msgstr "Changement d’abonnement"

#: includes/class-wc-subscriptions-order.php:751
msgctxt "An order type"
msgid "Subscription Resubscribe"
msgstr "Réabonnement d’abonnement"

#: includes/class-wc-subscriptions-order.php:750
msgctxt "An order type"
msgid "Subscription Renewal"
msgstr "Renouvellement de l’abonnement"

#: includes/class-wc-subscriptions-order.php:749
msgctxt "An order type"
msgid "Subscription Parent"
msgstr "Parent d’abonnement"

#: includes/class-wcs-retry-manager.php:328
msgctxt "used in order note as reason for why subscription status changed"
msgid "Subscription renewal payment retry:"
msgstr "Nouvelle tentative de paiement de renouvellement d’abonnement :"

#: includes/admin/reports/class-wcs-report-retention-rate.php:162
msgctxt "X axis label on retention rate graph"
msgid "Number of months after sign-up"
msgstr "Nombre de mois après l’inscription"

#: includes/admin/reports/class-wcs-report-retention-rate.php:159
msgctxt "X axis label on retention rate graph"
msgid "Number of weeks after sign-up"
msgstr "Nombre de semaines après l’inscription"

#: includes/admin/reports/class-wcs-report-retention-rate.php:156
msgctxt "X axis label on retention rate graph"
msgid "Number of days after sign-up"
msgstr "Nombre de jours après l’inscription"

#: includes/admin/reports/class-wcs-report-cache-manager.php:314
msgctxt "Whether the Report Cache has been enabled"
msgid "Report Cache Enabled"
msgstr "Cache de rapport activé"

#: tests/unit/scheduler/scheduler.php:69 wcs-functions.php:307
msgctxt "table heading"
msgid "Cancelled Date"
msgstr "Date d’annulation"

#: templates/emails/cancelled-subscription.php:23
#: templates/emails/expired-subscription.php:23
#: templates/emails/on-hold-subscription.php:23
#: tests/unit/scheduler/scheduler.php:68 wcs-functions.php:306
msgctxt "table heading"
msgid "Last Order Date"
msgstr "Date de la dernière commande"

#: includes/class-wcs-retry-manager.php:120
msgctxt "table heading"
msgid "Renewal Payment Retry"
msgstr "Nouvelle tentative de paiement de renouvellement"

#. translators: %s: gateway ID.
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:175
#: includes/class-wcs-change-payment-method-admin.php:53
msgctxt ""
"The gateway ID displayed on the Edit Subscriptions screen when editing "
"payment method."
msgid "Gateway ID: [%s]"
msgstr "ID de passerelle : [%s]"

#: includes/admin/class-wcs-admin-system-status.php:135
msgctxt "label for the system status page"
msgid "Subscriptions Template Theme Overrides"
msgstr "Remplacement du thème de modèle d’abonnements"

#. translators: placeholder is trial period validation message if passed an
#. invalid value (e.g. "Trial period can not exceed 4 weeks")
#: includes/admin/class-wc-subscriptions-admin.php:287
msgctxt "Trial period field tooltip on Edit Product administration screen"
msgid ""
"An optional period of time to wait before charging the first recurring "
"payment. Any sign up fee will still be charged at the outset of the "
"subscription. %s"
msgstr ""
"Une période d’attente facultative avant de facturer le premier paiement "
"récurrent. Les frais d’inscription seront toujours facturés au début de "
"l’abonnement. %s"

#: templates/single-product/add-to-cart/variable-subscription.php:45
msgid "Clear"
msgstr "Effacer"

#. translators: placeholder is localised date string
#: templates/emails/plain/on-hold-subscription.php:36
msgid "Date Suspended: %s"
msgstr "Date de la suspension : %s"

#. translators: placeholder is last time subscription was paid
#: templates/emails/plain/on-hold-subscription.php:32
msgid "Last Order: %s"
msgstr "Dernière commande : %s"

#. translators: placeholder is localised date string
#: templates/emails/plain/expired-subscription.php:39
msgid "End Date: %s"
msgstr "Date de fin : %s"

#. translators: placeholder is last time subscription was paid
#: templates/emails/plain/cancelled-subscription.php:32
#: templates/emails/plain/expired-subscription.php:32
msgid "Last Order Date: %s"
msgstr "Date de la dernière commande : %s"

#. translators: $1: customer's billing first name and last name
#: templates/emails/on-hold-subscription.php:16
#: templates/emails/plain/on-hold-subscription.php:16
msgid ""
"A subscription belonging to %1$s has been suspended by the user. Their "
"subscription's details are as follows:"
msgstr ""
"Un abonnement appartenant à %1$s a été suspendu par l’utilisateur. Les "
"détails de son abonnement sont les suivants :"

#: templates/emails/cancelled-subscription.php:41
#: templates/emails/expired-subscription.php:41
#: templates/emails/on-hold-subscription.php:41
msgid "-"
msgstr "-"

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s:
#. lowercase human time diff in the form returned by wcs_get_human_time_diff(),
#. e.g. 'in 12 hours'
#: templates/emails/admin-payment-retry.php:24
#: templates/emails/plain/admin-payment-retry.php:21
msgid "The renewal order is as follows:"
msgstr "La commande de renouvellement est la suivante :"

#: templates/cart/cart-recurring-shipping.php:34
msgid ""
"There are no shipping methods available. Please double check your address, "
"or contact us if you need any help."
msgstr ""
"Aucune méthode de livraison disponible. Veuillez vérifier votre adresse, ou "
"contactez-nous si vous avez besoin d’aide."

#: templates/cart/cart-recurring-shipping.php:32
msgid "Shipping costs will be calculated once you have provided your address."
msgstr ""
"Les coûts de livraison seront calculés une fois que vous aurez indiqué votre "
"adresse."

#. translators: placeholders are opening/closing tags linking to documentation
#. on outdated templates.
#: includes/admin/class-wcs-admin-system-status.php:145
msgid "%1$sLearn how to update%2$s"
msgstr "%1$sApprenez comment mettre à jour%2$s"

#. translators: %1$s is the file version, %2$s is the core version
#: includes/admin/class-wcs-admin-system-status.php:191
msgid "version %1$s is out of date. The core version is %2$s"
msgstr "version %1$s est obsolète. La version du noyau est %2$s"

#: includes/admin/class-wcs-admin-system-status.php:57
msgid "This section shows any information about Subscriptions."
msgstr "Cette section contient des informations sur Subscriptions."

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:228
msgid "Shipping"
msgstr "Expédition"

#: includes/upgrades/templates/wcs-about.php:213
msgid "Go to WooCommerce Subscriptions Settings &raquo;"
msgstr "Accéder aux paramètres de WooCommerce Subscriptions &raquo;"

#: includes/upgrades/templates/wcs-about.php:212
msgid "See the full guide to What's New in Subscriptions version 2.1 &raquo;"
msgstr ""
"Consulter le guide complet des nouveautés de Subscriptions version 2.1 "
"&raquo;"

#. translators: placeholders are opening and closing <a> tags
#: includes/upgrades/templates/wcs-about.php:206
msgid ""
"Subscriptions also now uses the renewal order to setup the cart for %smanual "
"renewals%s, making it easier to add products or discounts to a single "
"renewal paid manually."
msgstr ""
"Subscriptions utilise désormais la commande de renouvellement pour "
"configurer le panier pour les %srenouvellements manuels%s, ce qui facilite "
"l’ajout de produits ou de remises à un seul renouvellement payé manuellement."

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about.php:202
msgid ""
"Subscriptions 2.1 now passes the renewal order's total, making it possible "
"to add a fee or discount to the renewal order with simple one-liners like "
"%s$order->add_fee()%s or %s$order->add_coupon()%s."
msgstr ""
"Subscriptions 2.1 transmet désormais le total de la commande de "
"renouvellement, ce qui permet d’ajouter des frais ou une remise à la "
"commande de renouvellement avec des mots simples comme %s$order->add_fee()%s "
"ou %s$order->add_coupon()%s."

#: includes/upgrades/templates/wcs-about.php:199
msgid ""
"In previous versions of Subscriptions, the subscription total was passed to "
"payment gateways as the amount to charge for automatic renewal payments. "
"This made it unnecessarily complicated to add one-time fees or discounts to "
"a renewal."
msgstr ""
"Dans les versions précédentes de Subscriptions, le total d’abonnement était "
"transmis aux passerelles de paiement comme montant à facturer pour les "
"paiements de renouvellement automatique. Cela compliquait inutilement "
"l’ajout de frais uniques ou de remises à un renouvellement."

#. translators: placeholders are opening and closing code tags
#: includes/upgrades/templates/wcs-about.php:197
msgid "Honour Renewal Order Data"
msgstr "Honorer les données de commande de renouvellement"

#. translators: all placeholders are opening and closing <code> tags, no need
#. to order them
#: includes/upgrades/templates/wcs-about.php:187
msgid ""
"Want the details of a specific subscription? Get %s/wp-"
"json/wc/v1/subscriptions/<id>/%s."
msgstr ""
"Vous voulez les détails d’un abonnement spécifique ? Obtenez %s/wp-"
"json/wc/v1/subscriptions/<id>/%s."

#. translators: all placeholders are opening and closing <code> tags, no need
#. to order them
#: includes/upgrades/templates/wcs-about.php:183
msgid ""
"Want to list all the subscriptions on a site? Get %s/wp-"
"json/wc/v1/subscriptions%s."
msgstr ""
"Vous voulez répertorier tous les abonnements sur un site ? Obtenez %s/wp-"
"json/wc/v1/subscriptions%s."

#: includes/upgrades/templates/wcs-about.php:180
msgid ""
"Your applications can now create, read, update or delete subscriptions via "
"RESTful API endpoints with the same design as the latest version of "
"WooCommerce's REST API endpoints."
msgstr ""
"Vos applications peuvent désormais créer, lire, mettre à jour ou supprimer "
"des abonnements via des points de terminaison API RESTful avec la même "
"conception que la dernière version des points de terminaison API RESTful de "
"WooCommerce."

#: includes/upgrades/templates/wcs-about.php:179
msgid ""
"Subscriptions 2.1 adds support for subscription data to this infrastructure."
msgstr ""
"Subscriptions 2.1 ajoute la prise en charge des données d’abonnement à cette "
"infrastructure."

#. translators: $1: opening <a> tag linking to WC API docs, $2: closing <a>
#. tag, $3: opening <a> tag linking to WP API docs, $4: closing <a> tag
#: includes/upgrades/templates/wcs-about.php:177
msgid ""
"WooCommerce 2.6 added support for %1$sREST API%2$s endpoints built on "
"WordPress core's %3$sREST API%4$s infrastructure."
msgstr ""
"WooCommerce 2.6 a ajouté la prise en charge des points de terminaison "
"%1$sAPI REST%2$s créés sur l’infrastructure %3$sAPI REST%4$s du cœur de "
"WordPress."

#: includes/upgrades/templates/wcs-about.php:174
msgid "WP REST API Endpoints"
msgstr "Points de terminaison API REST WP"

#. translators: all placeholders are opening and closing <code> tags, no need
#. to order them
#: includes/upgrades/templates/wcs-about.php:166
msgid ""
"To apply a specific rule based on certain conditions, like high value orders "
"or an infrequent renewal schedule, you can use the retry specific "
"%s'wcs_get_retry_rule'%s filter. This provides the ID of the renewal order "
"for the failed payment, which can be used to find information about the "
"products, subscription and totals to which the failed payment relates."
msgstr ""
"Pour appliquer une règle spécifique basée sur certaines conditions, comme "
"des commandes de valeur élevée ou un calendrier de renouvellement peu "
"fréquent, vous pouvez utiliser le filtre %s'wcs_get_retry_rule'%s spécifique "
"aux nouvelles tentatives. Cela fournit l’ID de la commande de renouvellement "
"pour le paiement échoué, qui peut être utilisé pour trouver des informations "
"sur les produits, l’abonnement et les totaux auxquels le paiement échoué se "
"rapporte."

#. translators: all placeholders are opening and closing <code> tags, no need
#. to order them
#: includes/upgrades/templates/wcs-about.php:162
msgid ""
"With the %s'wcs_default_retry_rules'%s filter, you can define a set of "
"default rules to apply to all failed payments in your store."
msgstr ""
"Avec le filtre %s'wcs_default_retry_rules'%s, vous pouvez définir un "
"ensemble de règles par défaut à appliquer à tous les paiements échoués dans "
"votre boutique."

#: includes/upgrades/templates/wcs-about.php:159
msgid ""
"The best part about the new automatic retry system is that the retry rules "
"are completely customisable."
msgstr ""
"La meilleure partie du nouveau système de nouvelle tentative automatique est "
"que les règles de nouvelle tentative sont entièrement personnalisables."

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about.php:157
msgid "Customise Retry Rules"
msgstr "Personnaliser des règles de nouvelle tentative"

#: includes/upgrades/templates/wcs-about.php:139
msgid ""
"With WooCommerce Subscribe All the Things, they can! This experimental "
"extension is exploring how to convert any product, including Product Bundles "
"and Composite Products, into a subscription product. It also offers "
"customers a way to subscribe to a cart of non-subscription products."
msgstr ""
"Avec WooCommerce Subscribe All the Things, ils le peuvent ! Cette extension "
"expérimentale explore comment convertir n’importe quel produit, dont des "
"groupes de produits et des produits composites, en un produit d’abonnement. "
"Elle permet aux clients de s’abonner à un panier de produits sans abonnement."

#: includes/upgrades/templates/wcs-about.php:138
msgid ""
"Want your customers to be able to subscribe to non-subscription products?"
msgstr ""
"Vous voulez que vos clients puissent s’abonner à des produits sans "
"abonnement ?"

#: includes/upgrades/templates/wcs-about.php:137
msgid "Subscribe All the Things"
msgstr "Subscribe All the Things"

#: includes/upgrades/templates/wcs-about.php:128
msgid ""
"This free extension makes it possible to migrate subscribers from 3rd party "
"systems to WooCommerce. It also makes it possible to export your "
"subscription data for analysis in spreadsheet tools or 3rd party apps."
msgstr ""
"Cette extension gratuite permet de migrer des abonnés de systèmes tiers vers "
"WooCommerce. Elle permet également d’exporter vos données d’abonnement pour "
"analyse dans des outils de feuille de calcul ou des applications tierces."

#: includes/upgrades/templates/wcs-about.php:127
msgid ""
"Import subscriptions to WooCommerce via CSV, or export your subscriptions "
"from WooCommerce to a CSV with the WooCommerce Subscriptions "
"Importer/Exporter extension."
msgstr ""
"Importez des abonnements vers WooCommerce via CSV ou exportez vos "
"abonnements depuis WooCommerce vers un fichier CSV avec l’extension "
"WooCommerce Subscriptions Importer/Exporter."

#: includes/upgrades/templates/wcs-about.php:117
msgid ""
"The Gifting extension makes it possible for one person to purchase a "
"subscription product for someone else. It then shares control of the "
"subscription between the purchaser and recipient, allowing both to manage "
"the subscription over its lifecycle."
msgstr ""
"L’extension Gifting permet à une personne d’acheter un produit d’abonnement "
"pour quelqu’un d’autre. Elle partage ensuite le contrôle de l’abonnement "
"entre l’acheteur et le destinataire, ce qui permet aux deux de gérer "
"l’abonnement tout au long de son cycle de vie."

#: includes/upgrades/templates/wcs-about.php:116
msgid ""
"What happens when a customer wants to purchase a subscription product for "
"someone else?"
msgstr ""
"Que se passe-t-il quand un client veut acheter un produit d’abonnement pour "
"quelqu’un d’autre ?"

#: includes/upgrades/templates/wcs-about.php:109
msgid ""
"That's not all we've working on for the last 12 months when it comes to "
"Subscriptions. We've also released mini-extensions to help you get the most "
"from your subscription store."
msgstr ""
"Ce n’est pas tout ce sur quoi nous travaillons depuis 12 mois en ce qui "
"concerne Subscriptions. Nous avons également publié des mini-extensions pour "
"vous aider à tirer le meilleur parti de votre boutique d’abonnement."

#: includes/upgrades/templates/wcs-about.php:108
msgid "But wait, there's more!"
msgstr "Attendez, vous avez encore tant à découvrir !"

#: includes/upgrades/templates/wcs-about.php:99
msgid "View Email Settings"
msgstr "Voir les paramètres d’e-mail"

#: includes/upgrades/templates/wcs-about.php:97
msgid ""
"These emails can be enabled, disabled and customised under the %sWooCommerce "
"> Settings > Emails%s administration screen."
msgstr ""
"Ces e-mails peuvent être activés, désactivés et personnalisés sur l’écran "
"d’administration %sWooCommerce > Réglages > E-mails%s."

#: includes/upgrades/templates/wcs-about.php:95
msgid "a subscription expires"
msgstr "un abonnement expire"

#: includes/upgrades/templates/wcs-about.php:94
msgid "an automatic payment fails"
msgstr "un paiement automatique échoue"

#: includes/upgrades/templates/wcs-about.php:93
msgid "a customer suspends a subscription"
msgstr "un client suspend un abonnement"

#: includes/upgrades/templates/wcs-about.php:91
msgid ""
"Subscriptions 2.1 also introduces a number of new emails to notify you when:"
msgstr ""
"Subscriptions 2.1 introduit également un certain nombre de nouveaux e-mails "
"pour vous informer quand :"

#: includes/upgrades/templates/wcs-about.php:90
msgid "New Subscription Emails"
msgstr "Nouveaux e-mails d’abonnement"

#: includes/upgrades/templates/wcs-about.php:78
msgid "Enable Automatic Retry"
msgstr "Activer la nouvelle tentative automatique"

#: includes/upgrades/templates/wcs-about.php:76
msgid ""
"The retry system is disabled by default. To enable it, visit the "
"Subscriptions settings administration screen."
msgstr ""
"Le système de nouvelle tentative est désactivé par défaut. Pour l’activer, "
"visitez l’écran d’administration de réglages des abonnements."

#: includes/upgrades/templates/wcs-about.php:74
msgid "the status applied to the renewal order and subscription"
msgstr "l’état appliqué à la commande de renouvellement et à l’abonnement"

#: includes/upgrades/templates/wcs-about.php:73
msgid "emails sent to the customer and store manager"
msgstr "les e-mails envoyés au client et au gérant de la boutique"

#: includes/upgrades/templates/wcs-about.php:72
msgid "how long to wait between retry attempts"
msgstr "la durée d’attente entre les nouvelles tentatives"

#: includes/upgrades/templates/wcs-about.php:71
msgid "the total number of retry attempts"
msgstr "le nombre total de nouvelles tentatives"

#: includes/upgrades/templates/wcs-about.php:69
msgid ""
"By default, Subscriptions will retry the payment 5 times over 7 days. The "
"rules that control the retry system can be modified to customise:"
msgstr ""
"Par défaut, Subscriptions tentera le paiement 5 fois pendant 7 jours. Les "
"règles qui contrôlent le système de nouvelle tentative peuvent être "
"modifiées pour personnaliser :"

#: includes/upgrades/templates/wcs-about.php:68
msgid ""
"Failed recurring payments can now be retried automatically. This helps "
"recover revenue that would otherwise be lost due to payment methods being "
"declined only temporarily."
msgstr ""
"Les paiements récurrents échoués peuvent maintenant être retentés "
"automatiquement. Cela permet de récupérer les revenus qui seraient autrement "
"perdus en raison du refus temporaire des moyens de paiement."

#: includes/upgrades/templates/wcs-about.php:67
msgid "Automatic Failed Payment Retry"
msgstr "Nouvelle tentative automatique de paiement échouée"

#: includes/upgrades/templates/wcs-about.php:54
msgid "View Reports"
msgstr "Voir les rapports"

#: includes/upgrades/templates/wcs-about.php:52
msgid ""
"Prior to Subscriptions 2.1, they were not easy to answer. Subscriptions 2.1 "
"introduces new reports to answer these questions, and many more."
msgstr ""
"Avant Subscriptions 2.1, il n’était pas facile d’y répondre. Subscriptions 2."
"1 introduit de nouveaux rapports pour répondre à ces questions, et bien "
"d’autres."

#: includes/upgrades/templates/wcs-about.php:51
msgid "These are important questions for any subscription commerce business."
msgstr ""
"Ce sont des questions importantes pour toute entreprise de commerce par "
"abonnement."

#: includes/upgrades/templates/wcs-about.php:50
msgid ""
"How many customers stay subscribed for more than 6 months? What is the "
"average lifetime value of your subscribers? How much renewal revenue will "
"your store earn next month?"
msgstr ""
"Combien de clients restent abonnés pendant plus de 6 mois ? Quelle est la "
"valeur moyenne de la durée de vie de vos abonnés ? Combien de revenus de "
"renouvellement votre boutique gagnera-t-elle le mois prochain ?"

#: includes/upgrades/templates/wcs-about.php:49
msgid "Subscription Reports"
msgstr "Rapports d’abonnement"

#: includes/upgrades/templates/wcs-about.php:23
msgid ""
"Version 2.1 introduces some great new features requested by store managers "
"just like you (and possibly even by %syou%s)."
msgstr ""
"La version 2.1 introduit de nouvelles fonctionnalités géniales demandées par "
"des gérants de boutique comme vous (et peut-être même par %svous%s)."

#: includes/upgrades/templates/wcs-about.php:19
msgid "Welcome to Subscriptions 2.1!"
msgstr "Bienvenue dans Subscriptions 2.1 !"

#: includes/upgrades/class-wcs-upgrade-2-2-7.php:60
msgid "Subscription end date in the past"
msgstr "Date de fin d’abonnement dans le passé"

#: includes/payment-retry/class-wcs-retry-post-store.php:46
msgid "No retries found"
msgstr "Aucune nouvelle tentative trouvée"

#: includes/payment-retry/class-wcs-retry-post-store.php:45
msgid "Search Renewal Payment Retries"
msgstr "Rechercher de nouvelles tentatives de paiement de renouvellement"

#: includes/payment-retry/class-wcs-retry-post-store.php:43
#: includes/payment-retry/class-wcs-retry-post-store.php:44
msgid "View Retry"
msgstr "Voir la nouvelle tentative"

#: includes/payment-retry/class-wcs-retry-post-store.php:42
msgid "New Retry"
msgstr "Nouvelle tentative"

#: includes/payment-retry/class-wcs-retry-post-store.php:41
msgid "Edit Retry"
msgstr "Modifier la nouvelle tentative"

#: includes/payment-retry/class-wcs-retry-post-store.php:39
msgid "Add New Retry"
msgstr "Ajouter une nouvelle tentative"

#: includes/payment-retry/class-wcs-retry-post-store.php:36
msgid "Renewal Payment Retry"
msgstr "Nouvelle tentative de paiement de renouvellement"

#: includes/payment-retry/class-wcs-retry-post-store.php:25
msgid ""
"Payment retry posts store details about the automatic retry of failed "
"renewal payments."
msgstr ""
"La nouvelle tentative de paiement sauvegarde les détails sur la nouvelle "
"tentative automatique des paiements de renouvellement échoués."

#. translators: 1,2: opening/closing link tags (to documentation).
#: includes/payment-retry/class-wcs-retry-admin.php:150
msgid ""
"Attempt to recover recurring revenue that would otherwise be lost due to "
"payment methods being declined only temporarily. %1$sLearn more%2$s."
msgstr ""
"Tentative de récupération des revenus récurrents qui seraient autrement "
"perdus en raison du refus temporaire des moyens de paiement. %1$sEn savoir "
"plus%2$s."

#: includes/payment-retry/class-wcs-retry-admin.php:145
msgid "Enable automatic retry of failed recurring payments"
msgstr ""
"Activer la nouvelle tentative automatique des paiements de renouvellement "
"échoués"

#: includes/payment-retry/class-wcs-retry-admin.php:144
msgid "Retry Failed Payments"
msgstr "Retenter les paiements échoués"

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:116
msgid "%d Cancelled Payment Retry"
msgid_plural "%d Cancelled Payment Retries"
msgstr[0] "%d Nouvelle tentative de paiement annulée"
msgstr[1] "%d Nouvelles tentatives de paiement annulées"

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:112
msgid "%d Successful Payment Retry"
msgid_plural "%d Successful Payment Retries"
msgstr[0] "%d Nouvelle tentative de paiement réussie"
msgstr[1] "%d Nouvelles tentatives de paiement réussies"

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:108
msgid "%d Failed Payment Retry"
msgid_plural "%d Failed Payment Retries"
msgstr[0] "%d Nouvelle tentative de paiement échouée"
msgstr[1] "%d Nouvelles tentatives de paiement échouées"

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:104
msgid "%d Processing Payment Retry"
msgid_plural "%d Processing Payment Retries"
msgstr[0] "%d Nouvelle tentative de paiement en cours de traitement"
msgstr[1] "%d Nouvelles tentatives de paiement en cours de traitement"

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:100
msgid "%d Pending Payment Retry"
msgid_plural "%d Pending Payment Retries"
msgstr[0] "%d Nouvelle tentative de paiement en attente"
msgstr[1] "%d Nouvelles tentatives de paiement en attente"

#: includes/payment-retry/class-wcs-retry-admin.php:46
msgid "Automatic Failed Payment Retries"
msgstr "Nouvelles tentatives automatiques de paiement échoué"

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:113
msgid "PayPal API error - credentials are incorrect."
msgstr "Erreur d’API PayPal - les identifiants de connexion sont incorrects."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:641
msgid "Invalid PayPal IPN Payload: unable to find matching subscription."
msgstr ""
"Charge utile PayPal IPN non valide : abonnement correspondant introuvable."

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:286
msgid "PayPal Subscription ID:"
msgstr "ID d’abonnement PayPal :"

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:194
msgid "Ignore this error (not recommended)"
msgstr "Ignorer cette erreur (non recommandé)"

#: includes/emails/class-wcs-email-payment-retry.php:30
msgid ""
"[{site_title}] Automatic payment failed for {order_number}, retry scheduled "
"to run {retry_time}"
msgstr ""
"[{site_title}] Paiement automatique échoué pour {order_number}, nouvelle "
"tentative planifiée le {retry_time}"

#: includes/emails/class-wcs-email-payment-retry.php:29
msgid "Automatic renewal payment failed"
msgstr "Paiement de renouvellement automatique échoué"

#: includes/emails/class-wcs-email-payment-retry.php:27
msgid ""
"Payment retry emails are sent to chosen recipient(s) when an attempt to "
"automatically process a subscription renewal payment has failed and a retry "
"rule has been applied to retry the payment in the future."
msgstr ""
"Les e-mails de nouvelle tentative de paiement sont envoyés au(x) "
"destinataire(s) choisi(s) lorsqu’une tentative de traitement automatique "
"d’un paiement de renouvellement d’abonnement a échoué et qu’une règle de "
"nouvelle tentative a été appliquée pour retenter le paiement à l’avenir."

#: includes/emails/class-wcs-email-payment-retry.php:26
msgid "Payment Retry"
msgstr "Nouvelle tentative de paiement"

#: includes/emails/class-wcs-email-on-hold-subscription.php:29
msgid "Subscription Suspended"
msgstr "Abonnement suspendu"

#: includes/emails/class-wcs-email-on-hold-subscription.php:27
msgid ""
"Suspended Subscription emails are sent when a customer manually suspends "
"their subscription."
msgstr ""
"Des e-mails Abonnement suspendu sont envoyés lorsqu’un client suspend "
"manuellement son abonnement."

#: includes/emails/class-wcs-email-on-hold-subscription.php:26
msgid "Suspended Subscription"
msgstr "Abonnement suspendu"

#: includes/emails/class-wcs-email-expired-subscription.php:78
#: includes/emails/class-wcs-email-on-hold-subscription.php:78
msgid "Subscription argument passed in is not an object."
msgstr "L’argument d’abonnement transmis n’est pas un objet."

#: includes/emails/class-wcs-email-expired-subscription.php:29
msgid "Subscription Expired"
msgstr "Abonnement expiré"

#: includes/emails/class-wcs-email-expired-subscription.php:27
msgid ""
"Expired Subscription emails are sent when a customer's subscription expires."
msgstr ""
"Des e-mails Abonnement expiré sont envoyés lorsque l’abonnement d’un client "
"expire."

#: includes/emails/class-wcs-email-expired-subscription.php:26
msgid "Expired Subscription"
msgstr "Abonnement expiré"

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:41
msgid ""
"Sent to a customer when the subscription is due for renewal and the renewal "
"requires a manual payment, either because it uses manual renewals or the "
"automatic recurring payment failed for the initial attempt and all automatic "
"retries (if any). The email contains renewal order information and payment "
"links."
msgstr ""
"Envoyé à un client lorsque l’abonnement doit être renouvelé et que le "
"renouvellement nécessite un paiement manuel, soit parce qu’il utilise des "
"renouvellements manuels, soit parce que le paiement récurrent automatique a "
"échoué à la première tentative et toutes les nouvelles tentatives "
"automatiques (le cas échéant). L’e-mail contient des informations sur la "
"commande de renouvellement et des liens de paiement."

#: includes/emails/class-wcs-email-customer-payment-retry.php:33
msgid "Automatic payment failed for order {order_number}"
msgstr "Paiement automatique échoué pour la commande {order_number}"

#: includes/emails/class-wcs-email-customer-payment-retry.php:32
msgid "Automatic payment failed for {order_number}, we will retry {retry_time}"
msgstr ""
"Paiement automatique échoué pour {order_number}, nous retenterons le "
"{retry_time}"

#: includes/emails/class-wcs-email-customer-payment-retry.php:25
msgid ""
"Sent to a customer when an attempt to automatically process a subscription "
"renewal payment has failed and a retry rule has been applied to retry the "
"payment in the future. The email contains the renewal order information, "
"date of the scheduled retry and payment links to allow the customer to pay "
"for the renewal order manually instead of waiting for the automatic retry."
msgstr ""
"Envoyé à un client lorsqu’une tentative de traitement automatique d’un "
"paiement de renouvellement d’abonnement a échoué et qu’une règle de nouvelle "
"tentative a été appliquée pour retenter le paiement à l’avenir. L’e-mail "
"contient des informations sur la commande de renouvellement, la date de la "
"nouvelle tentative planifiée et des liens de paiement pour permettre au "
"client de payer la commande de renouvellement manuellement au lieu "
"d’attendre la nouvelle tentative automatique."

#: includes/emails/class-wcs-email-customer-payment-retry.php:24
msgid "Customer Payment Retry"
msgstr "Nouvelle tentative de paiement du client"

#: includes/class-wcs-webhooks.php:113
msgid " Subscription switched"
msgstr "Abonnement changé"

#: includes/class-wcs-query.php:297
msgid "Endpoint for the My Account &rarr; View Subscription page"
msgstr ""
"Point de terminaison pour la page Mon compte &rarr; Afficher l’abonnement"

#: includes/class-wcs-query.php:288
msgid "Endpoint for the My Account &rarr; Subscriptions page"
msgstr "Point de terminaison pour la page Mon compte &rarr; Abonnements"

#: includes/class-wcs-query.php:131
msgid "My Subscription"
msgstr "Mon abonnement"

#. translators: $1: the token/credit card label, 2$-3$: opening and closing
#. strong and link tags
#: includes/class-wcs-my-account-payment-methods.php:103
msgid ""
"The deleted payment method was used for automatic subscription payments. To "
"avoid failed renewal payments in future the subscriptions using this payment "
"method have been updated to use your %1$s. To change the payment method of "
"individual subscriptions go to your %2$sMy Account > Subscriptions%3$s page."
msgstr ""
"Le moyen de paiement supprimé était utilisé pour les paiements d’abonnement "
"automatiques. Pour éviter l’échec des paiements de renouvellement à l’avenir,"
" les abonnements utilisant ce moyen de paiement ont été mis à jour pour "
"utiliser votre %1$s. Pour modifier le moyen de paiement d’abonnements "
"individuels, allez sur la page %2$sMon compte > Abonnements%3$s."

#: includes/class-wcs-my-account-payment-methods.php:80
msgid ""
"The deleted payment method was used for automatic subscription payments, we "
"couldn't find an alternative token payment method token to change your "
"subscriptions to."
msgstr ""
"Le moyen de paiement supprimé était utilisé pour les paiements d’abonnement "
"automatiques. Nous n’avons pas pu trouver un autre jeton de moyen de "
"paiement pour modifier vos abonnements."

#. translators: %s: order number.
#: includes/class-wcs-cart-resubscribe.php:320
msgid "Customer resubscribed in order #%s"
msgstr "Client réabonné dans la commande n°%s"

#: includes/class-wcs-cart-renewal.php:227
msgid "Complete checkout to renew your subscription."
msgstr "Terminez la validation de commande pour renouveler votre abonnement."

#: includes/class-wcs-cached-data-manager.php:225
msgid "Weekly"
msgstr "Hebdomadaire"

#: includes/class-wc-subscriptions-synchroniser.php:311
msgid "Month for Synchronisation"
msgstr "Mois de synchronisation"

#: includes/class-wc-subscriptions-synchroniser.php:48
msgid "Synchronise renewals"
msgstr "Synchroniser les renouvellements"

#: includes/class-wc-subscriptions-switcher.php:1974
msgid "The item on the switch order cannot be found."
msgstr "L’article sur la commande de changement est introuvable."

#: includes/class-wc-subscriptions-switcher.php:1972
msgid "The original subscription item being switched cannot be found."
msgstr ""
"L’article d’abonnement original en cours de changement est introuvable."

#: includes/class-wc-subscriptions-switcher.php:1377
msgid "You can only switch to a subscription product."
msgstr "Vous pouvez passer uniquement à un produit d’abonnement."

#. translators: %s: order number.
#: includes/class-wc-subscriptions-switcher.php:1126
msgid "Switch order cancelled due to a new switch order being created #%s."
msgstr ""
"Commande de changement annulée en raison d’une nouvelle commande de "
"changement créée n° %s."

#: includes/class-wc-subscriptions-order.php:745
msgid "All orders types"
msgstr "Tous les types de commande"

#: includes/class-wc-subscriptions-order.php:473
msgid "Parent Order"
msgstr "Commande parente"

#: includes/class-wc-subscriptions-order.php:471
msgid "Resubscribe Order"
msgstr "Commande de réabonnement"

#: includes/class-wc-subscriptions-order.php:469
msgid "Renewal Order"
msgstr "Commande de renouvellement"

#: includes/class-wc-subscriptions-order.php:449
msgid "Subscription Relationship"
msgstr "Relation d’abonnement"

#. translators: placeholder is a subscription ID.
#. translators: %d: subscription ID.
#: includes/class-wc-subscriptions-manager.php:167
#: includes/gateways/class-wc-subscriptions-payment-gateways.php:215
msgid "Subscription doesn't exist in scheduled action: %d"
msgstr "L’abonnement n’existe pas dans l’action planifiée : %d"

#. translators: placeholder is an order note.
#: includes/class-wc-subscriptions-manager.php:124
msgid "Error: Unable to create renewal order with note \"%s\""
msgstr ""
"Erreur : impossible de créer une commande de renouvellement avec la note "
"« %s »"

#: includes/class-wc-subscriptions-coupon.php:714
msgid "Renewal Discount"
msgstr "Remise sur le renouvellement"

#: includes/class-wc-subscriptions-coupon.php:696
msgid "Renewal cart discount"
msgstr "Remise sur le panier de renouvellement"

#: includes/class-wc-subscriptions-coupon.php:695
msgid "Renewal product discount"
msgstr "Remise sur le produit de renouvellement"

#: includes/class-wc-subscriptions-coupon.php:694
msgid "Renewal % discount"
msgstr "Remise % sur le renouvellement"

#: includes/class-wc-subscriptions-coupon.php:512
msgid "Sorry, only recurring coupons can be applied to subscriptions."
msgstr ""
"Désolé, seuls les codes promo récurrents peuvent être appliqués aux "
"abonnements."

#. translators: placeholder is coupon code
#: includes/class-wc-subscriptions-coupon.php:509
msgid ""
"Sorry, \"%s\" can only be applied to subscription parent orders which "
"contain a product with signup fees."
msgstr ""
"Désolé, « %s » ne peut être appliqué qu’aux commandes parentes d’abonnement "
"qui contiennent un produit avec des frais d’inscription."

#: includes/class-wc-subscriptions-coupon.php:505
msgid ""
"Sorry, recurring coupons can only be applied to subscriptions or "
"subscription orders."
msgstr ""
"Désolé, les codes promo récurrents ne peuvent être appliqués qu’à des "
"abonnements ou des commandes d’abonnement."

#: includes/class-wc-subscriptions-cart.php:1240
msgid "Invalid recurring shipping method."
msgstr "Méthode de livraison récurrente non valide."

#: includes/class-wc-subscription.php:2462
#: includes/class-wc-subscriptions-checkout.php:343
msgid "Backordered"
msgstr "En cours d&rsquo;approvisionnement"

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2410
msgid "The %s date must occur after the cancellation date."
msgstr "La date de %s doit être postérieure à la date d’annulation."

#. translators: %d: subscription ID.
#. translators: %d: order ID.
#: includes/class-wc-subscription.php:1343
#: includes/class-wc-subscription.php:2442
msgid "Subscription #%d: "
msgstr "Abonnement n° %d :"

#: includes/class-wc-subscription.php:1213
msgid "Not cancelled"
msgstr "Pas annulé"

#. translators: %s: new order status
#: includes/class-wc-subscription.php:590
msgid "Status set to %s."
msgstr "État défini sur %s."

#. translators: 1: subscription status, 2: error message.
#: includes/class-wc-subscription.php:546
msgid "Unable to change subscription status to \"%1$s\". Exception: %2$s"
msgstr ""
"Impossible de modifier l’état d’abonnement sur « %1$s ». Exception : %2$s"

#. translators: placeholder is an error message.
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:242
msgid "Cannot create subscription: %s."
msgstr "Impossible de créer l’abonnement : %s."

#: includes/api/class-wc-rest-subscriptions-controller.php:541
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:388
msgid "The subscription's end date."
msgstr "Date de fin de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:536
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:383
msgid "The subscription's next payment date."
msgstr "Date du prochain paiement de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:531
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:378
msgid "The subscription's trial date"
msgstr "Date d’essai de l’abonnement"

#: includes/api/class-wc-rest-subscriptions-controller.php:526
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:373
msgid "The subscription's start date."
msgstr "Date de début de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:519
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:366
msgid "Payment gateway ID."
msgstr "ID de la passerelle de paiement."

#: includes/api/class-wc-rest-subscriptions-controller.php:514
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:361
msgid "Subscription payment details."
msgstr "Détails de paiement de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:508
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:355
msgid "Billing period for the subscription."
msgstr "Période de facturation de l’abonnement."

#: includes/api/class-wc-rest-subscriptions-controller.php:503
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:350
msgid "The number of billing periods between subscription renewals."
msgstr ""
"Nombre de périodes de facturation entre les renouvellements d’abonnement."

#. translators: placeholder is an error message.
#: includes/api/class-wc-rest-subscriptions-controller.php:472
#: includes/api/class-wc-rest-subscriptions-controller.php:778
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:287
msgid "Updating subscription dates errored with message: %s"
msgstr "Erreur de mise à jour des dates d’abonnement avec le message : %s"

#. translators: 1$: gateway id, 2$: error message
#: includes/api/class-wc-rest-subscriptions-controller.php:402
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:336
msgid ""
"Subscription payment method could not be set to %1$s with error message: %2$s"
msgstr ""
"Le moyen de paiement de l’abonnement n’a pas pu être défini sur %1$s avec le "
"message d’erreur : %2$s"

#: includes/api/class-wc-rest-subscriptions-controller.php:184
msgid "Customer ID is invalid."
msgstr "L&rsquo;ID client est non valide."

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:256
msgid "Renewals amount"
msgstr "Montant des renouvellements"

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:247
msgid "Renewals count"
msgstr "Nombre de renouvellements"

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:182
msgid "Next 7 Days"
msgstr "7 prochains jours"

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:181
msgid "Next Month"
msgstr "Mois suivant"

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:180
msgid "Next 30 Days"
msgstr "30 prochains jours"

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:179
msgid "Next 12 Months"
msgstr "12 prochains mois"

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:103
msgid "%s average renewal amount"
msgstr "%s montant de renouvellement moyen"

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:89
msgid "%s renewal income in this period"
msgstr "%s revenus de renouvellement sur cette période"

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:295
msgid "Recovered Renewal Revenue"
msgstr "Revenu de renouvellement récupéré"

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:279
msgid "Pending retries"
msgstr "Nouvelles tentatives en attente"

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:263
msgid "Failed retries"
msgstr "Nouvelles tentatives échouées"

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:247
msgid "Successful retries"
msgstr "Nouvelles tentatives réussies"

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:145
msgid "The number of renewal payment retries not yet processed."
msgstr ""
"Nombre de nouvelles tentatives de renouvellement de paiement non encore "
"traitées."

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:144
msgid "%s retry attempts pending"
msgstr "%s nouvelles tentatives en attente"

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:137
msgid ""
"The number of renewal payment retries for this period which did not result "
"in a successful payment."
msgstr ""
"Nombre de nouvelles tentatives de paiement de renouvellement pour cette "
"période qui n’ont pas abouti à un paiement réussi."

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:136
msgid "%s retry attempts failed"
msgstr "%s nouvelles tentatives échouées"

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:129
msgid ""
"The number of renewal payment retries for this period which were able to "
"process the payment which had previously failed one or more times."
msgstr ""
"Nombre de nouvelles tentatives de paiement de renouvellement pour cette "
"période qui ont pu traiter le paiement qui avait échoué précédemment une ou "
"plusieurs fois."

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:128
msgid "%s retry attempts succeeded"
msgstr "%s nouvelles tentatives réussies"

#. translators: %s: renewal count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:122
msgid ""
"The number of renewal orders which had a failed payment use the retry system."
msgstr ""
"Nombre de commandes de renouvellement dont le paiement échoué utilise le "
"système de nouvelle tentative."

#. translators: %s: renewal count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:121
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:96
msgid "%s renewal orders"
msgstr "%s commandes de renouvellement"

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:114
msgid ""
"The total amount of revenue, including tax and shipping, recovered with the "
"failed payment retry system for renewal orders with a failed payment."
msgstr ""
"Montant total des revenus, y compris les frais d’expédition et de taxe, "
"récupéré avec le système de nouvelle tentative de paiement échoué pour les "
"commandes de renouvellement avec un paiement échoué."

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:113
msgid "%s renewal revenue recovered"
msgstr "%s revenu de renouvellement récupéré"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:1004
msgid "Renewal Totals"
msgstr "Totaux de renouvellements"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:984
msgid "Resubscribe Totals"
msgstr "Totaux de réabonnements"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:964
msgid "Signup Totals"
msgstr "Totaux d’inscriptions"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:949
msgid "Cancellations"
msgstr "Annulations"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:901
msgid "Number of renewals"
msgstr "Nombre de renouvellements"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:886
msgid "Number of resubscribes"
msgstr "Nombre de réabonnements"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:871
msgid "Subscriptions signups"
msgstr "Inscriptions d’abonnements"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:839
msgid "Switched subscriptions"
msgstr "Abonnements changés"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:780
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:199
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:213
msgid "Export CSV"
msgstr "Exporter un CSV"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:735
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:162
msgid "Last 7 Days"
msgstr "7 derniers jours"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:734
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:161
msgid "This Month"
msgstr "Ce mois-ci"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:733
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:160
msgid "Last Month"
msgstr "Mois dernier"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:732
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:159
msgid "Year"
msgstr "Année"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:718
msgid "Change in subscriptions between the start and end of the period."
msgstr "Changement des abonnements entre le début et la fin de la période."

#. translators: %s: subscription net loss (with percentage).
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:713
msgid "%s net subscription loss"
msgstr "%s perte nette d’abonnement"

#. translators: %s: subscription net gain (with percentage).
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:710
msgid "%s net subscription gain"
msgstr "%s gain net d’abonnement"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:693
msgid ""
"The number of subscriptions during this period with an end date in the "
"future and a status other than pending."
msgstr ""
"Nombre d’abonnements pendant cette période avec une date de fin dans le "
"futur et un état autre que en attente."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:667
msgid ""
"The number of subscriptions which have either expired or reached the end of "
"the prepaid term if it was previously cancelled."
msgstr ""
"Nombre d’abonnements qui ont expiré ou ont atteint la fin du terme prépayé "
"s’il était précédemment annulé."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:645
msgid ""
"The number of subscriptions cancelled by the customer or store manager "
"during this period.  The pre-paid term may not yet have ended during this "
"period."
msgstr ""
"Nombre d’abonnements annulés par le client ou le gérant de la boutique "
"pendant cette période. Le terme prépayé peut ne pas être encore terminé "
"pendant cette période."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:623
msgid ""
"The number of subscriptions upgraded, downgraded or cross-graded during this "
"period."
msgstr ""
"Nombre d’abonnements mis à niveau, rétrogradés ou reclassés pendant cette "
"période."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:601
msgid "The number of renewal orders processed during this period."
msgstr "Nombre de commandes de renouvellement traitées pendant cette période."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:579
msgid "The number of resubscribe orders processed during this period."
msgstr "Nombre de commandes de réabonnement traitées pendant cette période."

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:557
msgid ""
"The number of subscriptions purchased in parent orders created during this "
"period. This represents the new subscriptions created by customers placing "
"an order via checkout."
msgstr ""
"Nombre d’abonnements achetés dans les commandes parentes créées pendant "
"cette période. Cela représente les nouveaux abonnements créés par les "
"clients passant une commande via la validation de commande."

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:505
msgid "The sum of all resubscribe orders including tax and shipping."
msgstr ""
"Somme de toutes les commandes de réabonnement, incluant les frais "
"d’expédition et de taxe."

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:504
msgid "%s resubscribe revenue in this period"
msgstr "%s revenu de réabonnement sur cette période"

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:497
msgid "The sum of all renewal orders including tax and shipping."
msgstr ""
"Somme de toutes les commandes de renouvellement, incluant les frais "
"d’expédition et de taxe."

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:496
msgid "%s renewal revenue in this period"
msgstr "%s revenu de renouvellement sur cette période"

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:489
msgid ""
"The sum of all subscription parent orders, including other items, fees, tax "
"and shipping."
msgstr ""
"Somme de toutes les commandes parentes d’abonnement, incluant d’autres "
"articles, les frais, les frais d’expédition et de taxe."

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:488
msgid "%s signup revenue in this period"
msgstr "%s revenu d’inscription sur cette période"

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:300
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:340
msgid "subscriptions"
msgstr "abonnements"

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:95
msgid "The average line total on all orders for this product line item."
msgstr ""
"Total moyen de la ligne pour toutes les commandes de cet article dans la "
"ligne de produit."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:95
msgid "Average Lifetime Value %s"
msgstr "Valeur moyenne de la durée de vie %s"

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:93
msgid "The average line total for this product on each subscription."
msgstr "Total moyen de la ligne pour ce produit sur chaque abonnement."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:93
msgid "Average Recurring Line Total %s"
msgstr "Total moyen de la ligne récurrente %s"

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:91
msgid ""
"The number of subscriptions that include this product as a line item and "
"have a status other than pending or trashed."
msgstr ""
"Nombre d’abonnements qui incluent ce produit en tant qu'article de la ligne "
"et dont l’état n’est pas en attente ou déplacé vers la corbeille."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:91
msgid "Subscription Count %s"
msgstr "Nombre d’abonnements %s"

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:89
msgid "Subscription Product"
msgstr "Produit d’abonnement"

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:30
msgid "No products found."
msgstr "Aucun produit trouvé."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:105
msgid "The total value of this customer's sign-up, switch and renewal orders."
msgstr ""
"Valeur totale des commandes d’inscription, de changement et de "
"renouvellement de ce client."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:105
msgid "Lifetime Value from Subscriptions %s"
msgstr "Valeur à vie des abonnements %s"

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:103
msgid ""
"The number of sign-up, switch and renewal orders this customer has placed "
"with your store with a paid status (i.e. processing or complete)."
msgstr ""
"Nombre des commandes d’inscription, de changement et de renouvellement "
"placées par ce client avec votre boutique avec un état payé (c.-à-d. en "
"cours de traitement ou terminé)."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:103
msgid "Total Subscription Orders %s"
msgstr "Total des commandes d’abonnement %s"

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:101
msgid ""
"The number of subscriptions this customer has with a status other than "
"pending or trashed."
msgstr ""
"Nombre d’abonnements de ce client avec un état autre que en attente ou "
"déplacé vers la corbeille."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:101
msgid "Total Subscriptions %s"
msgstr "Total des abonnements %s"

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:99
msgid ""
"The number of subscriptions this customer has with a status of active or "
"pending cancellation."
msgstr ""
"Nombre d’abonnements de ce client avec un état d’annulation active ou en "
"attente."

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:99
msgid "Active Subscriptions %s"
msgstr "Abonnements actifs %s"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:48
msgid "Average Lifetime Value"
msgstr "Valeur moyenne de la durée de vie"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:47
msgid "Total Subscription Orders"
msgstr "Total des commandes d’abonnement"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:46
msgid "Total Subscriptions"
msgstr "Total des abonnements"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:45
msgid "Active Subscriptions"
msgstr "Abonnements actifs"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:44
msgid "Total Subscribers"
msgstr "Total des abonnés"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:32
msgid "No customers found."
msgstr "Aucun client."

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:23
msgid "Customers"
msgstr "Clients"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:22
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:97
msgid "Customer"
msgstr "Client"

#: includes/admin/reports/class-wcs-report-retention-rate.php:226
msgid "Unended Subscription Count"
msgstr "Nombre d’abonnements non terminés"

#. translators: %d refers to the number of times we have detected cache update
#. failures
#: includes/admin/reports/class-wcs-report-cache-manager.php:323
msgid "%d failures"
msgid_plural "%d failure"
msgstr[0] "%d échecs"
msgstr[1] "%d échec"

#: includes/admin/reports/class-wcs-report-cache-manager.php:320
msgid "Cache Update Failures"
msgstr "Échecs de mise à jour du cache"

#: includes/admin/reports/class-wcs-report-cache-manager.php:265
msgid ""
"Please note: data for this report is cached. The data displayed may be out "
"of date by up to 24 hours. The cache is updated each morning at 4am in your "
"site's timezone."
msgstr ""
"Veuillez noter : les données de ce rapport sont mises en cache. Les données "
"affichées peuvent être obsolètes jusqu’à 24 heures. Le cache est mis à jour "
"chaque matin à 4 h dans le fuseau horaire de votre site."

#: includes/admin/meta-boxes/views/html-retries-table.php:32
msgid ""
"The email sent to the customer when the renewal payment or payment retry "
"failed to notify them that the payment would be retried."
msgstr ""
"E-mail envoyé au client lorsque le paiement de renouvellement ou la nouvelle "
"tentative de paiement n’a pas pu l’informer que le paiement serait retenté."

#: includes/admin/meta-boxes/views/html-retries-table.php:28
msgid ""
"The status applied to the subscription for the time between when the renewal "
"payment failed or last retry occurred and when this retry was processed."
msgstr ""
"État appliqué à l’abonnement pour le temps écoulé entre l’échec du paiement "
"de renouvellement ou la dernière tentative et le traitement de cette "
"nouvelle tentative."

#: includes/admin/meta-boxes/views/html-retries-table.php:27
msgid "Status of Subscription"
msgstr "État de l’abonnement"

#: includes/admin/meta-boxes/views/html-retries-table.php:24
msgid ""
"The status applied to the order for the time between when the renewal "
"payment failed or last retry occurred and when this retry was processed."
msgstr ""
"État appliqué à la commande pour le temps écoulé entre l’échec du paiement "
"de renouvellement ou la dernière tentative et le traitement de cette "
"nouvelle tentative."

#: includes/admin/meta-boxes/views/html-retries-table.php:23
msgid "Status of Order"
msgstr "État de la commande"

#: includes/admin/meta-boxes/views/html-retries-table.php:20
msgid ""
"The status of the automatic payment retry: pending means the retry will be "
"processed in the future, failed means the payment was not successful when "
"retried and completed means the payment succeeded when retried."
msgstr ""
"État de la nouvelle tentative de paiement automatique : en attente signifie "
"que la nouvelle tentative sera traitée à l’avenir, échoué signifie que le "
"paiement a échoué lors de la nouvelle tentative et terminé signifie que le "
"paiement a réussi lors de la nouvelle tentative."

#: includes/admin/meta-boxes/views/html-retries-table.php:19
msgid "Retry Status"
msgstr "État de la nouvelle tentative"

#: includes/admin/meta-boxes/views/html-retries-table.php:17
msgid "Retry Date"
msgstr "Date de la nouvelle tentative"

#. translators: placeholder is error message from the payment gateway or
#. subscriptions when updating the status
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:401
msgid "Error updating some information: %s"
msgstr "Erreur lors de la mise à jour de certaines informations : %s"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:264
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:294
msgid "Customer Provided Note"
msgstr "Note fournie par le client"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:231
msgid "Load shipping address"
msgstr "Charger l&rsquo;adresse de livraison"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:140
msgid "Load billing address"
msgstr "Charger l&rsquo;adresse de facturation"

#. Author of the plugin
#: includes/admin/class-wcs-admin-reports.php:104
#: includes/admin/reports/class-wcs-report-cache-manager.php:262
msgid "WooCommerce"
msgstr "WooCommerce"

#: includes/admin/class-wcs-admin-reports.php:83
msgid "Failed Payment Retries"
msgstr "Nouvelles tentatives de paiement échouées"

#: includes/admin/class-wcs-admin-reports.php:73
msgid "Subscriptions by Customer"
msgstr "Abonnements par client"

#: includes/admin/class-wcs-admin-reports.php:67
msgid "Subscriptions by Product"
msgstr "Abonnements par produit"

#: includes/admin/class-wcs-admin-reports.php:61
msgid "Retention Rate"
msgstr "Taux de conservation"

#: includes/admin/class-wcs-admin-reports.php:55
msgid "Upcoming Recurring Revenue"
msgstr "Revenus récurrents à venir"

#: includes/admin/class-wcs-admin-reports.php:49
msgid "Subscription Events by Date"
msgstr "Événements d’abonnement par date"

#. translators: 1: user display name 2: user ID 3: user email
#: includes/admin/class-wcs-admin-post-types.php:1107
msgid "%1$s (#%2$s &ndash; %3$s)"
msgstr "%1$s (n°%2$s – %3$s)"

#: includes/admin/class-wcs-admin-post-types.php:654
msgid ""
"This date should be treated as an estimate only. The payment gateway for "
"this subscription controls when payments are processed."
msgstr ""
"Cette date ne doit être considérée que comme une estimation. La passerelle "
"de paiement pour cet abonnement contrôle le moment où les paiements sont "
"traités."

#: includes/admin/class-wcs-admin-post-types.php:651
msgid "Y/m/d g:i:s A"
msgstr ""
"d/m/Y à G h i mi\n"
" s s"

#: includes/admin/class-wcs-admin-post-types.php:569
msgid "Show more details"
msgstr "Afficher plus de détails"

#: includes/admin/class-wcs-admin-post-types.php:429
msgid "Last Order Date"
msgstr "Date de la dernière commande"

#: includes/admin/class-wcs-admin-meta-boxes.php:187
msgid "Retry Renewal Payment"
msgstr "Retenter le paiement de renouvellement"

#: includes/admin/class-wcs-admin-meta-boxes.php:144
msgid ""
"Are you sure you want to retry payment for this renewal order?\n"
"\n"
"This will attempt to charge the customer and send renewal order emails (if "
"emails are enabled)."
msgstr ""
"Voulez-vous vraiment retenter le paiement pour cette commande de "
"renouvellement ?\n"
"\n"
"Cela tentera de facturer le client et d’envoyer des e-mails de commande de "
"renouvellement (si les e-mails sont activés)."

#. translators: placeholders are opening and closing link tags
#: includes/admin/class-wc-subscriptions-admin.php:1925
msgid ""
"Payment gateways which don't support automatic recurring payments can be "
"used to process %1$smanual subscription renewal payments%2$s."
msgstr ""
"Les passerelles de paiement qui ne prennent pas en charge les paiements "
"récurrents automatiques peuvent être utilisées pour traiter les "
"%1$spaiements de renouvellement d’abonnement manuels%2$s."

#: includes/admin/class-wc-subscriptions-admin.php:1917
msgid "Recurring Payments"
msgstr "Paiements récurrents"

#. translators: $1-2: opening and closing tags of a link that takes to Woo
#. marketplace / Stripe product page
#: includes/admin/class-wc-subscriptions-admin.php:1912
msgid ""
"No payment gateways capable of processing automatic subscription payments "
"are enabled. If you would like to process automatic payments, we recommend "
"the %1$sfree Stripe extension%2$s."
msgstr ""
"Aucune passerelle de paiement capable de traiter les paiements d’abonnement "
"automatique n’est activée. Si vous voulez traiter les paiements automatiques,"
" nous vous recommandons l’%1$sextension Stripe gratuite%2$s."

#: includes/admin/class-wc-subscriptions-admin.php:1893
msgid ""
"This subscription is no longer editable because the payment gateway does not "
"allow modification of recurring amounts."
msgstr ""
"Cet abonnement n’est plus modifiable car la passerelle de paiement "
"n’autorise pas la modification des montants récurrents."

#: includes/admin/class-wc-subscriptions-admin.php:1889
msgid "Subscription items can no longer be edited."
msgstr "Les articles d’abonnement ne peuvent plus être modifiés."

#: includes/admin/class-wc-subscriptions-admin.php:1335
msgid ""
"Allow a subscription product to be purchased with other products and "
"subscriptions in the same transaction."
msgstr ""
"Autoriser l’achat d’un produit d’abonnement avec d’autres produits et "
"abonnements dans la même transaction."

#. translators: placeholders are opening and closing link tags
#: includes/admin/class-wc-subscriptions-admin.php:1301
msgid ""
"If you don't want new subscription purchases to automatically charge renewal "
"payments, you can turn off automatic payments. Existing automatic "
"subscriptions will continue to charge customers automatically. %1$sLearn "
"more%2$s."
msgstr ""
"Si vous ne souhaitez pas que les nouveaux achats d’abonnement facturent "
"automatiquement les paiements de renouvellement, vous pouvez désactiver les "
"paiements automatiques. Les abonnements automatiques existants continueront "
"de facturer les clients automatiquement. %1$sEn savoir plus%2$s."

#. translators: %s: subscription status.
#: includes/admin/class-wc-subscriptions-admin.php:778
msgid ""
"Unable to change subscription status to \"%s\". Please assign a customer to "
"the subscription to activate it."
msgstr ""
"Impossible de modifier l’état d’abonnement sur « %s ». Veuillez assigner un "
"client à l’abonnement pour l’activer."

#: includes/admin/class-wc-subscriptions-admin.php:330
msgid ""
"Automatically expire the subscription after this length of time. This length "
"is in addition to any free trial or amount of time provided before a "
"synchronised first renewal date."
msgstr ""
"Expire automatiquement l’abonnement après cette durée. Cette durée s’ajoute "
"à tout essai gratuit ou à toute durée accordée avant une première date de "
"renouvellement synchronisée."

#: includes/admin/class-wc-subscriptions-admin.php:327
#: includes/admin/class-wc-subscriptions-admin.php:451
#: templates/admin/html-variation-price.php:66
msgid "Expire after"
msgstr "Expire après"

#: includes/admin/class-wc-subscriptions-admin.php:306
msgid "Subscription interval"
msgstr "Intervalle d’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:285
msgid "Choose the subscription price, billing interval and period."
msgstr ""
"Choisissez le prix de l’abonnement, l’intervalle de facturation et la "
"période."

#: tests/unit/scheduler/scheduler.php:66 wcs-functions.php:304
msgctxt "table heading"
msgid "Trial End"
msgstr "Fin de l’essai"

#: wcs-functions.php:277
msgid "Can not get address type display name. Address type is not a string."
msgstr ""
"Impossible d’obtenir le nom d’affichage du type d’adresse. Le type d’adresse "
"n’est pas une chaîne."

#. translators: placeholder is order date parsed by strftime
#: wcs-functions.php:163
msgctxt "The post title for the new subscription"
msgid "Subscription &ndash; %s"
msgstr "Abonnement &ndash; %s"

#: wcs-functions.php:142
msgctxt "Error message while creating a subscription"
msgid "Invalid subscription customer_id."
msgstr "Abonnement non valide customer_id."

#: wcs-functions.php:132
msgctxt "Error message while creating a subscription"
msgid "Subscription created date must be before current day."
msgstr ""
"La date de création de l’abonnement doit être antérieure au jour en cours."

#: wcs-functions.php:137
msgctxt "Error message while creating a subscription"
msgid ""
"Invalid date. The date must be a string and of the format: \"Y-m-d H:i:s\"."
msgstr ""
"Date non valide. La date doit être une chaîne au format : « Y-m-d H:i:s »."

#: templates/single-product/add-to-cart/variable-subscription.php:23
msgid "This product is currently out of stock and unavailable."
msgstr "Ce produit est actuellement en rupture et indisponible."

#. translators: $1: formatted order total for the order, $2: number of items
#. bought
#: templates/myaccount/related-orders.php:56
msgid "%1$s for %2$d item"
msgid_plural "%1$s for %2$d items"
msgstr[0] "%1$s pour %2$d article"
msgstr[1] "%1$s pour %2$d articles"

#: templates/myaccount/my-subscriptions.php:46
#: templates/myaccount/related-orders.php:53
#: templates/myaccount/related-subscriptions.php:42
msgctxt "Used in data attribute. Escaped"
msgid "Total"
msgstr "Total"

#: templates/myaccount/my-subscriptions.php:40
#: tests/unit/scheduler/scheduler.php:67 wcs-functions.php:305
msgctxt "table heading"
msgid "Next Payment"
msgstr "Paiement suivant"

#: tests/unit/scheduler/scheduler.php:70 wcs-functions.php:308
msgctxt "table heading"
msgid "End Date"
msgstr "Date de fin"

#: tests/unit/scheduler/scheduler.php:65 wcs-functions.php:303
msgctxt "table heading"
msgid "Start Date"
msgstr "Date de début"

#. translators: placeholder is localised start date
#: templates/emails/plain/subscription-info.php:29
msgctxt "in plain emails for subscription information"
msgid "Start date: %s"
msgstr ""

#. translators: placeholder is localised end date, or "when cancelled"
#: templates/emails/plain/subscription-info.php:33
msgctxt "in plain emails for subscription information"
msgid "End date: %s"
msgstr "Date de fin : %s"

#: templates/emails/plain/subscription-info.php:31
msgctxt "Used as end date for an indefinite subscription"
msgid "When Cancelled"
msgstr "En cas d’annulation"

#. translators: placeholder is the formatted order total for the subscription
#: templates/emails/plain/subscription-info.php:35
msgctxt "in plain emails for subscription information"
msgid "Recurring price: %s"
msgstr "Prix récurrent : %s"

#. translators: placeholder is subscription's number
#: templates/emails/plain/subscription-info.php:25
msgctxt "in plain emails for subscription information"
msgid "Subscription: %s"
msgstr "Abonnement : %s"

#: templates/emails/plain/cancelled-subscription.php:44
#: templates/emails/plain/expired-subscription.php:44
#: templates/emails/plain/on-hold-subscription.php:40
msgctxt "in plain emails for subscription information"
msgid "View Subscription: %s"
msgstr "Afficher l’abonnement : %s"

#. translators: %1$s: name of the blog, %2$s: link to checkout payment url,
#. note: no full stop due to url at the end
#: templates/emails/customer-renewal-invoice.php:31
#: templates/emails/plain/customer-renewal-invoice.php:23
msgctxt "In customer renewal invoice email"
msgid ""
"The automatic payment to renew your subscription with %1$s has failed. To "
"reactivate the subscription, please log in and pay for the renewal from your "
"account page: %2$s"
msgstr ""
"Le paiement automatique pour renouveler votre abonnement avec %1$s a échoué. "
"Pour réactiver l’abonnement, veuillez vous connecter et payer le "
"renouvellement sur la page de votre compte : %2$s"

#. translators: %1$s: name of the blog, %2$s: link to checkout payment url,
#. note: no full stop due to url at the end
#: templates/emails/customer-renewal-invoice.php:22
#: templates/emails/plain/customer-renewal-invoice.php:20
msgctxt "In customer renewal invoice email"
msgid ""
"An order has been created for you to renew your subscription on %1$s. To pay "
"for this invoice please use the following link: %2$s"
msgstr ""
"Une commande a été créée pour vous afin de renouveler votre abonnement sur "
"%1$s. Pour payer cette facture, veuillez utiliser le lien suivant : %2$s"

#: templates/emails/cancelled-subscription.php:24
msgctxt "table headings in notification email"
msgid "End of Prepaid Term"
msgstr "Fin du terme prépayé"

#: templates/myaccount/my-subscriptions.php:23
#: templates/myaccount/related-subscriptions.php:23
#: templates/myaccount/related-subscriptions.php:39
msgctxt "table heading"
msgid "Next payment"
msgstr "Paiement suivant"

#. translators: $1: customer's first name and last name, $2: how many
#. subscriptions customer switched
#: templates/emails/admin-new-switch-order.php:18
#: templates/emails/plain/admin-new-switch-order.php:18
msgctxt "Used in switch notification admin email"
msgid ""
"Customer %1$s has switched their subscription. The details of their new "
"subscription are as follows:"
msgid_plural ""
"Customer %1$s has switched %2$d of their subscriptions. The details of their "
"new subscriptions are as follows:"
msgstr[0] ""
"Le client %1$s a changé son abonnement. Les détails de son nouvel abonnement "
"sont les suivants :"
msgstr[1] ""
"Le client %1$s a changé %2$d de ses abonnements. Les détails de ses nouveaux "
"abonnements sont les suivants :"

#: templates/emails/cancelled-subscription.php:22
#: templates/emails/email-order-details.php:38
#: templates/emails/expired-subscription.php:22
#: templates/emails/on-hold-subscription.php:22
msgctxt "table headings in notification email"
msgid "Price"
msgstr "Prix"

#. translators: $1: customer's billing first name and last name
#: templates/emails/admin-new-renewal-order.php:16
#: templates/emails/plain/admin-new-renewal-order.php:16
msgctxt "Used in admin email: new renewal order"
msgid ""
"You have received a subscription renewal order from %1$s. Their order is as "
"follows:"
msgstr ""
"Vous avez reçu une commande de renouvellement d’abonnement de %1$s. Voici sa "
"commande :"

#: templates/checkout/form-change-payment-method.php:47
msgctxt "text on button on checkout page"
msgid "Change payment method"
msgstr "Modifier le moyen de paiement"

#: templates/checkout/form-change-payment-method.php:22
msgctxt "table headings in notification email"
msgid "Totals"
msgstr "Totaux"

#: templates/checkout/form-change-payment-method.php:21
#: templates/emails/email-order-details.php:37
msgctxt "table headings in notification email"
msgid "Quantity"
msgstr "Quantité"

#: templates/checkout/form-change-payment-method.php:20
#: templates/emails/email-order-details.php:36
#: templates/myaccount/subscription-totals-table.php:21
msgctxt "table headings in notification email"
msgid "Product"
msgstr "Produit"

#: templates/admin/html-variation-price.php:31
msgid "Subscription trial period:"
msgstr "Période d’essai d’abonnement :"

#: templates/admin/deprecated/html-variation-price.php:59
msgctxt ""
"Edit product screen, between the Billing Period and Subscription Length "
"dropdowns"
msgid "for"
msgstr "pour"

#: includes/wcs-time-functions.php:191
msgctxt ""
"Used in the trial period dropdown. Number is in text field. 0, 2+ will need "
"plural, 1 will need singular."
msgid "year"
msgid_plural "years"
msgstr[0] "année"
msgstr[1] "années"

#: includes/wcs-time-functions.php:190
msgctxt ""
"Used in the trial period dropdown. Number is in text field. 0, 2+ will need "
"plural, 1 will need singular."
msgid "month"
msgid_plural "months"
msgstr[0] "mois"
msgstr[1] "mois"

#: includes/wcs-time-functions.php:189
msgctxt ""
"Used in the trial period dropdown. Number is in text field. 0, 2+ will need "
"plural, 1 will need singular."
msgid "week"
msgid_plural "weeks"
msgstr[0] "semaine"
msgstr[1] "semaines"

#: includes/wcs-time-functions.php:188
msgctxt ""
"Used in the trial period dropdown. Number is in text field. 0, 2+ will need "
"plural, 1 will need singular."
msgid "day"
msgid_plural "days"
msgstr[0] "jour"
msgstr[1] "jours"

#. translators: period interval, placeholder is ordinal (eg "$10 every
#. _2nd/3rd/4th_", etc)
#: includes/wcs-time-functions.php:163
msgctxt "period interval with ordinal number (e.g. \"every 2nd\""
msgid "every %s"
msgstr "chaque %s"

#: includes/wcs-time-functions.php:105
msgctxt "Subscription lengths. e.g. \"For 1 year...\""
msgid "1 year"
msgstr "1 an"

#: includes/wcs-time-functions.php:101
msgctxt "Subscription lengths. e.g. \"For 1 month...\""
msgid "1 month"
msgstr "1 mois"

#: includes/wcs-time-functions.php:97
msgctxt "Subscription lengths. e.g. \"For 1 week...\""
msgid "1 week"
msgstr "1 semaine"

#: includes/wcs-time-functions.php:93
msgctxt "Subscription lengths. e.g. \"For 1 day...\""
msgid "1 day"
msgstr "1 jour"

#. translators: placeholder is number of years. (e.g. "Bill this every year / 4
#. years")
#: includes/wcs-time-functions.php:37
msgctxt "Subscription billing period."
msgid "year"
msgid_plural "%s years"
msgstr[0] "année"
msgstr[1] "%s années"

#. translators: placeholder is number of months. (e.g. "Bill this every month
#. 4 months")
#: includes/wcs-time-functions.php:35
msgctxt "Subscription billing period."
msgid "month"
msgid_plural "%s months"
msgstr[0] "mois"
msgstr[1] "%s mois"

#. translators: placeholder is number of weeks. (e.g. "Bill this every week / 4
#. weeks")
#: includes/wcs-time-functions.php:33
msgctxt "Subscription billing period."
msgid "week"
msgid_plural "%s weeks"
msgstr[0] "semaine"
msgstr[1] "%s semaines"

#. translators: placeholder is number of days. (e.g. "Bill this every day / 4
#. days")
#: includes/wcs-time-functions.php:31
msgctxt "Subscription billing period."
msgid "day"
msgid_plural "%s days"
msgstr[0] "jour"
msgstr[1] "%s jours"

#: includes/wcs-order-functions.php:152
msgctxt ""
"Refers to the type of the copy being performed: \"copy_order\", "
"\"subscription\", \"renewal_order\", \"resubscribe_order\""
msgid "Invalid data. Type of copy is not a string."
msgstr "Données non valides. Le type de copie n’est pas une chaîne."

#: includes/wcs-order-functions.php:148
msgctxt ""
"In wcs_copy_order_meta error message. Refers to origin and target order "
"objects."
msgid "Invalid data. Orders expected aren't orders."
msgstr ""
"Données non valides. Les commandes attendues ne sont pas des commandes."

#: includes/upgrades/templates/wcs-about-2-0.php:121
msgctxt "h3 on the About Subscriptions page for this new feature"
msgid "Change Payment Method"
msgstr "Modifier le moyen de paiement"

#. translators: 1$: error message, 2$: opening link tag, 3$: closing link tag,
#. 4$: break tag
#: includes/upgrades/class-wc-subscriptions-upgrader.php:436
msgctxt ""
"Error message that gets sent to front end when upgrading Subscriptions"
msgid ""
"Unable to repair subscriptions.%4$sError: %1$s%4$sPlease refresh the page "
"and try again. If problem persists, %2$scontact support%3$s."
msgstr ""
"Impossible de réparer les abonnements.%4$sErreur : %1$s%4$sVeuillez "
"rafraîchir la page et réessayer. Si le problème persiste, %2$scontactez "
"l’assistance%3$s."

#. translators: $1: "Repaired x subs with incorrect dates...", $2: "X others
#. were checked and no repair needed", $3: "(in X seconds)". Ordering for RTL
#. languages.
#: includes/upgrades/class-wc-subscriptions-upgrader.php:417
msgctxt "The assembled repair message that gets sent to front end."
msgid "%1$s%2$s %3$s"
msgstr ""

#. translators: placeholder is "{execution_time}", which will be replaced on
#. front end with actual time
#: includes/upgrades/class-wc-subscriptions-upgrader.php:414
msgctxt "Repair message that gets sent to front end."
msgid "(in %s seconds)"
msgstr "(en %s secondes)"

#. translators: placeholder is number of subscriptions that were checked and
#. did not need repairs. There's a space at the beginning!
#: includes/upgrades/class-wc-subscriptions-upgrader.php:410
msgctxt "Repair message that gets sent to front end."
msgid " %d other subscription was checked and did not need any repairs."
msgid_plural ""
"%d other subscriptions were checked and did not need any repairs."
msgstr[0] ""
" %d autre abonnement a été vérifié et ne nécessitait aucune réparation."
msgstr[1] ""
"%d autres abonnements ont été vérifiés et ne nécessitaient aucune réparation."

#. translators: placeholder is the number of subscriptions repaired
#: includes/upgrades/class-wc-subscriptions-upgrader.php:404
msgctxt "Repair message that gets sent to front end."
msgid ""
"Repaired %d subscriptions with incorrect dates, line tax data or missing "
"customer notes."
msgstr ""
"Réparation de %d abonnements avec des dates incorrectes, des données de "
"ligne de TVA ou des notes client manquantes."

#. translators: placeholder is "{time_left}", will be replaced on front end
#. with actual time
#: includes/upgrades/class-wc-subscriptions-upgrader.php:379
#: includes/upgrades/class-wc-subscriptions-upgrader.php:425
msgctxt "Message that gets sent to front end."
msgid "Estimated time left (minutes:seconds): %s"
msgstr "Temps restant estimé (minutes:secondes) : %s"

#. translators: 1$: number of subscriptions upgraded, 2$: "{execution_time}",
#. will be replaced on front end with actual time it took
#: includes/upgrades/class-wc-subscriptions-upgrader.php:376
msgid "Migrated %1$s subscriptions to the new structure (in %2$s seconds)."
msgstr ""
"Migration de %1$s abonnements vers la nouvelle structure (en %2$s secondes)."

#. translators: 1$: number of action scheduler hooks upgraded, 2$:
#. "{execution_time}", will be replaced on front end with actual time
#: includes/upgrades/class-wc-subscriptions-upgrader.php:364
msgid ""
"Migrated %1$s subscription related hooks to the new scheduler (in %2$s "
"seconds)."
msgstr ""
"Migration de %1$s crochets liés à l’abonnement vers le nouveau planificateur "
"(en %2$s secondes)."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:275
msgctxt ""
"when it is a payment change, and there is a subscr_signup message, this will "
"be a confirmation message that PayPal accepted it being the new payment "
"method"
msgid "IPN subscription payment method changed to PayPal."
msgstr "Le moyen de paiement d’abonnement IPN a été remplacé par PayPal."

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response.php:150
#: templates/admin/deprecated/order-shipping-html.php:14
#: templates/admin/deprecated/order-tax-html.php:9
msgctxt "no information about something"
msgid "N/A"
msgstr "ND"

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response.php:138
msgctxt "used in api error message if there is no long message"
msgid "Unknown error"
msgstr "Erreur inconnue"

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response.php:136
msgctxt "used in api error message if there is no severity code from PayPal"
msgid "Error"
msgstr "Erreur"

#: includes/gateways/paypal/class-wcs-paypal.php:626
msgctxt ""
"used in User Agent data sent to PayPal to help identify where a payment came "
"from"
msgid "WooCommerce Subscriptions PayPal"
msgstr "WooCommerce Subscriptions PayPal"

#: includes/gateways/paypal/class-wcs-paypal.php:362
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:208
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:320
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:336
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:365
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:384
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:150
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:156
msgctxt ""
"hash before the order number. Used as a character to remove from the actual "
"order number"
msgid "#"
msgstr "n°"

#. translators: $1: {blogname}, $2: {order_date}, variables will be substituted
#. when email is sent out
#: includes/emails/class-wcs-email-completed-renewal-order.php:40
msgctxt "Default email subject for email with downloadable files in it"
msgid ""
"Your %1$s subscription renewal order from %2$s is complete - download your "
"files"
msgstr ""
"Votre commande de renouvellement d’abonnement de %1$s du %2$s est terminée, "
"téléchargez vos fichiers"

#: includes/emails/class-wcs-email-completed-renewal-order.php:38
msgctxt "Default email heading for email with downloadable files in it"
msgid "Your subscription renewal order is complete - download your files"
msgstr ""
"Votre commande de renouvellement d’abonnement est terminée, téléchargez vos "
"fichiers"

#. translators: $1: {blogname}, $2: {order_date}, variables that will be
#. substituted when email is sent out
#: includes/emails/class-wcs-email-completed-renewal-order.php:31
msgctxt ""
"Default email subject for email to customer on completed renewal order"
msgid "Your %1$s renewal order from %2$s is complete"
msgstr "Votre commande de renouvellement de %1$s du %2$s est terminée"

#: includes/emails/class-wcs-email-completed-renewal-order.php:29
msgctxt ""
"Default email heading for email to customer on completed renewal order"
msgid "Your renewal order is complete"
msgstr "Votre commande de renouvellement est terminée"

#: includes/emails/class-wcs-email-cancelled-subscription.php:173
#: includes/emails/class-wcs-email-expired-subscription.php:171
#: includes/emails/class-wcs-email-on-hold-subscription.php:171
msgctxt "text, html or multipart"
msgid "Email type"
msgstr "Type d’e-mail"

#: includes/emails/class-wcs-email-cancelled-subscription.php:165
#: includes/emails/class-wcs-email-expired-subscription.php:163
#: includes/emails/class-wcs-email-on-hold-subscription.php:163
msgctxt ""
"Name the setting that controls the main heading contained within the email "
"notification"
msgid "Email Heading"
msgstr "En-tête de l’e-mail"

#. translators: placeholder is {blogname}, a variable that will be substituted
#. when email is sent out
#: includes/emails/class-wcs-email-cancelled-subscription.php:31
msgctxt "default email subject for cancelled emails sent to the admin"
msgid "[%s] Subscription Cancelled"
msgstr "[%s] Abonnement annulé"

#: includes/class-wcs-user-change-status-handler.php:77
msgctxt "Notice displayed to user confirming their action."
msgid "Your subscription has been cancelled."
msgstr "Votre abonnement a été annulé."

#: includes/class-wcs-user-change-status-handler.php:76
msgctxt "order note left on subscription after user action"
msgid "Subscription cancelled by the subscriber from their account page."
msgstr "Abonnement annulé par l’abonné sur sa page de compte."

#: includes/class-wcs-user-change-status-handler.php:68
msgctxt "Notice displayed to user confirming their action."
msgid "Your subscription has been put on hold."
msgstr "Votre abonnement a été mis en attente."

#: includes/class-wcs-user-change-status-handler.php:67
msgctxt "order note left on subscription after user action"
msgid "Subscription put on hold by the subscriber from their account page."
msgstr "Abonnement mis en attente par l’abonné sur sa page de compte."

#: includes/class-wcs-user-change-status-handler.php:58
msgctxt "Notice displayed to user confirming their action."
msgid "Your subscription has been reactivated."
msgstr "Votre abonnement a été réactivé."

#: includes/class-wcs-user-change-status-handler.php:57
msgctxt "order note left on subscription after user action"
msgid "Subscription reactivated by the subscriber from their account page."
msgstr "Abonnement réactivé par l’abonné sur sa page de compte."

#: includes/class-wcs-cart-renewal.php:676
msgctxt ""
"Used in WooCommerce by removed item notification: \"_All linked subscription "
"items were_ removed. Undo?\" Filter for item title."
msgid "All linked subscription items were"
msgstr "Tous les articles d’abonnement liés étaient"

#: includes/class-wc-subscriptions-synchroniser.php:319
#: templates/admin/deprecated/html-variation-synchronisation.php:36
#: templates/admin/html-variation-synchronisation.php:42
msgctxt "input field placeholder for day field for annual subscriptions"
msgid "Day"
msgstr "Jour"

#: includes/class-wc-subscriptions-switcher.php:433
#: includes/class-wc-subscriptions-synchroniser.php:236
msgctxt "when to prorate first payment / subscription length"
msgid "For All Subscription Products"
msgstr "Pour tous les produits d’abonnement"

#. translators: $1: opening link tag, $2: order number, $3: closing link tag
#: includes/class-wc-subscriptions-order.php:1056
msgid "Subscription cancelled for refunded order %1$s#%2$s%3$s."
msgstr "Abonnement annulé pour la commande remboursée %1$sn°%2$s%3$s."

#: includes/class-wc-subscriptions-manager.php:1079
msgctxt "Subscription status"
msgid "On-hold"
msgstr "En attente"

#: includes/class-wc-subscriptions-manager.php:1075
msgctxt "Subscription status"
msgid "Failed"
msgstr "Échec"

#: includes/class-wc-subscriptions-manager.php:87
#: includes/class-wc-subscriptions-manager.php:1918
#: includes/class-wc-subscriptions-manager.php:1936
msgctxt "used in order note as reason for why subscription status changed"
msgid "Subscription renewal payment due:"
msgstr "Paiement de renouvellement d’abonnement à régler :"

#. translators: 1$: coupon code that is being removed
#: includes/class-wc-subscriptions-coupon.php:474
msgid "Sorry, the \"%1$s\" coupon is only valid for renewals."
msgstr ""
"Désolé, le code promo « %1$s » n’est valide que pour les renouvellements."

#. translators: placeholder is an internal error number
#: includes/class-wc-subscriptions-checkout.php:213
msgid "Error %d: Unable to add tax to subscription. Please try again."
msgstr ""
"Erreur %d : Impossible d’ajouter la TVA à l’abonnement. Veuillez réessayer."

#: includes/class-wc-subscriptions-change-payment-gateway.php:709
#: includes/class-wc-subscriptions-change-payment-gateway.php:747
msgctxt "the page title of the change payment method form"
msgid "Change payment method"
msgstr "Modifier le moyen de paiement"

#: includes/class-wc-subscriptions-change-payment-gateway.php:277
msgctxt "label on button, imperative"
msgid "Change payment"
msgstr "Modifier le paiement"

#. translators: placeholder is the formatted total to be paid for the
#. subscription wrapped in <strong> tags
#: templates/checkout/subscription-receipt.php:30
msgid "Total: %s"
msgstr "Total : %s"

#: includes/api/legacy/class-wc-api-subscriptions.php:613
msgctxt "API response confirming order note deleted from a subscription"
msgid "Permanently deleted subscription note"
msgstr "Note d’abonnement supprimée définitivement"

#. translators: placeholder is error message
#: includes/api/legacy/class-wc-api-subscriptions.php:276
msgctxt "API error message when editing the order failed"
msgid "Edit subscription failed with error: %s"
msgstr "La modification de l’abonnement a échoué avec une erreur : %s"

#: includes/admin/meta-boxes/views/html-related-orders-table.php:21
#: templates/myaccount/my-subscriptions.php:24
#: templates/myaccount/related-orders.php:25
#: templates/myaccount/related-subscriptions.php:24
#: templates/myaccount/subscription-totals-table.php:22
msgctxt "table heading"
msgid "Total"
msgstr "Total"

#. translators: $1: is opening link, $2: is subscription order number, $3: is
#. closing link tag, $4: is user's name
#: includes/admin/class-wcs-admin-post-types.php:565
msgctxt "Subscription title on admin table. (e.g.: #211 for John Doe)"
msgid "%1$s#%2$s%3$s for %4$s"
msgstr "%1$sn°%2$s%3$s pour %4$s"

#. translators: placeholder is customer's billing phone number
#: includes/admin/class-wcs-admin-post-types.php:537
msgid "Tel: %s"
msgstr "Tél : %s"

#. translators: placeholder is customer's billing email
#: includes/admin/class-wcs-admin-post-types.php:532
msgid "Email: %s"
msgstr "E-mail : %s"

#: includes/admin/class-wcs-admin-post-types.php:431
msgctxt "number of orders linked to a subscription"
msgid "Orders"
msgstr "Commandes"

#: includes/admin/class-wcs-admin-post-types.php:427
msgid "Trial End"
msgstr "Fin de l’essai"

#: includes/admin/class-wcs-admin-post-types.php:424
msgid "Items"
msgstr "Articles"

#: includes/admin/class-wcs-admin-post-types.php:335
msgctxt "Used in order note. Reason why status changed."
msgid "Subscription status changed by bulk edit:"
msgstr "État d’abonnement modifié en lot :"

#: includes/admin/class-wcs-admin-post-types.php:260
#: includes/admin/class-wcs-admin-post-types.php:473
#: includes/class-wc-subscriptions-manager.php:1854
#: includes/wcs-user-functions.php:354
#: templates/myaccount/related-orders.php:78
msgctxt "an action on a subscription"
msgid "Cancel"
msgstr "Annuler"

#: includes/admin/class-wcs-admin-post-types.php:258
msgctxt "an action on a subscription"
msgid "Activate"
msgstr "Activer"

#: includes/admin/class-wc-subscriptions-admin.php:1729
#: includes/admin/class-wcs-admin-system-status.php:107
msgctxt "Live or Staging, Label on WooCommerce -> System Status page"
msgid "Subscriptions Mode"
msgstr "Mode d’abonnements"

#: includes/admin/class-wc-subscriptions-admin.php:864
msgid ""
"Warning: Deleting a user will also delete the user's subscriptions. The "
"user's orders will remain but be reassigned to the 'Guest' user.\n"
"\n"
"Do you want to continue to delete this user and any associated subscriptions?"
msgstr ""
"Attention : supprimer un utilisateur supprimera également les abonnements de "
"cet utilisateur. Les commandes de l’utilisateur resteront mais seront "
"réaffectées à l’utilisateur « Invité ».\n"
"\n"
"Voulez-vous continuer et supprimer cet utilisateur et les abonnements "
"associés ?"

#. translators: placeholder is trial period validation message if passed an
#. invalid value (e.g. "Trial period can not exceed 4 weeks")
#: templates/admin/deprecated/html-variation-price.php:118
#: templates/admin/html-variation-price.php:27
msgctxt "Trial period dropdown's description in pricing fields"
msgid ""
"An optional period of time to wait before charging the first recurring "
"payment. Any sign up fee will still be charged at the outset of the "
"subscription. %s"
msgstr ""
"Une période d’attente facultative avant de facturer le premier paiement "
"récurrent. Les frais d’inscription seront toujours facturés au début de "
"l’abonnement. %s"

#. Description of the plugin
msgid ""
"Sell products and services with recurring payments in your WooCommerce Store."
msgstr ""
"Vendez des produits et des services avec des paiements récurrents dans votre "
"boutique WooCommerce."

#. Plugin Name of the plugin
#: includes/privacy/class-wcs-privacy.php:40
msgid "WooCommerce Subscriptions"
msgstr "Abonnements WooCommerce"

#. translators: opening/closing <a> tags - linked to ticket form.
#: woocommerce-subscriptions.php:1251
msgid ""
"Please upgrade the WooCommerce Subscriptions plugin to version 2.0 or newer "
"immediately. If you need assistance, after upgrading to Subscriptions v2.0, "
"please %1$sopen a support ticket%2$s."
msgstr ""
"Veuillez mettre à niveau l’extension WooCommerce Subscriptions vers la "
"version 2.0 ou plus récente immédiatement. Si vous avez besoin d’aide, après "
"la mise à niveau vers Subscriptions v2.0, veuillez %1$souvrir un ticket "
"d’assistance%2$s."

#. translators: placeholder is Subscriptions version number.
#: woocommerce-subscriptions.php:1249
msgid ""
"Warning! You are running version %s of WooCommerce Subscriptions plugin code "
"but your database has been upgraded to Subscriptions version 2.0. This will "
"cause major problems on your store."
msgstr ""
"Attention ! Vous exécutez la version %s du code d’extension WooCommerce "
"Subscriptions, mais votre base de données a été mise à niveau vers "
"Subscriptions version 2.0. Cela entraînera des problèmes majeurs sur votre "
"boutique."

#. translators: placeholders are opening and closing tags. Leads to docs on
#. version 2
#: woocommerce-subscriptions.php:1233
msgid ""
"Warning! Version 2.0 is a major update to the WooCommerce Subscriptions "
"extension. Before updating, please create a backup, update all WooCommerce "
"extensions and test all plugins, custom code and payment gateways with "
"version 2.0 on a staging site. %1$sLearn more about the changes in version 2."
"0 &raquo;%2$s"
msgstr ""
"Attention ! La version 2.0 est une mise à jour majeure de l’extension "
"WooCommerce Subscriptions. Avant la mise à jour, veuillez créer une "
"sauvegarde, mettre à jour toutes les extensions WooCommerce et tester toutes "
"les extensions, le code personnalisé et les passerelles de paiement avec la "
"version 2.0 sur un site de préproduction. %1$sEn savoir plus sur les "
"modifications dans la version 2.0 &raquo;%2$s"

#: woocommerce-subscriptions.php:1150
msgid "Support"
msgstr "Forums de support"

#: includes/upgrades/templates/wcs-about-2-0.php:36
#: woocommerce-subscriptions.php:1149
msgctxt "short for documents"
msgid "Docs"
msgstr "Documentations"

#: woocommerce-subscriptions.php:944
msgid "Enable automatic payments"
msgstr "Activer les paiements automatiques"

#: woocommerce-subscriptions.php:939
msgid "Quit nagging me (but don't enable automatic payments)"
msgstr "Ne plus me rappeler (mais ne pas activer les paiements automatiques)"

#: woocommerce-subscriptions.php:761
msgid "Variable Subscription"
msgstr "Abonnement variable"

#. translators: 1$-2$: opening and closing <strong> tags, 3$: minimum supported
#. WooCommerce version, 4$-5$: opening and closing link tags, leads to plugin
#. admin
#: woocommerce-subscriptions.php:730
msgid ""
"%1$sWooCommerce Subscriptions is inactive.%2$s This version of Subscriptions "
"requires WooCommerce %3$s or newer. Please %4$supdate WooCommerce to version "
"%3$s or newer &raquo;%5$s"
msgstr ""
"%1$sWooCommerce Subscriptions est inactif.%2$s Cette version de "
"Subscriptions requiert %3$s ou version plus récente. Veuillez %4$smettre à "
"jour WooCommerce vers la version %3$s ou plus récente &raquo;%5$s"

#. translators: 1$-2$: opening and closing <strong> tags, 3$-4$: link tags,
#. takes to woocommerce plugin on wp.org, 5$-6$: opening and closing link tags,
#. leads to plugins.php in admin
#: woocommerce-subscriptions.php:727
msgid ""
"%1$sWooCommerce Subscriptions is inactive.%2$s The %3$sWooCommerce "
"plugin%4$s must be active for WooCommerce Subscriptions to work. Please "
"%5$sinstall & activate WooCommerce &raquo;%6$s"
msgstr ""
"%1$sWooCommerce Subscriptions est inactif.%2$s L’%3$sextension "
"WooCommerce%4$s doit être active pour que WooCommerce Subscriptions "
"fonctionne. Veuillez %5$sinstaller et activer WooCommerce &raquo;%6$s"

#. translators: placeholder is a number, numbers ending in 3
#: woocommerce-subscriptions.php:688
msgid "%srd"
msgstr "%se"

#. translators: placeholder is a number, numbers ending in 2
#: woocommerce-subscriptions.php:684
msgid "%snd"
msgstr "%se"

#. translators: placeholder is a number, numbers ending in 1
#: woocommerce-subscriptions.php:680
msgid "%sst"
msgstr "%ser"

#. translators: placeholder is a number, this is for the teens
#. translators: placeholder is a number, numbers ending in 4-9, 0
#: woocommerce-subscriptions.php:675 woocommerce-subscriptions.php:692
msgid "%sth"
msgstr "%se"

#: includes/class-wc-subscriptions-cart-validator.php:68
#: woocommerce-subscriptions.php:535
msgid ""
"A subscription has been removed from your cart. Products and subscriptions "
"can not be purchased at the same time."
msgstr ""
"Un abonnement a été supprimé de votre panier. Les produits et les "
"abonnements ne peuvent pas être achetés en même temps."

#: includes/class-wc-subscriptions-cart-validator.php:62
#: woocommerce-subscriptions.php:529
msgid ""
"A subscription has been removed from your cart. Due to payment gateway "
"restrictions, different subscription products can not be purchased at the "
"same time."
msgstr ""
"Un abonnement a été supprimé de votre panier. En raison des restrictions de "
"la passerelle de paiement, différents produits d’abonnement ne peuvent pas "
"être achetés en même temps."

#: includes/class-wc-subscriptions-cart-validator.php:56
#: woocommerce-subscriptions.php:523
msgid ""
"A subscription renewal has been removed from your cart. Multiple "
"subscriptions can not be purchased at the same time."
msgstr ""
"Un renouvellement d’abonnement a été supprimé de votre panier. Plusieurs "
"abonnements ne peuvent pas être achetés en même temps."

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:353
msgctxt "post status label including post count"
msgid "Pending Cancellation <span class=\"count\">(%s)</span>"
msgid_plural "Pending Cancellation <span class=\"count\">(%s)</span>"
msgstr[0] "Annulation en attente <span class=\"count\">(%s)</span>"
msgstr[1] "Annulation en attente <span class=\"count\">(%s)</span>"

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:351
msgctxt "post status label including post count"
msgid "Expired <span class=\"count\">(%s)</span>"
msgid_plural "Expired <span class=\"count\">(%s)</span>"
msgstr[0] "Expiré <span class=\"count\">(%s)</span>"
msgstr[1] "Expiré <span class=\"count\">(%s)</span>"

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:349
msgctxt "post status label including post count"
msgid "Switched <span class=\"count\">(%s)</span>"
msgid_plural "Switched <span class=\"count\">(%s)</span>"
msgstr[0] "Changé <span class=\"count\">(%s)</span>"
msgstr[1] "Changé <span class=\"count\">(%s)</span>"

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:347
msgctxt "post status label including post count"
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actif <span class=\"count\">(%s)</span>"
msgstr[1] "Actif <span class=\"count\">(%s)</span>"

#. translators: placeholders are opening and closing link tags
#: woocommerce-subscriptions.php:332
msgid "%1$sAdd a subscription product &raquo;%2$s"
msgstr "%1$sAjouter un produit d’abonnement &raquo;%2$s"

#. translators: placeholders are opening and closing link tags
#: woocommerce-subscriptions.php:330
msgid "%1$sLearn more about managing subscriptions &raquo;%2$s"
msgstr "%1$sEn savoir plus sur la gestion des abonnements &raquo;%2$s"

#: woocommerce-subscriptions.php:328
msgid ""
"Subscriptions will appear here for you to view and manage once purchased by "
"a customer."
msgstr ""
"Les abonnements apparaîtront ici pour que vous puissiez les consulter et les "
"gérer une fois achetés par un client."

#: woocommerce-subscriptions.php:326
msgid "No Subscriptions found"
msgstr "Aucun abonnement trouvé"

#: woocommerce-subscriptions.php:281
msgid "This is where subscriptions are stored."
msgstr "Il s’agit de l’emplacement de stockage des abonnements."

#: woocommerce-subscriptions.php:278
msgctxt "custom post type setting"
msgid "Parent Subscriptions"
msgstr "Abonnements parents"

#: woocommerce-subscriptions.php:277
msgctxt "custom post type setting"
msgid "No Subscriptions found in trash"
msgstr "Aucun abonnement trouvé dans la corbeille"

#: woocommerce-subscriptions.php:273 woocommerce-subscriptions.php:274
msgctxt "custom post type setting"
msgid "View Subscription"
msgstr "Afficher l’abonnement"

#: woocommerce-subscriptions.php:272
msgctxt "custom post type setting"
msgid "New Subscription"
msgstr "Nouvel abonnement"

#: woocommerce-subscriptions.php:271
msgctxt "custom post type setting"
msgid "Edit Subscription"
msgstr "Modifier l’abonnement"

#: woocommerce-subscriptions.php:270
msgctxt "custom post type setting"
msgid "Edit"
msgstr "Modifier"

#: woocommerce-subscriptions.php:269
msgctxt "custom post type setting"
msgid "Add New Subscription"
msgstr "Ajouter un nouvel abonnement"

#: woocommerce-subscriptions.php:268
msgctxt "custom post type setting"
msgid "Add Subscription"
msgstr "Ajouter un abonnement"

#: wcs-functions.php:345
msgid "Date type can not be an empty string."
msgstr "Le type de date ne peut pas être une chaîne vide."

#: wcs-functions.php:343
msgid "Date type is not a string."
msgstr "Le type de date n’est pas une chaîne."

#: wcs-functions.php:254
msgid "Can not get status name. Status is not a string."
msgstr "Impossible d’obtenir le nom de l’état. L’état n’est pas une chaîne."

#: wcs-functions.php:238
msgctxt "Subscription status"
msgid "Pending Cancellation"
msgstr "Annulation en attente"

#: includes/class-wc-subscriptions-manager.php:1069 wcs-functions.php:237
msgctxt "Subscription status"
msgid "Expired"
msgstr "Expiré"

#: includes/class-wc-subscriptions-switcher.php:2704 wcs-functions.php:236
msgctxt "Subscription status"
msgid "Switched"
msgstr "Changé"

#: includes/class-wc-subscriptions-manager.php:1066 wcs-functions.php:235
msgctxt "Subscription status"
msgid "Cancelled"
msgstr "Annulé"

#: wcs-functions.php:234
msgctxt "Subscription status"
msgid "On hold"
msgstr "En attente"

#: includes/class-wc-subscriptions-manager.php:1063 wcs-functions.php:233
msgctxt "Subscription status"
msgid "Active"
msgstr "Actif"

#: includes/class-wc-subscriptions-manager.php:1072 wcs-functions.php:232
msgctxt "Subscription status"
msgid "Pending"
msgstr "En attente"

#: tests/unit/wcs_test_wcs_functions.php:833
msgctxt "table column header"
msgid "Big Bang"
msgstr "Big Bang"

#: templates/single-product/add-to-cart/subscription.php:32
#: templates/single-product/add-to-cart/variable-subscription.php:30
msgid "You have an active subscription to this product already."
msgstr "Vous avez déjà un abonnement actif à ce produit."

#: includes/privacy/class-wcs-privacy-exporters.php:84 wcs-functions.php:283
msgid "Shipping Address"
msgstr "Adresse de livraison"

#: includes/privacy/class-wcs-privacy-exporters.php:83 wcs-functions.php:284
msgid "Billing Address"
msgstr "Adresse de facturation"

#: includes/admin/meta-boxes/views/html-retries-table.php:31
msgid "Email"
msgstr "Adresse de messagerie"

#. translators: placeholder is price string, denotes tax included in cart/order
#. total
#: includes/wcs-cart-functions.php:320
msgctxt "includes tax"
msgid "(includes %s)"
msgstr "(inclut %s)"

#: templates/myaccount/subscription-totals-table.php:35
msgid "Are you sure you want remove this item from your subscription?"
msgstr "Voulez-vous vraiment supprimer cet article de votre abonnement ?"

#: templates/myaccount/subscription-totals.php:23
msgid "Subscription totals"
msgstr "Totaux des abonnements"

#: templates/myaccount/subscription-details.php:100
msgctxt "date on subscription updates list. Will be localized"
msgid "l jS \\o\\f F Y, h:ia"
msgstr "\\f F Y, h:ial jS \\o"

#: templates/myaccount/subscription-details.php:94
msgid "Subscription updates"
msgstr "Mises à jour des abonnements"

#: templates/myaccount/subscription-details.php:81
msgid "Actions"
msgstr "Actions"

#: templates/myaccount/subscription-details.php:28
msgctxt "customer subscription table header"
msgid "Trial end date"
msgstr "Date de fin d’essai"

#: templates/myaccount/subscription-details.php:27
msgctxt "customer subscription table header"
msgid "End date"
msgstr "Date de fin"

#: templates/myaccount/subscription-details.php:26
msgctxt "customer subscription table header"
msgid "Next payment date"
msgstr "Date du paiement suivant"

#: templates/myaccount/subscription-details.php:25
msgctxt "customer subscription table header"
msgid "Last order date"
msgstr "Date de la dernière commande"

#: includes/class-wcs-template-loader.php:28
msgid "My Account"
msgstr "Mon compte :"

#: includes/class-wc-subscriptions-change-payment-gateway.php:250
#: includes/class-wcs-template-loader.php:28
#: includes/wcs-helper-functions.php:286
msgid "Invalid Subscription."
msgstr "Abonnement non valide."

#: templates/myaccount/related-subscriptions.php:15
msgid "Related subscriptions"
msgstr "Abonnements liés"

#: templates/myaccount/my-subscriptions.php:50
#: templates/myaccount/related-orders.php:84
#: templates/myaccount/related-subscriptions.php:46
msgctxt "view a subscription"
msgid "View"
msgstr "Voir"

#: templates/myaccount/related-orders.php:65
msgctxt "pay for a subscription"
msgid "Pay"
msgstr "Payer"

#: templates/myaccount/related-orders.php:22
msgid "Order"
msgstr "Ordre"

#: includes/admin/class-wcs-admin-meta-boxes.php:79
#: includes/admin/class-wcs-admin-meta-boxes.php:83
msgid "Related Orders"
msgstr "Commandes similaires"

#. translators: placeholder is the display name of a payment gateway a
#. subscription was paid by
#. translators: %s: payment method.
#: includes/admin/class-wcs-admin-post-types.php:609
#: includes/class-wc-subscription.php:2025
msgid "Via %s"
msgstr "Via %s"

#: templates/myaccount/my-subscriptions.php:33
#: templates/myaccount/related-subscriptions.php:31
msgid "ID"
msgstr "ID"

#: includes/admin/class-wcs-admin-post-types.php:428
msgid "Next Payment"
msgstr "Paiement suivant"

#: includes/admin/class-wcs-admin-post-types.php:430
msgid "End Date"
msgstr "Date de fin"

#: includes/admin/class-wcs-admin-post-types.php:426
msgid "Start Date"
msgstr "Date de début"

#: includes/class-wcs-query.php:296
msgid "View subscription"
msgstr "Afficher l’abonnement"

#: templates/emails/plain/subscription-info.php:20
#: templates/emails/subscription-info.php:21
msgid "Subscription information"
msgstr "Informations sur l’abonnement"

#. translators: placeholder is subscription's view url
#: templates/emails/plain/customer-completed-switch-order.php:35
msgid "View your subscription: %s"
msgstr "Afficher votre abonnement : %s"

#. translators: placeholder is order's view url
#: templates/emails/plain/customer-completed-switch-order.php:24
msgid "View your order: %s"
msgstr "Afficher votre commande : %s"

#. translators: placeholder is localised date string
#: templates/emails/plain/cancelled-subscription.php:39
msgid "End of Prepaid Term: %s"
msgstr "Fin du terme prépayé : %s"

#. translators: $1: customer's billing first name and last name
#: templates/emails/cancelled-subscription.php:16
#: templates/emails/plain/cancelled-subscription.php:16
msgid ""
"A subscription belonging to %1$s has been cancelled. Their subscription's "
"details are as follows:"
msgstr ""
"Un abonnement appartenant à %1$s a été annulé. Les détails de son abonnement "
"sont les suivants :"

#. translators: placeholder is the subscription order number wrapped in
#. <strong> tags
#: templates/checkout/subscription-receipt.php:18
#: templates/emails/plain/email-order-details.php:19
msgid "Subscription Number: %s"
msgstr "Numéro d’abonnement : %s"

#: templates/emails/plain/email-order-details.php:17
msgid "Order date: %s"
msgstr "Date de la commande : %s"

#: templates/emails/plain/email-order-details.php:16
msgid "Order number: %s"
msgstr "Numéro de commande : %s"

#: templates/emails/customer-renewal-invoice.php:24
#: templates/emails/customer-renewal-invoice.php:33
msgid "Pay Now &raquo;"
msgstr "Payer maintenant &raquo;"

#: templates/emails/customer-completed-switch-order.php:18
#: templates/emails/plain/customer-completed-switch-order.php:17
msgid ""
"You have successfully changed your subscription items. Your new order and "
"subscription details are shown below for your reference:"
msgstr ""
"Vous avez modifié avec succès vos articles d’abonnement. Les détails de "
"votre nouvelle commande et de votre abonnement sont indiqués ci-dessous pour "
"votre référence :"

#: includes/admin/class-wcs-admin-post-types.php:423
#: templates/emails/cancelled-subscription.php:21
#: templates/emails/expired-subscription.php:21
#: templates/emails/on-hold-subscription.php:21
#: templates/myaccount/my-subscriptions.php:21
#: templates/myaccount/related-subscriptions.php:21
#: woocommerce-subscriptions.php:267
msgid "Subscription"
msgstr "Abonnement"

#. translators: $1: customer's billing first name and last name
#: templates/emails/expired-subscription.php:16
#: templates/emails/plain/expired-subscription.php:16
msgid ""
"A subscription belonging to %1$s has expired. Their subscription's details "
"are as follows:"
msgstr ""
"Un abonnement appartenant à %1$s a expiré. Les détails de son abonnement "
"sont les suivants :"

#: templates/emails/admin-new-switch-order.php:28
#: templates/emails/customer-completed-switch-order.php:26
msgid "New subscription details"
msgstr "Détails du nouvel abonnement"

#: templates/emails/admin-new-switch-order.php:20
msgid "Switch Order Details"
msgstr "Détails de la commande de changement"

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:43
msgid "Customer Totals"
msgstr "Totaux des clients"

#: includes/privacy/class-wcs-privacy-exporters.php:79
msgid "Recurring Total"
msgstr ""

#. translators: %s: shipping method label.
#: includes/wcs-cart-functions.php:97 includes/wcs-cart-functions.php:102
msgid "Shipping via %s"
msgstr "Livraison via %s"

#: templates/checkout/recurring-totals.php:31
#: templates/checkout/recurring-totals.php:32
msgid "Subtotal"
msgstr "Sous-total"

#: templates/checkout/recurring-totals.php:151
#: templates/checkout/recurring-totals.php:152
msgid "Recurring total"
msgstr ""

#: templates/checkout/form-change-payment-method.php:82
msgid ""
"Sorry, it seems no payment gateways support changing the recurring payment "
"method. Please contact us if you require assistance or to make alternate "
"arrangements."
msgstr ""
"Désolé, il semble qu’aucune passerelle de paiement ne prenne en charge la "
"modification du moyen de paiement récurrent. Veuillez nous contacter si vous "
"avez besoin d’aide ou si vous désirez mettre en place une alternative."

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:21
msgid "Products"
msgstr "Produits"

#: templates/admin/deprecated/order-tax-html.php:21
msgid "Shipping Tax:"
msgstr "Taxe d’expédition :"

#: templates/admin/deprecated/order-tax-html.php:17
msgid "Recurring Sales Tax:"
msgstr "Taxes de vente récurrentes :"

#: templates/admin/deprecated/order-shipping-html.php:34
#: templates/admin/deprecated/order-shipping-html.php:36
msgid "Other"
msgstr "Autre"

#: templates/admin/deprecated/order-shipping-html.php:13
msgid "Shipping Method"
msgstr "Méthode d’expédition"

#: templates/admin/deprecated/order-shipping-html.php:8
msgid "Label"
msgstr "Nom"

#: templates/admin/html-variation-price.php:49
msgid "Billing interval:"
msgstr "Intervalle de facturation :"

#. translators: %s: currency symbol.
#. translators: placeholder is a currency symbol / code
#: includes/admin/class-wc-subscriptions-admin.php:301
#: templates/admin/html-variation-price.php:44
msgid "Subscription price (%s)"
msgstr "Prix de l’abonnement (%s)"

#: includes/admin/class-wc-subscriptions-admin.php:354
#: templates/admin/html-variation-price.php:25
msgid "Free trial"
msgstr "Essai gratuit"

#. translators: %s is a currency symbol / code
#: includes/admin/class-wc-subscriptions-admin.php:340
#: templates/admin/html-variation-price.php:20
msgid "Sign-up fee (%s)"
msgstr "Frais d’inscription (%s)"

#: templates/admin/deprecated/html-variation-price.php:105
msgctxt "example number of days / weeks / months"
msgid "e.g. 3"
msgstr "par ex. 3"

#: includes/wcs-user-functions.php:345
#: templates/single-product/add-to-cart/subscription.php:30
#: templates/single-product/add-to-cart/variable-subscription.php:28
msgid "Resubscribe"
msgstr "Se réabonner"

#: includes/wcs-time-functions.php:210
msgctxt "no trial period"
msgid "no"
msgstr "non"

#: includes/wcs-time-functions.php:159
msgctxt "period interval (eg \"$10 _every_ 2 weeks\")"
msgid "every"
msgstr "chaque"

#: includes/wcs-order-functions.php:523
msgid "Invalid data. No valid item id was passed in."
msgstr ""
"Données non valides. Aucun identifiant d’article valide n’a été transmis."

#: includes/wcs-order-functions.php:519
msgid "Invalid data. No valid subscription / order was passed in."
msgstr ""
"Données non valides. Aucun abonnement/commande valide n’a été transmis."

#. translators: placeholder is an order type.
#: includes/wcs-order-functions.php:329
msgid "\"%s\" is not a valid new order type."
msgstr "« %s » n’est pas un type de nouvelle commande valide."

#: includes/wcs-order-functions.php:324
msgid "$type passed to the function was not a string."
msgstr "$type transmis à la fonction n’était pas une chaîne."

#. translators: placeholder is a date.
#: includes/wcs-order-functions.php:305
msgid "Resubscribe Order &ndash; %s"
msgstr "Commande de réabonnement &ndash; %s"

#. translators: placeholder is a date.
#: includes/wcs-order-functions.php:301
msgid "Subscription Renewal Order &ndash; %s"
msgstr "Commande de renouvellement d’abonnement &ndash; %s"

#. translators: placeholders are strftime() strings.
#. translators: Order date parsed by strftime
#: includes/wcs-order-functions.php:296 wcs-functions.php:161
msgctxt ""
"Used in subscription post title. \"Subscription renewal order - <this>\""
msgid "%b %d, %Y @ %I:%M %p"
msgstr "%b %d, %Y à %I:%M %p"

#. translators: minute placeholder for time input, javascript format
#: includes/wcs-helper-functions.php:48
msgid "MM"
msgstr "MM"

#. translators: hour placeholder for time input, javascript format
#: includes/wcs-helper-functions.php:45
msgid "HH"
msgstr "HH"

#. translators: date placeholder for input, javascript format
#: includes/wcs-helper-functions.php:40
msgid "YYYY-MM-DD"
msgstr "AAAA-MM-JJ"

#. translators: 1$: trial length (e.g. "3 weeks"), 2$: subscription string
#. (e.g. "$10 up front then $5 on March 23rd every 3rd year")
#: includes/wcs-formatting-functions.php:219
msgid "%1$s free trial then %2$s"
msgstr "%1$s essai gratuit puis %2$s"

#. translators: 1$: subscription string (e.g. "$10 up front then $5 on March
#. 23rd every 3rd year"), 2$: trial length (e.g. "3 weeks")
#: includes/wcs-formatting-functions.php:216
msgid "%1$s after %2$s free trial"
msgstr "%1$s après l’essai gratuit de %2$s"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount, 4$: subscription period (e.g. "month" or "3 months")
#: includes/wcs-formatting-functions.php:194
msgid "%1$s %2$s then %3$s / %4$s"
msgid_plural "%1$s %2$s then %3$s every %4$s"
msgstr[0] "%1$s %2$s puis %3$s / %4$s"
msgstr[1] "%1$s %2$s puis %3$s chaque %4$s"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount, 4$: month (e.g. "March"), 5$: day of the month (e.g.
#. "23rd"), 6$: interval (e.g. "3rd")
#: includes/wcs-formatting-functions.php:184
msgid "%1$s %2$s then %3$s on %4$s %5$s every %6$s year"
msgstr "%1$s %2$s puis %3$s le %4$s %5$s chaque %6$s année"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount, 4$: month of year (e.g. "March"), 5$: day of the month
#. (e.g. "23rd")
#: includes/wcs-formatting-functions.php:175
msgid "%1$s %2$s then %3$s on %4$s %5$s each year"
msgstr "%1$s %2$s puis %3$s le %4$s %5$s chaque année"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount, 4$: day of the month (e.g. "23rd"), 5$: interval (e.g.
#. "3rd")
#: includes/wcs-formatting-functions.php:157
msgid "%1$s %2$s then %3$s on the %4$s day of every %5$s month"
msgstr "%1$s %2$s puis %3$s le %4$s jour de chaque %5$s mois"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount, 4$: interval (e.g. "3rd")
#: includes/wcs-formatting-functions.php:154
msgid "%1$s %2$s then %3$s on the last day of every %4$s month"
msgstr "%1$s %2$s puis %3$s le dernier jour de chaque %4$s mois"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount, 4$: day of the month (e.g. "23rd"); (e.g. "$10 up
#. front then $40 on the 23rd of each month")
#: includes/wcs-formatting-functions.php:138
msgid "%1$s %2$s then %3$s on the %4$s of each month"
msgstr "%1$s %2$s puis %3$s le %4$s de chaque mois"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount; (e.g. "$10 up front then $30 on the last day of each
#. month")
#: includes/wcs-formatting-functions.php:135
msgid "%1$s %2$s then %3$s on the last day of each month"
msgstr "%1$s %2$s puis %3$s le dernier jour de chaque mois"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front" ),
#. 3$: recurring amount, 4$: interval (e.g. "2nd week"), 5$: day of the week
#. (e.g. "Thursday"); (e.g. "$10 up front, then $20 every 2nd week on
#. Wednesday")
#: includes/wcs-formatting-functions.php:122
msgid "%1$s %2$s then %3$s every %4$s on %5$s"
msgstr "%1$s %2$s puis %3$s chaque %4$s le %5$s"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount string, 4$: payment day of the week (e.g. "$15 up
#. front, then $10 every Wednesday")
#: includes/wcs-formatting-functions.php:113
msgid "%1$s %2$s then %3$s every %4$s"
msgstr "%1$s %2$s puis %3$s chaque %4$s"

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"),
#. 3$: recurring amount string (e.g. "£10 / month" )
#: includes/wcs-formatting-functions.php:99
msgid "%1$s %2$s then %3$s"
msgstr "%1$s %2$s puis %3$s"

#: includes/wcs-formatting-functions.php:41
msgctxt "initial payment on a subscription"
msgid "up front"
msgstr "à l’avance"

#. translators: placeholder is either subscription key or a subscription id,
#. or, failing that, empty (e.g. "145_21" or "145")
#: includes/wcs-deprecated-functions.php:180
msgid ""
"Could not get subscription. Most likely the subscription key does not refer "
"to a subscription. The key was: \"%s\"."
msgstr ""
"Impossible d’obtenir l’abonnement. Très probablement, la clé d’abonnement ne "
"fait pas référence à un abonnement. La clé était : « %s »."

#. translators: placeholder is a date
#: includes/wcs-cart-functions.php:389
#: tests/unit/wcs_test_wcs_cart_functions.php:225
msgid "First renewal: %s"
msgstr "Premier renouvellement : %s"

#: includes/wcs-cart-functions.php:282
msgid "Free shipping coupon"
msgstr "Code promo livraison gratuite"

#: includes/wcs-cart-functions.php:227
msgctxt "shipping method price"
msgid "Free"
msgstr "Gratuit"

#: includes/upgrades/templates/wcs-upgrade.php:72
msgid ""
"There was an error with the update. Please refresh the page and try again."
msgstr ""
"Une erreur est survenue lors de la mise à jour. Veuillez rafraîchir la page "
"et réessayer."

#: includes/upgrades/templates/wcs-upgrade.php:71
msgid "Update Error"
msgstr "Erreur de mise à jour"

#. translators: $1: placeholder is number of weeks, 2$: path to the file
#: includes/upgrades/templates/wcs-upgrade.php:66
msgid ""
"To record the progress of the update a new log file was created. This file "
"will be automatically deleted in %1$d weeks. If you would like to delete it "
"sooner, you can find it here: %2$s"
msgstr ""
"Pour enregistrer la progression de la mise à jour, un nouveau fichier "
"journal a été créé. Ce fichier sera supprimé automatiquement dans "
"%1$d semaines. Si vous voulez le supprimer plus tôt, vous pouvez le trouver "
"ici : %2$s"

#: includes/upgrades/templates/wcs-upgrade.php:63
msgid "Continue"
msgstr "Continuer"

#: includes/upgrades/templates/wcs-upgrade.php:62
msgid "Your database has been updated successfully!"
msgstr "Votre base de données a été mise à jour avec succès !"

#: includes/upgrades/templates/wcs-upgrade.php:61
msgid "Update Complete"
msgstr "Mise à jour terminée"

#: includes/upgrades/templates/wcs-upgrade.php:53
msgid ""
"Remember, although the update process may take a while, customers and other "
"non-administrative users can browse and purchase from your store without "
"interruption while the update is in progress."
msgstr ""
"Rappelez-vous que, bien que le processus de mise à jour puisse prendre un "
"certain temps, les clients et autres utilisateurs non administratifs peuvent "
"parcourir et acheter dans votre boutique sans interruption pendant que la "
"mise à jour est en cours."

#: includes/upgrades/templates/wcs-upgrade.php:51
msgid ""
"Please keep this page open until the update process completes. No need to "
"refresh or restart the process."
msgstr ""
"Veuillez garder cette page ouverte jusqu’à la fin du processus de mise à "
"jour. Pas besoin de rafraîchir ou de redémarrer le processus."

#: includes/upgrades/templates/wcs-upgrade.php:50
msgid ""
"This page will display the results of the process as each batch of "
"subscriptions is updated."
msgstr ""
"Cette page affichera les résultats du processus au fur et à mesure que "
"chaque lot d’abonnements est mis à jour."

#: includes/upgrades/templates/wcs-upgrade.php:49
msgid "Update in Progress"
msgstr "Mise à jour en cours"

#: includes/upgrades/templates/wcs-upgrade.php:45
msgctxt "text on submit button"
msgid "Update Database"
msgstr "Mettre à jour la base de données"

#: includes/upgrades/templates/wcs-upgrade.php:43
msgid ""
"Customers and other non-administrative users can browse and purchase from "
"your store without interruption while the update is in progress."
msgstr ""
"Les clients et autres utilisateurs non administratifs peuvent parcourir et "
"acheter dans votre boutique sans interruption pendant que la mise à jour est "
"en cours."

#: includes/upgrades/templates/wcs-upgrade.php:41
msgid "The update process may take a little while, so please be patient."
msgstr ""
"Le processus de mise à jour peut prendre un peu de temps, alors soyez "
"patient."

#. translators: 1$: number of subscriptions on site, 2$, lower estimate
#. (minutes), 3$: upper estimate
#: includes/upgrades/templates/wcs-upgrade.php:38
msgid ""
"The full update process for the %1$d subscriptions on your site will take "
"between %2$d and %3$d minutes."
msgstr ""
"Le processus complet de mise à jour des %1$d abonnements sur votre site "
"prendra entre %2$d et %3$d minutes. "

#. translators: placeholders are opening and closing tags
#: includes/upgrades/templates/wcs-upgrade.php:33
msgid ""
"Before we send you on your way, we need to update your database to the "
"newest version. If you do not have a recent backup of your site, %snow is "
"the time to create one%s."
msgstr ""
"Avant de vous laisser, nous devons mettre à jour votre base de données avec "
"la dernière version. Si vous ne disposez pas d’une sauvegarde récente de "
"votre site, %sil est maintenant temps d’en créer une%s."

#: includes/upgrades/templates/wcs-upgrade.php:30
msgid "The WooCommerce Subscriptions plugin has been updated!"
msgstr "L’extension WooCommerce Subscriptions a été mise à jour !"

#: includes/upgrades/templates/wcs-upgrade.php:29
msgid "Database Update Required"
msgstr "Mise à jour de base de données obligatoire"

#: includes/upgrades/templates/wcs-upgrade.php:19
msgid "WooCommerce Subscriptions Update"
msgstr "Mise à jour de WooCommerce Subscriptions"

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:36
msgid ""
"Rest assured, although the update process may take a little while, it is "
"coded to prevent defects, your site is safe and will be up and running again,"
" faster than ever, shortly."
msgstr ""
"Rassurez-vous, bien que le processus de mise à jour puisse prendre un peu de "
"temps, il est codé pour éviter les défauts, votre site est sûr et sera à "
"nouveau opérationnel, plus rapidement que jamais, sous peu."

#. translators: placeholder is number of seconds
#: includes/upgrades/templates/wcs-upgrade-in-progress.php:34
msgid ""
"If you received a server error and reloaded the page to find this notice, "
"please refresh the page in %s seconds and the upgrade routine will "
"recommence without issues."
msgstr ""
"Si vous avez reçu une erreur de serveur et que vous voyez cet avis après "
"avoir rechargé la page, veuillez rafraîchir la page dans %s secondes et la "
"routine de mise à niveau recommencera sans problème."

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:31
msgid ""
"The WooCommerce Subscriptions plugin is currently running its database "
"upgrade routine."
msgstr ""
"L’extension WooCommerce Subscriptions exécute actuellement sa routine de "
"mise à niveau de la base de données."

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:30
msgid "The Upgrade is in Progress"
msgstr "La mise à niveau est en cours"

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:24
msgid "WooCommerce Subscriptions Update in Progress"
msgstr "Mise à jour de WooCommerce Subscriptions en cours"

#: includes/upgrades/templates/wcs-about-2-0.php:194
msgid "Go to WooCommerce Subscriptions Settings"
msgstr "Accéder aux paramètres de WooCommerce Subscriptions"

#. translators: all placeholders are opening and closing <code> tags, no need
#. to order them
#: includes/upgrades/templates/wcs-about-2-0.php:188
msgid ""
"Want to list all the subscriptions on a site? Get %sexample.com/wc-"
"api/v2/subscriptions/%s. Want the details of a specific subscription? Get "
"%s/wc-api/v2/subscriptions/<id>/%s."
msgstr ""
"Vous voulez répertorier tous les abonnements sur un site ? Obtenez %sexample."
"com/wc-api/v2/subscriptions/%s. Vous voulez les détails d’un abonnement "
"spécifique ? Obtenez %s/wc-api/v2/subscriptions/<id>/%s."

#: includes/upgrades/templates/wcs-about-2-0.php:185
msgid ""
"We didn't just improve interfaces for humans, we also improved them for "
"computers. Your applications can now create, read, update or delete "
"subscriptions via RESTful API endpoints."
msgstr ""
"Nous n’avons pas seulement amélioré les interfaces pour les humains, nous "
"les avons également améliorées pour les ordinateurs. Vos applications "
"peuvent désormais créer, lire, mettre à jour ou supprimer des abonnements "
"via des points de terminaison API RESTful."

#: includes/upgrades/templates/wcs-about-2-0.php:184
msgid "REST API Endpoints"
msgstr "Points de terminaison API REST"

#. translators: all placeholders are opening and closing <code> tags, no need
#. to order them
#: includes/upgrades/templates/wcs-about-2-0.php:180
msgid ""
"Because the %sWC_Subscription%s class extends %sWC_Order%s, you can use its "
"familiar methods, like %s$subscription->update_status()%s or %s$subscription-"
">get_total()%s."
msgstr ""
"Comme la classe %sWC_Subscription%s inclut %sWC_Order%s, vous pouvez "
"utiliser ses méthodes familières, comme %s$subscription->update_status()%s "
"ou %s$subscription->get_total()%s."

#: includes/upgrades/templates/wcs-about-2-0.php:177
msgid ""
"Subscriptions 2.0 introduces a new object for working with a subscription at "
"the application level. The cumbersome APIs for retrieving or modifying a "
"subscription's data are gone!"
msgstr ""
"Subscriptions 2.0 introduit un nouvel objet pour fonctionner avec un "
"abonnement au niveau de l’application. Les API encombrantes pour récupérer "
"ou modifier les données d’un abonnement ont disparu !"

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about-2-0.php:175
msgid "New %sWC_Subscription%s Object"
msgstr "Nouvel objet %sWC_Subscription%s"

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about-2-0.php:169
msgid ""
"Developers can also now use all the familiar WordPress functions, like "
"%sget_posts()%s, to query or modify subscription data."
msgstr ""
"De même, les développeurs peuvent désormais utiliser toutes les fonctions "
"WordPress familières, comme %sget_posts()%s, pour interroger ou modifier les "
"données d’abonnement."

#: includes/upgrades/templates/wcs-about-2-0.php:166
msgid ""
"By making a subscription a Custom Order Type, a subscription is also now a "
"custom post type. This makes it faster to query subscriptions and it uses a "
"database schema that is as scalable as WordPress posts and pages."
msgstr ""
"En faisant d’un abonnement un type de commande personnalisé, un abonnement "
"devient également un type d’article personnalisé. Cela accélère "
"l’interrogation des abonnements et utilise un schéma de base de données "
"aussi évolutif que les articles et les pages WordPress."

#. translators: placeholders are opening and closing code tags
#: includes/upgrades/templates/wcs-about-2-0.php:164
msgid "New %sshop_subscription%s Post Type"
msgstr "Nouveau type d’article %sshop_subscription%s"

#: includes/upgrades/templates/wcs-about-2-0.php:158
msgid ""
"Subscriptions 2.0 introduces a new architecture built on the WooCommerce "
"Custom Order Types API."
msgstr ""
"Subscriptions 2.0 introduit une nouvelle architecture basée sur l’API "
"WooCommerce Custom Order Types."

#: includes/upgrades/templates/wcs-about-2-0.php:157
#: includes/upgrades/templates/wcs-about.php:151
msgid "Peek Under the Hood for Developers"
msgstr "Informations détaillées pour les développeurs"

#: includes/upgrades/templates/wcs-about-2-0.php:150
msgid "And much more..."
msgstr "Et bien plus encore…"

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:137
msgid ""
"It was already possible to change a subscription's next payment date, but "
"some store managers wanted to provide a customer with an extended free trial "
"or add an extra month to the expiration date. Now you can change all of "
"these dates from the %sEdit Subscription%s screen."
msgstr ""
"Il était déjà possible de modifier la date du prochain paiement d’un "
"abonnement, mais certains gérants de boutique souhaitaient proposer à un "
"client un essai gratuit prolongé ou ajouter un mois supplémentaire à la date "
"d’expiration. Nous pouvez désormais modifier toutes ces dates sur l’écran "
"%sModifier l’abonnement%s."

#: includes/upgrades/templates/wcs-about-2-0.php:134
msgid "Change Trial and End Dates"
msgstr "Modifier les dates d’essai et de fin"

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:124
msgid ""
"For a store manager to change a subscription from automatic to manual "
"renewal payments (or manual to automatic) with Subscriptions v1.5, the "
"database needed to be modified directly. Subscriptions now provides a way "
"for payment gateways to allow you to change that from the new %sEdit "
"Subscription%s interface."
msgstr ""
"Pour qu’un gérant de boutique puisse changer un abonnement de paiements de "
"renouvellement automatiques à manuels (ou de manuels à automatiques) avec "
"Subscriptions v1.5, la base de données devait être modifiée directement. "
"Subscriptions permet désormais aux passerelles de paiement de vous autoriser "
"à changer cela dans la nouvelle interface %sModifier l’abonnement%s."

#. translators: placeholders are for opening and closing link (<a>) tags
#. translators: placeholders are opening and closing anchor tags linking to
#. documentation
#: includes/upgrades/templates/wcs-about-2-0.php:115
#: includes/upgrades/templates/wcs-about-2-0.php:128
#: includes/upgrades/templates/wcs-about-2-0.php:141
#: includes/upgrades/templates/wcs-about.php:120
#: includes/upgrades/templates/wcs-about.php:131
#: includes/upgrades/templates/wcs-about.php:142
#: includes/upgrades/templates/wcs-about.php:170
#: includes/upgrades/templates/wcs-about.php:191
msgid "%sLearn more &raquo;%s"
msgstr "%sEn savoir plus &raquo;%s"

#. translators: placeholders are for opening and closing link (<a>) tags
#: includes/upgrades/templates/wcs-about-2-0.php:111
msgid ""
"By default, adding new files to an existing subscription product will "
"automatically provide active subscribers with access to the new files. "
"However, now you can enable a %snew content dripping setting%s to provide "
"subscribers with access to new files only after the next renewal payment."
msgstr ""
"Par défaut, l’ajout de nouveaux fichiers à un produit d’abonnement existant "
"fournira automatiquement aux abonnés actifs l’accès aux nouveaux fichiers. "
"Cependant, vous pouvez activer un %snouveau paramètre de diffusion de "
"contenu%s pour permettre aux abonnés d’accéder aux nouveaux fichiers "
"uniquement après le prochain paiement de renouvellement."

#: includes/admin/class-wc-subscriptions-admin.php:1348
#: includes/upgrades/templates/wcs-about-2-0.php:108
msgid "Drip Downloadable Content"
msgstr "Diffuser le contenu téléchargeable"

#. translators: placeholders are opening and closing link tags
#: includes/upgrades/templates/wcs-about-2-0.php:97
msgid "Learn more about the new %sView Subscription page%s."
msgstr "En savoir plus sur la nouvelle %spage Afficher l’abonnement%s."

#: includes/upgrades/templates/wcs-about-2-0.php:93
msgid ""
"This new page is also where the customer can suspend or cancel their "
"subscription, change payment method, change shipping address or "
"upgrade/downgrade an item."
msgstr ""
"Cette nouvelle page permet également au client de suspendre ou d’annuler son "
"abonnement, de modifier le moyen de paiement, de modifier l’adresse de "
"livraison ou de mettre à niveau/rétrograder un article."

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:91
msgid ""
"Your customers can now view the full details of a subscription, including "
"line items, billing and shipping address, billing schedule and renewal "
"orders, from a special %sMy Account > View Subscription%s page."
msgstr ""
"Vos clients peuvent désormais voir tous les détails d’un abonnement, "
"notamment les articles d'une ligne, l’adresse de facturation et de livraison,"
" le calendrier de facturation et les commandes de renouvellement, sur une "
"page spéciale %sMon compte > Afficher l’abonnement%s."

#: includes/upgrades/templates/wcs-about-2-0.php:87
msgid "New View Subscription Page"
msgstr "Nouvelle page Afficher l’abonnement"

#. translators: placeholers are link tags: 1$-2$ new subscription page, 3$-4$:
#. docs on woocommerce.com
#: includes/upgrades/templates/wcs-about-2-0.php:76
msgid ""
"%1$sAdd a subscription%2$s now or %3$slearn more%4$s about the new interface."
msgstr ""
"%1$sAjoutez un abonnement%2$s maintenant ou %3$sdécouvrez%4$s la nouvelle "
"interface."

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:72
msgid ""
"The new interface is also built on the existing %sEdit Order%s screen. If "
"you've ever modified an order, you already know how to modify a subscription."
msgstr ""
"La nouvelle interface est également basée sur l’écran %sModifier la "
"commande%s existant. Si vous avez déjà modifié une commande, vous savez déjà "
"comment modifier un abonnement."

#: includes/upgrades/templates/wcs-about-2-0.php:69
msgid ""
"Subscriptions v2.0 introduces a new administration interface to add or edit "
"a subscription. You can make all the familiar changes, like modifying "
"recurring totals or subscription status. You can also make some new "
"modifications, like changing the expiration date, adding a shipping cost or "
"adding a product line item."
msgstr ""
"Subscriptions v2.0 introduit une nouvelle interface d’administration pour "
"ajouter ou modifier un abonnement. Vous pouvez apporter toutes les "
"modifications habituelles, comme la modification des totaux récurrents ou de "
"l’état de l’abonnement. Vous pouvez également apporter de nouvelles "
"modifications, comme modifier la date d’expiration, ajouter des frais de "
"port ou ajouter article d'une ligne de produits."

#: includes/upgrades/templates/wcs-about-2-0.php:68
msgid "New Add/Edit Subscription Screen"
msgstr "Nouvel écran Ajouter/Modifier un abonnement"

#. translators: placeholders are opening and closing link tags
#: includes/upgrades/templates/wcs-about-2-0.php:56
msgid "Learn more about the new %smultiple subscriptions%s feature."
msgstr ""
"En savoir plus sur la nouvelle fonctionnalité %sabonnements multiples%s."

#: includes/upgrades/templates/wcs-about-2-0.php:53
msgid ""
"Customers can now purchase different subscription products in one "
"transaction. The products can bill on any schedule and have any combination "
"of sign-up fees and/or free trials."
msgstr ""
"Les clients peuvent désormais acheter différents produits d’abonnement en "
"une seule transaction. Les produits peuvent être facturés selon n’importe "
"quel calendrier et avoir n’importe quelle combinaison de frais d’inscription "
"et/ou d’essais gratuits."

#: includes/upgrades/templates/wcs-about-2-0.php:52
msgid "It's now easier for your customers to buy more subscriptions!"
msgstr ""
"Il est désormais plus facile pour vos clients d’acheter plus d’abonnements !"

#: includes/upgrades/templates/wcs-about-2-0.php:51
msgid "Multiple Subscriptions"
msgstr "Abonnements multiples"

#: includes/upgrades/templates/wcs-about-2-0.php:42
#: includes/upgrades/templates/wcs-about.php:41
msgid "Check Out What's New"
msgstr "Découvrir les nouveautés"

#. translators: placeholder is version number
#: includes/upgrades/templates/wcs-about-2-0.php:31
#: includes/upgrades/templates/wcs-about.php:30
msgid "Version %s"
msgstr "Version %s"

#: includes/upgrades/templates/update-welcome-notice.php:6
#: includes/upgrades/templates/wcs-about-2-0.php:25
#: includes/upgrades/templates/wcs-about.php:24
msgid "We hope you enjoy it!"
msgstr "Nous espérons que vous l’apprécierez !"

#: includes/upgrades/templates/wcs-about-2-0.php:24
msgid ""
"Version 2.0 has been in development for more than a year. We've reinvented "
"the extension to take into account 3 years of feedback from store managers."
msgstr ""
"La version 2.0 est en développement depuis plus d’un an. Nous avons "
"réinventé l’extension pour prendre en compte 3 ans de commentaires de la "
"part des gérants de boutique."

#. translators: placeholder is Subscription version string ('2.3')
#: includes/upgrades/templates/update-welcome-notice.php:2
#: includes/upgrades/templates/wcs-about-2-0.php:23
#: includes/upgrades/templates/wcs-about.php:22
msgid ""
"Thank you for updating to the latest version of WooCommerce Subscriptions."
msgstr "Merci d’être passé à la dernière version de WooCommerce Subscriptions."

#: includes/upgrades/templates/wcs-about-2-0.php:20
msgid "Welcome to Subscriptions 2.0"
msgstr "Bienvenue dans Subscriptions 2.0"

#: includes/upgrades/class-wc-subscriptions-upgrader.php:640
msgid "About WooCommerce Subscriptions"
msgstr "À propos de WooCommerce Subscriptions"

#: includes/upgrades/class-wc-subscriptions-upgrader.php:640
msgid "Welcome to WooCommerce Subscriptions 2.1"
msgstr "Bienvenue dans WooCommerce Subscriptions 2.1"

#. translators: 1$: error message, 2$: opening link tag, 3$: closing link tag,
#. 4$: break tag
#: includes/upgrades/class-wc-subscriptions-upgrader.php:389
msgid ""
"Unable to upgrade subscriptions.%4$sError: %1$s%4$sPlease refresh the page "
"and try again. If problem persists, %2$scontact support%3$s."
msgstr ""
"Impossible de mettre à niveau les abonnements.%4$sErreur : %1$s%4$sVeuillez "
"rafraîchir la page et réessayer. Si le problème persiste, %2$scontactez "
"l’assistance%3$s."

#. translators: placeholder is number of upgraded subscriptions
#: includes/upgrades/class-wc-subscriptions-upgrader.php:355
msgctxt "used in the subscriptions upgrader"
msgid "Marked %s subscription products as \"sold individually\"."
msgstr "%s produits d’abonnement marqués comme « vendus individuellement »."

#. translators: placeholder is a list of version numbers (e.g. "1.3 & 1.4 &
#. 1.5")
#: includes/upgrades/class-wc-subscriptions-upgrader.php:347
msgid "Database updated to version %s"
msgstr "Base de données mise à jour vers la version %s"

#: includes/payment-retry/class-wcs-retry-post-store.php:47
msgid "No retries found in trash"
msgstr "Aucune nouvelle tentative trouvée dans la corbeille"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:138
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:229
#: includes/payment-retry/class-wcs-retry-post-store.php:40
msgid "Edit"
msgstr "Modifier"

#: includes/payment-retry/class-wcs-retry-post-store.php:38
msgid "Add"
msgstr "Ajouter"

#. translators: placeholder is a number of days.
#: includes/wcs-time-functions.php:58
msgid "%s day"
msgid_plural "a %s-day"
msgstr[0] "%s jour"
msgstr[1] "de %s jours"

#. translators: placeholder is a number of weeks.
#: includes/wcs-time-functions.php:60
msgid "%s week"
msgid_plural "a %s-week"
msgstr[0] "%s semaine"
msgstr[1] "de %s semaines"

#. translators: placeholder is a number of months.
#: includes/wcs-time-functions.php:62
msgid "%s month"
msgid_plural "a %s-month"
msgstr[0] "%s mois"
msgstr[1] "de %s mois"

#. translators: placeholder is a number of years.
#: includes/wcs-time-functions.php:64
msgid "%s year"
msgid_plural "a %s-year"
msgstr[0] "%s an"
msgstr[1] "de %s ans"

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:66
msgid "Subscription reactivated with PayPal"
msgstr "Abonnement réactivé avec PayPal"

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:53
msgid "Subscription suspended with PayPal"
msgstr "Abonnement suspendu avec PayPal"

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:42
msgid "Subscription cancelled with PayPal"
msgstr "Abonnement annulé avec PayPal"

#. translators: 1$: subscription ID, 2$: order ID, 3$: names of items, comma
#. separated
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:78
msgctxt "item name sent to paypal"
msgid "Subscription %1$s (Order %2$s) - %3$s"
msgstr "Abonnement %1$s (Commande %2$s) - %3$s"

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:503
msgid "IPN subscription payment failure."
msgstr "Échec du paiement d’abonnement IPN."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:487
msgid "IPN subscription cancelled."
msgstr "Abonnement IPN annulé."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:464
msgid "IPN subscription suspended."
msgstr "Abonnement IPN suspendu."

#. translators: placeholder is payment status (e.g. "completed")
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:422
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:431
msgctxt "used in order note"
msgid "IPN subscription payment %s."
msgstr "Paiement d’abonnement IPN %s."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:374
msgid "IPN subscription failing payment method changed."
msgstr "Abonnement IPN échouant au moyen de paiement modifié."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:332
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:412
msgid "IPN subscription payment completed."
msgstr "Paiement d’abonnement IPN terminé."

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:279
msgid "IPN subscription sign up completed."
msgstr "Inscription d’abonnement IPN terminée."

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-ipn-handler.php:94
msgid "Billing agreement cancelled at PayPal."
msgstr "Accord de facturation annulé chez PayPal."

#: includes/privacy/class-wcs-privacy.php:236
msgid "N/A"
msgstr "ND"

#. translators: placeholder is localised datetime
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response-payment.php:119
msgid "expected clearing date %s"
msgstr "date de compensation prévue %s"

#. translators: %s: product SKU.
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:536
msgid "SKU: %s"
msgstr "UGS : %s"

#. translators: 1$: new status (e.g. "Cancel"), 2$: blog name
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:416
msgctxt "data sent to paypal"
msgid "%1$s subscription event triggered at %2$s"
msgstr "%1$s événement d’abonnement déclenché sur %2$s"

#. translators: placeholder is blogname
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:306
msgid "%s - Order"
msgstr "%s - Commande"

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:279
msgid "Total Discount"
msgstr "Remise totale"

#. translators: placeholder is blogname
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:71
msgctxt "data sent to paypal"
msgid "Orders with %s"
msgstr "Commandes avec %s"

#. translators: placeholders are opening and closing link tags. 1$-2$: docs on
#. woocommerce, 3$-4$: dismiss link
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:172
msgid ""
"There is a problem with PayPal. Your PayPal account is issuing out-of-date "
"subscription IDs. %1$sLearn more%2$s. %3$sDismiss%4$s."
msgstr ""
"Il y a un problème avec PayPal. Votre compte PayPal émet des ID d’abonnement "
"obsolètes. %1$sEn savoir plus%2$s. %3$sIgnorer%4$s."

#. translators: placeholders are link opening and closing tags. 1$-2$: to
#. gateway settings, 3$-4$: support docs on woocommerce.com
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:159
msgid ""
"There is a problem with PayPal. Your API credentials may be incorrect. "
"Please update your %1$sAPI credentials%2$s. %3$sLearn more%4$s."
msgstr ""
"Il y a un problème avec PayPal. Vos identifiants de connexion API peuvent "
"être incorrects. Veuillez mettre à jour vos %1$sidentifiants de connexion "
"API%2$s. %3$sEn savoir plus%4$s."

#. translators: placeholders are opening and closing strong tags.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:148
msgid ""
"%1$sPayPal Reference Transactions are enabled on your account%2$s. All "
"subscription management features are now enabled. Happy selling!"
msgstr ""
"%1$sPayPal Reference Transactions est activé sur votre compte%2$s. Toutes "
"les fonctionnalités de gestion des abonnements sont maintenant activées. "
"Bonne vente !"

#. translators: opening/closing tags - links to documentation.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:122
msgid ""
"%1$sPayPal Reference Transactions are not enabled on your account%2$s, some "
"subscription management features are not enabled. Please contact PayPal and "
"request they %3$senable PayPal Reference Transactions%4$s on your account. "
"%5$sCheck PayPal Account%6$s  %3$sLearn more %7$s"
msgstr ""
"%1$sPayPal Reference Transactions n’est pas activé sur votre compte%2$s, "
"certaines fonctionnalités de gestion des abonnements ne sont pas activées. "
"Veuillez contacter PayPal et leur demander d’%3$sactiver PayPal Reference "
"Transactions%4$s sur votre compte. %5$sVérifier le compte PayPal%6$s %3$sEn "
"savoir plus%7$s"

#. translators: placeholders are opening and closing link tags. 1$-2$: to docs
#. on woocommerce, 3$-4$ to gateway settings on the site
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:110
msgid ""
"PayPal is inactive for subscription transactions. Please %1$sset up the "
"PayPal IPN%2$s and %3$senter your API credentials%4$s to enable PayPal for "
"Subscriptions."
msgstr ""
"PayPal est inactif pour les transactions d’abonnement. Veuillez "
"%1$sconfigurer l’IPN PayPal%2$s et %3$ssaisir vos identifiants de connexion "
"API%4$s afin d’activer PayPal pour Subscriptions."

#. translators: $1 and $2 are opening and closing strong tags, respectively.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:63
msgid ""
"It is %1$sstrongly recommended you do not change the Receiver Email "
"address%2$s if you have active subscriptions with PayPal. Doing so can break "
"existing subscriptions."
msgstr ""
"Il vous est %1$sfortement recommandé de ne pas modifier l’adresse e-mail du "
"destinataire%2$s si vous avez des abonnements actifs avec PayPal. Cela peut "
"interrompre les abonnements existants."

#: includes/gateways/paypal/class-wcs-paypal.php:460
msgid ""
"Are you sure you want to change the payment method from PayPal standard?\n"
"\n"
"This will suspend the subscription at PayPal."
msgstr ""
"Voulez-vous vraiment modifier le moyen de paiement de PayPal Standard ?\n"
"\n"
"Cela suspendra l’abonnement chez PayPal."

#. translators: placeholder is a transaction ID.
#: includes/gateways/paypal/class-wcs-paypal.php:407
msgid "PayPal payment approved (ID: %s)"
msgstr "Paiement PayPal approuvé (ID : %s)"

#. translators: placeholder is PayPal transaction status message
#: includes/gateways/paypal/class-wcs-paypal.php:403
msgid "PayPal payment declined: %s"
msgstr "Paiement PayPal refusé : %s"

#. translators: placeholder is PayPal transaction status message
#: includes/gateways/paypal/class-wcs-paypal.php:391
msgid "PayPal Transaction Held: %s"
msgstr "Transaction PayPal en cours : %s"

#. translators: placeholders are PayPal API error code and PayPal API error
#. message
#: includes/gateways/paypal/class-wcs-paypal.php:386
msgid "PayPal API error: (%1$d) %2$s"
msgstr "Erreur d’API PayPal : (%1$d) %2$s"

#: includes/gateways/paypal/class-wcs-paypal.php:282
msgid ""
"An error occurred, please try again or try an alternate form of payment."
msgstr ""
"Une erreur est survenue, veuillez réessayer ou essayer un autre mode de "
"paiement."

#: includes/gateways/paypal/class-wcs-paypal.php:220
msgid "Unable to find order for PayPal billing agreement."
msgstr "Commande pour l’accord de facturation PayPal introuvable."

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:137
msgid ""
"Sorry, it seems there are no available payment methods which support "
"subscriptions. Please contact us if you require assistance or wish to make "
"alternate arrangements."
msgstr ""
"Désolé, il semble qu’aucun moyen de paiement ne soit disponible pour les "
"abonnements. Veuillez nous contacter si vous avez besoin d’aide ou si vous "
"désirez mettre en place une alternative."

#: includes/emails/class-wcs-email-new-switch-order.php:26
msgid "[{blogname}] Subscription Switched ({order_number}) - {order_date}"
msgstr "[{blogname}] Abonnement changé ({order_number}) - {order_date}"

#: includes/emails/class-wcs-email-new-switch-order.php:23
msgid ""
"Subscription switched emails are sent when a customer switches a "
"subscription."
msgstr ""
"Les e-mails de changement d’abonnement sont envoyés lorsqu’un client change "
"d’abonnement."

#: includes/emails/class-wcs-email-new-switch-order.php:22
#: includes/emails/class-wcs-email-new-switch-order.php:25
msgid "Subscription Switched"
msgstr "Abonnement changé"

#: includes/emails/class-wcs-email-new-renewal-order.php:26
msgid ""
"[{blogname}] New subscription renewal order ({order_number}) - {order_date}"
msgstr ""
"[{blogname}] Nouvelle commande de renouvellement d’abonnement ({order_number}"
") - {order_date}"

#: includes/emails/class-wcs-email-new-renewal-order.php:25
msgid "New subscription renewal order"
msgstr "Nouvelle commande de renouvellement d’abonnement"

#: includes/emails/class-wcs-email-new-renewal-order.php:23
msgid ""
"New renewal order emails are sent when a subscription renewal payment is "
"processed."
msgstr ""
"Les e-mails de nouvelle commande de renouvellement sont envoyés lorsqu’un "
"paiement de renouvellement d’abonnement est traité."

#: includes/emails/class-wcs-email-new-renewal-order.php:22
msgid "New Renewal Order"
msgstr "Nouvelle commande de renouvellement"

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:49
msgid "Invoice for renewal order {order_number}"
msgstr "Facture pour la commande de renouvellement {order_number}"

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:48
msgid "Invoice for renewal order {order_number} from {order_date}"
msgstr ""
"Facture pour la commande de renouvellement {order_number} du {order_date}"

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:40
msgid "Customer Renewal Invoice"
msgstr "Facture de renouvellement client"

#: includes/emails/class-wcs-email-processing-renewal-order.php:29
msgid "Your {blogname} renewal order receipt from {order_date}"
msgstr "Votre reçu de commande de renouvellement de {blogname} du {order_date}"

#: includes/emails/class-wcs-email-processing-renewal-order.php:28
msgid "Thank you for your order"
msgstr "Merci pour votre commande"

#: includes/emails/class-wcs-email-processing-renewal-order.php:25
msgid ""
"This is an order notification sent to the customer after payment for a "
"subscription renewal order is completed. It contains the renewal order "
"details."
msgstr ""
"Il s’agit d’une notification de commande envoyée au client après le paiement "
"d’une commande de renouvellement d’abonnement. Elle contient les détails de "
"la commande de renouvellement."

#: includes/emails/class-wcs-email-processing-renewal-order.php:24
msgid "Processing Renewal order"
msgstr "Traitement de la commande de renouvellement"

#: includes/emails/class-wcs-email-completed-switch-order.php:39
msgid ""
"Your {blogname} subscription change from {order_date} is complete - download "
"your files"
msgstr ""
"Votre changement d’abonnement de {blogname} du {order_date} est terminé, "
"téléchargez vos fichiers"

#: includes/emails/class-wcs-email-completed-switch-order.php:38
msgid "Your subscription change is complete - download your files"
msgstr "Votre changement d’abonnement est terminé, téléchargez vos fichiers"

#: includes/emails/class-wcs-email-completed-switch-order.php:31
msgid "Your {blogname} subscription change from {order_date} is complete"
msgstr ""
"Votre changement d’abonnement de {blogname} du {order_date} est terminé"

#: includes/emails/class-wcs-email-completed-switch-order.php:30
msgid "Your subscription change is complete"
msgstr "Votre changement d’abonnement est terminé"

#: includes/emails/class-wcs-email-completed-switch-order.php:27
msgid ""
"Subscription switch complete emails are sent to the customer when a "
"subscription is switched successfully."
msgstr ""
"Les e-mails de changement d’abonnement terminé sont envoyés au client "
"lorsqu’un abonnement est changé avec succès."

#: includes/emails/class-wcs-email-completed-switch-order.php:26
msgid "Subscription Switch Complete"
msgstr "Changement d’abonnement terminé"

#: includes/emails/class-wcs-email-completed-renewal-order.php:26
msgid ""
"Renewal order complete emails are sent to the customer when a subscription "
"renewal order is marked complete and usually indicates that the item for "
"that renewal period has been shipped."
msgstr ""
"Des e-mails de fin de commande de renouvellement sont envoyés au client "
"lorsqu’une commande de renouvellement d’abonnement est marquée comme "
"terminée. Ils indiquent en général que l’article pour cette période de "
"renouvellement a été expédié."

#: includes/emails/class-wcs-email-completed-renewal-order.php:25
msgid "Completed Renewal Order"
msgstr "Commande de renouvellement terminée"

#: includes/emails/class-wcs-email-cancelled-subscription.php:181
#: includes/emails/class-wcs-email-expired-subscription.php:179
#: includes/emails/class-wcs-email-on-hold-subscription.php:179
msgctxt "email type"
msgid "Multipart"
msgstr "Multi-parties"

#: includes/emails/class-wcs-email-cancelled-subscription.php:180
#: includes/emails/class-wcs-email-expired-subscription.php:178
#: includes/emails/class-wcs-email-on-hold-subscription.php:178
msgctxt "email type"
msgid "HTML"
msgstr "HTML"

#: includes/emails/class-wcs-email-cancelled-subscription.php:179
#: includes/emails/class-wcs-email-expired-subscription.php:177
#: includes/emails/class-wcs-email-on-hold-subscription.php:177
msgctxt "email type"
msgid "Plain text"
msgstr "Texte brut"

#: includes/emails/class-wcs-email-cancelled-subscription.php:175
#: includes/emails/class-wcs-email-expired-subscription.php:173
#: includes/emails/class-wcs-email-on-hold-subscription.php:173
msgid "Choose which format of email to send."
msgstr "Choisissez le format d’envoi des e-mails."

#. translators: %s: default e-mail heading.
#: includes/emails/class-wcs-email-expired-subscription.php:166
#: includes/emails/class-wcs-email-on-hold-subscription.php:166
msgid ""
"This controls the main heading contained within the email notification. "
"Leave blank to use the default heading: <code>%s</code>."
msgstr ""
"Cela contrôle l’en-tête principal contenu dans la notification par e-mail. "
"Laisser vide pour utiliser l’en-tête par défaut : <code>%s</code>."

#. translators: %s: default e-mail subject.
#: includes/emails/class-wcs-email-cancelled-subscription.php:160
#: includes/emails/class-wcs-email-expired-subscription.php:158
#: includes/emails/class-wcs-email-on-hold-subscription.php:158
msgid ""
"This controls the email subject line. Leave blank to use the default subject:"
" %s."
msgstr ""
"Cela contrôle la ligne d’objet de l’e-mail. Laisser vide pour utiliser le "
"sujet par défaut : %s."

#: includes/emails/class-wcs-email-cancelled-subscription.php:157
#: includes/emails/class-wcs-email-expired-subscription.php:155
#: includes/emails/class-wcs-email-on-hold-subscription.php:155
msgctxt "of an email"
msgid "Subject"
msgstr "Sujet"

#. translators: placeholder is admin email
#: includes/emails/class-wcs-email-cancelled-subscription.php:152
#: includes/emails/class-wcs-email-expired-subscription.php:150
#: includes/emails/class-wcs-email-on-hold-subscription.php:150
msgid "Enter recipients (comma separated) for this email. Defaults to %s."
msgstr ""
"Saisir les destinataires (séparés par une virgule) pour cet e-mail. Par "
"défaut à %s."

#: includes/emails/class-wcs-email-cancelled-subscription.php:149
#: includes/emails/class-wcs-email-expired-subscription.php:147
#: includes/emails/class-wcs-email-on-hold-subscription.php:147
msgctxt "of an email"
msgid "Recipient(s)"
msgstr "Destinataire(s)"

#: includes/emails/class-wcs-email-cancelled-subscription.php:145
#: includes/emails/class-wcs-email-customer-renewal-invoice.php:212
#: includes/emails/class-wcs-email-expired-subscription.php:143
#: includes/emails/class-wcs-email-on-hold-subscription.php:143
msgid "Enable this email notification"
msgstr "Activer cette notification par e-mail"

#: includes/emails/class-wcs-email-cancelled-subscription.php:143
#: includes/emails/class-wcs-email-customer-renewal-invoice.php:210
#: includes/emails/class-wcs-email-expired-subscription.php:141
#: includes/emails/class-wcs-email-on-hold-subscription.php:141
msgctxt "an email notification"
msgid "Enable/Disable"
msgstr "Activer/Désactiver"

#: includes/emails/class-wcs-email-cancelled-subscription.php:29
msgid "Subscription Cancelled"
msgstr "Abonnement annulé"

#: includes/emails/class-wcs-email-cancelled-subscription.php:27
msgid ""
"Cancelled Subscription emails are sent when a customer's subscription is "
"cancelled (either by a store manager, or the customer)."
msgstr ""
"Les e-mails d’abonnement annulé sont envoyés lorsque l’abonnement d’un "
"client est annulé (par un gérant de boutique ou par le client)."

#: includes/emails/class-wcs-email-cancelled-subscription.php:26
msgid "Cancelled Subscription"
msgstr "Abonnement annulé"

#: includes/class-wcs-webhooks.php:112
msgid " Subscription deleted"
msgstr "Abonnement supprimé"

#: includes/class-wcs-webhooks.php:111
msgid " Subscription updated"
msgstr "Abonnement mis à jour"

#: includes/class-wcs-webhooks.php:110
msgid " Subscription created"
msgstr "Abonnement créé"

#. translators: placeholder is subscription's new status, translated
#: includes/class-wcs-user-change-status-handler.php:116
msgid ""
"That subscription can not be changed to %s. Please contact us if you need "
"assistance."
msgstr ""
"Cet abonnement ne peut pas être modifié sur %s. Contactez-nous si vous avez "
"besoin d’aide."

#: includes/class-wcs-user-change-status-handler.php:103
msgid ""
"That subscription does not exist. Please contact us if you need assistance."
msgstr ""
"Cet abonnement n’existe pas. Contactez-nous si vous avez besoin d’aide."

#: includes/class-wcs-user-change-status-handler.php:71
msgid ""
"You can not suspend that subscription - the suspension limit has been "
"reached. Please contact us if you need assistance."
msgstr ""
"Vous ne pouvez pas suspendre cet abonnement - la limite de suspension a été "
"atteinte. Contactez-nous si vous avez besoin d’aide."

#: includes/class-wcs-user-change-status-handler.php:61
msgid ""
"You can not reactivate that subscription until paying to renew it. Please "
"contact us if you need assistance."
msgstr ""
"Vous ne pouvez pas réactiver cet abonnement tant que vous n’avez pas payé "
"pour le renouveler. Contactez-nous si vous avez besoin d’aide."

#: includes/class-wcs-remove-item.php:188
msgid ""
"The item was not removed because this Subscription's payment method does not "
"support removing an item."
msgstr ""
"L’article n’a pas été supprimé, car le moyen de paiement de cet abonnement "
"ne prend pas en charge la suppression d’un article."

#: includes/class-wcs-remove-item.php:184
msgid "You cannot remove an item that does not exist. "
msgstr "Vous ne pouvez pas supprimer un article qui n’existe pas. "

#: includes/class-wcs-remove-item.php:180
msgid "You cannot modify a subscription that does not belong to you."
msgstr "Vous ne pouvez pas modifier un abonnement qui ne vous appartient pas."

#: includes/class-wcs-remove-item.php:176
#: includes/class-wcs-user-change-status-handler.php:107
msgid "Security error. Please contact us if you need assistance."
msgstr "Erreur de sécurité. Contactez-nous si vous avez besoin d’aide."

#. translators: placeholders are 1$: item name, and, 2$: opening and, 3$:
#. closing link tags
#: includes/class-wcs-remove-item.php:140
msgid ""
"You have successfully removed \"%1$s\" from your subscription. %2$sUndo?%3$s"
msgstr ""
"Vous avez bien supprimé « %1$s » de votre abonnement. %2$sAnnuler ?%3$s"

#. translators: 1$: product name, 2$: product id
#: includes/class-wcs-remove-item.php:137
msgctxt "used in order note"
msgid "Customer removed \"%1$s\" (Product ID: #%2$d) via the My Account page."
msgstr ""
"Le client a supprimé « %1$s » (ID produit : n°%2$d) sur la page Mon compte."

#: includes/class-wcs-remove-item.php:119
msgid "Your request to undo your previous action was unsuccessful."
msgstr "Votre demande d’annulation de votre action précédente a échoué."

#. translators: 1$: product name, 2$: product id
#: includes/class-wcs-remove-item.php:114
msgctxt "used in order note"
msgid "Customer added \"%1$s\" (Product ID: #%2$d) via the My Account page."
msgstr ""
"Le client a ajouté « %1$s » (ID produit : n°%2$d) sur la page Mon compte."

#. translators: %d: subscription ID.
#: includes/class-wcs-remove-item.php:79
msgctxt "hash before subscription ID"
msgid "Subscription #%d does not exist."
msgstr "L’abonnement n°%d n’existe pas."

#. translators: %s: subscription ID.
#. translators: %s: order number.
#. translators: placeholder is a subscription ID.
#: includes/class-wc-subscriptions-addresses.php:207
#: includes/class-wc-subscriptions-change-payment-gateway.php:741
#: includes/class-wcs-query.php:101
msgctxt "hash before order number"
msgid "Subscription #%s"
msgstr "Abonnement n°%s"

#: includes/class-wcs-change-payment-method-admin.php:122
msgid "Please choose a valid payment gateway to change to."
msgstr ""
"Veuillez choisir une passerelle de paiement valide vers laquelle passer."

#: includes/class-wcs-cart-resubscribe.php:91
#: includes/class-wcs-cart-resubscribe.php:119
msgid "Complete checkout to resubscribe."
msgstr "Terminez la validation de commande pour vous réabonner."

#: includes/class-wcs-cart-resubscribe.php:82
msgid ""
"You can not resubscribe to that subscription. Please contact us if you need "
"assistance."
msgstr ""
"Vous ne pouvez pas vous réabonner à cet abonnement. Contactez-nous si vous "
"avez besoin d’aide."

#: includes/class-wcs-cart-resubscribe.php:74
#: includes/early-renewal/class-wcs-cart-early-renewal.php:91
msgid "That subscription does not exist. Has it been deleted?"
msgstr "Cet abonnement n’existe pas. A-t-il été supprimé ?"

#: includes/class-wcs-cart-resubscribe.php:70
msgid "There was an error with your request to resubscribe. Please try again."
msgstr ""
"Une erreur est survenue lors de votre demande de réabonnement. Veuillez "
"réessayer."

#: includes/class-wcs-cart-renewal.php:647
msgid "All linked subscription items have been removed from the cart."
msgstr "Tous les articles d’abonnement liés ont été supprimés du panier."

#: includes/class-wcs-cart-renewal.php:376
msgid ""
"We couldn't find the original renewal order for an item in your cart. The "
"item was removed."
msgid_plural ""
"We couldn't find the original renewal orders for items in your cart. The "
"items were removed."
msgstr[0] ""
"Nous n’avons pas trouvé la commande de renouvellement d’origine pour un "
"article dans votre panier. L’article a été supprimé."
msgstr[1] ""
"Nous n’avons pas trouvé les commandes de renouvellement d’origine pour des "
"articles dans votre panier. Les articles ont été supprimés."

#: includes/class-wcs-cart-renewal.php:369
msgid ""
"We couldn't find the original subscription for an item in your cart. The "
"item was removed."
msgid_plural ""
"We couldn't find the original subscriptions for items in your cart. The "
"items were removed."
msgstr[0] ""
"Nous n’avons pas trouvé l’abonnement d’origine pour un article dans votre "
"panier. L’article a été supprimé."
msgstr[1] ""
"Nous n’avons pas trouvé les abonnements d’origine pour des articles dans "
"votre panier. Les articles ont été supprimés."

#. translators: %s is subscription's number
#: includes/class-wcs-cart-renewal.php:327
msgid "Subscription #%s has not been added to the cart."
msgstr "L’abonnement n°%s n’a pas été ajouté au panier."

#. translators: placeholder is an item name
#: includes/class-wcs-cart-renewal.php:292
msgid ""
"The %s product has been deleted and can no longer be renewed. Please choose "
"a new product or contact us for assistance."
msgstr ""
"Le produit %s a été supprimé et ne peut plus être renouvelé. Veuillez "
"choisir un nouveau produit ou nous contacter pour obtenir de l’aide."

#: includes/class-wcs-cart-initial-payment.php:62
#: includes/class-wcs-cart-renewal.php:194
msgid "That doesn't appear to be your order."
msgstr "Cela ne semble pas être votre commande."

#: includes/class-wcs-auth.php:45
msgid "View and manage subscriptions"
msgstr "Afficher et gérer les abonnements"

#: includes/class-wcs-auth.php:42
msgid "Create subscriptions"
msgstr "Créer des abonnements"

#: includes/class-wcs-auth.php:39
msgid "View subscriptions"
msgstr "Afficher les abonnements"

#. translators: placeholder is a date
#: includes/class-wc-subscriptions-synchroniser.php:841
msgid "First payment: %s"
msgstr "Premier paiement : %s"

#. translators: placeholder is a date
#: includes/class-wc-subscriptions-synchroniser.php:838
msgid "First payment prorated. Next payment: %s"
msgstr "Premier paiement au prorata. Paiement suivant : %s"

#: includes/class-wc-subscriptions-synchroniser.php:831
msgid "Today!"
msgstr "Aujourd’hui !"

#: includes/class-wc-subscriptions-synchroniser.php:783
msgid "Last day of the month"
msgstr "Dernier jour du mois"

#. translators: placeholder is a number of day with language specific suffix
#. applied (e.g. "1st", "3rd", "5th", etc...)
#: includes/class-wc-subscriptions-synchroniser.php:781
msgid "%s day of the month"
msgstr "%s jour du mois"

#. translators: placeholder is a day of the week
#: includes/class-wc-subscriptions-synchroniser.php:775
msgid "%s each week"
msgstr "%s chaque semaine"

#: includes/class-wc-subscriptions-synchroniser.php:750
#: includes/class-wc-subscriptions-synchroniser.php:767
msgid "Do not synchronise"
msgstr "Ne pas synchroniser"

#: includes/class-wc-subscriptions-switcher.php:432
#: includes/class-wc-subscriptions-synchroniser.php:235
msgctxt "when to prorate first payment / subscription length"
msgid "For Virtual Subscription Products Only"
msgstr "Pour les produits d’abonnement virtuels uniquement"

#: includes/class-wc-subscriptions-synchroniser.php:227
msgid ""
"If a subscription is synchronised to a specific day of the week, month or "
"year, charge a prorated amount for the subscription at the time of sign up."
msgstr ""
"Si un abonnement est synchronisé sur un jour spécifique de la semaine, du "
"mois ou de l’année, facturez un montant au prorata pour l’abonnement au "
"moment de l’inscription."

#: includes/class-wc-subscriptions-synchroniser.php:219
msgid "Align Subscription Renewal Day"
msgstr "Aligner le jour de renouvellement d’abonnement"

#. translators: placeholders are opening and closing link tags
#: includes/class-wc-subscriptions-synchroniser.php:213
msgctxt "used in the general subscription options page"
msgid ""
"Align subscription renewal to a specific day of the week, month or year. For "
"example, the first day of the month. %1$sLearn more%2$s."
msgstr ""
"Alignez le renouvellement d’abonnement sur un jour spécifique de la semaine, "
"du mois ou de l’année. Par exemple, le premier jour du mois. %1$sEn savoir "
"plus%2$s."

#: includes/class-wc-subscriptions-synchroniser.php:210
msgid "Synchronisation"
msgstr "Synchronisation"

#. translators: placeholder is a year (e.g. "2016")
#: includes/class-wc-subscriptions-synchroniser.php:51
msgctxt "used in subscription product edit screen"
msgid ""
"Align the payment date for this subscription to a specific day of the year. "
"If the date has already taken place this year, the first payment will be "
"processed in %s. Set the day to 0 to disable payment syncing for this "
"product."
msgstr ""
"Alignez la date de paiement de cet abonnement sur un jour spécifique de "
"l’année. Si la date a déjà eu lieu cette année, le premier paiement sera "
"traité en %s. Définissez le jour sur 0 pour désactiver la synchronisation "
"des paiements pour ce produit."

#: includes/class-wc-subscriptions-synchroniser.php:49
msgid ""
"Align the payment date for all customers who purchase this subscription to a "
"specific day of the week or month."
msgstr ""
"Alignez la date de paiement pour tous les clients qui achètent cet "
"abonnement sur un jour spécifique de la semaine ou du mois."

#: templates/admin/deprecated/html-variation-synchronisation.php:30
msgid "Synchronise Renewals"
msgstr "Synchroniser les renouvellements"

#. translators: %1: product subtotal, %2: HTML span tag, %3: direction
#. (upgrade, downgrade, crossgrade), %4: closing HTML span tag
#: includes/class-wc-subscriptions-switcher.php:1866
msgctxt "product subtotal string"
msgid "%1$s %2$s(%3$s)%4$s"
msgstr "%1$s %2$s(%3$s)%4$s"

#: includes/class-wc-subscriptions-switcher.php:1861
msgctxt "a switch type"
msgid "Crossgrade"
msgstr "Reclassement"

#: includes/class-wc-subscriptions-switcher.php:1858
msgctxt "a switch type"
msgid "Upgrade"
msgstr "Mise à niveau"

#: includes/class-wc-subscriptions-switcher.php:1855
msgctxt "a switch type"
msgid "Downgrade"
msgstr "Rétrogradation"

#: includes/class-wc-subscriptions-switcher.php:1493
msgid "There was an error locating the switch details."
msgstr ""
"Une erreur est survenue lors de la localisation des détails du changement."

#: includes/class-wc-subscriptions-switcher.php:1452
msgid ""
"You can not switch this subscription. It appears you do not own the "
"subscription."
msgstr ""
"Vous ne pouvez pas changer cet abonnement. Il semble que cet abonnement ne "
"vous appartient pas."

#: includes/class-wc-subscriptions-switcher.php:1405
msgid "You can not switch to the same subscription."
msgstr "Vous ne pouvez pas passer au même abonnement."

#: includes/class-wc-subscriptions-switcher.php:1383
msgid "We can not find your old subscription item."
msgstr "Nous ne trouvons pas votre ancien article d’abonnement."

#: includes/class-wc-subscriptions-switcher.php:1211
msgid "Switched Subscription"
msgstr "Abonnement changé"

#: includes/class-wc-subscriptions-switcher.php:1196
msgid "Switch Order"
msgstr "Commande de changement"

#. translators: 1$: old item, 2$: new item when switching
#: includes/class-wc-subscriptions-switcher.php:1985
msgctxt "used in order notes"
msgid "Customer switched from: %1$s to %2$s."
msgstr "Le client est passé de : %1$s à %2$s."

#: includes/class-wc-subscriptions-switcher.php:444
#: includes/class-wc-subscriptions-switcher.php:542
#: includes/class-wc-subscriptions-switcher.php:2563
msgid "Upgrade or Downgrade"
msgstr "Mettre à niveau ou rétrograder"

#: includes/class-wc-subscriptions-switcher.php:440
msgid ""
"Customise the text displayed on the button next to the subscription on the "
"subscriber's account page. The default is \"Switch Subscription\", but you "
"may wish to change this to \"Upgrade\" or \"Change Subscription\"."
msgstr ""
"Personnalisez le texte affiché sur le bouton en regard de l’abonnement sur "
"la page du compte de l’abonné. Le texte par défaut est « Changer "
"d’abonnement », mais vous pouvez le remplacer par « Mettre à niveau » ou "
"« Modifier l’abonnement »."

#: includes/class-wc-subscriptions-switcher.php:439
msgid "Switch Button Text"
msgstr "Texte du bouton de changement"

#: includes/class-wc-subscriptions-switcher.php:424
msgid ""
"When switching to a subscription with a length, you can take into account "
"the payments already completed by the customer when determining how many "
"payments the subscriber needs to make for the new subscription."
msgstr ""
"Lors du passage à un abonnement avec une durée, vous pouvez prendre en "
"compte les paiements déjà effectués par le client pour déterminer le nombre "
"de paiements que l’abonné doit effectuer pour le nouvel abonnement."

#: includes/class-wc-subscriptions-switcher.php:423
msgid "Prorate Subscription Length"
msgstr "Durée de l’abonnement au prorata"

#: includes/class-wc-subscriptions-switcher.php:417
msgctxt "when to prorate signup fee when switching"
msgid "Always"
msgstr "Toujours"

#: includes/class-wc-subscriptions-switcher.php:416
msgctxt "when to prorate signup fee when switching"
msgid "Never (charge the full sign up fee)"
msgstr "Jamais (facturer l’intégralité des frais d’inscription)"

#: includes/class-wc-subscriptions-switcher.php:415
msgctxt "when to prorate signup fee when switching"
msgid "Never (do not charge a sign up fee)"
msgstr "Jamais (ne pas facturer de frais d’inscription)"

#: includes/class-wc-subscriptions-switcher.php:408
msgid ""
"When switching to a subscription with a sign up fee, you can require the "
"customer pay only the gap between the existing subscription's sign up fee "
"and the new subscription's sign up fee (if any)."
msgstr ""
"Lors du passage à un abonnement avec des frais d’inscription, vous pouvez "
"demander au client de ne payer que l’écart entre les frais d’inscription de "
"l’abonnement existant et ceux du nouvel abonnement (le cas échéant)."

#: includes/class-wc-subscriptions-switcher.php:407
msgid "Prorate Sign up Fee"
msgstr "Frais d’inscription au prorata"

#: includes/class-wc-subscriptions-switcher.php:401
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades & Downgrades of All Subscription Products"
msgstr ""
"Pour les mises à niveau et les rétrogradations de tous les produits "
"d’abonnement"

#: includes/class-wc-subscriptions-switcher.php:400
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades & Downgrades of Virtual Subscription Products Only"
msgstr ""
"Pour les mises à niveau et les rétrogradations des produits d’abonnement "
"virtuels uniquement"

#: includes/class-wc-subscriptions-switcher.php:399
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades of All Subscription Products"
msgstr "Pour les mises à niveau de tous les produits d’abonnement"

#: includes/class-wc-subscriptions-switcher.php:398
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades of Virtual Subscription Products Only"
msgstr "Pour les mises à niveau des produits d’abonnement virtuels uniquement"

#: includes/class-wc-subscriptions-switcher.php:390
msgid ""
"When switching to a subscription with a different recurring payment or "
"billing period, should the price paid for the existing billing period be "
"prorated when switching to the new subscription?"
msgstr ""
"Lors du passage à un abonnement avec une période de paiement ou de "
"facturation récurrente différente, le prix payé pour la période de "
"facturation existante doit-il être calculé au prorata lors du passage au "
"nouvel abonnement ?"

#: includes/class-wc-subscriptions-switcher.php:389
msgid "Prorate Recurring Payment"
msgstr "Paiement récurrent au prorata"

#: includes/class-wc-subscriptions-switcher.php:490
msgctxt "when to allow switching"
msgid "Between Grouped Subscriptions"
msgstr "Entre des abonnements groupés"

#: includes/class-wc-subscriptions-switcher.php:486
msgctxt "when to allow switching"
msgid "Between Subscription Variations"
msgstr "Entre des variantes d’abonnement"

#: includes/class-wc-subscriptions-switcher.php:397
#: includes/class-wc-subscriptions-switcher.php:431
msgctxt "when to allow a setting"
msgid "Never"
msgstr "Jamais"

#: includes/class-wc-subscriptions-switcher.php:479
msgid "Allow Switching"
msgstr "Autoriser le changement"

#. translators: placeholders are opening and closing link tags
#: includes/class-wc-subscriptions-switcher.php:379
msgid ""
"Allow subscribers to switch (upgrade or downgrade) between different "
"subscriptions. %1$sLearn more%2$s."
msgstr ""
"Autorisez les abonnés à basculer (mettre à niveau ou rétrograder) entre "
"différents abonnements. %1$sEn savoir plus%2$s."

#: includes/class-wc-subscriptions-switcher.php:376
msgid "Switching"
msgstr "Changement"

#. translators: 1$: is the "You have already subscribed to this product"
#. notice, 2$-4$: opening/closing link tags, 3$: an order number
#: includes/class-wc-subscriptions-switcher.php:281
msgid ""
"%1$s Complete payment on %2$sOrder %3$s%4$s to be able to change your "
"subscription."
msgstr ""
"%1$s Effectuez le paiement sur la %2$scommande %3$s%4$s pour pouvoir changer "
"votre abonnement."

#: includes/class-wc-subscriptions-switcher.php:272
msgid ""
"You have already subscribed to this product and it is limited to one per "
"customer. You can not purchase the product again."
msgstr ""
"Vous êtes déjà abonné à ce produit et il est limité à un par client. Vous ne "
"pouvez pas racheter le produit."

#: includes/class-wc-subscriptions-switcher.php:230
#: includes/class-wc-subscriptions-switcher.php:1249
msgid ""
"Your cart contained an invalid subscription switch request. It has been "
"removed."
msgid_plural ""
"Your cart contained invalid subscription switch requests. They have been "
"removed."
msgstr[0] ""
"Votre panier contenait une demande de changement d’abonnement non valide. "
"Elle a été supprimée."
msgstr[1] ""
"Votre panier contenait des demandes de changement d’abonnement non valides. "
"Elles ont été supprimées."

#: includes/class-wc-subscriptions-switcher.php:190
msgid "Choose a new subscription."
msgstr "Choisissez un nouvel abonnement."

#: includes/class-wc-subscriptions-switcher.php:188
msgid ""
"You have a subscription to this product. Choosing a new subscription will "
"replace your existing subscription."
msgstr ""
"Vous avez un abonnement à ce produit. Choisir un nouvel abonnement "
"remplacera votre abonnement existant."

#: includes/class-wc-subscriptions-renewal-order.php:181
msgid "Subscription renewal orders cannot be cancelled."
msgstr ""
"Les commandes de renouvellement d’abonnement ne peuvent pas être annulées."

#. translators: placeholder is order ID
#: includes/class-wc-subscriptions-renewal-order.php:161
msgid "Order %s created to record renewal."
msgstr "Commande %s créée pour enregistrer le renouvellement."

#. translators: 1$: subscription string (e.g. "$15 on March 15th every 3 years
#. for 6 years with 2 months free trial"), 2$: signup fee price (e.g. "and a
#. $30 sign-up fee").
#: includes/class-wc-subscriptions-product.php:398
msgid "%1$s and a %2$s sign-up fee"
msgstr "%1$s et des frais d’inscription de %2$s"

#. translators: 1$: subscription string (e.g. "$15 on March 15th every 3 years
#. for 6 years"), 2$: trial length (e.g.: "with 4 months free trial").
#: includes/class-wc-subscriptions-product.php:393
msgid "%1$s with %2$s free trial"
msgstr "%1$s avec un essai gratuit de %2$s"

#. translators: 1$: subscription string (e.g. "$10 up front then $5 on March
#. 23rd every 3rd year"), 2$: length (e.g. "4 years").
#. translators: 1$: subscription string (e.g. "$10 up front then $5 on March
#. 23rd every 3rd year"), 2$: length (e.g. "4 years")
#: includes/class-wc-subscriptions-product.php:387
#: includes/wcs-formatting-functions.php:209
msgid "%1$s for %2$s"
msgstr "%1$s pour %2$s"

#. translators: 1$: recurring amount, 2$: subscription period (e.g. "month" or
#. "3 months") (e.g. "$15 / month" or "$15 every 2nd month").
#. translators: 1$: recurring amount, 2$: subscription period (e.g. "month" or
#. "3 months") (e.g. "$15 / month" or "$15 every 2nd month")
#: includes/class-wc-subscriptions-product.php:367
#: includes/wcs-formatting-functions.php:198
msgid "%1$s / %2$s"
msgid_plural "%1$s every %2$s"
msgstr[0] "%1$s / %2$s"
msgstr[1] "%1$s chaque %2$s"

#. translators: 1$: recurring amount, 2$: month (e.g. "March"), 3$: day of the
#. month (e.g. "23rd").
#. translators: 1$: recurring amount, 2$: month (e.g. "March"), 3$: day of the
#. month (e.g. "23rd") (e.g. "$15 on March 15th every 3rd year")
#: includes/class-wc-subscriptions-product.php:355
#: includes/wcs-formatting-functions.php:187
msgid "%1$s on %2$s %3$s every %4$s year"
msgstr "%1$s le %2$s %3$s chaque %4$s année"

#. translators: 1$: <price> on, 2$: <date>, 3$: <month> each year (e.g. "$15 on
#. March 15th each year").
#. translators: 1$: recurring amount, 2$: month (e.g. "March"), 3$: day of the
#. month (e.g. "23rd") (e.g. "$15 on March 15th every 3rd year")
#: includes/class-wc-subscriptions-product.php:347
#: includes/wcs-formatting-functions.php:178
msgid "%1$s on %2$s %3$s each year"
msgstr "%1$s le %2$s %3$s chaque année"

#. translators: 1$: <price> on the, 2$: <date> day of every, 3$: <interval>
#. month (e.g. "$10 on the 23rd day of every 2nd month").
#. translators: 1$: recurring amount, 2$: day of the month (e.g. "23rd") (e.g.
#. "$5 every 23rd of each month")
#: includes/class-wc-subscriptions-product.php:335
#: includes/wcs-formatting-functions.php:165
msgid "%1$s on the %2$s day of every %3$s month"
msgstr "%1$s le %2$s jour de chaque %3$s mois"

#. translators: 1$: recurring amount, 2$: interval (e.g. "3rd") (e.g. "$10 on
#. the last day of every 3rd month").
#. translators: 1$: recurring amount, 2$: interval (e.g. "3rd") (e.g. "$10 on
#. the last day of every 3rd month")
#: includes/class-wc-subscriptions-product.php:328
#: includes/wcs-formatting-functions.php:162
msgid "%1$s on the last day of every %2$s month"
msgstr "%1$s le dernier jour de chaque %2$s mois"

#. translators: 1$: recurring amount, 2$: day of the month (e.g. "23rd") (e.g.
#. "$5 every 23rd of each month").
#. translators: 1$: recurring amount, 2$: day of the month (e.g. "23rd") (e.g.
#. "$5 every 23rd of each month")
#: includes/class-wc-subscriptions-product.php:319
#: includes/wcs-formatting-functions.php:146
msgid "%1$s on the %2$s of each month"
msgstr "%1$s le %2$s de chaque mois"

#. translators: placeholder is recurring amount.
#. translators: placeholder is recurring amount
#: includes/class-wc-subscriptions-product.php:315
#: includes/wcs-formatting-functions.php:143
msgid "%s on the last day of each month"
msgstr "%s le dernier jour de chaque mois"

#. translators: 1$: recurring amount string, 2$: period, 3$: day of the week
#. (e.g. "$10 every 2nd week on Wednesday").
#. translators: 1$: recurring amount string, 2$: period, 3$: day of the week
#. (e.g. "$10 every 2nd week on Wednesday")
#: includes/class-wc-subscriptions-product.php:304
#: includes/wcs-formatting-functions.php:125
msgid "%1$s every %2$s on %3$s"
msgstr "%1$s chaque %2$s le %3$s"

#. translators: 1$: recurring amount string, 2$: day of the week (e.g. "$10
#. every Wednesday").
#. translators: 1$: recurring amount string, 2$: day of the week (e.g. "$10
#. every Wednesday")
#. translators: %1$: recurring amount (e.g. "$15"), %2$: subscription period
#. (e.g. "month") (e.g. "$15 every 2nd month")
#: includes/class-wc-subscriptions-product.php:300
#: includes/wcs-formatting-functions.php:116
#: includes/wcs-formatting-functions.php:201
msgid "%1$s every %2$s"
msgstr "%1$s chaque %2$s"

#: includes/class-wc-subscriptions-order.php:748
msgctxt "An order type"
msgid "Original"
msgstr "Taille d’origine"

#. translators: placeholders are opening and closing link tags
#: includes/class-wc-subscriptions-order.php:386
msgid "View the status of your subscription in %1$syour account%2$s."
msgid_plural "View the status of your subscriptions in %1$syour account%2$s."
msgstr[0] "Affichez l’état de votre abonnement dans %1$svotre compte%2$s."
msgstr[1] "Affichez l’état de vos abonnements dans %1$svotre compte%2$s."

#: includes/class-wc-subscriptions-order.php:379
msgid "Your subscription will be activated when payment clears."
msgid_plural "Your subscriptions will be activated when payment clears."
msgstr[0] "Votre abonnement sera activé une fois le paiement effectué."
msgstr[1] "Vos abonnements seront activés une fois le paiement effectué."

#: includes/class-wc-subscriptions-manager.php:2310
msgid "Date Changed"
msgstr "Date de modification"

#: includes/class-wc-subscriptions-manager.php:2285
msgid "Please enter all date fields."
msgstr "Veuillez saisir tous les champs de date."

#: includes/class-wc-subscriptions-manager.php:2281
msgid "Only store managers can edit payment dates."
msgstr "Seuls les gérants de boutique peuvent modifier les dates de paiement."

#: includes/class-wc-subscriptions-manager.php:2277
msgid "Invalid security token, please reload the page and try again."
msgstr "Jeton de sécurité non valide, veuillez recharger la page et réessayer."

#. translators: placeholder is subscription ID
#: includes/class-wc-subscriptions-manager.php:2186
msgid "Failed sign-up for subscription %s."
msgstr "Échec de l’inscription pour l’abonnement %s."

#: includes/class-wc-subscriptions-manager.php:1853
msgid "Change"
msgstr "Modifier"

#. translators: all fields are full html nodes: 1$: month input, 2$: day input,
#. 3$: year input. Change the order if you'd like
#: includes/class-wc-subscriptions-manager.php:1848
msgid "%1$s%2$s, %3$s"
msgstr "%1$s%2$s, %3$s"

#. translators: all fields are full html nodes: 1$: month input, 2$: day input,
#. 3$: year input, 4$: hour input, 5$: minute input. Change the order if you'd
#. like
#: includes/class-wc-subscriptions-manager.php:1844
msgid "%1$s%2$s, %3$s @ %4$s : %5$s"
msgstr "%1$s%2$s, %3$s @ %4$s : %5$s"

#. translators: 1$: month number (e.g. "01"), 2$: month abbreviation (e.g.
#. "Jan")
#: includes/class-wc-subscriptions-manager.php:1831
msgctxt "used in a select box"
msgid "%1$s-%2$s"
msgstr "%1$s-%2$s"

#: includes/class-wc-subscriptions-manager.php:567
msgid "Pending subscription created."
msgstr "Abonnement en attente créé."

#: includes/class-wc-subscriptions-manager.php:522
msgid "Error: Unable to add product to created subscription. Please try again."
msgstr ""
"Erreur : Impossible d’ajouter le produit à l’abonnement créé. Veuillez "
"réessayer."

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:426
msgid "Failed to process failed payment on subscription for order #%1$s: %2$s"
msgstr ""
"Échec du traitement du paiement échoué de l’abonnement pour la commande "
"n°%1$s : %2$s"

#: includes/class-wc-subscriptions-manager.php:416
msgid "Subscription sign up failed."
msgstr "Échec de l’inscription à l’abonnement."

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:390
msgid "Failed to set subscription as expired for order #%1$s: %2$s"
msgstr ""
"Échec du marquage de l’abonnement comme expiré pour la commande n°%1$s : %2$s"

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:362
msgid "Failed to cancel subscription after order #%1$s was cancelled: %2$s"
msgstr ""
"Échec de l’annulation de l’abonnement après l’annulation de la commande "
"n°%1$s : %2$s"

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:334
msgid ""
"Failed to update subscription status after order #%1$s was put on-hold: %2$s"
msgstr ""
"Échec de la mise à jour de l’état de l’abonnement après la mise en attente "
"de la commande n°%1$s : %2$s"

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:306
msgid "Failed to activate subscription status for order #%1$s: %2$s"
msgstr ""
"Échec de l’activation de l’état de l’abonnement pour la commande n°%1$s: %2$s"

#: includes/class-wc-subscriptions-coupon.php:479
msgid ""
"Sorry, this coupon is only valid for subscription products with a sign-up "
"fee."
msgstr ""
"Désolé, ce code promo n’est valide que pour les produits d’abonnement avec "
"des frais d’inscription."

#: includes/class-wc-subscriptions-coupon.php:468
msgid "Sorry, this coupon is only valid for subscription products."
msgstr "Désolé, ce code promo n’est valide que pour les produits d’abonnement."

#: includes/class-wc-subscriptions-coupon.php:463
msgid "Sorry, this coupon is only valid for new subscriptions."
msgstr "Désolé, ce code promo n’est valide que pour les nouveaux abonnements."

#: includes/class-wc-subscriptions-coupon.php:457
msgid ""
"Sorry, this coupon is only valid for an initial payment and the cart does "
"not require an initial payment."
msgstr ""
"Désolé, ce code promo n’est valide que pour un paiement initial et le "
"produit ne nécessite pas de paiement initial."

#: includes/class-wc-subscriptions-coupon.php:167
msgid "Recurring Product % Discount"
msgstr "% de remise sur les produits récurrents"

#: includes/class-wc-subscriptions-coupon.php:166
msgid "Recurring Product Discount"
msgstr "Remise sur les produits récurrents"

#: includes/class-wc-subscriptions-coupon.php:165
msgid "Sign Up Fee % Discount"
msgstr "% de remise sur les frais d’inscription"

#: includes/class-wc-subscriptions-coupon.php:164
msgid "Sign Up Fee Discount"
msgstr "Remise sur les frais d’inscription"

#: includes/class-wc-subscriptions-manager.php:500
msgid "Error: Unable to create subscription. Please try again."
msgstr "Erreur : Impossible de créer l’abonnement. Veuillez réessayer."

#. translators: placeholder is an internal error number
#: includes/class-wc-subscriptions-checkout.php:225
msgid "Error %d: Unable to create order. Please try again."
msgstr "Erreur %d : Impossible de créer la commande. Veuillez réessayer."

#. translators: placeholder is an internal error number
#: includes/class-wc-subscriptions-checkout.php:196
#: includes/class-wc-subscriptions-checkout.php:389
msgid "Error %d: Unable to create subscription. Please try again."
msgstr "Erreur %d : Impossible de créer l’abonnement. Veuillez réessayer."

#. translators: 1: old payment title, 2: new payment title.
#: includes/class-wc-subscriptions-change-payment-gateway.php:534
msgctxt "%1$s: old payment title, %2$s: new payment title"
msgid ""
"Payment method changed from \"%1$s\" to \"%2$s\" by the subscriber from "
"their account page."
msgstr ""
"Le moyen de paiement est passé de « %1$s » à « %2$s » par l’abonné sur sa "
"page de compte."

#: includes/class-wc-subscriptions-change-payment-gateway.php:340
msgid "Payment method updated."
msgstr "Moyen de paiement mis à jour."

#: includes/class-wc-subscriptions-change-payment-gateway.php:259
msgid "Invalid order."
msgstr "Commande non valide."

#. translators: placeholder is either empty or "Next payment is due..."
#: includes/class-wc-subscriptions-change-payment-gateway.php:211
msgid "Choose a new payment method.%s"
msgstr "Choisissez un nouveau moyen de paiement.%s"

#. translators: placeholder is next payment's date
#: includes/class-wc-subscriptions-change-payment-gateway.php:205
msgid " Next payment is due %s."
msgstr " Le prochain paiement est dû le %s."

#: includes/class-wc-subscriptions-change-payment-gateway.php:256
#: includes/class-wcs-query.php:243
msgid "The payment method can not be changed for that subscription."
msgstr "Le moyen de paiement ne peut pas être modifié pour cet abonnement."

#: includes/class-wc-subscriptions-change-payment-gateway.php:253
#: includes/class-wcs-cart-resubscribe.php:78
#: includes/class-wcs-cart-resubscribe.php:129
#: includes/class-wcs-user-change-status-handler.php:111
#: includes/early-renewal/class-wcs-cart-early-renewal.php:95
msgid "That doesn't appear to be one of your subscriptions."
msgstr "Cela ne semble pas être l’un de vos abonnements."

#: includes/api/class-wc-rest-subscriptions-controller.php:293
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:172
msgid "Invalid subscription id."
msgstr "ID d’abonnement non valide."

#: includes/class-wc-subscriptions-change-payment-gateway.php:247
#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:106
msgid "There was an error with your request. Please try again."
msgstr "Une erreur est survenue lors de votre demande. Veuillez réessayer."

#: includes/class-wc-subscriptions-change-payment-gateway.php:181
msgid ""
"Sorry, this subscription change payment method request is invalid and cannot "
"be processed."
msgstr ""
"Désolé, cette demande de changement de moyen de paiement pour l’abonnement "
"n’est pas valide et ne peut pas être traitée."

#. translators: placeholder is the display name of the payment method
#: templates/checkout/subscription-receipt.php:37
msgid "Payment Method: %s"
msgstr "Moyen de paiement : %s"

#. translators: placeholder is the subscription's next payment date (either
#. human readable or normal date) wrapped in <strong> tags
#: templates/checkout/subscription-receipt.php:24
msgid "Next Payment Date: %s"
msgstr "Date du prochain paiement : %s"

#: includes/privacy/class-wcs-privacy-exporters.php:77
msgid "Subscription Number"
msgstr "Numéro d’abonnement"

#: includes/class-wc-subscriptions-cart.php:2218
msgid "now"
msgstr "maintenant"

#: includes/class-wc-subscriptions-cart-validator.php:136
#: includes/class-wc-subscriptions-cart.php:1542
msgid ""
"That subscription product can not be added to your cart as it already "
"contains a subscription renewal."
msgstr ""
"Ce produit d’abonnement ne peut pas être ajouté à votre panier car il "
"contient déjà un renouvellement d’abonnement."

#: includes/class-wc-subscriptions-cart.php:995
msgid "Please enter a valid postcode/ZIP."
msgstr "Veuillez saisir un code postal valide."

#. translators: $1: address type (Shipping Address / Billing Address), $2:
#. opening <strong> tag, $3: closing </strong> tag
#: includes/class-wc-subscriptions-addresses.php:84
msgid "Update the %1$s used for %2$sall%3$s of my active subscriptions"
msgstr "Mettre à jour l’%1$s utilisée pour %2$stous%3$s mes abonnements actifs"

#: includes/class-wc-subscriptions-addresses.php:71
msgid ""
"Both the shipping address used for the subscription and your default "
"shipping address for future purchases will be updated."
msgstr ""
"L’adresse de livraison utilisée pour l’abonnement et votre adresse de "
"livraison par défaut pour les achats futurs seront mises à jour."

#: includes/class-wc-subscriptions-addresses.php:47
msgid "Change address"
msgstr "Modifier l’adresse"

#: includes/class-wc-subscription.php:2098 wcs-functions.php:828
msgid "Payment method meta must be an array."
msgstr "Les métadonnées du moyen de paiement doivent être un tableau."

#: includes/admin/class-wcs-admin-post-types.php:961
#: includes/class-wc-subscription.php:2007
#: includes/class-wcs-change-payment-method-admin.php:168
msgid "Manual Renewal"
msgstr "Renouvellement manuel"

#: includes/class-wc-subscription.php:1790
msgid "Subscription Cancelled: maximum number of failed payments reached."
msgstr "Abonnement annulé : nombre maximal d’échecs de paiement atteint."

#: includes/class-wc-subscription.php:1785
msgid "Payment failed."
msgstr "Échec du paiement."

#. translators: %s: date type (e.g. "trial_end").
#: includes/class-wc-subscription.php:1334
msgid ""
"The %s date of a subscription can not be deleted. You must delete the order."
msgstr ""
"La date de %s d’un abonnement ne peut pas être supprimée. Vous devez "
"supprimer la commande."

#: includes/class-wc-subscription.php:1329
msgid "The start date of a subscription can not be deleted, only updated."
msgstr ""
"La date de début d’un abonnement ne peut pas être supprimée, uniquement mise "
"à jour."

#. translators: %s: date type (e.g. "next_payment").
#: includes/class-wc-subscription.php:2432
msgid "The %s date must occur after the start date."
msgstr "La date de %s doit être postérieure à la date de début."

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2427
msgid "The %s date must occur after the trial end date."
msgstr "La date de %s doit être postérieure à la date de fin d’essai."

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2421
msgid "The %s date must occur after the next payment date."
msgstr "La date de %s doit être postérieure à la date du prochain paiement."

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2416
msgid "The %s date must occur after the last payment date."
msgstr "La date de %s doit être postérieure à la date du dernier paiement."

#. translators: placeholder is date type (e.g. "end", "next_payment"...)
#: includes/class-wc-subscription.php:2372
msgctxt "appears in an error message if date is wrong format"
msgid "Invalid %s date. The date must be of the format: \"Y-m-d H:i:s\"."
msgstr "Date %s non valide. La date doit être au format : « Y-m-d H:i:s »."

#: includes/class-wc-subscription.php:2345
msgid ""
"Invalid data. First parameter has a date that is not in the registered date "
"types."
msgstr ""
"Données non valides. Le premier paramètre a une date qui n’est pas dans les "
"types de date enregistrés."

#: includes/class-wc-subscription.php:2338
msgid "Invalid data. First parameter was empty when passed to update_dates()."
msgstr ""
"Données non valides. Le premier paramètre était vide lorsqu’il a été passé à "
"update_dates()."

#: includes/class-wc-subscription.php:2334
msgid "Invalid format. First parameter needs to be an array."
msgstr "Format non valide. Le premier paramètre doit être un tableau."

#: includes/class-wc-subscription.php:1218
msgctxt "original denotes there is no date to display"
msgid "-"
msgstr "-"

#: includes/class-wc-subscription.php:1210
msgid "Not yet ended"
msgstr "Pas encore terminé"

#. translators: placeholder is human time diff (e.g. "3 weeks")
#: includes/class-wc-subscription.php:1203
#: includes/wcs-formatting-functions.php:246
msgid "%s ago"
msgstr "il y a %s"

#. translators: placeholder is human time diff (e.g. "3 weeks")
#: includes/class-wc-subscription.php:1200
#: includes/class-wc-subscriptions-manager.php:2304
msgid "In %s"
msgstr "Dans %s"

#. translators: 1: old subscription status 2: new subscription status
#: includes/class-wc-subscription.php:576
msgid "Status changed from %1$s to %2$s."
msgstr "État passé de %1$s à %2$s."

#. translators: %s: subscription status.
#: includes/class-wc-subscription.php:423
msgid "Unable to change subscription status to \"%s\"."
msgstr "Impossible de modifier l’état d’abonnement sur « %s »."

#: includes/api/legacy/class-wc-api-subscriptions.php:398 wcs-functions.php:147
msgid "Invalid subscription billing period given."
msgstr "Période de facturation de l’abonnement donnée non valide."

#: includes/api/legacy/class-wc-api-subscriptions.php:387 wcs-functions.php:152
msgid ""
"Invalid subscription billing interval given. Must be an integer greater than "
"0."
msgstr ""
"Intervalle de facturation de l’abonnement donné non valide. Doit être un "
"entier supérieur à 0."

#. translators: 1$: gateway id, 2$: error message
#: includes/api/legacy/class-wc-api-subscriptions.php:352
msgid ""
"Subscription payment method could not be set to %1$s and has been set to "
"manual with error message: %2$s"
msgstr ""
"Le moyen de paiement de l’abonnement n’a pas pu être défini sur %1$s et a "
"été défini sur manuel avec le message d’erreur : %2$s"

#: includes/api/class-wc-rest-subscriptions-controller.php:364
#: includes/api/legacy/class-wc-api-subscriptions.php:314
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:306
msgid ""
"Gateway does not support admin changing the payment method on a Subscription."
msgstr ""
"La passerelle ne prend pas en charge la modification par l’administrateur du "
"moyen de paiement sur un abonnement."

#: includes/api/legacy/class-wc-api-subscriptions.php:248
msgid "The requested subscription cannot be edited."
msgstr "L’abonnement demandé ne peut pas être modifié."

#: includes/api/legacy/class-wc-api-subscriptions.php:173
msgid "You do not have permission to create subscriptions"
msgstr "Vous n’avez pas le droit de créer des abonnements"

#: includes/api/legacy/class-wc-api-subscriptions.php:124
msgid "You do not have permission to read the subscriptions count"
msgstr "Vous n’avez pas le droit de lire le comptage des abonnements"

#: includes/api/legacy/class-wc-api-subscriptions.php:102 wcs-functions.php:178
msgid "Invalid subscription status given."
msgstr "État d’abonnement donné non valide."

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:63
msgid "Error: unable to find timezone of your browser."
msgstr "Erreur : le fuseau horaire de votre navigateur est introuvable."

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:63
msgid "Timezone:"
msgstr "Fuseau horaire :"

#: templates/admin/html-variation-price.php:56
msgid "Billing Period:"
msgstr "Période de facturation :"

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:43
msgid "Recurring:"
msgstr "Récurrent :"

#: includes/admin/class-wcs-admin-post-types.php:425
msgid "Total"
msgstr "Total"

#: includes/admin/class-wcs-admin-post-types.php:422
#: includes/admin/meta-boxes/views/html-related-orders-table.php:20
#: templates/myaccount/my-subscriptions.php:22
#: templates/myaccount/my-subscriptions.php:37
#: templates/myaccount/related-orders.php:24
#: templates/myaccount/related-orders.php:50
#: templates/myaccount/related-subscriptions.php:22
#: templates/myaccount/related-subscriptions.php:36
#: templates/myaccount/subscription-details.php:18
msgid "Status"
msgstr "État"

#: includes/admin/meta-boxes/views/html-related-orders-table.php:19
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:776
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:195
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:209
#: templates/myaccount/related-orders.php:23
#: templates/myaccount/related-orders.php:47
msgid "Date"
msgstr "Date"

#: includes/admin/meta-boxes/views/html-related-orders-table.php:18
msgid "Relationship"
msgstr "Relation"

#: includes/admin/meta-boxes/views/html-related-orders-table.php:17
#: templates/myaccount/related-orders.php:42
msgid "Order Number"
msgstr "Numéro de commande"

#: includes/admin/meta-boxes/views/html-related-orders-row.php:36
#: includes/admin/meta-boxes/views/html-retries-table.php:47
msgid "Unpublished"
msgstr "Non publié"

#. translators: php date format
#: includes/admin/meta-boxes/views/html-related-orders-row.php:33
#: includes/admin/meta-boxes/views/html-retries-table.php:44
msgctxt "post date"
msgid "Y/m/d g:i:s A"
msgstr ""
"d/m/Y à G h i mi\n"
" s s"

#. translators: placeholder is an order number.
#. translators: placeholder is an order ID.
#. translators: %s: order number.
#. translators: %s: order ID.
#: includes/admin/meta-boxes/views/html-related-orders-row.php:21
#: includes/admin/meta-boxes/views/html-unknown-related-orders-row.php:18
#: includes/class-wc-subscriptions-renewal-order.php:158
#: includes/early-renewal/class-wcs-cart-early-renewal.php:309
#: includes/early-renewal/wcs-early-renewal-functions.php:162
#: templates/myaccount/my-subscriptions.php:34
#: templates/myaccount/related-orders.php:44
#: templates/myaccount/related-subscriptions.php:33
msgctxt "hash before order number"
msgid "#%s"
msgstr "n°%s"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:295
msgid "Customer's notes about the order"
msgstr "Notes du client à propos de la commande"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:242
msgid "No shipping address set."
msgstr "Aucune adresse de livraison."

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:170
#: includes/class-wcs-change-payment-method-admin.php:38
#: includes/class-wcs-change-payment-method-admin.php:51
msgid "Payment Method"
msgstr "Moyen de paiement"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:150
msgid "No billing address set."
msgstr "Aucune adresse de facturation."

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:148
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:150
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:240
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:242
msgid "Address"
msgstr "Adresse"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:93
msgid "Subscription status:"
msgstr "État de l’abonnement :"

#: includes/admin/class-wcs-admin-post-types.php:1114
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:85
msgid "Search for a customer&hellip;"
msgstr "Recherche d’un client&hellip;"

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:55
msgid "Customer:"
msgstr "Client :"

#. translators: placeholder is the ID of the subscription
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:48
msgctxt "edit subscription header"
msgid "Subscription #%s details"
msgstr "Détails de l’abonnement n°%s"

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:93
msgctxt "relation to order"
msgid "Renewal Order"
msgstr "Commande de renouvellement"

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:96
msgctxt "relation to order"
msgid "Parent Order"
msgstr "Commande parente"

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:82
msgctxt "relation to order"
msgid "Initial Subscription"
msgstr "Abonnement initial"

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:99
msgctxt "relation to order"
msgid "Resubscribed Subscription"
msgstr "Abonnement réabonné"

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:77
msgctxt "relation to order"
msgid "Subscription"
msgstr "Abonnement"

#: includes/admin/class-wcs-admin-post-types.php:955
msgid "None"
msgstr "Aucun"

#: includes/admin/class-wcs-admin-post-types.php:954
msgid "Any Payment Method"
msgstr "Tout moyen de paiement"

#. translators: php date string
#: includes/admin/class-wcs-admin-post-types.php:918
msgid "Subscription draft updated."
msgstr "Brouillon d’abonnement mis à jour."

#. translators: php date string
#: includes/admin/class-wcs-admin-post-types.php:917
msgctxt "used in \"Subscription scheduled for <date>\""
msgid "M j, Y @ G:i"
msgstr "j M Y @ G:i"

#. translators: php date string
#: includes/admin/class-wcs-admin-post-types.php:917
msgid "Subscription scheduled for: %1$s."
msgstr "Abonnement planifié pour : %1$s."

#: includes/admin/class-wcs-admin-post-types.php:915
msgid "Subscription submitted."
msgstr "Abonnement envoyé."

#: includes/admin/class-wcs-admin-post-types.php:914
msgid "Subscription saved."
msgstr "Abonnement enregistré."

#. translators: placeholder is previous post title
#: includes/admin/class-wcs-admin-post-types.php:912
msgctxt "used in post updated messages"
msgid "Subscription restored to revision from %s"
msgstr "Abonnement restauré à la révision à partir de %s"

#: includes/admin/class-wcs-admin-post-types.php:909
msgid "Custom field deleted."
msgstr "Champ personnalisé supprimé."

#: includes/admin/class-wcs-admin-post-types.php:908
msgid "Custom field updated."
msgstr "Champ personnalisé mis à jour."

#. translators: placeholder is previous post title
#: includes/admin/class-wcs-admin-post-types.php:907
#: includes/admin/class-wcs-admin-post-types.php:910
#: includes/admin/class-wcs-admin-post-types.php:913
msgid "Subscription updated."
msgstr "Abonnement mis à jour."

#. translators: %d: item count.
#: includes/admin/class-wcs-admin-post-types.php:590
msgid "%d item"
msgid_plural "%d items"
msgstr[0] "%d élément"
msgstr[1] "%d éléments"

#: includes/admin/class-wcs-admin-post-types.php:527
msgctxt "meaning billing address"
msgid "Billing:"
msgstr "Facturation :"

#: includes/admin/class-wcs-admin-post-types.php:499
msgid "Cancel Now"
msgstr "Annuler maintenant"

#: includes/admin/class-wcs-admin-post-types.php:493
msgid "Delete this item permanently"
msgstr "Supprimer cet article définitivement"

#: includes/admin/class-wcs-admin-post-types.php:489
msgid "Move this item to the Trash"
msgstr "Déplacer cet article vers la corbeille"

#: includes/admin/class-wcs-admin-post-types.php:487
#: includes/class-wc-subscriptions-product.php:767
msgid "Restore"
msgstr "Restaurer"

#: includes/admin/class-wcs-admin-post-types.php:487
#: includes/class-wc-subscriptions-product.php:766
msgid "Restore this item from the Trash"
msgstr "Restaurer cet article depuis la corbeille"

#: includes/admin/class-wcs-admin-post-types.php:475
#: includes/admin/class-wcs-admin-post-types.php:493
msgid "Delete Permanently"
msgstr "Supprimer définitivement"

#: includes/admin/class-wcs-admin-post-types.php:474
#: includes/admin/class-wcs-admin-post-types.php:489
msgid "Trash"
msgstr "Corbeille"

#: includes/early-renewal/class-wcs-cart-early-renewal.php:367
msgid "Cancel"
msgstr "Annuler"

#: includes/admin/class-wcs-admin-post-types.php:472
#: includes/wcs-user-functions.php:333
msgid "Suspend"
msgstr "Suspendre"

#: includes/admin/class-wcs-admin-post-types.php:471
#: includes/wcs-user-functions.php:338
msgid "Reactivate"
msgstr "Réactiver"

#. translators: 1$: is the number of subscriptions not updated, 2$: is the
#. error message
#: includes/admin/class-wcs-admin-post-types.php:398
msgid "%1$s subscription could not be updated: %2$s"
msgid_plural "%1$s subscriptions could not be updated: %2$s"
msgstr[0] "%1$s abonnement n’a pas pu être mis à jour : %2$s"
msgstr[1] "%1$s abonnements n’ont pas pu être mis à jour : %2$s"

#. translators: placeholder is the number of subscriptions updated
#: includes/admin/class-wcs-admin-post-types.php:391
msgid "%s subscription status changed."
msgid_plural "%s subscription statuses changed."
msgstr[0] "%s état d’abonnement modifié."
msgstr[1] "%s états d’abonnement modifiés."

#: includes/admin/class-wcs-admin-post-types.php:259
msgctxt "an action on a subscription"
msgid "Put on-hold"
msgstr "Mettre en attente"

#: includes/admin/class-wcs-admin-post-types.php:214
msgid "Search for a product&hellip;"
msgstr "Rechercher un produit…"

#: includes/admin/class-wcs-admin-meta-boxes.php:211
msgid "Create pending renewal order requested by admin action."
msgstr ""
"Créez une commande de renouvellement en attente demandée par une action de "
"l’administrateur."

#: includes/admin/class-wcs-admin-meta-boxes.php:200
msgid "Process renewal order action requested by admin."
msgstr ""
"Traitez l’action de commande de renouvellement demandée par l’administrateur."

#: includes/admin/class-wcs-admin-meta-boxes.php:181
msgid "Create pending renewal order"
msgstr "Créer une commande de renouvellement en attente"

#: includes/admin/class-wcs-admin-meta-boxes.php:177
msgid "Process renewal"
msgstr "Traiter le renouvellement"

#: includes/admin/class-wcs-admin-meta-boxes.php:130
msgid ""
"Are you sure you want to process a renewal?\n"
"\n"
"This will charge the customer and email them the renewal order (if emails "
"are enabled)."
msgstr ""
"Voulez-vous vraiment traiter un renouvellement ?\n"
"\n"
"Cela facturera le client et lui enverra la commande de renouvellement par e-"
"mail (si les e-mails sont activés)."

#: includes/admin/class-wcs-admin-meta-boxes.php:129
msgid "Please enter a date after the next payment."
msgstr "Veuillez saisir une date après le prochain paiement."

#: includes/admin/class-wcs-admin-meta-boxes.php:128
msgid "Please enter a date before the next payment."
msgstr "Veuillez saisir une date avant le prochain paiement."

#: includes/admin/class-wcs-admin-meta-boxes.php:126
#: includes/admin/class-wcs-admin-meta-boxes.php:127
msgid "Please enter a date after the start date."
msgstr "Veuillez saisir une date après la date de début."

#: includes/admin/class-wcs-admin-meta-boxes.php:125
msgid "Please enter a date after the trial end."
msgstr "Veuillez saisir une date après la fin de l’essai."

#: includes/admin/class-wcs-admin-meta-boxes.php:124
msgid "Please enter a date at least one hour into the future."
msgstr "Veuillez saisir une date au moins une heure dans le futur."

#: includes/admin/class-wcs-admin-meta-boxes.php:123
msgid "Please enter a start date in the past."
msgstr "Veuillez saisir une date de début dans le passé."

#: includes/admin/class-wcs-admin-meta-boxes.php:73
msgctxt "meta box title"
msgid "Subscription Data"
msgstr "Données d’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:1791
msgid ""
"Supports automatic renewal payments with the WooCommerce Subscriptions "
"extension."
msgstr ""
"Prend en charge les paiements de renouvellement automatiques avec "
"l’extension WooCommerce Subscriptions."

#: includes/admin/class-wc-subscriptions-admin.php:1760
msgid "Automatic Recurring Payments"
msgstr "Paiements récurrents automatiques"

#: includes/admin/class-wc-subscriptions-admin.php:1730
#: includes/admin/class-wcs-admin-system-status.php:109
msgctxt "refers to live site"
msgid "Live"
msgstr "En direct"

#: includes/admin/class-wc-subscriptions-admin.php:1730
#: includes/admin/class-wcs-admin-system-status.php:109
msgctxt "refers to staging site"
msgid "Staging"
msgstr "Préproduction"

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:933
msgid "Subscriptions Ended"
msgstr "Abonnements terminés"

#: includes/admin/class-wc-subscriptions-admin.php:1724
#: includes/admin/class-wcs-admin-system-status.php:95
#: includes/admin/reports/class-wcs-report-cache-manager.php:316
msgid "No"
msgstr "Non"

#: includes/admin/class-wc-subscriptions-admin.php:1724
#: includes/admin/class-wc-subscriptions-admin.php:1791
#: includes/admin/class-wcs-admin-system-status.php:95
#: includes/admin/reports/class-wcs-report-cache-manager.php:316
msgid "Yes"
msgstr "Oui"

#: includes/admin/class-wc-subscriptions-admin.php:1723
#: includes/admin/class-wcs-admin-system-status.php:93
msgctxt "label that indicates whether debugging is turned on for the plugin"
msgid "WCS_DEBUG"
msgstr "WCS_DEBUG"

#. translators: placeholder is a time period (e.g. "4 weeks")
#: includes/admin/class-wc-subscriptions-admin.php:1661
msgid "The trial period can not exceed %s."
msgstr "La période d’essai ne peut pas dépasser %s."

#. translators: number of 1$: days, 2$: weeks, 3$: months, 4$: years
#: includes/admin/class-wc-subscriptions-admin.php:1656
msgid "The trial period can not exceed: %1$s, %2$s, %3$s or %4$s."
msgstr "La période d’essai ne peut pas dépasser : %1$s, %2$s, %3$s ou %4$s."

#. translators: placeholders are opening link tag, ID of sub, and closing link
#. tag
#: includes/admin/class-wc-subscriptions-admin.php:1627
#: includes/admin/class-wc-subscriptions-admin.php:1632
msgid "Showing orders for %1$sSubscription %2$s%3$s"
msgstr "Affichage des commandes pour %1$sAbonnement %2$s%3$s"

#. translators: placeholder is a number
#: includes/admin/class-wc-subscriptions-admin.php:1497
msgid "We can't find a subscription with ID #%d. Perhaps it was deleted?"
msgstr ""
"Nous ne trouvons pas d’abonnement avec l’ID n°%d. Peut-être a-t-il été "
"supprimé ?"

#: includes/admin/class-wc-subscriptions-admin.php:1414
#: includes/upgrades/templates/wcs-about-2-0.php:35
#: includes/upgrades/templates/wcs-about.php:34
#: woocommerce-subscriptions.php:1148
msgid "Settings"
msgstr "Réglages"

#: includes/admin/class-wc-subscriptions-admin.php:1413
msgid "Add a Subscription Product"
msgstr "Ajouter un produit d’abonnement"

#. translators: $1-$2: opening and closing <strong> tags, $3-$4: opening and
#. closing <em> tags
#: includes/admin/class-wc-subscriptions-admin.php:1395
msgid ""
"%1$sWooCommerce Subscriptions Installed%2$s &#8211; %3$sYou're ready to "
"start selling subscriptions!%4$s"
msgstr ""
"%1$sWooCommerce Subscriptions installé%2$s &#8211; %3$sVous êtes prêt à "
"commencer à vendre des abonnements !%4$s"

#. translators: $1-$2: opening and closing tags. Link to documents->payment
#. gateways, 3$-4$: opening and closing tags. Link to WooCommerce extensions
#. shop page
#: includes/admin/class-wc-subscriptions-admin.php:1932
msgid ""
"Find new gateways that %1$ssupport automatic subscription payments%2$s in "
"the official %3$sWooCommerce Marketplace%4$s."
msgstr ""
"Trouvez de nouvelles passerelles %1$sprenant en charge les paiements "
"d’abonnement automatiques%2$s sur la %3$splace de marché officielle de "
"WooCommerce%4$s."

#. translators: %s is a line break.
#: includes/admin/class-wc-subscriptions-admin.php:1354
msgid ""
"Enabling this grants access to new downloadable files added to a product "
"only after the next renewal is processed.%sBy default, access to new "
"downloadable files added to a product is granted immediately to any customer "
"that has an active subscription with that product."
msgstr ""
"L’activation de cette option donne accès aux nouveaux fichiers "
"téléchargeables ajoutés à un produit uniquement après le traitement du "
"prochain renouvellement.%sPar défaut, l’accès aux nouveaux fichiers "
"téléchargeables ajoutés à un produit est accordé immédiatement à tout client "
"disposant d’un abonnement actif avec ce produit."

#: includes/admin/class-wc-subscriptions-admin.php:1349
msgid "Enable dripping for downloadable content on subscription products."
msgstr ""
"Activez la diffusion pour le contenu téléchargeable sur les produits "
"d’abonnement."

#: includes/admin/class-wc-subscriptions-admin.php:1331
msgid ""
"Allow multiple subscriptions and products to be purchased simultaneously."
msgstr "Autorisez l’achat simultané de plusieurs abonnements et produits."

#: includes/admin/class-wc-subscriptions-admin.php:1330
msgid "Mixed Checkout"
msgstr "Validation de commande mixte"

#: includes/admin/class-wc-subscriptions-admin.php:1326
msgid ""
"Set a maximum number of times a customer can suspend their account for each "
"billing period. For example, for a value of 3 and a subscription billed "
"yearly, if the customer has suspended their account 3 times, they will not "
"be presented with the option to suspend their account until the next year. "
"Store managers will always be able to suspend an active subscription. Set "
"this to 0 to turn off the customer suspension feature completely."
msgstr ""
"Définissez un nombre maximal de fois qu’un client peut suspendre son compte "
"pour chaque période de facturation. Par exemple, pour une valeur de 3 et un "
"abonnement facturé annuellement, si le client a suspendu son compte 3 fois, "
"il ne se verra pas proposer la possibilité de suspendre son compte jusqu’à "
"l’année suivante. Les gérants de boutique pourront toujours suspendre un "
"abonnement actif. Définissez ce paramètre sur 0 pour désactiver complètement "
"la fonctionnalité de suspension du client."

#: includes/admin/class-wc-subscriptions-admin.php:1319
msgid "Customer Suspensions"
msgstr "Suspensions de client"

#: includes/admin/class-wc-subscriptions-admin.php:1312
msgctxt "options section heading"
msgid "Miscellaneous"
msgstr "Divers"

#: includes/admin/class-wc-subscriptions-admin.php:1296
msgid "Turn off Automatic Payments"
msgstr "Désactiver les paiements automatiques"

#. translators: placeholders are opening and closing link tags
#: includes/admin/class-wc-subscriptions-admin.php:1290
msgid ""
"With manual renewals, a customer's subscription is put on-hold until they "
"login and pay to renew it. %1$sLearn more%2$s."
msgstr ""
"Avec les renouvellements manuels, l’abonnement d’un client est mis en "
"attente jusqu’à ce qu’il se connecte et paie pour le renouveler. %1$sEn "
"savoir plus%2$s."

#: includes/admin/class-wc-subscriptions-admin.php:1285
msgid "Accept Manual Renewals"
msgstr "Accepter les renouvellements manuels"

#: includes/admin/class-wc-subscriptions-admin.php:1284
msgid "Manual Renewal Payments"
msgstr "Paiements de renouvellement manuels"

#: includes/admin/class-wc-subscriptions-admin.php:1277
msgctxt "option section heading"
msgid "Renewals"
msgstr "Renouvellements"

#: includes/admin/class-wc-subscriptions-admin.php:1261
msgid ""
"If a subscriber's subscription is manually cancelled or expires, she will be "
"assigned this role."
msgstr ""
"Si l’abonnement d’un abonné est annulé manuellement ou expire, ce rôle lui "
"sera attribué."

#: includes/admin/class-wc-subscriptions-admin.php:1260
msgid "Inactive Subscriber Role"
msgstr "Rôle d’abonné inactif"

#: includes/admin/class-wc-subscriptions-admin.php:1249
msgid ""
"When a subscription is activated, either manually or after a successful "
"purchase, new users will be assigned this role."
msgstr ""
"Lorsqu’un abonnement est activé, manuellement ou après un achat réussi, les "
"nouveaux utilisateurs se verront attribuer ce rôle."

#: includes/admin/class-wc-subscriptions-admin.php:1248
msgid "Subscriber Default Role"
msgstr "Rôle par défaut des abonnés"

#. translators: placeholders are <em> tags
#: includes/admin/class-wc-subscriptions-admin.php:1243
msgid ""
"Choose the default roles to assign to active and inactive subscribers. For "
"record keeping purposes, a user account must be created for subscribers. "
"Users with the %1$sadministrator%2$s role, such as yourself, will never be "
"allocated these roles to prevent locking out administrators."
msgstr ""
"Choisissez les rôles par défaut à attribuer aux abonnés actifs et inactifs. "
"À des fins de tenue de rapports, un compte d’utilisateur doit être créé pour "
"les abonnés. Les utilisateurs avec le rôle %1$sadministrateur%2$s, comme "
"vous-même, ne se verront jamais attribuer ces rôles pour éviter le blocage "
"des administrateurs."

#: includes/admin/class-wc-subscriptions-admin.php:1240
msgid "Roles"
msgstr "Rôles"

#: includes/admin/class-wc-subscriptions-admin.php:1224
msgid ""
"Use this field to customise the text displayed on the checkout button when "
"an order contains a subscription. Normally the checkout submission button "
"displays \"Place order\". When the cart contains a subscription, this is "
"changed to \"Sign up now\"."
msgstr ""
"Utilisez ce champ pour personnaliser le libellé affiché sur le bouton de "
"paiement lorsqu’une commande contient un abonnement. Normalement, le bouton "
"d’envoi de la validation de commande affiche « Passer la commande ». Lorsque "
"le panier contient un abonnement, celui-ci devient « Inscription »."

#: includes/admin/class-wc-subscriptions-admin.php:1223
msgid "Place Order Button Text"
msgstr "Libellé du bouton Passer la commande"

#: includes/admin/class-wc-subscriptions-admin.php:1216
#: includes/admin/class-wc-subscriptions-admin.php:1219
#: includes/admin/class-wc-subscriptions-admin.php:1228
#: includes/admin/class-wc-subscriptions-admin.php:1231
#: includes/class-wc-product-subscription-variation.php:98
#: includes/class-wc-product-variable-subscription.php:73
#: includes/class-wc-subscriptions-product.php:1196
#: includes/class-wc-subscriptions-product.php:1214
#: woocommerce-subscriptions.php:606
msgid "Sign up now"
msgstr "Inscription"

#: includes/admin/class-wc-subscriptions-admin.php:1212
msgid ""
"A product displays a button with the text \"Add to cart\". By default, a "
"subscription changes this to \"Sign up now\". You can customise the button "
"text for subscriptions here."
msgstr ""
"Un produit affiche un bouton avec le libellé « Ajouter au panier ». Par "
"défaut, un abonnement remplace ce libellé par « Inscription ». Vous pouvez "
"personnaliser le libellé du bouton pour les abonnements ici."

#: includes/admin/class-wc-subscriptions-admin.php:1211
msgid "Add to Cart Button Text"
msgstr "Libellé du bouton Ajouter au panier"

#: includes/admin/class-wc-subscriptions-admin.php:1204
msgid "Button Text"
msgstr "Libellé du bouton"

#: includes/admin/class-wc-subscriptions-admin.php:1012
#: includes/admin/class-wc-subscriptions-admin.php:1164
#: includes/admin/class-wcs-admin-reports.php:46
#: includes/admin/class-wcs-admin-system-status.php:56
#: includes/admin/class-wcs-wc-admin-manager.php:38
#: includes/admin/class-wcs-wc-admin-manager.php:80
#: includes/admin/class-wcs-wc-admin-manager.php:92
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:917
#: includes/class-wcs-query.php:108 includes/class-wcs-query.php:133
#: includes/class-wcs-query.php:287
#: includes/privacy/class-wcs-privacy-exporters.php:51
#: woocommerce-subscriptions.php:266 woocommerce-subscriptions.php:279
msgid "Subscriptions"
msgstr "Abonnements"

#: includes/admin/class-wc-subscriptions-admin.php:990
#: woocommerce-subscriptions.php:275
msgid "Search Subscriptions"
msgstr "Rechercher des abonnements"

#: includes/admin/class-wc-subscriptions-admin.php:986
msgid "Manage Subscriptions"
msgstr "Gérer les abonnements"

#: includes/admin/class-wc-subscriptions-admin.php:943
msgid "Active subscriber?"
msgstr "Abonné actif ?"

#. translators: placeholders are for HTML tags. They are 1$: "<h3>", 2$:
#. "</h3>", 3$: "<p>", 4$: "</p>"
#: includes/admin/class-wc-subscriptions-admin.php:888
msgctxt ""
"used in admin pointer script params in javascript as price pointer content"
msgid ""
"%1$sSet a Price%2$s%3$sSubscription prices are a little different to other "
"product prices. For a subscription, you can set a billing period, length, "
"sign-up fee and free trial.%4$s"
msgstr ""
"%1$sDéfinir un prix%2$s%3$sLes prix d’abonnement sont un peu différents des "
"prix des autres produits. Pour un abonnement, vous pouvez définir une "
"période de facturation, une durée, des frais d’inscription et un essai "
"gratuit.%4$s"

#. translators: placeholders are for HTML tags. They are 1$: "<h3>", 2$:
#. "</h3>", 3$: "<p>", 4$: "<em>", 5$: "</em>", 6$: "<em>", 7$: "</em>", 8$:
#. "</p>"
#: includes/admin/class-wc-subscriptions-admin.php:886
msgctxt ""
"used in admin pointer script params in javascript as type pointer content"
msgid ""
"%1$sChoose Subscription%2$s%3$sThe WooCommerce Subscriptions extension adds "
"two new subscription product types - %4$sSimple subscription%5$s and "
"%6$sVariable subscription%7$s.%8$s"
msgstr ""
"%1$sChoisir l’abonnement%2$s%3$sL’extension WooCommerce Subscriptions ajoute "
"deux nouveaux types de produit d’abonnement : %4$sAbonnement simple%5$s et "
"%6$sAbonnement variable%7$s.%8$s"

#: includes/admin/class-wc-subscriptions-admin.php:857
msgid ""
"You are deleting a subscription item. You will also need to manually cancel "
"and trash the subscription on the Manage Subscriptions screen."
msgstr ""
"Vous supprimez un article d’abonnement. Vous devrez également annuler et "
"supprimer manuellement l’abonnement sur l’écran Gérer les abonnements."

#: includes/admin/class-wc-subscriptions-admin.php:856
msgid ""
"WARNING: Bad things are about to happen!\n"
"\n"
"The payment gateway used to purchase this subscription does not support "
"modifying a subscription's details.\n"
"\n"
"Changes to the billing period, recurring discount, recurring tax or "
"recurring total may not be reflected in the amount charged by the payment "
"gateway."
msgstr ""
"ATTENTION : De mauvaises choses sont sur le point d’arriver !\n"
"\n"
"La passerelle de paiement utilisée pour acheter cet abonnement ne prend pas "
"en charge la modification des détails d’un abonnement.\n"
"\n"
"Les modifications de la période de facturation, de la remise récurrente, des "
"taxes récurrentes ou du total récurrent peuvent ne pas être reflétées dans "
"le montant facturé par la passerelle de paiement."

#: includes/admin/class-wc-subscriptions-admin.php:820
msgid ""
"Trashing this order will also trash the subscriptions purchased with the "
"order."
msgstr ""
"Le déplacement de cette commande vers la corbeille entraînera également le "
"déplacement vers la corbeille des abonnements achetés avec la commande."

#: includes/admin/class-wc-subscriptions-admin.php:843
msgid ""
"You are about to trash one or more orders which contain a subscription.\n"
"\n"
"Trashing the orders will also trash the subscriptions purchased with these "
"orders."
msgstr ""
"Vous êtes sur le point de déplacer vers la corbeille une ou plusieurs "
"commandes contenant un abonnement.\n"
"\n"
"Le déplacement des commandes vers la corbeille entraînera également le "
"déplacement vers la corbeille des abonnements achetés avec ces commandes."

#: includes/admin/class-wc-subscriptions-admin.php:835
msgid ""
"Enter a new interval as a single number (e.g. to charge every 2nd month, "
"enter 2):"
msgstr ""
"Saisir un nouvel intervalle sous forme de nombre unique (par ex. pour "
"facturer tous les 2 mois, saisissez 2) :"

#: includes/admin/class-wc-subscriptions-admin.php:834
msgid "Enter a new length (e.g. 5):"
msgstr "Saisir une nouvelle durée (par ex. 5) :"

#: includes/admin/class-wc-subscriptions-admin.php:833
msgid "Enter the new period, either day, week, month or year:"
msgstr "Saisir la nouvelle période, soit jour, semaine, mois ou année :"

#: includes/admin/class-wc-subscriptions-admin.php:453
msgid "Free trial period"
msgstr "Période d’essai gratuit"

#: includes/admin/class-wc-subscriptions-admin.php:452
msgid "Free trial length"
msgstr "Durée de l’essai gratuit"

#: includes/admin/class-wc-subscriptions-admin.php:312
#: includes/admin/class-wc-subscriptions-admin.php:450
msgid "Subscription period"
msgstr "Période d’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:449
msgid "Subscription billing interval"
msgstr "Intervalle de facturation de l’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:448
msgid "Subscription sign-up fee"
msgstr "Frais d’inscription de l’abonnement"

#: includes/upgrades/templates/wcs-about.php:115
msgid "Subscription Gifting"
msgstr "Offre d’abonnement"

#: includes/class-wcs-limiter.php:52
msgid "Limit to one of any status"
msgstr "Limiter à un avec n’importe quel état"

#: includes/class-wcs-limiter.php:51
msgid "Limit to one active subscription"
msgstr "Limiter à un abonnement actif"

#: includes/class-wcs-limiter.php:50
msgid "Do not limit"
msgstr "Ne pas limiter"

#. translators: placeholders are opening and closing link tags
#: includes/class-wcs-limiter.php:48
msgid ""
"Only allow a customer to have one subscription to this product. %1$sLearn "
"more%2$s."
msgstr ""
"Autorisez un client à n’avoir qu’un seul abonnement à ce produit. %1$sEn "
"savoir plus%2$s."

#: includes/class-wcs-limiter.php:46
msgid "Limit subscription"
msgstr "Limiter l’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:390
msgid ""
"Shipping for subscription products is normally charged on the initial order "
"and all renewal orders. Enable this to only charge shipping once on the "
"initial order. Note: for this setting to be enabled the subscription must "
"not have a free trial or a synced renewal date."
msgstr ""
"La livraison des produits d’abonnement est normalement facturée sur la "
"commande initiale et toutes les commandes de renouvellement. Activez cette "
"option pour ne facturer les frais d’expédition qu’une seule fois sur la "
"commande initiale. Remarque : pour que ce paramètre soit activé, "
"l’abonnement ne doit pas avoir d’essai gratuit ni de date de renouvellement "
"synchronisée."

#: includes/admin/class-wc-subscriptions-admin.php:389
msgid "One time shipping"
msgstr "Livraison unique"

#: includes/admin/class-wc-subscriptions-admin.php:357
#: templates/admin/deprecated/html-variation-price.php:115
msgid "Subscription Trial Period"
msgstr "Période d’essai d’abonnement"

#: templates/admin/deprecated/html-variation-price.php:97
#: templates/admin/deprecated/html-variation-price.php:104
msgid "Free Trial"
msgstr "Essai gratuit"

#: includes/admin/class-wc-subscriptions-admin.php:342
msgid ""
"Optionally include an amount to be charged at the outset of the subscription."
" The sign-up fee will be charged immediately, even if the product has a free "
"trial or the payment dates are synced."
msgstr ""
"Incluez éventuellement un montant à facturer au début de l’abonnement. Les "
"frais d’inscription seront facturés immédiatement, même si le produit a un "
"essai gratuit ou si les dates de paiement sont synchronisées."

#. translators: %s is a currency symbol / code
#. translators: placeholder is a currency symbol / code
#: includes/admin/class-wc-subscriptions-admin.php:341
#: templates/admin/deprecated/html-variation-price.php:31
#: templates/admin/deprecated/html-variation-price.php:86
#: templates/admin/html-variation-price.php:21
#: templates/admin/html-variation-price.php:47
msgctxt "example price"
msgid "e.g. 9.90"
msgstr "par ex. 9,90"

#: templates/admin/deprecated/html-variation-price.php:85
msgid "Sign-up Fee (%s)"
msgstr "Frais d’inscription (%s)"

#: templates/admin/deprecated/html-variation-price.php:69
msgid "Subscription Length"
msgstr "Durée de l’abonnement"

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:34
#: templates/admin/deprecated/html-variation-price.php:57
msgid "Billing Period"
msgstr "Période de facturation"

#: templates/admin/deprecated/html-variation-price.php:46
msgid "Subscription Periods"
msgstr "Périodes d’abonnement"

#: includes/admin/class-wc-subscriptions-admin.php:305
msgctxt "example price"
msgid "e.g. 5.90"
msgstr "par ex. 5,90"

#. translators: placeholder is a currency symbol / code
#: templates/admin/deprecated/html-variation-price.php:20
#: templates/admin/deprecated/html-variation-price.php:30
msgid "Subscription Price (%s)"
msgstr "Prix de l’abonnement (%s)"

#: includes/admin/class-wc-subscriptions-admin.php:199
msgid "Variable subscription"
msgstr "Abonnement variable"

#: includes/admin/class-wc-subscriptions-admin.php:198
msgid "Simple subscription"
msgstr "Abonnement simple"

#. Plugin URI of the plugin
msgid "https://www.woocommerce.com/products/woocommerce-subscriptions/"
msgstr "https://www.woocommerce.com/products/woocommerce-subscriptions/"

#. translators: billing period (e.g. "every week").
#: includes/class-wc-subscriptions-product.php:377
msgid "every %s"
msgstr "chaque %s"

#: includes/admin/class-wc-subscriptions-admin.php:1320
msgctxt "there's a number immediately in front of this text"
msgid "suspensions per billing period."
msgstr "suspensions par période de facturation."
