// title heading
.widget-text-heading {
    margin: 0;
    .title {
        font-size: 36px;
        font-weight: 400;
        margin: 0 0 5px;
    }
    .des {
        font-size: 13px;
    }
    &.center {
        text-align: center;
    }
}

.edumy-heading-title {
    color: $headings-color;  
    font-size: 26px;
    font-weight: 600;
    margin: 0 0 8px 0;
}

.edumy-heading-subtitle {
    font-size: 16px;
    line-height: 26px;
    margin: 0;
}

.elementor-widget-edumy_heading_big{
    .edumy-heading-subtitle{
        font-size: 18px;
        font-family: $headings-font-family;
    }
}

.elementor-lightbox{
    .elementor-lightbox-image{
        @include border-radius(5px);
    }
}

.elementor-288{
    .elementor-element{
        &.elementor-element-7c9574a {
            margin-bottom: 10px;
        }                        
        &.elementor-element-db209ec{
            overflow: hidden;               
        }
        &.elementor-element-3771e4e {
            top: 0;
            position: absolute;            
            @include rtl-right(0);
        }
    }  
} 

.elementor-2709{
    .elementor-element{
        &.elementor-element-79267f5,
        &.elementor-element-9bb2b01{
            .elementor-button{
                .elementor-button-icon{                                            
                    i{
                        &:before{
                            font-size: 15px;                            
                            position: absolute;
                            margin: 0;                            
                            @include rtl-right(-24px);
                        }
                    }
                }
            }                 
        }             
    }        
}

.elementor-widget-edumy_primary_menu {
    &.menu-border{
        &:before{
            content:'';            
            background-color: rgba($white, .2);            
            position: absolute;
            @include rtl-left(0);            
            @include vertical-align(absolute);
            @include size(1px, 50px);
        }
    }
}
.elementor-element{
    &.elementor-element-57d1440{
        &.elementor-widget-image{
            .elementor-image{
                position: relative;                
                &:before{
                    content: "";
                    background-color: $white;
                    position: absolute;
                    bottom: -20px;
                    @include rtl-right(-20px);                    
                    @include square(100%);
                    @include border-radius(5px);
                }
                img{
                    position: relative;
                }
            }
        }
    } 
    &.elementor-element-8373e7e{
        &.elementor-widget-text-editor{
            a{
                color: $gray-smoke;                
                font-size: 14px;
                font-weight: 400;
                font-family: $headings-font-family;
                @include hover-focus-active() {
                    color: $white;
                }
            }
        }
    }
    &.elementor-widget-icon{
        &.icon-theme-box{
            [class*="icon-"]{
                &:before{
                    font-size: 76px;
                    margin: 0;
                }                
            }
        }
    }      


    &.elementor-element-ddf1b2f{        
        .login-account{         
            height: 39px;   
        }
        .setting-account{
            font-size: $font-size-base;
            font-family: $font-family-base; 
        }         
    }


    &.highlighter-bkg-1{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-1.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            top: -13px;
            z-index: -1;
            @include rtl-right(0);               
            @include size(535px,930px);
        }
    }
    &.highlighter-bkg-2{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-2.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            bottom: -38%;
            z-index: -1;
            @include rtl-left(0);
            @include size(727px,987px);
        }
    }
    &.highlighter-bkg-3{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-3.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            top: -52%;
            z-index: -1;
            @include rtl-right(0);
            @include size(897px,867px);            
        }
    }
    &.highlighter-bkg-4{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-4.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            top: -34%;            
            @include rtl-right(20px);
            @include size(409px,677px);  
        }
    }
    &.highlighter-bkg-5{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-5.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            top: -80%;            
            z-index: -1;
            @include rtl-right(0);
            @include size(535px,923px);  
        }
    }
    &.highlighter-bkg-6{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-6.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            top: -65px;            
            z-index: -1;
            @include rtl-left(0);
            @include size(727px,987px);  
        }
    }
    &.highlighter-bkg-7{
        &:after{
            content: '';
            background-image: url('../images/highlighter-bkg-7.png');
            background-repeat: no-repeat;
            background-position: 0 0; 
            background-size: cover;
            position: absolute;
            top: 85px;
            z-index: -1;
            @include rtl-right(0);
            @include size(897px,867px);  
        }
    }
    &.image-box{
        position: relative;                
        &:before{
            content: "";
            background-color: $table-bg-accent;
            position: absolute;
            bottom: -20px;
            @include rtl-right(-20px);                    
            @include square(100%);
            @include border-radius(5px);
        }
        img{
            position: relative;
        }
    }
    &.elementor-widget-image-carousel{
        .elementor-slick-slider{
            .slick-slide-inner{
                position: relative;
                overflow: hidden;
                @include rtl-padding-left(0 !important);
                @include rtl-margin-left(30px);
                a{
                    &:before{
                        content: "";
                        top: 0;                 
                        z-index: 1;       
                        position: absolute;
                        visibility: hidden;
                        background-color: $theme-color;
                        @include square(100%);
                        @include rtl-left(0);
                        @include opacity(0);
                        @include transition-all();    
                        @include border-radius(5px);                
                    }
                }                
                @include hover-focus-active() {
                    a{
                        &:before{
                            visibility: visible;
                            @include opacity(.8);
                        }
                    }                    
                    img{
                        @include scale(1.1);
                    }
                }
                img{
                    @include transition-all();
                }
            }
            .slick-arrows-inside{                
                .slick-prev{
                    @include rtl-left(30px);
                }
                .slick-next{
                    @include rtl-right(30px);
                }
            }             
        }         
        .slick-arrow{
            @include opacity(1);
            @include square(50px);
            @include border-radius(5px);                        
            @include transition-all();
            background-color: $white;  
            @include hover-focus-active() {                
                background-color: $theme-color-second;
                &:before{
                    color: $white;
                }
            }
            &:before{
                color: $theme-color;                
                content: "\f108";
                font-family: $icon-font-family;                            
                font-size: 15px;
                @include opacity(1);   
            }
            &.slick-next{
                &:before{
                    content: "\f107";
                }
            }            
        }
        &.style2{
            .slick-arrow{
                background-color: #192675;
                @include border-radius(100%);                
                @include transition-all();
                @include hover-focus-active() {
                    background-color: $theme-color-second;                      
                }
                &:before{
                    color: $white;                           
                }                
            }
        }
    }
    &.elementor-widget-google_maps{
        @include border-radius(5px);
        iframe{
            @include border-radius(5px);
        }
    }
    &.elementor-widget-image-gallery{
        .gallery-item{
            > div{
                overflow: hidden;
                position: relative;
                @include border-radius(5px);                
            }            
            img{
                @include border-radius(5px);                
            }
        }
    }
    &.elementor-widget-accordion{
        .elementor-accordion{     
            .elementor-accordion-item{
                margin-bottom: 10px;
                &:last-child{
                    margin-bottom: 0px;
                }
            }
            .elementor-tab-title{
                font-size: 18px;
                font-family: $headings-font-family;
                font-weight: 400;                
                background-color: #f9fafc;
                padding-left: 24px;
                padding-right: 24px;
                @include border-radius(5px);
                a{
                    color: $gray-text;
                    @include hover-focus-active() {
                        color: $gray-text;
                    }
                }                
                .elementor-accordion-icon{
                    i{
                        &:before{
                            margin: 0px;
                        }
                    }
                }
            }
            .elementor-accordion-item,
            .elementor-tab-content{
                border: 0;
            }          
            .elementor-tab-content{
                h3{
                    font-size: 18px;
                    font-weight: 400;
                    margin: 15px 0 10px 0;
                }
                p{
                    margin-bottom: 20px;
                    &:last-child{
                        margin-bottom: 10px;
                    }
                }
            }  
        }
    }
}

.elementor-widget-text-editor{
    .wpcf7-form{
        width: 547px;
        label{
            width: 100%;
        }
        input[type='text'],
        input[type='email'],
        input[type='password'] {
            border: 0;
            border-bottom: 1px solid $white;
            background-color: transparent;
            @include border-radius(0);
            color: $white;
            font-weight: 400;
            font-size: $font-size-base;
            font-family: $font-family-base;
            @include placeholder($white);
            padding-left: 0;
            padding-right: 0;
            width: 100%;
        }
        input[type="submit"]{
            outline: none;
            background-color: transparent;
            border: 2px solid $white;
            color: $white;
            font-size: 15px;
            font-family: $font-family-base;
            padding: 11px 56px;
            margin-top: 26px;
            text-transform: capitalize;
            @include transition-all();
            @include border-radius(50px);
            @include hover-focus-active() {
                color: $white;
                background-color: $theme-color;                
                border-color: $theme-color;
            }
        }
        p{
            position: relative;            
        }
        span.wpcf7-not-valid-tip{
            font-size: 12px;
            margin-top: 10px;
        }
        div.wpcf7-validation-errors{
            margin-top: 15px;
            padding: 10px 30px;
            color: #f7e700;
            @include border-radius(50px);
        }
    }
}
.apus-language{
    color: $gray-smoke;
    &.gray{
        color: $gray-smoke;
        .btn{
            color: $gray-smoke;
            @include hover-focus-active() {
                color: $white;
            }
        }
    }
    &.white{
        color: $white;                 
        .btn{
            color: $white;  
            @include hover-focus-active() {
                color: $white;
            }
        }
    }
    &.open{
        .dropdown-menu{        
            visibility: visible;
            pointer-events: auto;
            @include opacity(1);            
            @include translateY(0);
            @include backface-visibility(visible);            
        }
    }
    .btn-link{
        outline: none;
        color: $headings-color;
        padding: 0;
        font-weight: 400;        
        font-size: 14px;
        text-transform: none;        
        text-decoration: none;
        background-color: transparent;
        font-family: $headings-font-family;
        [class*="icon-"]{
            &:before{
                font-size: 10px;                
                @include rtl-margin(0,0,0,5px);
            }            
        }
        @include hover-focus-active() {
            color: $theme-color;
            text-decoration: none;
        }
    }
    .dropdown-menu{              
        display: block;          
        border: 0;
        padding: 0;
        min-width: 170px;
        pointer-events: none;
        margin: 15px 0 0 0;
        visibility: hidden;
        @include opacity(0);
        @include transition-all();
        @include translateY(30px);        
        @include rtl-left(auto);
        @include rtl-right(0);
        @include backface-visibility(hidden);
        > li{
            background-color: transparent;
            > a{
                color: $headings-color;
                padding-top: 10px;
                padding-bottom: 10px;
                background-color: transparent;
                @include hover-focus-active() {
                    background-color: transparent;
                }
            }
        }         
        &:before{                       
            top: -10px;
            content: "";
            position: absolute;                 
            border-left: 11px solid transparent;
            border-right: 11px solid transparent;  
            border-bottom: 11px solid $white;
            @include square(0);                             
            @include rtl-right(20px);
            @include transition-all();
        }
    }
}

.elementor-element{
    &.elementor-element-2afecbe{
        .apus-language{
            .dropdown-menu{
                margin-top: 10px;
            }
        }         
    }
}

.cta-mobile-app{
    margin-bottom: 0;
}

.vertical-wrapper {
    position: relative;  
    &:after{
        content: '';
        position: absolute;
        bottom: -25px;      
        pointer-events: none;
        visibility: hidden;
        background-color: transparent;
        @include size(100%,25px);        
        @include rtl-left(0);
        @include opacity(0);
    }
    &.style2{
        &:after{
            bottom: -30px;        
            height: 30px;
        }
        .content-vertical{
            margin-top: 22px;
        }
        .title-vertical {
            background-color: $border-form-color;
            color: $headings-color;
            text-transform: none;
            font-size: 16px;
            font-family: $headings-font-family;
            @include rtl-padding-left(42px);
            @include rtl-padding-right(42px);
        }
    }
    &.style3{
        .content-vertical{
            margin-top: 30px;
        }
        .title-vertical{
            padding: 11px 30px 12px;
            background-color: $headings-color;
        }
        &:after{
            height: 32px;
            bottom: -32px;
        }
    }
    .title-vertical {
        color: $white;
        background-color: $theme-color;
        font-weight: 400;        
        font-size: 16px;
        text-transform: uppercase;
        padding: 12px 30px;
        margin: 0;
        cursor: pointer;
        line-height: normal;
        @include flexbox();
        @include align-items(center);
        @include justify-content(center);
        @include border-radius(50px);
        @include transition-all();
        i {
            font-size: 16px;
            @include rtl-margin-right(15px);
        }
        .show-down {
            font-size: 16px;            
            @include rtl-margin(1px, 0, 0, 10px);
            @include rtl-float-right();
        }
    }
    .content-vertical {
        padding: 0;     
        margin: 20px 0 0 0;           
        top: 120%;                
        z-index: 8;
        position: absolute;
        min-width: 322px;
        width: 100% !important;
        background-color: #191919;
        visibility: hidden;
        pointer-events: none;
        @include rtl-left(0);                
        @include border-radius(5px);
        @include transition-all();
        @include opacity(0);
        @include backface-visibility(hidden);
        .apus-vertical-menu {
            border: 0;
            margin: 0;
            padding: 0;
            width: 100%;
            background-color: transparent;
            @include border-radius(0);    
            &:before{                       
                top: -10px;
                content: "";
                position: absolute;     
                @include square(0);                             
                @include rtl-left(24px);
                @include transition-all();
                border-left: 11px solid transparent;
                border-right: 11px solid transparent;  
                border-bottom: 11px solid #191919;
            }                        
            > li{           
                float: none;     
                position: static;
                border-bottom: 1px solid #2e2e2e;
                background-color: transparent;                
                @include clearfix();
                > a{
                    width: 100%;                    
                    position: relative;
                    font-size: 16px;                    
                    font-weight: 400;                    
                    color: $gray-smoke;
                    padding: 13px 30px;                       
                    background-color: transparent;                                      
                    font-family: $headings-font-family;
                    @include rtl-float-left();                    
                    @include transition(all .1s ease-in);                     
                    @include hover-focus-active() {                     
                        color: $white;                                                                        
                        background-color: $headings-color; 
                        @include rtl-padding-left(36px);
                    }                   
                    .caret {             
                        top: 15px;           
                        margin: 0;
                        border: 0;        
                        position: absolute;
                        overflow: hidden;    
                        font-size: 8px;         
                        @include square(20px);                        
                        @include flexbox();
                        @include justify-content(center);
                        @include align-items(center);                       
                        @include rtl-right(23px);                        
                        &:before {                            
                            content: "\f104";                            
                            font-family: $icon-font-family;         
                        }                                              
                    }
                }
                &.active{
                    > a{
                        color: $white;
                    }
                }
                &:last-child{
                    border-bottom: 0;
                    a{
                        @include border-radius-separate(0, 0, 5px, 5px);
                    }
                } 
                &:first-child{
                    border-top: 0;
                    a{
                        @include border-radius-separate(5px, 5px, 0px, 0px);
                    }
                }  
                .dropdown-menu{
                    display: block;
                    pointer-events: none;
                    top: 0;      
                    padding: 20px 30px;
                    border: 0;              
                    min-width: 300px;
                    height: 100%;
                    background-color: $headings-color;  
                    visibility: hidden;
                    @include backface-visibility(hidden);
                    @include transition-all();
                    @include opacity(0);                                      
                    @include rtl-margin-left(-4px);
                    @include rtl-right(auto);
                    @include rtl-left(100%);                                        
                    @include box-shadow(none);
                    @include border-radius-separate(0px, 5px, 0px, 5px);
                    &.vertical-megamenu{
                        padding: 30px;
                    }
                    li{
                        border-bottom: 0;
                        padding: 0;                                                
                        > a{
                            color: $gray-smoke;
                            font-size: 16px;
                            font-family: $headings-font-family;
                            background-color: transparent;
                            padding: 7px 0;
                            @include hover-focus-active() {
                                color: $white;
                            }
                        }
                        &:first-child{
                            > a{
                                padding-top: 0;
                            }
                        }
                        &:last-child{
                            > a{
                                padding-bottom: 0;
                            }
                        }                        
                    } 
                    [class*="icon-"]{
                        &:before{
                            font-size: 14px;
                        }
                    }                     
                    .widget-nav-menu{
                        .menu{
                            li{
                                a{
                                    padding: 0;
                                }
                            }                                
                        }                             
                    }                                           
                }
                &:hover{
                    > .dropdown-menu{
                        visibility: visible;
                        pointer-events: auto;
                        @include opacity(1);
                        @include backface-visibility(visible);
                    }
                }
            }       
        }
    }
    &.show-always {
        .content-vertical {
            display: block;
        }
    }
    &:hover{
        &:after{
            @include opacity(1);
            pointer-events: auto;
            visibility: visible;         
        }
        .content-vertical{
            top: 100%;  
            pointer-events: auto;
            visibility: visible;
            @include opacity(1);
            @include backface-visibility(visible);            
        }        
    }
}

.top-wrapper-menu{
    .login{
        color: $white;
        font-size: 16px;        
        position: relative;
        @include flexbox();
        @include align-items(center);
        font-family: $headings-font-family;        
        &:after{
            content: '';
            position: absolute;
            top: -7px;
            background-color: rgba(255, 255, 255, 0.2);            
            @include rtl-right(-22px);
            @include size(1px, 50px);
        }
        [class*="icon"]{
            &:before{
                font-size: 18px;
            }
        }
    }
}

body.home {
    .vertical-wrapper{
        &.show-in-home {
            .content-vertical {
                display: block;
            }
        }
    }    
}

.apus_custom_menu {
    .widget_nav_menu{
        margin-bottom: 0;
    }
    &.white{
        .menu{
            li{
                a{
                    font-size: 14px;
                    color: $headings-color;
                    font-family: $headings-font-family;
                    @include flexbox();
                    @include align-items(center);
                    &:before{                        
                        @include rtl-margin-right(13px);                        
                        font-size: 20px;
                        color: $headings-color;
                        content: "\f11d";
                        font-family: $icon-font-family;
                        @include transition-all();
                    }                    
                    @include hover-focus-active() {
                        color: $theme-color;
                        &:before{  
                            color: $theme-color;
                        }
                    }
                }
            }
        }
    }
    &.gray{
        .menu{
            li{
                a{
                    font-size: 14px;
                    color: $gray-smoke;
                    font-family: $headings-font-family;
                    @include flexbox();
                    @include align-items(center);
                    &:before{                        
                        @include rtl-margin-right(13px);                        
                        font-size: 20px;
                        color: $gray-smoke;
                        content: "\f11d";
                        font-family: $icon-font-family;
                        @include transition-all();
                    }                    
                    @include hover-focus-active() {
                        color: $white;
                        &:before{  
                            color: $white;
                        }
                    }
                }
            }
        }
    }
    &.center {
        text-align: center;
        li {
            display: inline-block;
            margin: 0 15px;
        }
    }
    &.left {
        text-align: left;
    }
    &.right {
        text-align: right;
    }
    &.inline {
        li {
            display: inline-block;
            vertical-align: middle;
            margin-bottom: 0;
            @include rtl-margin-right(40px);            
            &:last-child {
                margin: 0;
            }
        }
    }
}

.apus-custom-toplink{
    list-style: none;
    padding: 0;
    margin: 0;    
    &.white{
        li{
            a{
                color: $white;
                @include hover-focus-active() {
                    color: $white;
                }
            }
        }
    }
    li{ 
        margin-bottom: 0;
        @include inline-block();
        @include rtl-margin-right(20px);        
        &:last-child{
            @include rtl-margin-right(0);
        }
        a{
            color: $headings-color;
            font-family: $headings-font-family;
            font-weight: 400;
            @include hover-focus-active() {
                color: $theme-color;
            }
        }
    }
    &.gray{
        color: #a5a6a6;
        li{
            a{
                color: $headings-color;
                @include hover-focus-active() {
                    color: $theme-color;
                }
            }
        }
    }
}

// slick
.slick-carousel {
    position: relative;
    margin-right: -($theme-margin / 2);
    margin-left: -($theme-margin / 2);
    .slick-arrow {                           
        @include square(48px);        
        @include transition(all 0.3s ease-in-out 0s);
        @include box-shadow(none);
        @include border-radius(50px);
        @include opacity(1);
        @include vertical-align(absolute);
        @include inline-block();
        background: $table-bg-accent;
        text-align: center;
        line-height: normal;
        font-size: 12px;
        color: $text-color;                
        z-index: 3;
        padding: 0;        
        border: 0;  
        [class*="icon"]{
            &:before{
                margin: 0;                
            }
        }
        .textnav {
            display: none;
        }
        @include hover-focus-active() {
            background-color: $theme-color;
            color: $white;
        }
    }
    .slick-prev {
        @include rtl-left(0);
        @media only screen and (min-width : 1224px) {            
            @include rtl-margin-left(15px);
        }
    }
    .slick-next {
        @include rtl-right(0);              
        @media only screen and (min-width : 1224px) {
            @include rtl-margin-right(15px);            
        }        
    }
    &:hover {
        .slick-arrow {
            @include opacity(1);
            margin: 0 !important;
        }
    }
    .slick-slide {
        outline: none !important;
        padding-left: $theme-margin / 2;
        padding-right: $theme-margin / 2;
    }

    &.show-text {
        .textnav {
            display: inline-block;
            margin: 0 2px;
        }

        .slick-arrow {
            @include size(auto, auto);
            background: transparent !important;
            font-weight: 500;
            font-size: 12px;
            color: $link-color;

            &:hover,
            &:active,
            &:focus {
                color: $theme-color;
            }
        }

        .slick-prev {
            left: $theme-margin / 2;
            right: inherit;
        }

        .slick-next {
            right: $theme-margin / 2;
            left: inherit;
        }
    }

    &.p-bottom {
        .slick-list {
            padding-bottom: 5px;
        }

        .slick-arrow {
            top: 100%;
            @include translateY(-100%);
        }
    }

    .slick-track {
        margin: inherit;
    }

    // dots
    .slick-dots {
        margin: 0 !important;
        padding: 20px 0 0;
        text-align: center;
        list-style: none;
        line-height: 1;
        li {            
            margin: 0 5px;
            position: relative;
            @include border-radius(100%);
            @include square(13px);
            @include inline-block();
            button {
                border: none;
                display: block;
                text-indent: -9999em;                
                padding: 0;
                background: #c7c7c7;
                @include center-box(absolute);
                @include size(8px, 8px);
                @include border-radius(100%);
                @include transition(all 0.2s ease-in-out 0s);
            }
            &.slick-active{
                button {
                    background: $theme-color;
                    @include square(100%);
                }
            }             
        }
    }
}

.slick-carousel-top {
    .slick-arrow {
        top: -75px;
        margin: 0 !important;
        @include border-radius(0px);        
        @include translate(0, 0);
        @include opacity(1);        
    }

    .slick-next {
        right: 15px;
    }

    .slick-prev {
        left: inherit;
        right: 45px;
    }
}

// carousel
/* 
 *  Owl Carousel - Animate Plugin
 */
.owl-carousel .animated {
    -webkit-animation-duration: 1000ms;
    animation-duration: 1000ms;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.owl-carousel .owl-animated-in {
    z-index: 0;
}

.owl-carousel .owl-animated-out {
    z-index: 1;
}

.owl-carousel .fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut;
}

/* 
 *  Owl Carousel - Auto Height Plugin
 */
.owl-height {
    -webkit-transition: height 500ms ease-in-out;
    -moz-transition: height 500ms ease-in-out;
    -ms-transition: height 500ms ease-in-out;
    -o-transition: height 500ms ease-in-out;
    transition: height 500ms ease-in-out;
}

.owl-carousel-top .owl-carousel,
.owl-carousel-top.owl-carousel {

    .owl-controls .owl-nav .owl-next,
    .owl-controls .owl-nav .owl-prev {
        @include opacity(1);
        background: #aaaaaa;
        border: none;
        color: $white;

        &:hover,
        &:active {
            background: #222222;
            color: $white;
        }
    }

    .owl-controls {
        top: -70px;
        width: 108px;
        @include rtl-right(0);
        @include translateY(0px);
    }
}

/* 
 *  Core Owl Carousel CSS File
 */
.owl-carousel {
    display: none;
    -webkit-tap-highlight-color: transparent;
    /* position relative and z-index fix webkit rendering fonts issue */
    position: relative;
    z-index: 1;
    margin-right: -($theme-margin / 2);
    margin-left: -($theme-margin / 2);
    cursor: pointer;

    &:hover {

        .owl-controls .owl-nav .owl-prev,
        .owl-controls .owl-nav .owl-next {
            @include opacity(1);
        }
    }
}

.owl-controls {
    .owl-dots {
        text-align: center;

        .owl-dot {
            cursor: pointer;
            margin: 0 5px;
            @include size(11px, 11px);
            @include border-radius(50%);
            display: inline-block;
            border: 2px solid #646a7c;
            background: #646a7c;

            &.active {
                border-color: $theme-color;
                background: $theme-color;
            }
        }
    }
}

.nav-bottom.owl-carousel {
    padding-bottom: 55px;

    .owl-controls {
        top: 100%;
    }
}

.nav-white {
    .owl-controls .owl-dots .owl-dot {
        border-color: $theme-color;
        background: transparent;

        &:hover,
        &.active {
            background: $white;
            border-color: $theme-color;
        }
    }
}

.nav-small {

    .owl-controls .owl-nav .owl-prev,
    .owl-controls .owl-nav .owl-next {
        @include size(30px, 30px);
        line-height: 28px;
        border-width: 1px;
        font-size: 18px;
    }
}

.nav-white {

    .owl-controls .owl-nav .owl-prev,
    .owl-controls .owl-nav .owl-next {
        border-color: rgba(255, 255, 255, 0.5);
        color: rgba(255, 255, 255, 0.5);

        &:hover,
        &:active {
            border-color: $theme-color;
            background-color: $theme-color;
            color: $white;
        }
    }
}

.owl-carousel .owl-stage {
    position: relative;
    -ms-touch-action: pan-Y;
}

.owl-carousel .owl-stage:after {
    content: ".";
    display: block;
    clear: both;
    visibility: hidden;
    line-height: 0;
    height: 0;
}

.owl-carousel .owl-stage-outer {
    position: relative;
    overflow: hidden;
    /* fix for flashing background */
    -webkit-transform: translate3d(0px, 0px, 0px);
}

.owl-carousel .owl-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    @include translateY(-50%);
}

.owl-controls .owl-nav .owl-prev,
.owl-controls .owl-nav .owl-next {
    cursor: pointer;
    display: inline-block;
    @include opacity(0);
    text-align: center;
    @include transition(all 0.3s ease);
    position: absolute;
    @include translateY(-50%);
    border: 2px solid $theme-color;
    top: 50%;
    left: 15px;
    z-index: 99;
    color: $theme-color;
    font-size: 30px;
    @include size(48px, 48px);
    @include border-radius(50%);
    line-height: 42px;
    background: transparent;

    &:active,
    &:hover {
        background: $theme-color;
        color: $white;
        border-color: $theme-color;
    }
}

.owl-carousel .owl-controls .owl-nav .owl-next {
    left: inherit;
    right: 15px;
}

.owl-carousel.owl-loaded {
    display: block;
}

.owl-carousel.owl-loading {
    opacity: 0;
    display: block;
}

.owl-carousel.owl-hidden {
    opacity: 0;
}

.owl-carousel .owl-refresh .owl-item {
    display: none;
}

.owl-carousel .owl-item {
    position: relative;
    min-height: 1px;
    float: left;
    -webkit-backface-visibility: hidden;
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    padding-right: $theme-padding / 2;
    padding-left: $theme-padding / 2;
}

.owl-carousel.owl-text-select-on .owl-item {
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
    user-select: auto;
}

.owl-carousel .owl-grab {
    cursor: move;
    cursor: -webkit-grab;
    cursor: -o-grab;
    cursor: -ms-grab;
    cursor: grab;
}

.owl-carousel.owl-rtl {
    direction: rtl;
}

.owl-carousel.owl-rtl .owl-item {
    float: right;
}

/* No Js */
.no-js .owl-carousel {
    display: block;
}

/* 
 *  Owl Carousel - Lazy Load Plugin
 */
.owl-carousel .owl-item .owl-lazy {
    opacity: 0;
    -webkit-transition: opacity 400ms ease;
    -moz-transition: opacity 400ms ease;
    -ms-transition: opacity 400ms ease;
    -o-transition: opacity 400ms ease;
    transition: opacity 400ms ease;
}

/* 
 *  Owl Carousel - Video Plugin
 */
.owl-carousel .owl-video-wrapper {
    position: relative;
    height: 100%;
    background: #000;
}

.owl-carousel .owl-video-play-icon {
    position: absolute;
    height: 80px;
    width: 80px;
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    background: url("owl.video.play.png") no-repeat;
    cursor: pointer;
    z-index: 1;
    -webkit-backface-visibility: hidden;
    -webkit-transition: scale 100ms ease;
    -moz-transition: scale 100ms ease;
    -ms-transition: scale 100ms ease;
    -o-transition: scale 100ms ease;
    transition: scale 100ms ease;
}

.owl-carousel .owl-video-play-icon:hover {
    -webkit-transition: scale(1.3, 1.3);
    -moz-transition: scale(1.3, 1.3);
    -ms-transition: scale(1.3, 1.3);
    -o-transition: scale(1.3, 1.3);
    transition: scale(1.3, 1.3);
}

.owl-carousel .owl-video-playing .owl-video-tn,
.owl-carousel .owl-video-playing .owl-video-play-icon {
    display: none;
}

.owl-carousel .owl-video-tn {
    opacity: 0;
    height: 100%;
    background-position: center center;
    background-repeat: no-repeat;
    -webkit-background-size: contain;
    -moz-background-size: contain;
    -o-background-size: contain;
    background-size: contain;
    -webkit-transition: opacity 400ms ease;
    -moz-transition: opacity 400ms ease;
    -ms-transition: opacity 400ms ease;
    -o-transition: opacity 400ms ease;
    transition: opacity 400ms ease;
}

.owl-carousel .owl-video-frame {
    position: relative;
    z-index: 1;
}

//wpb_gmaps_widget
.wpb_gmaps_widget.wpb_content_element {
    margin: 0;
}

.widget-googlemap {
    position: relative;

    .apus-google-map {
        z-index: 1;
    }

    .map-content {
        position: absolute;
        top: 0;
        left: 0;
        @include size(100%, 100%);
    }

    .content-info {
        position: relative;
        z-index: 9;
        background: rgba(#111111, 0.8);
        padding: 30px;

        @media(min-width: 1024px) {
            padding: 65px 65px 40px;
        }
    }

    .description {
        margin: 0 0 35px;
    }

    .info-wrapper {
        margin: 0 0 40px;

        &:nth-child(2n) {
            font-weight: normal;
        }

        .icon {
            @include size(70px, 70px);
            line-height: 76px;
            text-align: center;
            border: 2px dashed #3f4143;
            @include border-radius(50%);
            @include rtl-float-left();
            @include rtl-margin-right(20px);

            i {
                font-size: 28px;
            }
        }

        .des {
            color: $white;
            overflow: hidden;
            margin: 6px 0 0;
        }
    }

    .info-top {
        border-bottom: 2px dashed #414445;
        overflow: hidden;
    }

    .info-bottom {
        margin: 40px 0;
        a {
            display: inline-block;
            text-align: center;
            line-height: 42px;            
            color: #101416;
            background: #3f4143;
            @include size(42px, 42px);
            @include border-radius(50%);
            @include hover-focus-active() {
                color: #0e1113;
                @include gradient-horizontal($theme-color-second, $theme-color);
            }
            + a {
                @include rtl-margin-left(12px);
            }
        }
    }
}

// widget-social

.widget-social {
    .title {
        font-size: 20px;
        font-weight: 400;
        line-height: 30px;        
        text-transform: uppercase;
        @include rtl-padding-right(25px);
    }
    > * {
        @include inline-block();        
    }
    .social {
        padding: 0;
        list-style: none;
        margin: 0;
        > li {
            padding: 0;            
            @include inline-block();
            @include rtl-margin(15px, 30px, 15px, 0);
            &:first-child {
                @include rtl-margin-left(0);
            }
            &:last-child {
                @include rtl-margin-right(0px);
            }
        }
        a {
            font-size: 17px;            
            text-align:center;
            color: $footer-color;   
            background-color: transparent;                     
            @include inline-block();            
            @include transition-all();            
            @include border-radius(50%);
            @include scale(1);
            @include hover-focus() {
                background-color: transparent !important;
                color: $headings-color;
                @include scale(1.1);
            }
        }
    }
    &.left {
        @include rtl-text-align-left();
    }
    &.right {
        @include rtl-text-align-right();
    }
    &.center {
        text-align: center;
    }    
    &.dark {
        .social {
            a {               
                color: $footer-color;
                @include hover-focus-active() {
                    color: $white;
                }                
            }            
        }
    }
    &.social-linklist{
        li{
            margin-top: 0;
            margin-bottom: 0;
        }
    }
}

// Widget Category Banner
.widget-category-banner{
    color: $white;
    position: relative;
    text-align: center;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top left;
    background-color: transparent;    
    @include border-radius(5px);
    @include size(100%, 160px);
    @include flexbox();        
    @include justify-content(center);
    @include align-items(center);    
    &:before{
        content: '';                
        top: 0px;
        z-index: 0;
        position: absolute;
        background-color: $black;
        @include rtl-left(0);
        @include square(100%);
        @include border-radius(5px);
        @include transition-all();
        @include opacity(.5);        
    }
    a{       
        z-index: 1;
        color: $white;                
        @include flexbox();
        @include align-items(center);   
        @include justify-content(center);
        @include square(100%);
        @include hover-focus-active() {
            color: $white;
        }
    }   
    .inner{
        position: relative;
        z-index: 1;
    }
    .banner-title{
        margin: 0 0 3px 0;
        color: $white;
        font-size: 18px;
        line-height: 28px;
        font-weight: 400;
    }    
    @include hover-focus-active() {
        &:before{
            background-color: $theme-color;
            @include opacity(.9);
        }
    }
}

.phone_header {
    img {
        @include rtl-base-toprightbottomleft(margin, 0, 10px, 0, 0);
        vertical-align: middle;
    }
    font-size: 22px;
    line-height: 1;
    font-family: $font-family-second;
    color: $link-color;
}

.contact-us {
    text-align: center;

    .title {
        margin: 0 0 10px;
        font-size: 26px;

        @media(min-width: 1200px) {
            font-size: 36px;
        }
    }

    .des {
        margin-bottom: 20px;

        @media(min-width: 1200px) {
            margin-bottom: 50px;
        }
    }

    .form-control {
        margin-bottom: 20px;

        @media(min-width: 1200px) {
            margin-bottom: $theme-margin;
        }
    }

    textarea {
        height: 150px;
        resize: none;
    }
}

.widget-process {
    padding-top: 35px;

    .title {
        color: $theme-color;
        margin: 0;
        font-size: 14px;
    }

    .des {
        font-size: 18px;
        font-family: $font-family-three;
        color: $white;
        font-weight: normal;

        @media(min-width: 1200px) {
            font-size: 20px;
        }
    }

    .top-img {
        margin-bottom: 12px;

        @media(min-width: 1200px) {
            margin-bottom: 20px;
        }
    }

    .proces-item {
        position: relative;

        .line-space {
            position: absolute;
            @include size(190px, 35px);
            left: 100%;
            @include translateX(-50%);
            bottom: 100%;
        }

        &:last-child {
            .line-space {
                display: none;
            }
        }
    }
}

// language
.language-wrapper {
    font-size: 12px;
    display: inline-block;
    position: relative;

    &:before {
        content: '';
        position: absolute;
        top: 100%;
        left: 0;
        @include size(100%, 5px);
    }

    .selected {
        .language-current {
            >img {
                @include rtl-base-toprightbottomleft(margin, 0, 10px, 0, 0);
                vertical-align: sub;
            }

            >i {
                @include rtl-margin-left(10px);
            }
        }
    }

    .dropdown-menu {
        background: $white;
        display: block;
        @include opacity(0);
        @include translateY(10px);
        font-size: 12px;
        margin-top: 5px;
        @include border-radius(0);
        padding: 13px 18px;
        min-width: 130px;
        @include box-shadow(none);
        border: 1px solid $border-color;
        @include transition(all 0.3s ease-in-out 0s);
        visibility: hidden;
    }

    &:hover {
        .dropdown-menu {
            visibility: visible;
            @include translateY(0);
            @include opacity(1);
        }
    }

    .list-language {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
            margin-bottom: 10px;

            &:last-child {
                margin-bottom: 0;
            }
        }

        a {

            &:hover,
            &:focus {
                color: $theme-color;
            }
        }

        img {
            @include rtl-margin-right(6px);
            margin-bottom: 0;
        }
    }
}

.top-info {
    line-height: 1.4;

    .item {
        i {
            @include rtl-margin-right(5px);
        }

        display: inline-block;
        vertical-align: middle;
        @include rtl-padding-right(18px);
        @include rtl-margin-right(18px);
        @include rtl-border-right(1px solid $border-color);

        &:last-child {
            margin: 0;
            padding: 0;
            border: 0;
        }
    }
}

//social-link
.social-link {
    display: inline-block;
    margin: 0 5px;
    padding: 0;

    li {
        display: inline-block;
        margin: 0 5px;

        a {
            background: #f4f4f4 none repeat scroll 0 0;
            border-radius: 100%;
            color: $text-color;
            display: inline-block;
            height: 40px;
            line-height: 38px;
            text-align: center;
            width: 40px;
            border: 1px solid $border-color;
        }
    }

    &.lighten {
        li a {
            background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
            border: 1px solid $white;
            color: $white;
        }
    }
}

// .widget-gallery
.widget-gallery {
    .image {
        position: relative;

        &:before {
            @include transition(all 0.2s ease-in-out 0s);
            content: '';
            @include size(100%, 100%);
            position: absolute;
            top: 0;
            left: 0;
            background: $theme-color;
            @include opacity(0);
            z-index: 2;
        }

        .content-cover {
            @include transition(all 0.2s ease-in-out 0s);
            @include opacity(0);
            position: absolute;
            text-align: center;
            width: 100%;
            top: 50%;
            left: 50%;
            @include translate(-50%, -50%);
            z-index: 9;
        }

        &:hover {
            &:before {
                @include opacity(0.9);
            }

            .content-cover {
                @include opacity(1);
            }
        }
    }

    .popup-image-gallery {
        @include size(60px, 60px);
        line-height: 60px;
        display: inline-block;
        text-align: center;
        background: $white;
        font-size: 24px;
        color: $theme-color;
        @include border-radius(50%);
        @include transition(all 0.2s ease-in-out 0s);

        &:hover,
        &:active {
            color: $theme-color;
            background: darken($white, 10%);
        }
    }

    .title {
        font-size: 24px;
        font-size: $font-family-second;
        margin: 0;
        color: $white;
    }

    .description {
        color: #e0dede;
        font-size: 12px;
        margin-bottom: 20px;
    }

    .gutter-default {
        margin-left: 0;
        margin-right: 0;

        >div {
            padding-right: 0;
            padding-left: 0;
        }
    }

    &.gutter30 {
        .title {
            font-size: 18px;
            font-family: $font-family-second;
        }

        .image {
            margin-bottom: 30px;
        }

        .description {
            margin-bottom: 10px;
        }

        .gutter-default {
            margin-left: -15px;
            margin-right: -15px;

            >div {
                padding-right: 15px;
                padding-left: 15px;
            }
        }
    }
}

// widget feature
.widget-features-box {
    .slick-slide{
        img {            
            @include inline-block();
        }
    }     
    .title {
        margin: 0;
        font-size: 16px;
    }
    &.style1{
        .icon{
            [class*="icon-"]{
                &:before{
                    font-size: 64px;                                        
                    line-height: normal;
                    @include rtl-margin-left(0);
                }
            }
        }
    }
    &.style1 {
        background-color: transparent;                
        .item-inner {
            position: relative;
        }        
        .features-box-image {
            margin-bottom: 12px;
        }
    }
    &.style2 {
        .features-box-content {
            font-size: 16px;
            line-height: 24px;
        }

        .title {
            margin: 0 0 6px;
            line-height: 1;
        }

        .features-box-content {
            overflow: hidden;
        }

        &.right {
            .features-box-image {
                float: right;
                padding-left: 15px;

                @media(min-width: 1200px) {
                    padding-left: 25px;
                }
            }
        }

        &.left {
            .features-box-image {
                float: left;
                padding-right: 15px;

                @media(min-width: 1200px) {
                    padding-right: 25px;
                }
            }
        }
    }
    &.style3 {
        outline: none;
    }
    &.style4 {
        outline: none;   
    }
    .list-vertical {
        .item {
            margin-bottom: 20px;
            @media(min-width: 1200px) {
                margin-bottom: 40px;
            }
            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.apus-countdown {
    .times {
        font-size: 15px;        
        text-transform: none;
        > div {                        
            padding: 0;            
            width: 25%;
            text-align: center;        
            @include rtl-text-align-left();
            @include inline-block();            
            @include rtl-margin-right(0);
            > span {                
                display: block;
                font-size: 26px;                
                margin-bottom: 3px;
                line-height: normal;
                color: $theme-color;
                font-family: $headings-font-family;
            }
        }
    }
}

.apus-countdown-dark {
    color: $white;
    .times {
        font-size: 15px;        
        text-transform: none;
        > div {                                                
            width: 25%;
            text-align: center;                                
            @include inline-block();            
            @include rtl-margin-right(0);               
            > span {                
                color: $white;
                display: block;
                font-size: 26px;                
                margin-bottom: 3px;
                line-height: normal;                
                font-family: $headings-font-family;
            }
        }
    }
}

.widget-countdown {
    .price {
        font-size: 18px;

        strong {
            font-family: $font-family-three;
            font-size: 20px;

            @media(min-width: 1200px) {
                font-size: 30px;
            }
        }
    }

    .title {
        font-size: 30px;
        margin: 0 0 10px;

        @media(min-width: 1200px) {
            font-size: 44px;
        }
    }

    .des {
        margin-bottom: 10px;
        line-height: 1.6;

        @media(min-width: 1200px) {
            margin-bottom: 30px;
        }
    }

    &.style2 {
        .apus-countdown .times>div {
            border-color: $white;
            background-color: $white;
            @include box-shadow(0 2px 2px 0 rgba(0, 0, 0, 0.1));
        }
    }

    &.style3 {
        .apus-countdown .times>div {
            border-color: $white;
            background-color: $white;
            @include box-shadow(0 2px 2px 0 rgba(0, 0, 0, 0.1));
            @include border-radius(50%);
            min-width: 60px;

            @media(min-width: 1200px) {
                min-width: 79px;

                span {
                    margin: 4px 0;
                }
            }
        }

        .title {
            position: relative;
            padding-bottom: 5px;

            &:before {
                content: '';
                @include size(60px, 3px);
                background: $theme-color;
                position: absolute;
                top: 100%;
                @include rtl-left(0);
            }
        }
    }

    .url-bottom {
        margin-top: 10px;

        @media(min-width: 1200px) {
            margin-top: 35px;
        }
    }

    .btn {
        border-width: 2px;
        @include border-radius(50px);
    }
}

.widget-banner {
    overflow: hidden;

    .btn {
        border-width: 2px;
    }

    .image-wrapper img {
        width: auto;
    }

    .banner-content {
        img {
            margin: 0;
        }
    }

    .flex-top {
        .banner-image {
            margin-top: 10px;

            @media(min-width: 1200px) {
                margin-top: 25px;
            }
        }

        .banner-content {
            margin-top: 20px;

            @media(min-width: 1200px) {
                margin-top: 45px;
            }
        }
    }

    &.style1 {
        .inner {
            position: relative;
            height: 200px;
        }

        .banner-image img {
            max-height: 150px;
        }

        @media(min-width: 1200px) {
            .inner {
                height: 450px;
            }

            .banner-image img {
                max-height: 300px;
            }
        }

        .btn {
            position: absolute;
            bottom: 0;
            left: 50%;
            @include opacity(0);
            @include transition(all 0.2s ease-in-out 0s);
            @include translateX(-50%);
            z-index: 2;
            padding: 8px 25px;
            @include border-radius(50px);

            @media(min-width: 1200px) {
                font-size: 16px;
            }
        }

        &:hover {
            .btn {
                bottom: $theme-margin;
                @include opacity(1);
            }
        }
    }

    &.style2 {
        background-color: $theme-color;

        .inner {
            height: 150px;
        }

        .banner-image img {
            max-height: 180px;
        }

        @media(min-width: 1200px) {
            .inner {
                height: 235px;
            }

            .banner-image img {
                max-height: 200px;
            }
        }

        .banner-content {
            font-size: 20px;
            color: #252525;
            line-height: 1.3;

            strong {
                font-weight: normal;
                font-family: $font-family-three;
            }

            @media(min-width: 1200px) {
                font-size: 30px;
            }
        }
    }

    &.style3 {
        background-color: $theme-color;

        .inner {
            height: 200px;
        }

        .banner-image img {
            max-height: 180px;
        }

        .link-bottom {
            padding-top: 5px;

            @media(min-width: 1200px) {
                padding-top: 15px;
            }
        }

        @media(min-width: 1200px) {
            .inner {
                height: 286px;
            }

            .banner-image img {
                max-height: 250px;
            }
        }

        .banner-content {
            font-size: 20px;
            color: #252525;
            line-height: 1.2;

            strong {
                font-weight: normal;
                font-family: $font-family-three;
            }

            @media(min-width: 1200px) {
                font-size: 30px;
            }
        }
    }

    &.style4 {
        background-color: $theme-color;

        .inner {
            height: 120px;
        }

        .banner-image img {
            max-height: 100px;
        }

        .link-bottom {
            padding-top: 5px;
        }

        @media(min-width: 1200px) {
            .inner {
                height: 180px;
            }

            .banner-image img {
                max-height: 150px;
            }
        }

        .banner-content {
            font-size: 18px;
            color: #252525;
            line-height: 1.2;

            strong {
                font-weight: normal;
                font-family: $font-family-three;
            }

            @media(min-width: 1200px) {
                font-size: 24px;
            }
        }

        .btn {
            font-size: 12px;
            padding: 6px 25px;
        }
    }
}

.widget-testimonials {
    text-align: center; 
    margin-bottom: 0;       
    &.style1{        
        .symbol{                                    
            top: 0;            
            display: block;
            position: absolute;            
            font-family: $headings-font-family;
            font-weight: 700;            
            color: #f1f3f7;    
            font-size: 604px;        
            line-height: 460px;
            @include size(auto,100%);
            @include rtl-left(calc(100% - 82%));                     
        }   
        .slick-carousel{
            margin: 0 0 30px 0;
            height: 86px;            
            .slick-track {  
                text-align: center;     
                @include flexbox();
                @include align-items(center);
                @include justify-content(center);
            }
            &.testimonial-thumbnail{
                text-align: center;
                max-width: 320px;
                margin-left: auto;
                margin-right: auto;
            }
        }         
        .description{
            white-space: pre-line;
        }
        .avarta{                      
            cursor: pointer;
            display: inline-block;            
            float: none;
            padding: 0 !important;
            margin: 0 15px !important;       
            border: 2px solid transparent;
            @include square(80px !important);
            @include scale(.8);
            @include transition-all();            
            &.slick-current{                
                border-color: $white;
                @include scale(1);
            }
        }
    }
    &.style2{
        max-width: 630px;
        margin-right: auto;
        margin-left: auto;
        .slick-carousel{
            .slick-dots{
                padding-top: 32px;
            }
        }
        .testimonials-item{
            &.slick-active{
                .info{
                    @include opacity(1);
                }
            }
            .info{
                position: relative;
                z-index: 2;
                text-align: center;
                max-width: 555px;
                margin-right: auto;
                margin-left: auto;                
                @include opacity(.5);
            }
        }
        .slick-carousel{
            .slick-slide{
                &.slick-active{
                    .description{
                        color: $white;
                        background-color: $theme-color;
                        &:before{
                            color: $white;
                        } 
                    }
                }                
            }
        }         
        .slick-list{
            overflow: visible;            
        }
        .description{            
            position: relative;        
            overflow: hidden;       
            margin-bottom: 30px;         
            background-color: transparent;
            @include rtl-text-align-left();
            @include transition-all();
            @include border-radius(5px);   
            @include rtl-padding(37px, 47px, 37px, 123px);
            &:before{
                top: 90px;
                content: '“';
                font-size: 180px;              
                position: absolute;
                color: #d4d4d4;
                font-family: $headings-font-family;
                @include rtl-left(24px);                
            }         
        }
        .avarta{
            margin: 0 auto 20px auto;
            @include square(80px);
        }
    }
    &.style3{        
        .avarta{
            margin: 0 auto;
            margin-bottom: 20px;
            overflow: hidden;
            @include square(110px);
            @include border-radius(100%);
            img{
                @include inline-block();
            }
        }
        .testimonials-item{
            padding: 15px;
        }
        .testimonial-info-container{            
            position: absolute;
            top: -55px;
            right: 0;
            left: 0;
            z-index: 1;
            padding: 0px 120px 60px;    
        }
        .info{
            position: relative;
            max-width: 750px;
            padding: 60px;
            margin-left: auto;
            margin-right: auto;
            margin-top: 55px;
            min-height: 300px;            
            &:after{
                top: 0;                
                content: '';
                background-color: $white;
                position: absolute;
                @include square(100%);
                @include rtl-left(0);
                @include rtl-right(0);
                @include border-radius(4px);
                @include box-shadow(0 0 20px 0 rgba(0, 0, 0, 0.09));  
            }   
            &:before{    
                top: -50px;        
                overflow: hidden;
                content: '“';
                color: $headings-color;
                font-family: 'Montserrat';
                font-weight: 700;
                font-size: 170px;
                position: absolute;                
                line-height: 152px;
                @include rtl-left(20px);
                @include square(80px);
            }              
        }
        .name-client{
            text-transform: none;
        }
        .job{
            font-size: 15px;
            margin-bottom: 30px;
        }
        .slick-dots{
            padding-top: 45px;
        }
    }
    img {        
        @include border-radius(100%);
    }
    .avarta {
        margin-bottom: 26px;    
        @include border-radius(100%);            
    }
    .name-client {
        margin: 0;
        font-size: 18px;   
        font-weight: 400;
        text-transform: uppercase;     
    }
    .job{
        font-size: 13px;
        margin-bottom: 20px;
    }
    .description {
        line-height: 30px;        
    }
    .rating {
        margin: 10px 0 0;
    }
}

// custom menu
.widget-nav-menu {    
    .widget-title {
        font-size: 20px;
        font-weight: 600;
        line-height: 30px;
        margin-bottom: 20px;        
        text-transform: uppercase;
    }    
    .menu {
        li { 
            margin: 0px;           
            line-height: 30px;            
            a {
                font-size: 14px;       
                color: inherit;                
                @include inline-block();                                    
                @include translateX(0);  
                @include transition-all();
                @include hover-focus-active() {
                    @include translateX(10px);  
                    color: $headings-color;
                }          
            }              
        }
    }    
    &.vertical{
        outline: none;
    }
    &.dark{
        .widget-title {
            color: $white;
        } 
        .menu {
            li {                
                a{
                    color: $footer-color;
                    @include hover-focus-active() {
                        color: $white;                        
                    }
                }
            }
        }
    }
    &.horizontal{
        .menu{
            li{
                @include inline-block();
                padding: 13px 15px;
                a{
                    @include rtl-float-left();
                    @include translateX(0);
                    @include hover-focus-active() {
                        color: $headings-color;
                        @include translateX(0);
                    }
                }
            }
        }        
    }
    &.horizontal_dark{
        .menu{
            li{
                @include inline-block();
                padding: 13px 15px;
                a{
                    color: $footer-color;
                    @include rtl-float-left();
                    @include translateX(0);
                    @include hover-focus-active() {
                        color: $white;
                        @include translateX(0);
                    }
                }                
            }
        }
    }
}

// widget copyright
.widget-copyright{
    font-size: 14px;
    line-height: 24px;
    color: $footer-color;
    &.dark{
        a{
            @include hover-focus-active() {
                color: $white;
            }
        }
    }
    a{
        color: $footer-color;    
        @include hover-focus-active() {
            color: $headings-color;
        }    
    }
}

// widget contact intro
.widget-contact-intro{
    color: $footer-color;
    .widget-title{     
        font-size: 20px;
        line-height: 30px;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 20px;
        color: $headings-color;
    }
    &.dark{
        .widget-title{
            color: $white;            
        }
        .menu{
            li{                  
                a{                    
                    @include hover-focus-active() {
                        color: $white;
                    }
                }
            }
        }
    }    
    .menu{
        li{
            margin-bottom: 0;         
            line-height: 30px;   
            a{
                color: $footer-color; 
                @include hover-focus-active() {
                    color: $headings-color;
                }               
            }
        }
    }
}

// widget app
.widget-app{
    &.dark{
        .widget-title{
            color: $white;
        }
    }
    .widget-title{        
        font-size: 20px;
        line-height: 30px;
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 20px;
    }
    ul{
        li{
            margin-bottom: 16px;
            &.app-store{
                a{
                    background-image: url('../images/iphone.png');
                    background-repeat: no-repeat;
                    background-position: 23px center;
                    background-size: 20px 38px;
                }
            }
            &.app-google-play{
                a{
                    background-image: url('../images/android.png');
                    background-repeat: no-repeat;
                    background-position: 23px center;
                    background-size: 27px 32px;
                    @include rtl-padding-left(68px);
                }
            }
            &:last-child{
                margin-bottom: 0;
            }
            a{
                display: block;
                color: $footer-color;
                background-color: $headings-color;             
                @include rtl-padding(15px, 23px, 15px, 63px);
                @include border-radius(5px);
                span{
                    display: block;
                } 
                @include hover-focus-active() {
                    color: $footer-color;
                }               
            }
            .app-heading{
                color: $white;
                font-size: 16px;
                line-height: normal;
                font-family: $headings-font-family;
            }
            .app-content{
                font-size: 13px;
                line-height: 24px;
            }
        }
    }
}

// widget action box 
.widget-action-box {
    padding: 20px 0;
    @include transition(all 0.2s ease-in-out 0s);    
    .action-inner {
        width: 50%;
        @include rtl-float-right();
    }
    .title {
        margin: 0 0 8px;
        font-size: 24px;
        font-family: $font-family-second;
    }
    .description {
        margin: 0 0 30px;
    }
    &:hover {
        @include box-shadow(0 15px 10px -10px rgba(0, 0, 0, 0.15));
    }
    &.style2 {
        &:hover {
            @include box-shadow(5px 5px 5px 0 rgba(0, 0, 0, 0.2));
        }
        padding:0;
        .box-img {
            text-align: center;
        }
        .action-v2 {
            padding: 15px 0 40px;
            display: table;
            width: 100%;

            >div {
                display: table-cell;
                vertical-align: middle;
                float: none;
            }
        }
    }
}

// instagram
.instagram-pics {
    margin-left: 0;
    margin-right: 0;

    >div {
        padding-left: 0;
        padding-right: 0;
        @include rtl-float-left();
    }

    .col-xs-cus-5 {
        width: 25%;
    }

    @media(min-width:768px) {
        .col-xs-cus-5 {
            width: 20%;
        }
    }
}

.item-instagram {
    position: relative;

    .like-comments {
        position: absolute;
        z-index: 9;
        top: 50%;
        text-align: center;
        width: 100%;
        @include translateY(-50%);
        color: $white;
        font-size: 16px;
        @include transition(all 0.2s ease-in-out 0s);
        @include opacity(0);

        i {
            @include rtl-margin-right(5px);
        }

        >span+span {
            @include rtl-margin-left(30px);
        }
    }

    a {
        display: block;
        position: relative;

        &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            @include size(100%, 100%);
            background: rgba(0, 0, 0, 0.3);
            @include opacity(0);
            z-index: 9;
            @include transition(all 0.2s ease-in-out 0s);
        }

        &:active,
        &:hover {
            &:before {
                @include opacity(1);
            }
        }
    }

    &:hover {
        a:before {
            @include opacity(1);
        }

        .like-comments {
            @include opacity(1);
        }
    }
}

.widget-instagram.style_large {
    .widget-title {
        text-align: center;
        font-size: 32px;
    }

    @media(min-width:1200px) {
        .instagram-pics {
            margin-left: -15px;
            margin-right: -15px;

            >div {
                padding-left: 15px;
                padding-right: 15px;
            }
        }
    }
}

.widget-instagram.style_small {
    .instagram-pics {
        margin-left: -4px;
        margin-right: -4px;

        >div {
            padding-left: 4px;
            padding-right: 4px;
        }

        a {
            margin-bottom: 8px;
        }
    }
}

//widget-brands
.widget-brand {
    text-align: center;
    padding: 15px 10px;
    &.default {        
        @include rtl-padding(30px, 0, 40px, 0);
        .slick-carousel{
            @media only screen and (min-width : 1224px) {
                .slick-dots{
                    display: none !important;
                }
            }
        }        
    }
    &.dark {
        background: #f5f5f5;
    }    
    .slick-slide{
        img {
            @include inline-block();
            @include transition-all();
        }
    }         
    .brand-item{
        @include rtl-text-align-left();
    }
}
.no-space {
    margin: 0 !important;
    >.apus-container {
        >.vc_fluid {
            padding: 0;
        }
    }

    .row {
        margin: 0;

        >[class *="col"] {
            padding: 0;
        }
    }
}

.space-45 {
    margin-bottom: 45px;
}

.no-margin {
    .widget {
        margin-bottom: 0;
    }
}

@media(max-width:1199px) {
    .bg-hidden-md {
        background-image: none !important;
    }
}

@media(max-width:991px) {
    .bg-hidden-sm {
        background-image: none !important;

        >.vc_column-inner {
            background-image: none !important;
        }
    }
}

@media(min-width:992px) {
    .space-padding-lr-100 {
        padding-left: 100px;
        padding-right: 100px;
    }

    .space-padding-lr-80 {
        padding-left: 80px;
        padding-right: 80px;
    }

    .space-rl-50 {
        padding-left: 50px;
        padding-right: 50px;
    }

    .space-l-65 {
        padding-left: 65px;
    }

    .top-170 {
        margin-top: -170px;
    }

    .left-100 {
        margin-left: -100px;
    }

    .space-left-45 {
        padding-left: 45px;
    }

    .right-30 {
        @include rtl-padding-right(30px);
    }

    .padding-lr-60 {
        padding-left: 60px;
        padding-right: 60px;
    }

    .padding-lr-45 {
        padding-left: 45px;
        padding-right: 45px;
    }

    .padding-lr-55 {
        padding-left: 55px;
        padding-right: 55px;
    }

    .padding-lr-25 {
        padding-left: 25px;
        padding-right: 25px;
    }

    .space-border {
        .row {
            margin: 0;

            >[class *="col"] {
                @include rtl-border-right(1px solid $border-color);

                &:last-child {
                    border: none;
                }
            }
        }
    }
}

@media(min-width:1200px) {
    .padding-lr-15 {
        padding-left: 15px;
        padding-right: 15px;
    }
}

.flow-hidden {
    overflow: hidden;
}

.skew-light {
    position: relative;

    &:before {
        content: '';
        background: rgba(255, 255, 255, 0.2);
        @include size(100%, 100%);
        position: absolute;
        top: 0;
        left: 0;
        -ms-transform: skewX(20deg);
        /* IE 9 */
        -webkit-transform: skewX(20deg);
        /* Safari */
        transform: skewX(20deg);
        /* Standard syntax */
    }
}

.skew-theme-right {
    position: relative;
    background: $theme-color;

    >* {
        position: relative;
        z-index: 2;
    }

    &:before {
        content: '';
        background: $theme-color;
        @include size(100%, 100%);
        position: absolute;
        top: 0;
        left: 60px;
        z-index: 1;
        -ms-transform: skewX(20deg);
        /* IE 9 */
        -webkit-transform: skewX(20deg);
        /* Safari */
        transform: skewX(20deg);
        /* Standard syntax */
    }
}

.skew-theme-left {
    position: relative;
    background: $theme-color;

    >* {
        position: relative;
        z-index: 2;
    }

    &:before {
        content: '';
        background: $theme-color;
        @include size(100%, 100%);
        position: absolute;
        top: 0;
        right: 60px;
        z-index: 1;
        -ms-transform: skewX(-20deg);
        /* IE 9 */
        -webkit-transform: skewX(-20deg);
        /* Safari */
        transform: skewX(-20deg);
        /* Standard syntax */
    }
}

.flow-theme-top-bottom-right {
    &:before {
        height: calc(100% + 172px);
        top: -110px;
    }

    &:after {
        content: '';
        position: absolute;
        @include size(100%, 100%);
        z-index: 1;
        background: $theme-color;
        right: 10%;
        top: -110px;
        height: calc(100% + 172px);
        width: 500%;
    }
}

.style-white {
    color: $white !important;

    .widget-title {
        color: $white !important;
    }
}

.border-top-theme {
    border-top: 3px solid $theme-color;
}

.font-size-36 {
    font-size: 36px !important;
}

.font-size-30 {
    font-size: 30px !important;
}

.space-left-8 {
    @include rtl-margin-left(8px);
}

.text-upper {
    text-transform: uppercase !important;
}

.space-10 {
    margin-bottom: 10px !important;
}

.space-15 {
    margin-bottom: 15px !important;
}

.space-20 {
    margin-bottom: 20px !important;
}

.space-25 {
    margin-bottom: 25px !important;
}

.space-30 {
    margin-bottom: 15px !important;

    @media(min-width: 768px) {
        margin-bottom: 30px !important;
    }
}

.space-50 {
    margin-bottom: 50px !important;
}

.space-padding-35 {
    padding: 35px 0 !important;
}

.space-padding-tb-90 {
    padding-top: 90px !important;
    padding-bottom: 90px !important;
}

.space-padding-tb-55 {
    padding-top: 55px !important;
    padding-bottom: 55px !important;
}

.space-top-0 {
    margin-top: 0;
}

.p-relative {
    position: relative !important;
}

.p-absolute {
    position: absolute !important;
}

.p-static {
    position: static !important;
}

.hr {
    border-top: 1px solid $border-color;
    min-height: 1px;
    width: 100%;
}

.no-float {
    float: none !important;
}

.no-padding-left {
    @include rtl-padding-left(0);
}

.no-padding-right {
    @include rtl-padding-right(0);
}

@media(min-width:1024px) {
    .padding-lr-35 {
        padding-left: 35px;
        padding-right: 35px;
    }
}

@media(min-width:1200px) {
    .gutter-50 {
        margin-left: -25px;
        margin-right: -25px;

        .vc_column_container>.vc_column-inner {
            padding-left: 25px;
            padding-right: 25px;
        }

        .vc_row,
        .row {
            margin-left: -25px;
            margin-right: -25px;
        }
    }
}

.text-theme {
    color: $theme-color !important;
}

.bg-theme {
    background: $theme-color;
}

.border-theme {
    border-color: $theme-color;
}

.radius-3x {
    @include border-radius(3px !important);
}

.radius-50 {
    @include border-radius(50px !important);
}

.deleted_wpb_single_image {
    position: relative;
    overflow: hidden;

    &:before {
        position: absolute;
        z-index: 2;
        @include transition(all 0.2s ease-in-out 0s);
        content: '';
        @include size(100%, 100%);
        background: $theme-color;
        @include opacity(0);
        top: 0;
        left: 0;
    }

    &:after {
        position: absolute;
        @include transition(all 0.3s ease-in-out 0s);
        content: '';
        top: $theme-margin;
        left: $theme-margin;
        right: $theme-margin;
        bottom: $theme-margin;
        border: 1px solid $white;
        z-index: 3;
        @include scale(0);
    }

    &:hover {
        &:before {
            @include opacity(0.5);
        }

        &:after {
            @include scale(1);
        }
    }
}

.list-contact {
    list-style: none;
    padding: 0;
    margin: 17px 0 0 !important;

    li {
        margin: 0 0 3px;

        i {
            @include rtl-margin-right(5px);
        }
    }
}

.gutter-medium-left {
    @media(min-width: 1200px) {
        >.vc_column-inner {
            @include rtl-padding-left(5px);
        }
    }

    @media(min-width: 768px) {
        .vc_row {
            margin-left: -10px;
            margin-right: -10px;

            .vc_column-inner {
                padding-left: 10px;
                padding-right: 10px;
            }
        }
    }
}

// counter
.edumy-counter {
    overflow: hidden;

    .image-icon {
        @include rtl-padding-right(20px);
    }

    .counter-wrapper {
        overflow: hidden;
        font-size: 48px;
        line-height: 1.2;
        font-family: $font-family-second;
        color: $link-color;
    }

    .edumy-counter-title {
        color: #707070;
        font-size: 16px;
    }
}

.widget-team {
    position: relative;
    text-align: center;

    .name-team {
        font-size: 22px;
        margin: 0 0 2px;
    }

    .job {
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .bottom-info {
        padding-top: 15px;
    }

    .description {
        color: #b2b2b2;
        margin-top: 20px;
    }

    .content {
        padding-top: 15px;
    }

    .top-image {
        position: relative;

        &:before {
            z-index: 1;
            content: '';
            position: absolute;
            @include size(100%, 100%);
            background: rgba(0, 0, 0, 0.5);
            @include transition(all 0.3s ease-in-out 0s);
            top: 0;
            left: 0;
            @include opacity(0);
        }

        .social {
            margin: 0;
            padding: 15px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            z-index: 2;
            @include transition(all 0.3s ease-in-out 0s);
            @include opacity(0);
            @include scale(0.5, 0);

            li {
                display: inline-block;
                margin: 0 10px;
            }

            a {
                @include transition(all 0.3s ease-in-out 0s);
                display: inline-block;
                color: $white;
                font-size: 22px;
            }
        }
    }

    &:hover {
        .top-image:before {
            @include opacity(1);
        }

        .social {
            @include opacity(1);
            @include scale(1, 1);
        }
    }
}

// vertical menu
.widget_apus_vertical_menu {
    .widget-title {
        font-size: 16px;
        font-weight: normal;
        margin: 0 0 10px;
        padding: 15px $theme-margin 0;
    }
    @include rtl-border-left(4px solid #2e2d2d);
    .apus-vertical-menu {
        border: none;
        >li {
            @include rtl-margin-left(-4px);
            >a {
                @include rtl-border-left(4px solid transparent);
                font-size: 16px;
                padding: 0 $theme-margin;
            }
            &.active,
            &:hover {
                >a {
                    border-color: $theme-color;
                }
            }
        }
    }
    &.darken {
        .apus-vertical-menu {
            background: transparent;
            >li {
                >a {
                    color: #eeeae2;
                    padding: 0 20px;
                }
                &.active,
                &:hover {
                    >a {
                        color: $theme-color;
                    }
                }
            }
        }
        .widget-title {
            color: #eeeae2;
            border: none;
            margin: 0;
            padding-right: 20px;
            padding-left: 20px;
            &:before {
                display: none;
            }
        }
    }
}

.tabs-v1 {
    margin-bottom: 30px;
    @media(min-width: 1200px) {
        margin-bottom: 55px;
    }
    .ps-container {
        position: relative;
    }
    .nav-tabs {
        border: none;
        @include flexbox();
        @include justify-content(center);
        > li {                        
            margin: 0 15px;
            > a {
                margin: 0;                
                text-align: center;
                font-size: 16px;                
                padding: 5px 15px;                
                text-transform: none;
                white-space: nowrap;
                color: $link-color;
                border-width: 0 0 2px 0 !important;
                border-style: solid !important;
                border-color: transparent !important;
                background-color: transparent !important;
                @include transition-all();
            }
            &.active {
                > a {                    
                    pointer-events: none;
                    color: $theme-color;
                    border-bottom-color: $theme-color !important;
                }
            }
        }
    }
    .tab-content {        
        background: #f9fafc;
        padding: 60px 0;
        border: 0;
        position: relative;
        &:before,&:after{
            content: '';
            top: 0;
            z-index: -1;
            position: absolute;
            width: 100vw;
            height: 100%;            
            background: #f9fafc;
        }
        &:before{
            @include rtl-left(-100%);
        }
        &:after{
            @include rtl-right(-100%);
        }
        h2{
            display: none;
        } 
        p{
            margin-bottom: 30px;
            &:last-child{
                margin-bottom: 0;
            }
        }
    }
}

.login-account{
    list-style: none;
    padding: 0;
    margin: 0;
    @include flexbox();
    @include align-items(center);
    li{
        a{
            [class*="icon-"]{
                &:before{
                    font-size: 22px;        
                    margin: 0;        
                }
            }                           
        }
        &.icon-log{
            @include rtl-margin(0,12px,0,0);
        }
    }
}

.form-acount,
.form-acount .inner,
.form-container,
.apus-register-form,
.apus-login-form{
    position: relative;
    background-color: $white;
    @include opacity(1);
    @include transition-all();
    &.loading{
        @include opacity(.2);
        &:before{
            content: '';
            background-image: url('../images/loading.gif');
            @include center-box(absolute);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center center;
            @include square(100px);
            overflow: hidden;
            z-index: 10;
        }
    }
}

.apus-mfp-zoom-in{
    .mfp-content{        
        .search-form-popup{                        
            .mfp-close{
                top: 0;        
                overflow: hidden;
                cursor: pointer;
                position: absolute;
                color: transparent;
                line-height: normal;
                background-color: transparent;
                padding: 0px;
                @include rtl-margin(22px, 71px, 0, 0);            
                @include flexbox();
                @include align-items(center);
                @include justify-content(center);
                @include rtl-right(0);
                @include opacity(1);
                @include square(50px);
                @include hover-focus-active() {
                    @include opacity(1);
                }
                &:before,&:after{
                    position: absolute;                        
                    content: ' ';
                    height: 36px;
                    width: 1px;
                    background-color: $white;
                    @include rtl-left(25px);
                }
                &:before{            
                    @include rotate(45deg);
                }
                &:after{            
                    @include rotate(-45deg);
                }
            }
            .search-form-popup-wrapper{        
                width: 800px;
                height: 66px;
                @include center-box(absolute);
            }
            .apus-search{
                height: 66px;                
                border: 0;
                outline: none;
                background-color: $white;
                @include border-radius(5px);
                padding: 15px 70px 15px 30px;
            }
            .btn-search-icon{
                border: 0;
                padding: 0;
                background-color: transparent;        
                @include vertical-align(absolute);
                @include rtl-right(20px);   
                @include hover-focus-active() {
                    [class*="icon"]{
                        &:before{
                            color: $text-second;
                        }
                    }
                }     
                [class*="icon"]{
                    @include square(30px);
                    @include flexbox();
                    @include align-items(center);
                    @include justify-content(center);            
                    &:before{
                        font-size: 26px;                
                        margin: 0;
                        color: $text-second;
                        @include rotate(90deg);
                    }
                }
            }
        }
        .apus-register-form{
            .list-roles{
                @include justify-content(center);
                margin-bottom: 20px;
                .role-wrapper{
                    margin: 0 10px;
                    position: relative;                
                    label {            
                        font-size: 14px;
                        font-weight: 400;                    
                        margin-bottom: 0;
                        cursor: pointer;        
                        color: $text-second;                                                        
                        position: relative;     
                        margin: 0;
                        width: 100%;
                        @include rtl-padding-left(28px); 
                        @include rtl-padding-right(28px);
                        &:after {
                            content: '';                        
                            top: 2px;
                            position: absolute;
                            border: 1px solid $border-input-form;
                            background-color: transparent;
                            @include border-radius(50px);
                            @include square(19px);                                  
                            @include rtl-left(0);                          
                            @include transition-all();
                            @include box-shadow(0px 1px 4px 0px rgba(0, 0, 0, 0.05));
                        }     
                        &:before{
                            visibility: hidden;
                            position: absolute;
                            content: '';
                            top: 6px;
                            z-index: 1;
                            display: block;                                                
                            border: solid $white;
                            border-width: 0 2px 2px 0;
                            pointer-events: none;
                            @include rtl-left(7px); 
                            @include size(5px,10px);                        
                            @include rotate(45deg);
                            @include opacity(0);
                        }           
                        .count{
                            top: 0;
                            color: $text-color;
                            position: absolute;
                            @include rtl-right(0);                        
                        }  
                        .review-stars-rated-wrapper{
                            margin-top: 5px;
                        }  
                    }
                    input[type=radio] {
                        visibility: hidden;
                        margin: 0;
                        position: absolute;                    
                        top: 8px;
                        @include rtl-left(3px);                   
                        &:checked{
                            + label{
                                &:after {                                
                                    border-color: $theme-color;
                                    background-color: $theme-color;
                                    @include box-shadow(none);
                                }
                                &:before{
                                    visibility: visible;                                
                                    pointer-events: auto;
                                    @include opacity(1);
                                }
                            }                        
                        }                    
                    }
                }
            }
        }
        .apus_login_register_form{
            @include border-radius(5px);
            background-color: $white;        
            padding: 0;
            max-width: 550px;            
            margin: auto;                        
            text-align: center;
            .sign-in-demo-notice{                
                margin: 0 40px;
                padding: 10px 20px;
                @include border-radius(3px);
                background-color: #e5eff8;
                strong{
                    color: $brand-info;
                }
            }
            .tab-pane{
                *{
                    &:focus{
                        outline: none;
                    }
                }                                
            }
            .mfp-close{
                @include square(50px);
                @include flexbox();
                @include align-items(center);
                @include justify-content(center);
                background-color: $theme-color;
                color: transparent;
                padding: 0;
                font-size: 0;
                outline: none;
                @include border-radius(100%);
                background-image: url('../images/close-icon.png');
                background-repeat: no-repeat;
                background-position: center center;
                background-size: 18px;
                @include opacity(1);
                top: -25px;
                @include rtl-right(-25px);
            }
            .form-login-register-inner{
                .ali-right{
                    @include rtl-text-align-right();
                }
                .back-link{
                    font-size: 14px;
                    color: $headings-color;
                    text-decoration: none;
                    @include transition-all();
                    @include hover-focus-active() {
                        color: $theme-color;
                        text-decoration: underline;
                    }
                }
                .nav-tabs{
                    @include clearfix();        
                    border: 0;
                    margin-bottom: 40px;
                    li{
                        width: 50%;
                        margin: 0;                    
                        a{
                            width: 100%;
                            border: 0;
                            padding: 20px 15px;
                            display: block;
                            text-align: center;
                            font-size: 18px;
                            color: $headings-color !important;
                            font-family: $headings-font-family;
                            font-weight: $headings-font-weight;
                            background-color: transparent;
                            @include transition-all();
                            @include border-radius-separate(5px, 5px, 0px, 0px);
                            @include hover-focus-active() {
                                color: $headings-color !important;
                                background-color: #f9fafc;
                            }
                        }
                        &.active{
                            a{
                                pointer-events: none;
                                background-color: #f9fafc;
                            }
                        }
                    }
                }
                .tab-content{                
                    padding: 40px 20px 30px 20px;                   
                    @media only screen and (min-width : 1224px) {
                        padding: 40px;
                    }
                    .title-account{
                        margin-top: 0;
                        margin-bottom: 30px;
                        text-align: center;
                        font-size: 25px;                    
                    }                
                    .form-group {
                        margin-bottom: 20px;                    
                    }
                    .action-login{
                        margin-bottom: 20px;
                        p{
                            margin-bottom: 0;
                        }            
                        .form-group {         
                            margin-bottom: 0;                          
                            label{                            
                                cursor: pointer; 
                                font-size: 14px;
                                color: $headings-color;
                                font-weight: 400;
                                margin-bottom: 0;      
                                white-space: nowrap;
                                @include flexbox();
                                @include align-items(center);                      
                                span{
                                    position: relative;  
                                    @include rtl-padding-left(14px);                                                      
                                    &:after {
                                        content: '';                                                                                                                
                                        top: 3px;                                                                                    
                                        background: transparent; 
                                        position: absolute;                                                                                   
                                        border: 1px solid $border-input-form;
                                        @include border-radius(3px);
                                        @include square(17px);
                                        @include rtl-left(-13px);
                                    }
                                    &:before{                                    
                                        visibility: hidden;
                                        @include opacity(0);
                                        content: '\f00c';
                                        font-family: 'FontAwesome';
                                        top: 2px;
                                        color: $headings-color;
                                        @include rtl-left(-10px);
                                        font-size: 12px;
                                        position: absolute;
                                        @include transition-all();
                                    }
                                }                            
                                input[type=checkbox] {
                                    visibility: hidden;
                                    margin: 0;
                                    &:checked+span:before {   
                                        visibility: visible;                             
                                        @include opacity(1);
                                    }
                                }
                            }                        
                        }
                    }                
                }
                .btn-block{
                    font-size: 19px;
                    font-weight: $headings-font-weight;
                    font-family: $headings-font-family;
                    @include border-radius(5px);
                    text-transform: none;
                    border: 0;      
                    height: 55px;
                    background-color: $theme-color-dark;
                }
            }
        }
    }
}

.login-register-mfp-zoom-in{
    .mfp-content{
        height: auto;
    }
}

.gallery{
    .gallery-item{
        .gallery-icon{
            a{
                position: relative;
                display: block;
                &:before{
                    content: '';
                    top: 0px;                
                    position: absolute;
                    pointer-events: none;
                    visibility: hidden;                    
                    background-color: $theme-color;                    
                    @include opacity(0);
                    @include square(100%);
                    @include rtl-left(0);
                    @include transition-all();                       
                }
                &:after{
                    color: $white;
                    font-size: 36px;
                    content: "\f162";                    
                    font-family: $icon-font-family;
                    pointer-events: none;
                    overflow: hidden;
                    cursor: pointer;
                    margin: auto;
                    bottom: 0;
                    top: 0;
                    position: absolute;
                    @include rtl-left(0);
                    @include rtl-right(0);                    
                    @include square(50px);                    
                    @include scale(0);
                    @include opacity(0);
                    @include transition-all();                    
                    @include flexbox();
                    @include align-items(center);
                    @include justify-content(center);
                }
                @include hover-focus-active() {
                    &:before{
                        visibility: visible;
                        pointer-events: auto;
                        @include opacity(.8);                 
                    }  
                    &:after{                     
                        pointer-events: auto;
                        overflow: visible;                        
                        @include scale(1);
                        @include opacity(1);
                    }                  
                }
            }            
        }
    }
}


.nav.tabs-product {
    border: none;
    &.has-border {
        border-bottom: 1px solid $border-color;
        > li {
            margin-bottom: -2px;
        }
    }
    >li {
        display: inline-block;
        float: none;
        margin-bottom: 0;
        >a {
            border: none !important;
            font-size: 18px;
            color: $text-color;
            font-family: $font-family-second;
            background: transparent;
            position: relative;
            padding: 0 0 8px;

            &:before {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                @include size(100%, 3px);
                background: $theme-color;
                @include transition(all 0.3s ease-in-out 0s);
                @include scale(0);
            }
        }

        &.active>a {

            &,
            &:hover,
            &:focus {
                color: $theme-color;

                &:before {
                    @include scale(1);
                }
            }
        }
    }

    &.center {
        text-align: center;

        >li {
            margin-left: 15px;
            margin-right: 15px;

            @media(min-width: 1200px) {
                margin-left: 10px;
                margin-right: 10px;
            }
        }

        margin-bottom: 25px;

        @media(min-width: 1200px) {
            margin-bottom: 35px;
        }
    }

    &.right {
        text-align: right;

        >li {
            @include rtl-margin-right(20px);

            @media(min-width: 1200px) {
                @include rtl-margin-right(40px);
            }

            &:last-child {
                @include rtl-margin-right(0);
            }
        }
    }

    &.left {
        text-align: left;

        >li {
            @include rtl-margin-left(20px);

            @media(min-width: 1200px) {
                @include rtl-margin-left(50px);
            }

            &:last-child {
                @include rtl-margin-left(0);
            }
        }
    }
}

@media(min-width: 768px) {
    .flex-middle-sm {
        display: -webkit-flex;
        /* Safari */
        display: flex;
        align-items: center;
        -webkit-align-items: center;
    }

    .flex-top-sm {
        display: -webkit-flex;
        /* Safari */
        display: flex;
        align-items: flex-start;
        -webkit-align-items: flex-start;
    }
}

.flex-middle {
    display: -webkit-flex;
    /* Safari */
    display: flex;
    align-items: center;
    -webkit-align-items: center;    
}

.flex-top {
    display: -webkit-flex;
    /* Safari */
    display: flex;
    align-items: flex-start;
    -webkit-align-items: flex-start;
}

.flex-bottom {
    display: -webkit-flex;
    /* Safari */
    display: flex;
    align-items: flex-end;
    -webkit-align-items: flex-end;
}

.max-1800 {
    max-width: 1800px;
    margin-left: auto;
    margin-right: auto;
}

.cl-3,
.cl-2 {
    @include rtl-float-left();

    >.row {
        margin: 0;
    }

    .widget {
        padding-left: $theme-margin / 2;
        padding-right: $theme-margin / 2;
    }
}

.cl-3 {
    width: 58.3333%;
}

.cl-2 {
    width: 41.6667%;
}

.font-second {
    font-family: $font-family-second !important;
    font-weight: normal;
}

.max-1770 {
    max-width: 1770px;
    margin-left: auto;
    margin-right: auto;
}

.updow {
    &:hover {

        .top-img img,
        .img img,
        .image-wrapper img {
            -webkit-animation: updow 0.8s ease-in-out 0s infinite;
            animation: updow 0.8s ease-in-out 0s infinite;
        }
    }
}

.updow-infinite {
    img {
        -webkit-animation: updow 1s ease-in-out 0s infinite;
        animation: updow 1s ease-in-out 0s infinite;
    }
}

@-webkit-keyframes updow {
    50% {
        @include translateY(-10px);
    }

    0%,
    100% {
        @include translateY(0px);
    }
}

@keyframes updow {
    50% {
        @include translateY(-10px);
    }

    0%,
    100% {
        @include translateY(0px);
    }
}

@-webkit-keyframes fadeleft {
    from {
        @include opacity(1);
    }

    to {
        @include opacity(0);
        @include translate(-15px, 0);
    }
}

@keyframes fadeleft {
    from {
        @include opacity(1);
    }

    to {
        @include opacity(0);
        @include translate(-15px, 0);
    }
}

@-webkit-keyframes faderight {
    from {
        @include opacity(0);
        @include translate(15px, 0);
    }

    to {
        @include opacity(1);
        @include translate(0, 0);
    }
}

@keyframes faderight {
    from {
        @include opacity(0);
        @include translate(15px, 0);
    }

    to {
        @include opacity(1);
        @include translate(0, 0);
    }
}