# Copyright (C) 2016 ApusTheme
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Edumy 1.2.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/theme/edumy\n"
"POT-Creation-Date: 2021-05-13 03:37:23+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: 404.php:34
msgid "Back To Homepage"
msgstr ""

#: comments.php:26 template-posts/loop/inner-grid-v3.php:36
#: template-posts/loop/inner-list.php:46 template-posts/single/inner.php:45
msgid "0 Comments"
msgstr ""

#: comments.php:26 template-posts/loop/inner-grid-v3.php:36
#: template-posts/loop/inner-list.php:46 template-posts/single/inner.php:45
msgid "1 Comment"
msgstr ""

#: comments.php:26 template-posts/loop/inner-grid-v3.php:36
#: template-posts/loop/inner-list.php:46 template-posts/single/inner.php:45
msgid "% Comments"
msgstr ""

#: comments.php:40
msgid "Comments are closed."
msgstr ""

#: comments.php:46
msgid "Leave a Comment"
msgstr ""

#: comments.php:48
msgid "Your Comment"
msgstr ""

#: comments.php:54
msgid "Name*"
msgstr ""

#: comments.php:57
msgid "Email*"
msgstr ""

#: comments.php:60 inc/vendors/simple-event/functions.php:141
#: templates-education/single-course/reviews.php:85
#: templates-education/single-course/reviews.php:113
#: templates-education-v4/single-course/reviews.php:85
#: templates-education-v4/single-course/reviews.php:113
#: widgets/profile-info.php:32
msgid "Website"
msgstr ""

#: comments.php:64
msgid "Submit Comment"
msgstr ""

#: comments.php:65
msgid "Your email address will not be published."
msgstr ""

#: content-search.php:7 inc/classes/megamenu.php:124
#: inc/classes/mobilemenu.php:114 inc/classes/mobileverticalmenu.php:114
#: inc/template-tags.php:49 template-posts/loop/inner-grid-v5.php:7
#: template-posts/loop/inner-grid-v6.php:7 template-posts/loop/inner-grid.php:7
msgid "Featured"
msgstr ""

#: footer.php:27
msgid "&copy; %s - Edumy. All Rights Reserved. <br/> Powered by <a href=\"//apusthemes.com\">ApusThemes</a>"
msgstr ""

#: functions.php:79
msgid "Primary Menu"
msgstr ""

#: functions.php:80
msgid "Vertical Menu"
msgstr ""

#: functions.php:81
msgid "My Account Menu"
msgstr ""

#. Translators: If there are characters in your language that are not supported
#. by Montserrat, translate this to 'off'. Do not translate into your own
#. language.

#: functions.php:148
msgctxt "Nunito font: on or off"
msgid "on"
msgstr ""

#: functions.php:149
msgctxt "Open Sans font: on or off"
msgid "on"
msgstr ""

#: functions.php:150
msgctxt "Montserrat font: on or off"
msgid "on"
msgstr ""

#: functions.php:235
msgid "Days"
msgstr ""

#: functions.php:236
msgid "Hrs"
msgstr ""

#: functions.php:237
msgid "Mins"
msgstr ""

#: functions.php:238
msgid "Secs"
msgstr ""

#: functions.php:255
msgid "Previous"
msgstr ""

#: functions.php:256 inc/functions-frontend.php:451
#: inc/functions-frontend.php:502
msgid "Next"
msgstr ""

#: functions.php:335
msgid "Sidebar Default"
msgstr ""

#: functions.php:337
msgid "Add widgets here to appear in your Sidebar."
msgstr ""

#: functions.php:345
msgid "Blog sidebar"
msgstr ""

#: functions.php:347 functions.php:357 functions.php:367 functions.php:377
#: functions.php:387 functions.php:397 functions.php:407
msgid "Add widgets here to appear in your sidebar."
msgstr ""

#: functions.php:355
msgid "Shop Sidebar"
msgstr ""

#: functions.php:365
msgid "Courses archive Sidebar"
msgstr ""

#: functions.php:375
msgid "Course single Sidebar"
msgstr ""

#: functions.php:385
msgid "Events archive Sidebar"
msgstr ""

#: functions.php:395
msgid "Event single Sidebar"
msgstr ""

#: functions.php:405
msgid "Profile Sidebar"
msgstr ""

#: functions.php:419
msgid "Apus Framework For Themes"
msgstr ""

#: functions.php:426
msgid "Elementor Page Builder"
msgstr ""

#: functions.php:432 inc/vendors/elementor/widgets/revslider.php:45
msgid "Revolution Slider"
msgstr ""

#: functions.php:439
msgid "Cmb2"
msgstr ""

#: functions.php:445
msgid "MailChimp for WordPress"
msgstr ""

#: functions.php:451
msgid "Contact Form 7"
msgstr ""

#: functions.php:458
msgid "Woocommerce"
msgstr ""

#: functions.php:465
msgid "LearnPress – WordPress LMS Plugin"
msgstr ""

#: functions.php:472
msgid "Apus Simple Event"
msgstr ""

#: functions.php:479
msgid "One Click Demo Import"
msgstr ""

#: headers/mobile/header-mobile.php:75 inc/classes/userinfo.php:173
#: inc/vendors/elementor/header_widgets/user_info.php:212
msgid "Log out "
msgstr ""

#: headers/mobile/header-mobile.php:83 inc/classes/userinfo.php:134
#: inc/vendors/elementor/header_widgets/user_info.php:220
#: template-parts/login-form.php:33 template-parts/login-register.php:6
#: woocommerce/myaccount/form-login.php:32
msgid "Login"
msgstr ""

#: headers/mobile/header-mobile.php:84 inc/classes/userinfo.php:136
#: inc/vendors/elementor/header_widgets/user_info.php:221
#: template-parts/login-register.php:7 template-parts/register-form.php:1
#: woocommerce/myaccount/form-login.php:77
#: woocommerce/myaccount/form-login.php:114
msgid "Register"
msgstr ""

#: headers/mobile/header-mobile.php:92
msgid "Library"
msgstr ""

#: headers/mobile/offcanvas-menu.php:5 inc/functions-frontend.php:611
#: inc/functions-frontend.php:626
msgid "Close"
msgstr ""

#: inc/classes/megamenu.php:116 inc/classes/mobilemenu.php:106
#: inc/classes/mobileverticalmenu.php:106
msgid "New"
msgstr ""

#: inc/classes/megamenu.php:120 inc/classes/mobilemenu.php:110
#: inc/classes/mobileverticalmenu.php:110
msgid "Hot"
msgstr ""

#: inc/classes/userinfo.php:34
msgid "Wrong username or password. Please try again!!!"
msgstr ""

#: inc/classes/userinfo.php:37
msgid "Signin successful, redirecting..."
msgstr ""

#: inc/classes/userinfo.php:54
msgid "Enter an username or e-mail address."
msgstr ""

#: inc/classes/userinfo.php:60
msgid "There is no user registered with that email address."
msgstr ""

#: inc/classes/userinfo.php:66
msgid "There is no user registered with that username."
msgstr ""

#: inc/classes/userinfo.php:69
msgid "Invalid username or e-mail address."
msgstr ""

#: inc/classes/userinfo.php:86
msgid "Your new password"
msgstr ""

#: inc/classes/userinfo.php:88
msgid "Your new password is: "
msgstr ""

#: inc/classes/userinfo.php:96
msgid "Check your email address for you new password."
msgstr ""

#: inc/classes/userinfo.php:98
msgid "System is unable to send you mail containg your new password."
msgstr ""

#: inc/classes/userinfo.php:101
msgid "Oops! Something went wrong while updating your account."
msgstr ""

#: inc/classes/userinfo.php:196
msgid "Required form field is missing"
msgstr ""

#: inc/classes/userinfo.php:200
msgid "Username too short. At least 4 characters is required"
msgstr ""

#: inc/classes/userinfo.php:204
msgid "That username already exists!"
msgstr ""

#: inc/classes/userinfo.php:208
msgid "The username you entered is not valid"
msgstr ""

#: inc/classes/userinfo.php:212
msgid "Password length must be greater than 5"
msgstr ""

#: inc/classes/userinfo.php:216
msgid "Password must be equal Confirm Password"
msgstr ""

#: inc/classes/userinfo.php:220
msgid "Email is not valid"
msgstr ""

#: inc/classes/userinfo.php:224
msgid "Email Already in use"
msgstr ""

#: inc/classes/userinfo.php:249
msgid "You have registered, redirecting ..."
msgstr ""

#: inc/classes/userinfo.php:256
msgid "Register user error!"
msgstr ""

#: inc/classes/userinfo.php:281
msgid "Nickname is required."
msgstr ""

#: inc/classes/userinfo.php:286
msgid "E-mail is required."
msgstr ""

#: inc/classes/userinfo.php:315
msgid "Profile has been successfully updated."
msgstr ""

#: inc/classes/userinfo.php:331
msgid "All fields are required."
msgstr ""

#: inc/classes/userinfo.php:336
msgid "New and retyped password are not same."
msgstr ""

#: inc/classes/userinfo.php:343
msgid "Your old password is not correct."
msgstr ""

#: inc/classes/userinfo.php:354
msgid "Your password has been successfully changed."
msgstr ""

#: inc/customizer.php:32
msgid "Base Color Scheme"
msgstr ""

#: inc/customizer.php:47
msgid "Header and Sidebar Text Color"
msgstr ""

#: inc/customizer.php:48 inc/customizer.php:64 inc/customizer.php:69
msgid "Applied to the header on small screens and the sidebar on wide screens."
msgstr ""

#: inc/customizer.php:63
msgid "Header and Sidebar Background Color"
msgstr ""

#: inc/customizer.php:118
#: inc/vendors/elementor/header_widgets/search_form.php:78
#: inc/vendors/elementor/widgets/brands.php:93
#: inc/vendors/elementor/widgets/heading.php:162
#: inc/vendors/elementor/widgets/popup_video.php:78
#: inc/vendors/elementor/widgets/posts.php:58
#: inc/vendors/elementor/widgets/posts.php:78
#: inc/vendors/learnpress/functions.php:173 inc/widgets/custom_menu.php:40
msgid "Default"
msgstr ""

#: inc/customizer.php:129 inc/vendors/elementor/header_widgets/user_info.php:66
#: inc/vendors/elementor/widgets/brands.php:94
#: inc/vendors/elementor/widgets/social_links.php:105
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:76
msgid "Dark"
msgstr ""

#: inc/customizer.php:140
msgid "Yellow"
msgstr ""

#: inc/customizer.php:151
msgid "Pink"
msgstr ""

#: inc/customizer.php:162
msgid "Purple"
msgstr ""

#: inc/customizer.php:173
msgid "Blue"
msgstr ""

#: inc/functions-frontend.php:37
msgid " / By: "
msgstr ""

#: inc/functions-frontend.php:60
msgid "by:"
msgstr ""

#: inc/functions-frontend.php:85
msgid "0 comments"
msgstr ""

#: inc/functions-frontend.php:85
msgid "1 comment"
msgstr ""

#: inc/functions-frontend.php:85
msgid "% comments"
msgstr ""

#: inc/functions-frontend.php:97
msgid "Tags: "
msgstr ""

#: inc/functions-frontend.php:153 inc/functions-frontend.php:155
#: inc/functions-frontend.php:290
msgid "The Blogs"
msgstr ""

#: inc/functions-frontend.php:166
#: inc/vendors/redux-framework/redux-config.php:188
#: inc/vendors/redux-framework/redux-config.php:293
msgid "Blog"
msgstr ""

#: inc/functions-frontend.php:180
msgid "Search results for \""
msgstr ""

#: inc/functions-frontend.php:182
msgid "Posts tagged \""
msgstr ""

#: inc/functions-frontend.php:188 inc/functions-frontend.php:288
msgid "Error 404"
msgstr ""

#: inc/functions-frontend.php:201 inc/samples/sample-data.php:10
msgid "Home"
msgstr ""

#: inc/functions-frontend.php:280
msgid "Search results for \"%s\""
msgstr ""

#: inc/functions-frontend.php:282
msgid "Posts tagged \"%s\""
msgstr ""

#: inc/functions-frontend.php:286
msgid "Articles posted by "
msgstr ""

#: inc/functions-frontend.php:450 inc/functions-frontend.php:501
msgid "Prev"
msgstr ""

#: inc/functions-frontend.php:458
msgid "Posts navigation"
msgstr ""

#: inc/functions-frontend.php:480
msgid "Post navigation"
msgstr ""

#: inc/functions-frontend.php:484
msgid "Published In"
msgstr ""

#: inc/functions-frontend.php:486
msgid "Previous Post"
msgstr ""

#: inc/functions-frontend.php:487
msgid "Next Post"
msgstr ""

#: inc/functions-frontend.php:640 search.php:22
msgid "Show Sidebar"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:334
msgid "Install Required Plugins"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:335
msgid "Install Plugins"
msgstr ""

#. translators: %s: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:337
msgid "Installing Plugin: %s"
msgstr ""

#. translators: %s: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:339
msgid "Updating Plugin: %s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:340
msgid "Something went wrong with the plugin API."
msgstr ""

#. translators: 1: plugin name(s).

#: inc/plugins/class-tgm-plugin-activation.php:377
msgid "Begin installing plugin"
msgid_plural "Begin installing plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:382
msgid "Begin updating plugin"
msgid_plural "Begin updating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:387
msgid "Begin activating plugin"
msgid_plural "Begin activating plugins"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:392
msgid "Return to Required Plugins Installer"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:393
#: inc/plugins/class-tgm-plugin-activation.php:917
#: inc/plugins/class-tgm-plugin-activation.php:2623
#: inc/plugins/class-tgm-plugin-activation.php:3670
msgid "Return to the Dashboard"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:394
#: inc/plugins/class-tgm-plugin-activation.php:3249
msgid "Plugin activated successfully."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:395
#: inc/plugins/class-tgm-plugin-activation.php:3042
msgid "The following plugin was activated successfully:"
msgstr ""

#. translators: 1: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:397
msgid "No action taken. Plugin %1$s was already active."
msgstr ""

#. translators: 1: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:399
msgid "Plugin not activated. A higher version of %s is needed for this theme. Please update the plugin."
msgstr ""

#. translators: 1: dashboard link.

#: inc/plugins/class-tgm-plugin-activation.php:401
msgid "All plugins installed and activated successfully. %1$s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:402
msgid "Dismiss this notice"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:403
msgid "There are one or more required or recommended plugins to install, update or activate."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:404
msgid "Please contact the administrator of this site for help."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:607
msgid "This plugin needs to be updated to be compatible with your theme."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:608
msgid "Update Required"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:725
msgid "Set the parent_slug config variable instead."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1024
msgid "The remote plugin package does not contain a folder with the desired slug and renaming did not work."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1024
#: inc/plugins/class-tgm-plugin-activation.php:1027
msgid "Please contact the plugin provider and ask them to package their plugin according to the WordPress guidelines."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1027
msgid "The remote plugin package consists of more than one file, but the files are not packaged in a folder."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:1211
#: inc/plugins/class-tgm-plugin-activation.php:3038
msgctxt "plugin A *and* plugin B"
msgid "and"
msgstr ""

#. translators: %s: version number

#: inc/plugins/class-tgm-plugin-activation.php:2072
msgid "TGMPA v%s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2363
msgid "Required"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2366
msgid "Recommended"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2382
msgid "WordPress Repository"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2385
msgid "External Source"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2388
msgid "Pre-Packaged"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2405
msgid "Not Installed"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2409
msgid "Installed But Not Activated"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2411
msgid "Active"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2417
msgid "Required Update not Available"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2420
msgid "Requires Update"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2423
msgid "Update recommended"
msgstr ""

#. translators: 1: install status, 2: update status

#: inc/plugins/class-tgm-plugin-activation.php:2432
msgctxt "Install/Update Status"
msgid "%1$s, %2$s"
msgstr ""

#. translators: 1: number of plugins.

#: inc/plugins/class-tgm-plugin-activation.php:2478
msgctxt "plugins"
msgid "All <span class=\"count\">(%s)</span>"
msgid_plural "All <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.

#: inc/plugins/class-tgm-plugin-activation.php:2482
msgid "To Install <span class=\"count\">(%s)</span>"
msgid_plural "To Install <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.

#: inc/plugins/class-tgm-plugin-activation.php:2486
msgid "Update Available <span class=\"count\">(%s)</span>"
msgid_plural "Update Available <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: 1: number of plugins.

#: inc/plugins/class-tgm-plugin-activation.php:2490
msgid "To Activate <span class=\"count\">(%s)</span>"
msgid_plural "To Activate <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: inc/plugins/class-tgm-plugin-activation.php:2572
msgctxt "as in: \"version nr unknown\""
msgid "unknown"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2580
msgid "Installed version:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2588
msgid "Minimum required version:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2600
msgid "Available version:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2623
msgid "No plugins to install, update or activate."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2637
msgid "Plugin"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2638
msgid "Source"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2639
msgid "Type"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2643
msgid "Version"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2644
#: woocommerce/myaccount/my-orders.php:36
#: woocommerce/myaccount/my-orders.php:57
msgid "Status"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup

#: inc/plugins/class-tgm-plugin-activation.php:2693
msgid "Install %2$s"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup

#: inc/plugins/class-tgm-plugin-activation.php:2698
msgid "Update %2$s"
msgstr ""

#. translators: %2$s: plugin name in screen reader markup

#: inc/plugins/class-tgm-plugin-activation.php:2704
msgid "Activate %2$s"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2774
msgid "Upgrade message from the plugin author:"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2807
msgid "Install"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2813
msgid "Update"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2816
msgid "Activate"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2847
msgid "No plugins were selected to be installed. No action taken."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2849
msgid "No plugins were selected to be updated. No action taken."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2890
msgid "No plugins are available to be installed at this time."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2892
msgid "No plugins are available to be updated at this time."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:2998
msgid "No plugins were selected to be activated. No action taken."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3024
msgid "No plugins are available to be activated at this time."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3248
msgid "Plugin activation failed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.

#: inc/plugins/class-tgm-plugin-activation.php:3588
msgid "Updating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#. translators: 1: plugin name, 2: error message.

#: inc/plugins/class-tgm-plugin-activation.php:3591
msgid "An error occurred while installing %1$s: <strong>%2$s</strong>."
msgstr ""

#. translators: 1: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:3593
msgid "The installation of %1$s failed."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3597
msgid "The installation and activation process is starting. This process may take a while on some hosts, so please be patient."
msgstr ""

#. translators: 1: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:3599
msgid "%1$s installed and activated successfully."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3599
#: inc/plugins/class-tgm-plugin-activation.php:3607
msgid "Show Details"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3599
#: inc/plugins/class-tgm-plugin-activation.php:3607
msgid "Hide Details"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3600
msgid "All installations and activations have been completed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.

#: inc/plugins/class-tgm-plugin-activation.php:3602
msgid "Installing and Activating Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3605
msgid "The installation process is starting. This process may take a while on some hosts, so please be patient."
msgstr ""

#. translators: 1: plugin name.

#: inc/plugins/class-tgm-plugin-activation.php:3607
msgid "%1$s installed successfully."
msgstr ""

#: inc/plugins/class-tgm-plugin-activation.php:3608
msgid "All installations have been completed."
msgstr ""

#. translators: 1: plugin name, 2: action number 3: total number of actions.

#: inc/plugins/class-tgm-plugin-activation.php:3610
msgid "Installing Plugin %1$s (%2$d/%3$d)"
msgstr ""

#: inc/template-tags.php:23
msgid "Comment navigation"
msgstr ""

#: inc/template-tags.php:26
msgid "Older Comments"
msgstr ""

#: inc/template-tags.php:30
msgid "Newer Comments"
msgstr ""

#: inc/template-tags.php:55
msgctxt "Used before post format."
msgid "Format"
msgstr ""

#: inc/template-tags.php:76
msgctxt "Used before publish date."
msgid "Posted on"
msgstr ""

#: inc/template-tags.php:85
msgctxt "Used before post author name."
msgid "Author"
msgstr ""

#: inc/template-tags.php:91 inc/template-tags.php:99
msgctxt "Used between list items, there is a space after the comma."
msgid ", "
msgstr ""

#: inc/template-tags.php:94
msgctxt "Used before category names."
msgid "Categories"
msgstr ""

#: inc/template-tags.php:102
msgctxt "Used before tag names."
msgid "Tags"
msgstr ""

#: inc/template-tags.php:113
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr ""

#. translators: %s: post title

#: inc/template-tags.php:123
msgid "Leave a comment<span class=\"screen-reader-text\"> on %s</span>"
msgstr ""

#: inc/template-tags.php:225
msgid "/ By: "
msgstr ""

#. translators: %s: Name of current post

#: inc/template-tags.php:261
msgid "Continue reading %s"
msgstr ""

#: inc/vendors/cmb2/header.php:11
msgid "Transparent all page ?"
msgstr ""

#: inc/vendors/cmb2/header.php:14 inc/vendors/cmb2/page.php:34
#: inc/vendors/cmb2/page.php:55 inc/vendors/cmb2/page.php:84
#: inc/vendors/elementor/event_widgets/events.php:150
#: inc/vendors/elementor/event_widgets/events.php:165
#: inc/vendors/elementor/learnpress_widgets/courses.php:141
#: inc/vendors/elementor/learnpress_widgets/courses.php:156
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:162
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:177
#: inc/vendors/elementor/learnpress_widgets/instructors.php:130
#: inc/vendors/elementor/learnpress_widgets/instructors.php:145
msgid "No"
msgstr ""

#: inc/vendors/cmb2/header.php:15 inc/vendors/cmb2/page.php:35
#: inc/vendors/cmb2/page.php:56 inc/vendors/cmb2/page.php:85
#: inc/vendors/elementor/event_widgets/events.php:149
#: inc/vendors/elementor/event_widgets/events.php:164
#: inc/vendors/elementor/learnpress_widgets/courses.php:140
#: inc/vendors/elementor/learnpress_widgets/courses.php:155
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:161
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:176
#: inc/vendors/elementor/learnpress_widgets/instructors.php:129
#: inc/vendors/elementor/learnpress_widgets/instructors.php:144
msgid "Yes"
msgstr ""

#: inc/vendors/cmb2/header.php:22 inc/vendors/cmb2/page.php:107
msgid "Display Settings"
msgstr ""

#: inc/vendors/cmb2/page.php:13 inc/vendors/cmb2/page.php:14
msgid "Global Setting"
msgstr ""

#: inc/vendors/cmb2/page.php:19
msgid "Select Layout"
msgstr ""

#: inc/vendors/cmb2/page.php:23
msgid "Main Content Only"
msgstr ""

#: inc/vendors/cmb2/page.php:24
#: inc/vendors/learnpress/functions-redux-configs.php:103
#: inc/vendors/learnpress/functions-redux-configs.php:104
#: inc/vendors/simple-event/functions-redux-configs.php:102
#: inc/vendors/simple-event/functions-redux-configs.php:103
#: inc/vendors/woocommerce/functions-redux-configs.php:127
#: inc/vendors/woocommerce/functions-redux-configs.php:128
msgid "Left Sidebar - Main Content"
msgstr ""

#: inc/vendors/cmb2/page.php:25
#: inc/vendors/learnpress/functions-redux-configs.php:108
#: inc/vendors/learnpress/functions-redux-configs.php:109
#: inc/vendors/simple-event/functions-redux-configs.php:107
#: inc/vendors/simple-event/functions-redux-configs.php:108
#: inc/vendors/woocommerce/functions-redux-configs.php:132
#: inc/vendors/woocommerce/functions-redux-configs.php:133
msgid "Main Content - Right Sidebar"
msgstr ""

#: inc/vendors/cmb2/page.php:31
#: inc/vendors/learnpress/functions-redux-configs.php:87
#: inc/vendors/learnpress/functions-redux-configs.php:147
#: inc/vendors/redux-framework/redux-config.php:244
#: inc/vendors/redux-framework/redux-config.php:324
#: inc/vendors/simple-event/functions-redux-configs.php:86
#: inc/vendors/simple-event/functions-redux-configs.php:146
#: inc/vendors/woocommerce/functions-redux-configs.php:111
#: inc/vendors/woocommerce/functions-redux-configs.php:227
msgid "Is Full Width?"
msgstr ""

#: inc/vendors/cmb2/page.php:41
msgid "Left Sidebar"
msgstr ""

#: inc/vendors/cmb2/page.php:47
msgid "Right Sidebar"
msgstr ""

#: inc/vendors/cmb2/page.php:53
msgid "Show Breadcrumb?"
msgstr ""

#: inc/vendors/cmb2/page.php:63
msgid "Breadcrumb Background Color"
msgstr ""

#: inc/vendors/cmb2/page.php:68
msgid "Breadcrumb Background Image"
msgstr ""

#: inc/vendors/cmb2/page.php:73
#: inc/vendors/redux-framework/redux-config.php:114
msgid "Header Layout Type"
msgstr ""

#: inc/vendors/cmb2/page.php:74 inc/vendors/cmb2/page.php:82
#: inc/vendors/redux-framework/redux-config.php:115
msgid "Choose a header for your website."
msgstr ""

#: inc/vendors/cmb2/page.php:81
msgid "Header Transparent"
msgstr ""

#: inc/vendors/cmb2/page.php:92
#: inc/vendors/redux-framework/redux-config.php:171
msgid "Footer Layout Type"
msgstr ""

#: inc/vendors/cmb2/page.php:93
#: inc/vendors/redux-framework/redux-config.php:172
msgid "Choose a footer for your website."
msgstr ""

#: inc/vendors/cmb2/page.php:100
msgid "Extra Class"
msgstr ""

#: inc/vendors/cmb2/page.php:101
msgid "If you wish to style particular content element differently, then use this field to add a class name and then refer to it in your css file."
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:15
msgid "Apus Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:27
msgid "Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:34
msgid "Get Events By"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:37
msgid "Recent Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:38
msgid "Upcoming Events"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:47
#: inc/vendors/elementor/learnpress_widgets/courses.php:48
msgid "Categories Slug"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:51
#: inc/vendors/elementor/learnpress_widgets/courses.php:52
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:66
msgid "Enter id spearate by comma(,)"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:58
#: inc/vendors/elementor/learnpress_widgets/courses.php:59
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:83
#: inc/vendors/elementor/learnpress_widgets/instructors.php:35
msgid "Limit"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:61
#: inc/vendors/elementor/learnpress_widgets/courses.php:62
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:85
#: inc/vendors/elementor/learnpress_widgets/instructors.php:38
msgid "Enter number products to display"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:78
#: inc/vendors/elementor/learnpress_widgets/courses.php:70
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:93
#: inc/vendors/elementor/learnpress_widgets/instructors.php:59
#: inc/vendors/elementor/widgets/brands.php:77
#: inc/vendors/elementor/widgets/features_box.php:163
#: inc/vendors/elementor/widgets/instagram.php:76
#: inc/vendors/elementor/widgets/posts.php:99
#: inc/vendors/redux-framework/redux-config.php:220
msgid "Layout"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:81
#: inc/vendors/elementor/learnpress_widgets/courses.php:73
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:96
#: inc/vendors/elementor/learnpress_widgets/instructors.php:62
#: inc/vendors/elementor/widgets/brands.php:80
#: inc/vendors/elementor/widgets/features_box.php:167
#: inc/vendors/elementor/widgets/instagram.php:79
#: inc/vendors/elementor/widgets/posts.php:102
#: inc/vendors/elementor/widgets/posts.php:115
#: inc/vendors/learnpress/functions-redux-configs.php:54
#: inc/vendors/simple-event/functions-redux-configs.php:54
#: inc/vendors/woocommerce/functions-redux-configs.php:67
msgid "Grid"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:82
#: inc/vendors/elementor/learnpress_widgets/courses.php:74
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:97
#: inc/vendors/elementor/learnpress_widgets/instructors.php:63
#: inc/vendors/elementor/widgets/brands.php:81
#: inc/vendors/elementor/widgets/features_box.php:166
#: inc/vendors/elementor/widgets/instagram.php:80
#: inc/vendors/elementor/widgets/posts.php:103
msgid "Carousel"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:83
#: inc/vendors/simple-event/functions-redux-configs.php:55
#: inc/vendors/woocommerce/functions-redux-configs.php:68
msgid "List"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:92
#: inc/vendors/elementor/learnpress_widgets/courses.php:83
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:106
#: inc/vendors/elementor/learnpress_widgets/instructors.php:72
#: inc/vendors/elementor/widgets/brands.php:103
#: inc/vendors/elementor/widgets/features_box.php:139
#: inc/vendors/elementor/widgets/instagram.php:67
#: inc/vendors/elementor/widgets/posts.php:89
#: inc/vendors/elementor/widgets/process.php:84
msgid "Columns"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:95
#: inc/vendors/elementor/learnpress_widgets/courses.php:86
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:108
#: inc/vendors/elementor/learnpress_widgets/instructors.php:75
#: inc/vendors/elementor/widgets/brands.php:106
#: inc/vendors/elementor/widgets/process.php:87
msgid "Enter your column number here"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:103
#: inc/vendors/elementor/learnpress_widgets/courses.php:94
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:116
#: inc/vendors/elementor/learnpress_widgets/instructors.php:83
msgid "Rows"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:106
#: inc/vendors/elementor/learnpress_widgets/courses.php:97
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:118
#: inc/vendors/elementor/learnpress_widgets/instructors.php:86
msgid "Enter your rows number here"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:117
#: inc/vendors/elementor/learnpress_widgets/courses.php:108
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:129
#: inc/vendors/elementor/learnpress_widgets/instructors.php:97
msgid "Show Navigation"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:119
#: inc/vendors/elementor/event_widgets/events.php:134
#: inc/vendors/elementor/learnpress_widgets/courses.php:110
#: inc/vendors/elementor/learnpress_widgets/courses.php:125
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:131
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:146
#: inc/vendors/elementor/learnpress_widgets/instructors.php:99
#: inc/vendors/elementor/learnpress_widgets/instructors.php:114
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:40
msgid "Show"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:120
#: inc/vendors/elementor/event_widgets/events.php:135
#: inc/vendors/elementor/learnpress_widgets/courses.php:111
#: inc/vendors/elementor/learnpress_widgets/courses.php:126
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:132
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:147
#: inc/vendors/elementor/learnpress_widgets/instructors.php:100
#: inc/vendors/elementor/learnpress_widgets/instructors.php:115
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:39
msgid "Hide"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:132
#: inc/vendors/elementor/learnpress_widgets/courses.php:123
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:144
#: inc/vendors/elementor/learnpress_widgets/instructors.php:112
msgid "Show Pagination"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:147
#: inc/vendors/elementor/learnpress_widgets/courses.php:138
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:159
#: inc/vendors/elementor/learnpress_widgets/instructors.php:127
msgid "Autoplay"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:162
#: inc/vendors/elementor/learnpress_widgets/courses.php:153
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:174
#: inc/vendors/elementor/learnpress_widgets/instructors.php:142
msgid "Infinite Loop"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:177
#: inc/vendors/elementor/header_widgets/logo.php:98
#: inc/vendors/elementor/header_widgets/primary_menu.php:61
#: inc/vendors/elementor/header_widgets/search_form.php:102
#: inc/vendors/elementor/header_widgets/user_info.php:88
#: inc/vendors/elementor/header_widgets/vertical_menu.php:74
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:87
#: inc/vendors/elementor/learnpress_widgets/courses.php:168
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:189
#: inc/vendors/elementor/learnpress_widgets/instructors.php:157
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:43
#: inc/vendors/elementor/widgets/banner.php:191
#: inc/vendors/elementor/widgets/brands.php:114
#: inc/vendors/elementor/widgets/call_to_action.php:112
#: inc/vendors/elementor/widgets/countdown.php:154
#: inc/vendors/elementor/widgets/features_box.php:205
#: inc/vendors/elementor/widgets/icon_box.php:99
#: inc/vendors/elementor/widgets/image_box.php:141
#: inc/vendors/elementor/widgets/instagram.php:130
#: inc/vendors/elementor/widgets/mailchimp.php:67
#: inc/vendors/elementor/widgets/nav_menu.php:80
#: inc/vendors/elementor/widgets/popup_video.php:88
#: inc/vendors/elementor/widgets/posts.php:140
#: inc/vendors/elementor/widgets/process.php:132
#: inc/vendors/elementor/widgets/revslider.php:54
#: inc/vendors/elementor/widgets/scroll_up.php:54
#: inc/vendors/elementor/widgets/social_links.php:114
#: inc/vendors/elementor/widgets/team.php:116
#: inc/vendors/elementor/widgets/testimonials.php:109
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:86
msgid "Extra class name"
msgstr ""

#: inc/vendors/elementor/event_widgets/events.php:179
#: inc/vendors/elementor/header_widgets/logo.php:100
#: inc/vendors/elementor/header_widgets/primary_menu.php:63
#: inc/vendors/elementor/header_widgets/search_form.php:104
#: inc/vendors/elementor/header_widgets/user_info.php:90
#: inc/vendors/elementor/header_widgets/vertical_menu.php:76
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:89
#: inc/vendors/elementor/learnpress_widgets/courses.php:170
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:191
#: inc/vendors/elementor/learnpress_widgets/instructors.php:159
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:45
#: inc/vendors/elementor/widgets/banner.php:193
#: inc/vendors/elementor/widgets/brands.php:116
#: inc/vendors/elementor/widgets/call_to_action.php:114
#: inc/vendors/elementor/widgets/countdown.php:156
#: inc/vendors/elementor/widgets/features_box.php:207
#: inc/vendors/elementor/widgets/icon_box.php:101
#: inc/vendors/elementor/widgets/image_box.php:143
#: inc/vendors/elementor/widgets/instagram.php:132
#: inc/vendors/elementor/widgets/mailchimp.php:69
#: inc/vendors/elementor/widgets/nav_menu.php:82
#: inc/vendors/elementor/widgets/popup_video.php:90
#: inc/vendors/elementor/widgets/posts.php:142
#: inc/vendors/elementor/widgets/process.php:134
#: inc/vendors/elementor/widgets/revslider.php:56
#: inc/vendors/elementor/widgets/scroll_up.php:56
#: inc/vendors/elementor/widgets/social_links.php:116
#: inc/vendors/elementor/widgets/team.php:118
#: inc/vendors/elementor/widgets/testimonials.php:111
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:88
msgid "If you wish to style particular content element differently, please add a class name to this field and refer to it in your custom CSS file."
msgstr ""

#: inc/vendors/elementor/functions.php:33
msgid "Edumy Elements"
msgstr ""

#: inc/vendors/elementor/functions.php:41
msgid "Edumy Header Elements"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:16
msgid "Apus Header Logo"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:28
#: inc/vendors/elementor/header_widgets/primary_menu.php:28
#: inc/vendors/elementor/header_widgets/search_form.php:28
#: inc/vendors/elementor/header_widgets/user_info.php:28
#: inc/vendors/elementor/header_widgets/vertical_menu.php:26
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:31
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:25
#: inc/vendors/elementor/widgets/banner.php:95
#: inc/vendors/elementor/widgets/brands.php:28
#: inc/vendors/elementor/widgets/call_to_action.php:28
#: inc/vendors/elementor/widgets/countdown.php:53
#: inc/vendors/elementor/widgets/features_box.php:106
#: inc/vendors/elementor/widgets/icon_box.php:59
#: inc/vendors/elementor/widgets/image_box.php:97
#: inc/vendors/elementor/widgets/popup_video.php:32
#: inc/vendors/elementor/widgets/popup_video.php:48
#: inc/vendors/elementor/widgets/process.php:28
#: inc/vendors/elementor/widgets/social_links.php:32
#: inc/vendors/elementor/widgets/testimonials.php:32
#: inc/vendors/elementor/widgets/testimonials.php:41
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:28
msgid "Content"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:36
msgid "Main Logo"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:50
msgid "Transparent Header Logo"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:73
#: inc/vendors/elementor/header_widgets/primary_menu.php:36
#: inc/vendors/elementor/header_widgets/search_form.php:45
#: inc/vendors/elementor/header_widgets/user_info.php:36
#: inc/vendors/elementor/widgets/countdown.php:71
#: inc/vendors/elementor/widgets/features_box.php:175
#: inc/vendors/elementor/widgets/heading.php:195
#: inc/vendors/elementor/widgets/process.php:103
#: inc/vendors/elementor/widgets/social_links.php:78
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:48
msgid "Alignment"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:77
#: inc/vendors/elementor/header_widgets/primary_menu.php:40
#: inc/vendors/elementor/header_widgets/search_form.php:49
#: inc/vendors/elementor/header_widgets/user_info.php:40
#: inc/vendors/elementor/widgets/banner.php:59
#: inc/vendors/elementor/widgets/banner.php:138
#: inc/vendors/elementor/widgets/countdown.php:75
#: inc/vendors/elementor/widgets/features_box.php:179
#: inc/vendors/elementor/widgets/heading.php:199
#: inc/vendors/elementor/widgets/process.php:107
#: inc/vendors/elementor/widgets/social_links.php:82
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:52
msgid "Left"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:81
#: inc/vendors/elementor/header_widgets/primary_menu.php:44
#: inc/vendors/elementor/header_widgets/search_form.php:53
#: inc/vendors/elementor/header_widgets/user_info.php:44
#: inc/vendors/elementor/widgets/banner.php:63
#: inc/vendors/elementor/widgets/banner.php:142
#: inc/vendors/elementor/widgets/countdown.php:79
#: inc/vendors/elementor/widgets/features_box.php:183
#: inc/vendors/elementor/widgets/heading.php:203
#: inc/vendors/elementor/widgets/popup_video.php:79
#: inc/vendors/elementor/widgets/process.php:111
#: inc/vendors/elementor/widgets/social_links.php:86
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:56
msgid "Center"
msgstr ""

#: inc/vendors/elementor/header_widgets/logo.php:85
#: inc/vendors/elementor/header_widgets/primary_menu.php:48
#: inc/vendors/elementor/header_widgets/search_form.php:57
#: inc/vendors/elementor/header_widgets/user_info.php:48
#: inc/vendors/elementor/widgets/banner.php:67
#: inc/vendors/elementor/widgets/banner.php:146
#: inc/vendors/elementor/widgets/countdown.php:83
#: inc/vendors/elementor/widgets/features_box.php:187
#: inc/vendors/elementor/widgets/heading.php:207
#: inc/vendors/elementor/widgets/process.php:115
#: inc/vendors/elementor/widgets/social_links.php:90
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:60
msgid "Right"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:16
msgid "Apus Header Primary Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:73
#: inc/vendors/elementor/header_widgets/vertical_menu.php:34
#: inc/vendors/elementor/header_widgets/vertical_menu.php:86
#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:33
#: inc/vendors/elementor/widgets/brands.php:37
#: inc/vendors/elementor/widgets/call_to_action.php:36
#: inc/vendors/elementor/widgets/countdown.php:36
#: inc/vendors/elementor/widgets/heading.php:101
#: inc/vendors/elementor/widgets/heading.php:108
#: inc/vendors/elementor/widgets/heading.php:236
#: inc/vendors/elementor/widgets/icon_box.php:49
#: inc/vendors/elementor/widgets/image_box.php:63
#: inc/vendors/elementor/widgets/instagram.php:40
#: inc/vendors/elementor/widgets/mailchimp.php:36
#: inc/vendors/elementor/widgets/mailchimp.php:45
#: inc/vendors/elementor/widgets/nav_menu.php:46
#: inc/vendors/elementor/widgets/nav_menu.php:93
#: inc/vendors/elementor/widgets/popup_video.php:40
#: inc/vendors/elementor/widgets/posts.php:34
#: inc/vendors/elementor/widgets/posts.php:62
#: inc/vendors/elementor/widgets/process.php:37
#: inc/vendors/elementor/widgets/social_links.php:126
#: inc/vendors/elementor/widgets/team.php:128
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:97
#: inc/vendors/redux-framework/redux-config.php:405
msgid "Title"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:80
msgid "Effect Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:83
msgid "Effect 1"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:84
msgid "Effect 2"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:85
msgid "Effect 3"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:93
msgid "Padding Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:104
msgid "Color Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:115
msgid "Color Hover Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:126
msgid "Color Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:137
msgid "Color Hover Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/primary_menu.php:148
msgid "Background Color Dropdown Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:16
msgid "Apus Header Search Form"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:36
msgid "Input placeholder"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:61
#: inc/vendors/elementor/widgets/banner.php:71
#: inc/vendors/elementor/widgets/banner.php:150
#: inc/vendors/elementor/widgets/countdown.php:87
#: inc/vendors/elementor/widgets/features_box.php:191
#: inc/vendors/elementor/widgets/heading.php:211
#: inc/vendors/elementor/widgets/process.php:119
msgid "Justified"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:75
msgid "Layout type"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:79
msgid "Popup"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:88
#: inc/vendors/elementor/header_widgets/user_info.php:62
#: inc/vendors/elementor/header_widgets/vertical_menu.php:60
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:74
#: inc/vendors/elementor/widgets/banner.php:164
#: inc/vendors/elementor/widgets/brands.php:90
#: inc/vendors/elementor/widgets/countdown.php:140
#: inc/vendors/elementor/widgets/countdown.php:165
#: inc/vendors/elementor/widgets/features_box.php:149
#: inc/vendors/elementor/widgets/features_box.php:217
#: inc/vendors/elementor/widgets/icon_box.php:82
#: inc/vendors/elementor/widgets/icon_box.php:111
#: inc/vendors/elementor/widgets/image_box.php:153
#: inc/vendors/elementor/widgets/instagram.php:117
#: inc/vendors/elementor/widgets/instagram.php:144
#: inc/vendors/elementor/widgets/mailchimp.php:54
#: inc/vendors/elementor/widgets/nav_menu.php:65
#: inc/vendors/elementor/widgets/popup_video.php:75
#: inc/vendors/elementor/widgets/process.php:145
#: inc/vendors/elementor/widgets/scroll_up.php:66
#: inc/vendors/elementor/widgets/social_links.php:101
#: inc/vendors/elementor/widgets/testimonials.php:95
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:73
msgid "Style"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:91
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:77
#: inc/vendors/elementor/learnpress_widgets/instructors.php:49
#: inc/vendors/elementor/widgets/banner.php:167
#: inc/vendors/elementor/widgets/countdown.php:143
#: inc/vendors/elementor/widgets/image_box.php:129
#: inc/vendors/elementor/widgets/instagram.php:120
msgid "Style 1"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:92
#: inc/vendors/elementor/learnpress_widgets/category_banner.php:78
#: inc/vendors/elementor/learnpress_widgets/instructors.php:50
#: inc/vendors/elementor/widgets/banner.php:168
#: inc/vendors/elementor/widgets/features_box.php:153
#: inc/vendors/elementor/widgets/image_box.php:130
#: inc/vendors/elementor/widgets/instagram.php:121
msgid "Style 2"
msgstr ""

#: inc/vendors/elementor/header_widgets/search_form.php:93
#: inc/vendors/elementor/widgets/banner.php:169
#: inc/vendors/elementor/widgets/features_box.php:154
#: inc/vendors/elementor/widgets/image_box.php:131
msgid "Style 3"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:16
msgid "Apus Header User Info"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:70
#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:77
#: inc/widgets/custom_menu.php:42
msgid "White"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:74
#: inc/widgets/custom_menu.php:41
msgid "Gray"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:100
#: inc/vendors/elementor/widgets/call_to_action.php:123
#: inc/vendors/elementor/widgets/mailchimp.php:78
#: inc/vendors/elementor/widgets/popup_video.php:100
#: inc/vendors/elementor/widgets/posts.php:153
#: inc/vendors/elementor/widgets/testimonials.php:121
msgid "Tyles"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:108
msgid "Text Color"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:120
#: inc/vendors/elementor/header_widgets/vertical_menu.php:116
msgid "Link Color"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:131
#: inc/vendors/elementor/header_widgets/vertical_menu.php:128
msgid "Link Hover Color"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:142
msgid "Link Drop Color"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:153
msgid "Link Drop Hover Color"
msgstr ""

#: inc/vendors/elementor/header_widgets/user_info.php:165
msgid "Typography"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:14
msgid "Apus Header Vertical Menu"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:46
msgid "Show menu condition"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:49
msgid "Always"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:50
msgid "In home page"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:51
msgid "When hover"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:63
msgid "Style 1 - Dark"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:64
msgid "Style 2 - White"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:65
msgid "Style 3 - Black"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:94
msgid "Background Color Title"
msgstr ""

#: inc/vendors/elementor/header_widgets/vertical_menu.php:105
#: inc/vendors/elementor/widgets/features_box.php:225
#: inc/vendors/elementor/widgets/icon_box.php:131
#: inc/vendors/elementor/widgets/scroll_up.php:74
msgid "Icon Color"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:16
msgid "Apus Category Banner"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:28
msgid "Category Banner"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:36
msgid "Category Title"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:38
msgid "Enter your category title"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:45
#: inc/vendors/elementor/widgets/call_to_action.php:45
#: inc/vendors/elementor/widgets/process.php:45
#: inc/vendors/learnpress/functions-user.php:105
#: inc/vendors/redux-framework/redux-config.php:411
msgid "Description"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:47
#: inc/vendors/elementor/widgets/call_to_action.php:47
#: inc/vendors/elementor/widgets/mailchimp.php:47
msgid "Enter your description here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:55
#: inc/vendors/elementor/widgets/banner.php:37
msgid "Image Background"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:57
#: inc/vendors/elementor/widgets/banner.php:39
msgid "Upload Image Background Here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:64
#: inc/vendors/elementor/widgets/banner.php:85
#: inc/vendors/elementor/widgets/countdown.php:101
msgid "URL"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/category_banner.php:67
msgid "Enter your category link here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:15
msgid "Apus Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:27
#: widgets/profile-instructor.php:37
msgid "Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:34
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:48
msgid "Get Courses By"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:37
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:51
msgid "Recent Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:38
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:52
msgid "Featured Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses.php:39
#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:53
msgid "Popular Courses"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:15
msgid "Apus Courses Tabs"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:40
msgid "Tab Title"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:62
msgid "Category Slug"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:73
msgid "Tabs"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/courses_tabs.php:75
msgid "Enter your product tabs here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/instructors.php:15
msgid "Apus Instructors"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/instructors.php:27
msgid "Instructors"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/instructors.php:46
msgid "Instructor item style"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:14
msgid "Apus My Wishlist"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:36
#: inc/vendors/elementor/widgets/call_to_action.php:39
#: inc/vendors/elementor/widgets/countdown.php:38
#: inc/vendors/elementor/widgets/instagram.php:43
#: inc/vendors/elementor/widgets/mailchimp.php:38
#: inc/vendors/elementor/widgets/nav_menu.php:48
#: inc/vendors/elementor/widgets/posts.php:37
msgid "Enter your title here"
msgstr ""

#: inc/vendors/elementor/learnpress_widgets/my-wishlist.php:66
msgid "Please login to view your wishlist"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:16
msgid "Apus Banner"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:28
msgid "Banner"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:46
#: inc/vendors/elementor/widgets/features_box.php:46
#: inc/vendors/elementor/widgets/team.php:88
#: inc/vendors/simple-event/functions.php:157 woocommerce/cart/cart.php:29
msgid "Image"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:48
#: inc/vendors/elementor/widgets/team.php:90
msgid "Upload Image Here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:55
msgid "Image Alignment"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:88
#: inc/vendors/elementor/widgets/call_to_action.php:67
#: inc/vendors/elementor/widgets/countdown.php:104
msgid "Enter your Button Link here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:97
#: inc/vendors/elementor/widgets/countdown.php:55
msgid "Enter your content here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:104
#: inc/vendors/elementor/widgets/call_to_action.php:54
#: inc/vendors/elementor/widgets/countdown.php:110
msgid "Button Text"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:106
#: inc/vendors/elementor/widgets/call_to_action.php:57
#: inc/vendors/elementor/widgets/countdown.php:112
msgid "Enter your button text here"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:113
#: inc/vendors/elementor/widgets/call_to_action.php:74
#: inc/vendors/elementor/widgets/countdown.php:119
#: inc/vendors/elementor/widgets/image_box.php:126
msgid "Button Style"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:116
#: inc/vendors/elementor/widgets/countdown.php:122
msgid "Theme Color"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:117
#: inc/vendors/elementor/widgets/countdown.php:123
msgid "Theme Outline Color"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:118
#: inc/vendors/elementor/widgets/call_to_action.php:77
#: inc/vendors/elementor/widgets/countdown.php:124
msgid "Default "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:119
#: inc/vendors/elementor/widgets/call_to_action.php:78
#: inc/vendors/elementor/widgets/countdown.php:125
msgid "Primary "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:120
#: inc/vendors/elementor/widgets/call_to_action.php:79
#: inc/vendors/elementor/widgets/countdown.php:126
msgid "Success "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:121
#: inc/vendors/elementor/widgets/call_to_action.php:80
#: inc/vendors/elementor/widgets/countdown.php:127
msgid "Info "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:122
#: inc/vendors/elementor/widgets/call_to_action.php:81
#: inc/vendors/elementor/widgets/countdown.php:128
msgid "Warning "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:123
#: inc/vendors/elementor/widgets/call_to_action.php:82
#: inc/vendors/elementor/widgets/countdown.php:129
msgid "Danger "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:124
#: inc/vendors/elementor/widgets/call_to_action.php:83
#: inc/vendors/elementor/widgets/countdown.php:130
msgid "Pink "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:125
#: inc/vendors/elementor/widgets/call_to_action.php:84
#: inc/vendors/elementor/widgets/countdown.php:131
msgid "White "
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:134
msgid "Content Alignment"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:170
#: inc/vendors/elementor/widgets/features_box.php:155
#: inc/vendors/elementor/widgets/image_box.php:132
msgid "Style 4"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:178
msgid "Vertical Content"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:181
msgid "Top"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:182
msgid "Middle"
msgstr ""

#: inc/vendors/elementor/widgets/banner.php:183
msgid "Bottom"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:16
msgid "Apus Brands"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:39
msgid "Brand Title"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:48
msgid "Brand Image"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:50
#: inc/vendors/elementor/widgets/testimonials.php:52
msgid "Upload Brand Image"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:57
#: inc/vendors/elementor/widgets/heading.php:143
#: inc/vendors/elementor/widgets/process.php:65
msgid "Link"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:60
msgid "Enter your brand link here"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:67
msgid "Brands"
msgstr ""

#: inc/vendors/elementor/widgets/brands.php:69
msgid "Enter your brands here"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:16
msgid "Apus Call To Action"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:64
msgid "Button Link"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:85
msgid "Outline "
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:86
msgid "Custom "
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:95
msgid "Call To Action Style"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:98
#: inc/vendors/elementor/widgets/icon_box.php:85
#: inc/vendors/elementor/widgets/mailchimp.php:57
#: inc/vendors/elementor/widgets/testimonials.php:98
msgid "Style1"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:99
#: inc/vendors/elementor/widgets/icon_box.php:86
#: inc/vendors/elementor/widgets/mailchimp.php:58
#: inc/vendors/elementor/widgets/testimonials.php:99
msgid "Style2"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:100
#: inc/vendors/elementor/widgets/icon_box.php:87
#: inc/vendors/elementor/widgets/testimonials.php:100
msgid "Style3"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:101
#: inc/vendors/elementor/widgets/icon_box.php:88
msgid "Style4"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:102
#: inc/vendors/elementor/widgets/icon_box.php:89
msgid "Style5"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:103
#: inc/vendors/elementor/widgets/icon_box.php:90
msgid "Style6"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:131
#: inc/vendors/elementor/widgets/icon_box.php:119
msgid "Background Color"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:142
#: inc/vendors/elementor/widgets/countdown.php:173
#: inc/vendors/elementor/widgets/features_box.php:237
#: inc/vendors/elementor/widgets/heading.php:244
#: inc/vendors/elementor/widgets/icon_box.php:143
#: inc/vendors/elementor/widgets/image_box.php:194
#: inc/vendors/elementor/widgets/instagram.php:151
#: inc/vendors/elementor/widgets/mailchimp.php:86
#: inc/vendors/elementor/widgets/nav_menu.php:101
#: inc/vendors/elementor/widgets/popup_video.php:108
#: inc/vendors/elementor/widgets/posts.php:161
#: inc/vendors/elementor/widgets/process.php:153
#: inc/vendors/elementor/widgets/testimonials.php:129
msgid "Title Color"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:154
#: inc/vendors/elementor/widgets/countdown.php:185
#: inc/vendors/elementor/widgets/features_box.php:250
#: inc/vendors/elementor/widgets/icon_box.php:156
#: inc/vendors/elementor/widgets/image_box.php:207
#: inc/vendors/elementor/widgets/instagram.php:163
#: inc/vendors/elementor/widgets/mailchimp.php:98
#: inc/vendors/elementor/widgets/nav_menu.php:113
#: inc/vendors/elementor/widgets/popup_video.php:120
#: inc/vendors/elementor/widgets/posts.php:173
#: inc/vendors/elementor/widgets/process.php:164
#: inc/vendors/elementor/widgets/testimonials.php:141
msgid "Title Typography"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:163
#: inc/vendors/elementor/widgets/countdown.php:194
#: inc/vendors/elementor/widgets/features_box.php:259
#: inc/vendors/elementor/widgets/icon_box.php:165
#: inc/vendors/elementor/widgets/image_box.php:216
#: inc/vendors/elementor/widgets/popup_video.php:129
#: inc/vendors/elementor/widgets/process.php:173
msgid "Description Color"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:175
#: inc/vendors/elementor/widgets/countdown.php:206
#: inc/vendors/elementor/widgets/features_box.php:271
#: inc/vendors/elementor/widgets/icon_box.php:177
#: inc/vendors/elementor/widgets/image_box.php:228
#: inc/vendors/elementor/widgets/popup_video.php:141
#: inc/vendors/elementor/widgets/process.php:185
msgid "Description Typography"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:184
#: inc/vendors/elementor/widgets/mailchimp.php:107
msgid "Button Color"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:197
#: inc/vendors/elementor/widgets/mailchimp.php:120
msgid "Button Background"
msgstr ""

#: inc/vendors/elementor/widgets/call_to_action.php:210
#: inc/vendors/elementor/widgets/mailchimp.php:133
msgid "Button Typography"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:16
msgid "Apus Countdown"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:28
msgid "Countdown"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:45 widgets/course-info.php:21
#: woocommerce/cart/cart.php:31 woocommerce/cart/cart.php:83
msgid "Price"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:47
msgid "Enter your Price here"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:60
msgid "End Date"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:144
msgid "Style 2(showdow)"
msgstr ""

#: inc/vendors/elementor/widgets/countdown.php:145
msgid "Style 3(circle)"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:16
msgid "Apus Features Box"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:32
#: inc/vendors/elementor/widgets/features_box.php:130
msgid "Features Box"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:42
msgid "Image or Icon"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:45
#: inc/vendors/elementor/widgets/features_box.php:55
#: inc/vendors/elementor/widgets/icon_box.php:40
#: inc/vendors/elementor/widgets/scroll_up.php:36
msgid "Icon"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:67
#: inc/vendors/elementor/widgets/image_box.php:40
#: inc/vendors/elementor/widgets/testimonials.php:50
msgid "Choose Image"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:96
msgid "Title & Description"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:98
#: inc/vendors/elementor/widgets/icon_box.php:51
#: inc/vendors/elementor/widgets/image_box.php:65
msgid "This is the heading"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:99
#: inc/vendors/elementor/widgets/heading.php:113
#: inc/vendors/elementor/widgets/icon_box.php:52
#: inc/vendors/elementor/widgets/image_box.php:66
msgid "Enter your title"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:108
#: inc/vendors/elementor/widgets/icon_box.php:61
#: inc/vendors/elementor/widgets/image_box.php:99
msgid "Click edit button to change this text. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:109
#: inc/vendors/elementor/widgets/icon_box.php:62
#: inc/vendors/elementor/widgets/image_box.php:100
msgid "Enter your description"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:119
#: inc/vendors/elementor/widgets/icon_box.php:72
#: inc/vendors/elementor/widgets/image_box.php:73
msgid "Link to"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:121
#: inc/vendors/elementor/widgets/icon_box.php:74
#: inc/vendors/elementor/widgets/image_box.php:75
#: inc/vendors/elementor/widgets/testimonials.php:79
msgid "https://your-link.com"
msgstr ""

#: inc/vendors/elementor/widgets/features_box.php:152
msgid "Style 1 (White)"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:42
msgid "Apus Heading"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:114
msgid "Add Your Heading Text Here"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:121
msgid "SubTitle"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:126
msgid "Enter your subtitle"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:134
msgid "Line Decor Image"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:136
msgid "Decor Background Image"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:158
msgid "Size"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:163
#: inc/vendors/elementor/widgets/instagram.php:93
msgid "Small"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:164
msgid "Medium"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:165
#: inc/vendors/elementor/widgets/instagram.php:94
msgid "Large"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:166
msgid "XL"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:167
msgid "XXL"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:175
msgid "HTML Tag"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:225
#: woocommerce/myaccount/my-orders.php:83
msgid "View"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:260
msgid "Subtitle Color"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:293
msgid "Blend Mode"
msgstr ""

#: inc/vendors/elementor/widgets/heading.php:296
#: inc/vendors/elementor/widgets/social_links.php:104
msgid "Normal"
msgstr ""

#: inc/vendors/elementor/widgets/icon_box.php:16
msgid "Apus Icon Box"
msgstr ""

#: inc/vendors/elementor/widgets/icon_box.php:32
msgid "Icon Box"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:16
msgid "Apus Image Box"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:32
msgid "Image Box"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:83
msgid "Layout Type"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:86
#: inc/vendors/learnpress/functions-redux-configs.php:156
msgid "Layout 1"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:87
#: inc/vendors/learnpress/functions-redux-configs.php:157
msgid "Layout 2"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:88
#: inc/vendors/learnpress/functions-redux-configs.php:158
msgid "Layout 3"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:113
msgid "Button text"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:116
msgid "Enter your button text"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:163
msgid "Background Overlay"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:174
msgid "Opacity"
msgstr ""

#: inc/vendors/elementor/widgets/image_box.php:237
msgid "Border Radius"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:16
msgid "Apus Instagram"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:32
msgid "Instagram"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:49
msgid "Instagram Username"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:57
#: inc/vendors/elementor/widgets/posts.php:44
msgid "Number"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:59
msgid "Number images to display"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:89
msgid "Photo size"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:92
msgid "Thumbnail"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:95
msgid "Original"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:104
msgid "Open links in"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:107
msgid "Current window (_self)"
msgstr ""

#: inc/vendors/elementor/widgets/instagram.php:108
msgid "New window (_blank)"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:16
msgid "Apus MailChimp Sign-Up Form"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:28
msgid "MailChimp Sign-Up Form"
msgstr ""

#: inc/vendors/elementor/widgets/mailchimp.php:141
msgid "Input Background"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:16
msgid "Apus Navigation Menu"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:38
msgid "Navigation Menu"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:55
msgid "Menu"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:68
msgid "Vertical"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:69
msgid "Horizontal"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:70
msgid "Vertical Dark"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:71
msgid "Horizontal Dark"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:125
msgid "Menu Item"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:133
msgid "Menu Color"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:144
msgid "Menu Color Hover"
msgstr ""

#: inc/vendors/elementor/widgets/nav_menu.php:156
msgid "Menu Typography"
msgstr ""

#: inc/vendors/elementor/widgets/popup_video.php:16
msgid "Apus Popup Video"
msgstr ""

#: inc/vendors/elementor/widgets/popup_video.php:56
msgid "Youtube Video Link"
msgstr ""

#: inc/vendors/elementor/widgets/popup_video.php:66
msgid "Background Image"
msgstr ""

#: inc/vendors/elementor/widgets/popup_video.php:68
#: inc/vendors/elementor/widgets/process.php:97
msgid "Upload Background Image"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:14
msgid "Apus Posts"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:26
msgid "Posts"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:47
msgid "Number posts to display"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:55
msgid "Order by"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:59
#: inc/vendors/learnpress/functions-user.php:95
#: inc/vendors/learnpress/functions-user.php:154
#: woocommerce/myaccount/my-orders.php:35
#: woocommerce/myaccount/my-orders.php:54
msgid "Date"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:60
msgid "ID"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:61
msgid "Author"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:63
msgid "Modified"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:64
msgid "Random"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:65
msgid "Comment count"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:66
msgid "Menu order"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:75
msgid "Sort order"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:79
msgid "Ascending"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:80
msgid "Descending"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:112
msgid "Item Style"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:116
msgid "Grid v2"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:117
msgid "Grid v3"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:118
msgid "Grid v4"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:119
msgid "Grid v5"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:120
msgid "Grid v6"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:121
#: inc/vendors/learnpress/functions-redux-configs.php:55
msgid "List v1"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:122
#: inc/vendors/learnpress/functions-redux-configs.php:56
msgid "List v2"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:182
msgid "Post Title Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:194
msgid "Post Title Typography"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:203
msgid "Post Excerpt Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:215
msgid "Post Excerpt Typography"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:224
msgid "Post Tag Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:236
msgid "Post Tag Typography"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:245
msgid "Post Read More Color"
msgstr ""

#: inc/vendors/elementor/widgets/posts.php:257
msgid "Post Read More Typography"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:16
msgid "Apus Process"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:56
msgid "Icon Image"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:58
msgid "Upload Icon Image"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:68
msgid "Enter your proces link here"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:75
msgid "Process"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:77
msgid "Enter your Process here"
msgstr ""

#: inc/vendors/elementor/widgets/process.php:95
msgid "Background Image Space Columns"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:14
msgid "Apus Slider Revolution"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:33
msgid "No sliders found"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:38
msgid "Revslider"
msgstr ""

#: inc/vendors/elementor/widgets/revslider.php:48
msgid "Select your Revolution Slider."
msgstr ""

#: inc/vendors/elementor/widgets/scroll_up.php:16
msgid "Apus Scroll Up"
msgstr ""

#: inc/vendors/elementor/widgets/scroll_up.php:28
msgid "Scroll Up"
msgstr ""

#: inc/vendors/elementor/widgets/scroll_up.php:45
msgid "Target ID"
msgstr ""

#: inc/vendors/elementor/widgets/scroll_up.php:47
msgid "Enter target id"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:16
msgid "Apus Social Links"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:41
#: inc/vendors/elementor/widgets/social_links.php:43
#: inc/vendors/elementor/widgets/team.php:41
#: inc/vendors/elementor/widgets/team.php:43
msgid "Social Title"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:51
#: inc/vendors/elementor/widgets/team.php:51
msgid "Social Link"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:54
#: inc/vendors/elementor/widgets/team.php:54
#: inc/vendors/elementor/widgets/testimonials.php:78
msgid "Enter your social link here"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:61
#: inc/vendors/elementor/widgets/team.php:61
msgid "Social Icon"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:69
#: inc/vendors/elementor/widgets/team.php:106
msgid "Socials"
msgstr ""

#: inc/vendors/elementor/widgets/social_links.php:134
#: inc/vendors/elementor/widgets/team.php:136
msgid "Background Hover Color"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:16
msgid "Apus Teams"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:32
msgid "Team"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:68
#: inc/vendors/elementor/widgets/team.php:70
msgid "Member Name"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:77
#: inc/vendors/elementor/widgets/team.php:79
msgid "Member Job"
msgstr ""

#: inc/vendors/elementor/widgets/team.php:96
#: inc/vendors/elementor/widgets/team.php:98
msgid "Member Description"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:16
msgid "Apus Testimonials"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:58
#: inc/vendors/simple-event/functions.php:170
#: templates-education/single-course/reviews.php:81
#: templates-education/single-course/reviews.php:109
#: templates-education-v4/single-course/reviews.php:81
#: templates-education-v4/single-course/reviews.php:109
#: woocommerce/single-product-reviews.php:68
msgid "Name"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:67
#: inc/vendors/learnpress/functions-user.php:12
#: inc/vendors/learnpress/functions-user.php:164
#: inc/vendors/simple-event/functions.php:176
msgid "Job"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:76
msgid "Link To"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:86
msgid "Testimonials"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:150
msgid "Testimonial Title Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:162
msgid "Testimonial Title Typography"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:171
msgid "Content Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:183
msgid "Content Typography"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:192
msgid "Job Color"
msgstr ""

#: inc/vendors/elementor/widgets/testimonials.php:204
msgid "Job Typography"
msgstr ""

#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:16
msgid "Apus Header Woo Mini Cart"
msgstr ""

#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:36
msgid "Hide Cart"
msgstr ""

#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:105
msgid "Background Color Count"
msgstr ""

#: inc/vendors/elementor/woo_header_widgets/woo_mini_cart.php:137
#: woocommerce/cart/mini-cart-button.php:4
msgid "View your shopping cart"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:8
msgid "Course Settings"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:14
#: inc/vendors/simple-event/functions-redux-configs.php:14
#: inc/vendors/woocommerce/functions-redux-configs.php:27
msgid "Breadcrumbs Setting"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:19
#: inc/vendors/redux-framework/redux-config.php:193
#: inc/vendors/redux-framework/redux-config.php:385
#: inc/vendors/simple-event/functions-redux-configs.php:19
#: inc/vendors/woocommerce/functions-redux-configs.php:32
msgid "Breadcrumbs"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:23
#: inc/vendors/redux-framework/redux-config.php:197
#: inc/vendors/redux-framework/redux-config.php:389
#: inc/vendors/simple-event/functions-redux-configs.php:23
#: inc/vendors/woocommerce/functions-redux-configs.php:36
msgid "Breadcrumbs Background Color"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:24
#: inc/vendors/redux-framework/redux-config.php:198
#: inc/vendors/redux-framework/redux-config.php:390
#: inc/vendors/simple-event/functions-redux-configs.php:24
#: inc/vendors/woocommerce/functions-redux-configs.php:37
msgid "The breadcrumbs background color of the site."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:32
#: inc/vendors/redux-framework/redux-config.php:206
#: inc/vendors/redux-framework/redux-config.php:398
#: inc/vendors/simple-event/functions-redux-configs.php:32
#: inc/vendors/woocommerce/functions-redux-configs.php:45
msgid "Breadcrumbs Background"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:33
#: inc/vendors/redux-framework/redux-config.php:207
#: inc/vendors/redux-framework/redux-config.php:399
#: inc/vendors/simple-event/functions-redux-configs.php:33
#: inc/vendors/woocommerce/functions-redux-configs.php:46
msgid "Upload a .jpg or .png image that will be your breadcrumbs."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:39
msgid "Courses Archives"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:46
#: inc/vendors/learnpress/functions-redux-configs.php:142
#: inc/vendors/simple-event/functions-redux-configs.php:46
#: inc/vendors/simple-event/functions-redux-configs.php:141
#: inc/vendors/woocommerce/functions-redux-configs.php:14
#: inc/vendors/woocommerce/functions-redux-configs.php:59
#: inc/vendors/woocommerce/functions-redux-configs.php:166
msgid "General Setting"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:51
msgid "Courses Layout"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:52
msgid "Choose a default layout archive course."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:63
msgid "Course Columns"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:71
msgid "Number of Courses Per Page"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:82
#: inc/vendors/learnpress/functions-redux-configs.php:178
#: inc/vendors/simple-event/functions-redux-configs.php:81
#: inc/vendors/simple-event/functions-redux-configs.php:159
#: inc/vendors/woocommerce/functions-redux-configs.php:106
#: inc/vendors/woocommerce/functions-redux-configs.php:197
msgid "Sidebar Setting"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:94
msgid "Archive Course Layout"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:95
msgid "Select the layout you want to apply on your archive course page."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:98
#: inc/vendors/learnpress/functions-redux-configs.php:99
#: inc/vendors/simple-event/functions-redux-configs.php:97
#: inc/vendors/simple-event/functions-redux-configs.php:98
#: inc/vendors/simple-event/functions-redux-configs.php:169
#: inc/vendors/simple-event/functions-redux-configs.php:170
#: inc/vendors/woocommerce/functions-redux-configs.php:122
#: inc/vendors/woocommerce/functions-redux-configs.php:123
msgid "Main Content"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:118
#: inc/vendors/redux-framework/redux-config.php:250
#: inc/vendors/simple-event/functions-redux-configs.php:117
#: inc/vendors/simple-event/functions-redux-configs.php:189
#: inc/vendors/woocommerce/functions-redux-configs.php:142
msgid "Archive Left Sidebar"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:119
#: inc/vendors/redux-framework/redux-config.php:251
#: inc/vendors/redux-framework/redux-config.php:331
#: inc/vendors/simple-event/functions-redux-configs.php:118
#: inc/vendors/simple-event/functions-redux-configs.php:190
#: inc/vendors/woocommerce/functions-redux-configs.php:143
#: inc/vendors/woocommerce/functions-redux-configs.php:234
msgid "Choose a sidebar for left sidebar."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:125
#: inc/vendors/redux-framework/redux-config.php:257
#: inc/vendors/simple-event/functions-redux-configs.php:124
#: inc/vendors/simple-event/functions-redux-configs.php:196
#: inc/vendors/woocommerce/functions-redux-configs.php:149
msgid "Archive Right Sidebar"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:126
#: inc/vendors/redux-framework/redux-config.php:258
#: inc/vendors/redux-framework/redux-config.php:338
#: inc/vendors/simple-event/functions-redux-configs.php:125
#: inc/vendors/simple-event/functions-redux-configs.php:197
#: inc/vendors/woocommerce/functions-redux-configs.php:150
#: inc/vendors/woocommerce/functions-redux-configs.php:241
msgid "Choose a sidebar for right sidebar."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:135
msgid "Course Single"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:153
msgid "Course Layout"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:154
msgid "Choose a default layout single course."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:165
#: inc/vendors/redux-framework/redux-config.php:344
#: inc/vendors/simple-event/functions-redux-configs.php:152
#: inc/vendors/woocommerce/functions-redux-configs.php:177
msgid "Show Social Share"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:171
msgid "Show Course Review Tab"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:184
msgid "Single Course Sidebar Layout"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:185
msgid "Select the layout you want to apply on your Single Course Page."
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:188
#: inc/vendors/learnpress/functions-redux-configs.php:189
#: inc/vendors/redux-framework/redux-config.php:229
#: inc/vendors/redux-framework/redux-config.php:230
#: inc/vendors/redux-framework/redux-config.php:309
#: inc/vendors/redux-framework/redux-config.php:310
#: inc/vendors/simple-event/functions-redux-configs.php:174
#: inc/vendors/simple-event/functions-redux-configs.php:175
#: inc/vendors/woocommerce/functions-redux-configs.php:212
#: inc/vendors/woocommerce/functions-redux-configs.php:213
msgid "Left - Main Sidebar"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:193
#: inc/vendors/learnpress/functions-redux-configs.php:194
#: inc/vendors/redux-framework/redux-config.php:234
#: inc/vendors/redux-framework/redux-config.php:235
#: inc/vendors/redux-framework/redux-config.php:314
#: inc/vendors/redux-framework/redux-config.php:315
#: inc/vendors/simple-event/functions-redux-configs.php:179
#: inc/vendors/simple-event/functions-redux-configs.php:180
#: inc/vendors/woocommerce/functions-redux-configs.php:217
#: inc/vendors/woocommerce/functions-redux-configs.php:218
msgid "Main - Right Sidebar"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:204
msgid "Course Block Setting"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:209
msgid "Show Courses Releated"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:214
msgid "Number of related courses to show"
msgstr ""

#: inc/vendors/learnpress/functions-redux-configs.php:225
msgid "Releated Courses Columns"
msgstr ""

#: inc/vendors/learnpress/functions-review.php:181
msgid "(%d Rating)"
msgid_plural "(%d Ratings)"
msgstr[0] ""
msgstr[1] ""

#: inc/vendors/learnpress/functions-review.php:183
msgid "(%d)"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:6
msgid "Instructor Profile"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:20
msgid "Mobile"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:28
msgid "Facebook Account"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:36
msgid "Twitter Account"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:44
msgid "Google Plus Account"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:52
msgid "LinkedIn Plus Account"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:60
msgid "Youtube Account"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:72
msgid "Instructor Education"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:85
msgid "School"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:90
msgid "Ex: Education"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:100
#: inc/vendors/learnpress/functions-user.php:159
msgid "Ex: 2015 - 2019"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:110
msgid "Ex: 12 Years"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:120
msgid "Add New Education"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:123
msgid "Remove Education"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:131
msgid "Instructor Experience"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:144
msgid "Company"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:149
msgid "Ex: Google"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:169
msgid "Ex: Web Designer"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:179
msgid "Add New Experience"
msgstr ""

#: inc/vendors/learnpress/functions-user.php:182
msgid "Remove Experience"
msgstr ""

#: inc/vendors/learnpress/functions-wishlist.php:30
#: inc/vendors/learnpress/functions-wishlist.php:136
msgid "Added to wishlist"
msgstr ""

#: inc/vendors/learnpress/functions-wishlist.php:61
#: inc/vendors/learnpress/functions-wishlist.php:128
msgid "Add to wishlist"
msgstr ""

#: inc/vendors/learnpress/functions.php:167
msgid "<span>%d</span> course found"
msgid_plural "<span>%d</span> courses found"
msgstr[0] ""
msgstr[1] ""

#: inc/vendors/learnpress/functions.php:174
msgid "Newest"
msgstr ""

#: inc/vendors/learnpress/functions.php:175
msgid "Oldest"
msgstr ""

#: inc/vendors/learnpress/functions.php:431
msgid "Beginner"
msgstr ""

#: inc/vendors/learnpress/functions.php:432
msgid "Intermediate"
msgstr ""

#: inc/vendors/learnpress/functions.php:433
msgid "Advanced"
msgstr ""

#: inc/vendors/learnpress/functions.php:438
msgid "All Levels"
msgstr ""

#: inc/vendors/learnpress/functions.php:442 widgets/course-features.php:51
msgid "Language"
msgstr ""

#: inc/vendors/learnpress/functions.php:445
msgid "The language of the course."
msgstr ""

#: inc/vendors/learnpress/functions.php:448
msgid "Skill Level"
msgstr ""

#: inc/vendors/learnpress/functions.php:452
msgid "The skill level of the course."
msgstr ""

#: inc/vendors/learnpress/functions.php:455 widgets/course-features.php:59
msgid "Certificate"
msgstr ""

#: inc/vendors/learnpress/functions.php:458
msgid "Set certificate course."
msgstr ""

#: inc/vendors/learnpress/functions.php:472
msgid "Video url"
msgstr ""

#: inc/vendors/learnpress/functions.php:475
msgid "Enter youtube or vimeo video."
msgstr ""

#: inc/vendors/learnpress/functions.php:478
#: inc/vendors/learnpress/functions.php:486
msgid "More Information"
msgstr ""

#: inc/vendors/one-click-demo-import/functions.php:21
#: inc/vendors/one-click-demo-import/functions.php:38
#: inc/vendors/one-click-demo-import/functions.php:54
#: inc/vendors/one-click-demo-import/functions.php:70
msgid "Import process may take 5-10 minutes. If you facing any issues please contact our support."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:52
msgid "1 Column"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:53
msgid "2 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:54
msgid "3 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:55
msgid "4 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:56
msgid "5 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:57
msgid "6 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:58
msgid "7 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:59
msgid "8 Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:66
msgid "Preload Website"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:72
msgid "Preload Icon"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:73
msgid "Upload a .png or .gif image that will be your preload icon."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:79
msgid "Image Lazy Loading"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:85
msgid "Smooth Scroll"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:91
msgid "Google Maps API Key"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:97
msgid "General"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:103
msgid "Header"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:109
msgid "Header Settings"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:117
msgid "You can add or edit a header in <a href=\"%s\" target=\"_blank\">Headers Builder</a>"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:122
msgid "Sticky Header"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:129
msgid "Header Mobile Settings"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:134
msgid "Mobile Logo Upload"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:135
msgid "Upload a .png or .gif image that will be your logo."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:140
msgid "Search Header"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:146
msgid "Show Cart Button"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:152
msgid "Show Login/Register"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:158
msgid "Show Library Menu"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:166
msgid "Footer"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:178
msgid "Back To Top Button"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:179
msgid "Toggle whether or not to enable a back to top button on your pages."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:214
msgid "Blog & Post Archives"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:221
#: inc/vendors/redux-framework/redux-config.php:301
msgid "Select the variation you want to apply on your store."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:224
#: inc/vendors/redux-framework/redux-config.php:225
#: inc/vendors/redux-framework/redux-config.php:304
#: inc/vendors/redux-framework/redux-config.php:305
#: inc/vendors/woocommerce/functions-redux-configs.php:207
#: inc/vendors/woocommerce/functions-redux-configs.php:208
msgid "Main Only"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:265
msgid "Display Mode"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:267
msgid "Grid Layout"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:268
msgid "Grid 2 Layout"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:269
msgid "List Layout"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:276
msgid "Blog Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:283
msgid "Thumbnail Size"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:284
msgid "This featured for the site is using Visual Composer."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:285
msgid "Enter thumbnail size. Example: thumbnail, medium, large, full or other sizes defined by current theme. Alternatively enter image size in pixels: 200x100 (Width x Height) ."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:300
msgid "Archive Blog Layout"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:330
msgid "Single Blog Left Sidebar"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:337
msgid "Single Blog Right Sidebar"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:350
msgid "Show Releated Posts"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:356
msgid "Number of related posts to show"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:367
msgid "Releated Blogs Columns"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:380
msgid "404 Page"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:420
msgid "Custom Style"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:426
msgid "Custom Color"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:429
msgid "Main Theme Color"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:430
msgid "The main color of the site."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:436
msgid "Second Color"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:437
msgid "The second color of the site."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:447
msgid "Custom Typography"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:450
msgid "Main Font Face"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:451
msgid "Pick the Main Font for your site."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:467
msgid "Heading Font Face"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:468
msgid "Pick the Heading Font for your site."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:490
msgid "Social Media"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:495
msgid "Enable Facebook Share"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:501
msgid "Enable twitter Share"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:507
msgid "Enable linkedin Share"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:513
msgid "Enable tumblr Share"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:519
msgid "Enable google plus Share"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:525
msgid "Enable pinterest Share"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:533
msgid "Import / Export"
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:534
msgid "Import and Export your Redux Framework settings from file, text or URL."
msgstr ""

#: inc/vendors/redux-framework/redux-config.php:575
#: inc/vendors/redux-framework/redux-config.php:576
msgid "Theme Options"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:8
msgid "Event Settings"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:39
msgid "Events Archives"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:51
msgid "Events Layout"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:52
msgid "Choose a default layout archive event."
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:62
msgid "Event Columns"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:70
msgid "Number of Events Per Page"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:93
msgid "Archive Event Layout"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:94
msgid "Select the layout you want to apply on your archive event page."
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:134
msgid "Event Single"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:165
msgid "Single Event Sidebar Layout"
msgstr ""

#: inc/vendors/simple-event/functions-redux-configs.php:166
msgid "Select the layout you want to apply on your Single Event Page."
msgstr ""

#: inc/vendors/simple-event/functions.php:129
msgid "Phone"
msgstr ""

#: inc/vendors/simple-event/functions.php:132
msgid "e.g. 1-896-567-234"
msgstr ""

#: inc/vendors/simple-event/functions.php:135
#: template-parts/register-form.php:18
#: templates-education/single-course/reviews.php:83
#: templates-education/single-course/reviews.php:111
#: templates-education-v4/single-course/reviews.php:83
#: templates-education-v4/single-course/reviews.php:111
#: widgets/profile-info.php:26 woocommerce/single-product-reviews.php:70
msgid "Email"
msgstr ""

#: inc/vendors/simple-event/functions.php:138
msgid "e.g. <EMAIL>"
msgstr ""

#: inc/vendors/simple-event/functions.php:144
msgid "e.g. http://www.edumy.com"
msgstr ""

#: inc/vendors/simple-event/functions.php:150
msgid "Participant {#}"
msgstr ""

#: inc/vendors/simple-event/functions.php:151
msgid "Add Another Participant"
msgstr ""

#: inc/vendors/simple-event/functions.php:152
msgid "Remove Participant"
msgstr ""

#: inc/vendors/simple-event/functions.php:173
msgid "e.g. John Doe"
msgstr ""

#: inc/vendors/simple-event/functions.php:179
msgid "e.g. Web Designer"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:8
msgid "Shop Settings"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:19
msgid "Enable Shop Catalog"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:21
msgid "Enable Catalog Mode for disable Add To Cart button, Cart, Checkout"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:52
msgid "Product Archives"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:64
msgid "Products Layout"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:65
msgid "Choose a default layout archive product."
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:75
msgid "Product Columns"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:83
msgid "Number of Products Per Page"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:93
msgid "Show Quick View"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:99
msgid "Enable Swap Image"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:118
msgid "Archive Product Layout"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:119
msgid "Select the layout you want to apply on your archive product page."
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:159
msgid "Single Product"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:171
msgid "Show Product Meta"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:183
msgid "Show Product Review Tab"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:189
msgid "Hidden Product Additional Information Tab"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:203
msgid "Single Product Sidebar Layout"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:204
msgid "Select the layout you want to apply on your Single Product Page."
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:233
msgid "Single Product Left Sidebar"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:240
msgid "Single Product Right Sidebar"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:248
msgid "Product Block Setting"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:253
msgid "Show Products Releated"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:258
msgid "Number of related products to show"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:269
msgid "Releated Products Columns"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:278
msgid "Show Products upsells"
msgstr ""

#: inc/vendors/woocommerce/functions-redux-configs.php:284
msgid "Upsells Products Columns"
msgstr ""

#: inc/vendors/woocommerce/functions.php:174
msgid "Unable to find any products that match the currenty query"
msgstr ""

#: inc/vendors/woocommerce/functions.php:176
msgid "View More"
msgstr ""

#: inc/vendors/woocommerce/functions.php:177
msgid "View Less"
msgstr ""

#: inc/vendors/woocommerce/functions.php:222
#: inc/vendors/woocommerce/functions.php:226
msgid "Shop"
msgstr ""

#: inc/vendors/woocommerce/functions.php:412
#: inc/vendors/woocommerce/functions.php:452
msgid "Placeholder"
msgstr ""

#: inc/vendors/woocommerce/functions.php:594
msgid "SOLD OUT"
msgstr ""

#: inc/widgets/course-features.php:7
msgid "Single Course:: Features"
msgstr ""

#: inc/widgets/course-features.php:8
msgid "Show list of course features"
msgstr ""

#: inc/widgets/course-features.php:29 inc/widgets/course-info.php:29
#: inc/widgets/course-tags.php:29 inc/widgets/custom_menu.php:46
#: inc/widgets/event-contact.php:29 inc/widgets/event-detail.php:29
#: inc/widgets/event-tags.php:29 inc/widgets/filter-category.php:29
#: inc/widgets/filter-instructor.php:29 inc/widgets/filter-level.php:29
#: inc/widgets/filter-price.php:29 inc/widgets/filter-rating.php:29
#: inc/widgets/profile-info.php:29 inc/widgets/profile-instructor.php:29
#: inc/widgets/recent_post.php:32 inc/widgets/search.php:30
#: inc/widgets/socials.php:37
msgid "Title:"
msgstr ""

#: inc/widgets/course-info.php:7
msgid "Single Course:: Information"
msgstr ""

#: inc/widgets/course-info.php:8
msgid "Show list of course information"
msgstr ""

#: inc/widgets/course-tags.php:7
msgid "Single Course:: Tags"
msgstr ""

#: inc/widgets/course-tags.php:8
msgid "Show list of course tags"
msgstr ""

#: inc/widgets/custom_menu.php:7
msgid "Apus Custom Menu Widget"
msgstr ""

#: inc/widgets/custom_menu.php:8
msgid "Show custom menu"
msgstr ""

#: inc/widgets/custom_menu.php:51
msgid "Menu:"
msgstr ""

#: inc/widgets/custom_menu.php:63
msgid "Style:"
msgstr ""

#: inc/widgets/event-contact.php:7
msgid "Single Event:: Contact"
msgstr ""

#: inc/widgets/event-contact.php:8
msgid "Show list of event contact"
msgstr ""

#: inc/widgets/event-detail.php:7
msgid "Single Event:: Detail"
msgstr ""

#: inc/widgets/event-detail.php:8
msgid "Show list of event detail"
msgstr ""

#: inc/widgets/event-tags.php:7
msgid "Single Event:: Tags"
msgstr ""

#: inc/widgets/event-tags.php:8
msgid "Show list of event tags"
msgstr ""

#: inc/widgets/filter-category.php:7
msgid "Courses:: Filter Categories"
msgstr ""

#: inc/widgets/filter-category.php:8
msgid "Show list of course filter category"
msgstr ""

#: inc/widgets/filter-instructor.php:7
msgid "Courses:: Filter Instructors"
msgstr ""

#: inc/widgets/filter-instructor.php:8
msgid "Show list of course filter instructor"
msgstr ""

#: inc/widgets/filter-level.php:7
msgid "Courses:: Filter Levels"
msgstr ""

#: inc/widgets/filter-level.php:8
msgid "Show list of course filter level"
msgstr ""

#: inc/widgets/filter-price.php:7
msgid "Courses:: Filter Prices"
msgstr ""

#: inc/widgets/filter-price.php:8
msgid "Show list of course filter price"
msgstr ""

#: inc/widgets/filter-rating.php:7
msgid "Courses:: Filter Ratings"
msgstr ""

#: inc/widgets/filter-rating.php:8
msgid "Show list of course filter rating"
msgstr ""

#: inc/widgets/profile-info.php:7
msgid "Profile:: Information"
msgstr ""

#: inc/widgets/profile-info.php:8
msgid "Show list of profile information"
msgstr ""

#: inc/widgets/profile-instructor.php:7
msgid "Profile:: Instructor Info"
msgstr ""

#: inc/widgets/profile-instructor.php:8
msgid "Show list of profile instructor information"
msgstr ""

#: inc/widgets/recent_post.php:7
msgid "Apus Recent Posts Widget"
msgstr ""

#: inc/widgets/recent_post.php:8
msgid "Show list of recent post"
msgstr ""

#: inc/widgets/recent_post.php:37 inc/widgets/search.php:35
msgid "Type:"
msgstr ""

#: inc/widgets/recent_post.php:49
msgid "Num Posts:"
msgstr ""

#: inc/widgets/search.php:7
msgid "Apus Search Widget"
msgstr ""

#: inc/widgets/search.php:8
msgid "Show search form in sidebar"
msgstr ""

#: inc/widgets/socials.php:7
msgid "Apus Socials"
msgstr ""

#: inc/widgets/socials.php:8
msgid "Socials for website."
msgstr ""

#: inc/widgets/socials.php:41
msgid "Select socials:"
msgstr ""

#: list-comments.php:21
msgid "%1$s"
msgstr ""

#: list-comments.php:24 woocommerce/myaccount/my-address.php:47
msgid "Edit"
msgstr ""

#: list-comments.php:25
msgid " Reply"
msgstr ""

#: list-comments.php:30
msgid "Your comment is awaiting moderation."
msgstr ""

#: page-profile.php:43 page.php:53 template-posts/single/inner.php:54
msgid "Pages:"
msgstr ""

#: page-profile.php:47 page.php:57 template-posts/single/inner.php:58
#: templates-event/single-simple_event.php:89
msgid "Page"
msgstr ""

#: searchform.php:13 widgets/search.php:15
msgid "Search"
msgstr ""

#: template-parts/login-form.php:5
msgid "Signin"
msgstr ""

#: template-parts/login-form.php:8
msgid "Username Or Email"
msgstr ""

#: template-parts/login-form.php:10
msgid "Enter username or email"
msgstr ""

#: template-parts/login-form.php:13 template-parts/register-form.php:23
#: woocommerce/myaccount/form-login.php:42
#: woocommerce/myaccount/form-login.php:99
msgid "Password"
msgstr ""

#: template-parts/login-form.php:15 template-parts/register-form.php:25
msgid "Enter Password"
msgstr ""

#: template-parts/login-form.php:22
msgid "Keep me signed in"
msgstr ""

#: template-parts/login-form.php:28
msgid "Forgot Password"
msgstr ""

#: template-parts/login-form.php:28
msgid "Lost Your Password?"
msgstr ""

#: template-parts/login-form.php:45
msgid "Reset Password"
msgstr ""

#: template-parts/login-form.php:49 template-parts/login-form.php:51
msgid "Username or E-mail"
msgstr ""

#: template-parts/login-form.php:58
msgid "Get New Password"
msgstr ""

#: template-parts/login-form.php:59 woocommerce/myaccount/my-orders.php:77
msgid "Cancel"
msgstr ""

#: template-parts/login-form.php:62
msgid "Back To Login"
msgstr ""

#: template-parts/login-form.php:68
msgid "Don't have an account"
msgstr ""

#: template-parts/posts-releated.php:32
msgid "Related Posts"
msgstr ""

#: template-parts/register-form.php:7
msgid "Please wait ..."
msgstr ""

#: template-parts/register-form.php:13 woocommerce/myaccount/form-login.php:85
msgid "Username"
msgstr ""

#: template-parts/register-form.php:15
msgid "Enter Username"
msgstr ""

#: template-parts/register-form.php:20
msgid "Enter Email"
msgstr ""

#: template-parts/register-form.php:28 template-parts/register-form.php:30
msgid "Confirm Password"
msgstr ""

#: template-parts/register-form.php:37
msgid "Register now"
msgstr ""

#: template-parts/register-form.php:47
msgid "Already have an account?"
msgstr ""

#: template-parts/searchform.php:7
msgid "Search course..."
msgstr ""

#: template-parts/sharebox.php:8
msgid "Share Link:"
msgstr ""

#: template-parts/sharebox.php:11
msgid "Share on facebook"
msgstr ""

#: template-parts/sharebox.php:18
msgid "Share on Twitter"
msgstr ""

#: template-parts/sharebox.php:25
msgid "Share on LinkedIn"
msgstr ""

#: template-parts/sharebox.php:32
msgid "Share on Tumblr"
msgstr ""

#: template-parts/sharebox.php:40
msgid "Share on Google plus"
msgstr ""

#: template-parts/sharebox.php:48
msgid "Share on Pinterest"
msgstr ""

#: template-posts/content-none.php:13
msgid "Oops!"
msgstr ""

#: template-posts/content-none.php:13
msgid "Sorry, but your search returned no results!"
msgstr ""

#: template-posts/content-none.php:15
msgid "Try again please, use the search form below."
msgstr ""

#: template-posts/loop/inner-list.php:51
msgid "Read more"
msgstr ""

#: templates-education/addons/co-instructors/single-course-tab.php:55
#: templates-education/single-course/tabs/instructor.php:55
#: templates-education-v4/addons/co-instructors/single-course-tab.php:55
#: templates-education-v4/single-course/tabs/instructor.php:55
msgid "%d Review"
msgid_plural "%d Reviews"
msgstr[0] ""
msgstr[1] ""

#: templates-education/addons/co-instructors/single-course-tab.php:59
#: templates-education/single-course/tabs/instructor.php:59
#: templates-education-v4/addons/co-instructors/single-course-tab.php:59
#: templates-education-v4/single-course/tabs/instructor.php:59
msgid "%d Student"
msgid_plural "%d Students"
msgstr[0] ""
msgstr[1] ""

#: templates-education/addons/co-instructors/single-course-tab.php:63
#: templates-education/single-course/tabs/instructor.php:63
#: templates-education-v4/addons/co-instructors/single-course-tab.php:63
#: templates-education-v4/single-course/tabs/instructor.php:63
msgid "%d Course"
msgid_plural "%d Courses"
msgstr[0] ""
msgstr[1] ""

#: templates-education/content-archive-course.php:91
#: templates-education-v4/content-archive-course.php:91
msgid "No course found."
msgstr ""

#: templates-education/content-course-list-wislist.php:37
#: templates-education-v4/content-course-list-wislist.php:37
msgid "Remove"
msgstr ""

#: templates-education/content-course-list.php:37
#: templates-education/content-course.php:38
#: templates-education-v4/content-course-list.php:37
#: templates-education-v4/content-course.php:38
msgid "Preview Course"
msgstr ""

#: templates-education/content-instructor-2.php:56
#: templates-education-v4/content-instructor-2.php:56
msgid "%d student"
msgid_plural "%d students"
msgstr[0] ""
msgstr[1] ""

#: templates-education/content-instructor-2.php:57
#: templates-education-v4/content-instructor-2.php:57
msgid "%d course"
msgid_plural "%d courses"
msgstr[0] ""
msgstr[1] ""

#: templates-education/profile/user-bio.php:24
#: templates-education-v4/profile/dashboard/general-statistic.php:81
#: templates-education-v4/profile/user-bio.php:24
msgid "Education"
msgstr ""

#: templates-education/profile/user-bio.php:47
#: templates-education-v4/profile/dashboard/general-statistic.php:104
#: templates-education-v4/profile/user-bio.php:47
msgid "Experience"
msgstr ""

#: templates-education/single-course/courses-releated.php:38
#: templates-education-v4/single-course/courses-releated.php:38
msgid "Related Courses"
msgstr ""

#: templates-education/single-course/header.php:20
#: templates-education-v4/single-course/header.php:21
msgid "Last updated %s"
msgstr ""

#: templates-education/single-course/header.php:26
#: templates-education-v4/single-course/header.php:27
#: templates-event/single-simple_event.php:47
msgid "Share"
msgstr ""

#: templates-education/single-course/instructor.php:23
#: templates-education-v4/single-course/instructor.php:23
msgid "About the Instructor"
msgstr ""

#: templates-education/single-course/review.php:23
#: templates-education-v4/single-course/review.php:23
msgid "Your comment is awaiting approval"
msgstr ""

#: templates-education/single-course/review.php:31
#: templates-education-v4/single-course/review.php:31
msgid "Rated %d out of 5"
msgstr ""

#: templates-education/single-course/review.php:41
#: templates-education-v4/single-course/review.php:41
msgid "Reply"
msgstr ""

#: templates-education/single-course/reviews.php:19
#: templates-education-v4/single-course/reviews.php:19
#: widgets/profile-instructor.php:41
msgid "Reviews"
msgstr ""

#: templates-education/single-course/reviews.php:27
#: templates-education-v4/single-course/reviews.php:27
msgid "%s stars"
msgstr ""

#: templates-education/single-course/reviews.php:45
#: templates-education-v4/single-course/reviews.php:45
msgid "%1$s rating"
msgid_plural "%1$s ratings"
msgstr[0] ""
msgstr[1] ""

#: templates-education/single-course/reviews.php:45
#: templates-education-v4/single-course/reviews.php:45
msgid "0 rating"
msgstr ""

#: templates-education/single-course/reviews.php:76
#: templates-education-v4/single-course/reviews.php:76
msgid "Reply comment"
msgstr ""

#: templates-education/single-course/reviews.php:77
#: templates-education/single-course/reviews.php:105
#: templates-education-v4/single-course/reviews.php:77
#: templates-education-v4/single-course/reviews.php:105
#: woocommerce/single-product-reviews.php:63
msgid "Leave a Reply to %s"
msgstr ""

#: templates-education/single-course/reviews.php:82
#: templates-education/single-course/reviews.php:110
#: templates-education-v4/single-course/reviews.php:82
#: templates-education-v4/single-course/reviews.php:110
msgid "Your Name"
msgstr ""

#: templates-education/single-course/reviews.php:84
#: templates-education/single-course/reviews.php:112
#: templates-education-v4/single-course/reviews.php:84
#: templates-education-v4/single-course/reviews.php:112
msgid "<EMAIL>"
msgstr ""

#: templates-education/single-course/reviews.php:86
#: templates-education/single-course/reviews.php:114
#: templates-education-v4/single-course/reviews.php:86
#: templates-education-v4/single-course/reviews.php:114
msgid "Your Website"
msgstr ""

#: templates-education/single-course/reviews.php:89
#: templates-education-v4/single-course/reviews.php:89
msgid "Submit"
msgstr ""

#: templates-education/single-course/reviews.php:94
#: templates-education-v4/single-course/reviews.php:94
msgid "Write Comment"
msgstr ""

#: templates-education/single-course/reviews.php:96
#: templates-education-v4/single-course/reviews.php:96
msgid "You must be logged in to reply this review."
msgstr ""

#: templates-education/single-course/reviews.php:104
#: templates-education-v4/single-course/reviews.php:104
#: woocommerce/single-product-reviews.php:62
msgid "Add a review"
msgstr ""

#: templates-education/single-course/reviews.php:104
#: templates-education-v4/single-course/reviews.php:104
#: woocommerce/single-product-reviews.php:62
msgid "Be the first to review &ldquo;%s&rdquo;"
msgstr ""

#: templates-education/single-course/reviews.php:117
#: templates-education-v4/single-course/reviews.php:117
msgid "Submit Review"
msgstr ""

#: templates-education/single-course/reviews.php:123
#: templates-education-v4/single-course/reviews.php:123
msgid "You must be logged in to post a review."
msgstr ""

#: templates-education/single-course/reviews.php:128
#: templates-education-v4/single-course/reviews.php:128
msgid "What is it like to Course?"
msgstr ""

#: templates-education/single-course/reviews.php:149
#: templates-education-v4/single-course/reviews.php:149
msgid "Review"
msgstr ""

#: templates-education/single-course/reviews.php:149
#: templates-education-v4/single-course/reviews.php:149
#: woocommerce/single-product-reviews.php:93
msgid "Write Review"
msgstr ""

#: templates-education-v4/courses-top-bar.php:22
msgid "Search courses..."
msgstr ""

#: templates-education-v4/pages/profile.php:50
msgid "This user does not public their profile."
msgstr ""

#: templates-education-v4/profile/dashboard/general-statistic.php:28
msgid "Enrolled Courses"
msgstr ""

#: templates-education-v4/profile/dashboard/general-statistic.php:32
msgid "Active Courses"
msgstr ""

#: templates-education-v4/profile/dashboard/general-statistic.php:36
msgid "Completed Courses"
msgstr ""

#: templates-education-v4/profile/dashboard/general-statistic.php:51
msgid "Total Courses"
msgstr ""

#: templates-education-v4/profile/dashboard/general-statistic.php:55
msgid "Total Students"
msgstr ""

#: templates-education-v4/single-course/tabs/tabs.php:44
msgid "You finished this course. This course has been blocked"
msgstr ""

#: templates-education-v4/single-course/tabs/tabs.php:49
msgid "This course has been blocked reason by expire"
msgstr ""

#: templates-event/loop/inner-list-small.php:31
#: templates-event/loop/inner-list.php:56 widgets/event-detail.php:51
msgid "Time:"
msgstr ""

#: templates-event/loop/inner-list-small.php:38
#: templates-event/loop/inner-list.php:63 widgets/event-detail.php:62
msgid "Address:"
msgstr ""

#: templates-event/loop/inner-list.php:41 widgets/event-detail.php:33
#: woocommerce/checkout/thankyou.php:44
msgid "Date:"
msgstr ""

#: templates-event/single-simple_event.php:53
msgid "Event Participants"
msgstr ""

#: templates-event/single-simple_event.php:87
msgid "Previous page"
msgstr ""

#: templates-event/single-simple_event.php:88
msgid "Next page"
msgstr ""

#: widgets/course-features.php:26
msgid "Duration"
msgstr ""

#: widgets/course-features.php:31
msgid "Lessons"
msgstr ""

#: widgets/course-features.php:36
msgid "Quizzes"
msgstr ""

#: widgets/course-features.php:41
msgid "Preview Lessons"
msgstr ""

#: widgets/course-features.php:46
msgid "Maximum Students"
msgstr ""

#: widgets/course-features.php:55
msgid "Skill level"
msgstr ""

#: widgets/filter-price.php:21
msgid "Free"
msgstr ""

#: widgets/filter-price.php:25
msgid "Paid"
msgstr ""

#: widgets/popup-newsletter.php:4
msgid "Close (Esc)"
msgstr ""

#: widgets/popup-newsletter.php:23
msgid "Don't show this popup again"
msgstr ""

#: widgets/profile-info.php:20
msgid "Phone Number"
msgstr ""

#: widgets/profile-info.php:39
msgid "Socials Media"
msgstr ""

#: widgets/profile-instructor.php:33
msgid "Total students"
msgstr ""

#: woocommerce/cart/cart.php:30
msgid "Product Name"
msgstr ""

#: woocommerce/cart/cart.php:32 woocommerce/cart/cart.php:89
#: woocommerce/global/quantity-input.php:32
msgid "Quantity"
msgstr ""

#: woocommerce/cart/cart.php:33 woocommerce/cart/cart.php:106
#: woocommerce/cart/mini-cart.php:88 woocommerce/myaccount/my-orders.php:37
#: woocommerce/myaccount/my-orders.php:60
msgid "Total"
msgstr ""

#: woocommerce/cart/cart.php:63
msgid "Product"
msgstr ""

#: woocommerce/cart/cart.php:78
msgid "Available on backorder"
msgstr ""

#: woocommerce/cart/cart.php:117 woocommerce/cart/mini-cart.php:65
msgid "Remove this item"
msgstr ""

#: woocommerce/cart/cart.php:136
msgid "Coupon:"
msgstr ""

#: woocommerce/cart/cart.php:136
msgid "Coupon code"
msgstr ""

#: woocommerce/cart/cart.php:136
msgid "Apply coupon"
msgstr ""

#: woocommerce/cart/cart.php:141
msgid "Update cart"
msgstr ""

#: woocommerce/cart/cross-sells.php:40
msgid "You may be interested in&hellip;"
msgstr ""

#: woocommerce/cart/mini-cart-content.php:5
msgid "My Cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:79
msgid "Currently Empty"
msgstr ""

#: woocommerce/cart/mini-cart.php:81
msgid "Continue shopping"
msgstr ""

#: woocommerce/cart/mini-cart.php:93
msgid "View Cart"
msgstr ""

#: woocommerce/cart/mini-cart.php:94
msgid "Checkout"
msgstr ""

#: woocommerce/cart/proceed-to-checkout-button.php:26
msgid "Proceed to Checkout"
msgstr ""

#: woocommerce/checkout/form-billing.php:24
msgid "Billing &amp; Shipping"
msgstr ""

#: woocommerce/checkout/form-billing.php:28
msgid "Billing details"
msgstr ""

#: woocommerce/checkout/form-billing.php:53
msgid "Create an account?"
msgstr ""

#: woocommerce/checkout/form-checkout.php:20
msgid "You must be logged in to checkout."
msgstr ""

#: woocommerce/checkout/form-checkout.php:50
msgid "Your order"
msgstr ""

#: woocommerce/checkout/payment.php:35
msgid "Sorry, it seems that there are no available payment methods for your state. Please contact us if you require assistance or wish to make alternate arrangements."
msgstr ""

#: woocommerce/checkout/payment.php:35
msgid "Please fill in your details above to see available payment methods."
msgstr ""

#. translators: $1 and $2 opening and closing emphasis tags respectively

#: woocommerce/checkout/payment.php:44
msgid "Since your browser does not support JavaScript, or it is disabled, please ensure you click the %1$sUpdate Totals%2$s button before placing your order. You may be charged more than the amount stated above if you fail to do so."
msgstr ""

#: woocommerce/checkout/payment.php:46
msgid "Update totals"
msgstr ""

#: woocommerce/checkout/thankyou.php:25
msgid "Unfortunately your order cannot be processed as the originating bank/merchant has declined your transaction. Please attempt your purchase again."
msgstr ""

#: woocommerce/checkout/thankyou.php:28 woocommerce/myaccount/my-orders.php:70
msgid "Pay"
msgstr ""

#: woocommerce/checkout/thankyou.php:30
msgid "My Account"
msgstr ""

#: woocommerce/checkout/thankyou.php:36 woocommerce/checkout/thankyou.php:74
msgid "Thank you. Your order has been received."
msgstr ""

#: woocommerce/checkout/thankyou.php:40
msgid "Order Number:"
msgstr ""

#: woocommerce/checkout/thankyou.php:49
msgid "Email:"
msgstr ""

#: woocommerce/checkout/thankyou.php:54
msgid "Total:"
msgstr ""

#: woocommerce/checkout/thankyou.php:59
msgid "Payment Method:"
msgstr ""

#: woocommerce/checkout/thankyou.php:78
msgid "COUNTINUE SHOPPING"
msgstr ""

#. translators: %s: Quantity.

#: woocommerce/global/quantity-input.php:28
msgid "%s quantity"
msgstr ""

#: woocommerce/global/quantity-input.php:43
msgctxt "Product quantity input tooltip"
msgid "Qty"
msgstr ""

#: woocommerce/myaccount/form-add-payment-method.php:57
msgid "Add payment method"
msgstr ""

#: woocommerce/myaccount/form-add-payment-method.php:63
msgid "New payment methods can only be added during checkout. Please contact us if you require assistance."
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:21
msgid "First name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:25
msgid "Last name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:30
msgid "Display name"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:31
msgid "This will be how your name will be displayed in the account section and in reviews"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:34
#: woocommerce/myaccount/form-login.php:92
msgid "Email address"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:38
msgid "Password Change"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:40
msgid "Current Password (leave blank to leave unchanged)"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:44
msgid "New Password (leave blank to leave unchanged)"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:48
msgid "Confirm New Password"
msgstr ""

#: woocommerce/myaccount/form-edit-account.php:56
msgid "Save changes"
msgstr ""

#: woocommerce/myaccount/form-login.php:38
msgid "Username or email address"
msgstr ""

#: woocommerce/myaccount/form-login.php:52
msgid "Remember me"
msgstr ""

#: woocommerce/myaccount/form-login.php:55
msgid "Lost your password?"
msgstr ""

#: woocommerce/myaccount/form-login.php:58
msgid "sign in"
msgstr ""

#: woocommerce/myaccount/form-login.php:67
msgid "OR Create an account"
msgstr ""

#: woocommerce/myaccount/form-login.php:105
msgid "A password will be sent to your email address."
msgstr ""

#: woocommerce/myaccount/form-login.php:122
msgid "OR LOGIN"
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:25
msgid "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email."
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:28
msgid "Username or email"
msgstr ""

#: woocommerce/myaccount/form-lost-password.php:38
msgid "Reset password"
msgstr ""

#: woocommerce/myaccount/my-address.php:17
msgid "My Addresses"
msgstr ""

#: woocommerce/myaccount/my-address.php:19
#: woocommerce/myaccount/my-address.php:25
msgid "Billing Address"
msgstr ""

#: woocommerce/myaccount/my-address.php:20
msgid "Shipping Address"
msgstr ""

#: woocommerce/myaccount/my-address.php:23
msgid "My Address"
msgstr ""

#: woocommerce/myaccount/my-address.php:35
msgid "The following addresses will be used on the checkout page by default."
msgstr ""

#: woocommerce/myaccount/my-address.php:67
msgid "You have not set up this type of address yet."
msgstr ""

#: woocommerce/myaccount/my-orders.php:26
msgid "Recent Orders"
msgstr ""

#: woocommerce/myaccount/my-orders.php:34
msgid "Order"
msgstr ""

#: woocommerce/myaccount/my-orders.php:49
msgid "Order Number"
msgstr ""

#: woocommerce/myaccount/my-orders.php:61
msgid "%s for %s item"
msgid_plural "%s for %s items"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/meta.php:35
msgid "SKU:"
msgstr ""

#: woocommerce/single-product/meta.php:35
msgid "N/A"
msgstr ""

#: woocommerce/single-product/meta.php:39
msgid "Category:"
msgid_plural "Categories:"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/meta.php:41
msgid "<span class=\"sub_title\">Tag:</span>"
msgid_plural "<span class=\"sub_title\">Tags:</span>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce/single-product/product-image-carousel.php:38
msgid "Watch video"
msgstr ""

#: woocommerce/single-product/product-image-carousel.php:52
#: woocommerce/single-product/product-image.php:36
msgid "Awaiting product image"
msgstr ""

#: woocommerce/single-product/related.php:45
msgid "Related Products"
msgstr ""

#: woocommerce/single-product/up-sells.php:42
msgid "Up-Sells Products"
msgstr ""

#: woocommerce/single-product-reviews.php:48
msgid "There are no reviews yet."
msgstr ""

#: woocommerce/single-product-reviews.php:72
msgid "submit review"
msgstr ""

#: woocommerce/single-product-reviews.php:78
msgid "You must be <a href=\"%s\">logged in</a> to post a review."
msgstr ""

#: woocommerce/single-product-reviews.php:83
msgid "Your Rating"
msgstr ""

#: woocommerce/single-product-reviews.php:84
msgid "Rate&hellip;"
msgstr ""

#: woocommerce/single-product-reviews.php:85
msgid "Perfect"
msgstr ""

#: woocommerce/single-product-reviews.php:86
msgid "Good"
msgstr ""

#: woocommerce/single-product-reviews.php:87
msgid "Average"
msgstr ""

#: woocommerce/single-product-reviews.php:88
msgid "Not that bad"
msgstr ""

#: woocommerce/single-product-reviews.php:89
msgid "Very Poor"
msgstr ""

#: woocommerce/single-product-reviews.php:102
msgid "Only logged in customers who have purchased this product may leave a review."
msgstr ""
#. Theme Name of the plugin/theme
msgid "Edumy"
msgstr ""

#. Theme URI of the plugin/theme
msgid "https://apusthemes.com/edumy"
msgstr ""

#. Description of the plugin/theme
msgid "Edumy – Education WordPress Theme For education, school, college, institute, university, online learning, training and kindergarten is a premium WordPress theme"
msgstr ""

#. Author of the plugin/theme
msgid "ApusTheme"
msgstr ""

#. Author URI of the plugin/theme
msgid "https://apusthemes.com/"
msgstr ""

#. Template Name of the plugin/theme
msgid "404 Page"
msgstr ""

#. Template Name of the plugin/theme
msgid "Page Profile template"
msgstr ""

#. Template Name of the plugin/theme
msgid "Page Default"
msgstr ""
