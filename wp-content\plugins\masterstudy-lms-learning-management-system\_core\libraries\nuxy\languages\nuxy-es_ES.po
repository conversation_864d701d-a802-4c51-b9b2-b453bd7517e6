msgid ""
msgstr ""
"Project-Id-Version: NUXY\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-23 06:46+0000\n"
"PO-Revision-Date: 2024-12-23 13:13+0000\n"
"Last-Translator: \n"
"Language-Team: Spanish (Spain)\n"
"Language: es_ES\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.1; wp-5.9.3\n"
"X-Domain: nuxy"

#: taxonomy_meta/fields/image.php:10
msgid "Add image"
msgstr "Añadir imagen"

#: metaboxes/metabox.php:248
msgid "Backup Font Family"
msgstr "Familia de fuentes de copia de seguridad"

#: metaboxes/google_fonts.php:22
msgid "Black 900"
msgstr "Negro 900"

#: metaboxes/google_fonts.php:23
msgid "Black 900 italic"
msgstr "Negro 900 cursiva"

#: metaboxes/google_fonts.php:20
msgid "Bold 700"
msgstr "Negrita 700"

#: metaboxes/google_fonts.php:21
msgid "Bold 700 italic"
msgstr "Negrita 700 cursiva"

#: metaboxes/google_fonts.php:53
msgid "Capitalize"
msgstr "Capitalizar"

#: metaboxes/google_fonts.php:43
msgid "Center"
msgstr "Centro"

#: settings/settings.php:94
msgid "Choose Page"
msgstr "Elegir página"

#: metaboxes/metabox.php:42
msgid "Choose User"
msgstr "Elija usuario"

#: metaboxes/fields/text.php:28
msgid "Copied"
msgstr "Copiado"

#: metaboxes/metabox.php:254
msgid "Copy settings"
msgstr "Copiar configuración"

#: metaboxes/metabox.php:258
msgid "Couldn't copy settings"
msgstr "No se pudo copiar la configuración"

#: metaboxes/google_fonts.php:29
msgid "Cyrillic"
msgstr "Cirílico"

#: metaboxes/google_fonts.php:30
msgid "Cyrillic ext"
msgstr "Extensión cirílica"

#: metaboxes/fields/duration.php:35
msgid "Days"
msgstr "Días"

#: metaboxes/google_fonts.php:41
msgid "Default"
msgstr "Por defecto"

#: metaboxes/metabox.php:301
msgid "Delete"
msgstr "Borrar"

#: taxonomy_meta/fields/image.php:13
msgid "Delete image"
msgstr "Eliminar imagen"

#: metaboxes/metabox.php:266
msgid ""
"Download and store Google Fonts locally. Set the fonts in the typography."
msgstr ""
"Descargue y guarde las fuentes de Google de forma local. Configure las "
"fuentes en la tipografía."

#: metaboxes/metabox.php:265
msgid "Download Google Fonts"
msgstr ""
"\n"
"21 / 5 000\n"
"Descargar fuentes de Google"

#: metaboxes/fields/duration.php:24
msgid "duration"
msgstr "duración"

#: metaboxes/fields/repeater.php:25 metaboxes/fields/textarea.php:25
#: metaboxes/fields/text.php:27
msgid "Enter"
msgstr "Introducir"

#: metaboxes/fields/number.php:22
msgid "Enter numbers..."
msgstr "Introducir números..."

#: helpers/file_upload.php:21
msgid "Error occurred, please try again"
msgstr "Error, inténtelo de nuevo"

#: metaboxes/metabox.php:259
msgid "Export options"
msgstr "Opciones de exportación"

#: metaboxes/metabox.php:252
msgid "Font Color"
msgstr "Color de fuente"

#: metaboxes/metabox.php:247
msgid "Font Family"
msgstr "Familia de fuentes"

#: metaboxes/metabox.php:243
msgid "Font size"
msgstr "Tamaño de fuente"

#: metaboxes/metabox.php:250
msgid "Font Subsets"
msgstr "Subconjuntos de fuentes"

#: metaboxes/metabox.php:263
msgid "Font Synchronize"
msgstr "Sincronizar fuente"

#: metaboxes/metabox.php:249
msgid "Font Weignt & Style"
msgstr "Fuente Weignt & Style"

#: metaboxes/google_fonts.php:31
msgid "Greek"
msgstr "Griego"

#: metaboxes/google_fonts.php:32
msgid "Greek ext"
msgstr "Extensión griega"

#: metaboxes/fields/duration.php:34
msgid "Hours"
msgstr "Horas"

#. Author URI of the plugin
msgid "https://stylemixthemes.com"
msgstr "https://stylemixthemes.com"

#: metaboxes/fields/image.php:24
msgid "Image URL"
msgstr "URL de la imagen"

#: metaboxes/metabox.php:260
msgid "Import options"
msgstr "Opciones de importación"

#: metaboxes/metabox.php:255
msgid "Import settings"
msgstr "Configuración de importación"

#: metaboxes/metabox-display.php:27
msgid "Import/Export"
msgstr "Importación/Exportación"

#: helpers/file_upload.php:65
msgid "Invalid file extension"
msgstr "Extensión de archivo no válida"

#: metaboxes/google_fonts.php:33
msgid "Latin"
msgstr "Latino"

#: metaboxes/google_fonts.php:34
msgid "Latin ext"
msgstr "Ext latino"

#: metaboxes/google_fonts.php:42
msgid "Left"
msgstr "Izquierda"

#: metaboxes/metabox.php:246
msgid "Letter spacing"
msgstr "Espaciado de letras"

#: metaboxes/google_fonts.php:14
msgid "Light 300"
msgstr "Luz 300"

#: metaboxes/google_fonts.php:15
msgid "Light 300 italic"
msgstr "Luz 300 cursiva"

#: metaboxes/metabox.php:244
msgid "Line height"
msgstr "Altura de línea"

#: metaboxes/google_fonts.php:52
msgid "Lowercase"
msgstr "Minúsculos"

#: metaboxes/google_fonts.php:18
msgid "Medium 500"
msgstr "Mediano 500"

#: metaboxes/google_fonts.php:19
msgid "Medium 500 italic"
msgstr "Cursiva mediana 500"

#: metaboxes/fields/duration.php:33
msgid "Minutes"
msgstr "Minutos"

#: metaboxes/google_fonts.php:50
msgid "Normal"
msgstr "Normal"

#. Name of the plugin
msgid "NUXY"
msgstr "NUXY"

#: settings/settings.php:162
msgid "Oops, something went wrong"
msgstr "Ups, algo salió mal"

#: helpers/file_upload.php:53
msgid "Please, select file"
msgstr "Por favor, seleccione el archivo"

#: metaboxes/metabox.php:302
msgid "Preview"
msgstr "Avance"

#: metaboxes/google_fonts.php:16
msgid "Regular 400"
msgstr "Regular 400"

#: metaboxes/google_fonts.php:17
msgid "Regular 400 italic"
msgstr "Regular 400 cursiva"

#: metaboxes/fields/image.php:27
msgid "Remove"
msgstr "Eliminar"

#: metaboxes/fields/image.php:26
msgid "Replace"
msgstr "Reemplazar"

#: metaboxes/google_fonts.php:44
msgid "Right"
msgstr "Bien"

#: settings/view/header.php:82
msgid "Save Settings"
msgstr "Guardar configuración"

#: settings/settings.php:158
msgid "Saved!"
msgstr "¡Salvado!"

#: settings/view/header.php:47
msgid "Search"
msgstr "Buscar"

#: taxonomy_meta/fields/image.php:45
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr "Seleccione o cargue medios de su persuasión elegida"

#: settings/settings.php:159
msgid "Settings are changed"
msgstr "Se han cambiado las configuraciones"

#: settings/settings.php:163
msgid "Settings are not changed"
msgstr "Los ajustes no se modifican"

#: metaboxes/metabox.php:257
msgid "Settings copied to buffer"
msgstr "Configuración copiada en el búfer"

#: metaboxes/metabox.php:261
msgid "Sorry, no matching options."
msgstr "Lo sentimos, no hay opciones de coincidencia."

#. Author of the plugin
msgid "StylemixThemes"
msgstr "StylemixThemes"

#: metaboxes/metabox.php:264
msgid ""
"Sync and update your fonts if they are displayed incorrectly on your website."
msgstr ""
"Sincronice y actualice sus fuentes si se muestran incorrectamente en su "
"sitio web."

#: metaboxes/metabox.php:262
msgid "Synchronize"
msgstr "Sincronizar"

#: metaboxes/metabox.php:251
msgid "Text Align"
msgstr "Alineación de texto"

#: metaboxes/metabox.php:253
msgid "Text transform"
msgstr "TTransformación de texto"

#: metaboxes/google_fonts.php:12
msgid "Thin 100"
msgstr "Delgado 100"

#: metaboxes/google_fonts.php:13
msgid "Thin 100 italic"
msgstr "Delgado 100 cursiva"

#: metaboxes/fields/image.php:25
msgid "Upload"
msgstr "Subir"

#: metaboxes/google_fonts.php:51
msgid "Uppercase"
msgstr "Mayúsculas"

#: taxonomy_meta/fields/image.php:47
msgid "Use this media"
msgstr "Utilice este medio"

#: metaboxes/google_fonts.php:35
msgid "Vietnamese"
msgstr "Vietnamita"

#: metaboxes/metabox.php:256
msgid ""
"WARNING! This will overwrite all existing option values, please proceed with "
"caution!"
msgstr ""
"¡ADVERTENCIA! Esto sobrescribirá todos los valores existentes, por favor "
"continúe ¡precaución!"

#: metaboxes/metabox.php:245
msgid "Word spacing"
msgstr "Espaciado de palabras"

#. Description of the plugin
msgid "WordPress Custom Fields & Theme Options with Vue.js."
msgstr "Campos personalizados y opciones de temas de WordPress con Vue.js."
