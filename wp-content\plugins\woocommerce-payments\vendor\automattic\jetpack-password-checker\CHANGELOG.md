# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.2.13] - 2023-04-10
### Added
- Add Jetpack Autoloader package suggestion. [#29988]

## [0.2.12] - 2023-03-20
### Changed
- Updated package dependencies. [#29480]

## [0.2.11] - 2023-02-20
### Changed
- Minor internal updates.

## [0.2.10] - 2023-01-11
### Changed
- Updated package dependencies.

## [0.2.9] - 2022-12-02
### Changed
- Updated package dependencies. [#27688]

## [0.2.8] - 2022-11-22
### Changed
- Updated package dependencies. [#27043]

## [0.2.7] - 2022-09-20
### Changed
- Updated package dependencies.

## [0.2.6] - 2022-07-26
### Changed
- Updated package dependencies. [#25158]

## [0.2.5] - 2022-06-21
### Changed
- Renaming master to trunk. [#24661]

## [0.2.4] - 2022-04-26
### Changed
- Updated package dependencies.

## [0.2.3] - 2022-03-29
### Changed
- Microperformance: Use === null instead of is_null

## [0.2.2] - 2022-01-25
### Changed
- Updated package dependencies.

## [0.2.1] - 2022-01-18
### Changed
- Updated package dependencies.

## [0.2.0] - 2022-01-04
### Changed
- Switch to pcov for code coverage.
- Updated package dependencies
- Updated package textdomain from `jetpack` to `jetpack-password-checker`.

## [0.1.8] - 2021-12-14
### Changed
- Updated package dependencies.

## [0.1.7] - 2021-11-02
### Changed
- Set `convertDeprecationsToExceptions` true in PHPUnit config.
- Update PHPUnit configs to include just what needs coverage rather than include everything then try to exclude stuff that doesn't.

## [0.1.6] - 2021-10-13
### Changed
- Updated package dependencies.

## [0.1.5] - 2021-10-12
### Changed
- Updated package dependencies

## [0.1.4] - 2021-09-28
### Changed
- Updated package dependencies.

## [0.1.3] - 2021-08-30
### Changed
- Run composer update on test-php command instead of phpunit
- Tests: update PHPUnit polyfills dependency (yoast/phpunit-polyfills).

## [0.1.2] - 2021-05-25
### Fixed
- Avoid checking in vendor directory.

## [0.1.1] - 2021-04-27
### Changed
- Updated package dependencies.

## 0.1.0 - 2021-03-30
### Added
- Initial release.

### Fixed
- Use `composer update` rather than `install` in scripts, as composer.lock isn't checked in.

[0.2.13]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.12...v0.2.13
[0.2.12]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.11...v0.2.12
[0.2.11]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.10...v0.2.11
[0.2.10]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.9...v0.2.10
[0.2.9]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.8...v0.2.9
[0.2.8]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.7...v0.2.8
[0.2.7]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.6...v0.2.7
[0.2.6]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.5...v0.2.6
[0.2.5]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.4...v0.2.5
[0.2.4]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.3...v0.2.4
[0.2.3]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.2...v0.2.3
[0.2.2]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/Automattic/jetpack-password-checker/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.8...v0.2.0
[0.1.8]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.7...v0.1.8
[0.1.7]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.6...v0.1.7
[0.1.6]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.5...v0.1.6
[0.1.5]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.4...v0.1.5
[0.1.4]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.3...v0.1.4
[0.1.3]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.1...v0.1.2
[0.1.1]: https://github.com/Automattic/jetpack-password-checker/compare/v0.1.0...v0.1.1
