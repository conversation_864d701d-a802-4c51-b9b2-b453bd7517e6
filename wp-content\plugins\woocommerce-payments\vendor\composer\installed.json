{"packages": [{"name": "automattic/jetpack-a8c-mc-stats", "version": "v1.4.20", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-a8c-mc-stats.git", "reference": "6743d34fe7556455e17cbe1b7c90ed39a1f69089"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-a8c-mc-stats/zipball/6743d34fe7556455e17cbe1b7c90ed39a1f69089", "reference": "6743d34fe7556455e17cbe1b7c90ed39a1f69089", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:43:38+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-a8c-mc-stats", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-a8c-mc-stats/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.4.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Used to record internal usage stats for Automattic. Not visible to site owners.", "support": {"source": "https://github.com/Automattic/jetpack-a8c-mc-stats/tree/v1.4.20"}, "install-path": "../automattic/jetpack-a8c-mc-stats"}, {"name": "automattic/jetpack-admin-ui", "version": "v0.2.20", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-admin-ui.git", "reference": "90f4de6c9d936bbf161f1c2356d98b00ba33576f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-admin-ui/zipball/90f4de6c9d936bbf161f1c2356d98b00ba33576f", "reference": "90f4de6c9d936bbf161f1c2356d98b00ba33576f", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/jetpack-logo": "^1.6.1", "automattic/wordbless": "dev-master", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-25T15:05:53+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-admin-ui", "textdomain": "jetpack-admin-ui", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-admin-ui/compare/${old}...${new}"}, "branch-alias": {"dev-trunk": "0.2.x-dev"}, "version-constants": {"::PACKAGE_VERSION": "src/class-admin-menu.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Generic Jetpack wp-admin UI elements", "support": {"source": "https://github.com/Automattic/jetpack-admin-ui/tree/v0.2.20"}, "install-path": "../automattic/jetpack-admin-ui"}, {"name": "automattic/jetpack-assets", "version": "v1.18.4", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-assets.git", "reference": "fbb76cd3d88ef31ba65d3fa0d3bfed155c016d05"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-assets/zipball/fbb76cd3d88ef31ba65d3fa0d3bfed155c016d05", "reference": "fbb76cd3d88ef31ba65d3fa0d3bfed155c016d05", "shasum": ""}, "require": {"automattic/jetpack-constants": "^1.6.22"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.4", "brain/monkey": "2.6.1", "wikimedia/testing-access-wrapper": "^1.0 || ^2.0", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-06-06T19:19:42+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-assets", "textdomain": "jetpack-assets", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-assets/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.18.x-dev"}}, "installation-source": "dist", "autoload": {"files": ["actions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Asset management utilities for Jetpack ecosystem packages", "support": {"source": "https://github.com/Automattic/jetpack-assets/tree/v1.18.4"}, "install-path": "../automattic/jetpack-assets"}, {"name": "automattic/jetpack-autoloader", "version": "v2.11.18", "version_normalized": "*********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-autoloader.git", "reference": "53cbf0528fa6931c4fa6465bccd37514f9eda720"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-autoloader/zipball/53cbf0528fa6931c4fa6465bccd37514f9eda720", "reference": "53cbf0528fa6931c4fa6465bccd37514f9eda720", "shasum": ""}, "require": {"composer-plugin-api": "^1.1 || ^2.0"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "yoast/phpunit-polyfills": "1.0.4"}, "time": "2023-03-29T12:51:59+00:00", "type": "composer-plugin", "extra": {"autotagger": true, "class": "Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin", "mirror-repo": "Automattic/jetpack-autoloader", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-autoloader/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "2.11.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Automattic\\Jetpack\\Autoloader\\": "src"}, "classmap": ["src/AutoloadGenerator.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Creates a custom autoloader for a plugin or theme.", "support": {"source": "https://github.com/Automattic/jetpack-autoloader/tree/v2.11.18"}, "install-path": "../automattic/jetpack-autoloader"}, {"name": "automattic/jetpack-config", "version": "v1.15.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-config.git", "reference": "f1fa6e24a89192336a1499968bf8c68e173b6e34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-config/zipball/f1fa6e24a89192336a1499968bf8c68e173b6e34", "reference": "f1fa6e24a89192336a1499968bf8c68e173b6e34", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:43:31+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-config", "textdomain": "jetpack-config", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-config/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.15.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Jetpack configuration package that initializes other packages and configures Jetpack's functionality. Can be used as a base for all variants of Jetpack package usage.", "support": {"source": "https://github.com/Automattic/jetpack-config/tree/v1.15.2"}, "install-path": "../automattic/jetpack-config"}, {"name": "automattic/jetpack-connection", "version": "v1.51.7", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-connection.git", "reference": "4c4bae836858957d9aaf6854cf4e24c3261242c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-connection/zipball/4c4bae836858957d9aaf6854cf4e24c3261242c4", "reference": "4c4bae836858957d9aaf6854cf4e24c3261242c4", "shasum": ""}, "require": {"automattic/jetpack-a8c-mc-stats": "^1.4.20", "automattic/jetpack-admin-ui": "^0.2.19", "automattic/jetpack-constants": "^1.6.22", "automattic/jetpack-redirect": "^1.7.25", "automattic/jetpack-roles": "^1.4.23", "automattic/jetpack-status": "^1.16.4"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/wordbless": "@dev", "brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:44:13+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-connection", "textdomain": "jetpack-connection", "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-connection/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.51.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["legacy", "src/", "src/webhooks"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Everything needed to connect to the Jetpack infrastructure", "support": {"source": "https://github.com/Automattic/jetpack-connection/tree/v1.51.7"}, "install-path": "../automattic/jetpack-connection"}, {"name": "automattic/jetpack-constants", "version": "v1.6.22", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-constants.git", "reference": "7b5c44d763c7b0dd7498be2b41a89bfefe84834c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-constants/zipball/7b5c44d763c7b0dd7498be2b41a89bfefe84834c", "reference": "7b5c44d763c7b0dd7498be2b41a89bfefe84834c", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:43:45+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-constants", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-constants/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.6.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A wrapper for defining constants in a more testable way.", "support": {"source": "https://github.com/Automattic/jetpack-constants/tree/v1.6.22"}, "install-path": "../automattic/jetpack-constants"}, {"name": "automattic/jetpack-identity-crisis", "version": "v0.8.43", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-identity-crisis.git", "reference": "8a01e7ed271544d354c2192f575a6fe7dc2ba4d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-identity-crisis/zipball/8a01e7ed271544d354c2192f575a6fe7dc2ba4d3", "reference": "8a01e7ed271544d354c2192f575a6fe7dc2ba4d3", "shasum": ""}, "require": {"automattic/jetpack-assets": "^1.18.1", "automattic/jetpack-connection": "^1.51.7", "automattic/jetpack-constants": "^1.6.22", "automattic/jetpack-logo": "^1.6.1", "automattic/jetpack-status": "^1.16.4"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/wordbless": "@dev", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:44:37+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-identity-crisis", "textdomain": "jetpack-idc", "version-constants": {"::PACKAGE_VERSION": "src/class-identity-crisis.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-identity-crisis/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.8.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Identity Crisis.", "support": {"source": "https://github.com/Automattic/jetpack-identity-crisis/tree/v0.8.43"}, "install-path": "../automattic/jetpack-identity-crisis"}, {"name": "automattic/jetpack-ip", "version": "v0.1.4", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-ip.git", "reference": "fde10bea279aca8adbae9d7ae27d971da3a932e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-ip/zipball/fde10bea279aca8adbae9d7ae27d971da3a932e3", "reference": "fde10bea279aca8adbae9d7ae27d971da3a932e3", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.4", "brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-05-29T19:04:13+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-ip", "changelogger": {"link-template": "https://github.com/automattic/jetpack-ip/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.1.x-dev"}, "textdomain": "jetpack-ip", "version-constants": {"::PACKAGE_VERSION": "src/class-utils.php"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities for working with IP addresses.", "support": {"source": "https://github.com/Automattic/jetpack-ip/tree/v0.1.4"}, "install-path": "../automattic/jetpack-ip"}, {"name": "automattic/jetpack-logo", "version": "v1.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-logo.git", "reference": "6a7b9e5602ca81c207e573dfed9e4fc1dd6a279b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-logo/zipball/6a7b9e5602ca81c207e573dfed9e4fc1dd6a279b", "reference": "6a7b9e5602ca81c207e573dfed9e4fc1dd6a279b", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:43:42+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-logo", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-logo/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.6.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "A logo for Jetpack", "support": {"source": "https://github.com/Automattic/jetpack-logo/tree/v1.6.1"}, "install-path": "../automattic/jetpack-logo"}, {"name": "automattic/jetpack-password-checker", "version": "v0.2.13", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-password-checker.git", "reference": "16b88d370ca2f59b38e6c44bc37fc43e72090dad"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-password-checker/zipball/16b88d370ca2f59b38e6c44bc37fc43e72090dad", "reference": "16b88d370ca2f59b38e6c44bc37fc43e72090dad", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/wordbless": "@dev", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:43:53+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-password-checker", "textdomain": "jetpack-password-checker", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-password-checker/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.2.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Password Checker.", "support": {"source": "https://github.com/Automattic/jetpack-password-checker/tree/v0.2.13"}, "install-path": "../automattic/jetpack-password-checker"}, {"name": "automattic/jetpack-redirect", "version": "v1.7.25", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-redirect.git", "reference": "67d7dce123d4af4fec4b4fe15e99aaad85308314"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-redirect/zipball/67d7dce123d4af4fec4b4fe15e99aaad85308314", "reference": "67d7dce123d4af4fec4b4fe15e99aaad85308314", "shasum": ""}, "require": {"automattic/jetpack-status": "^1.16.4"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:44:05+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-redirect", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-redirect/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.7.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities to build URLs to the jetpack.com/redirect/ service", "support": {"source": "https://github.com/Automattic/jetpack-redirect/tree/v1.7.25"}, "install-path": "../automattic/jetpack-redirect"}, {"name": "automattic/jetpack-roles", "version": "v1.4.23", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-roles.git", "reference": "f147b3e8061fc0de2a892ddc4f4156eb995545f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-roles/zipball/f147b3e8061fc0de2a892ddc4f4156eb995545f9", "reference": "f147b3e8061fc0de2a892ddc4f4156eb995545f9", "shasum": ""}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:43:48+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-roles", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-roles/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.4.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Utilities, related with user roles and capabilities.", "support": {"source": "https://github.com/Automattic/jetpack-roles/tree/v1.4.23"}, "install-path": "../automattic/jetpack-roles"}, {"name": "automattic/jetpack-status", "version": "v1.17.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-status.git", "reference": "0032ee4bce1d4644722ba46858c702a0afa76cff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-status/zipball/0032ee4bce1d4644722ba46858c702a0afa76cff", "reference": "0032ee4bce1d4644722ba46858c702a0afa76cff", "shasum": ""}, "require": {"automattic/jetpack-constants": "^1.6.22"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/jetpack-ip": "^0.1.3", "brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-05-11T05:50:45+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-status", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-status/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.17.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Used to retrieve information about the current status of Jetpack and the site overall.", "support": {"source": "https://github.com/Automattic/jetpack-status/tree/v1.17.1"}, "install-path": "../automattic/jetpack-status"}, {"name": "automattic/jetpack-sync", "version": "v1.47.7", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/Automattic/jetpack-sync.git", "reference": "d37f35bf8bf43ab1e1c665af2831e30814354d27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/jetpack-sync/zipball/d37f35bf8bf43ab1e1c665af2831e30814354d27", "reference": "d37f35bf8bf43ab1e1c665af2831e30814354d27", "shasum": ""}, "require": {"automattic/jetpack-connection": "^1.51.7", "automattic/jetpack-constants": "^1.6.22", "automattic/jetpack-identity-crisis": "^0.8.43", "automattic/jetpack-ip": "^0.1.2", "automattic/jetpack-password-checker": "^0.2.13", "automattic/jetpack-roles": "^1.4.23", "automattic/jetpack-status": "^1.16.4"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/wordbless": "@dev", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "time": "2023-04-10T11:44:43+00:00", "type": "jetpack-library", "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-sync", "textdomain": "jetpack-sync", "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-sync/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.47.x-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Everything needed to allow syncing to the WP.com infrastructure.", "support": {"source": "https://github.com/Automattic/jetpack-sync/tree/v1.47.7"}, "install-path": "../automattic/jetpack-sync"}, {"name": "composer/installers", "version": "v1.10.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "1a0357fccad9d1cc1ea0c9a05b8847fbccccb78d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/1a0357fccad9d1cc1ea0c9a05b8847fbccccb78d", "reference": "1a0357fccad9d1cc1ea0c9a05b8847fbccccb78d", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "time": "2021-01-14T11:07:16+00:00", "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.10.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./installers"}, {"name": "woocommerce/subscriptions-core", "version": "6.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Automattic/woocommerce-subscriptions-core.git", "reference": "b48c46a6a08b73d8afc7a0e686173c5b5c55ecbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Automattic/woocommerce-subscriptions-core/zipball/b48c46a6a08b73d8afc7a0e686173c5b5c55ecbb", "reference": "b48c46a6a08b73d8afc7a0e686173c5b5c55ecbb", "shasum": ""}, "require": {"composer/installers": "~1.2", "php": "^7.1"}, "require-dev": {"dave-liddament/sarb": "^1.1", "phpunit/phpunit": "9.5.14", "woocommerce/woocommerce-sniffs": "0.1.0", "yoast/phpunit-polyfills": "1.0.3"}, "time": "2023-07-18T06:28:51+00:00", "type": "wordpress-plugin", "extra": {"phpcodesniffer-search-depth": 2}, "installation-source": "dist", "archive": {"exclude": ["!/build", "*.zip", "node_modules"]}, "scripts": {"phpcs": ["bin/phpcs.sh"], "lint": ["find . \\( -path ./vendor \\) -prune -o \\( -name '*.php' \\) -exec php -lf {} \\;| (! grep -v \"No syntax errors detected\" )"], "test": ["phpunit"]}, "license": ["GPL-3.0-or-later"], "description": "Sell products and services with recurring payments in your WooCommerce Store.", "homepage": "https://github.com/Automattic/woocommerce-subscriptions-core", "support": {"source": "https://github.com/Automattic/woocommerce-subscriptions-core/tree/6.0.0", "issues": "https://github.com/Automattic/woocommerce-subscriptions-core/issues"}, "install-path": "../woocommerce/subscriptions-core"}], "dev": false, "dev-package-names": []}