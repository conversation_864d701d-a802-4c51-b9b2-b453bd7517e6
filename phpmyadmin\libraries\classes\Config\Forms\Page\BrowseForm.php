<?php
/**
 * User preferences form
 */

declare(strict_types=1);

namespace Php<PERSON>yAdmin\Config\Forms\Page;

use PhpMyAdmin\Config\Forms\BaseForm;
use PhpMyAdmin\Config\Forms\User\MainForm;

class BrowseForm extends BaseForm
{
    /**
     * @return array
     */
    public static function getForms()
    {
        return [
            'Browse' => MainForm::getForms()['Browse'],
        ];
    }
}
