/*------------------------------------------------------------------
[Table of contents]
1. form
2. utilities
3. theme effect
4. buttons
5. alerts
6. woocommerce
7. woocommerce widgets
-------------------------------------------------------------------*/
/**
* Web Application Prefix Apply For Making Owner Styles
*/
/**
 *   Blocks Layout Selectors
 */
/********* LAYOUT **************/
/* carousel-controls-v1 */
/* carousel-controls-v2 */
/* carousel-controls-v3 */
/****/
.animated-underline {
  position: relative;
}
.animated-underline:after {
  bottom: 0;
  content: "";
  background-color: #2441e7;
  position: absolute;
  width: 100%;
  height: 1px;
  left: 0;
  -webkit-transform: scaleX(0);
  -moz-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform-origin: 100% 50%;
  -moz-transform-origin: 100% 50%;
  -ms-transform-origin: 100% 50%;
  transform-origin: 100% 50%;
  -webkit-transition: transform 0.3s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.5s;
  -o-transition: transform 0.3s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.5s;
  transition: transform 0.3s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.5s;
}
.rtl .animated-underline:after {
  right: 0;
  left: auto;
}
.animated-underline:hover, .animated-underline:focus, .animated-underline:active {
  background-color: transparent;
}
.animated-underline:hover:after, .animated-underline:focus:after, .animated-underline:active:after {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform-origin: 0 50%;
  -moz-transform-origin: 0 50%;
  -ms-transform-origin: 0 50%;
  transform-origin: 0 50%;
  -webkit-transform: scaleX(1);
  -moz-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
  -webkit-transition: transform 0.3s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.3s;
  -o-transition: transform 0.3s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.3s;
  transition: transform 0.3s cubic-bezier(0.77, 0, 0.175, 1), opacity 0.3s;
}

.animated-underline-center {
  position: relative;
}
.animated-underline-center:before, .animated-underline-center:after {
  content: '';
  position: absolute;
  -webkit-transition: transform 0.2s ease;
  -o-transition: transform 0.2s ease;
  transition: transform 0.2s ease;
}
.animated-underline-center:before {
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background: #2441e7;
  -webkit-transform: scaleX(0);
  -ms-transform: scaleX(0);
  -o-transform: scaleX(0);
  transform: scaleX(0);
}
.animated-underline-center:hover:before {
  -webkit-transform: scaleX(1);
  -ms-transform: scaleX(1);
  -o-transform: scaleX(1);
  transform: scaleX(1);
}

.margin-top-1 {
  margin-top: 1px !important;
}

.margin-left-1 {
  margin-left: 1px !important;
}

.margin-bottom-1 {
  margin-bottom: 1px !important;
}

.margin-right-1 {
  margin-right: 1px !important;
}

.margin-top-2 {
  margin-top: 2px !important;
}

.margin-left-2 {
  margin-left: 2px !important;
}

.margin-bottom-2 {
  margin-bottom: 2px !important;
}

.margin-right-2 {
  margin-right: 2px !important;
}

.margin-top-3 {
  margin-top: 3px !important;
}

.margin-left-3 {
  margin-left: 3px !important;
}

.margin-bottom-3 {
  margin-bottom: 3px !important;
}

.margin-right-3 {
  margin-right: 3px !important;
}

.margin-top-4 {
  margin-top: 4px !important;
}

.margin-left-4 {
  margin-left: 4px !important;
}

.margin-bottom-4 {
  margin-bottom: 4px !important;
}

.margin-right-4 {
  margin-right: 4px !important;
}

.margin-top-5 {
  margin-top: 5px !important;
}

.margin-left-5 {
  margin-left: 5px !important;
}

.margin-bottom-5 {
  margin-bottom: 5px !important;
}

.margin-right-5 {
  margin-right: 5px !important;
}

.margin-top-6 {
  margin-top: 6px !important;
}

.margin-left-6 {
  margin-left: 6px !important;
}

.margin-bottom-6 {
  margin-bottom: 6px !important;
}

.margin-right-6 {
  margin-right: 6px !important;
}

.margin-top-7 {
  margin-top: 7px !important;
}

.margin-left-7 {
  margin-left: 7px !important;
}

.margin-bottom-7 {
  margin-bottom: 7px !important;
}

.margin-right-7 {
  margin-right: 7px !important;
}

.margin-top-8 {
  margin-top: 8px !important;
}

.margin-left-8 {
  margin-left: 8px !important;
}

.margin-bottom-8 {
  margin-bottom: 8px !important;
}

.margin-right-8 {
  margin-right: 8px !important;
}

.margin-top-9 {
  margin-top: 9px !important;
}

.margin-left-9 {
  margin-left: 9px !important;
}

.margin-bottom-9 {
  margin-bottom: 9px !important;
}

.margin-right-9 {
  margin-right: 9px !important;
}

.margin-top-10 {
  margin-top: 10px !important;
}

.margin-left-10 {
  margin-left: 10px !important;
}

.margin-bottom-10 {
  margin-bottom: 10px !important;
}

.margin-right-10 {
  margin-right: 10px !important;
}

.margin-top-11 {
  margin-top: 11px !important;
}

.margin-left-11 {
  margin-left: 11px !important;
}

.margin-bottom-11 {
  margin-bottom: 11px !important;
}

.margin-right-11 {
  margin-right: 11px !important;
}

.margin-top-12 {
  margin-top: 12px !important;
}

.margin-left-12 {
  margin-left: 12px !important;
}

.margin-bottom-12 {
  margin-bottom: 12px !important;
}

.margin-right-12 {
  margin-right: 12px !important;
}

.margin-top-13 {
  margin-top: 13px !important;
}

.margin-left-13 {
  margin-left: 13px !important;
}

.margin-bottom-13 {
  margin-bottom: 13px !important;
}

.margin-right-13 {
  margin-right: 13px !important;
}

.margin-top-14 {
  margin-top: 14px !important;
}

.margin-left-14 {
  margin-left: 14px !important;
}

.margin-bottom-14 {
  margin-bottom: 14px !important;
}

.margin-right-14 {
  margin-right: 14px !important;
}

.margin-top-15 {
  margin-top: 15px !important;
}

.margin-left-15 {
  margin-left: 15px !important;
}

.margin-bottom-15 {
  margin-bottom: 15px !important;
}

.margin-right-15 {
  margin-right: 15px !important;
}

.margin-top-16 {
  margin-top: 16px !important;
}

.margin-left-16 {
  margin-left: 16px !important;
}

.margin-bottom-16 {
  margin-bottom: 16px !important;
}

.margin-right-16 {
  margin-right: 16px !important;
}

.margin-top-17 {
  margin-top: 17px !important;
}

.margin-left-17 {
  margin-left: 17px !important;
}

.margin-bottom-17 {
  margin-bottom: 17px !important;
}

.margin-right-17 {
  margin-right: 17px !important;
}

.margin-top-18 {
  margin-top: 18px !important;
}

.margin-left-18 {
  margin-left: 18px !important;
}

.margin-bottom-18 {
  margin-bottom: 18px !important;
}

.margin-right-18 {
  margin-right: 18px !important;
}

.margin-top-19 {
  margin-top: 19px !important;
}

.margin-left-19 {
  margin-left: 19px !important;
}

.margin-bottom-19 {
  margin-bottom: 19px !important;
}

.margin-right-19 {
  margin-right: 19px !important;
}

.margin-top-20 {
  margin-top: 20px !important;
}

.margin-left-20 {
  margin-left: 20px !important;
}

.margin-bottom-20 {
  margin-bottom: 20px !important;
}

.margin-right-20 {
  margin-right: 20px !important;
}

.margin-top-21 {
  margin-top: 21px !important;
}

.margin-left-21 {
  margin-left: 21px !important;
}

.margin-bottom-21 {
  margin-bottom: 21px !important;
}

.margin-right-21 {
  margin-right: 21px !important;
}

.margin-top-22 {
  margin-top: 22px !important;
}

.margin-left-22 {
  margin-left: 22px !important;
}

.margin-bottom-22 {
  margin-bottom: 22px !important;
}

.margin-right-22 {
  margin-right: 22px !important;
}

.margin-top-23 {
  margin-top: 23px !important;
}

.margin-left-23 {
  margin-left: 23px !important;
}

.margin-bottom-23 {
  margin-bottom: 23px !important;
}

.margin-right-23 {
  margin-right: 23px !important;
}

.margin-top-24 {
  margin-top: 24px !important;
}

.margin-left-24 {
  margin-left: 24px !important;
}

.margin-bottom-24 {
  margin-bottom: 24px !important;
}

.margin-right-24 {
  margin-right: 24px !important;
}

.margin-top-25 {
  margin-top: 25px !important;
}

.margin-left-25 {
  margin-left: 25px !important;
}

.margin-bottom-25 {
  margin-bottom: 25px !important;
}

.margin-right-25 {
  margin-right: 25px !important;
}

.margin-top-26 {
  margin-top: 26px !important;
}

.margin-left-26 {
  margin-left: 26px !important;
}

.margin-bottom-26 {
  margin-bottom: 26px !important;
}

.margin-right-26 {
  margin-right: 26px !important;
}

.margin-top-27 {
  margin-top: 27px !important;
}

.margin-left-27 {
  margin-left: 27px !important;
}

.margin-bottom-27 {
  margin-bottom: 27px !important;
}

.margin-right-27 {
  margin-right: 27px !important;
}

.margin-top-28 {
  margin-top: 28px !important;
}

.margin-left-28 {
  margin-left: 28px !important;
}

.margin-bottom-28 {
  margin-bottom: 28px !important;
}

.margin-right-28 {
  margin-right: 28px !important;
}

.margin-top-29 {
  margin-top: 29px !important;
}

.margin-left-29 {
  margin-left: 29px !important;
}

.margin-bottom-29 {
  margin-bottom: 29px !important;
}

.margin-right-29 {
  margin-right: 29px !important;
}

.margin-top-30 {
  margin-top: 30px !important;
}

.margin-left-30 {
  margin-left: 30px !important;
}

.margin-bottom-30 {
  margin-bottom: 30px !important;
}

.margin-right-30 {
  margin-right: 30px !important;
}

.margin-top-31 {
  margin-top: 31px !important;
}

.margin-left-31 {
  margin-left: 31px !important;
}

.margin-bottom-31 {
  margin-bottom: 31px !important;
}

.margin-right-31 {
  margin-right: 31px !important;
}

.margin-top-32 {
  margin-top: 32px !important;
}

.margin-left-32 {
  margin-left: 32px !important;
}

.margin-bottom-32 {
  margin-bottom: 32px !important;
}

.margin-right-32 {
  margin-right: 32px !important;
}

.margin-top-33 {
  margin-top: 33px !important;
}

.margin-left-33 {
  margin-left: 33px !important;
}

.margin-bottom-33 {
  margin-bottom: 33px !important;
}

.margin-right-33 {
  margin-right: 33px !important;
}

.margin-top-34 {
  margin-top: 34px !important;
}

.margin-left-34 {
  margin-left: 34px !important;
}

.margin-bottom-34 {
  margin-bottom: 34px !important;
}

.margin-right-34 {
  margin-right: 34px !important;
}

.margin-top-35 {
  margin-top: 35px !important;
}

.margin-left-35 {
  margin-left: 35px !important;
}

.margin-bottom-35 {
  margin-bottom: 35px !important;
}

.margin-right-35 {
  margin-right: 35px !important;
}

.margin-top-36 {
  margin-top: 36px !important;
}

.margin-left-36 {
  margin-left: 36px !important;
}

.margin-bottom-36 {
  margin-bottom: 36px !important;
}

.margin-right-36 {
  margin-right: 36px !important;
}

.margin-top-37 {
  margin-top: 37px !important;
}

.margin-left-37 {
  margin-left: 37px !important;
}

.margin-bottom-37 {
  margin-bottom: 37px !important;
}

.margin-right-37 {
  margin-right: 37px !important;
}

.margin-top-38 {
  margin-top: 38px !important;
}

.margin-left-38 {
  margin-left: 38px !important;
}

.margin-bottom-38 {
  margin-bottom: 38px !important;
}

.margin-right-38 {
  margin-right: 38px !important;
}

.margin-top-39 {
  margin-top: 39px !important;
}

.margin-left-39 {
  margin-left: 39px !important;
}

.margin-bottom-39 {
  margin-bottom: 39px !important;
}

.margin-right-39 {
  margin-right: 39px !important;
}

.margin-top-40 {
  margin-top: 40px !important;
}

.margin-left-40 {
  margin-left: 40px !important;
}

.margin-bottom-40 {
  margin-bottom: 40px !important;
}

.margin-right-40 {
  margin-right: 40px !important;
}

.margin-top-41 {
  margin-top: 41px !important;
}

.margin-left-41 {
  margin-left: 41px !important;
}

.margin-bottom-41 {
  margin-bottom: 41px !important;
}

.margin-right-41 {
  margin-right: 41px !important;
}

.margin-top-42 {
  margin-top: 42px !important;
}

.margin-left-42 {
  margin-left: 42px !important;
}

.margin-bottom-42 {
  margin-bottom: 42px !important;
}

.margin-right-42 {
  margin-right: 42px !important;
}

.margin-top-43 {
  margin-top: 43px !important;
}

.margin-left-43 {
  margin-left: 43px !important;
}

.margin-bottom-43 {
  margin-bottom: 43px !important;
}

.margin-right-43 {
  margin-right: 43px !important;
}

.margin-top-44 {
  margin-top: 44px !important;
}

.margin-left-44 {
  margin-left: 44px !important;
}

.margin-bottom-44 {
  margin-bottom: 44px !important;
}

.margin-right-44 {
  margin-right: 44px !important;
}

.margin-top-45 {
  margin-top: 45px !important;
}

.margin-left-45 {
  margin-left: 45px !important;
}

.margin-bottom-45 {
  margin-bottom: 45px !important;
}

.margin-right-45 {
  margin-right: 45px !important;
}

.margin-top-46 {
  margin-top: 46px !important;
}

.margin-left-46 {
  margin-left: 46px !important;
}

.margin-bottom-46 {
  margin-bottom: 46px !important;
}

.margin-right-46 {
  margin-right: 46px !important;
}

.margin-top-47 {
  margin-top: 47px !important;
}

.margin-left-47 {
  margin-left: 47px !important;
}

.margin-bottom-47 {
  margin-bottom: 47px !important;
}

.margin-right-47 {
  margin-right: 47px !important;
}

.margin-top-48 {
  margin-top: 48px !important;
}

.margin-left-48 {
  margin-left: 48px !important;
}

.margin-bottom-48 {
  margin-bottom: 48px !important;
}

.margin-right-48 {
  margin-right: 48px !important;
}

.margin-top-49 {
  margin-top: 49px !important;
}

.margin-left-49 {
  margin-left: 49px !important;
}

.margin-bottom-49 {
  margin-bottom: 49px !important;
}

.margin-right-49 {
  margin-right: 49px !important;
}

.margin-top-50 {
  margin-top: 50px !important;
}

.margin-left-50 {
  margin-left: 50px !important;
}

.margin-bottom-50 {
  margin-bottom: 50px !important;
}

.margin-right-50 {
  margin-right: 50px !important;
}

.margin-top-51 {
  margin-top: 51px !important;
}

.margin-left-51 {
  margin-left: 51px !important;
}

.margin-bottom-51 {
  margin-bottom: 51px !important;
}

.margin-right-51 {
  margin-right: 51px !important;
}

.margin-top-52 {
  margin-top: 52px !important;
}

.margin-left-52 {
  margin-left: 52px !important;
}

.margin-bottom-52 {
  margin-bottom: 52px !important;
}

.margin-right-52 {
  margin-right: 52px !important;
}

.margin-top-53 {
  margin-top: 53px !important;
}

.margin-left-53 {
  margin-left: 53px !important;
}

.margin-bottom-53 {
  margin-bottom: 53px !important;
}

.margin-right-53 {
  margin-right: 53px !important;
}

.margin-top-54 {
  margin-top: 54px !important;
}

.margin-left-54 {
  margin-left: 54px !important;
}

.margin-bottom-54 {
  margin-bottom: 54px !important;
}

.margin-right-54 {
  margin-right: 54px !important;
}

.margin-top-55 {
  margin-top: 55px !important;
}

.margin-left-55 {
  margin-left: 55px !important;
}

.margin-bottom-55 {
  margin-bottom: 55px !important;
}

.margin-right-55 {
  margin-right: 55px !important;
}

.margin-top-56 {
  margin-top: 56px !important;
}

.margin-left-56 {
  margin-left: 56px !important;
}

.margin-bottom-56 {
  margin-bottom: 56px !important;
}

.margin-right-56 {
  margin-right: 56px !important;
}

.margin-top-57 {
  margin-top: 57px !important;
}

.margin-left-57 {
  margin-left: 57px !important;
}

.margin-bottom-57 {
  margin-bottom: 57px !important;
}

.margin-right-57 {
  margin-right: 57px !important;
}

.margin-top-58 {
  margin-top: 58px !important;
}

.margin-left-58 {
  margin-left: 58px !important;
}

.margin-bottom-58 {
  margin-bottom: 58px !important;
}

.margin-right-58 {
  margin-right: 58px !important;
}

.margin-top-59 {
  margin-top: 59px !important;
}

.margin-left-59 {
  margin-left: 59px !important;
}

.margin-bottom-59 {
  margin-bottom: 59px !important;
}

.margin-right-59 {
  margin-right: 59px !important;
}

.margin-top-60 {
  margin-top: 60px !important;
}

.margin-left-60 {
  margin-left: 60px !important;
}

.margin-bottom-60 {
  margin-bottom: 60px !important;
}

.margin-right-60 {
  margin-right: 60px !important;
}

.margin-top-61 {
  margin-top: 61px !important;
}

.margin-left-61 {
  margin-left: 61px !important;
}

.margin-bottom-61 {
  margin-bottom: 61px !important;
}

.margin-right-61 {
  margin-right: 61px !important;
}

.margin-top-62 {
  margin-top: 62px !important;
}

.margin-left-62 {
  margin-left: 62px !important;
}

.margin-bottom-62 {
  margin-bottom: 62px !important;
}

.margin-right-62 {
  margin-right: 62px !important;
}

.margin-top-63 {
  margin-top: 63px !important;
}

.margin-left-63 {
  margin-left: 63px !important;
}

.margin-bottom-63 {
  margin-bottom: 63px !important;
}

.margin-right-63 {
  margin-right: 63px !important;
}

.margin-top-64 {
  margin-top: 64px !important;
}

.margin-left-64 {
  margin-left: 64px !important;
}

.margin-bottom-64 {
  margin-bottom: 64px !important;
}

.margin-right-64 {
  margin-right: 64px !important;
}

.margin-top-65 {
  margin-top: 65px !important;
}

.margin-left-65 {
  margin-left: 65px !important;
}

.margin-bottom-65 {
  margin-bottom: 65px !important;
}

.margin-right-65 {
  margin-right: 65px !important;
}

.margin-top-66 {
  margin-top: 66px !important;
}

.margin-left-66 {
  margin-left: 66px !important;
}

.margin-bottom-66 {
  margin-bottom: 66px !important;
}

.margin-right-66 {
  margin-right: 66px !important;
}

.margin-top-67 {
  margin-top: 67px !important;
}

.margin-left-67 {
  margin-left: 67px !important;
}

.margin-bottom-67 {
  margin-bottom: 67px !important;
}

.margin-right-67 {
  margin-right: 67px !important;
}

.margin-top-68 {
  margin-top: 68px !important;
}

.margin-left-68 {
  margin-left: 68px !important;
}

.margin-bottom-68 {
  margin-bottom: 68px !important;
}

.margin-right-68 {
  margin-right: 68px !important;
}

.margin-top-69 {
  margin-top: 69px !important;
}

.margin-left-69 {
  margin-left: 69px !important;
}

.margin-bottom-69 {
  margin-bottom: 69px !important;
}

.margin-right-69 {
  margin-right: 69px !important;
}

.margin-top-70 {
  margin-top: 70px !important;
}

.margin-left-70 {
  margin-left: 70px !important;
}

.margin-bottom-70 {
  margin-bottom: 70px !important;
}

.margin-right-70 {
  margin-right: 70px !important;
}

.margin-top-71 {
  margin-top: 71px !important;
}

.margin-left-71 {
  margin-left: 71px !important;
}

.margin-bottom-71 {
  margin-bottom: 71px !important;
}

.margin-right-71 {
  margin-right: 71px !important;
}

.margin-top-72 {
  margin-top: 72px !important;
}

.margin-left-72 {
  margin-left: 72px !important;
}

.margin-bottom-72 {
  margin-bottom: 72px !important;
}

.margin-right-72 {
  margin-right: 72px !important;
}

.margin-top-73 {
  margin-top: 73px !important;
}

.margin-left-73 {
  margin-left: 73px !important;
}

.margin-bottom-73 {
  margin-bottom: 73px !important;
}

.margin-right-73 {
  margin-right: 73px !important;
}

.margin-top-74 {
  margin-top: 74px !important;
}

.margin-left-74 {
  margin-left: 74px !important;
}

.margin-bottom-74 {
  margin-bottom: 74px !important;
}

.margin-right-74 {
  margin-right: 74px !important;
}

.margin-top-75 {
  margin-top: 75px !important;
}

.margin-left-75 {
  margin-left: 75px !important;
}

.margin-bottom-75 {
  margin-bottom: 75px !important;
}

.margin-right-75 {
  margin-right: 75px !important;
}

.margin-top-76 {
  margin-top: 76px !important;
}

.margin-left-76 {
  margin-left: 76px !important;
}

.margin-bottom-76 {
  margin-bottom: 76px !important;
}

.margin-right-76 {
  margin-right: 76px !important;
}

.margin-top-77 {
  margin-top: 77px !important;
}

.margin-left-77 {
  margin-left: 77px !important;
}

.margin-bottom-77 {
  margin-bottom: 77px !important;
}

.margin-right-77 {
  margin-right: 77px !important;
}

.margin-top-78 {
  margin-top: 78px !important;
}

.margin-left-78 {
  margin-left: 78px !important;
}

.margin-bottom-78 {
  margin-bottom: 78px !important;
}

.margin-right-78 {
  margin-right: 78px !important;
}

.margin-top-79 {
  margin-top: 79px !important;
}

.margin-left-79 {
  margin-left: 79px !important;
}

.margin-bottom-79 {
  margin-bottom: 79px !important;
}

.margin-right-79 {
  margin-right: 79px !important;
}

.margin-top-80 {
  margin-top: 80px !important;
}

.margin-left-80 {
  margin-left: 80px !important;
}

.margin-bottom-80 {
  margin-bottom: 80px !important;
}

.margin-right-80 {
  margin-right: 80px !important;
}

.padding-top-1 {
  padding-top: 1px !important;
}

.padding-left-1 {
  padding-left: 1px !important;
}

.padding-bottom-1 {
  padding-bottom: 1px !important;
}

.padding-right-1 {
  padding-right: 1px !important;
}

.padding-top-2 {
  padding-top: 2px !important;
}

.padding-left-2 {
  padding-left: 2px !important;
}

.padding-bottom-2 {
  padding-bottom: 2px !important;
}

.padding-right-2 {
  padding-right: 2px !important;
}

.padding-top-3 {
  padding-top: 3px !important;
}

.padding-left-3 {
  padding-left: 3px !important;
}

.padding-bottom-3 {
  padding-bottom: 3px !important;
}

.padding-right-3 {
  padding-right: 3px !important;
}

.padding-top-4 {
  padding-top: 4px !important;
}

.padding-left-4 {
  padding-left: 4px !important;
}

.padding-bottom-4 {
  padding-bottom: 4px !important;
}

.padding-right-4 {
  padding-right: 4px !important;
}

.padding-top-5 {
  padding-top: 5px !important;
}

.padding-left-5 {
  padding-left: 5px !important;
}

.padding-bottom-5 {
  padding-bottom: 5px !important;
}

.padding-right-5 {
  padding-right: 5px !important;
}

.padding-top-6 {
  padding-top: 6px !important;
}

.padding-left-6 {
  padding-left: 6px !important;
}

.padding-bottom-6 {
  padding-bottom: 6px !important;
}

.padding-right-6 {
  padding-right: 6px !important;
}

.padding-top-7 {
  padding-top: 7px !important;
}

.padding-left-7 {
  padding-left: 7px !important;
}

.padding-bottom-7 {
  padding-bottom: 7px !important;
}

.padding-right-7 {
  padding-right: 7px !important;
}

.padding-top-8 {
  padding-top: 8px !important;
}

.padding-left-8 {
  padding-left: 8px !important;
}

.padding-bottom-8 {
  padding-bottom: 8px !important;
}

.padding-right-8 {
  padding-right: 8px !important;
}

.padding-top-9 {
  padding-top: 9px !important;
}

.padding-left-9 {
  padding-left: 9px !important;
}

.padding-bottom-9 {
  padding-bottom: 9px !important;
}

.padding-right-9 {
  padding-right: 9px !important;
}

.padding-top-10 {
  padding-top: 10px !important;
}

.padding-left-10 {
  padding-left: 10px !important;
}

.padding-bottom-10 {
  padding-bottom: 10px !important;
}

.padding-right-10 {
  padding-right: 10px !important;
}

.padding-top-11 {
  padding-top: 11px !important;
}

.padding-left-11 {
  padding-left: 11px !important;
}

.padding-bottom-11 {
  padding-bottom: 11px !important;
}

.padding-right-11 {
  padding-right: 11px !important;
}

.padding-top-12 {
  padding-top: 12px !important;
}

.padding-left-12 {
  padding-left: 12px !important;
}

.padding-bottom-12 {
  padding-bottom: 12px !important;
}

.padding-right-12 {
  padding-right: 12px !important;
}

.padding-top-13 {
  padding-top: 13px !important;
}

.padding-left-13 {
  padding-left: 13px !important;
}

.padding-bottom-13 {
  padding-bottom: 13px !important;
}

.padding-right-13 {
  padding-right: 13px !important;
}

.padding-top-14 {
  padding-top: 14px !important;
}

.padding-left-14 {
  padding-left: 14px !important;
}

.padding-bottom-14 {
  padding-bottom: 14px !important;
}

.padding-right-14 {
  padding-right: 14px !important;
}

.padding-top-15 {
  padding-top: 15px !important;
}

.padding-left-15 {
  padding-left: 15px !important;
}

.padding-bottom-15 {
  padding-bottom: 15px !important;
}

.padding-right-15 {
  padding-right: 15px !important;
}

.padding-top-16 {
  padding-top: 16px !important;
}

.padding-left-16 {
  padding-left: 16px !important;
}

.padding-bottom-16 {
  padding-bottom: 16px !important;
}

.padding-right-16 {
  padding-right: 16px !important;
}

.padding-top-17 {
  padding-top: 17px !important;
}

.padding-left-17 {
  padding-left: 17px !important;
}

.padding-bottom-17 {
  padding-bottom: 17px !important;
}

.padding-right-17 {
  padding-right: 17px !important;
}

.padding-top-18 {
  padding-top: 18px !important;
}

.padding-left-18 {
  padding-left: 18px !important;
}

.padding-bottom-18 {
  padding-bottom: 18px !important;
}

.padding-right-18 {
  padding-right: 18px !important;
}

.padding-top-19 {
  padding-top: 19px !important;
}

.padding-left-19 {
  padding-left: 19px !important;
}

.padding-bottom-19 {
  padding-bottom: 19px !important;
}

.padding-right-19 {
  padding-right: 19px !important;
}

.padding-top-20 {
  padding-top: 20px !important;
}

.padding-left-20 {
  padding-left: 20px !important;
}

.padding-bottom-20 {
  padding-bottom: 20px !important;
}

.padding-right-20 {
  padding-right: 20px !important;
}

.padding-top-21 {
  padding-top: 21px !important;
}

.padding-left-21 {
  padding-left: 21px !important;
}

.padding-bottom-21 {
  padding-bottom: 21px !important;
}

.padding-right-21 {
  padding-right: 21px !important;
}

.padding-top-22 {
  padding-top: 22px !important;
}

.padding-left-22 {
  padding-left: 22px !important;
}

.padding-bottom-22 {
  padding-bottom: 22px !important;
}

.padding-right-22 {
  padding-right: 22px !important;
}

.padding-top-23 {
  padding-top: 23px !important;
}

.padding-left-23 {
  padding-left: 23px !important;
}

.padding-bottom-23 {
  padding-bottom: 23px !important;
}

.padding-right-23 {
  padding-right: 23px !important;
}

.padding-top-24 {
  padding-top: 24px !important;
}

.padding-left-24 {
  padding-left: 24px !important;
}

.padding-bottom-24 {
  padding-bottom: 24px !important;
}

.padding-right-24 {
  padding-right: 24px !important;
}

.padding-top-25 {
  padding-top: 25px !important;
}

.padding-left-25 {
  padding-left: 25px !important;
}

.padding-bottom-25 {
  padding-bottom: 25px !important;
}

.padding-right-25 {
  padding-right: 25px !important;
}

.padding-top-26 {
  padding-top: 26px !important;
}

.padding-left-26 {
  padding-left: 26px !important;
}

.padding-bottom-26 {
  padding-bottom: 26px !important;
}

.padding-right-26 {
  padding-right: 26px !important;
}

.padding-top-27 {
  padding-top: 27px !important;
}

.padding-left-27 {
  padding-left: 27px !important;
}

.padding-bottom-27 {
  padding-bottom: 27px !important;
}

.padding-right-27 {
  padding-right: 27px !important;
}

.padding-top-28 {
  padding-top: 28px !important;
}

.padding-left-28 {
  padding-left: 28px !important;
}

.padding-bottom-28 {
  padding-bottom: 28px !important;
}

.padding-right-28 {
  padding-right: 28px !important;
}

.padding-top-29 {
  padding-top: 29px !important;
}

.padding-left-29 {
  padding-left: 29px !important;
}

.padding-bottom-29 {
  padding-bottom: 29px !important;
}

.padding-right-29 {
  padding-right: 29px !important;
}

.padding-top-30 {
  padding-top: 30px !important;
}

.padding-left-30 {
  padding-left: 30px !important;
}

.padding-bottom-30 {
  padding-bottom: 30px !important;
}

.padding-right-30 {
  padding-right: 30px !important;
}

.padding-top-31 {
  padding-top: 31px !important;
}

.padding-left-31 {
  padding-left: 31px !important;
}

.padding-bottom-31 {
  padding-bottom: 31px !important;
}

.padding-right-31 {
  padding-right: 31px !important;
}

.padding-top-32 {
  padding-top: 32px !important;
}

.padding-left-32 {
  padding-left: 32px !important;
}

.padding-bottom-32 {
  padding-bottom: 32px !important;
}

.padding-right-32 {
  padding-right: 32px !important;
}

.padding-top-33 {
  padding-top: 33px !important;
}

.padding-left-33 {
  padding-left: 33px !important;
}

.padding-bottom-33 {
  padding-bottom: 33px !important;
}

.padding-right-33 {
  padding-right: 33px !important;
}

.padding-top-34 {
  padding-top: 34px !important;
}

.padding-left-34 {
  padding-left: 34px !important;
}

.padding-bottom-34 {
  padding-bottom: 34px !important;
}

.padding-right-34 {
  padding-right: 34px !important;
}

.padding-top-35 {
  padding-top: 35px !important;
}

.padding-left-35 {
  padding-left: 35px !important;
}

.padding-bottom-35 {
  padding-bottom: 35px !important;
}

.padding-right-35 {
  padding-right: 35px !important;
}

.padding-top-36 {
  padding-top: 36px !important;
}

.padding-left-36 {
  padding-left: 36px !important;
}

.padding-bottom-36 {
  padding-bottom: 36px !important;
}

.padding-right-36 {
  padding-right: 36px !important;
}

.padding-top-37 {
  padding-top: 37px !important;
}

.padding-left-37 {
  padding-left: 37px !important;
}

.padding-bottom-37 {
  padding-bottom: 37px !important;
}

.padding-right-37 {
  padding-right: 37px !important;
}

.padding-top-38 {
  padding-top: 38px !important;
}

.padding-left-38 {
  padding-left: 38px !important;
}

.padding-bottom-38 {
  padding-bottom: 38px !important;
}

.padding-right-38 {
  padding-right: 38px !important;
}

.padding-top-39 {
  padding-top: 39px !important;
}

.padding-left-39 {
  padding-left: 39px !important;
}

.padding-bottom-39 {
  padding-bottom: 39px !important;
}

.padding-right-39 {
  padding-right: 39px !important;
}

.padding-top-40 {
  padding-top: 40px !important;
}

.padding-left-40 {
  padding-left: 40px !important;
}

.padding-bottom-40 {
  padding-bottom: 40px !important;
}

.padding-right-40 {
  padding-right: 40px !important;
}

.padding-top-41 {
  padding-top: 41px !important;
}

.padding-left-41 {
  padding-left: 41px !important;
}

.padding-bottom-41 {
  padding-bottom: 41px !important;
}

.padding-right-41 {
  padding-right: 41px !important;
}

.padding-top-42 {
  padding-top: 42px !important;
}

.padding-left-42 {
  padding-left: 42px !important;
}

.padding-bottom-42 {
  padding-bottom: 42px !important;
}

.padding-right-42 {
  padding-right: 42px !important;
}

.padding-top-43 {
  padding-top: 43px !important;
}

.padding-left-43 {
  padding-left: 43px !important;
}

.padding-bottom-43 {
  padding-bottom: 43px !important;
}

.padding-right-43 {
  padding-right: 43px !important;
}

.padding-top-44 {
  padding-top: 44px !important;
}

.padding-left-44 {
  padding-left: 44px !important;
}

.padding-bottom-44 {
  padding-bottom: 44px !important;
}

.padding-right-44 {
  padding-right: 44px !important;
}

.padding-top-45 {
  padding-top: 45px !important;
}

.padding-left-45 {
  padding-left: 45px !important;
}

.padding-bottom-45 {
  padding-bottom: 45px !important;
}

.padding-right-45 {
  padding-right: 45px !important;
}

.padding-top-46 {
  padding-top: 46px !important;
}

.padding-left-46 {
  padding-left: 46px !important;
}

.padding-bottom-46 {
  padding-bottom: 46px !important;
}

.padding-right-46 {
  padding-right: 46px !important;
}

.padding-top-47 {
  padding-top: 47px !important;
}

.padding-left-47 {
  padding-left: 47px !important;
}

.padding-bottom-47 {
  padding-bottom: 47px !important;
}

.padding-right-47 {
  padding-right: 47px !important;
}

.padding-top-48 {
  padding-top: 48px !important;
}

.padding-left-48 {
  padding-left: 48px !important;
}

.padding-bottom-48 {
  padding-bottom: 48px !important;
}

.padding-right-48 {
  padding-right: 48px !important;
}

.padding-top-49 {
  padding-top: 49px !important;
}

.padding-left-49 {
  padding-left: 49px !important;
}

.padding-bottom-49 {
  padding-bottom: 49px !important;
}

.padding-right-49 {
  padding-right: 49px !important;
}

.padding-top-50 {
  padding-top: 50px !important;
}

.padding-left-50 {
  padding-left: 50px !important;
}

.padding-bottom-50 {
  padding-bottom: 50px !important;
}

.padding-right-50 {
  padding-right: 50px !important;
}

.padding-top-51 {
  padding-top: 51px !important;
}

.padding-left-51 {
  padding-left: 51px !important;
}

.padding-bottom-51 {
  padding-bottom: 51px !important;
}

.padding-right-51 {
  padding-right: 51px !important;
}

.padding-top-52 {
  padding-top: 52px !important;
}

.padding-left-52 {
  padding-left: 52px !important;
}

.padding-bottom-52 {
  padding-bottom: 52px !important;
}

.padding-right-52 {
  padding-right: 52px !important;
}

.padding-top-53 {
  padding-top: 53px !important;
}

.padding-left-53 {
  padding-left: 53px !important;
}

.padding-bottom-53 {
  padding-bottom: 53px !important;
}

.padding-right-53 {
  padding-right: 53px !important;
}

.padding-top-54 {
  padding-top: 54px !important;
}

.padding-left-54 {
  padding-left: 54px !important;
}

.padding-bottom-54 {
  padding-bottom: 54px !important;
}

.padding-right-54 {
  padding-right: 54px !important;
}

.padding-top-55 {
  padding-top: 55px !important;
}

.padding-left-55 {
  padding-left: 55px !important;
}

.padding-bottom-55 {
  padding-bottom: 55px !important;
}

.padding-right-55 {
  padding-right: 55px !important;
}

.padding-top-56 {
  padding-top: 56px !important;
}

.padding-left-56 {
  padding-left: 56px !important;
}

.padding-bottom-56 {
  padding-bottom: 56px !important;
}

.padding-right-56 {
  padding-right: 56px !important;
}

.padding-top-57 {
  padding-top: 57px !important;
}

.padding-left-57 {
  padding-left: 57px !important;
}

.padding-bottom-57 {
  padding-bottom: 57px !important;
}

.padding-right-57 {
  padding-right: 57px !important;
}

.padding-top-58 {
  padding-top: 58px !important;
}

.padding-left-58 {
  padding-left: 58px !important;
}

.padding-bottom-58 {
  padding-bottom: 58px !important;
}

.padding-right-58 {
  padding-right: 58px !important;
}

.padding-top-59 {
  padding-top: 59px !important;
}

.padding-left-59 {
  padding-left: 59px !important;
}

.padding-bottom-59 {
  padding-bottom: 59px !important;
}

.padding-right-59 {
  padding-right: 59px !important;
}

.padding-top-60 {
  padding-top: 60px !important;
}

.padding-left-60 {
  padding-left: 60px !important;
}

.padding-bottom-60 {
  padding-bottom: 60px !important;
}

.padding-right-60 {
  padding-right: 60px !important;
}

.padding-top-61 {
  padding-top: 61px !important;
}

.padding-left-61 {
  padding-left: 61px !important;
}

.padding-bottom-61 {
  padding-bottom: 61px !important;
}

.padding-right-61 {
  padding-right: 61px !important;
}

.padding-top-62 {
  padding-top: 62px !important;
}

.padding-left-62 {
  padding-left: 62px !important;
}

.padding-bottom-62 {
  padding-bottom: 62px !important;
}

.padding-right-62 {
  padding-right: 62px !important;
}

.padding-top-63 {
  padding-top: 63px !important;
}

.padding-left-63 {
  padding-left: 63px !important;
}

.padding-bottom-63 {
  padding-bottom: 63px !important;
}

.padding-right-63 {
  padding-right: 63px !important;
}

.padding-top-64 {
  padding-top: 64px !important;
}

.padding-left-64 {
  padding-left: 64px !important;
}

.padding-bottom-64 {
  padding-bottom: 64px !important;
}

.padding-right-64 {
  padding-right: 64px !important;
}

.padding-top-65 {
  padding-top: 65px !important;
}

.padding-left-65 {
  padding-left: 65px !important;
}

.padding-bottom-65 {
  padding-bottom: 65px !important;
}

.padding-right-65 {
  padding-right: 65px !important;
}

.padding-top-66 {
  padding-top: 66px !important;
}

.padding-left-66 {
  padding-left: 66px !important;
}

.padding-bottom-66 {
  padding-bottom: 66px !important;
}

.padding-right-66 {
  padding-right: 66px !important;
}

.padding-top-67 {
  padding-top: 67px !important;
}

.padding-left-67 {
  padding-left: 67px !important;
}

.padding-bottom-67 {
  padding-bottom: 67px !important;
}

.padding-right-67 {
  padding-right: 67px !important;
}

.padding-top-68 {
  padding-top: 68px !important;
}

.padding-left-68 {
  padding-left: 68px !important;
}

.padding-bottom-68 {
  padding-bottom: 68px !important;
}

.padding-right-68 {
  padding-right: 68px !important;
}

.padding-top-69 {
  padding-top: 69px !important;
}

.padding-left-69 {
  padding-left: 69px !important;
}

.padding-bottom-69 {
  padding-bottom: 69px !important;
}

.padding-right-69 {
  padding-right: 69px !important;
}

.padding-top-70 {
  padding-top: 70px !important;
}

.padding-left-70 {
  padding-left: 70px !important;
}

.padding-bottom-70 {
  padding-bottom: 70px !important;
}

.padding-right-70 {
  padding-right: 70px !important;
}

.padding-top-71 {
  padding-top: 71px !important;
}

.padding-left-71 {
  padding-left: 71px !important;
}

.padding-bottom-71 {
  padding-bottom: 71px !important;
}

.padding-right-71 {
  padding-right: 71px !important;
}

.padding-top-72 {
  padding-top: 72px !important;
}

.padding-left-72 {
  padding-left: 72px !important;
}

.padding-bottom-72 {
  padding-bottom: 72px !important;
}

.padding-right-72 {
  padding-right: 72px !important;
}

.padding-top-73 {
  padding-top: 73px !important;
}

.padding-left-73 {
  padding-left: 73px !important;
}

.padding-bottom-73 {
  padding-bottom: 73px !important;
}

.padding-right-73 {
  padding-right: 73px !important;
}

.padding-top-74 {
  padding-top: 74px !important;
}

.padding-left-74 {
  padding-left: 74px !important;
}

.padding-bottom-74 {
  padding-bottom: 74px !important;
}

.padding-right-74 {
  padding-right: 74px !important;
}

.padding-top-75 {
  padding-top: 75px !important;
}

.padding-left-75 {
  padding-left: 75px !important;
}

.padding-bottom-75 {
  padding-bottom: 75px !important;
}

.padding-right-75 {
  padding-right: 75px !important;
}

.padding-top-76 {
  padding-top: 76px !important;
}

.padding-left-76 {
  padding-left: 76px !important;
}

.padding-bottom-76 {
  padding-bottom: 76px !important;
}

.padding-right-76 {
  padding-right: 76px !important;
}

.padding-top-77 {
  padding-top: 77px !important;
}

.padding-left-77 {
  padding-left: 77px !important;
}

.padding-bottom-77 {
  padding-bottom: 77px !important;
}

.padding-right-77 {
  padding-right: 77px !important;
}

.padding-top-78 {
  padding-top: 78px !important;
}

.padding-left-78 {
  padding-left: 78px !important;
}

.padding-bottom-78 {
  padding-bottom: 78px !important;
}

.padding-right-78 {
  padding-right: 78px !important;
}

.padding-top-79 {
  padding-top: 79px !important;
}

.padding-left-79 {
  padding-left: 79px !important;
}

.padding-bottom-79 {
  padding-bottom: 79px !important;
}

.padding-right-79 {
  padding-right: 79px !important;
}

.padding-top-80 {
  padding-top: 80px !important;
}

.padding-left-80 {
  padding-left: 80px !important;
}

.padding-bottom-80 {
  padding-bottom: 80px !important;
}

.padding-right-80 {
  padding-right: 80px !important;
}

@-webkit-keyframes updow {
  50% {
    -webkit-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  0%, 100% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}
@keyframes updow {
  50% {
    -webkit-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  0%, 100% {
    -webkit-transform: translateY(0px);
    -ms-transform: translateY(0px);
    -o-transform: translateY(0px);
    transform: translateY(0px);
  }
}
@-webkit-keyframes fadeleft {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(-15px, 0);
    -ms-transform: translate(-15px, 0);
    -o-transform: translate(-15px, 0);
    transform: translate(-15px, 0);
  }
}
@keyframes fadeleft {
  from {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  to {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(-15px, 0);
    -ms-transform: translate(-15px, 0);
    -o-transform: translate(-15px, 0);
    transform: translate(-15px, 0);
  }
}
@-webkit-keyframes faderight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(15px, 0);
    -ms-transform: translate(15px, 0);
    -o-transform: translate(15px, 0);
    transform: translate(15px, 0);
  }
  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@keyframes faderight {
  from {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: translate(15px, 0);
    -ms-transform: translate(15px, 0);
    -o-transform: translate(15px, 0);
    transform: translate(15px, 0);
  }
  to {
    opacity: 1;
    filter: alpha(opacity=100);
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    -o-transform: translate(0, 0);
    transform: translate(0, 0);
  }
}
@-webkit-keyframes autofill {
  to {
    color: #4f4f4f;
    background: transparent;
  }
}
.hover-zoom {
  position: relative;
  overflow: hidden;
}
.hover-zoom img {
  max-width: 100%;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.hover-zoom:hover img {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
@media (max-width: 991px) {
  .hover-zoom img {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
  .hover-zoom:hover img {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
}
.hover-zoom > * {
  overflow: hidden;
}

.hover-zoom-bg {
  position: relative;
  overflow: hidden;
}
@media (min-width: 1200px) {
  .hover-zoom-bg > div {
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
  }
  .hover-zoom-bg:hover > div {
    -webkit-transform: scale(1.1);
    -ms-transform: scale(1.1);
    -o-transform: scale(1.1);
    transform: scale(1.1);
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .hover-zoom-bg > div {
    -webkit-transition: none;
    -o-transition: none;
    transition: none;
  }
  .hover-zoom-bg:hover > div {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
}

/* 1. form */
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.coupon_code_wrapper #coupon_code {
  cursor: pointer;
  height: 50px;
  padding: 10px 18px;
  background-color: transparent;
  border: 1px solid #dddddd;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2);
}
input[type="text"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="datetime"]::-moz-placeholder,
input[type="datetime-local"]::-moz-placeholder,
input[type="date"]::-moz-placeholder,
input[type="month"]::-moz-placeholder,
input[type="time"]::-moz-placeholder,
input[type="week"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
input[type="email"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="color"]::-moz-placeholder,
.coupon_code_wrapper #coupon_code::-moz-placeholder {
  color: #7e7e7e;
  opacity: 1;
}
input[type="text"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="datetime"]:-ms-input-placeholder,
input[type="datetime-local"]:-ms-input-placeholder,
input[type="date"]:-ms-input-placeholder,
input[type="month"]:-ms-input-placeholder,
input[type="time"]:-ms-input-placeholder,
input[type="week"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="color"]:-ms-input-placeholder,
.coupon_code_wrapper #coupon_code:-ms-input-placeholder {
  color: #7e7e7e;
}
input[type="text"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="datetime"]::-webkit-input-placeholder,
input[type="datetime-local"]::-webkit-input-placeholder,
input[type="date"]::-webkit-input-placeholder,
input[type="month"]::-webkit-input-placeholder,
input[type="time"]::-webkit-input-placeholder,
input[type="week"]::-webkit-input-placeholder,
input[type="number"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder,
input[type="url"]::-webkit-input-placeholder,
input[type="search"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
input[type="color"]::-webkit-input-placeholder,
.coupon_code_wrapper #coupon_code::-webkit-input-placeholder {
  color: #7e7e7e;
}
input[type="text"]:hover,
input[type="password"]:hover,
input[type="datetime"]:hover,
input[type="datetime-local"]:hover,
input[type="date"]:hover,
input[type="month"]:hover,
input[type="time"]:hover,
input[type="week"]:hover,
input[type="number"]:hover,
input[type="email"]:hover,
input[type="url"]:hover,
input[type="search"]:hover,
input[type="tel"]:hover,
input[type="color"]:hover,
.coupon_code_wrapper #coupon_code:hover {
  outline: none;
}
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus,
.coupon_code_wrapper #coupon_code:focus {
  outline: none;
}

textarea {
  height: 150px;
  padding: 10px 18px;
  background-color: transparent;
  border: 1px solid #dddddd;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2);
}
textarea::-moz-placeholder {
  color: #7e7e7e;
  opacity: 1;
}
textarea:-ms-input-placeholder {
  color: #7e7e7e;
}
textarea::-webkit-input-placeholder {
  color: #7e7e7e;
}
textarea:hover {
  outline: none;
}
textarea:focus {
  outline: none;
}

input[type="radio"],
input[type="checkbox"] {
  border-color: #eeeeee;
}

input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield !important;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"],
button[type="button"],
button[type="reset"],
button[type="submit"] {
  color: #fff;
  border: 0;
  cursor: pointer;
  font-size: 14px;
  padding: 10px 24px;
  background-color: #2441e7;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  display: inline-block;
  vertical-align: middle;
}
.lt-ie8 button, .lt-ie8
input[type="button"], .lt-ie8
input[type="reset"], .lt-ie8
input[type="submit"], .lt-ie8
button[type="button"], .lt-ie8
button[type="reset"], .lt-ie8
button[type="submit"] {
  display: inline;
  zoom: 1;
}
button:disabled, button [disabled],
input[type="button"]:disabled,
input[type="button"] [disabled],
input[type="reset"]:disabled,
input[type="reset"] [disabled],
input[type="submit"]:disabled,
input[type="submit"] [disabled],
button[type="button"]:disabled,
button[type="button"] [disabled],
button[type="reset"]:disabled,
button[type="reset"] [disabled],
button[type="submit"]:disabled,
button[type="submit"] [disabled] {
  pointer-events: none;
}
button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
button[type="button"]:hover,
button[type="reset"]:hover,
button[type="submit"]:hover {
  outline: none;
}
button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
button[type="button"]:focus,
button[type="reset"]:focus,
button[type="submit"]:focus {
  outline: none;
}

select {
  outline: none;
  cursor: pointer !important;
  height: 50px;
  color: #6f7074;
  margin-bottom: 20px;
  padding: 0 15px !important;
  font-size: 15px;
  cursor: pointer;
  border: 1px solid #dddddd;
  background-image: url("../images/select.png");
  background-size: 8px;
  background-color: transparent;
  background-position: 95% 50%;
  background-repeat: no-repeat;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.09);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.09);
}
select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
  -webkit-text-shadow: 0 0 0 #000;
  -moz-text-shadow: 0 0 0 #000;
  -ms-text-shadow: 0 0 0 #000;
  -o-text-shadow: 0 0 0 #000;
}
select option:not(:checked) {
  color: #555555;
}
select:hover {
  outline: none;
}
select:focus {
  outline: none;
}
select:active {
  outline: none;
}

.btn, .viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a, .wfg-button, #add_payment_method .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button {
  outline: none !important;
}
.btn.btn-outline, .btn-outline.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline, .btn-outline.wfg-button, #add_payment_method .wc-proceed-to-checkout a.btn-outline.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.btn-outline.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.btn-outline.checkout-button {
  padding: 9px 46px;
  background-color: transparent;
  border: 2px solid #fff;
  color: #fff;
  text-transform: capitalize;
  font-size: 16px;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  text-align: center;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  display: inline-block;
  vertical-align: middle;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.lt-ie8 .btn.btn-outline, .lt-ie8 .btn-outline.viewmore-products-btn, .lt-ie8 .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline, .woocommerce .wishlist_table td.product-add-to-cart .lt-ie8 a.btn-outline, .lt-ie8 .btn-outline.wfg-button, .lt-ie8 #add_payment_method .wc-proceed-to-checkout a.btn-outline.checkout-button, #add_payment_method .wc-proceed-to-checkout .lt-ie8 a.btn-outline.checkout-button, .lt-ie8 .woocommerce-cart .wc-proceed-to-checkout a.btn-outline.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .lt-ie8 a.btn-outline.checkout-button, .lt-ie8 .woocommerce-checkout .wc-proceed-to-checkout a.btn-outline.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .lt-ie8 a.btn-outline.checkout-button {
  display: inline;
  zoom: 1;
}
.btn.btn-outline:hover, .btn-outline.viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline:hover, .btn-outline.wfg-button:hover, #add_payment_method .wc-proceed-to-checkout a.btn-outline.checkout-button:hover, .woocommerce-cart .wc-proceed-to-checkout a.btn-outline.checkout-button:hover, .woocommerce-checkout .wc-proceed-to-checkout a.btn-outline.checkout-button:hover {
  background-color: #fff;
  color: #0a0a0a;
}
.btn.btn-outline:focus, .btn-outline.viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline:focus, .btn-outline.wfg-button:focus, #add_payment_method .wc-proceed-to-checkout a.btn-outline.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout a.btn-outline.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout a.btn-outline.checkout-button:focus {
  background-color: #fff;
  color: #0a0a0a;
}
.btn.btn-outline:active, .btn-outline.viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline:active, .btn-outline.wfg-button:active, #add_payment_method .wc-proceed-to-checkout a.btn-outline.checkout-button:active, .woocommerce-cart .wc-proceed-to-checkout a.btn-outline.checkout-button:active, .woocommerce-checkout .wc-proceed-to-checkout a.btn-outline.checkout-button:active {
  background-color: #fff;
  color: #0a0a0a;
}
.btn.course-share, .course-share.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.course-share, .course-share.wfg-button, #add_payment_method .wc-proceed-to-checkout a.course-share.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.course-share.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.course-share.checkout-button {
  padding: 3px 15px;
  border: 0;
  color: #fff;
  background-color: #2441e7;
  font-size: 15px;
  line-height: 1.2;
  font-weight: 400;
  font-family: "Nunito", Arial, sans-serif;
  text-transform: none;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -moz-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.btn.course-share [class*="icon"], .course-share.viewmore-products-btn [class*="icon"], .woocommerce .wishlist_table td.product-add-to-cart a.course-share [class*="icon"], .course-share.wfg-button [class*="icon"], #add_payment_method .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"], .woocommerce-cart .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"], .woocommerce-checkout .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"] {
  font-weight: 400;
}
.btn.course-share [class*="icon"]:before, .course-share.viewmore-products-btn [class*="icon"]:before, .woocommerce .wishlist_table td.product-add-to-cart a.course-share [class*="icon"]:before, .course-share.wfg-button [class*="icon"]:before, #add_payment_method .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"]:before, .woocommerce-cart .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"]:before, .woocommerce-checkout .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"]:before {
  font-size: 20px;
  margin: 0 10px 0 0;
}
.rtl .btn.course-share [class*="icon"]:before, .rtl .course-share.viewmore-products-btn [class*="icon"]:before, .rtl .woocommerce .wishlist_table td.product-add-to-cart a.course-share [class*="icon"]:before, .woocommerce .wishlist_table td.product-add-to-cart .rtl a.course-share [class*="icon"]:before, .rtl .course-share.wfg-button [class*="icon"]:before, .rtl #add_payment_method .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"]:before, #add_payment_method .wc-proceed-to-checkout .rtl a.course-share.checkout-button [class*="icon"]:before, .rtl .woocommerce-cart .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"]:before, .woocommerce-cart .wc-proceed-to-checkout .rtl a.course-share.checkout-button [class*="icon"]:before, .rtl .woocommerce-checkout .wc-proceed-to-checkout a.course-share.checkout-button [class*="icon"]:before, .woocommerce-checkout .wc-proceed-to-checkout .rtl a.course-share.checkout-button [class*="icon"]:before {
  margin: 0 0 0 10px;
}
.btn.btn-ct-link, .btn-ct-link.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link, .btn-ct-link.wfg-button, #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button {
  padding: 0;
  border: 0;
  height: 30px;
  font-weight: 600;
  color: #0a0a0a;
  text-transform: none;
  font-family: "Nunito", Arial, sans-serif;
  background-color: transparent;
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -moz-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
}
.btn.btn-ct-link:hover, .btn-ct-link.viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link:hover, .btn-ct-link.wfg-button:hover, #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button:hover, .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button:hover, .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button:hover {
  color: #0a0a0a;
}
.btn.btn-ct-link:focus, .btn-ct-link.viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link:focus, .btn-ct-link.wfg-button:focus, #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button:focus {
  color: #0a0a0a;
}
.btn.btn-ct-link:active, .btn-ct-link.viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link:active, .btn-ct-link.wfg-button:active, #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button:active, .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button:active, .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button:active {
  color: #0a0a0a;
}
.btn.btn-ct-link [class*="icon"], .btn-ct-link.viewmore-products-btn [class*="icon"], .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link [class*="icon"], .btn-ct-link.wfg-button [class*="icon"], #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"], .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"], .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"] {
  font-weight: 400;
  width: 30px;
  height: 30px;
  margin: -6px 5px 0 0;
}
.rtl .btn.btn-ct-link [class*="icon"], .rtl .btn-ct-link.viewmore-products-btn [class*="icon"], .rtl .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link [class*="icon"], .woocommerce .wishlist_table td.product-add-to-cart .rtl a.btn-ct-link [class*="icon"], .rtl .btn-ct-link.wfg-button [class*="icon"], .rtl #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"], #add_payment_method .wc-proceed-to-checkout .rtl a.btn-ct-link.checkout-button [class*="icon"], .rtl .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"], .woocommerce-cart .wc-proceed-to-checkout .rtl a.btn-ct-link.checkout-button [class*="icon"], .rtl .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"], .woocommerce-checkout .wc-proceed-to-checkout .rtl a.btn-ct-link.checkout-button [class*="icon"] {
  margin: -6px 0 0 5px;
}
.btn.btn-ct-link [class*="icon"]:before, .btn-ct-link.viewmore-products-btn [class*="icon"]:before, .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link [class*="icon"]:before, .btn-ct-link.wfg-button [class*="icon"]:before, #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"]:before, .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"]:before, .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"]:before {
  font-size: 22px;
  margin: 0 15px 0 0;
}
.rtl .btn.btn-ct-link [class*="icon"]:before, .rtl .btn-ct-link.viewmore-products-btn [class*="icon"]:before, .rtl .woocommerce .wishlist_table td.product-add-to-cart a.btn-ct-link [class*="icon"]:before, .woocommerce .wishlist_table td.product-add-to-cart .rtl a.btn-ct-link [class*="icon"]:before, .rtl .btn-ct-link.wfg-button [class*="icon"]:before, .rtl #add_payment_method .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"]:before, #add_payment_method .wc-proceed-to-checkout .rtl a.btn-ct-link.checkout-button [class*="icon"]:before, .rtl .woocommerce-cart .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"]:before, .woocommerce-cart .wc-proceed-to-checkout .rtl a.btn-ct-link.checkout-button [class*="icon"]:before, .rtl .woocommerce-checkout .wc-proceed-to-checkout a.btn-ct-link.checkout-button [class*="icon"]:before, .woocommerce-checkout .wc-proceed-to-checkout .rtl a.btn-ct-link.checkout-button [class*="icon"]:before {
  margin: 0 0 0 15px;
}
.btn.btn-read-more, .btn-read-more.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more, .btn-read-more.wfg-button, #add_payment_method .wc-proceed-to-checkout a.btn-read-more.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.checkout-button {
  border: 0;
  outline: none;
  background-color: #fff;
  color: #2441e7;
  font-size: 14px;
  line-height: normal;
  text-transform: none;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  display: inline-block;
  vertical-align: middle;
  padding: 13px 28px 14px 40px;
}
.lt-ie8 .btn.btn-read-more, .lt-ie8 .btn-read-more.viewmore-products-btn, .lt-ie8 .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more, .woocommerce .wishlist_table td.product-add-to-cart .lt-ie8 a.btn-read-more, .lt-ie8 .btn-read-more.wfg-button, .lt-ie8 #add_payment_method .wc-proceed-to-checkout a.btn-read-more.checkout-button, #add_payment_method .wc-proceed-to-checkout .lt-ie8 a.btn-read-more.checkout-button, .lt-ie8 .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .lt-ie8 a.btn-read-more.checkout-button, .lt-ie8 .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .lt-ie8 a.btn-read-more.checkout-button {
  display: inline;
  zoom: 1;
}
.rtl .btn.btn-read-more, .rtl .btn-read-more.viewmore-products-btn, .rtl .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more, .woocommerce .wishlist_table td.product-add-to-cart .rtl a.btn-read-more, .rtl .btn-read-more.wfg-button, .rtl #add_payment_method .wc-proceed-to-checkout a.btn-read-more.checkout-button, #add_payment_method .wc-proceed-to-checkout .rtl a.btn-read-more.checkout-button, .rtl .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .rtl a.btn-read-more.checkout-button, .rtl .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .rtl a.btn-read-more.checkout-button {
  padding: 13px 40px 14px 28px;
}
.btn.btn-read-more [class*="icon"]:before, .btn-read-more.viewmore-products-btn [class*="icon"]:before, .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more [class*="icon"]:before, .btn-read-more.wfg-button [class*="icon"]:before, #add_payment_method .wc-proceed-to-checkout a.btn-read-more.checkout-button [class*="icon"]:before, .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.checkout-button [class*="icon"]:before, .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.checkout-button [class*="icon"]:before {
  margin: 0;
  font-size: 17px;
  margin-left: 10px;
}
.rtl .btn.btn-read-more [class*="icon"]:before, .rtl .btn-read-more.viewmore-products-btn [class*="icon"]:before, .rtl .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more [class*="icon"]:before, .woocommerce .wishlist_table td.product-add-to-cart .rtl a.btn-read-more [class*="icon"]:before, .rtl .btn-read-more.wfg-button [class*="icon"]:before, .rtl #add_payment_method .wc-proceed-to-checkout a.btn-read-more.checkout-button [class*="icon"]:before, #add_payment_method .wc-proceed-to-checkout .rtl a.btn-read-more.checkout-button [class*="icon"]:before, .rtl .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.checkout-button [class*="icon"]:before, .woocommerce-cart .wc-proceed-to-checkout .rtl a.btn-read-more.checkout-button [class*="icon"]:before, .rtl .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.checkout-button [class*="icon"]:before, .woocommerce-checkout .wc-proceed-to-checkout .rtl a.btn-read-more.checkout-button [class*="icon"]:before {
  margin-right: 10px;
  margin-left: inherit;
}
.btn.btn-read-more.style2, .btn-read-more.style2.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more.style2, .btn-read-more.style2.wfg-button, #add_payment_method .wc-proceed-to-checkout a.btn-read-more.style2.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.style2.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.style2.checkout-button {
  color: #051925;
}
.btn.btn-read-more.style3, .btn-read-more.style3.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more.style3, .btn-read-more.style3.wfg-button, #add_payment_method .wc-proceed-to-checkout a.btn-read-more.style3.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.style3.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.style3.checkout-button {
  padding: 0;
  margin: 0;
  background-color: transparent;
}
.btn.btn-read-more.style4, .btn-read-more.style4.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-read-more.style4, .btn-read-more.style4.wfg-button, #add_payment_method .wc-proceed-to-checkout a.btn-read-more.style4.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.btn-read-more.style4.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.btn-read-more.style4.checkout-button {
  outline: none;
}

.btn-outline .elementor-button {
  background-color: transparent;
}
.btn-outline .elementor-button [class*="icon"]:before {
  font-size: 15px;
}

.btn-outline-light {
  color: #fff;
  background-color: transparent;
  border-color: #fff;
}
.btn-outline-light:hover, .btn-outline-light:focus, .btn-outline-light:active, .btn-outline-light.active {
  color: #121212;
  background-color: #fff;
  border-color: #121212;
}
.open .btn-outline-light.dropdown-toggle {
  color: #121212;
  background-color: #fff;
  border-color: #121212;
}
.btn-outline-light:active, .btn-outline-light.active {
  background-image: none;
}
.open .btn-outline-light.dropdown-toggle {
  background-image: none;
}
.btn-outline-light.disabled, .btn-outline-light.disabled:hover, .btn-outline-light.disabled:focus, .btn-outline-light.disabled:active, .btn-outline-light.disabled.active, .btn-outline-light[disabled], .btn-outline-light[disabled]:hover, .btn-outline-light[disabled]:focus, .btn-outline-light[disabled]:active, .btn-outline-light[disabled].active, fieldset[disabled] .btn-outline-light, fieldset[disabled] .btn-outline-light:hover, fieldset[disabled] .btn-outline-light:focus, fieldset[disabled] .btn-outline-light:active, fieldset[disabled] .btn-outline-light.active {
  background-color: transparent;
  border-color: #fff;
}
.btn-outline-light .badge {
  color: transparent;
  background-color: #fff;
}

.btn-shop {
  padding: 0 0 3px;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  border-bottom: 2px solid #f43434;
}

.btn-outline.btn-default {
  background: transparent;
  border-color: #fff;
  color: #fff;
}
.btn-outline.btn-default:hover {
  color: #2441e7;
  border-color: #fff;
  background: #fff;
}
.btn-outline.btn-primary {
  background: transparent;
  border-color: #121212;
  color: #121212;
}
.btn-outline.btn-primary:hover {
  color: #fff;
  border-color: #121212;
  background: #121212;
}
.btn-outline.btn-success, .btn-outline.wfg-button.wfg-add-gifts {
  background: transparent;
  border-color: #5cb85c;
  color: #5cb85c;
}
.btn-outline.btn-success:hover, .btn-outline.wfg-button.wfg-add-gifts:hover {
  color: #fff;
  border-color: #5cb85c;
  background: #5cb85c;
}
.btn-outline.btn-info {
  background: transparent;
  border-color: #5bc0de;
  color: #5bc0de;
}
.btn-outline.btn-info:hover {
  color: #fff;
  border-color: #5bc0de;
  background: #5bc0de;
}
.btn-outline.btn-danger, .btn-outline.wfg-button.wfg-no-thanks {
  background: transparent;
  border-color: #e44343;
  color: #e44343;
}
.btn-outline.btn-danger:hover, .btn-outline.wfg-button.wfg-no-thanks:hover {
  color: #fff;
  border-color: #e44343;
  background: #e44343;
}
.btn-outline.btn-warning {
  background: transparent;
  border-color: #f0ad4e;
  color: #f0ad4e;
}
.btn-outline.btn-warning:hover {
  color: #fff;
  border-color: #f0ad4e;
  background: #f0ad4e;
}

.btn-inverse.btn-primary:hover {
  color: #121212;
  background: #fff;
}
.btn-inverse.btn-success:hover, .btn-inverse.wfg-button.wfg-add-gifts:hover {
  color: #5cb85c;
  background: #fff;
}
.btn-inverse.btn-info:hover {
  color: #5bc0de;
  background: #fff;
}
.btn-inverse.btn-danger:hover, .btn-inverse.wfg-button.wfg-no-thanks:hover {
  color: #e44343;
  background: #fff;
}
.btn-inverse.btn-warning:hover {
  color: #f0ad4e;
  background: #fff;
}
.btn-inverse.btn-theme:hover, .btn-inverse.viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a.btn-inverse:hover {
  color: #2441e7;
  background: #fff;
}

.btn-compare.btn-outline {
  color: #4c4c4c;
  background: #fff;
  border: 1px solid #e9e9e9;
  height: 45px;
}
.btn-compare.btn-outline:hover, .btn-compare.btn-outline:active {
  color: #fff;
  background: #4c4c4c;
  border-color: #4c4c4c;
}

.reamore {
  font-size: 14px;
  font-weight: 500;
  color: #2441e7 !important;
  text-transform: uppercase;
  padding: 0 0 4px;
  border-bottom: 2px solid #2441e7;
}
.reamore i {
  margin-left: 8px;
}
.rtl .reamore i {
  margin-right: 8px;
  margin-left: inherit;
}

.apus-loadmore-btn {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  background: #2441e7;
  color: #fff;
  text-transform: uppercase;
  padding: 12px 30px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.apus-loadmore-btn:hover, .apus-loadmore-btn:active {
  color: #fff;
  background: #121212;
}

.viewmore-products-btn {
  position: relative;
}
.viewmore-products-btn:before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  z-index: 2;
  opacity: 0;
  filter: alpha(opacity=0);
  background: rgba(255, 255, 255, 0.9) url(../images/loading-quick.gif) no-repeat scroll center center/20px auto;
}
.viewmore-products-btn.loading:before {
  opacity: 1;
  filter: alpha(opacity=100);
}

button:focus,
.btn:focus,
.viewmore-products-btn:focus,
.woocommerce .wishlist_table td.product-add-to-cart a:focus,
.wfg-button:focus,
#add_payment_method .wc-proceed-to-checkout a.checkout-button:focus,
.woocommerce-cart .wc-proceed-to-checkout a.checkout-button:focus,
.woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:focus {
  outline: none !important;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

.btn-link {
  color: #2441e7;
  font-size: 14px;
  font-weight: normal;
  text-transform: uppercase;
}
.btn-link:hover, .btn-link:active {
  text-decoration: underline;
}

.radius-0 {
  border-radius: 0 !important;
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  -ms-border-radius: 0 !important;
  -o-border-radius: 0 !important;
}

.radius-circle {
  border-radius: 100px !important;
  -webkit-border-radius: 100px !important;
  -moz-border-radius: 100px !important;
  -ms-border-radius: 100px !important;
  -o-border-radius: 100px !important;
}

.btn-3d {
  -webkit-box-shadow: 0 0 10px 0 rgba(36, 65, 231, 0.8);
  box-shadow: 0 0 10px 0 rgba(36, 65, 231, 0.8);
}

.read-more {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #2441e7;
}

.btn-white {
  background: #fff;
  color: #2441e7;
  border-color: #fff;
}
.btn-white:active, .btn-white:hover {
  color: #2441e7;
  background: #f2f2f2;
  border-color: #f2f2f2;
}

.btn-white.btn-br-white {
  background: #fff;
  color: #252525;
  border-color: #fff;
}
.btn-white.btn-br-white:active, .btn-white.btn-br-white:hover {
  color: #252525;
  background: #d9d9d9;
  border-color: #d9d9d9;
}

.btn-gradient {
  border: none !important;
  overflow: hidden;
  background-image: -webkit-linear-gradient(left, #ff1053 0%, #2441e7 100%);
  background-image: -o-linear-gradient(left, #ff1053 0%, #2441e7 100%);
  background-image: linear-gradient(to right, #ff1053 0%, #2441e7 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFF1053', endColorstr='#FF2441E7', GradientType=1);
  position: relative;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  padding: 12px 30px;
}
.btn-gradient:before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  opacity: 0;
  filter: alpha(opacity=0);
  background-image: -webkit-linear-gradient(left, #dc003e 0%, #152fc3 100%);
  background-image: -o-linear-gradient(left, #dc003e 0%, #152fc3 100%);
  background-image: linear-gradient(to right, #dc003e 0%, #152fc3 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFDC003E', endColorstr='#FF152FC3', GradientType=1);
  -webkit-transition: opacity 0.5s ease-out;
  -o-transition: opacity 0.5s ease-out;
  transition: opacity 0.5s ease-out;
}
.btn-gradient > * {
  position: relative;
  z-index: 2;
}
.btn-gradient:hover, .btn-gradient:active {
  background-image: -webkit-linear-gradient(left, #ff1053 0%, #2441e7 100%);
  background-image: -o-linear-gradient(left, #ff1053 0%, #2441e7 100%);
  background-image: linear-gradient(to right, #ff1053 0%, #2441e7 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFF1053', endColorstr='#FF2441E7', GradientType=1);
}
.btn-gradient:hover:before, .btn-gradient:active:before {
  opacity: 1;
  filter: alpha(opacity=100);
}
.btn-gradient.btn-white {
  color: #252525;
}
.btn-gradient.btn-white:before {
  content: '';
  border-radius: 0px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  width: auto;
  height: auto;
  top: 2px;
  right: 2px;
  left: 2px;
  bottom: 2px;
  opacity: 1;
  filter: alpha(opacity=100);
  background: #fff;
}
.btn-gradient.btn-white:hover, .btn-gradient.btn-white:active {
  color: #fff !important;
}
.btn-gradient.btn-white:hover:before, .btn-gradient.btn-white:active:before {
  opacity: 0;
  filter: alpha(opacity=0);
}

.btn-readmore {
  margin-top: 30px;
  color: #7e7e7e;
  font-weight: 400;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  font-size: 14px;
  position: relative;
  padding: 8px 30px;
  background-color: transparent;
  border: 2px solid #eeeeee;
  display: inline-block;
  vertical-align: middle;
  border-radius: 30px;
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -ms-border-radius: 30px;
  -o-border-radius: 30px;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.lt-ie8 .btn-readmore {
  display: inline;
  zoom: 1;
}
.btn-readmore:hover {
  color: #fff;
  border-color: #2441e7;
  background-color: #2441e7;
}
.btn-readmore:hover i {
  color: #fff !important;
}
.btn-readmore:focus {
  color: #fff;
  border-color: #2441e7;
  background-color: #2441e7;
}
.btn-readmore:focus i {
  color: #fff !important;
}
.btn-readmore:active {
  color: #fff;
  border-color: #2441e7;
  background-color: #2441e7;
}
.btn-readmore:active i {
  color: #fff !important;
}
.btn-readmore i:before {
  font-size: 14px;
  margin-left: 10px;
}
.rtl .btn-readmore i:before {
  margin-right: 10px;
  margin-left: inherit;
}

.btn-lighten {
  border-color: #fff;
  color: #fff;
  background: transparent;
}
.btn-lighten:hover {
  color: #fff;
  background: transparent;
  border-color: #fff;
}

.btn-outline.btn-white {
  background: transparent;
  color: #252525;
  border-color: #2441e7;
}
.btn-outline.btn-white:active, .btn-outline.btn-white:hover {
  color: #fff;
  background: #2441e7;
  border-color: #2441e7;
}

.btn-pink {
  color: #fff;
  background-color: #e3a3a2;
  border-color: #e3a3a2;
}
.btn-pink:hover, .btn-pink:focus, .btn-pink:active, .btn-pink.active, .open > .btn-pink.dropdown-toggle {
  color: #fff;
  background-color: #d77c7b;
  border-color: #d77c7b;
}
.btn-pink:active, .btn-pink.active, .open > .btn-pink.dropdown-toggle {
  background-image: none;
}
.btn-pink.disabled, .btn-pink.disabled:hover, .btn-pink.disabled:focus, .btn-pink.disabled:active, .btn-pink.disabled.active, .btn-pink[disabled], .btn-pink[disabled]:hover, .btn-pink[disabled]:focus, .btn-pink[disabled]:active, .btn-pink[disabled].active, fieldset[disabled] .btn-pink, fieldset[disabled] .btn-pink:hover, fieldset[disabled] .btn-pink:focus, fieldset[disabled] .btn-pink:active, fieldset[disabled] .btn-pink.active {
  background-color: #e3a3a2;
  border-color: #e3a3a2;
}
.btn-pink .badge {
  color: #e3a3a2;
  background-color: #fff;
}

.btn-primary.btn-inverse:active, .btn-primary.btn-inverse:hover {
  background: #fff !important;
  color: #121212 !important;
  border-color: #121212 !important;
}

.btn-theme, .viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a {
  color: #fff;
  background-color: #2441e7;
  border-color: #2441e7;
}
.btn-theme:hover, .viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a:hover, .btn-theme:focus, .viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a:focus, .btn-theme:active, .viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a:active, .btn-theme.active, .active.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.active, .open > .btn-theme.dropdown-toggle, .open > .dropdown-toggle.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart .open > a.dropdown-toggle {
  color: #fff;
  background-color: #152fc3;
  border-color: #152fc3;
}
.btn-theme:active, .viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a:active, .btn-theme.active, .active.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.active, .open > .btn-theme.dropdown-toggle, .open > .dropdown-toggle.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart .open > a.dropdown-toggle {
  background-image: none;
}
.btn-theme.disabled, .disabled.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.disabled, .btn-theme.disabled:hover, .disabled.viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a.disabled:hover, .btn-theme.disabled:focus, .disabled.viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a.disabled:focus, .btn-theme.disabled:active, .disabled.viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a.disabled:active, .btn-theme.disabled.active, .disabled.active.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.disabled.active, .btn-theme[disabled], [disabled].viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a[disabled], .btn-theme[disabled]:hover, [disabled].viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a[disabled]:hover, .btn-theme[disabled]:focus, [disabled].viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a[disabled]:focus, .btn-theme[disabled]:active, [disabled].viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a[disabled]:active, .btn-theme[disabled].active, [disabled].active.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a[disabled].active, fieldset[disabled] .btn-theme, fieldset[disabled] .viewmore-products-btn, fieldset[disabled] .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart fieldset[disabled] a, fieldset[disabled] .btn-theme:hover, fieldset[disabled] .viewmore-products-btn:hover, fieldset[disabled] .woocommerce .wishlist_table td.product-add-to-cart a:hover, .woocommerce .wishlist_table td.product-add-to-cart fieldset[disabled] a:hover, fieldset[disabled] .btn-theme:focus, fieldset[disabled] .viewmore-products-btn:focus, fieldset[disabled] .woocommerce .wishlist_table td.product-add-to-cart a:focus, .woocommerce .wishlist_table td.product-add-to-cart fieldset[disabled] a:focus, fieldset[disabled] .btn-theme:active, fieldset[disabled] .viewmore-products-btn:active, fieldset[disabled] .woocommerce .wishlist_table td.product-add-to-cart a:active, .woocommerce .wishlist_table td.product-add-to-cart fieldset[disabled] a:active, fieldset[disabled] .btn-theme.active, fieldset[disabled] .active.viewmore-products-btn, fieldset[disabled] .woocommerce .wishlist_table td.product-add-to-cart a.active, .woocommerce .wishlist_table td.product-add-to-cart fieldset[disabled] a.active {
  background-color: #2441e7;
  border-color: #2441e7;
}
.btn-theme .badge, .viewmore-products-btn .badge, .woocommerce .wishlist_table td.product-add-to-cart a .badge {
  color: #2441e7;
  background-color: #fff;
}
.btn-theme:active, .viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a:active, .btn-theme:hover, .viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a:hover {
  color: #fff !important;
}

.btn-dark {
  color: #252525;
  background-color: #cccccc;
  border-color: #cccccc;
}
.btn-dark:hover, .btn-dark:focus, .btn-dark:active, .btn-dark.active, .open > .btn-dark.dropdown-toggle {
  color: #252525;
  background-color: #b3b3b3;
  border-color: #b3b3b3;
}
.btn-dark:active, .btn-dark.active, .open > .btn-dark.dropdown-toggle {
  background-image: none;
}
.btn-dark.disabled, .btn-dark.disabled:hover, .btn-dark.disabled:focus, .btn-dark.disabled:active, .btn-dark.disabled.active, .btn-dark[disabled], .btn-dark[disabled]:hover, .btn-dark[disabled]:focus, .btn-dark[disabled]:active, .btn-dark[disabled].active, fieldset[disabled] .btn-dark, fieldset[disabled] .btn-dark:hover, fieldset[disabled] .btn-dark:focus, fieldset[disabled] .btn-dark:active, fieldset[disabled] .btn-dark.active {
  background-color: #cccccc;
  border-color: #cccccc;
}
.btn-dark .badge {
  color: #cccccc;
  background-color: #252525;
}
.btn-dark:active, .btn-dark:hover {
  color: #181818 !important;
}

.btn-theme-second {
  color: #fff;
  background-color: #ff1053;
  border-color: #ff1053;
}
.btn-theme-second:hover, .btn-theme-second:focus, .btn-theme-second:active, .btn-theme-second.active, .open > .btn-theme-second.dropdown-toggle {
  color: #fff;
  background-color: #dc003e;
  border-color: #dc003e;
}
.btn-theme-second:active, .btn-theme-second.active, .open > .btn-theme-second.dropdown-toggle {
  background-image: none;
}
.btn-theme-second.disabled, .btn-theme-second.disabled:hover, .btn-theme-second.disabled:focus, .btn-theme-second.disabled:active, .btn-theme-second.disabled.active, .btn-theme-second[disabled], .btn-theme-second[disabled]:hover, .btn-theme-second[disabled]:focus, .btn-theme-second[disabled]:active, .btn-theme-second[disabled].active, fieldset[disabled] .btn-theme-second, fieldset[disabled] .btn-theme-second:hover, fieldset[disabled] .btn-theme-second:focus, fieldset[disabled] .btn-theme-second:active, fieldset[disabled] .btn-theme-second.active {
  background-color: #ff1053;
  border-color: #ff1053;
}
.btn-theme-second .badge {
  color: #ff1053;
  background-color: #fff;
}
.btn-theme-second:active, .btn-theme-second:hover {
  color: #fff;
}

.btn-theme.btn-outline, .btn-outline.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline {
  color: #2441e7;
  border-color: #2441e7;
  background: transparent;
}
.btn-theme.btn-outline:hover, .btn-outline.viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline:hover, .btn-theme.btn-outline:active, .btn-outline.viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a.btn-outline:active {
  color: #fff;
  background: #2441e7;
  border-color: #2441e7;
}

.more-link {
  color: #2441e7;
  display: inline-block;
  font-weight: normal;
  margin: 10px 0;
  text-transform: capitalize;
}
.more-link:hover {
  text-decoration: none;
}

.btn-shaded-sm {
  position: relative;
}
.btn-shaded-sm:before {
  content: '';
  position: absolute;
  top: 0px;
  left: 0px;
  border-width: 20px 10px;
  border-style: solid;
  border-color: transparent transparent transparent rgba(255, 255, 255, 0.4);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  border-color: #eeeeee;
  -webkit-text-fill-color: #6f7074;
  -webkit-box-shadow: 0 0 0px 1000px #fff inset;
  -webkit-transition: background-color 5000s ease-in-out 0s;
  -o-transition: background-color 5000s ease-in-out 0s;
  transition: background-color 5000s ease-in-out 0s;
}

/* Search
------------------------------------------------*/
.search-popup .dropdown-menu {
  padding: 10px;
}

.btn-action {
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  font-size: 11px;
  font-weight: 900;
  line-height: 30px;
  margin-bottom: 0;
  padding: 0px 10px;
  text-align: center;
  text-transform: uppercase;
  -webkit-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;
  vertical-align: middle;
  white-space: nowrap;
}

.searchform .input-search {
  padding: 15px;
  border-right: 0;
  line-height: 1.5;
}
.rtl .searchform .input-search {
  border-left: 0;
  border-right: inherit;
}
.searchform .btn-search {
  vertical-align: top;
  color: #adafac;
  padding: 12px 8px;
}
.searchform .input-group-btn {
  line-height: 100%;
}

.search-category .btn, .search-category .viewmore-products-btn, .search-category .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .search-category a, .search-category .wfg-button, .search-category #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .search-category a.checkout-button, .search-category .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .search-category a.checkout-button, .search-category .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .search-category a.checkout-button {
  margin-left: 10px !important;
  border-radius: 0px !important;
  -webkit-border-radius: 0px !important;
  -moz-border-radius: 0px !important;
  -ms-border-radius: 0px !important;
  -o-border-radius: 0px !important;
}
.rtl .search-category .btn, .rtl .search-category .viewmore-products-btn, .rtl .search-category .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .rtl .search-category a, .rtl .search-category .wfg-button, .rtl .search-category #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .rtl .search-category a.checkout-button, .rtl .search-category .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .rtl .search-category a.checkout-button, .rtl .search-category .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .rtl .search-category a.checkout-button {
  margin-right: 10px !important;
  margin-left: inherit;
}
.search-category .wpo-search-inner label.form-control {
  border: none;
  border-bottom-right-radius: 0px;
  border-top-right-radius: 0px;
}
.search-category select {
  border: none;
  text-transform: capitalize;
  font-weight: 500;
}

/* comment form
------------------------------------------------*/
.chosen-container {
  width: 100% !important;
}

.input-group-form {
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  background: transparent;
  margin: 0 0 5px 0;
}
.input-group-form .form-control-reversed {
  border: 0px;
  background: #222222;
  color: #cccccc;
  font-size: 14px;
  height: 34px;
}
.input-group-form .form-control-reversed:hover, .input-group-form .form-control-reversed:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.input-group-form .input-group-addon {
  border: 0;
  background: #222222;
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}

.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  margin-left: 0 !important;
}
.rtl .radio input[type="radio"], .rtl
.radio-inline input[type="radio"], .rtl
.checkbox input[type="checkbox"], .rtl
.checkbox-inline input[type="checkbox"] {
  margin-right: 0 !important;
  margin-left: inherit;
}

/* 2. utilities */
/*-------------------------------------------
    No Margin
-------------------------------------------*/
.no-margin {
  margin: 0 !important;
}
.no-margin .pagination {
  margin: 0;
}

/*-------------------------------------------
    No Padding
-------------------------------------------*/
.no-padding {
  padding: 0 !important;
}

.no-position {
  position: static !important;
}

.inline-block {
  display: inline-block;
  vertical-align: middle;
}
.lt-ie8 .inline-block {
  display: inline;
  zoom: 1;
}

.no-background {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: none !important;
}

.no-effect a:before {
  content: none !important;
}

/*------------------------------------*\
    Clear List Style
\*------------------------------------*/
.clear-list {
  padding: 0;
  margin: 0;
  list-style: none;
}

/*-------------------------------------------
    Text Transform
-------------------------------------------*/
.text-uppercase {
  text-transform: uppercase !important;
}

/*-------------------------------------------
    Align
-------------------------------------------*/
.separator_align_center {
  text-align: center !important;
}

.vc_align_right,
.separator_align_right {
  text-align: right !important;
}

.separator_align_left {
  text-align: left !important;
}

/*------------------------------------*\
    Font size heading title
\*------------------------------------*/
.font-size-lg {
  font-size: 48px;
}

.font-size-md {
  font-size: 30px;
}

.font-size-sm {
  font-size: 19px;
}

.font-size-xs {
  font-size: 15px;
}

/*------------------------------------*\
    Border
\*------------------------------------*/
.no-border {
  border: 0px !important;
}

/*------------------------------------*\
    No background
\*------------------------------------*/
.bg-transparent {
  background: transparent !important;
}

/* 3. theme effect */
.effect-1 {
  position: relative;
}
.effect-1:after {
  content: '';
  display: block;
  width: 0px;
  height: 1px;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
  margin: auto;
}
.effect-1:hover:after {
  width: 100%;
  height: 1px;
  background: #2441e7;
}

.zoom-2 {
  overflow: hidden;
  display: block;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}
.zoom-2 img {
  position: relative;
  width: 100%;
  height: auto;
  -webkit-transition: all 0.2s ease-out;
  -o-transition: all 0.2s ease-out;
  transition: all 0.2s ease-out;
  -webkit-transform-origin: center center;
  -moz-transform-origin: center center;
  -ms-transform-origin: center center;
  transform-origin: center center;
}
.zoom-2:hover img {
  -webkit-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

.filter-grayscale, .category-image img {
  -webkit-transition: all 0.6s ease-out 0s;
  -o-transition: all 0.6s ease-out 0s;
  transition: all 0.6s ease-out 0s;
}
.filter-grayscale:hover, .category-image img:hover {
  filter: grayscale(100%);
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
}

.filter-brightness {
  -webkit-transition: all 0.4s ease-out 0s;
  -o-transition: all 0.4s ease-out 0s;
  transition: all 0.4s ease-out 0s;
}
.filter-brightness:hover {
  filter: brightness(0.4);
  -webkit-filter: brightness(0.4);
  -moz-filter: brightness(0.4);
  -o-filter: brightness(0.4);
  -ms-filter: brightness(0.4);
}

.filter-blur {
  -webkit-transition: all 0.6s ease-out 0s;
  -o-transition: all 0.6s ease-out 0s;
  transition: all 0.6s ease-out 0s;
}
.filter-blur:hover {
  filter: blur(5px);
  -webkit-filter: blur(5px);
  -moz-filter: blur(5px);
  -o-filter: blur(5px);
  -ms-filter: blur(5px);
}

.close .fa {
  -webkit-transition: all 1s ease-in-out;
  -o-transition: all 1s ease-in-out;
  transition: all 1s ease-in-out;
}
.close:hover .fa {
  -webkit-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  transform: rotate(360deg);
}

.image-overlay-1:after, .image-overlay-1:before {
  content: "";
  display: block;
  position: absolute;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}
.rtl .image-overlay-1:after, .rtl .image-overlay-1:before {
  right: 0;
  left: auto;
}
.image-overlay-1:after {
  top: -100%;
}
.image-overlay-1:before {
  bottom: -100%;
}
.image-overlay-1:hover:after {
  top: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}
.image-overlay-1:hover:before {
  bottom: -50%;
  opacity: 1;
  filter: alpha(opacity=100);
}

.image-plus-1 {
  position: relative;
}
.image-plus-1::before {
  overflow: hidden;
  position: absolute;
  top: 0;
  content: "";
  z-index: 100;
  width: 100%;
  height: 100%;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  -webkit-transform: scale(1.5);
  -moz-transform: scale(1.5);
  -ms-transform: scale(1.5);
  -o-transform: scale(1.5);
  transform: scale(1.5);
  background: url("../images/plus.png") no-repeat scroll center center/60px 60px rgba(0, 0, 0, 0.6);
}
.rtl .image-plus-1::before {
  right: 0;
  left: auto;
}
.image-plus-1:hover::before {
  visibility: visible;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.image-plus-2 {
  position: relative;
}
.image-plus-2::before {
  content: "";
  z-index: 199;
  top: 0;
  position: absolute;
  background: url("../images/plus.png") no-repeat scroll center center/60px 60px rgba(0, 0, 0, 0.8);
  width: 100%;
  height: 100%;
  left: 0;
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-background-size: 10px 10px, 100% 100%;
  -moz-background-size: 10px 10px, 100% 100%;
  -ms-background-size: 10px 10px, 100% 100%;
  -o-background-size: 10px 10px, 100% 100%;
  background-size: 10px 10px, 100% 100%;
  -webkit-background-origin: padding-box, padding-box;
  -moz-background-origin: padding-box, padding-box;
  -ms-background-origin: padding-box, padding-box;
  -o-background-origin: padding-box, padding-box;
  background-origin: padding-box, padding-box;
}
.rtl .image-plus-2::before {
  right: 0;
  left: auto;
}
.image-plus-2:hover::before {
  opacity: .6;
  visibility: visible;
  -webkit-background-size: 60px 60px, 100% 100%;
  -moz-background-size: 60px 60px, 100% 100%;
  -ms-background-size: 60px 60px, 100% 100%;
  -o-background-size: 60px 60px, 100% 100%;
  background-size: 60px 60px, 100% 100%;
}

.image-plus-3 {
  position: relative;
}
.image-plus-3::before {
  content: "";
  top: 0;
  overflow: hidden;
  position: absolute;
  z-index: 100;
  -webkit-transform: scale(0.5) rotateX(180deg);
  -moz-transform: scale(0.5) rotateX(180deg);
  -ms-transform: scale(0.5) rotateX(180deg);
  -o-transform: scale(0.5) rotateX(180deg);
  transform: scale(0.5) rotateX(180deg);
  width: 100%;
  height: 100%;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease 0s;
  -o-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
  background: rgba(0, 0, 0, 0.8);
}
.rtl .image-plus-3::before {
  right: 0;
  left: auto;
}
.image-plus-3:hover::before {
  visibility: visible;
  opacity: 0.6;
  filter: alpha(opacity=60);
  -webkit-transform: scale(1) rotateX(0deg);
  -moz-transform: scale(1) rotateX(0deg);
  -ms-transform: scale(1) rotateX(0deg);
  -o-transform: scale(1) rotateX(0deg);
  transform: scale(1) rotateX(0deg);
}

.icon-effect-1 {
  position: relative;
}
.icon-effect-1:before {
  content: "";
  display: block;
  -webkit-transform: scale(0.5);
  -ms-transform: scale(0.5);
  -o-transform: scale(0.5);
  transform: scale(0.5);
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), background-color 0.2s cubic-bezier(0.19, 1, 0.22, 1);
  -o-transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), background-color 0.2s cubic-bezier(0.19, 1, 0.22, 1);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), background-color 0.2s cubic-bezier(0.19, 1, 0.22, 1);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  width: 100%;
  height: 100%;
  position: absolute;
  background-color: transparent;
}
.icon-effect-1:hover:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  -webkit-transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), background-color 0.2s cubic-bezier(0.19, 1, 0.22, 1);
  -o-transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), background-color 0.2s cubic-bezier(0.19, 1, 0.22, 1);
  transition: transform 0.5s cubic-bezier(0.19, 1, 0.22, 1), background-color 0.2s cubic-bezier(0.19, 1, 0.22, 1);
}

/* 4. buttons */
.btn, .viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a, .wfg-button, #add_payment_method .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  font-family: "Open Sans";
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  letter-spacing: 0;
  padding: 10px 30px;
  font-size: 14px;
  line-height: 1.71;
  border-radius: 0px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  text-transform: uppercase;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.btn:focus, .viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a:focus, .wfg-button:focus, #add_payment_method .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:focus, .btn:active:focus, .viewmore-products-btn:active:focus, .woocommerce .wishlist_table td.product-add-to-cart a:active:focus, .wfg-button:active:focus, #add_payment_method .wc-proceed-to-checkout a.checkout-button:active:focus, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:active:focus, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:active:focus, .btn.active:focus, .active.viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a.active:focus, .active.wfg-button:focus, #add_payment_method .wc-proceed-to-checkout a.active.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout a.active.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout a.active.checkout-button:focus {
  outline: 0;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn:hover, .viewmore-products-btn:hover, .woocommerce .wishlist_table td.product-add-to-cart a:hover, .wfg-button:hover, #add_payment_method .wc-proceed-to-checkout a.checkout-button:hover, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:hover, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:hover, .btn:focus, .viewmore-products-btn:focus, .woocommerce .wishlist_table td.product-add-to-cart a:focus, .wfg-button:focus, #add_payment_method .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:focus {
  color: #222222;
  text-decoration: none;
}
.btn:active, .viewmore-products-btn:active, .woocommerce .wishlist_table td.product-add-to-cart a:active, .wfg-button:active, #add_payment_method .wc-proceed-to-checkout a.checkout-button:active, .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:active, .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:active, .btn.active, .active.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.active, .active.wfg-button, #add_payment_method .wc-proceed-to-checkout a.active.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.active.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.active.checkout-button {
  outline: 0;
  background-image: none;
}
.btn.disabled, .disabled.viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a.disabled, .disabled.wfg-button, #add_payment_method .wc-proceed-to-checkout a.disabled.checkout-button, .woocommerce-cart .wc-proceed-to-checkout a.disabled.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a.disabled.checkout-button, .btn[disabled], [disabled].viewmore-products-btn, .woocommerce .wishlist_table td.product-add-to-cart a[disabled], [disabled].wfg-button, #add_payment_method .wc-proceed-to-checkout a[disabled].checkout-button, .woocommerce-cart .wc-proceed-to-checkout a[disabled].checkout-button, .woocommerce-checkout .wc-proceed-to-checkout a[disabled].checkout-button, fieldset[disabled] .btn, fieldset[disabled] .viewmore-products-btn, fieldset[disabled] .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart fieldset[disabled] a, fieldset[disabled] .wfg-button, fieldset[disabled] #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout fieldset[disabled] a.checkout-button, fieldset[disabled] .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout fieldset[disabled] a.checkout-button, fieldset[disabled] .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout fieldset[disabled] a.checkout-button {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}
.btn-default:hover, .btn-default:focus, .btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
  color: #333;
  background-color: #e6e6e6;
  border-color: #b3b3b3;
}
.btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
  background-image: none;
}
.btn-default.disabled, .btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled:active, .btn-default.disabled.active, .btn-default[disabled], .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled]:active, .btn-default[disabled].active, fieldset[disabled] .btn-default, fieldset[disabled] .btn-default:hover, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default:active, fieldset[disabled] .btn-default.active {
  background-color: #fff;
  border-color: #ccc;
}
.btn-default .badge {
  color: #fff;
  background-color: #333;
}

.btn-primary {
  color: #fff;
  background-color: #121212;
  border-color: #050505;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: black;
  border-color: black;
}
.btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
  background-image: none;
}
.btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled]:active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary:active, fieldset[disabled] .btn-primary.active {
  background-color: #121212;
  border-color: #050505;
}
.btn-primary .badge {
  color: #121212;
  background-color: #fff;
}

.btn-success, .wfg-button.wfg-add-gifts {
  color: #fff;
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.btn-success:hover, .wfg-button.wfg-add-gifts:hover, .btn-success:focus, .wfg-button.wfg-add-gifts:focus, .btn-success:active, .wfg-button.wfg-add-gifts:active, .btn-success.active, .active.wfg-button.wfg-add-gifts, .open > .btn-success.dropdown-toggle, .open > .dropdown-toggle.wfg-button.wfg-add-gifts {
  color: #fff;
  background-color: #449d44;
  border-color: #3d8b3d;
}
.btn-success:active, .wfg-button.wfg-add-gifts:active, .btn-success.active, .active.wfg-button.wfg-add-gifts, .open > .btn-success.dropdown-toggle, .open > .dropdown-toggle.wfg-button.wfg-add-gifts {
  background-image: none;
}
.btn-success.disabled, .disabled.wfg-button.wfg-add-gifts, .btn-success.disabled:hover, .disabled.wfg-button.wfg-add-gifts:hover, .btn-success.disabled:focus, .disabled.wfg-button.wfg-add-gifts:focus, .btn-success.disabled:active, .disabled.wfg-button.wfg-add-gifts:active, .btn-success.disabled.active, .disabled.active.wfg-button.wfg-add-gifts, .btn-success[disabled], [disabled].wfg-button.wfg-add-gifts, .btn-success[disabled]:hover, [disabled].wfg-button.wfg-add-gifts:hover, .btn-success[disabled]:focus, [disabled].wfg-button.wfg-add-gifts:focus, .btn-success[disabled]:active, [disabled].wfg-button.wfg-add-gifts:active, .btn-success[disabled].active, [disabled].active.wfg-button.wfg-add-gifts, fieldset[disabled] .btn-success, fieldset[disabled] .wfg-button.wfg-add-gifts, fieldset[disabled] .btn-success:hover, fieldset[disabled] .wfg-button.wfg-add-gifts:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .wfg-button.wfg-add-gifts:focus, fieldset[disabled] .btn-success:active, fieldset[disabled] .wfg-button.wfg-add-gifts:active, fieldset[disabled] .btn-success.active, fieldset[disabled] .active.wfg-button.wfg-add-gifts {
  background-color: #5cb85c;
  border-color: #4cae4c;
}
.btn-success .badge, .wfg-button.wfg-add-gifts .badge {
  color: #5cb85c;
  background-color: #fff;
}

.btn-info {
  color: #fff;
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #31b0d5;
  border-color: #28a1c5;
}
.btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
  background-image: none;
}
.btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled]:active, .btn-info[disabled].active, fieldset[disabled] .btn-info, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info:active, fieldset[disabled] .btn-info.active {
  background-color: #5bc0de;
  border-color: #46b8da;
}
.btn-info .badge {
  color: #5bc0de;
  background-color: #fff;
}

.btn-warning {
  color: #fff;
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #ec971f;
  border-color: #df8a13;
}
.btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
  background-image: none;
}
.btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled]:active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning:active, fieldset[disabled] .btn-warning.active {
  background-color: #f0ad4e;
  border-color: #eea236;
}
.btn-warning .badge {
  color: #f0ad4e;
  background-color: #fff;
}

.btn-danger, .wfg-button.wfg-no-thanks {
  color: #fff;
  background-color: #e44343;
  border-color: #e12d2d;
}
.btn-danger:hover, .wfg-button.wfg-no-thanks:hover, .btn-danger:focus, .wfg-button.wfg-no-thanks:focus, .btn-danger:active, .wfg-button.wfg-no-thanks:active, .btn-danger.active, .active.wfg-button.wfg-no-thanks, .open > .btn-danger.dropdown-toggle, .open > .dropdown-toggle.wfg-button.wfg-no-thanks {
  color: #fff;
  background-color: #d51f1f;
  border-color: #bf1b1b;
}
.btn-danger:active, .wfg-button.wfg-no-thanks:active, .btn-danger.active, .active.wfg-button.wfg-no-thanks, .open > .btn-danger.dropdown-toggle, .open > .dropdown-toggle.wfg-button.wfg-no-thanks {
  background-image: none;
}
.btn-danger.disabled, .disabled.wfg-button.wfg-no-thanks, .btn-danger.disabled:hover, .disabled.wfg-button.wfg-no-thanks:hover, .btn-danger.disabled:focus, .disabled.wfg-button.wfg-no-thanks:focus, .btn-danger.disabled:active, .disabled.wfg-button.wfg-no-thanks:active, .btn-danger.disabled.active, .disabled.active.wfg-button.wfg-no-thanks, .btn-danger[disabled], [disabled].wfg-button.wfg-no-thanks, .btn-danger[disabled]:hover, [disabled].wfg-button.wfg-no-thanks:hover, .btn-danger[disabled]:focus, [disabled].wfg-button.wfg-no-thanks:focus, .btn-danger[disabled]:active, [disabled].wfg-button.wfg-no-thanks:active, .btn-danger[disabled].active, [disabled].active.wfg-button.wfg-no-thanks, fieldset[disabled] .btn-danger, fieldset[disabled] .wfg-button.wfg-no-thanks, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .wfg-button.wfg-no-thanks:hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .wfg-button.wfg-no-thanks:focus, fieldset[disabled] .btn-danger:active, fieldset[disabled] .wfg-button.wfg-no-thanks:active, fieldset[disabled] .btn-danger.active, fieldset[disabled] .active.wfg-button.wfg-no-thanks {
  background-color: #e44343;
  border-color: #e12d2d;
}
.btn-danger .badge, .wfg-button.wfg-no-thanks .badge {
  color: #e44343;
  background-color: #fff;
}

.btn-link {
  color: #252525;
  font-weight: normal;
  cursor: pointer;
  border-radius: 0;
}
.btn-link, .btn-link:active, .btn-link[disabled], fieldset[disabled] .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {
  border-color: transparent;
}
.btn-link:hover, .btn-link:focus {
  color: #222222;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:hover, fieldset[disabled] .btn-link:focus {
  color: #777777;
  text-decoration: none;
}

.btn-lg {
  padding: 14px 35px;
  font-size: 19px;
  line-height: 1.33333;
  border-radius: 10px;
}

.btn-sm {
  padding: 12px 20px;
  font-size: 13px;
  line-height: 1.5;
  border-radius: 0px;
}

.btn-xs {
  padding: 4px 8px;
  font-size: 13px;
  line-height: 1.5;
  border-radius: 0px;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-block + .btn-block {
  margin-top: 5px;
}

input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}

/* 5. alerts */
.alert {
  padding: 15px;
  margin-bottom: 27px;
  border: 1px solid transparent;
  border-radius: 0px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: bold;
}
.alert > p,
.alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}

.alert-dismissable,
.alert-dismissible {
  padding-right: 35px;
}
.alert-dismissable .close,
.alert-dismissible .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}

.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
  color: #3c763d;
}
.alert-success hr {
  border-top-color: #c9e2b3;
}
.alert-success .alert-link {
  color: #2b542c;
}
.alert-success .close {
  color: #3c763d;
}

.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #31708f;
}
.alert-info hr {
  border-top-color: #a6e1ec;
}
.alert-info .alert-link {
  color: #245269;
}
.alert-info .close {
  color: #31708f;
}

.alert-warning {
  background-color: #fcf8e3;
  border-color: #faebcc;
  color: #8a6d3b;
}
.alert-warning hr {
  border-top-color: #f7e1b5;
}
.alert-warning .alert-link {
  color: #66512c;
}
.alert-warning .close {
  color: #8a6d3b;
}

.alert-danger {
  background-color: #f2dede;
  border-color: #ebccd1;
  color: #a94442;
}
.alert-danger hr {
  border-top-color: #e4b9c0;
}
.alert-danger .alert-link {
  color: #843534;
}
.alert-danger .close {
  color: #a94442;
}

/* 6. woocommerce */
@media (max-width: 1440px) {
  .elementor.elementor-302 .elementor-element.elementor-element-8a6d5c3 {
    padding: 0 30px;
  }
}
/*-------------------------------------------
    Price
-------------------------------------------*/
.cart2 {
  text-align: center;
}
.cart2 .count {
  font-size: 12px;
  text-align: center;
  display: block;
}

.pp_gallery ul {
  height: auto;
}
.pp_gallery ul a {
  height: auto;
}

.woocommerce .woocommerce-ordering, .woocommerce-page .woocommerce-ordering {
  margin-bottom: 0;
}
.woocommerce .woocommerce-ordering select, .woocommerce-page .woocommerce-ordering select {
  margin-bottom: 0;
}
.woocommerce #review_form #respond p, .woocommerce-page #review_form #respond p {
  margin: 20px 0 0 0;
  line-height: normal;
  width: 100%;
  padding: 0;
  display: inline-block;
}
.woocommerce #review_form #respond p.form-submit, .woocommerce-page #review_form #respond p.form-submit {
  margin-bottom: 0;
}
.woocommerce div.product form.cart .group_table td.woocommerce-grouped-product-list-item__label, .woocommerce-page div.product form.cart .group_table td.woocommerce-grouped-product-list-item__label {
  padding-right: 20px;
  padding-left: 20px;
}
.woocommerce div.product form.cart .group_table td.woocommerce-grouped-product-list-item__price, .woocommerce-page div.product form.cart .group_table td.woocommerce-grouped-product-list-item__price {
  color: #c75533;
  font-weight: 600;
  font-family: "Nunito", Arial, sans-serif;
}
.woocommerce div.product form.cart .group_table td.woocommerce-grouped-product-list-item__price del, .woocommerce-page div.product form.cart .group_table td.woocommerce-grouped-product-list-item__price del {
  color: #b6b9c7;
}
.woocommerce div.product form.cart div.quantity, .woocommerce-page div.product form.cart div.quantity {
  border: 0;
  position: relative;
  height: 56px;
  overflow: visible;
  padding: 0;
  width: 122px;
  border: 1px solid #dddddd;
  margin: 0 30px 0 0;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.09);
  box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.09);
}
.rtl .woocommerce div.product form.cart div.quantity, .rtl .woocommerce-page div.product form.cart div.quantity {
  margin: 0 0 0 30px;
}
.woocommerce div.product form.cart div.quantity .minus, .woocommerce div.product form.cart div.quantity .plus, .woocommerce-page div.product form.cart div.quantity .minus, .woocommerce-page div.product form.cart div.quantity .plus {
  border: 0;
  color: transparent;
  background-color: transparent;
  background-image: url("../images/select.png");
  background-size: 8px;
  background-color: transparent;
  background-position: 50% 5px;
  background-repeat: no-repeat;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  width: 30px;
  height: 24px;
}
.woocommerce div.product form.cart div.quantity .plus, .woocommerce-page div.product form.cart div.quantity .plus {
  bottom: 2px;
  top: auto;
  outline: none;
  right: 2px;
}
.rtl .woocommerce div.product form.cart div.quantity .plus, .rtl .woocommerce-page div.product form.cart div.quantity .plus {
  left: 2px;
  right: auto;
}
.woocommerce div.product form.cart div.quantity .minus, .woocommerce-page div.product form.cart div.quantity .minus {
  top: 2px;
  bottom: auto;
  outline: none;
  right: 2px;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.rtl .woocommerce div.product form.cart div.quantity .minus, .rtl .woocommerce-page div.product form.cart div.quantity .minus {
  left: 2px;
  right: auto;
}
.woocommerce div.product form.cart div.quantity input.qty, .woocommerce-page div.product form.cart div.quantity input.qty {
  border: 0;
  padding: 10px 20px;
  font-weight: 400;
  width: 80px;
  height: 100%;
  outline: none;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.woocommerce .woocommerce-tabs .tab-content .woocommerce-noreviews, .woocommerce-page .woocommerce-tabs .tab-content .woocommerce-noreviews {
  margin-bottom: 10px;
}
.woocommerce .woocommerce-result-count, .woocommerce-page .woocommerce-result-count {
  margin: 0 0 25px 0;
  padding: 12px 0;
  color: #6f7074;
}
.woocommerce table.shop_attributes, .woocommerce-page table.shop_attributes {
  margin-bottom: 0;
  border: 1px solid #eeeeee;
}
.woocommerce table.shop_attributes th, .woocommerce-page table.shop_attributes th {
  font-size: 18px;
  font-weight: normal;
  text-transform: none;
  width: 30%;
  padding: 18px;
  font-weight: 600;
  color: #0a0a0a;
  font-family: "Nunito", Arial, sans-serif;
  background-color: #fff !important;
  border: 1px solid #eeeeee;
}
.woocommerce table.shop_attributes td, .woocommerce-page table.shop_attributes td {
  padding: 18px;
  background-color: #fff !important;
  border: 1px solid #eeeeee;
}
.woocommerce table.shop_attributes td p, .woocommerce-page table.shop_attributes td p {
  padding: 0;
}

.woocommerce-notices-wrapper {
  outline: none;
}

.woocommerce div.product form.cart .variations select {
  margin-bottom: 0;
}

.woocommerce #respond input#submit.loading, .woocommerce a.button.loading, .woocommerce button.button.loading, .woocommerce input.button.loading {
  opacity: 1;
  filter: alpha(opacity=100);
  padding-right: 30px;
}
.woocommerce #respond input#submit.loading:after, .woocommerce a.button.loading:after, .woocommerce button.button.loading:after, .woocommerce input.button.loading:after {
  color: #fff;
  margin: -7px;
  background: transparent;
  z-index: 9;
  font-size: 14px;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 14px;
  height: 14px;
}
.woocommerce #respond input#submit.loading:before, .woocommerce a.button.loading:before, .woocommerce button.button.loading:before, .woocommerce input.button.loading:before {
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: 8;
  position: absolute;
  top: -1px;
  left: -1px;
  background: #fff;
  width: calc(100% + 2px);
  height: calc(100% + 2px);
  content: '';
}

.woocommerce div.product div.images .woocommerce-product-gallery__trigger {
  border: 1px solid #2441e7;
  background: #2441e7;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger:hover, .woocommerce div.product div.images .woocommerce-product-gallery__trigger:active {
  background: #1835da;
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger:before {
  border-color: #fff;
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger:after {
  background: #fff;
}

@media (min-width: 1200px) {
  .woocommerce div.product div.images .flex-control-thumbs li {
    width: 33.33%;
  }
  .woocommerce div.product div.images .flex-control-thumbs li:nth-child(3n + 1) {
    clear: left;
  }
}
.woocommerce div.product div.images .flex-control-thumbs {
  margin-left: -10px;
  margin-right: -10px;
  margin-top: 20px;
}
.woocommerce div.product div.images .flex-control-thumbs li {
  padding-right: 10px;
  padding-left: 10px;
  margin-bottom: 20px;
}
.woocommerce div.product div.images .flex-control-thumbs li img {
  border: 1px solid #fff;
  opacity: 0.8;
  filter: alpha(opacity=80);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.woocommerce div.product div.images .flex-control-thumbs li img:hover, .woocommerce div.product div.images .flex-control-thumbs li img:active, .woocommerce div.product div.images .flex-control-thumbs li img.flex-active {
  border-color: #2441e7;
}

.shop-pagination .apus-pagination {
  margin: 0;
  float: left;
}
.rtl .shop-pagination .apus-pagination {
  float: right;
}
.shop-pagination .woocommerce-result-count {
  float: right;
  margin: 5px 0 0;
}
.rtl .shop-pagination .woocommerce-result-count {
  float: left;
}

.woocommerce div.product form.cart .variations {
  outline: none;
}

table.variations .tawcvs-swatches .swatch-color {
  opacity: 1;
  filter: alpha(opacity=100);
  width: 24px;
  height: 24px;
  line-height: 24px;
  position: relative;
  border: none;
  margin-right: 15px;
}
.rtl table.variations .tawcvs-swatches .swatch-color {
  margin-left: 15px;
  margin-right: inherit;
}
table.variations .tawcvs-swatches .swatch-color:before {
  display: none !important;
}
table.variations .tawcvs-swatches .swatch-color:after {
  content: '';
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  z-index: 2;
  position: absolute;
  top: -1px;
  left: -1px;
  width: 26px;
  height: 26px;
  border: 5px solid #fff;
}
table.variations .tawcvs-swatches .swatch-color.selected {
  -webkit-box-shadow: none;
  box-shadow: none;
}
table.variations .tawcvs-swatches .swatch-color.selected:after {
  top: 1px;
  left: 1px;
  width: 22px;
  height: 22px;
  border: 3px solid #fff;
}
table.variations .tawcvs-swatches .swatch-label {
  font-size: 12px;
  font-weight: 400;
  color: #7e7e7e;
  padding: 9px;
  display: inline-block;
  line-height: 1;
  background: #f2f3f5;
  min-width: 30px;
  text-align: center;
  height: auto;
  width: auto;
  border: 0 !important;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  margin-right: 8px;
  text-transform: uppercase;
  opacity: 1;
  filter: alpha(opacity=100);
}
.rtl table.variations .tawcvs-swatches .swatch-label {
  margin-left: 8px;
  margin-right: inherit;
}
table.variations .tawcvs-swatches .swatch-label.selected {
  -webkit-box-shadow: none;
  box-shadow: none;
  background: #2441e7;
  color: #fff;
}

.woocommerce div.product form.cart .variations td.label {
  padding: 10px 0;
  text-align: inherit;
  display: table-cell;
  vertical-align: middle;
}
.woocommerce div.product form.cart .variations td.label label {
  margin: 0;
}

.woocommerce div.product form.cart.swatches-support .variations td.label {
  vertical-align: top;
}

.woocommerce div.product form.cart .reset_variations {
  color: #e44343;
}
.woocommerce div.product form.cart .reset_variations i {
  font-size: 12px;
  margin-right: 3px;
  color: #e23e1d;
}
.rtl .woocommerce div.product form.cart .reset_variations i {
  margin-left: 3px;
  margin-right: inherit;
}

.woocommerce div.product p.price ins, .woocommerce div.product span.price ins {
  outline: none;
}

.woocommerce #respond input#submit.added:after,
.woocommerce a.button.added:after,
.woocommerce button.button.added:after,
.woocommerce input.button.added:after {
  display: none;
}

.woocommerce form .form-row input.input-text,
.woocommerce form .form-row textarea {
  resize: none;
  padding: 10px 20px;
  line-height: 1.8;
  border: 1px solid #eeeeee;
  background-color: transparent;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}
.woocommerce form .form-row input.input-text:hover,
.woocommerce form .form-row textarea:hover {
  border-color: #eeeeee !important;
}
.woocommerce form .form-row input.input-text:focus,
.woocommerce form .form-row textarea:focus {
  border-color: #eeeeee !important;
}
.woocommerce form .form-row input.input-text:active,
.woocommerce form .form-row textarea:active {
  border-color: #eeeeee !important;
}

.refund-shop {
  margin-bottom: 30px;
}
.refund-shop .btn, .refund-shop .viewmore-products-btn, .refund-shop .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .refund-shop a, .refund-shop .wfg-button, .refund-shop #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .refund-shop a.checkout-button, .refund-shop .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .refund-shop a.checkout-button, .refund-shop .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .refund-shop a.checkout-button {
  letter-spacing: 1px;
}

.woocommerce form .form-row textarea {
  padding: 20px;
  height: 120px;
  resize: none;
  cursor: pointer;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.woocommerce table.wishlist_table thead th {
  color: #0a0a0a;
  padding: 20px 0;
  border-bottom: 1px solid #eeeeee;
}

.woocommerce .wishlist_table td.product-add-to-cart a {
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  display: inline-block !important;
  background-image: none !important;
}

.woocommerce table.wishlist_table tbody td {
  padding: 10px 0;
  text-align: inherit;
  border-width: 0 0 1px;
  border-bottom: 1px solid #eeeeee;
}
@media (min-width: 992px) {
  .woocommerce table.wishlist_table tbody td {
    padding: 20px 0;
  }
}

.woocommerce table.wishlist_table tfoot td {
  border: none;
}

.woocommerce table.wishlist_table {
  font-size: 15px;
}
.woocommerce table.wishlist_table .product-name {
  white-space: nowrap;
  padding-right: 20px;
  padding-left: 20px;
}
@media (min-width: 992px) {
  .woocommerce table.wishlist_table .product-name {
    padding-right: 50px;
    padding-left: 50px;
  }
}
.woocommerce table.wishlist_table .media-body {
  width: auto;
}
.woocommerce table.wishlist_table .product-thumbnail a {
  display: block;
  width: 80px;
}
@media (min-width: 1200px) {
  .woocommerce table.wishlist_table .product-thumbnail a {
    width: 170px;
  }
}

.select2-container .select2-selection--single .select2-selection__rendered {
  padding-top: 13px;
  padding-bottom: 13px;
  line-height: 22px;
}

.select2-container .select2-selection--single {
  height: 50px;
  background-color: transparent;
  border: 0;
  padding: 0;
  outline: none;
  color: #6f7074;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.select2-container .select2-selection--single:hover {
  outline: none;
}
.select2-container .select2-selection--single:focus {
  outline: none;
}
.select2-container .select2-selection--single:active {
  outline: none;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 12px;
}

.woocommerce #respond input#submit, .woocommerce a.button,
.woocommerce button.button, .woocommerce input.button {
  color: #fff;
  background-color: #2441e7;
  border-color: #2441e7;
  font-weight: 400;
  font-size: 14px;
  padding: 15px 30px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.woocommerce #respond input#submit:hover, .woocommerce a.button:hover,
.woocommerce button.button:hover, .woocommerce input.button:hover {
  color: #fff;
  background-color: #2441e7;
}
.woocommerce #respond input#submit:focus, .woocommerce a.button:focus,
.woocommerce button.button:focus, .woocommerce input.button:focus {
  color: #fff;
  background-color: #2441e7;
}
.woocommerce #respond input#submit:active, .woocommerce a.button:active,
.woocommerce button.button:active, .woocommerce input.button:active {
  color: #fff;
  background-color: #2441e7;
}

p.cart-empty {
  margin-bottom: 30px;
}

.return-to-shop {
  margin-bottom: 0;
}

.woocommerce .return-to-shop .button,
.woocommerce .track_order .button {
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}

.woocommerce #respond input#submit {
  color: #fff;
  text-transform: capitalize;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  font-weight: 400;
  font-size: 15px;
  outline: none;
  cursor: pointer;
  border: none;
  background-color: #2441e7;
  background-image: url("../images/btn-arrow.png");
  background-position: 80% 50%;
  background-repeat: no-repeat;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  vertical-align: middle;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  padding: 20px 78px 20px 40px;
}
.lt-ie8 .woocommerce #respond input#submit {
  display: inline;
  zoom: 1;
}
.rtl .woocommerce #respond input#submit {
  padding: 20px 40px 20px 78px;
}
.woocommerce #respond input#submit:hover {
  color: #fff;
  background-color: #2441e7;
  background-image: url("../images/btn-arrow.png");
}
.woocommerce #respond input#submit:focus {
  color: #fff;
  background-color: #2441e7;
  background-image: url("../images/btn-arrow.png");
}
.woocommerce #respond input#submit:active {
  color: #fff;
  background-color: #2441e7;
  background-image: url("../images/btn-arrow.png");
}

.track_order {
  max-width: 770px;
  margin: auto;
  padding: 15px;
  background: #f2f3f5;
}
@media (min-width: 992px) {
  .track_order {
    padding: 70px;
  }
}
.track_order .form-row {
  width: 100% !important;
}
.track_order .form-row input.input-text {
  padding: 5px 20px;
  background: #fff !important;
  height: 45px;
}
.track_order .form-row:last-child {
  margin-bottom: 0;
}
.track_order .form-row label {
  font-family: "Open Sans";
  color: #252525;
}

.woocommerce-message {
  line-height: 2.5;
}

.apus-filter .woocommerce-message {
  display: none;
}

#add_payment_method #payment ul.payment_methods, .woocommerce-cart #payment ul.payment_methods, .woocommerce-checkout #payment ul.payment_methods {
  border: 0;
  padding: 0;
}
#add_payment_method #payment ul.payment_methods li, .woocommerce-cart #payment ul.payment_methods li, .woocommerce-checkout #payment ul.payment_methods li {
  padding: 0;
  margin-bottom: 15px;
}
#add_payment_method #payment ul.payment_methods li .payment_box, .woocommerce-cart #payment ul.payment_methods li .payment_box, .woocommerce-checkout #payment ul.payment_methods li .payment_box {
  padding: 20px;
  margin: 20px 0;
  background-color: #fff;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.09);
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.09);
}
#add_payment_method #payment ul.payment_methods li label, .woocommerce-cart #payment ul.payment_methods li label, .woocommerce-checkout #payment ul.payment_methods li label {
  font-size: 14px;
  cursor: pointer;
  font-weight: 400;
  display: inline;
}
#add_payment_method #payment ul.payment_methods li:last-child, .woocommerce-cart #payment ul.payment_methods li:last-child, .woocommerce-checkout #payment ul.payment_methods li:last-child {
  margin-bottom: 0;
}
#add_payment_method #payment ul.payment_methods li .about_paypal, .woocommerce-cart #payment ul.payment_methods li .about_paypal, .woocommerce-checkout #payment ul.payment_methods li .about_paypal {
  margin: 0 10px;
  float: none;
}

#add_payment_method #payment ul.payment_methods li input, .woocommerce-cart #payment ul.payment_methods li input, .woocommerce-checkout #payment ul.payment_methods li input {
  margin-right: 10px;
}
.rtl #add_payment_method #payment ul.payment_methods li input, .rtl .woocommerce-cart #payment ul.payment_methods li input, .rtl .woocommerce-checkout #payment ul.payment_methods li input {
  margin-left: 10px;
  margin-right: inherit;
}

.woocommerce table.shop_table {
  border: 0;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
.woocommerce table.shop_table th {
  padding: 25px 0;
}
.woocommerce table.shop_table th.product-remove .remove {
  border: 0;
}
.woocommerce table.shop_table .reader-text {
  display: none;
}
.woocommerce table.shop_table td {
  border: none;
  border-top: 1px solid #eeeeee;
  overflow: hidden;
  padding: 20px 0;
}
.woocommerce table.shop_table td.product-price {
  color: #7e7e7e;
  font-size: 15px;
  padding-left: 0;
  padding-right: 0;
}
.woocommerce table.shop_table td .btn, .woocommerce table.shop_table td .viewmore-products-btn, .woocommerce table.shop_table .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table table.shop_table td.product-add-to-cart a, .woocommerce table.shop_table td .wfg-button, .woocommerce table.shop_table td #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button, .woocommerce table.shop_table td .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button, .woocommerce table.shop_table td .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button {
  border: 0;
  padding: 14px 42px;
  color: #fff;
  line-height: inherit;
  text-transform: none;
  background-color: #2441e7;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.woocommerce table.shop_table td .btn:hover, .woocommerce table.shop_table td .viewmore-products-btn:hover, .woocommerce table.shop_table .wishlist_table td.product-add-to-cart a:hover, .woocommerce .wishlist_table table.shop_table td.product-add-to-cart a:hover, .woocommerce table.shop_table td .wfg-button:hover, .woocommerce table.shop_table td #add_payment_method .wc-proceed-to-checkout a.checkout-button:hover, #add_payment_method .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:hover, .woocommerce table.shop_table td .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:hover, .woocommerce-cart .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:hover, .woocommerce table.shop_table td .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:hover, .woocommerce-checkout .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:hover {
  color: #fff !important;
}
.woocommerce table.shop_table td .btn:focus, .woocommerce table.shop_table td .viewmore-products-btn:focus, .woocommerce table.shop_table .wishlist_table td.product-add-to-cart a:focus, .woocommerce .wishlist_table table.shop_table td.product-add-to-cart a:focus, .woocommerce table.shop_table td .wfg-button:focus, .woocommerce table.shop_table td #add_payment_method .wc-proceed-to-checkout a.checkout-button:focus, #add_payment_method .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:focus, .woocommerce table.shop_table td .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:focus, .woocommerce table.shop_table td .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:focus {
  color: #fff !important;
}
.woocommerce table.shop_table td .btn:active, .woocommerce table.shop_table td .viewmore-products-btn:active, .woocommerce table.shop_table .wishlist_table td.product-add-to-cart a:active, .woocommerce .wishlist_table table.shop_table td.product-add-to-cart a:active, .woocommerce table.shop_table td .wfg-button:active, .woocommerce table.shop_table td #add_payment_method .wc-proceed-to-checkout a.checkout-button:active, #add_payment_method .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:active, .woocommerce table.shop_table td .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:active, .woocommerce-cart .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:active, .woocommerce table.shop_table td .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:active, .woocommerce-checkout .wc-proceed-to-checkout .woocommerce table.shop_table td a.checkout-button:active {
  color: #fff !important;
}
.woocommerce table.shop_table td.product-thumbnail a {
  padding: 5px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  display: inline-block;
  vertical-align: middle;
  border: 2px solid #eeeeee;
}
.lt-ie8 .woocommerce table.shop_table td.product-thumbnail a {
  display: inline;
  zoom: 1;
}
.woocommerce table.shop_table td.product-quantity .quantity {
  margin: 0px;
  padding: 0px;
  border: 1px solid #eeeeee;
  background-color: #fff;
  width: 88px;
  height: 52px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0 2px 2px -2px rgba(0, 0, 0, 0.09);
  box-shadow: 0 2px 2px -2px rgba(0, 0, 0, 0.09);
}
.woocommerce table.shop_table td.product-quantity input.qty {
  margin: 0;
  font-weight: 400;
  font-size: 14px;
  border: 0;
  color: #6f7074;
  padding: 0px 0px 0 15px;
  font-family: "Nunito", Arial, sans-serif;
  width: 56px;
  height: 100%;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
}
.woocommerce table.shop_table td.product-quantity .plus, .woocommerce table.shop_table td.product-quantity .minus {
  -webkit-box-shadow: none;
  box-shadow: none;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  outline: none;
  height: 25px;
  border-width: 0 0 1px 1px;
  border-style: solid;
  border-color: #eeeeee;
}
.woocommerce table.shop_table td.product-quantity .plus:hover, .woocommerce table.shop_table td.product-quantity .minus:hover {
  color: #0a0a0a;
  background-color: transparent;
}
.woocommerce table.shop_table td.product-quantity .plus:focus, .woocommerce table.shop_table td.product-quantity .minus:focus {
  color: #0a0a0a;
  background-color: transparent;
}
.woocommerce table.shop_table td.product-quantity .plus:active, .woocommerce table.shop_table td.product-quantity .minus:active {
  color: #0a0a0a;
  background-color: transparent;
}
.woocommerce table.shop_table td.product-quantity .minus {
  border-bottom: 0px;
}
.woocommerce table.shop_table td.product-subtotal {
  color: #7e7e7e !important;
  font-size: 15px !important;
}
.woocommerce table.shop_table .quantity-wrapper > label {
  display: none;
}
.woocommerce table.shop_table .product-remove .remove {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-image: url("../images/close-dark.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-color: transparent;
}
.woocommerce table.shop_table .product-remove .remove:hover {
  background-color: transparent;
}
.woocommerce table.shop_table .product-remove .remove:focus {
  background-color: transparent;
}
.woocommerce table.shop_table .product-remove .remove:active {
  background-color: transparent;
}
.woocommerce table.shop_table tbody .actions p {
  margin: 0;
}
.woocommerce table.shop_table tbody .product-subtotal {
  color: #2441e7;
  font-size: 16px;
  font-family: "Open Sans";
}
.woocommerce table.shop_table tbody .order-total .woocommerce-Price-amount {
  color: #222;
  font-weight: 600;
  font-size: 15px;
}
.woocommerce table.shop_table tbody .product-name {
  font-size: 18px;
  font-weight: 400;
  font-family: "Nunito", Arial, sans-serif;
}
.woocommerce table.shop_table tbody .cart-subtotal .woocommerce-Price-amount,
.woocommerce table.shop_table tbody .order-total .woocommerce-Price-amount {
  font-size: 20px;
  font-weight: 400;
}
.woocommerce table.shop_table th {
  border: none;
  font-family: "Open Sans";
  text-transform: uppercase;
  color: #252525;
  font-size: 16px;
  font-weight: 400;
}
.woocommerce table.shop_table .list-bundles {
  font-size: 14px;
  list-style: none;
  padding-left: 25px;
}
.rtl .woocommerce table.shop_table .list-bundles {
  padding-right: 25px;
  padding-left: inherit;
}
.woocommerce table.shop_table .list-bundles strong {
  font-weight: 500;
}
.woocommerce table.shop_table .list-bundles ul {
  list-style: inside none disc;
  padding: 0;
  margin: 0;
}

#add_payment_method .wc-proceed-to-checkout .btn, #add_payment_method .wc-proceed-to-checkout .viewmore-products-btn, #add_payment_method .wc-proceed-to-checkout .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart #add_payment_method .wc-proceed-to-checkout a, #add_payment_method .wc-proceed-to-checkout .wfg-button, #add_payment_method .wc-proceed-to-checkout a.checkout-button,
.woocommerce-cart .wc-proceed-to-checkout .btn,
.woocommerce-cart .wc-proceed-to-checkout .viewmore-products-btn,
.woocommerce-cart .wc-proceed-to-checkout .woocommerce .wishlist_table td.product-add-to-cart a,
.woocommerce .wishlist_table td.product-add-to-cart .woocommerce-cart .wc-proceed-to-checkout a,
.woocommerce-cart .wc-proceed-to-checkout .wfg-button,
.woocommerce-cart #add_payment_method .wc-proceed-to-checkout a.checkout-button,
#add_payment_method .woocommerce-cart .wc-proceed-to-checkout a.checkout-button,
.woocommerce-cart .wc-proceed-to-checkout a.checkout-button,
.woocommerce-checkout .wc-proceed-to-checkout .btn,
.woocommerce-checkout .wc-proceed-to-checkout .viewmore-products-btn,
.woocommerce-checkout .wc-proceed-to-checkout .woocommerce .wishlist_table td.product-add-to-cart a,
.woocommerce .wishlist_table td.product-add-to-cart .woocommerce-checkout .wc-proceed-to-checkout a,
.woocommerce-checkout .wc-proceed-to-checkout .wfg-button,
.woocommerce-checkout #add_payment_method .wc-proceed-to-checkout a.checkout-button,
#add_payment_method .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button,
.woocommerce-checkout .wc-proceed-to-checkout a.checkout-button {
  text-transform: none;
  border-color: #ff1053;
  background-color: #ff1053;
  font-size: 18px;
  font-weight: 400;
  padding: 12px 30px;
  font-family: "Nunito", Arial, sans-serif;
}

#add_payment_method .checkout .col-2 h3#ship-to-different-address,
.woocommerce-cart .checkout .col-2 h3#ship-to-different-address,
.woocommerce-checkout .checkout .col-2 h3#ship-to-different-address {
  font-size: 20px;
}

.woocommerce .cart_totals {
  border: 2px solid #eeeeee;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  padding: 30px;
}
.woocommerce .cart_totals > h2 {
  margin: 0;
  text-transform: none;
  font-size: 20px;
  position: relative;
  padding: 0 0 15px 0;
  font-weight: 600;
  border-bottom: 1px solid #eeeeee;
}
.woocommerce .cart_totals table.shop_table {
  border: none;
  margin: 0;
}
.woocommerce .cart_totals table.shop_table th, .woocommerce .cart_totals table.shop_table td {
  padding: 20px 0;
}
.woocommerce .cart_totals table.shop_table th .woocommerce-Price-amount, .woocommerce .cart_totals table.shop_table td .woocommerce-Price-amount {
  color: #7e7e7e;
  font-size: 15px;
  font-weight: 400;
}
.woocommerce .cart_totals table.shop_table label {
  font-weight: 400;
}
.woocommerce .cart_totals table.shop_table tr.order-total .amount {
  font-size: 22px;
  font-weight: 700;
  color: #c75533;
  font-family: "Nunito", Arial, sans-serif;
}
.woocommerce .cart_totals table.shop_table th {
  color: #0a0a0a;
  font-family: "Nunito", Arial, sans-serif;
  font-weight: 400;
  font-size: 18px;
  text-transform: none;
}
.woocommerce .cart_totals .wc-proceed-to-checkout .btn, .woocommerce .cart_totals .wc-proceed-to-checkout .viewmore-products-btn, .woocommerce .cart_totals .wc-proceed-to-checkout .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .cart_totals .wc-proceed-to-checkout a, .woocommerce .cart_totals .wc-proceed-to-checkout .wfg-button, .woocommerce .cart_totals #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .woocommerce .cart_totals .wc-proceed-to-checkout a.checkout-button, .woocommerce .cart_totals .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .woocommerce .cart_totals .wc-proceed-to-checkout a.checkout-button, .woocommerce .cart_totals .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .woocommerce .cart_totals .wc-proceed-to-checkout a.checkout-button {
  border: 0;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}

.woocommerce table.cart {
  position: relative;
  border-width: 0 2px 2px 2px;
  border-style: solid;
  border-color: #eeeeee;
  -webkit-border-top-left-radius: 0;
  -webkit-border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -moz-border-radius-topleft: 0;
  -moz-border-radius-topright: 0;
  -moz-border-radius-bottomright: 5px;
  -moz-border-radius-bottomleft: 5px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}
.woocommerce table.cart thead th {
  background-color: #2441e7;
}
.woocommerce table.cart thead th.product-thumbnail {
  -webkit-border-top-left-radius: 5px;
  -webkit-border-top-right-radius: 0;
  -webkit-border-bottom-right-radius: 0px;
  -webkit-border-bottom-left-radius: 0px;
  -moz-border-radius-topleft: 5px;
  -moz-border-radius-topright: 0;
  -moz-border-radius-bottomright: 0px;
  -moz-border-radius-bottomleft: 0px;
  border-top-left-radius: 5px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}
.woocommerce table.cart thead th.product-remove {
  -webkit-border-top-left-radius: 0;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 0px;
  -webkit-border-bottom-left-radius: 0px;
  -moz-border-radius-topleft: 0;
  -moz-border-radius-topright: 5px;
  -moz-border-radius-bottomright: 0px;
  -moz-border-radius-bottomleft: 0px;
  border-top-left-radius: 0;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}
.woocommerce table.cart th {
  color: #fff;
  text-transform: none;
  padding: 12px;
}
.woocommerce table.cart th.product-thumbnail {
  padding-left: 30px;
}
.rtl .woocommerce table.cart th.product-thumbnail {
  padding-right: 30px;
  padding-left: inherit;
}
.woocommerce table.cart th.product-remove {
  text-align: right;
}
.rtl .woocommerce table.cart th.product-remove {
  text-align: left;
}
.woocommerce table.cart th.product-remove .remove {
  background-size: 16px;
  background-image: url("../images/close-icon.png");
}
.woocommerce table.cart td {
  padding: 30px;
}
.woocommerce .woocommerce-shipping-calculator .select2-container--default .select2-selection--single {
  border: 1px solid #eeeeee;
}
.woocommerce .woocommerce-shipping-calculator .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-left: 18px;
  padding-right: 18px;
}
.rtl .woocommerce .woocommerce-shipping-calculator .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-right: 18px;
  padding-left: inherit;
}
.rtl .woocommerce .woocommerce-shipping-calculator .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-left: 18px;
  padding-right: inherit;
}
.woocommerce .woocommerce-shipping-calculator .shipping-calculator-button {
  display: inline-block;
  vertical-align: middle;
  margin-bottom: 30px;
}
.lt-ie8 .woocommerce .woocommerce-shipping-calculator .shipping-calculator-button {
  display: inline;
  zoom: 1;
}

.woocommerce-checkout .woocommerce-info {
  border-top: 0px;
  background: transparent;
  text-align: center;
  margin-bottom: 10px;
  padding-bottom: 0;
  padding-left: 0;
}
.rtl .woocommerce-checkout .woocommerce-info {
  padding-right: 0;
  padding-left: inherit;
}
.woocommerce-checkout .woocommerce-info:before {
  content: none;
}
.woocommerce-checkout .woocommerce-info a {
  color: #ff1053;
}
.woocommerce-checkout .details-review {
  border: 2px solid #eeeeee;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  padding: 30px;
}
.woocommerce-checkout form.woocommerce-form-coupon {
  border: 0;
  padding: 0;
  width: 436px;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
.woocommerce-checkout form.woocommerce-form-coupon p:first-child {
  margin-bottom: 30px;
}
.woocommerce-checkout form.woocommerce-form-coupon .button {
  padding: 17px 40px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.woocommerce-checkout form.woocommerce-form-coupon .form-row-first {
  width: 210px;
}
.woocommerce-checkout form.woocommerce-form-coupon .form-row-first .input-text {
  width: 100%;
  padding-left: 20px;
  padding-right: 20px;
}

.woocommerce-table--order-details tfoot .woocommerce-Price-amount {
  font-size: 24px;
}

.woocommerce-error li, .woocommerce-info li, .woocommerce-message li {
  font-weight: 400;
}

#add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment {
  background: #fff;
}
#add_payment_method #payment .place-order, .woocommerce-cart #payment .place-order, .woocommerce-checkout #payment .place-order {
  padding: 30px 0 10px !important;
}
#add_payment_method #payment .place-order #place_order, .woocommerce-cart #payment .place-order #place_order, .woocommerce-checkout #payment .place-order #place_order {
  font-size: 18px;
  margin-top: 20px;
  color: #fff;
  font-weight: 400;
  text-transform: none;
  border-color: #ff1053;
  background: #ff1053;
  font-family: "Nunito", Arial, sans-serif;
  text-transform: capitalize;
}

#add_payment_method #payment div.payment_box, .woocommerce-cart #payment div.payment_box, .woocommerce-checkout #payment div.payment_box {
  background: #fff;
}

#add_payment_method #payment div.payment_box::before, .woocommerce-cart #payment div.payment_box::before, .woocommerce-checkout #payment div.payment_box::before {
  border-bottom-color: #fff;
  content: none;
}

.woocommerce #customer_details .woocommerce-shipping-fields,
.woocommerce #customer_details .woocommerce-account-fields {
  margin-top: 30px;
}
.woocommerce #customer_details h3.form-row {
  font-size: 18px;
  font-weight: 400;
  text-transform: capitalize;
  margin: 0;
  padding: 20px 0;
}
.woocommerce #customer_details .shipping_address > * > .select2-hidden-accessible {
  height: 0;
}

label.checkbox span {
  padding-left: 20px;
}
.rtl label.checkbox span {
  padding-right: 20px;
  padding-left: inherit;
}

.woocommerce form .woocommerce-billing-fields > h3 {
  font-size: 18px;
  font-weight: 400;
  text-transform: capitalize;
  margin: 0;
  padding: 20px 0;
}
.woocommerce form .woocommerce-billing-fields .select2-container {
  height: 50px;
  border: 1px solid #eeeeee !important;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > label {
  font-weight: 400;
}
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input {
  overflow: hidden;
  width: calc(100% - 200px) !important;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: #eeeeee;
  padding: 10px 0;
  border-radius: 0 !important;
  -webkit-border-radius: 0 !important;
  -moz-border-radius: 0 !important;
  -ms-border-radius: 0 !important;
  -o-border-radius: 0 !important;
  float: right;
}
.rtl .woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container, .rtl
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select, .rtl
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input {
  float: left;
}
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-container:focus,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > select:focus,
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > input:focus {
  border-color: #2441e7;
}
.woocommerce form .woocommerce-billing-fields .woocommerce-billing-fields__field-wrapper > * > .select2-hidden-accessible {
  height: 0;
}

.woocommerce .cart-collaterals .cross-sells, .woocommerce-page .cart-collaterals .cross-sells,
.woocommerce .cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals {
  width: 100%;
}

.woocommerce div.product.first {
  clear: left;
}
.rtl .woocommerce div.product.first {
  clear: right;
}
.woocommerce div.product .product_title {
  font-size: 26px;
  margin: 0 0 15px 0;
  font-weight: 400;
  line-height: 24px;
}

.woocommerce p.stars.selected a.active::before, .woocommerce p.stars:hover a::before,
.woocommerce p.stars.selected a:not(.active):before {
  content: '';
}

.woocommerce div.product p.price, .woocommerce div.product span.price {
  color: #c75533;
  font-weight: 700;
  font-family: "Nunito", Arial, sans-serif;
}
.woocommerce div.product p.price del, .woocommerce div.product span.price del {
  color: #b6b9c7;
}

.woocommerce div.product p.price del, .woocommerce div.product span.price del {
  opacity: 1;
  filter: alpha(opacity=100);
}

.variations label {
  color: #7e7e7e;
  font-size: 15px;
  text-transform: capitalize;
  font-weight: 400 !important;
  padding-right: 5px;
}
.rtl .variations label {
  padding-left: 5px;
  padding-right: inherit;
}
.variations .value {
  padding: 0;
}

.woocommerce div.product form.cart .group_table {
  border: none;
  margin-bottom: 30px;
}
.woocommerce div.product form.cart .group_table .price del {
  font-size: 12px !important;
}
.woocommerce div.product form.cart .group_table .price,
.woocommerce div.product form.cart .group_table .price ins {
  font-size: 15px !important;
  color: #2441e7;
}
.woocommerce div.product form.cart .group_table label {
  font-weight: 500;
}
.woocommerce div.product form.cart .group_table td {
  vertical-align: middle;
}
.woocommerce div.product form.cart .group_table td:first-child {
  padding-right: 0;
  text-align: left;
}
.rtl .woocommerce div.product form.cart .group_table td:first-child {
  text-align: right;
}
.woocommerce div.product form.cart .group_table .quantity .reader-text {
  display: none;
}

.woocommerce #respond input#submit.disabled,
.woocommerce #respond input#submit:disabled,
.woocommerce #respond input#submit:disabled[disabled],
.woocommerce a.button.disabled,
.woocommerce a.button:disabled,
.woocommerce a.button:disabled[disabled],
.woocommerce button.button.disabled,
.woocommerce button.button:disabled,
.woocommerce button.button:disabled[disabled],
.woocommerce input.button.disabled,
.woocommerce input.button:disabled,
.woocommerce input.button:disabled[disabled] {
  opacity: 1;
  filter: alpha(opacity=100);
  pointer-events: none;
}

.woocommerce div.product form.cart .button {
  color: #fff;
  border: 0;
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  position: relative;
  text-transform: capitalize;
  background-color: #ff1053;
  padding: 19px 42px 19px 80px;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  float: left;
}
.rtl .woocommerce div.product form.cart .button {
  padding: 19px 80px 19px 42px;
}
.rtl .woocommerce div.product form.cart .button {
  float: right;
}
.woocommerce div.product form.cart .button:hover {
  color: #fff;
  background-color: #ff1053;
}
.woocommerce div.product form.cart .button:focus {
  color: #fff;
  background-color: #ff1053;
}
.woocommerce div.product form.cart .button:active {
  color: #fff;
  background-color: #ff1053;
}
.woocommerce div.product form.cart .button:before {
  color: #fff;
  content: "\f101";
  font-family: "Flaticon";
  display: block;
  font-size: 24px;
  left: 42px;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  transform: translateY(-50%);
}
.rtl .woocommerce div.product form.cart .button:before {
  right: 42px;
  left: auto;
}

.woocommerce .details-product .information .stock.out-of-stock {
  background: #f2f3f5;
  border-color: #f2f3f5;
  color: #cccccc;
  margin: 10px 0;
  width: 100%;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}

.woocommerce div.product form.cart.group_product {
  width: 100%;
}

.woocommerce div.product form.cart .group_table .label {
  padding: 0.5em;
  vertical-align: middle;
  font-size: 14px;
  display: table-cell;
  text-align: inherit;
  white-space: normal;
}
.woocommerce div.product form.cart .group_table .label label {
  font-weight: 400;
}

.woocommerce div.product form.cart .variations td {
  line-height: inherit;
  font-size: inherit;
  padding: 10px 0;
  vertical-align: middle;
}
.woocommerce div.product form.cart .variations td .tawcvs-swatches {
  padding: 0;
}

.woocommerce .order_details {
  padding: 0;
}

.woocommerce table.shop_table input.button:disabled,
.woocommerce table.shop_table input.button {
  background-color: transparent;
  border: 2px solid #2441e7;
  color: #2441e7;
  text-transform: capitalize;
  padding: 16px 48px;
  font-weight: 400;
  outline: none;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.woocommerce table.shop_table input.button:disabled:hover,
.woocommerce table.shop_table input.button:hover {
  background: #2441e7;
  color: #fff;
  border-color: #2441e7;
}
.woocommerce table.shop_table input.button:disabled:focus,
.woocommerce table.shop_table input.button:focus {
  background: #2441e7;
  color: #fff;
  border-color: #2441e7;
}
.woocommerce table.shop_table input.button:disabled:active,
.woocommerce table.shop_table input.button:active {
  background: #2441e7;
  color: #fff;
  border-color: #2441e7;
}

.woocommerce .woocommerce-message .button,
.woocommerce .checkout_coupon .button {
  background-color: #2441e7;
  padding: 12px 30px;
  border: 0;
  color: #fff;
  font-size: 15px;
  font-weight: 400;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  display: inline-block;
  vertical-align: middle;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.lt-ie8 .woocommerce .woocommerce-message .button, .lt-ie8
.woocommerce .checkout_coupon .button {
  display: inline;
  zoom: 1;
}
.woocommerce .woocommerce-message .button:hover,
.woocommerce .checkout_coupon .button:hover {
  color: #fff;
  background-color: #2441e7;
}
.woocommerce .woocommerce-message .button:focus,
.woocommerce .checkout_coupon .button:focus {
  color: #fff;
  background-color: #2441e7;
}
.woocommerce .woocommerce-message .button:active,
.woocommerce .checkout_coupon .button:active {
  color: #fff;
  background-color: #2441e7;
}

.woocommerce #content table.cart td.actions .input-text,
.woocommerce table.cart td.actions .input-text,
.woocommerce-page #content table.cart td.actions .input-text,
.woocommerce-page table.cart td.actions .input-text {
  width: auto;
  height: 41px;
  padding: 5px 10px !important;
  margin-right: 10px !important;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}
.rtl .woocommerce #content table.cart td.actions .input-text, .rtl
.woocommerce table.cart td.actions .input-text, .rtl
.woocommerce-page #content table.cart td.actions .input-text, .rtl
.woocommerce-page table.cart td.actions .input-text {
  margin-left: 10px !important;
  margin-right: inherit;
}

#add_payment_method table.cart img, .woocommerce-cart table.cart img, .woocommerce-checkout table.cart img {
  width: 100px;
}

.woocommerce .percent-sale,
.woocommerce span.onsale {
  color: #fff;
  font-size: 12px;
  background: #2441e7;
  padding: 6px 10px;
  position: absolute;
  text-align: center;
  text-transform: uppercase;
  top: 30px;
  min-height: auto;
  font-family: "Open Sans";
  z-index: 9;
  width: 56px;
  height: 56px;
  left: auto;
  right: 30px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}
.rtl .woocommerce .percent-sale, .rtl
.woocommerce span.onsale {
  right: auto;
  left: auto;
}
.rtl .woocommerce .percent-sale, .rtl
.woocommerce span.onsale {
  left: 30px;
  right: auto;
}

.popup-cart .title-count,
.popup-cart .title-add {
  font-size: 20px;
  margin: 0 0 20px;
}
.popup-cart .gr-buttons {
  margin: 50px 0 0;
}
.popup-cart .title-add {
  color: #5cb85c;
}
.popup-cart .image img {
  max-width: 100px;
}
.popup-cart .name {
  margin: 30px 0 0;
}
.popup-cart .widget-product {
  margin-top: 30px;
}

#apus-cart-modal .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 99;
  background: #fff;
  width: 30px;
  height: 30px;
  line-height: 26px;
  text-align: center;
  display: inline-block;
}
.rtl #apus-cart-modal .btn-close {
  left: 0;
  right: auto;
}
#apus-cart-modal .modal-content {
  background: #fff none repeat scroll 0 0;
  min-width: 1000px;
  max-width: 100%;
  margin-top: 50px;
}
#apus-cart-modal .modal-body {
  padding: 60px;
}

.name {
  font-size: 18px;
  line-height: 26px;
  font-weight: 400;
}

.product-block {
  position: relative;
}
.product-block .sale-perc {
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  padding: 0 5px;
  line-height: 1.7;
  position: absolute;
  top: 12px;
  z-index: 8;
  background-color: #fd5f5c;
  text-transform: uppercase;
  font-family: "Open Sans";
  left: 12px;
}
.rtl .product-block .sale-perc {
  right: 12px;
  left: auto;
}
.product-block .out-of-stock {
  background: #d4d4d4;
  color: #fff !important;
  font-size: 14px !important;
  font-weight: 400;
  padding: 0 8px;
  position: absolute;
  right: 12px;
  text-transform: uppercase;
  font-family: "Open Sans";
  top: 12px;
  z-index: 8;
}
.rtl .product-block .out-of-stock {
  left: 12px;
  right: auto;
}
.product-block .image {
  position: relative;
}
.product-block .image .downsale {
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 8;
  padding: 2px 10px;
  background-color: #d42e2e;
  color: #fff;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
}
.product-block .image img {
  display: inline-block;
  width: 100%;
  -webkit-transition: all 0.5s ease-in-out 0s;
  -o-transition: all 0.5s ease-in-out 0s;
  transition: all 0.5s ease-in-out 0s;
}
.product-block .image .image-effect {
  top: 0;
  position: absolute;
  left: 50%;
  z-index: 2;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
.product-block .image .image-no-effect {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}
.product-block .block-inner:hover .image .image-hover {
  opacity: 0;
  filter: alpha(opacity=0);
}
.product-block .block-inner:hover .image .image-effect {
  opacity: 1;
  filter: alpha(opacity=100);
}
.product-block .block-inner.text-center .image img {
  margin: auto;
}
.product-block .block-inner.text-center .image .image-effect {
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -o-transform: translateX(-50%);
  transform: translateX(-50%);
}
.product-block .clear {
  display: none !important;
}
.product-block .product-cats {
  font-size: 12px;
  margin: 15px 0 11px;
  text-transform: uppercase;
}
.product-block .product-cats a {
  color: #4c4c4c;
}
.product-block .product-cats a:hover, .product-block .product-cats a:active {
  color: #2441e7;
}
.product-block .rating > * {
  display: inline-block !important;
  vertical-align: middle;
  margin: 0 !important;
  float: none;
}
.product-block .rating .counts {
  color: #999591;
  font-size: 13px;
}
.product-block .feedback,
.product-block .sub-title {
  display: none;
}
.product-block .product-image {
  position: relative;
  display: block;
}
.product-block:hover .image .image-no-effect {
  -webkit-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
.product-block.grid {
  position: relative;
  margin: 0 0 30px 0;
  padding: 20px;
  background-color: transparent;
  border: 2px solid #eeeeee;
  text-align: left;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.rtl .product-block.grid {
  text-align: right;
}
.product-block.grid.noborder {
  border: 0 !important;
}
.product-block.grid .name {
  margin: 0 0 3px 0;
}
.product-block.grid .image.out .product-image {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.product-block.grid .groups-button {
  position: absolute;
  bottom: 4px;
  right: 0;
}
.rtl .product-block.grid .groups-button {
  left: 0;
  right: auto;
}
.product-block.grid .groups-button .button.added,
.product-block.grid .groups-button .add_to_cart_button.added {
  display: none;
}
.product-block.grid .product-info-left {
  padding-right: 60px;
}
.rtl .product-block.grid .product-info-left {
  padding-left: 60px;
  padding-right: inherit;
}
.product-block.grid .product-cat {
  margin: 0 0 8px;
  font-size: 12px;
  font-family: "Open Sans";
  letter-spacing: 2px;
  text-transform: uppercase;
}
.product-block.grid .product-cat a {
  color: #2441e7;
}
.product-block.grid .caption {
  padding: 20px 15px 15px;
  text-align: center;
}
.product-block.grid .block-inner {
  position: relative;
  margin-bottom: 20px;
  overflow: hidden;
}
.product-block.grid .title-wrapper {
  position: relative;
}
.product-block.grid .groups-button > div {
  outline: none;
}
.product-block.grid .groups-button .add-cart .added_to_cart,
.product-block.grid .groups-button .add-cart .button {
  border: 0;
  font-size: 0;
  padding: 0;
  display: block;
  font-weight: 400;
  background-color: #ff1053 !important;
  pointer-events: none;
  visibility: hidden;
  position: relative;
  color: #fff !important;
  width: 50px;
  height: 50px;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}
.product-block.grid .groups-button .add-cart .added_to_cart:before,
.product-block.grid .groups-button .add-cart .button:before {
  font-size: 23px;
  font-family: "Flaticon";
  content: "\f101";
  top: 50%;
  left: 50%;
  position: absolute;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.product-block.grid .groups-button .add-cart .added_to_cart.product_type_grouped:before, .product-block.grid .groups-button .add-cart .added_to_cart.product_type_external:before, .product-block.grid .groups-button .add-cart .added_to_cart.product_type_variable:before,
.product-block.grid .groups-button .add-cart .button.product_type_grouped:before,
.product-block.grid .groups-button .add-cart .button.product_type_external:before,
.product-block.grid .groups-button .add-cart .button.product_type_variable:before {
  content: "\f107";
}
.product-block.grid .groups-button .add-cart .added_to_cart:before {
  color: #fff;
  content: "\f107";
}
.product-block.grid .metas {
  padding: 0;
  margin: 0;
}
.product-block.grid .swatches-wrapper {
  list-style: none;
  padding: 0;
  padding: 0 0 10px;
  line-height: 1.2;
  position: absolute;
  bottom: 0;
  margin: 0;
  z-index: 9;
  left: 0;
  width: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  -webkit-transform: translateY(10px);
  -ms-transform: translateY(10px);
  -o-transform: translateY(10px);
  transform: translateY(10px);
}
.product-block.grid .swatches-wrapper li {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}
.rtl .product-block.grid .swatches-wrapper li {
  margin-left: 5px;
  margin-right: inherit;
}
.product-block.grid .swatches-wrapper li:last-child {
  margin-right: 0;
}
.rtl .product-block.grid .swatches-wrapper li:last-child {
  margin-left: 0;
  margin-right: inherit;
}
.product-block.grid .swatches-wrapper .label {
  padding: 0;
  font-size: 15px;
  color: #7e7e7e;
  font-weight: 500;
}
.product-block.grid .swatches-wrapper .swatch-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}
.product-block.grid .price {
  display: block;
  margin: 0 0 3px 0;
  font-size: 18px !important;
}
.product-block.grid:not(.grid-deal):hover {
  border-color: #fff !important;
}
.product-block.grid:hover {
  -webkit-box-shadow: 0px 0px 30px 0px rgba(32, 32, 32, 0.15);
  box-shadow: 0px 0px 30px 0px rgba(32, 32, 32, 0.15);
}
.product-block.grid:hover .groups-button .add-cart .button, .product-block.grid:hover .groups-button .add-cart .added_to_cart {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  pointer-events: auto;
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}
.product-block.grid-deal {
  border: 1px solid #2441e7;
  margin-bottom: 0;
}
.product-block.grid-deal:hover {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.product-block.grid-deal:hover .name {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
}
.product-block.grid-deal:hover .price {
  -webkit-transform: translateY(0px);
  -ms-transform: translateY(0px);
  -o-transform: translateY(0px);
  transform: translateY(0px);
}
.product-block.grid-deal:before {
  display: none;
}
.product-block.grid-deal .groups-button {
  position: static;
  margin: 15px 0 -5px;
}
@media (min-width: 1200px) {
  .product-block.grid-deal .groups-button {
    margin: 25px 0 -8px;
  }
}
.product-block.grid-deal .groups-button > div {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translate(0, 0) scale(1, 1);
  -moz-transform: translate(0, 0) scale(1, 1);
  -ms-transform: translate(0, 0) scale(1, 1);
  -o-transform: translate(0, 0) scale(1, 1);
  transform: translate(0, 0) scale(1, 1);
}
.product-block.grid-deal .time-wrapper {
  margin-top: 10px;
}
@media (min-width: 1200px) {
  .product-block.grid-deal .time-wrapper {
    margin-top: 20px;
  }
}

.woocommerce .archive-shop .products {
  margin-bottom: 32px;
}
.woocommerce .archive-shop .products.related {
  margin-bottom: 0;
  margin-top: 55px;
}
.woocommerce .archive-shop .products.related .widget-title {
  margin-bottom: 50px;
}

.products-list .product-block-list {
  margin: 0 0 30px;
}

.product-block-list {
  padding: 15px;
  border: 1px solid #eeeeee;
  overflow: hidden;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
@media (min-width: 1200px) {
  .product-block-list {
    padding: 30px;
    background: #fff;
  }
}
.product-block-list .onsale {
  top: 0 !important;
  left: 0 !important;
}
.product-block-list:hover {
  border-color: #2441e7;
}
.product-block-list .product-cat {
  font-family: "Open Sans";
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 12px;
}
.product-block-list .name {
  font-family: "Open Sans";
  font-size: 24px;
  margin: 0 0 10px;
  font-weight: 400;
}
@media (min-width: 1200px) {
  .product-block-list .name {
    font-size: 30px;
  }
}
.product-block-list .cate-wrapper {
  margin: 0 0 8px;
}
.product-block-list .cate-wrapper .product-cats {
  margin: 0;
}
.product-block-list .add-cart {
  margin-bottom: 10px;
  margin-top: 10px;
}
@media (min-width: 1200px) {
  .product-block-list .add-cart {
    margin-top: 20px;
  }
}
.product-block-list .add-cart .added {
  display: none !important;
}
.product-block-list .add-cart .wc-forward {
  width: 100%;
}
.product-block-list .add-cart .added_to_cart,
.product-block-list .add-cart a.button {
  font-size: 14px;
  font-family: "Open Sans";
  display: inline-block;
  width: 100%;
  padding: 15px;
  background: #fff;
  color: #2441e7;
  text-transform: uppercase;
  text-align: center;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  border: 1px solid #2441e7;
  line-height: 1;
}
.product-block-list .add-cart .added_to_cart:hover, .product-block-list .add-cart .added_to_cart:focus,
.product-block-list .add-cart a.button:hover,
.product-block-list .add-cart a.button:focus {
  color: #fff;
  background: #2441e7;
  border-color: #2441e7;
}
.product-block-list .add-cart .added_to_cart.loading:after,
.product-block-list .add-cart a.button.loading:after {
  margin-top: -7px;
}
.product-block-list .top-list-info {
  position: relative;
}
.product-block-list .rating {
  margin-bottom: 15px;
}
.product-block-list .rating > * {
  display: inline-block;
  float: none;
  vertical-align: text-top;
  line-height: 1;
}
.product-block-list .rating .counts {
  margin-left: 2px;
}
.rtl .product-block-list .rating .counts {
  margin-right: 2px;
  margin-left: inherit;
}
.product-block-list .price {
  display: block;
  margin-top: 10px;
  font-family: "Open Sans";
  font-weight: 400;
  font-size: 24px !important;
  color: #2441e7 !important;
  margin: 0;
}
@media (min-width: 1200px) {
  .product-block-list .price {
    margin-top: 20px;
  }
}
.product-block-list .price del {
  color: #b7b7b7;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  font-size: 14px !important;
}
.product-block-list .avaibility-wrapper {
  margin-bottom: 20px;
  font-size: 14px;
}
.product-block-list .bottom-list {
  margin-top: 35px;
}
.product-block-list .bottom-list > div {
  float: left;
}
.rtl .product-block-list .bottom-list > div {
  float: right;
}
.product-block-list .flex-middle {
  overflow: hidden;
}
.product-block-list .left-infor {
  padding-left: 20px;
  position: relative;
}
.rtl .product-block-list .left-infor {
  padding-right: 20px;
  padding-left: inherit;
}
@media (min-width: 1200px) {
  .product-block-list .left-infor {
    padding-left: 30px;
  }
  .rtl .product-block-list .left-infor {
    padding-right: 30px;
    padding-left: inherit;
  }
}
.product-block-list .left-infor:before {
  content: '';
  position: absolute;
  top: -200px;
  left: 0;
  width: 1px;
  height: 1000px;
  background: #eeeeee;
}
.rtl .product-block-list .left-infor:before {
  right: 0;
  left: auto;
}
.product-block-list .bottom-list > * {
  display: inline-block;
  vertical-align: top;
}
.product-block-list .wrapper-image {
  position: relative;
}
.product-block-list .wrapper-image .swatches-wrapper {
  z-index: 8;
  list-style: none;
  padding: 0;
  margin: 0;
  line-height: 1.2;
  text-align: center;
  position: absolute;
  left: 0;
  width: 100%;
  bottom: 20px;
}
.product-block-list .wrapper-image .swatches-wrapper li {
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
}
.rtl .product-block-list .wrapper-image .swatches-wrapper li {
  margin-left: 5px;
  margin-right: inherit;
}
.product-block-list .wrapper-image .swatches-wrapper li:last-child {
  margin-right: 0;
}
.rtl .product-block-list .wrapper-image .swatches-wrapper li:last-child {
  margin-left: 0;
  margin-right: inherit;
}
.product-block-list .wrapper-image .swatches-wrapper .label {
  padding: 5px 8px;
  font-size: 15px;
  color: #7e7e7e;
  font-weight: 500;
  display: inline-block;
  background: #fff;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
.product-block-list .wrapper-image .swatches-wrapper .swatch-color {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.shop-list-small {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  border: 1px solid #eeeeee;
  padding: 10px;
}
@media (min-width: 1200px) {
  .shop-list-small {
    padding: 30px;
  }
}
.shop-list-small .content-left {
  width: 110px;
  padding: 0 15px 0 0;
  float: left;
}
.rtl .shop-list-small .content-left {
  padding: 0 0 0 15px;
}
.rtl .shop-list-small .content-left {
  float: right;
}
.shop-list-small .content-body {
  overflow: hidden;
  width: calc(100% - 110px);
}
.shop-list-small .name {
  margin: 0 0 2px;
  font-size: 16px;
  font-family: "Open Sans";
}
.shop-list-small:hover {
  border-color: #2441e7;
  z-index: 2;
}

.shop-list-normal {
  margin-bottom: 20px;
}
.shop-list-normal .content-left {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  width: 90px;
  float: left;
  border: 1px solid #eeeeee;
  padding: 5px;
}
.rtl .shop-list-normal .content-left {
  float: right;
}
@media (min-width: 1200px) {
  .shop-list-normal .content-left {
    width: 110px;
  }
}
.shop-list-normal .content-body {
  width: calc(100% - 90px);
  overflow: hidden;
  padding-left: 15px;
}
.rtl .shop-list-normal .content-body {
  padding-right: 15px;
  padding-left: inherit;
}
@media (min-width: 1200px) {
  .shop-list-normal .content-body {
    padding-left: 25px;
    width: calc(100% - 110px);
  }
  .rtl .shop-list-normal .content-body {
    padding-right: 25px;
    padding-left: inherit;
  }
}
.shop-list-normal .name {
  margin: 0 0 2px;
  font-size: 16px;
  font-family: "Open Sans";
}
.shop-list-normal:hover .content-left {
  border-color: #2441e7;
}

.shop-list-smallest .name a {
  color: #252525;
}
.shop-list-smallest .name a:hover, .shop-list-smallest .name a:active {
  color: #2441e7;
  text-decoration: none;
}
.shop-list-smallest .content-left {
  width: 90px;
  padding-right: 20px;
}
.rtl .shop-list-smallest .content-left {
  padding-left: 20px;
  padding-right: inherit;
}

.woocommerce.carousel.inner-list-smallest {
  border-top: 1px solid #eeeeee;
}
.woocommerce.carousel.inner-list-smallest .shop-list-smallest {
  margin-bottom: 0;
  border-top: none;
}

.woocommerce .woocommerce-product-rating .star-rating {
  margin: 0;
  display: inline-block;
  float: none;
  vertical-align: middle;
}
.woocommerce .woocommerce-product-rating .woocommerce-review-link {
  display: inline-block;
  font-size: 14px;
  line-height: 1;
}

.woocommerce #content div.product div.summary, .woocommerce div.product div.summary, .woocommerce-page #content div.product div.summary, .woocommerce-page div.product div.summary,
.woocommerce #content div.product div.images, .woocommerce div.product div.images, .woocommerce-page #content div.product div.images, .woocommerce-page div.product div.images {
  width: 100%;
}

.single_variation_wrap div.qty {
  font-size: 15px;
  text-transform: uppercase;
  color: #7e7e7e;
  font-family: "Open Sans";
  margin-top: 10px;
  margin-right: 10px;
}
.rtl .single_variation_wrap div.qty {
  margin-left: 10px;
  margin-right: inherit;
}

@media (min-width: 1024px) {
  .wrapper-shop {
    padding-top: 50px;
    padding-bottom: 50px;
  }
}
.wrapper-shop .apus-pagination {
  border-top: 1px solid #eeeeee;
  padding-top: 40px;
  margin-top: 0;
}
.wrapper-shop aside.sidebar {
  background: transparent;
}

.thumbnails-image ul {
  list-style: none;
  margin: 0;
  padding: 0;
}
.thumbnails-image .prev,
.thumbnails-image .next {
  display: block;
  width: 100%;
  text-align: center;
  font-size: 18px;
  color: #000;
}
.thumbnails-image .thumb-link {
  display: block;
  opacity: 0.4;
  filter: alpha(opacity=40);
  margin: 10px 0;
}
.thumbnails-image .thumb-link:hover, .thumbnails-image .thumb-link.active {
  opacity: 1;
  filter: alpha(opacity=100);
}

.user_photo_thumbs {
  list-style: none;
  padding: 0;
  text-align: center;
  margin: 10px 0 0;
}
.user_photo_thumbs li {
  display: inline-block;
  margin: 0 4px;
  width: 70px;
  opacity: 0.4;
  filter: alpha(opacity=40);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.user_photo_thumbs li:hover, .user_photo_thumbs li.active, .user_photo_thumbs li:active {
  opacity: 1;
  filter: alpha(opacity=100);
}

.user_photo {
  margin-top: 50px;
}

.delivery_info {
  text-align: center;
  background: #f5f5f5;
  font-size: 14px;
  padding: 8px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.delivery_info:hover {
  background: gainsboro;
}
.delivery_info i {
  font-size: 16px;
  margin-right: 10px;
}
.rtl .delivery_info i {
  margin-left: 10px;
  margin-right: inherit;
}

@media (min-width: 1200px) {
  .details-product .left-detail {
    padding-right: 0;
  }
  .rtl .details-product .left-detail {
    padding-left: 0;
    padding-right: inherit;
  }
}
.details-product .shipping_info {
  margin-top: 15px;
  font-size: 14px;
  color: #b7b7b7;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
@media (min-width: 1200px) {
  .details-product .shipping_info {
    margin-top: 40px;
  }
}
.details-product .shipping_info:hover {
  color: #7e7e7e;
}
.details-product .shipping_info ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.details-product .shipping_info ul i {
  margin-right: 6px;
}
.rtl .details-product .shipping_info ul i {
  margin-left: 6px;
  margin-right: inherit;
}
.details-product .shipping_info ul li {
  margin-bottom: 0px;
}
@media (min-width: 1200px) {
  .details-product .shipping_info ul li {
    margin-bottom: 5px;
  }
}
.details-product .shipping_info ul li:last-child {
  margin-bottom: 0;
}
.details-product .price-rating-wrapper {
  margin-top: 10px;
  clear: both;
  overflow: hidden;
}
@media (min-width: 1200px) {
  .details-product .price-rating-wrapper {
    margin-top: 20px;
  }
}
.details-product .price-rating-wrapper .price {
  margin-right: 15px !important;
  line-height: 1.4;
}
.rtl .details-product .price-rating-wrapper .price {
  margin-left: 15px !important;
  margin-right: inherit;
}
.details-product .price-rating-wrapper .price del {
  display: block !important;
}
.details-product .price-rating-wrapper > * {
  display: inline-block;
  vertical-align: bottom;
}
.details-product .pro-info {
  margin: 0 0 20px;
}
@media (min-width: 1200px) {
  .details-product .pro-info {
    font-size: 30px;
  }
}
.details-product .popup-video {
  background: #fff;
  height: 40px;
  line-height: 40px;
  min-width: 40px;
  overflow: hidden;
  display: inline-block;
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
  -webkit-align-items: center;
  /* Safari 7.0+ */
  flex-direction: row;
  -webkit-flex-direction: row;
}
.details-product .popup-video i {
  height: 40px;
  line-height: 40px;
  width: 40px;
  font-size: 13px;
  text-align: center;
  text-indent: 3px;
}
.details-product .popup-video span {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  white-space: nowrap;
  max-width: 0;
  padding: 0;
  overflow: hidden;
}
.details-product .popup-video:hover span {
  max-width: 280px;
  padding-right: 12px;
}
.rtl .details-product .popup-video:hover span {
  padding-left: 12px;
  padding-right: inherit;
}
.details-product .product-cat {
  font-family: "Open Sans";
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 12px;
}
.details-product .product-cat a {
  color: #2441e7;
}
.details-product div.video {
  z-index: 8;
  position: absolute;
  left: 10px;
  bottom: 10px;
}
.rtl .details-product div.video {
  right: 10px;
  left: auto;
}
@media (min-width: 768px) {
  .details-product div.video {
    left: 20px;
    bottom: 20px;
  }
  .rtl .details-product div.video {
    right: 20px;
    left: auto;
  }
}
.details-product .apus-countdown {
  margin-top: 5px;
}
.details-product .special-product {
  padding: 8px 0;
}
.details-product .apus-countdown .times {
  margin-bottom: 5px;
}
.details-product .apus-countdown .times > span {
  color: #ff1053;
  margin-bottom: 5px;
}
.details-product .apus-countdown .times > div {
  text-align: center;
  vertical-align: middle;
  min-width: 40px;
  font-size: 12px;
  display: inline-block;
  font-weight: 400;
  text-transform: uppercase;
  margin: 0 5px;
  padding: 8px;
}
.details-product .apus-countdown .times > div:first-child {
  margin-left: 0;
}
.rtl .details-product .apus-countdown .times > div:first-child {
  margin-right: 0;
  margin-left: inherit;
}
.details-product .apus-countdown .times > div span {
  font-weight: 500;
  margin-bottom: 5px;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
  font-size: 18px;
  display: block;
  color: #252525;
}
.details-product .top-content {
  margin-bottom: 54px;
}
.details-product .apus-woocommerce-product-gallery-thumbs .slick-slide:hover .thumbs-inner, .details-product .apus-woocommerce-product-gallery-thumbs .slick-slide:active .thumbs-inner, .details-product .apus-woocommerce-product-gallery-thumbs .slick-slide.slick-current .thumbs-inner {
  border-color: #2441e7;
}
.details-product .apus-woocommerce-product-gallery-thumbs .slick-slide .thumbs-inner {
  padding: 5px;
  max-width: 100%;
  display: block;
  cursor: pointer;
  position: relative;
  border: 1px solid #eeeeee;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.details-product .apus-woocommerce-product-gallery-thumbs .slick-slide .thumbs-inner:hover {
  border-color: #2441e7;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical {
  margin: 0;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-slide {
  padding: 0;
  margin-bottom: 10px;
  border: none;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow {
  text-align: center;
  background-color: transparent !important;
  border: 0 !important;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow i {
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 1px 1px rgba(0, 0, 0, 0.2);
  line-height: 30px;
  display: inline-block;
  -webkit-transition: all 0.2s ease-in-outs 0s;
  -o-transition: all 0.2s ease-in-outs 0s;
  transition: all 0.2s ease-in-outs 0s;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow:hover i, .details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-arrow:focus i {
  color: #fff;
  background-color: #2441e7;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-prev {
  top: inherit;
  bottom: 100%;
  -webkit-transform: translate(0, -5px);
  -ms-transform: translate(0, -5px);
  -o-transform: translate(0, -5px);
  transform: translate(0, -5px);
  width: 100%;
  left: 0;
  font-size: 11px;
}
.details-product .apus-woocommerce-product-gallery-thumbs.vertical .slick-next {
  width: 100%;
  top: 100%;
  bottom: inherit;
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
  right: 0;
  font-size: 11px;
}
.details-product .image-mains {
  max-width: 100%;
  position: relative;
}
.details-product .image-mains .apus-woocommerce-product-gallery-wrapper.full-width {
  width: 100% !important;
  float: none !important;
}
.details-product .image-mains.thumbnails-bottom .apus-woocommerce-product-gallery-wrapper {
  margin-bottom: 30px;
}
.details-product .image-mains.thumbnails-left .apus-woocommerce-product-gallery-wrapper {
  width: calc(100% - 100px);
  float: right;
}
@media (min-width: 1200px) {
  .details-product .image-mains.thumbnails-left .apus-woocommerce-product-gallery-wrapper {
    width: calc(100% - 160px);
  }
}
.details-product .image-mains.thumbnails-left .wrapper-thumbs {
  float: left;
  width: 100px;
  padding-right: 20px;
}
@media (min-width: 1200px) {
  .details-product .image-mains.thumbnails-left .wrapper-thumbs {
    padding-right: 30px;
    width: 160px;
  }
}
@media (max-width: 767px) {
  .details-product .image-mains.thumbnails-left .apus-woocommerce-product-gallery-wrapper {
    width: calc(100% - 70px);
  }
  .details-product .image-mains.thumbnails-left .wrapper-thumbs {
    width: 70px;
    padding-right: 10px;
  }
}
.details-product .image-mains.thumbnails-right .apus-woocommerce-product-gallery-wrapper {
  width: calc(100% - 160px);
  float: left;
}
.details-product .image-mains.thumbnails-right .wrapper-thumbs {
  float: right;
  width: 160px;
  padding-left: 20px;
}
@media (min-width: 1200px) {
  .details-product .image-mains.thumbnails-right .wrapper-thumbs {
    padding-left: 30px;
  }
}
@media (max-width: 767px) {
  .details-product .image-mains.thumbnails-right .apus-woocommerce-product-gallery-wrapper {
    width: calc(100% - 70px);
  }
  .details-product .image-mains.thumbnails-right .wrapper-thumbs {
    width: 70px;
    padding-left: 10px;
  }
}
.details-product .description .title {
  font-size: 21px;
}
.details-product .apus-woocommerce-product-gallery-wrapper {
  position: relative;
  border: 2px solid #eeeeee;
  padding: 30px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.details-product .apus-woocommerce-product-gallery-wrapper .downsale {
  font-size: 12px;
  font-weight: 500;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9;
  padding: 2px 10px;
  background: #d42e2e;
  color: #fff;
  display: inline-block;
  vertical-align: middle;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
.lt-ie8 .details-product .apus-woocommerce-product-gallery-wrapper .downsale {
  display: inline;
  zoom: 1;
}
.details-product .apus-woocommerce-product-gallery-wrapper .apus-woocommerce-product-gallery {
  margin: 0;
}
.details-product .apus-woocommerce-product-gallery-wrapper .apus-woocommerce-product-gallery .slick-slide {
  padding: 0;
}
.details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger {
  z-index: 8;
  bottom: 30px;
  border: 0;
  position: absolute;
  color: #0a0a0a;
  background-color: #edeff7;
  opacity: 1;
  filter: alpha(opacity=100);
  left: 30px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.rtl .details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger {
  right: 30px;
  left: auto;
}
.details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger:hover {
  color: #fff;
  background-color: #2441e7;
}
.details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger:active {
  color: #fff;
  background-color: #2441e7;
}
.details-product .apus-woocommerce-product-gallery-wrapper .woocommerce-product-gallery__trigger [class*="icon"]:before {
  margin: 0;
  font-size: 24px;
}
.details-product .apus-woocommerce-product-gallery-wrapper:hover .woocommerce-product-gallery__trigger {
  opacity: 1;
  filter: alpha(opacity=100);
}
.details-product .woocommerce-product-details__short-description.hideContent {
  overflow: hidden;
  height: 60px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.details-product .woocommerce-variation-add-to-cart {
  width: 100%;
  overflow: visible;
}
.details-product .woocommerce-variation-add-to-cart:before, .details-product .woocommerce-variation-add-to-cart:after {
  content: " ";
  display: table;
}
.details-product .woocommerce-variation-add-to-cart:after {
  clear: both;
}
.details-product .list li {
  margin-bottom: 10px;
}
.details-product .list i {
  color: #2441e7;
  margin-right: 8px;
}
.rtl .details-product .list i {
  margin-left: 8px;
  margin-right: inherit;
}
.details-product .woocommerce-variation-price {
  margin-bottom: 30px;
  margin-top: 26px;
}
.details-product .product_meta {
  line-height: 1.1;
  clear: both;
  padding-top: 30px;
  margin-top: 0;
  color: #7e7e7e;
}
.details-product .product_meta a {
  color: #7e7e7e;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.details-product .product_meta a:hover {
  text-decoration: underline;
}
.details-product .product_meta a:active {
  text-decoration: underline;
}
.details-product .product_meta > * {
  margin-bottom: 10px;
  display: block;
  border: 0;
}
.details-product .product_meta .sku {
  outline: none;
}
.details-product .information {
  position: relative;
  padding-left: 20px;
}
.rtl .details-product .information {
  padding-right: 20px;
  padding-left: inherit;
}
.details-product .information .summary {
  width: 100%;
  float: none !important;
  margin: 0 !important;
}
.details-product .information .single_variation_wrap {
  padding-top: 10px;
}
.details-product .information .price {
  font-size: 26px !important;
}
.details-product .information .woocommerce-product-rating {
  margin-bottom: 20px !important;
}
.details-product .information .woocommerce-product-rating .text-customer {
  display: none;
}
.details-product .information .woocommerce-product-details__short-description {
  margin-bottom: 25px;
}
.details-product .information .woocommerce-product-details__short-description p:last-child {
  margin-bottom: 0;
}
.details-product .information .view-more-desc {
  font-size: 14px;
  color: #b7b7b7;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.details-product .information .view-more-desc:hover {
  color: #252525;
}
.details-product .information .view-more-desc.view-less {
  color: #e44343;
}
.details-product .information .woocommerce-product-details__short-description-wrapper.v2 {
  margin-bottom: 15px;
}
@media (min-width: 1200px) {
  .details-product .information .woocommerce-product-details__short-description-wrapper.v2 {
    margin-bottom: 30px;
  }
}
.details-product .information .woocommerce-product-details__short-description-wrapper.v2 .woocommerce-product-details__short-description {
  margin-bottom: 3px;
}
.details-product .information .top-info-detail {
  margin-bottom: 15px;
}
.details-product .information .cart {
  width: 100%;
  margin: 10px 0 !important;
}
.details-product .information .cart.grouped_form, .details-product .information .cart.variations_form {
  outline: normal;
}
@media (min-width: 1200px) {
  .details-product .information .cart {
    margin: 22px 0 10px !important;
  }
}
.details-product .information .cart .group_table tr td:first-child div.quantity {
  margin: 0 !important;
}
.details-product .information .cart div.quantity-wrapper {
  margin: 0;
  overflow: visible;
  float: left;
}
.rtl .details-product .information .cart div.quantity-wrapper {
  float: right;
}
.details-product .information .cart div.quantity-wrapper > * {
  display: inline-block;
  vertical-align: middle;
  float: none !important;
}
.lt-ie8 .details-product .information .cart div.quantity-wrapper > * {
  display: inline;
  zoom: 1;
}
.details-product .information .cart div.quantity-wrapper > label {
  display: none;
}
.details-product .information .cart.grouped_form .quantity-wrapper {
  margin: 0 !important;
}
.details-product .information .cart.grouped_form .quantity-wrapper label {
  display: none;
}
.details-product .information .clear {
  display: none;
}
.details-product .information .product_title {
  clear: both;
}
.details-product .title-cat-wishlist-wrapper {
  position: relative;
  margin-bottom: 20px;
  padding-right: 30px;
}
.rtl .details-product .title-cat-wishlist-wrapper {
  padding-left: 30px;
  padding-right: inherit;
}
@media (min-width: 1200px) {
  .details-product .title-cat-wishlist-wrapper {
    margin-bottom: 30px;
  }
}
.details-product .apus-social-share {
  margin-top: 34px;
}
.details-product .apus-social-share .bo-sicolor {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
}
.details-product .apus-social-share span {
  font-family: "Nunito", Arial, sans-serif;
  color: #0a0a0a;
  font-size: 18px;
  line-height: 24px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 20px;
}
.lt-ie8 .details-product .apus-social-share span {
  display: inline;
  zoom: 1;
}
.rtl .details-product .apus-social-share span {
  margin-left: 20px;
  margin-right: inherit;
}
.details-product .apus-social-share a {
  margin-right: 20px;
  display: inline-block;
  color: #b3b7c8;
  font-size: 16px;
}
.rtl .details-product .apus-social-share a {
  margin-left: 20px;
  margin-right: inherit;
}
.details-product .apus-social-share a:hover {
  color: #0a0a0a;
}
.details-product .apus-social-share a:focus {
  color: #0a0a0a;
}
.details-product .apus-social-share a:active {
  color: #0a0a0a;
}
.details-product .apus-discounts {
  margin: 20px 0 15px;
  padding: 15px 20px;
  background: #eceff6;
  font-size: 13px;
}
.details-product .apus-discounts ul {
  margin: 0;
  list-style: none;
  padding: 0;
}
.details-product .apus-discounts ul li {
  margin: 0 0 3px;
}
.details-product .apus-discounts ul li:before {
  font-family: 'FontAwesome';
  color: #2441e7;
  content: "\f00c";
  margin-right: 8px;
}
.rtl .details-product .apus-discounts ul li:before {
  margin-left: 8px;
  margin-right: inherit;
}
.details-product .apus-discounts .icon {
  display: inline-block;
  vertical-align: middle;
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  color: #fff;
  background: #a7b5d5;
  font-size: 14px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  margin-right: 10px;
}
.rtl .details-product .apus-discounts .icon {
  margin-left: 10px;
  margin-right: inherit;
}
.details-product .apus-discounts .title {
  font-size: 18px;
  margin: 0 0 10px;
}
.details-product .product-free-gift {
  margin: 0 0 20px;
  padding: 15px 20px;
  background: #f2dede;
}
.details-product .product-free-gift .icon {
  display: inline-block;
  vertical-align: middle;
  width: 35px;
  height: 35px;
  text-align: center;
  line-height: 35px;
  color: #fff;
  background: #e23e1d;
  font-size: 14px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  margin-right: 10px;
}
.rtl .details-product .product-free-gift .icon {
  margin-left: 10px;
  margin-right: inherit;
}
.details-product .product-free-gift .title {
  font-size: 18px;
  margin: 0 0 10px;
}
.details-product .product-free-gift .list-gift {
  font-size: 13px;
  list-style: none;
  padding: 0;
  margin: 0;
}
.details-product .product-free-gift .list-gift li {
  margin-bottom: 3px;
}
.details-product .product-free-gift .list-gift i {
  color: #e23e1d;
}
.details-product .product-free-gift .hightcolor {
  font-weight: 500;
  color: #e23e1d;
}

.details-product.layout-v1 .summary-right .summary {
  padding-left: 20px;
  position: relative;
}
.rtl .details-product.layout-v1 .summary-right .summary {
  padding-right: 20px;
  padding-left: inherit;
}
@media (min-width: 1600px) {
  .details-product.layout-v1 .summary-right .summary {
    padding-left: 50px;
  }
  .rtl .details-product.layout-v1 .summary-right .summary {
    padding-right: 50px;
    padding-left: inherit;
  }
}
.details-product.layout-v1 .summary-right .summary:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 1000px;
  background: #eeeeee;
}
.rtl .details-product.layout-v1 .summary-right .summary:before {
  right: 0;
  left: auto;
}
@media (min-width: 1600px) {
  .details-product.layout-v1 .summary-left .summary {
    padding-right: 20px;
  }
  .rtl .details-product.layout-v1 .summary-left .summary {
    padding-left: 20px;
    padding-right: inherit;
  }
}
.details-product.layout-v2 .image-mains.thumbnails-bottom .apus-woocommerce-product-gallery-wrapper {
  margin-top: 10px;
}
.details-product.layout-v2 .product_meta > * {
  display: block;
  border: none;
  width: 100%;
  padding: 0;
  margin: 0 0 8px;
}
.details-product.layout-v2 .product_meta > *:last-child {
  margin: 0;
}
.details-product.layout-v2 .wrapper-thumbs .slick-slider {
  margin-left: -5px;
  margin-right: -5px;
}
.details-product.layout-v2 .wrapper-thumbs .slick-slider .slick-slide {
  padding-right: 5px;
  padding-left: 5px;
}
.details-product.layout-v2 .tabs-v1 .tab-content {
  padding: 15px;
  margin: 0;
  border-width: 0 1px 1px;
  border-style: solid;
  border-color: #eeeeee;
}
@media (min-width: 1200px) {
  .details-product.layout-v2 .tabs-v1 .tab-content {
    padding: 30px;
  }
}
.details-product.layout-v2 .tabs-v1 #commentform,
.details-product.layout-v2 .tabs-v1 table.shop_attributes {
  margin: 0;
}

.accessoriesproducts-wrapper {
  position: relative;
}
.accessoriesproducts-wrapper.loading:before {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  content: '';
  background: url("../images/loading-quick.gif") center center no-repeat rgba(255, 255, 255, 0.9);
}

.accessoriesproducts .product-block.grid {
  margin-bottom: 25px;
}
.accessoriesproducts .product-block.grid .accessory-add-product {
  position: absolute;
  left: 10px;
  bottom: -25px;
}
.rtl .accessoriesproducts .product-block.grid .accessory-add-product {
  right: 10px;
  left: auto;
}
.accessoriesproducts .check-all-items-wrapper {
  margin: 0 0 10px;
}
.accessoriesproducts .check-all-items-wrapper input {
  margin-right: 6px;
}
.rtl .accessoriesproducts .check-all-items-wrapper input {
  margin-left: 6px;
  margin-right: inherit;
}
.accessoriesproducts .total-price-wrapper {
  font-size: 14px;
  color: #252525;
  margin: 0 0 5px;
}
.accessoriesproducts .total-price {
  display: block;
  color: #ff1053;
  font-size: 18px;
  font-weight: normal;
}

/*------------------------------------*\
    Product Category and Subcategories
\*------------------------------------*/
.product-category .product-category-content {
  position: relative;
  overflow: hidden;
  min-height: 45px;
  margin: 0 0 30px 0;
}
.product-category .product-category-image {
  display: block;
}
.product-category .product-category-image img {
  display: block;
  width: 100% \9;
  max-width: 100%;
  height: auto;
}
.product-category .product-category-title {
  text-transform: none;
  position: absolute;
  text-align: center;
  bottom: 0;
  left: 0;
  width: 100%;
  font-weight: 400;
  font-size: 15px;
  color: #fff;
  margin: 0;
  padding: 15px 10px;
  background: rgba(0, 0, 0, 0.3);
}
.rtl .product-category .product-category-title {
  right: 0;
  left: auto;
}
.product-category .product-category-title .count {
  background: transparent;
  color: #fff;
}

/**
 *
 *  Woocommerce Form
 */
.form-row .checkbox, .form-row .input-radio {
  margin-bottom: 0;
  margin-top: 0;
}

.woocommerce form .form-row {
  margin: 0 0 20px;
  padding: 0;
}
.woocommerce form .form-row.place-order {
  padding-bottom: 0;
  margin-bottom: 0;
}

.woocommerce .col2-set .col-2, .woocommerce-page .col2-set .col-2,
.woocommerce .col2-set .col-1, .woocommerce-page .col2-set .col-1 {
  width: 100%;
}

/* End
------------------------------------------------*/
p.demo_store {
  top: 0;
  position: fixed;
  left: 0;
  right: 0;
  width: 100%;
  height: auto;
  text-align: center;
  font-size: 30px;
  padding: .5em 0;
  z-index: 99998;
  border: 1px solid #d5d5d5;
  -webkit-box-shadow: 0, 0, 0, 3px, rgba(255, 255, 255, 0.2);
  box-shadow: 0, 0, 0, 3px, rgba(255, 255, 255, 0.2);
}
.rtl p.demo_store {
  right: 0;
  left: auto;
}
.rtl p.demo_store {
  left: 0;
  right: auto;
}
.rtl p.demo_store {
  text-align: right;
}

/*-------------------------------*\
    Utilities
\*------------------------------------*/
.woocommerce #reviews #comments ol.commentlist {
  padding: 0;
}

.woocommerce #reviews #comments ol.commentlist li {
  margin: 0;
  padding: 0 0 30px;
  line-height: 1.5;
}
.woocommerce #reviews #comments ol.commentlist li .meta .woocommerce-review__author {
  color: #0a0a0a;
  font-size: 20px;
  font-weight: 400;
  font-family: "Nunito", Arial, sans-serif;
}
.woocommerce #reviews #comments ol.commentlist li .apus-avata {
  min-width: 80px;
}
@media (min-width: 768px) {
  .woocommerce #reviews #comments ol.commentlist li .apus-avata {
    min-width: 100px;
  }
}
.woocommerce #reviews #comments ol.commentlist li .apus-avata .apus-image {
  display: inline-block;
}
.woocommerce #reviews #comments ol.commentlist li img.avatar {
  border: none;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  padding: 0;
  position: relative;
}
.woocommerce #reviews #comments ol.commentlist li .star-rating {
  margin: 6px 0;
  float: none;
}
.woocommerce #reviews #comments ol.commentlist li .star-rating:before {
  color: #d0d23c;
}
.woocommerce #reviews #comments ol.commentlist li .star-rating span:before {
  color: #d0d23c;
}
.woocommerce #reviews #comments ol.commentlist li .top-info {
  margin: 0 0 8px;
}
.woocommerce #reviews #comments ol.commentlist li .dokan-review-author-img {
  float: left;
  padding-right: 30px;
}
.rtl .woocommerce #reviews #comments ol.commentlist li .dokan-review-author-img {
  float: right;
}
.rtl .woocommerce #reviews #comments ol.commentlist li .dokan-review-author-img {
  padding-left: 30px;
  padding-right: inherit;
}
.woocommerce #reviews #comments ol.commentlist li .comment-text {
  overflow: hidden;
  border: none;
  padding: 0;
  margin: 0;
}
.woocommerce #reviews #comments ol.commentlist li .description {
  margin-top: 10px;
}
.woocommerce #reviews #comments ol.commentlist li .description p {
  margin: 0;
}
.woocommerce #reviews #comments ol.commentlist li .apus-author {
  font-size: 16px;
  color: #252525;
  margin: 0;
}
.woocommerce #reviews #comments ol.commentlist li .date {
  font-size: 14px;
  color: #b3b7c8;
}
.woocommerce #reviews #comments ol.commentlist li .content-comment {
  margin-top: 15px;
}
.woocommerce #reviews #comments ol.commentlist li .comment-text {
  padding-left: 30px;
}
.rtl .woocommerce #reviews #comments ol.commentlist li .comment-text {
  padding-right: 30px;
  padding-left: inherit;
}

.woocommerce p.stars a[class*="star-"]:before {
  display: none;
}

#respond .comment-reply-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 24px !important;
}
#respond .comment-reply-title #cancel-comment-reply-link {
  color: #e44343;
}
#respond #commentform {
  margin-bottom: 0;
}
#respond label {
  cursor: pointer;
  color: #7e7e7e;
  font-weight: 400;
}
#respond .form-submit input {
  left: auto;
}
.rtl #respond .form-submit input {
  right: auto;
  left: auto;
}
#respond textarea {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
#respond p.stars {
  position: relative;
  padding: 0 0 10px;
}
#respond p.stars a {
  text-indent: -9999px;
  position: relative;
  color: #d0d23c;
  font-weight: 700;
  display: inline-block;
  vertical-align: middle;
  margin-right: 1em;
}
.lt-ie8 #respond p.stars a {
  display: inline;
  zoom: 1;
}
.rtl #respond p.stars a {
  margin-left: 1em;
  margin-right: inherit;
}
#respond p.stars a:last-child {
  border-right: 0;
}
.rtl #respond p.stars a:last-child {
  border-left: 0;
  border-right: inherit;
}
#respond p.stars a.star-1:after, #respond p.stars a.star-2:after, #respond p.stars a.star-3:after, #respond p.stars a.star-4:after, #respond p.stars a.star-5:after {
  top: -6px;
  font-family: "FontAwesome";
  text-indent: 0;
  position: absolute;
  left: 0;
  color: #cccccc;
}
.rtl #respond p.stars a.star-1:after, .rtl #respond p.stars a.star-2:after, .rtl #respond p.stars a.star-3:after, .rtl #respond p.stars a.star-4:after, .rtl #respond p.stars a.star-5:after {
  right: 0;
  left: auto;
}
#respond p.stars a.star-1:hover:after, #respond p.stars a.star-1.active:after, #respond p.stars a.star-2:hover:after, #respond p.stars a.star-2.active:after, #respond p.stars a.star-3:hover:after, #respond p.stars a.star-3.active:after, #respond p.stars a.star-4:hover:after, #respond p.stars a.star-4.active:after, #respond p.stars a.star-5:hover:after, #respond p.stars a.star-5.active:after {
  color: #d0d23c;
}
#respond p.stars a.star-1 {
  width: 1.5em;
}
#respond p.stars a.star-1:after {
  content: "\f005";
}
#respond p.stars a.star-1:hover:after, #respond p.stars a.star-1.active:after {
  content: "\f005";
}
#respond p.stars a.star-2 {
  width: 2.5em;
}
#respond p.stars a.star-2:after {
  content: "\f005\f005";
}
#respond p.stars a.star-2:hover:after, #respond p.stars a.star-2.active:after {
  content: "\f005\f005";
}
#respond p.stars a.star-3 {
  width: 3.5em;
}
#respond p.stars a.star-3:after {
  content: "\f005\f005\f005";
}
#respond p.stars a.star-3:hover:after, #respond p.stars a.star-3.active:after {
  content: "\f005\f005\f005";
}
#respond p.stars a.star-4 {
  width: 4.5em;
}
#respond p.stars a.star-4:after {
  content: "\f005\f005\f005\f005";
}
#respond p.stars a.star-4:hover:after, #respond p.stars a.star-4.active:after {
  content: "\f005\f005\f005\f005";
}
#respond p.stars a.star-5 {
  width: 5.5em;
  border: 0;
}
#respond p.stars a.star-5:after {
  content: "\f005\f005\f005\f005\f005";
}
#respond p.stars a.star-5:hover:after, #respond p.stars a.star-5.active:after {
  content: "\f005\f005\f005\f005\f005";
}
#respond p.stars a.active:after, #respond p.stars a:hover:after, #respond p.stars a:active:after {
  color: #d0d23c;
}

.woocommerce #reviews #comment {
  height: 150px;
  resize: none;
}
.woocommerce #reviews .form-control {
  background-color: #fff;
}

/*------------------------------------*\
    Quantity inputs
\*------------------------------------*/
.woocommerce .quantity .qty {
  width: 80px;
  font-size: 14px;
  height: 50px;
  border: 1px solid #eeeeee;
  padding: 10px 20px;
  font-weight: 500;
  color: #7e7e7e;
}

.woocommerce .quantity .reader-text {
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 0;
  margin-right: 10px;
}
.rtl .woocommerce .quantity .reader-text {
  margin-left: 10px;
  margin-right: inherit;
}

.woocommerce a.remove {
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}

/*------------------------------------*\
    Forms
\*------------------------------------*/
.form-row:before, .form-row:after {
  content: " ";
  display: table;
}
.form-row:after {
  clear: both;
}
.form-row label.hidden {
  visibility: hidden;
}
.form-row label.inline {
  display: inline;
}
.form-row label {
  display: block;
  font-weight: 500;
}
.form-row select {
  cursor: pointer;
}
.form-row .required {
  color: #ff003a;
  font-weight: 700;
  border: 0;
}
.form-row .input-text {
  width: 100%;
  padding: 8px 10px;
}
.form-row.form-row-first {
  width: 47%;
  float: left;
}
.rtl .form-row.form-row-first {
  float: right;
}
.form-row.form-row-last {
  width: 47%;
  float: right;
}
.rtl .form-row.form-row-last {
  float: left;
}
.form-row.form-row-wide {
  clear: both;
}

.select2-container .select2-choice {
  padding: 5px 7px;
}

.product-quantity .input-text .input-sm {
  height: 45px;
  padding: 10px 10px;
  font-size: 13px;
  line-height: 1.5;
  border-radius: 0;
}
.product-quantity .input-text select.input-sm {
  height: 45px;
  line-height: 45px;
}
.product-quantity .input-text textarea.input-sm,
.product-quantity .input-text select[multiple].input-sm {
  height: auto;
}

.i-am-new li {
  background-image: none !important;
  background-color: #fff !important;
  -webkit-border-radius: 0px !important;
  border-radius: 0px !important;
}
.i-am-new li .noty_message {
  padding: 20px 0 !important;
}

/*------------------------------------*\
    Mini cart and wishlist
\*------------------------------------*/
.total-minicart {
  display: none;
}

.wishlist-icon,
.mini-cart {
  display: inline-block;
  position: relative;
  padding: 0;
  color: #252525;
  line-height: 1;
}
.wishlist-icon i,
.mini-cart i {
  font-size: 21px;
  margin: 0 !important;
}
.wishlist-icon .count,
.mini-cart .count {
  position: absolute;
  top: -5px;
  font-size: 10px;
  color: #fff;
  background-color: #102495;
  line-height: 16px;
  min-width: 15px;
  padding: 0;
  overflow: hidden;
  text-align: center;
  right: -5px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  display: inline-block;
  vertical-align: middle;
  width: 17px;
  height: 17px;
}
.rtl .wishlist-icon .count, .rtl
.mini-cart .count {
  left: -5px;
  right: auto;
}
.lt-ie8 .wishlist-icon .count, .lt-ie8
.mini-cart .count {
  display: inline;
  zoom: 1;
}

.wishlist-icon i {
  margin-right: 6px;
}
.rtl .wishlist-icon i {
  margin-left: 6px;
  margin-right: inherit;
}

/*------------------------------------*\
    Star ratings
\*------------------------------------*/
.woocommerce .star-rating span::before,
.woocommerce p.stars a:hover::after,
.woocommerce p.stars a::after,
.woocommerce .star-rating span::before {
  color: #d0d23c;
}
.woocommerce .star-rating {
  word-break: normal;
}
.woocommerce .star-rating:before {
  color: #d0d23c;
  opacity: 1;
  filter: alpha(opacity=100);
}
.woocommerce .star-rating span:before {
  color: #d0d23c;
}
.woocommerce .woocommerce-review-link {
  color: #999591;
}

/*------------------------------------*\
    Filter
\*------------------------------------*/
.archive-shop .page-title {
  display: none;
}

.show-filter {
  font-size: 18px;
  color: #2441e7;
  cursor: pointer;
  font-weight: 400;
  text-transform: uppercase;
  letter-spacing: 1px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.show-filter:hover, .show-filter:active {
  color: #2441e7;
}
.show-filter i {
  margin-left: 10px;
}
.rtl .show-filter i {
  margin-right: 10px;
  margin-left: inherit;
}

.apus-shop-menu {
  font-size: 15px;
  margin: 0;
  position: relative;
}
.apus-shop-menu .filter-action i {
  margin-right: 3px;
}
.rtl .apus-shop-menu .filter-action i {
  margin-left: 3px;
  margin-right: inherit;
}
.apus-shop-menu ul.apus-filter-menu {
  padding: 0;
  margin: 5px 0 0;
  list-style: none;
  float: right;
}
.rtl .apus-shop-menu ul.apus-filter-menu {
  float: left;
}
.apus-shop-menu ul.apus-filter-menu li {
  display: inline-block;
}
.apus-shop-menu ul.apus-categories {
  float: left;
  padding: 0;
  margin: 2px 0 0;
  list-style: none;
}
.rtl .apus-shop-menu ul.apus-categories {
  float: right;
}
.apus-shop-menu ul.apus-categories li {
  display: inline-block;
  margin-right: 40px;
}
.rtl .apus-shop-menu ul.apus-categories li {
  margin-left: 40px;
  margin-right: inherit;
}
.apus-shop-menu ul.apus-categories li a {
  text-transform: capitalize;
  padding: 0;
  font-size: 16px;
  font-weight: 500;
  color: #252525;
  position: relative;
  display: inline-block;
}
.apus-shop-menu ul.apus-categories li .product-count {
  font-size: 14px;
  color: #7e7e7e;
  margin: 0 2px;
  vertical-align: top;
  display: inline-block;
}
.apus-shop-menu ul.apus-categories li.current-cat > a {
  color: #2441e7;
}
.apus-shop-menu ul.apus-categories .apus-shop-sub-categories {
  padding: 0px;
  margin: 10px 0 0;
}
.apus-shop-menu ul.apus-categories .apus-shop-sub-categories li a {
  font-size: 16px;
}
.apus-shop-menu .content-inner #apus-orderby {
  margin-left: 40px;
}
.rtl .apus-shop-menu .content-inner #apus-orderby {
  margin-right: 40px;
  margin-left: inherit;
}

.apus-shop-header {
  background: transparent;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.apus-shop-header.filter-active {
  background: #fff;
  border-bottom: 1px solid #eeeeee;
  margin-bottom: 30px;
}
.apus-shop-header .apus-sidebar-header {
  display: none;
  border: 1px solid #2441e7;
  padding: 20px 30px;
  background: #fff;
}
.apus-shop-header .apus-widget-scroll ul li {
  padding: 0;
  list-style: none;
  font-size: 14px;
  margin: 0 0 10px;
}
.apus-shop-header .apus-sidebar-inner {
  padding: 0 15px;
}
.apus-shop-header .apus-sidebar-inner ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.apus-shop-header .apus-sidebar-inner .apus-widget-title {
  font-weight: 400;
  font-size: 18px;
  text-transform: capitalize;
  margin: 0 0 20px;
}
.apus-shop-header .widget_layered_nav ul li a, .apus-shop-header .product-categories li a {
  font-size: 14px;
  padding: 0 !important;
}
.apus-shop-header .widget_layered_nav ul li .count, .apus-shop-header .product-categories li .count {
  float: none;
}
.apus-shop-header .widget_layered_nav li.chosen,
.apus-shop-header .product-categories li.chosen {
  color: #2441e7;
}
.apus-shop-header .widget_layered_nav li.chosen > a,
.apus-shop-header .product-categories li.chosen > a {
  color: #2441e7;
}

.apus-categories-dropdown {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: none;
  color: #252525;
  font-size: 14px;
  margin-top: 4px;
}
.apus-categories-dropdown .category-dropdown-label {
  cursor: pointer;
}
.apus-categories-dropdown option {
  font-size: 16px;
  color: #7e7e7e;
}
.apus-categories-dropdown option[selected="selected"] {
  color: #252525;
}
.apus-categories-dropdown .dropdown-menu {
  min-width: 200px;
  padding: 20px 30px;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  border: 1px solid #2441e7;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.apus-categories-dropdown .dropdown-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.apus-categories-dropdown .dropdown-menu ul li {
  margin: 0 0 5px;
}
.apus-categories-dropdown .dropdown-menu ul li a {
  color: #7e7e7e;
}
.apus-categories-dropdown .dropdown-menu ul li a:hover, .apus-categories-dropdown .dropdown-menu ul li a:active {
  color: #252525;
}
.apus-categories-dropdown .dropdown-menu ul li.active {
  color: #252525;
}
.apus-categories-dropdown .dropdown-menu ul li:last-child {
  margin: 0;
}

.before-shop-header-wrapper {
  position: relative;
}
@media (min-width: 768px) {
  .before-shop-header-wrapper .before-shop-loop-fillter {
    position: absolute;
    top: 20px;
  }
}

.pagination-top {
  margin-top: -6px;
}
.pagination-top .apus-pagination.pagination-woo {
  margin: 0;
}
.pagination-top .apus-pagination .apus-pagination-inner {
  padding: 0;
}
.pagination-top.has-fillter .apus-pagination .apus-pagination-inner {
  padding: 0 30px;
}

.apus-filter {
  padding: 0 0 20px;
  margin: 0px 0 20px;
  border-bottom: 1px solid #eeeeee;
}
@media (min-width: 1200px) {
  .apus-filter {
    margin-bottom: 40px;
  }
}
.apus-filter .shop-page-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 24px;
}
.apus-filter .woocommerce-result-count {
  font-size: 18px;
  font-weight: 400;
  float: right;
  margin: 0;
}
.rtl .apus-filter .woocommerce-result-count {
  float: left;
}
.apus-filter #apus-orderby {
  float: left;
}
.rtl .apus-filter #apus-orderby {
  float: right;
}
.apus-filter .woocommerce-ordering {
  margin: 0 0 25px 0;
}
.apus-filter .orderby-wrapper > * {
  display: inline-block;
  vertical-align: middle;
  float: none;
}
.apus-filter select {
  font-size: 16px;
  color: #7e7e7e;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url("../images/select.png") #fff right 10px center no-repeat;
  font-weight: 400;
  padding: 3px 20px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  margin: 0;
  border: 1px solid #eeeeee;
}
.apus-filter .display-mode {
  margin-top: 4px;
}
.apus-filter .change-view {
  color: #cccccc;
  display: inline-block;
}
.apus-filter .change-view i {
  font-size: 24px;
  vertical-align: middle;
}
.apus-filter .change-view + .change-view {
  margin-left: 10px;
}
.rtl .apus-filter .change-view + .change-view {
  margin-right: 10px;
  margin-left: inherit;
}
@media (min-width: 1200px) {
  .apus-filter .change-view + .change-view {
    margin-left: 20px;
  }
  .rtl .apus-filter .change-view + .change-view {
    margin-right: 20px;
    margin-left: inherit;
  }
}
.apus-filter .change-view:hover, .apus-filter .change-view.active {
  color: #2441e7;
}
@media (min-width: 1200px) {
  .apus-filter .form-edumy-ppp .edumy-wc-wppp-select {
    min-width: 190px;
  }
}
.apus-filter .form-edumy-ppp,
.apus-filter .orderby-wrapper {
  margin-right: 10px;
}
.rtl .apus-filter .form-edumy-ppp, .rtl
.apus-filter .orderby-wrapper {
  margin-left: 10px;
  margin-right: inherit;
}
@media (min-width: 1200px) {
  .apus-filter .form-edumy-ppp,
  .apus-filter .orderby-wrapper {
    margin-right: 30px;
  }
  .rtl .apus-filter .form-edumy-ppp, .rtl
  .apus-filter .orderby-wrapper {
    margin-left: 30px;
    margin-right: inherit;
  }
}

.form-edumy-ppp {
  float: left;
}
.rtl .form-edumy-ppp {
  float: right;
}
.form-edumy-ppp select {
  font-size: 16px;
  color: #7e7e7e;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: url("../images/select.png") #fff right 10px center no-repeat;
  font-weight: 400;
  border: 1px solid #eeeeee;
  padding: 3px 20px;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  margin: 0;
  border: 1px solid #eeeeee;
}

.apus-after-loop-shop {
  margin-bottom: 10px;
}
.apus-after-loop-shop .form-edumy-ppp select {
  min-width: 120px;
}
@media (min-width: 768px) {
  .apus-after-loop-shop {
    margin-bottom: 45px;
  }
  .apus-after-loop-shop .woocommerce-result-count {
    float: right;
    margin: 4px 0 0;
  }
  .rtl .apus-after-loop-shop .woocommerce-result-count {
    float: left;
  }
  .apus-after-loop-shop .apus-pagination {
    float: left;
    margin: 0;
    padding: 0;
  }
  .rtl .apus-after-loop-shop .apus-pagination {
    float: right;
  }
}

#apus-orderby .orderby-label {
  color: #7e7e7e;
  display: inline-block;
  font-size: 14px;
  font-weight: 300;
  cursor: pointer;
  border: 1px solid #eeeeee;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  padding: 4px 15px;
}
#apus-orderby .dropdown-menu {
  min-width: 200px;
  padding: 20px 30px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  border: 1px solid #2441e7;
  -webkit-box-shadow: none;
  box-shadow: none;
}
#apus-orderby .dropdown-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
#apus-orderby .dropdown-menu ul li {
  margin: 0 0 5px;
}
#apus-orderby .dropdown-menu ul li a {
  color: #7e7e7e;
}
#apus-orderby .dropdown-menu ul li a:hover, #apus-orderby .dropdown-menu ul li a:active {
  color: #252525;
}
#apus-orderby .dropdown-menu ul li.active {
  color: #252525;
}
#apus-orderby .dropdown-menu ul li:last-child {
  margin: 0;
}

/*------------------------------------*\
    Mini Cart
\*------------------------------------*/
.apus-topcart .dropdown-menu {
  top: 50px;
  margin: 0;
  padding: 30px;
  min-width: 414px;
  border: none;
  background: #fff;
  visibility: hidden;
  pointer-events: none;
  display: block !important;
  right: -20px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  -webkit-transform: translateY(30px);
  -ms-transform: translateY(30px);
  -o-transform: translateY(30px);
  transform: translateY(30px);
  -webkit-box-shadow: 0 0 18px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 18px 0 rgba(0, 0, 0, 0.1);
}
.rtl .apus-topcart .dropdown-menu {
  left: -20px;
  right: auto;
}
.apus-topcart .dropdown-menu:before {
  top: -8px;
  content: "";
  position: absolute;
  background: #fff;
  width: 0;
  height: 0;
  right: 25px;
  width: 16px;
  height: 16px;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-box-shadow: -2px -2px 10px -5px rgba(0, 0, 0, 0.2);
  box-shadow: -2px -2px 10px -5px rgba(0, 0, 0, 0.2);
}
.rtl .apus-topcart .dropdown-menu:before {
  left: 25px;
  right: auto;
}
.apus-topcart .buttons {
  margin: 0;
}
.apus-topcart .buttons .btn, .apus-topcart .buttons .viewmore-products-btn, .apus-topcart .buttons .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .apus-topcart .buttons a, .apus-topcart .buttons .wfg-button, .apus-topcart .buttons #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .apus-topcart .buttons a.checkout-button, .apus-topcart .buttons .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .apus-topcart .buttons a.checkout-button, .apus-topcart .buttons .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .apus-topcart .buttons a.checkout-button {
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
}
.apus-topcart .buttons .wc-forward {
  text-transform: none;
  padding: 12px 34px;
  font-family: "Nunito", Arial, sans-serif;
  font-size: 16px;
  line-height: normal;
  border: 0;
  font-weight: 700;
  margin: 0 20px 0 0;
}
.rtl .apus-topcart .buttons .wc-forward {
  margin: 0 0 0 20px;
}
.apus-topcart .buttons .wc-forward:hover {
  color: #fff;
  border-color: #2441e7;
  background: #2441e7;
}
.apus-topcart .buttons .wc-forward:focus {
  color: #fff;
  border-color: #2441e7;
  background: #2441e7;
}
.apus-topcart .buttons .wc-forward:active {
  color: #fff;
  border-color: #2441e7;
  background: #2441e7;
}
.apus-topcart .buttons .wc-forward:last-child {
  margin: 0;
}
.apus-topcart .open .dropdown-menu {
  display: block;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
  visibility: visible;
  pointer-events: auto;
}
.apus-topcart .overlay-offcanvas-content {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
  cursor: no-drop;
  -webkit-transform: translateX(-30px);
  -ms-transform: translateX(-30px);
  -o-transform: translateX(-30px);
  transform: translateX(-30px);
  visibility: hidden;
  z-index: 2;
}
.apus-topcart .overlay-offcanvas-content.active {
  visibility: visible;
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}
.apus-topcart .offcanvas-content {
  z-index: 3;
  position: fixed;
  right: 0;
  top: 0;
  background: #fff;
  -webkit-transition: all 0.35s ease-in-out 0s;
  -o-transition: all 0.35s ease-in-out 0s;
  transition: all 0.35s ease-in-out 0s;
  opacity: 0;
  filter: alpha(opacity=0);
  width: 400px;
  height: 100vh;
  -webkit-transform: translateX(100%);
  -ms-transform: translateX(100%);
  -o-transform: translateX(100%);
  transform: translateX(100%);
}
.apus-topcart .offcanvas-content.active {
  opacity: 1;
  filter: alpha(opacity=100);
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  -o-transform: translateY(0);
  transform: translateY(0);
}
.apus-topcart .offcanvas-content .shopping_cart_content .cart_list {
  max-height: calc(100% - 180px);
}
.apus-topcart .offcanvas-content .title-cart-canvas {
  font-size: 16px;
  text-align: center;
  margin: 0 0 10px;
  padding: 10px;
  border-bottom: 1px solid #eeeeee;
  text-transform: uppercase;
  position: relative;
}
.apus-topcart .offcanvas-content .title-cart-canvas .close-cart {
  position: absolute;
  top: 11px;
  left: 14px;
  z-index: 1;
  background: #fff;
  font-size: 18px;
  cursor: pointer;
  color: #e44343;
}
.rtl .apus-topcart .offcanvas-content .title-cart-canvas .close-cart {
  right: 14px;
  left: auto;
}
.apus-topcart .offcanvas-content .shopping_cart_content {
  padding: 10px;
  height: calc(100vh - 50px);
  display: -webkit-flex;
  /* Safari */
  display: flex;
  flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
}
@media (min-width: 1200px) {
  .apus-topcart .offcanvas-content .shopping_cart_content {
    padding: 15px 15px 30px;
  }
}
.apus-topcart .offcanvas-content .shopping_cart_content .cart-bottom {
  align-self: flex-end;
  -webkit-align-self: flex-end;
  width: 100%;
}
.apus-topcart .offcanvas-content .shopping_cart_content .cart_list {
  width: 100%;
}

.shopping_cart_content {
  font-size: 14px;
}
.shopping_cart_content .variation {
  margin: 0 0 3px;
  overflow: hidden;
}
.shopping_cart_content .variation dt {
  margin-right: 5px;
}
.rtl .shopping_cart_content .variation dt {
  margin-left: 5px;
  margin-right: inherit;
}
.shopping_cart_content .variation dt, .shopping_cart_content .variation dd {
  float: left;
}
.rtl .shopping_cart_content .variation dt, .rtl .shopping_cart_content .variation dd {
  float: right;
}
.shopping_cart_content .variation dt p, .shopping_cart_content .variation dd p {
  margin: 0;
}
.shopping_cart_content .cart_list {
  padding: 0 0 10px;
  max-height: 270px;
  overflow: hidden;
}
.shopping_cart_content .cart_list > div {
  margin: 0;
  overflow: hidden;
  padding: 10px 0;
  border-bottom: 1px solid #dedede;
}
.shopping_cart_content .cart_list > div.empty {
  border: none;
  margin: 0;
  color: #252525;
}
.shopping_cart_content .cart_list > div:first-child {
  padding-top: 0px;
}
.shopping_cart_content .cart_list > div:last-child {
  border: none;
  padding-bottom: 0px;
}
.shopping_cart_content .cart_list .image {
  width: 60px;
  height: 60px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
  display: block;
}
.shopping_cart_content .cart_list .image img {
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  -o-border-radius: 100%;
}
.shopping_cart_content .cart_list .quantity {
  font-family: "Open Sans";
  font-size: 16px;
  color: #7e7e7e;
  padding: 0;
  display: inline;
}
.shopping_cart_content .cart_list .name {
  margin: 0;
  font-size: 16px;
  font-family: "Nunito", Arial, sans-serif;
}
.shopping_cart_content .cart_list .cart-item {
  margin: 0;
  font-size: 16px;
}
.shopping_cart_content .cart_list .media-body {
  width: 1000px;
  padding-right: 20px;
}
.rtl .shopping_cart_content .cart_list .media-body {
  padding-left: 20px;
  padding-right: inherit;
}
.shopping_cart_content .cart_list .cart-main-content {
  text-align: left;
  position: relative;
}
.rtl .shopping_cart_content .cart_list .cart-main-content {
  text-align: right;
}
.shopping_cart_content .cart_list .cart-main-content .remove {
  top: 22px;
  z-index: 9;
  display: block;
  position: absolute;
  background-image: url("../images/close-dark.png");
  background-repeat: no-repeat;
  background-position: 0 0;
  background-color: transparent;
  width: 15px;
  height: 16px;
  right: 0;
}
.rtl .shopping_cart_content .cart_list .cart-main-content .remove {
  left: 0;
  right: auto;
}
.shopping_cart_content .cart_list .cart-main-content .remove:hover, .shopping_cart_content .cart_list .cart-main-content .remove:focus {
  background-color: transparent;
}
.shopping_cart_content .cart_list .cart-item {
  overflow: hidden;
}
.shopping_cart_content .cart_list .remove i {
  display: none;
}
.shopping_cart_content .total {
  overflow: hidden;
  position: relative;
  margin: 0;
  font-weight: 400;
  text-transform: none;
  padding: 20px 0;
  font-size: 16px;
  color: #0a0a0a !important;
  font-weight: 700;
  font-family: "Open Sans";
}
.shopping_cart_content .total.empty {
  border: none;
  margin: 0;
  padding-top: 0;
  color: #0a0a0a;
  font-weight: 700;
  text-align: center;
  font-family: "Nunito", Arial, sans-serif;
  font-size: 22px;
}
.shopping_cart_content .total.empty + .buttons {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}
.shopping_cart_content .buttons {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}
.shopping_cart_content .buttons .checkout {
  margin-left: auto;
}
.rtl .shopping_cart_content .buttons .checkout {
  margin-right: auto;
  margin-left: inherit;
}

.woocommerce a.remove {
  padding: 0;
  margin: auto;
  color: #e44343;
  font-size: 32px;
  background: transparent;
}

/** Plugins  add to wishlist, compare **/
.place-order {
  padding: 30px;
}

.input-text {
  border: 1px solid #e5e5e5;
  padding: 5px 10px;
}

.woocommerce address {
  margin-bottom: 20px;
}

.product-categories {
  list-style: none;
  margin: 0;
  font-size: 14px;
  padding: 0;
  overflow: hidden;
}
.product-categories + .view-more-list-cat {
  position: absolute;
  background: #fff;
  bottom: 1px;
  left: 1px;
  width: calc(100% - 2px);
  z-index: 99;
  display: block;
  color: #5cb85c;
  padding: 5px 54px 15px;
}
.product-categories + .view-more-list-cat.view-less {
  color: #e44343;
}
.product-categories + .view-more-list-cat:hover, .product-categories + .view-more-list-cat:active {
  text-decoration: underline;
}
.product-categories.hideContent {
  height: 435px;
}
.product-categories.showContent {
  height: auto;
}
.product-categories .children {
  list-style: none;
  padding: 0;
}
.product-categories li {
  line-height: 32px;
}
.product-categories li li {
  padding-left: 20px;
}
.rtl .product-categories li li {
  padding-right: 20px;
  padding-left: inherit;
}
.product-categories li.current-cat-parent > .count, .product-categories li.current-cat > .count, .product-categories li:hover > .count {
  color: #2441e7;
}
.product-categories li.current-cat-parent > a, .product-categories li.current-cat > a, .product-categories li:hover > a {
  color: #2441e7;
}
.product-categories li .count {
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  font-size: 14px;
  font-weight: 400;
  display: inline-block;
  float: right;
  margin-top: 3px;
}
.rtl .product-categories li .count {
  float: left;
}
.product-categories li a {
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  color: #7e7e7e;
}
.product-categories li a:hover, .product-categories li a:active {
  color: #2441e7;
}
.product-categories li.cat-parent {
  position: relative;
}
.product-categories li.cat-parent > i {
  padding-right: 12px;
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 13px;
}
.rtl .product-categories li.cat-parent > i {
  padding-left: 12px;
  padding-right: inherit;
}
.rtl .product-categories li.cat-parent > i {
  right: 0;
  left: auto;
}

.top-archive-shop {
  padding-bottom: 30px;
}

.apus-results {
  margin-top: 10px;
}
.apus-results .apus-results-reset {
  display: inline-block;
  padding: 6px 15px;
  background: #e44343;
  color: #fff;
  white-space: nowrap;
  font-weight: 400;
  font-size: 15px;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.apus-results .apus-results-reset:hover, .apus-results .apus-results-reset:active {
  color: #fff;
  background: #d51f1f;
}

.ajax-pagination {
  text-align: center;
  margin: 10px 0;
}
.ajax-pagination.apus-loader .apus-loadmore-btn {
  display: none;
}
.ajax-pagination.apus-loader:after {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" width="38" height="38" viewBox="0 0 38 38" stroke="rgba(102,102,102,0.25)"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg transform="translate(1 1)" stroke-width="2"%3E%3Ccircle stroke-opacity=".55" cx="18" cy="18" r="18"/%3E%3Cpath d="M36 18c0-9.94-8.06-18-18-18"%3E%3CanimateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  background-position: center center;
  background-repeat: no-repeat;
  content: "";
  width: 40px;
  height: 40px;
  display: block;
  width: 100%;
}
.ajax-pagination .apus-loadmore-btn + .apus-allproducts {
  display: none;
}
.ajax-pagination .apus-loadmore-btn.hidden + .apus-allproducts {
  display: block;
  color: #e44343;
}

.add-cart > .added {
  display: none !important;
}
.add-cart .added_to_cart:after {
  display: none;
}

.apus-shop-products-wrapper.loading {
  position: relative;
}
.apus-shop-products-wrapper.loading:before {
  background: url("../images/loading-quick.gif") center 100px/50px no-repeat rgba(255, 255, 255, 0.9);
  position: absolute;
  width: 100%;
  height: 100%;
  content: "";
  left: 0;
  top: 0;
  z-index: 99;
}

.woocommerce-account .woocommerce-MyAccount-content,
.woocommerce-account .woocommerce-MyAccount-navigation {
  width: 100%;
  float: none;
}

.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link {
  display: inline-block;
  vertical-align: middle;
  margin-right: 30px;
}
.lt-ie8 .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link {
  display: inline;
  zoom: 1;
}
.rtl .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link {
  margin-left: 30px;
  margin-right: inherit;
}
.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a {
  padding: 0;
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.lt-ie8 .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a {
  display: inline;
  zoom: 1;
}
.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a:before {
  background: #2441e7;
  position: absolute;
  bottom: -2px;
  content: '';
  left: 0;
  width: 100%;
  height: 2px;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.rtl .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link a:before {
  right: 0;
  left: auto;
}
.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link.is-active > a, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:hover > a, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:active > a {
  color: #2441e7;
}
.woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link.is-active > a:before, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:hover > a:before, .woocommerce-account .woocommerce-MyAccount-navigation .woocommerce-MyAccount-navigation-link:active > a:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

.woocommerce-account-wrapper {
  border: 2px solid #edeff7;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  max-width: 945px;
  margin-left: auto;
  margin-right: auto;
  padding: 30px;
}

.woocommerce-MyAccount-content {
  padding: 40px 0;
}
.woocommerce-MyAccount-content h2 {
  margin: 20px 0 10px;
  text-transform: uppercase;
  font-size: 18px;
  font-family: "Open Sans";
}

.edit-account br {
  display: none;
}
.edit-account input[type="text"],
.edit-account input[type="password"] {
  height: 40px;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -ms-border-radius: 3px;
  -o-border-radius: 3px;
}
.edit-account input[type="text"]:focus,
.edit-account input[type="password"]:focus {
  border-color: #eeeeee;
}
.edit-account legend {
  font-size: 72px;
  font-weight: 300;
  border: none;
  margin: 30px 0 0;
}
.edit-account label {
  font-weight: normal;
  font-size: 16px;
  color: #252525;
}

.woocommerce-MyAccount-content .woocommerce-Message,
.woocommerce-MyAccount-navigation .woocommerce-Message {
  margin-bottom: 0;
}

.woocommerce-MyAccount-content p {
  margin-bottom: 20px;
}
.woocommerce-MyAccount-content .form-group span {
  display: inline-block;
  margin-top: 10px;
}

form.login,
form.register {
  margin: 0 !important;
  border: 0 !important;
  padding: 0 !important;
}
form.login .btn, form.login .viewmore-products-btn, form.login .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart form.login a, form.login .wfg-button, form.login #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout form.login a.checkout-button, form.login .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout form.login a.checkout-button, form.login .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout form.login a.checkout-button,
form.register .btn,
form.register .viewmore-products-btn,
form.register .woocommerce .wishlist_table td.product-add-to-cart a,
.woocommerce .wishlist_table td.product-add-to-cart form.register a,
form.register .wfg-button,
form.register #add_payment_method .wc-proceed-to-checkout a.checkout-button,
#add_payment_method .wc-proceed-to-checkout form.register a.checkout-button,
form.register .woocommerce-cart .wc-proceed-to-checkout a.checkout-button,
.woocommerce-cart .wc-proceed-to-checkout form.register a.checkout-button,
form.register .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button,
.woocommerce-checkout .wc-proceed-to-checkout form.register a.checkout-button {
  height: 55px;
  font-size: 19px;
  font-weight: 600;
  background-color: #0b1967;
  font-family: "Nunito", Arial, sans-serif;
  text-transform: capitalize;
  border-color: #0b1967;
  display: inline-block;
  vertical-align: middle;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
}
.lt-ie8 form.login .btn, .lt-ie8 form.login .viewmore-products-btn, .lt-ie8 form.login .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .lt-ie8 form.login a, .lt-ie8 form.login .wfg-button, .lt-ie8 form.login #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .lt-ie8 form.login a.checkout-button, .lt-ie8 form.login .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .lt-ie8 form.login a.checkout-button, .lt-ie8 form.login .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .lt-ie8 form.login a.checkout-button, .lt-ie8
form.register .btn, .lt-ie8
form.register .viewmore-products-btn, .lt-ie8
form.register .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .lt-ie8
form.register a, .lt-ie8
form.register .wfg-button, .lt-ie8
form.register #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .lt-ie8
form.register a.checkout-button, .lt-ie8
form.register .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .lt-ie8
form.register a.checkout-button, .lt-ie8
form.register .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .lt-ie8
form.register a.checkout-button {
  display: inline;
  zoom: 1;
}
form.login .btn:hover, form.login .viewmore-products-btn:hover, form.login .woocommerce .wishlist_table td.product-add-to-cart a:hover, .woocommerce .wishlist_table td.product-add-to-cart form.login a:hover, form.login .wfg-button:hover, form.login #add_payment_method .wc-proceed-to-checkout a.checkout-button:hover, #add_payment_method .wc-proceed-to-checkout form.login a.checkout-button:hover, form.login .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:hover, .woocommerce-cart .wc-proceed-to-checkout form.login a.checkout-button:hover, form.login .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:hover, .woocommerce-checkout .wc-proceed-to-checkout form.login a.checkout-button:hover,
form.register .btn:hover,
form.register .viewmore-products-btn:hover,
form.register .woocommerce .wishlist_table td.product-add-to-cart a:hover,
.woocommerce .wishlist_table td.product-add-to-cart form.register a:hover,
form.register .wfg-button:hover,
form.register #add_payment_method .wc-proceed-to-checkout a.checkout-button:hover,
#add_payment_method .wc-proceed-to-checkout form.register a.checkout-button:hover,
form.register .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:hover,
.woocommerce-cart .wc-proceed-to-checkout form.register a.checkout-button:hover,
form.register .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:hover,
.woocommerce-checkout .wc-proceed-to-checkout form.register a.checkout-button:hover {
  background-color: #0b1967;
}
form.login .btn:focus, form.login .viewmore-products-btn:focus, form.login .woocommerce .wishlist_table td.product-add-to-cart a:focus, .woocommerce .wishlist_table td.product-add-to-cart form.login a:focus, form.login .wfg-button:focus, form.login #add_payment_method .wc-proceed-to-checkout a.checkout-button:focus, #add_payment_method .wc-proceed-to-checkout form.login a.checkout-button:focus, form.login .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-cart .wc-proceed-to-checkout form.login a.checkout-button:focus, form.login .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:focus, .woocommerce-checkout .wc-proceed-to-checkout form.login a.checkout-button:focus,
form.register .btn:focus,
form.register .viewmore-products-btn:focus,
form.register .woocommerce .wishlist_table td.product-add-to-cart a:focus,
.woocommerce .wishlist_table td.product-add-to-cart form.register a:focus,
form.register .wfg-button:focus,
form.register #add_payment_method .wc-proceed-to-checkout a.checkout-button:focus,
#add_payment_method .wc-proceed-to-checkout form.register a.checkout-button:focus,
form.register .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:focus,
.woocommerce-cart .wc-proceed-to-checkout form.register a.checkout-button:focus,
form.register .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:focus,
.woocommerce-checkout .wc-proceed-to-checkout form.register a.checkout-button:focus {
  background-color: #0b1967;
}
form.login .btn:active, form.login .viewmore-products-btn:active, form.login .woocommerce .wishlist_table td.product-add-to-cart a:active, .woocommerce .wishlist_table td.product-add-to-cart form.login a:active, form.login .wfg-button:active, form.login #add_payment_method .wc-proceed-to-checkout a.checkout-button:active, #add_payment_method .wc-proceed-to-checkout form.login a.checkout-button:active, form.login .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:active, .woocommerce-cart .wc-proceed-to-checkout form.login a.checkout-button:active, form.login .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:active, .woocommerce-checkout .wc-proceed-to-checkout form.login a.checkout-button:active,
form.register .btn:active,
form.register .viewmore-products-btn:active,
form.register .woocommerce .wishlist_table td.product-add-to-cart a:active,
.woocommerce .wishlist_table td.product-add-to-cart form.register a:active,
form.register .wfg-button:active,
form.register #add_payment_method .wc-proceed-to-checkout a.checkout-button:active,
#add_payment_method .wc-proceed-to-checkout form.register a.checkout-button:active,
form.register .woocommerce-cart .wc-proceed-to-checkout a.checkout-button:active,
.woocommerce-cart .wc-proceed-to-checkout form.register a.checkout-button:active,
form.register .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button:active,
.woocommerce-checkout .wc-proceed-to-checkout form.register a.checkout-button:active {
  background-color: #0b1967;
}
form.login .lost_password a,
form.register .lost_password a {
  outline: none;
  color: #c75533;
  text-decoration: underline;
}
form.login br,
form.register br {
  display: none;
}
form.login label,
form.register label {
  font-weight: 400;
}
form.login .form-group,
form.register .form-group {
  margin: 0 0 20px;
}
form.login .form-group:last-child,
form.register .form-group:last-child {
  margin-bottom: 0;
}
form.login .form-group .topmenu-menu,
form.register .form-group .topmenu-menu {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}
form.login .form-group .topmenu-menu > li a,
form.register .form-group .topmenu-menu > li a {
  background-color: transparent;
  color: #7e7e7e;
  font-size: 16px;
  font-family: "Nunito", Arial, sans-serif;
  display: inline-block;
  vertical-align: middle;
}
.lt-ie8 form.login .form-group .topmenu-menu > li a, .lt-ie8
form.register .form-group .topmenu-menu > li a {
  display: inline;
  zoom: 1;
}
form.login .form-group .topmenu-menu > li a:hover,
form.register .form-group .topmenu-menu > li a:hover {
  color: #2441e7;
}
form.login .form-group .topmenu-menu > li a:focus,
form.register .form-group .topmenu-menu > li a:focus {
  color: #2441e7;
}
form.login .form-group .topmenu-menu > li a:active,
form.register .form-group .topmenu-menu > li a:active {
  color: #2441e7;
}
form.login .form-group .topmenu-menu > li a [class*="icon"]:before, form.login .form-group .topmenu-menu > li a [class*="icon"]:after,
form.register .form-group .topmenu-menu > li a [class*="icon"]:before,
form.register .form-group .topmenu-menu > li a [class*="icon"]:after {
  font-size: 24px;
  margin-right: 0px;
  margin-left: 0;
}
.rtl form.login .form-group .topmenu-menu > li a [class*="icon"]:before, .rtl form.login .form-group .topmenu-menu > li a [class*="icon"]:after, .rtl
form.register .form-group .topmenu-menu > li a [class*="icon"]:before, .rtl
form.register .form-group .topmenu-menu > li a [class*="icon"]:after {
  margin-left: 0px;
  margin-right: inherit;
}
.rtl form.login .form-group .topmenu-menu > li a [class*="icon"]:before, .rtl form.login .form-group .topmenu-menu > li a [class*="icon"]:after, .rtl
form.register .form-group .topmenu-menu > li a [class*="icon"]:before, .rtl
form.register .form-group .topmenu-menu > li a [class*="icon"]:after {
  margin-right: 0;
  margin-left: inherit;
}
form.login .form-group .topmenu-menu > li.register,
form.register .form-group .topmenu-menu > li.register {
  margin-left: auto;
}
.rtl form.login .form-group .topmenu-menu > li.register, .rtl
form.register .form-group .topmenu-menu > li.register {
  margin-right: auto;
  margin-left: inherit;
}
form.login .form-group .topmenu-menu > li.register a [class*="icon"]:before, form.login .form-group .topmenu-menu > li.register a [class*="icon"]:after,
form.register .form-group .topmenu-menu > li.register a [class*="icon"]:before,
form.register .form-group .topmenu-menu > li.register a [class*="icon"]:after {
  font-size: 26px;
}
form.login .input-text,
form.register .input-text {
  background: #fff !important;
  border: 1px solid #eeeeee !important;
}
form.login input[type="checkbox"],
form.register input[type="checkbox"] {
  margin-right: 7px;
}
.rtl form.login input[type="checkbox"], .rtl
form.register input[type="checkbox"] {
  margin-left: 7px;
  margin-right: inherit;
}
form.login .input-submit ~ span,
form.register .input-submit ~ span {
  margin: 10px 0 0;
}
form.login .input-submit ~ span.pull-left,
form.register .input-submit ~ span.pull-left {
  margin-left: 15px;
}
.rtl form.login .input-submit ~ span.pull-left, .rtl
form.register .input-submit ~ span.pull-left {
  margin-right: 15px;
  margin-left: inherit;
}
form.login .input-submit ~ span.lost_password a,
form.register .input-submit ~ span.lost_password a {
  color: #2441e7;
}
form.login .user-role,
form.register .user-role {
  padding-left: 20px;
}
form.login .user-role [type="radio"],
form.register .user-role [type="radio"] {
  margin-top: 11px;
}

.login-wrapper .mfp-content {
  width: 550px !important;
  max-width: 80%;
  background-color: #fff;
  border: 0;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
}
.login-wrapper .mfp-content .header_customer_login {
  padding: 40px;
}
.login-wrapper .mfp-content .header_customer_login .btn, .login-wrapper .mfp-content .header_customer_login .viewmore-products-btn, .login-wrapper .mfp-content .header_customer_login .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart .login-wrapper .mfp-content .header_customer_login a, .login-wrapper .mfp-content .header_customer_login .wfg-button, .login-wrapper .mfp-content .header_customer_login #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout .login-wrapper .mfp-content .header_customer_login a.checkout-button, .login-wrapper .mfp-content .header_customer_login .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout .login-wrapper .mfp-content .header_customer_login a.checkout-button, .login-wrapper .mfp-content .header_customer_login .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout .login-wrapper .mfp-content .header_customer_login a.checkout-button {
  outline: none;
  border: 0;
  font-weight: 700;
  font-size: 18px;
  line-height: normal;
  padding: 16px 30px;
  text-transform: capitalize;
  font-family: "Nunito", Arial, sans-serif;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  background-color: #0b1967;
}
.login-wrapper .title {
  text-align: center;
}
.login-wrapper .apus-mfp-close {
  font-size: 20px;
  background-color: #2441e7;
  color: #fff;
  line-height: normal;
  border: none;
  margin: -25px;
  width: 50px;
  height: 50px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  -webkit-transition: all 0.2s ease-in-out;
  -moz-transition: all 0.2s ease-in-out;
  -ms-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  opacity: 1;
  filter: alpha(opacity=100);
}
.login-wrapper .apus-mfp-close:hover {
  outline: none;
}
.login-wrapper .apus-mfp-close:focus {
  outline: none;
}
.login-wrapper .apus-mfp-close:active {
  outline: none;
}
.login-wrapper .apus-mfp-close .icon-theme {
  display: block;
  background-image: url("../images/close-icon.png");
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: cover;
  width: 18px;
  height: 19px;
}

.cart_item {
  margin: 0 0 20px;
  padding: 0 0 20px;
  border-bottom: 1px solid #eeeeee;
}
.cart_item > .media-left {
  width: 70%;
}
.cart_item img {
  width: 90px;
  max-width: none;
}
.cart_item .content-left {
  overflow: hidden;
  padding-left: 20px;
}
.rtl .cart_item .content-left {
  padding-right: 20px;
  padding-left: inherit;
}
.cart_item .product-name {
  font-size: 18px;
  font-weight: 400;
  margin: 0 0 15px;
}
.cart_item .price {
  font-size: 20px;
  font-family: "Open Sans";
  color: #4c4c4c;
  font-weight: 400;
}
.cart_item a.remove {
  margin: 0 0 15px;
  display: inline-block;
  font-size: 32px;
  color: #7e7e7e !important;
}
.cart_item a.remove:hover, .cart_item a.remove:active {
  color: #e44343 !important;
}

div.cart .input-text {
  height: 53px;
  border: 2px solid #eeeeee;
}
div.cart .input-text:focus, div.cart .input-text:active {
  border-color: #252525;
}
div.cart label {
  font-size: 18px;
  color: #000;
}

.woocommerce .order-review #order_review_heading {
  font-size: 20px;
  font-weight: 600;
  text-transform: none;
  margin: 0;
  padding: 0;
  color: #0a0a0a;
}
.woocommerce .order-review table.shop_table {
  margin: 0;
}

#order_review .shop_table {
  border: none;
  margin-bottom: 25px;
}
#order_review .shop_table td {
  padding: 25px 0;
  border-width: 0 0 1px;
  border-style: solid;
  border-color: #eeeeee;
}
#order_review .cart_item {
  margin: 0;
  padding: 0;
  border: none;
}
#order_review .product-name {
  margin: 0;
  font-size: 16px;
  line-height: 28px;
}
#order_review .product-name strong {
  font-weight: 400;
}
#order_review .product-total {
  font-weight: 600;
  color: #2441e7;
}
#order_review > .media-left {
  width: auto;
}
#order_review .woocommerce-Price-amount {
  color: #252525;
  font-weight: 600;
}
#order_review .subtotal tr > * {
  border-bottom: 1px solid #eeeeee !important;
}
#order_review .subtotal th {
  border: none;
  font-weight: 600;
  color: #252525;
  text-transform: none;
  font-size: 18px;
  font-family: "Nunito", Arial, sans-serif;
}
#order_review .subtotal td {
  padding: 10px 0;
  font-weight: 400;
  text-align: right;
}
.rtl #order_review .subtotal td {
  text-align: left;
}
#order_review .subtotal td label {
  font-weight: 400;
}
#order_review .subtotal .order-total strong {
  font-size: 20px;
}
#order_review .subtotal .amount {
  font-weight: 600;
}
#order_review .order-total .amount,
#order_review .cart-subtotal .amount {
  font-family: "Nunito", Arial, sans-serif;
  color: #c75533;
}

.order-total .amount {
  font-size: 22px;
}

.apus-checkout-step {
  padding: 0 0 30px;
}
.apus-checkout-step ul {
  padding: 0;
  list-style: none;
  margin: 0 auto;
  text-transform: uppercase;
  width: 100%;
}
.apus-checkout-step ul li {
  position: relative;
  text-align: center;
  float: left;
}
.rtl .apus-checkout-step ul li {
  float: right;
}
@media (min-width: 768px) {
  .apus-checkout-step ul li {
    width: 33.33%;
  }
}
.apus-checkout-step li {
  font-size: 20px;
  font-weight: 500;
  color: #232530;
  line-height: 60px;
  overflow: hidden;
  position: relative;
  background: #fff;
}
.apus-checkout-step li:first-child:before {
  display: none;
}
.apus-checkout-step li:first-child:after {
  border-width: 1px 0 1px 1px;
}
.apus-checkout-step li:before {
  content: '';
  z-index: 1;
  position: absolute;
  top: 0px;
  left: -43px;
  border: 1px solid #eeeeee;
  width: 60px;
  height: 60px;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  background: #fff;
}
.apus-checkout-step li:after {
  position: absolute;
  content: '';
  border-width: 1px 0;
  border-style: solid;
  border-color: #eeeeee;
  width: calc(100% - 30px);
  height: 100%;
  z-index: 5;
  left: 0;
  top: 0;
}
.rtl .apus-checkout-step li:after {
  right: 0;
  left: auto;
}
.apus-checkout-step li .inner {
  position: relative;
}
.apus-checkout-step li .inner:after {
  content: '';
  z-index: 1;
  position: absolute;
  top: 0px;
  right: -30px;
  border-style: solid;
  border-color: #fff #fff #fff transparent;
  border-width: 30px;
  width: 60px;
  height: 60px;
  background: #fff;
}
.apus-checkout-step li .inner:before {
  content: '';
  z-index: 1;
  position: absolute;
  top: 0px;
  right: 12px;
  border: 1px solid #eeeeee;
  border-width: 1px 1px 0 0;
  width: 60px;
  height: 60px;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  background: #fff;
  z-index: 2;
}
.apus-checkout-step li.active {
  background: #2441e7;
  color: #fff;
}
.apus-checkout-step li.active:after {
  border-color: #2441e7;
}
.apus-checkout-step li.active .inner:after {
  border-color: #fff #2441e7;
}
.apus-checkout-step li.active .inner:before {
  display: none;
}
.apus-checkout-step li.active .step {
  opacity: 0.1;
  filter: alpha(opacity=10);
  color: #fff;
}
.apus-checkout-step .inner-step {
  position: relative;
  z-index: 7;
}
.apus-checkout-step .step {
  z-index: 6;
  position: absolute;
  top: -1px;
  right: 70px;
  line-height: 60px;
  font-size: 48px;
  text-transform: uppercase;
  font-weight: 600;
  display: inline-block;
  text-align: center;
  color: #eae9ec;
}
.rtl .apus-checkout-step .step {
  left: 70px;
  right: auto;
}

.woocommerce-thankyou-order-received {
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 30px;
  text-align: center;
  color: #0a0a0a;
}
@media (min-width: 768px) {
  .woocommerce-thankyou-order-received {
    font-size: 28px;
  }
}

.woocommerce-thankyou-order-details {
  text-align: center;
}

.woocommerce ul.order_details li {
  float: none;
  display: inline-block;
  font-size: 12px;
}
.woocommerce ul.order_details li strong {
  margin-top: 5px;
  font-weight: 400;
  color: #252525;
}
.woocommerce ul.order_details li.method strong {
  color: #e44343;
}

.woo-pay-perfect {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
}

.product-top-title {
  position: relative;
}
.product-top-title .view-more {
  position: absolute;
  top: 5px;
  right: 0;
}
.rtl .product-top-title .view-more {
  left: 0;
  right: auto;
}

.layout-detail-product #tabs-list-specifications td {
  padding: 15px;
  border-color: #eff0f2;
}
.layout-detail-product #tabs-list-specifications td:first-child {
  font-weight: 500;
  text-transform: uppercase;
}

.accessoriesproducts .list-accesories {
  margin-bottom: 10px;
}
.accessoriesproducts .check-item {
  margin-top: 10px;
}

.wcv-pro-vendorlist {
  margin: 0 0 30px;
  padding: 0 0 30px;
  border-bottom: 1px solid #eeeeee;
  border-top: none;
  background: #fff;
  height: auto;
}
.wcv-pro-vendorlist .name-store {
  font-size: 18px;
  margin: 10px 0;
}
.wcv-pro-vendorlist:hover .avatar:before {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.wcv-pro-vendorlist .avatar {
  display: inline-block;
  position: relative;
  line-height: 0;
  max-width: 100%;
}
.wcv-pro-vendorlist .avatar:before {
  -webkit-transition: all 0.2s ease 0s;
  -o-transition: all 0.2s ease 0s;
  transition: all 0.2s ease 0s;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #2441e7;
  opacity: 0;
  filter: alpha(opacity=0);
}
.wcv-pro-vendorlist .metas {
  margin: 0 0 5px;
}
.wcv-pro-vendorlist .metas > * {
  display: inline-block;
  font-size: 14px;
}
.wcv-pro-vendorlist .metas > * + * {
  margin-left: 20px;
}
.rtl .wcv-pro-vendorlist .metas > * + * {
  margin-right: 20px;
  margin-left: inherit;
}
.wcv-pro-vendorlist .metas .total-value {
  font-weight: normal;
}
.wcv-pro-vendorlist .store-address,
.wcv-pro-vendorlist .store-phone {
  font-size: 14px;
  margin: 0 0 7px;
}
.wcv-pro-vendorlist .store-address:last-child,
.wcv-pro-vendorlist .store-phone:last-child {
  margin: 0;
}

.pv_shop_description {
  padding: 0 15px 30px;
}

.wcv-header-container {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 30px;
  background: url("../images/bg-vendor.jpg") repeat rgba(255, 255, 255, 0.9);
}
.wcv-header-container .store-banner {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}
.wcv-header-container .wcv-store-grid {
  padding: 0 0 30px 0;
}
.wcv-header-container #inner-element {
  background: transparent none repeat scroll 0 0;
  clear: both;
  overflow: hidden;
  position: static;
  max-width: none;
  width: 100%;
  padding: 0;
}
.wcv-header-container #inner-element .store-info {
  text-align: inherit;
}

.store-info .title-store {
  display: inline-block;
}
.store-info .wcv-verified-vendor {
  display: inline-block;
  vertical-align: top;
  margin: 0 15px;
  font-size: 12px;
  color: #fff;
  background: #4a90de;
  padding: 5px 18px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}
.store-info .social-icons {
  list-style: none;
  margin: 25px 0 0 !important;
  padding: 0;
}
.store-info .social-icons li {
  display: inline-block;
  margin-right: 10px;
}
.rtl .store-info .social-icons li {
  margin-left: 10px;
  margin-right: inherit;
}
.store-info .social-icons li a {
  display: inline-block;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border: 1px solid #405e9c;
  width: 40px;
  height: 40px;
  font-size: 16px;
  line-height: 38px;
  text-align: center;
}
.store-info .social-icons li a.facebook {
  border: 1px solid #405e9c;
  color: #405e9c !important;
}
.store-info .social-icons li a.facebook:hover, .store-info .social-icons li a.facebook:active {
  background: #405e9c;
}
.store-info .social-icons li a.twitter {
  border: 1px solid #55acee;
  color: #55acee !important;
}
.store-info .social-icons li a.twitter:hover, .store-info .social-icons li a.twitter:active {
  background: #55acee;
}
.store-info .social-icons li a.instagram {
  border: 1px solid #5280a5;
  color: #5280a5 !important;
}
.store-info .social-icons li a.instagram:hover, .store-info .social-icons li a.instagram:active {
  background: #5280a5;
}
.store-info .social-icons li a.googleplus {
  color: #cd2129 !important;
  border: 1px solid #cd2129;
}
.store-info .social-icons li a.googleplus:hover, .store-info .social-icons li a.googleplus:active {
  background: #cd2129;
}
.store-info .social-icons li a.linkedin {
  color: #318dc1 !important;
  border: 1px solid #318dc1;
}
.store-info .social-icons li a.linkedin:hover, .store-info .social-icons li a.linkedin:active {
  background: #318dc1;
}
.store-info .social-icons li a.youtube {
  color: #cb312e !important;
  border: 1px solid #cb312e;
}
.store-info .social-icons li a.youtube:hover, .store-info .social-icons li a.youtube:active {
  background: #cb312e;
}
.store-info .social-icons li a:hover, .store-info .social-icons li a:active {
  color: #fff !important;
  background: #405e9c;
}
.store-info .title-store {
  font-size: 24px;
  margin: 0 0 10px 0;
  line-height: 1.1;
}
.store-info .rating-products-wrapper {
  margin: 0 0 20px;
  font-size: 16px;
}
.store-info .rating-products-wrapper > * {
  display: inline-block;
  vertical-align: top;
}
.store-info .rating-products-wrapper > * > * {
  display: block;
}
.store-info .store-address address {
  margin: 0;
}
.store-info .store-address i,
.store-info .store-phone i {
  margin-right: 8px;
}
.rtl .store-info .store-address i, .rtl
.store-info .store-phone i {
  margin-left: 8px;
  margin-right: inherit;
}
.store-info .total-products {
  padding-left: 50px;
}
.rtl .store-info .total-products {
  padding-right: 50px;
  padding-left: inherit;
}
.store-info .total-products .total-value {
  font-size: 24px;
  color: #242424;
  font-weight: normal;
  line-height: 1.1;
}
.store-info .media-body {
  max-width: 600px;
  font-size: 14px;
}
.store-info .media-left {
  padding-right: 30px;
}
.rtl .store-info .media-left {
  padding-left: 30px;
  padding-right: inherit;
}
.store-info .favourite-wrapper {
  clear: both;
  overflow: hidden;
  width: 100%;
  padding: 10px 0 0;
}
.store-info .denso-favourite-vendor {
  border-color: #eeeeee;
  font-size: 12px;
  display: block;
  font-weight: 400;
  padding: 8px 15px;
  text-transform: capitalize;
}
.store-info .denso-favourite-vendor:hover, .store-info .denso-favourite-vendor:active {
  border-color: #2441e7;
}
.store-info .denso-favourite-vendor i {
  margin-right: 3px;
}
.rtl .store-info .denso-favourite-vendor i {
  margin-left: 3px;
  margin-right: inherit;
}
.store-info .denso-favourite-vendor.added {
  color: #fff;
  border-color: #2441e7;
  background: #2441e7;
}

.store-aurhor-inner {
  text-align: center;
  margin-top: 30px;
}
.store-aurhor-inner .avatar {
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border: 2px solid #e8e8e8;
  overflow: hidden;
}
.store-aurhor-inner .store-aurhor .name-author {
  margin: 5px 0 10px;
}

.vendor-reviews-inner {
  background: #f8f8f8;
  border: 2px solid #eeeeee;
  padding: 20px;
}
.vendor-reviews-inner .title-info {
  font-size: 16px !important;
  margin: 10px 0 20px  !important;
}
.vendor-reviews-inner .star-rating {
  float: left;
  margin-right: 40px;
}
.rtl .vendor-reviews-inner .star-rating {
  float: right;
}
.rtl .vendor-reviews-inner .star-rating {
  margin-left: 40px;
  margin-right: inherit;
}
.vendor-reviews-inner .special-progress > * {
  display: inline-block;
  vertical-align: top;
}
.vendor-reviews-inner .special-progress .progress {
  width: 210px;
  margin: 0;
}
.vendor-reviews-inner .special-progress .progress .progress-bar {
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
.vendor-reviews-inner .special-progress .value {
  margin: 0 8px;
  line-height: 1;
}
.vendor-reviews-inner .average-value {
  font-size: 30px;
  font-weight: normal;
  color: #242424;
  display: inline-block;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  text-align: center;
  line-height: 1.2;
  border: 1px solid #eeeeee;
  padding: 20px 5px;
}
.vendor-reviews-inner .average-value span {
  font-size: 12px;
  font-weight: 400;
  display: block;
}

.special-progress {
  margin: 5px 0 0;
}
.special-progress .claimed {
  margin-bottom: 2px;
}
.special-progress .claimed strong {
  color: #252525;
}
.special-progress .progress {
  background: #eaeaea;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  height: 12px;
  margin: 5px 0 17px;
}
.special-progress .progress .progress-bar {
  background: #2441e7;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 10px 10px;
}

.single-rating {
  margin: 0 0 30px;
  padding: 0 0 20px;
  border-bottom: 1px solid #eeeeee;
}
.single-rating:last-child {
  border: none;
  padding: 0;
  margin: 0;
}
.single-rating .avatar {
  max-width: none;
  min-width: 70px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}
.single-rating .media-left {
  padding-right: 20px;
}
.rtl .single-rating .media-left {
  padding-left: 20px;
  padding-right: inherit;
}
.single-rating .stars-value {
  float: right;
}
.rtl .single-rating .stars-value {
  float: left;
}
.single-rating .stars-value .fa-star {
  color: #fednormal;
}
.single-rating h4 {
  font-weight: 400;
  font-size: 10px;
  margin: 0 0 15px;
  color: #7e7e7e;
}
.single-rating h4 .name {
  font-weight: normal;
  font-size: 12px;
  color: #464646;
  text-transform: uppercase;
}
.single-rating h6 {
  margin: 0 0 15px;
}

.btn-showserach-dokan {
  cursor: pointer;
}

.wrapper-dokan .btn-showserach-dokan {
  padding: 6px 9px;
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  border-width: 2px;
}
.wrapper-dokan .dokan-seller-search-form {
  font-size: 14px;
  margin: 0;
  width: 0;
  overflow: hidden;
  -webkit-transition: all 0.2s ease-in-out 0s;
  -o-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
}
.wrapper-dokan .dokan-seller-search-form input {
  width: 100% !important;
  padding-top: 3px !important;
  padding-bottom: 3px !important;
}
.wrapper-dokan .dokan-seller-search-form.active {
  width: 220px;
}
.wrapper-dokan > * {
  display: inline-block;
  vertical-align: top;
  margin-right: 10px;
}
.rtl .wrapper-dokan > * {
  margin-left: 10px;
  margin-right: inherit;
}

#dokan-seller-listing-wrap ul.dokan-seller-wrap li {
  margin-bottom: 30px;
}

.dokan-list-inline > li > a {
  font-family: "Nunito", Arial, sans-serif;
}

.dokan-widget-area #cat-drop-stack > ul,
.dokan-store-menu #cat-drop-stack > ul {
  list-style: none;
  padding: 0;
}
.dokan-widget-area #cat-drop-stack > ul li,
.dokan-store-menu #cat-drop-stack > ul li {
  margin-bottom: 5px;
}
.dokan-widget-area #cat-drop-stack > ul li:last-child,
.dokan-store-menu #cat-drop-stack > ul li:last-child {
  margin-bottom: 0;
}
.dokan-widget-area #cat-drop-stack > ul a:hover, .dokan-widget-area #cat-drop-stack > ul:focus,
.dokan-store-menu #cat-drop-stack > ul a:hover,
.dokan-store-menu #cat-drop-stack > ul:focus {
  color: #2441e7;
}

.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name {
  font-weight: 500;
}

.dokan-single-store .profile-info .dokan-store-info {
  list-style: none;
  font-size: 14px;
}
.dokan-single-store .profile-info .dokan-store-info li {
  float: none !important;
}
.dokan-single-store .profile-info .dokan-store-info li:before {
  display: none;
}

.dokan-store-location,
.dokan-store-contact {
  list-style: none;
}
.dokan-store-location ul,
.dokan-store-contact ul {
  list-style: none;
  padding: 0;
}

.dokan-store-tabss {
  margin-bottom: 20px;
}
.dokan-store-tabss .dokan-right {
  margin: 0;
  margin-top: 10px;
}
@media (min-width: 768px) {
  .dokan-store-tabss {
    margin-bottom: 30px;
  }
}

.dokan-store-sidebar #dokan-store-location {
  height: 200px;
  width: 100%;
}

.wfg-popup {
  border: 0 !important;
}

.wfg-popup h2.wfg-title {
  background: #2441e7;
  color: #fff;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.wfg-gifts .wfg-gift-item h3 {
  background: #fff;
  color: #252525;
  border-top: 1px solid #eeeeee;
}

.wfg-gifts .wfg-gift-item {
  border: 1px solid #eeeeee;
}

#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-footer .seller-avatar img {
  margin: 0;
}

#dokan-seller-listing-wrap ul.dokan-seller-wrap .btn, #dokan-seller-listing-wrap ul.dokan-seller-wrap .viewmore-products-btn, #dokan-seller-listing-wrap ul.dokan-seller-wrap .woocommerce .wishlist_table td.product-add-to-cart a, .woocommerce .wishlist_table td.product-add-to-cart #dokan-seller-listing-wrap ul.dokan-seller-wrap a, #dokan-seller-listing-wrap ul.dokan-seller-wrap .wfg-button, #dokan-seller-listing-wrap ul.dokan-seller-wrap #add_payment_method .wc-proceed-to-checkout a.checkout-button, #add_payment_method .wc-proceed-to-checkout #dokan-seller-listing-wrap ul.dokan-seller-wrap a.checkout-button, #dokan-seller-listing-wrap ul.dokan-seller-wrap .woocommerce-cart .wc-proceed-to-checkout a.checkout-button, .woocommerce-cart .wc-proceed-to-checkout #dokan-seller-listing-wrap ul.dokan-seller-wrap a.checkout-button, #dokan-seller-listing-wrap ul.dokan-seller-wrap .woocommerce-checkout .wc-proceed-to-checkout a.checkout-button, .woocommerce-checkout .wc-proceed-to-checkout #dokan-seller-listing-wrap ul.dokan-seller-wrap a.checkout-button {
  border-radius: 50px;
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -ms-border-radius: 50px;
  -o-border-radius: 50px;
  border-width: 2px;
  padding: 8px 30px;
}

#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-wrapper {
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-wrapper:hover {
  -webkit-box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.2);
  box-shadow: 0px 0px 35px 0px rgba(0, 0, 0, 0.2);
}

.product-block .wcvendors_sold_by_in_loop {
  position: absolute;
  z-index: 99;
  top: 0;
  left: 0;
  width: 50px;
  height: 50px;
}
.rtl .product-block .wcvendors_sold_by_in_loop {
  right: 0;
  left: auto;
}
.product-block .wcvendors_sold_by_in_loop img {
  max-width: 100%;
  max-height: 100%;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.seller-info-social {
  list-style: none;
}
.seller-info-social li {
  display: inline-block;
  margin-right: 20px;
}
.rtl .seller-info-social li {
  margin-left: 20px;
  margin-right: inherit;
}

.seller-info-top {
  margin-bottom: 20px;
}
.seller-info-top .store-brand img {
  max-width: none;
}

#woocommerce-accordion .panel {
  margin: 0;
  border: none;
  border-bottom: 1px solid #eeeeee;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
}
#woocommerce-accordion .panel > .panel-heading {
  text-transform: uppercase;
  border: none;
  padding: 18px 0;
  font-weight: 400;
  font-size: 16px;
  border-radius: 0;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  -ms-border-radius: 0;
  -o-border-radius: 0;
  background: #fff !important;
}
#woocommerce-accordion .panel > .panel-heading:hover a, #woocommerce-accordion .panel > .panel-heading:active a {
  color: #2441e7;
}
#woocommerce-accordion .panel .panel-title {
  font-size: 16px;
  font-weight: 400;
}
#woocommerce-accordion .panel .panel-title > :not(.collapsed) {
  color: #2441e7;
}
#woocommerce-accordion .panel .panel-body {
  padding: 0;
  border: none;
}
#woocommerce-accordion .title {
  margin: 0 0 10px;
  font-size: 24px;
}

.wrapper-filter {
  min-height: 73px;
  position: relative;
  padding: 20px 0;
  border-bottom: 1px solid #eeeeee;
}

.shop-top-sidebar-wrapper {
  background: #fff;
  padding: 20px 0 0;
  display: block;
  overflow: hidden;
  width: 100% !important;
}
@media (min-width: 992px) {
  .shop-top-sidebar-wrapper {
    padding: 40px 0 0;
  }
}
.shop-top-sidebar-wrapper .dropdown > span {
  color: #252525;
  font-weight: 500;
  font-size: 15px;
  display: block;
  margin: 0 0 15px;
  text-transform: uppercase;
}
.shop-top-sidebar-wrapper .widget {
  margin-bottom: 0;
}
@media (max-width: 767px) {
  .shop-top-sidebar-wrapper {
    margin-bottom: 15px;
  }
}
.shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner {
  margin-left: -15px;
  margin-right: -15px;
}
.shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner > * {
  padding-left: 15px;
  padding-right: 15px;
  float: left;
  width: 100%;
}
.rtl .shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner > * {
  float: right;
}
@media (min-width: 768px) {
  .shop-top-sidebar-wrapper .shop-top-sidebar-wrapper-inner > * {
    width: 20%;
  }
}
.shop-top-sidebar-wrapper .wrapper-limit {
  padding: 10px;
}
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter {
  padding: 0;
  margin: 0;
  list-style: none;
}
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting li,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter li {
  margin-bottom: 8px;
}
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting li:last-child,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter li:last-child {
  margin: 0;
}
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting a,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter a {
  white-space: nowrap;
}
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting .active,
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting .current,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter .active,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter .current {
  color: #2441e7;
}
.shop-top-sidebar-wrapper .wrapper-limit .apus-product-sorting,
.shop-top-sidebar-wrapper .wrapper-limit .apus-price-filter,
.shop-top-sidebar-wrapper .wrapper-limit .woocommerce-widget-layered-nav-list {
  height: 200px;
}
.shop-top-sidebar-wrapper .tagcloud {
  height: 200px;
}

.products-wrapper-grid-banner .cl-3 div.product.col-sm-4.first,
.products-wrapper-grid-banner .cl-2 div.product.col-sm-4.first {
  clear: none;
}
@media (min-width: 768px) {
  .products-wrapper-grid-banner .cl-3 div.product.col-sm-4:nth-child(3n + 1),
  .products-wrapper-grid-banner .cl-2 div.product.col-sm-4:nth-child(3n + 1) {
    clear: both;
  }
}
.products-wrapper-grid-banner .col-md-cus-5 {
  float: left;
  padding-left: 15px;
  padding-right: 15px;
}
.rtl .products-wrapper-grid-banner .col-md-cus-5 {
  float: right;
}
@media (min-width: 992px) {
  .products-wrapper-grid-banner .col-md-cus-5 {
    width: 20%;
  }
}

.product-bundles {
  padding: 20px 0;
}
.product-bundles .product-item {
  overflow: hidden;
  clear: both;
  margin-bottom: 10px;
}
.product-bundles .product-item .product-image {
  float: left;
  width: 80px;
  padding-right: 10px;
}
.rtl .product-bundles .product-item .product-image {
  float: right;
}
.rtl .product-bundles .product-item .product-image {
  padding-left: 10px;
  padding-right: inherit;
}
.product-bundles .product-item .product-content {
  overflow: hidden;
}
.product-bundles .product-item .product-name {
  display: block;
  color: #252525;
  margin: 3px 0;
}
.product-bundles .total {
  padding-bottom: 10px;
}
.product-bundles .total-discount {
  color: #5cb85c;
}
.product-bundles .total-price {
  font-size: 18px;
  font-weight: 500;
  color: #252525;
}

.product-category h3 {
  margin: 15px 0 0;
  font-size: 18px;
}
.product-category h3 .count {
  background: transparent;
  padding: 0;
}
.product-category .category-body {
  margin: 0 0 20px;
  text-align: center;
}
@media (min-width: 768px) {
  .product-category .category-body {
    margin: 0 0 30px;
  }
}

/* 7. woocommerce widgets */
.widget.widget-products .tab-content .ajax-loading {
  background: url("../images/loading-quick.gif") center 100px no-repeat #fff;
}
.widget.widget-products .widget-title {
  padding: 0 0 10px;
  margin-bottom: 25px;
}
.widget.widget-products .slick-carousel-top .slick-arrow {
  top: -60px;
}
.widget.widget-products.column1 .shop-list-small {
  margin-top: -1px;
}

.link-readmore {
  position: relative;
  padding: 30px 0;
}
.link-readmore:before {
  content: '';
  background: #eeeeee;
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: 1px;
  z-index: 2;
}
.link-readmore .link-inner {
  display: inline-block;
  padding: 0 30px;
  background: #fff;
  position: relative;
  z-index: 3;
}

.woocommerce-tabs.tabs-v1 {
  margin-bottom: 0;
}

.category-item {
  text-align: center;
  border: 1px solid #eeeeee;
  -webkit-transition: all 0.35s ease-in-out 0s;
  -o-transition: all 0.35s ease-in-out 0s;
  transition: all 0.35s ease-in-out 0s;
  padding: 10px;
}
@media (min-width: 1200px) {
  .category-item {
    padding: 50px 30px 30px;
  }
}
.category-item .image-wrapper {
  margin-bottom: 10px;
}
@media (min-width: 1200px) {
  .category-item .image-wrapper {
    margin-bottom: 25px;
  }
}
.category-item .cat-title {
  margin: 0;
  font-size: 18px;
}
@media (min-width: 1200px) {
  .category-item .cat-title {
    font-size: 24px;
  }
}
.category-item .product-nb {
  font-size: 12px;
  color: #2441e7;
  letter-spacing: 1px;
  text-transform: uppercase;
}
.category-item:hover {
  border-color: #2441e7;
}

.woocommerce .cart-collaterals-totals {
  margin-bottom: 60px;
}

/*------------------------------------*\
    Widget Price Filter
\*------------------------------------*/
.woocommerce .widget_price_filter .ui-slider .ui-slider-range {
  background: #2441e7;
}

.woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content {
  height: 6px;
  background-color: #dddde1;
  margin: 10px 15px 36px 10px;
}
.rtl .woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content {
  margin: 10px 10px 36px 15px;
}

.widget_price_filter {
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
}
.widget_price_filter .price_slider_wrapper {
  overflow: hidden;
}
.widget_price_filter .price_slider_amount .price_label {
  color: #4f4f4f;
  font-weight: 400;
  font-size: 15px;
  display: inline-block;
  text-transform: capitalize;
  float: left;
}
.rtl .widget_price_filter .price_slider_amount .price_label {
  float: right;
}
.widget_price_filter .ui-slider {
  position: relative;
  text-align: left;
}
.rtl .widget_price_filter .ui-slider {
  text-align: right;
}
.widget_price_filter .ui-slider .ui-slider-range {
  top: 0;
  height: 100%;
  background-color: #dddddd;
}
.widget_price_filter .price_slider_wrapper .ui-widget-content {
  background: #eaeaea;
  height: 4px;
  margin: 5px 10px 20px;
}

.woocommerce .widget_price_filter .price_slider_amount .button {
  border: 0;
  color: #fff;
  font-size: 14px;
  padding: 10px 15px;
  font-weight: 400;
  letter-spacing: normal;
  background-color: #2441e7;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  border-radius: 5;
  -webkit-border-radius: 5;
  -moz-border-radius: 5;
  -ms-border-radius: 5;
  -o-border-radius: 5;
  float: right;
}
.rtl .woocommerce .widget_price_filter .price_slider_amount .button {
  float: left;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-handle {
  z-index: 2;
  position: absolute;
  cursor: pointer;
  background-color: #fff;
  top: -8px;
  border: 2px solid #2441e7;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
}

.woocommerce .widget_price_filter .price_slider_amount {
  text-align: left;
  margin-top: 22px;
}
.rtl .woocommerce .widget_price_filter .price_slider_amount {
  text-align: right;
}
.woocommerce .widget_price_filter .price_slider_amount > input {
  width: 48%;
  margin-bottom: 5px;
  border: 2px solid #eeeeee;
}
.woocommerce .widget_price_filter .price_slider_amount > input:focus {
  border-color: #000;
}

/*------------------------------------*\
    Product List Widget
\*------------------------------------*/
.woocommerce ul.product_list_widget {
  list-style: none;
  border: 1px solid #eeeeee;
}
.woocommerce ul.product_list_widget li {
  clear: both;
  margin: 0;
  padding: 10px;
  border-bottom: 1px solid #eeeeee;
}
@media (min-width: 768px) {
  .woocommerce ul.product_list_widget li {
    padding: 30px 20px;
  }
}
.woocommerce ul.product_list_widget li:last-child {
  border-bottom: 0;
}
.woocommerce ul.product_list_widget li .review {
  clear: left;
}
.rtl .woocommerce ul.product_list_widget li .review {
  clear: right;
}
.woocommerce ul.product_list_widget li img {
  width: 100%;
  margin: 0;
  float: none;
}
.woocommerce ul.product_list_widget .star-rating {
  display: none;
}
.woocommerce ul.product_list_widget .woocommerce-Price-amount {
  font-size: 16px;
  color: #252525;
  font-family: "Open Sans";
}
.woocommerce ul.product_list_widget del .woocommerce-Price-amount {
  font-size: 14px;
  color: #b7b7b7;
}
.woocommerce ul.product_list_widget .product-title {
  font-size: 16px;
  display: block;
  margin: 0 0 5px;
  font-family: "Open Sans";
  height: 24px;
  overflow: hidden;
}
.woocommerce ul.product_list_widget .product-title a {
  font-weight: 400;
}
.woocommerce ul.product_list_widget .left-content {
  float: left;
  padding-right: 15px;
  width: 80px;
}
.rtl .woocommerce ul.product_list_widget .left-content {
  float: right;
}
.rtl .woocommerce ul.product_list_widget .left-content {
  padding-left: 15px;
  padding-right: inherit;
}
.woocommerce ul.product_list_widget .right-content {
  margin-top: 5px;
  overflow: hidden;
}

.product_list_v1_widget .product-block {
  padding: 25px 15px;
  border-bottom: 1px solid #eeeeee;
  margin-top: 0;
}
.product_list_v1_widget .product-block:last-child {
  border-bottom: none;
}
.product_list_v1_widget .product-block .image {
  padding: 0;
}
@media (min-width: 1199px) {
  .product_list_v1_widget .product-block .image {
    width: 150px;
    height: auto;
  }
}
@media (max-width: 1199px) {
  .product_list_v1_widget .product-block .image {
    width: 100px;
    height: auto;
  }
}
.product_list_v1_widget .product-block .caption .price {
  margin-bottom: 10px;
  text-align: left;
}
.rtl .product_list_v1_widget .product-block .caption .price {
  text-align: right;
}
.product_list_v1_widget .product-block .caption .action-bottom {
  min-height: 40px;
}
.product_list_v1_widget .product-block .caption .action-bottom .btn-cart {
  display: inline-block;
  background-color: #2441e7;
  display: inline-block;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}
.product_list_v1_widget .product-block .caption .action-bottom .btn-cart a {
  min-width: 135px;
  padding: 5px;
  display: block;
  text-align: left;
}
.product_list_v1_widget .product-block .caption .action-bottom .btn-cart:hover {
  background-color: #ff1053;
}
.product_list_v1_widget .product-block .caption .action-bottom .btn-cart .icon-cart {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  background-color: #fff;
  margin-right: 5px;
}
.rtl .product_list_v1_widget .product-block .caption .action-bottom .btn-cart .icon-cart {
  margin-left: 5px;
  margin-right: inherit;
}
.product_list_v1_widget .product-block .caption .action-bottom .btn-cart .title-cart {
  font-size: 12px;
  color: #fff;
  font-weight: normal;
  text-transform: uppercase;
  font-family: "Open Sans";
  padding-right: 10px;
}
.rtl .product_list_v1_widget .product-block .caption .action-bottom .btn-cart .title-cart {
  padding-left: 10px;
  padding-right: inherit;
}
@media (max-width: 991px) {
  .product_list_v1_widget .product-block .caption .action-bottom .btn-cart .icon-cart {
    display: none !important;
  }
  .product_list_v1_widget .product-block .caption .action-bottom .btn-cart .title-cart {
    display: block !important;
    line-height: 32px;
    padding-right: 0;
    text-align: center;
  }
  .rtl .product_list_v1_widget .product-block .caption .action-bottom .btn-cart .title-cart {
    padding-left: 0;
    padding-right: inherit;
  }
}
.product_list_v1_widget .name {
  font-weight: 400;
  margin-top: 0;
  height: 42px;
  overflow: hidden;
}

/*------------------------------------*\
    Product Special Widget
\*------------------------------------*/
.product_special_widget .widget-product {
  margin: 0;
  position: relative;
  border-bottom: 1px solid #fff;
}
.product_special_widget .widget-product:first-child {
  padding: 0;
}
.product_special_widget .widget-product:first-child .image {
  max-width: 60%;
  position: relative;
  margin: 0;
  margin-right: 10px;
}
.rtl .product_special_widget .widget-product:first-child .image {
  margin-left: 10px;
  margin-right: inherit;
}
.product_special_widget .widget-product:first-child .image .first-order {
  width: 32px;
  height: 32px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: #2441e7;
  padding: 5px 11px;
  z-index: 99;
  color: #fff;
  font-weight: 900;
}
.product_special_widget .widget-product:first-child .media-body {
  max-width: 40%;
  float: none;
  padding: 0;
}
.product_special_widget .widget-product .media-body {
  padding: 0 10px 10px 40px;
}
.product_special_widget .widget-product .order {
  width: 32px;
  background: #DADADA;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  color: #6A6A6A;
  font-weight: 900;
  padding: 0 10px;
}
.product_special_widget .widget-product .order span {
  position: relative;
  top: 50%;
  margin-top: -10px;
  display: block;
}
.product_special_widget .widget-product .review {
  clear: left;
}
.rtl .product_special_widget .widget-product .review {
  clear: right;
}
.product_special_widget .widget-product .rating {
  margin-bottom: 25px;
}
.product_special_widget .widget-product .star-rating {
  margin: 0;
}
.product_special_widget .widget-product .name {
  font-size: 14px;
  font-weight: 400;
}
.product_special_widget .widget-product .price {
  text-align: left;
}
.rtl .product_special_widget .widget-product .price {
  text-align: right;
}
.product_special_widget .widget-product .price > * {
  color: #000;
}
.product_special_widget .widget-product.last {
  background: #F5F5F5;
}

/*------------------------------------*\
    Widget Sidebar
\*------------------------------------*/
.apus-sidebar .product_list_widget .image {
  margin-right: 10px;
  width: 80px;
  height: auto;
}
.rtl .apus-sidebar .product_list_widget .image {
  margin-left: 10px;
  margin-right: inherit;
}

.woo-deals.widget-content {
  padding-bottom: 0 !important;
}
.woo-deals .pts-countdown {
  padding: 5px 0;
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  font-size: 10px;
}
.woo-deals .time {
  padding: 18px 0;
  position: absolute;
  width: 100%;
  text-align: center;
  left: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.64);
}
.woo-deals .countdown-times {
  -webkit-transform: translate(0px, 0px);
  -ms-transform: translate(0px, 0px);
  -o-transform: translate(0px, 0px);
  transform: translate(0px, 0px);
  -webkit-transition: all 0.4s ease;
  -o-transition: all 0.4s ease;
  transition: all 0.4s ease;
  text-align: center;
}
.woo-deals .countdown-times > .time-details {
  display: inline-block;
  background: #555857;
  padding: 10px 8px;
  color: #fff;
  margin: 0 2.5px;
  position: relative;
  border-radius: 0px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  border: 0;
  box-shadow: 0 -15px 15px -10px rgba(0, 0, 0, 0.3) inset;
  -o-box-shadow: 0 -15px 15px -10px rgba(0, 0, 0, 0.3) inset;
  -moz-box-shadow: 0 -15px 15px -10px rgba(0, 0, 0, 0.3) inset;
  -webkit-box-shadow: 0 -15px 15px -10px rgba(0, 0, 0, 0.3) inset;
  -ms-box-shadow: 0 -15px 15px -10px rgba(0, 0, 0, 0.3) inset;
}
.woo-deals .countdown-times > .time-details:before {
  display: block;
  width: 100%;
  height: 1px;
  background: #1e1f1f;
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  margin-top: -1px;
}
.woo-deals .countdown-times > .time-details > b {
  display: block;
  font-size: 18px;
  font-weight: 600;
}

.vertical-menu .product-block.product-list .image {
  width: 70px;
  height: auto;
}

/*------------------------------------*\
    Widget currency-switcher
\*------------------------------------*/
.woocommerce-currency-switcher-form {
  min-width: 100px;
}
.woocommerce-currency-switcher-form .dd-select {
  background: #fff !important;
  border: none;
  border-radius: 0;
}
.woocommerce-currency-switcher-form ul.dd-options {
  border: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.woocommerce-currency-switcher-form ul.dd-options li {
  padding: 0;
  border: none;
}

.widget-woocommerce-currency-switcher .dd-desc {
  display: none;
}
.widget-woocommerce-currency-switcher a.dd-option,
.widget-woocommerce-currency-switcher .dd-selected {
  padding: 5px 10px !important;
  color: #7e7e7e;
}
.widget-woocommerce-currency-switcher label {
  line-height: 100%;
  float: left;
  margin: 0;
}
.rtl .widget-woocommerce-currency-switcher label {
  float: right;
}
.widget-woocommerce-currency-switcher .dd-pointer {
  border: none !important;
  margin: 0 !important;
}
.widget-woocommerce-currency-switcher .dd-pointer:before {
  font-family: FontAwesome;
  position: absolute;
  line-height: 100%;
  right: 0;
  bottom: -4px;
}
.widget-woocommerce-currency-switcher .dd-pointer.dd-pointer-down:before {
  content: "\f107";
}
.widget-woocommerce-currency-switcher .dd-pointer.dd-pointer-up:before {
  content: "\f106";
}

.widget-productcats.style2 .widget-heading {
  background: #fff;
  text-align: left;
}
.rtl .widget-productcats.style2 .widget-heading {
  text-align: right;
}
.widget-productcats.style2 .widget-heading .widget-title {
  border-bottom: 1px solid #eeeeee;
  font-size: 24px;
}
.widget-productcats.style2 .widget-heading .nav-tabs {
  float: right;
  margin: -44px 0 0;
}
.rtl .widget-productcats.style2 .widget-heading .nav-tabs {
  float: left;
}

.widget.widget-compare-device .widget-title {
  font-size: 30px;
  margin: 0 0 30px;
  font-weight: normal;
}
.widget.widget-compare-device table {
  border: none;
  color: #757575;
}
.widget.widget-compare-device thead td {
  background: #fff !important;
  text-align: center !important;
}
.widget.widget-compare-device thead .name-title {
  font-size: 16px;
  color: #0a0a0a;
  margin: 10px 0;
}
.widget.widget-compare-device table td {
  border: none;
}
.widget.widget-compare-device table tr > td:first-child {
  color: #252525;
  text-align: left;
}
.rtl .widget.widget-compare-device table tr > td:first-child {
  text-align: right;
}
.widget.widget-compare-device table tr td {
  padding: 12px;
  text-align: center;
}
.widget.widget-compare-device table tr:nth-child(2n+1) {
  background-color: #fafafa;
}

.apus-products-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.apus-products-list .product-block {
  padding: 10px 0;
  background: #fff;
}
.apus-products-list .media-left {
  padding: 0;
}
.apus-products-list .media-body {
  padding-left: 20px;
}
.rtl .apus-products-list .media-body {
  padding-right: 20px;
  padding-left: inherit;
}
.apus-products-list .rating {
  display: none;
}
.apus-products-list .name {
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  margin: 0;
}
.apus-products-list .name a {
  color: #54b551;
  font-size: 16px;
  text-transform: capitalize;
}
.apus-products-list .product-block:hover .name a {
  color: #2441e7;
}
.apus-products-list .groups-button * i {
  color: #7e7e7e;
}
.apus-products-list .groups-button * i:hover {
  color: #2441e7;
}
.apus-products-list .groups-button .addcart, .apus-products-list .groups-button .yith-wcwl-add-to-wishlist, .apus-products-list .groups-button .quick-view {
  display: inline-block;
  padding-right: 26px;
}
.rtl .apus-products-list .groups-button .addcart, .rtl .apus-products-list .groups-button .yith-wcwl-add-to-wishlist, .rtl .apus-products-list .groups-button .quick-view {
  padding-left: 26px;
  padding-right: inherit;
}
.apus-products-list .groups-button .addcart .add-cart a {
  background: transparent;
  padding: 0;
}
.apus-products-list .groups-button .addcart .add-cart a .title-cart {
  display: none;
}
.apus-products-list .groups-button .yith-wcwl-add-to-wishlist {
  vertical-align: bottom;
}
.apus-products-list .groups-button .yith-wcwl-add-to-wishlist .sub-title {
  display: none;
}
.apus-products-list .groups-button .yith-wcwl-add-to-wishlist .feedback {
  display: none;
}
.apus-products-list .groups-button .quick-view {
  padding-right: 0px;
  vertical-align: middle;
}
.rtl .apus-products-list .groups-button .quick-view {
  padding-left: 0px;
  padding-right: inherit;
}
.apus-products-list .groups-button .quick-view a.quickview {
  background: transparent;
  border: none;
  padding: 0px;
}
.apus-products-list .price {
  margin-bottom: 10px;
}
.apus-products-list .price span.woocs_price_code del span.woocommerce-Price-amount {
  font-size: 20px;
  color: #888625;
}
.apus-products-list .price span.woocs_price_code ins span.woocommerce-Price-amount {
  font-size: 24px;
  font-weight: normal;
  color: #888625;
}
.apus-products-list .price span.woocs_price_code span.woocommerce-Price-amount {
  font-size: 24px;
  font-weight: normal;
  color: #888625;
}

.sub-categories .sub-title {
  font-size: 15px;
  color: #fff;
  background: #121212;
  padding: 14px 40px;
  margin: 0;
  text-transform: uppercase;
}
.sub-categories .sub-title .icon {
  margin-right: 20px;
}
.rtl .sub-categories .sub-title .icon {
  margin-left: 20px;
  margin-right: inherit;
}
.sub-categories .sub-title .pull-right {
  margin-top: 3px;
}
.sub-categories > .list-square {
  padding: 15px 40px;
  background: #f5f5f5;
}
.sub-categories > .list-square > li > a {
  color: #7e7e7e;
}
.sub-categories > .list-square > li > a:before {
  background: #7e7e7e;
}
.sub-categories > .list-square > li:hover > a, .sub-categories > .list-square > li.active > a {
  color: #252525;
}
.sub-categories > .list-square > li:hover > a:before, .sub-categories > .list-square > li.active > a:before {
  background: #252525;
}

.widget_deals_products .widget-title-wrapper {
  position: relative;
  margin: 0 0 50px;
}
.widget_deals_products .widget-title-wrapper .widget-title {
  margin: 0;
  font-size: 20px;
}
.widget_deals_products .widget-title-wrapper .widget-title > span {
  padding: 0 0 17px;
}
@media (min-width: 992px) {
  .widget_deals_products .widget-title-wrapper .widget-title + .apus-countdown {
    position: absolute;
    top: 0;
    background: #fff;
    right: 0;
  }
  .rtl .widget_deals_products .widget-title-wrapper .widget-title + .apus-countdown {
    left: 0;
    right: auto;
  }
  .widget_deals_products .widget-title-wrapper .widget-title + .apus-countdown .times > div:last-child {
    margin-right: 0;
  }
  .rtl .widget_deals_products .widget-title-wrapper .widget-title + .apus-countdown .times > div:last-child {
    margin-left: 0;
    margin-right: inherit;
  }
}

.list-banner-category .category-wrapper {
  position: relative;
}
.list-banner-category .category-wrapper .category-meta {
  position: absolute;
  bottom: 50px;
  left: 0;
  z-index: 1;
}
.rtl .list-banner-category .category-wrapper .category-meta {
  right: 0;
  left: auto;
}
.list-banner-category .title {
  margin: 0;
  font-size: 36px;
  letter-spacing: 0.5px;
}
.list-banner-category .title a:hover, .list-banner-category .title a:active {
  text-decoration: underline;
}

.all-products {
  font-size: 36px;
  color: #252525;
  text-align: right;
}
.rtl .all-products {
  text-align: left;
}
.all-products a:hover, .all-products a:active {
  text-decoration: underline;
}

.grid-banner-category.style1 .link-action {
  display: block;
  position: relative;
}
.grid-banner-category.style1 .link-action:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.grid-banner-category.style1 .link-action .title {
  font-size: 14px;
  text-transform: uppercase;
  margin: 0;
  display: inline-block;
  font-weight: 500;
  padding: 10px 35px;
  background: #fff;
  letter-spacing: 1px;
}
.grid-banner-category.style1 .link-action .info {
  text-align: center;
  top: 50%;
  margin-top: -19px;
  position: absolute;
  left: 0;
  width: 100%;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: all 0.3s ease-in-out 0s;
  -o-transition: all 0.3s ease-in-out 0s;
  transition: all 0.3s ease-in-out 0s;
}
.grid-banner-category.style1 .link-action:hover:before,
.grid-banner-category.style1 .link-action:hover .info, .grid-banner-category.style1 .link-action:active:before,
.grid-banner-category.style1 .link-action:active .info {
  opacity: 1;
  filter: alpha(opacity=100);
}
.grid-banner-category.style1 .link-action:hover .info, .grid-banner-category.style1 .link-action:active .info {
  -webkit-animation: zoomInDown 0.5s linear 1;
  /* Safari 4.0 - 8.0 */
  animation: zoomInDown 0.5s linear 1;
}
.grid-banner-category.style2 .link-action {
  display: block;
  position: relative;
  overflow: hidden;
}
.grid-banner-category.style2 .link-action:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 200%;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 100% 0;
  -webkit-border-radius: 0 0 100% 0;
  -moz-border-radius: 0 0 100% 0;
  -ms-border-radius: 0 0 100% 0;
  -o-border-radius: 0 0 100% 0;
  -webkit-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  transform-origin: 0 0;
  -ms-transform-origin: 0 0;
  /* IE 9 */
  -webkit-origin: 0 0;
  /* Safari 3-8 */
  -webkit-transition: all 0.4s ease-in-out 0s;
  -o-transition: all 0.4s ease-in-out 0s;
  transition: all 0.4s ease-in-out 0s;
}
.grid-banner-category.style2 .link-action .title {
  font-size: 16px;
  text-transform: uppercase;
  margin: 0;
  display: inline-block;
  font-weight: 500;
  padding: 10px 35px;
  background: #fff;
  letter-spacing: 1px;
  border: 1px solid #ebebeb;
}
.grid-banner-category.style2 .link-action .info {
  text-align: center;
  top: 10px;
  position: absolute;
  left: 10px;
}
.rtl .grid-banner-category.style2 .link-action .info {
  right: 10px;
  left: auto;
}
@media (min-width: 1200px) {
  .grid-banner-category.style2 .link-action .info {
    top: 40px;
    left: 40px;
  }
  .rtl .grid-banner-category.style2 .link-action .info {
    right: 40px;
    left: auto;
  }
}
.grid-banner-category.style2 .link-action:hover:before, .grid-banner-category.style2 .link-action:active:before {
  -webkit-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
}

table > thead > tr > th, table > thead > tr > td, .table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
  border: 0;
}

table > thead > tr > th, table > thead > tr > td, table > tbody > tr > th, table > tbody > tr > td, table > tfoot > tr > th, table > tfoot > tr > td, .table-bordered > thead > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > tfoot > tr > td {
  border-bottom: 0;
  border-right: 0;
}

#add_payment_method table.cart td.actions .coupon .input-text, .woocommerce-cart table.cart td.actions .coupon .input-text, .woocommerce-checkout table.cart td.actions .coupon .input-text {
  height: 50px;
  width: auto;
  margin-right: 20px !important;
  padding: 10px 25px !important;
  background-color: transparent;
  border: 1px solid #dddddd;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  -o-border-radius: 5px;
  -webkit-box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 1px 1px -1px rgba(0, 0, 0, 0.2);
}
.rtl #add_payment_method table.cart td.actions .coupon .input-text, .rtl .woocommerce-cart table.cart td.actions .coupon .input-text, .rtl .woocommerce-checkout table.cart td.actions .coupon .input-text {
  margin-left: 20px !important;
  margin-right: inherit;
}
#add_payment_method table.cart td.actions .coupon .input-text::-moz-placeholder, .woocommerce-cart table.cart td.actions .coupon .input-text::-moz-placeholder, .woocommerce-checkout table.cart td.actions .coupon .input-text::-moz-placeholder {
  color: #7e7e7e;
  opacity: 1;
}
#add_payment_method table.cart td.actions .coupon .input-text:-ms-input-placeholder, .woocommerce-cart table.cart td.actions .coupon .input-text:-ms-input-placeholder, .woocommerce-checkout table.cart td.actions .coupon .input-text:-ms-input-placeholder {
  color: #7e7e7e;
}
#add_payment_method table.cart td.actions .coupon .input-text::-webkit-input-placeholder, .woocommerce-cart table.cart td.actions .coupon .input-text::-webkit-input-placeholder, .woocommerce-checkout table.cart td.actions .coupon .input-text::-webkit-input-placeholder {
  color: #7e7e7e;
}
#add_payment_method table.cart td.actions .coupon .input-text:hover, .woocommerce-cart table.cart td.actions .coupon .input-text:hover, .woocommerce-checkout table.cart td.actions .coupon .input-text:hover {
  outline: none;
}
#add_payment_method table.cart td.actions .coupon .input-text:focus, .woocommerce-cart table.cart td.actions .coupon .input-text:focus, .woocommerce-checkout table.cart td.actions .coupon .input-text:focus {
  outline: none;
}
#add_payment_method table.cart td.actions .coupon .input-text:active, .woocommerce-cart table.cart td.actions .coupon .input-text:active, .woocommerce-checkout table.cart td.actions .coupon .input-text:active {
  outline: none;
}

.woocommerce-order-details,
.woocommerce-checkout {
  margin-bottom: 50px;
}

.select2-container--default .select2-selection--single {
  border: none;
}

.woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt {
  display: inline-block;
  white-space: nowrap;
  vertical-align: middle;
  font-size: 14px;
  font-weight: normal;
  text-transform: uppercase;
  color: #fff;
  padding: 12px 30px;
  border-radius: 20px;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -ms-border-radius: 20px;
  -o-border-radius: 20px;
}
.woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover {
  color: #fff;
}
.woocommerce #respond input#submit.alt:active, .woocommerce a.button.alt:active, .woocommerce button.button.alt:active, .woocommerce input.button.alt:active {
  color: #fff;
}

.woocommerce-customer-details > h2,
.woocommerce-order-details__title {
  font-size: 28px;
}

.woocommerce form .form-row .input-checkbox {
  position: static;
  float: none;
  display: inline-block;
  vertical-align: middle;
  margin: 0 5px 0 0;
}
.lt-ie8 .woocommerce form .form-row .input-checkbox {
  display: inline;
  zoom: 1;
}
.rtl .woocommerce form .form-row .input-checkbox {
  margin: 0 0 0 5px;
}
.woocommerce form .form-row .input-checkbox + label {
  display: inline-block;
}

.widget-categoriestabs .nav-tabs {
  margin: 40px 0;
  border: none;
  text-align: center;
}
.widget-categoriestabs .nav-tabs > li {
  margin: 0 12px;
  display: inline-block;
  float: none;
}
.widget-categoriestabs .nav-tabs > li.active > a {
  text-decoration: underline;
  color: #000;
}
.widget-categoriestabs .nav-tabs > li > a {
  text-transform: capitalize;
  font-size: 16px;
  color: #000;
  border: none !important;
}
.widget-categoriestabs .nav-tabs > li > a .product-count {
  font-size: 14px;
  color: #7e7e7e;
  font-family: "Open Sans";
  display: inline-block;
  vertical-align: top;
}

.woocommerce-checkout {
  margin-bottom: 30px;
}

.woocommerce-widget-layered-nav .view-more-list {
  font-size: 14px;
  text-decoration: underline;
  color: #5cb85c;
}
.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list {
  overflow: hidden;
}
.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list.hideContent {
  margin-bottom: 10px;
  height: 260px;
}
.woocommerce-widget-layered-nav .woocommerce-widget-layered-nav-list.showContent {
  height: auto;
  margin-bottom: 10px;
}

.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item {
  font-size: 15px;
  margin: 0 0 5px;
  width: 100%;
  white-space: nowrap;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item:last-child {
  margin: 0;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a {
  color: #7e7e7e;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a:hover {
  color: #2441e7;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a:active {
  color: #2441e7;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a .swatch-color {
  display: inline-block;
  vertical-align: baseline;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  margin-right: 10px;
}
.rtl .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a .swatch-color {
  margin-left: 10px;
  margin-right: inherit;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item > a .swatch-label {
  display: none;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a {
  color: #2441e7;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a .swatch-color {
  display: none;
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a:before {
  vertical-align: baseline;
  color: #2441e7;
  content: "\f14a";
  font-family: 'FontAwesome';
}
.woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a:hover:before, .woocommerce-widget-layered-nav-list .woocommerce-widget-layered-nav-list__item.chosen > a:active:before {
  color: #e44343;
  font-family: 'FontAwesome';
  content: "\f057";
}

.apus-price-filter,
.apus-product-sorting {
  list-style: none;
  padding: 0;
  margin: 0;
}
.apus-price-filter li,
.apus-product-sorting li {
  margin-bottom: 5px;
}
.apus-price-filter li:last-child,
.apus-product-sorting li:last-child {
  margin-bottom: 0;
}
.apus-price-filter li a,
.apus-product-sorting li a {
  color: #7e7e7e;
}
.apus-price-filter li a:hover, .apus-price-filter li a:active,
.apus-product-sorting li a:hover,
.apus-product-sorting li a:active {
  color: #2441e7;
}
.apus-price-filter li.current, .apus-price-filter li.active,
.apus-product-sorting li.current,
.apus-product-sorting li.active {
  color: #2441e7;
}

.widget.widget-products-tabs {
  margin-bottom: 0;
}
@media (min-width: 1200px) {
  .widget.widget-products-tabs .widget-title {
    font-size: 44px;
  }
}
.widget.widget-products-tabs .top-info {
  overflow: hidden;
  margin-bottom: 15px;
  -webkit-justify-content: space-between;
  /* Safari 6.1+ */
  justify-content: space-between;
}
@media (min-width: 1200px) {
  .widget.widget-products-tabs .top-info {
    margin-bottom: 35px;
  }
}
.widget.widget-products-tabs .top-info .nav.tabs-product.center {
  margin-bottom: 0;
}
.widget.widget-products-tabs .widget-title {
  padding: 0 0 10px;
  margin: 0;
}
.widget.widget-products-tabs .widget-title:before {
  width: 2000px;
}
.widget.widget-products-tabs .widget-title.center:before, .widget.widget-products-tabs .widget-title.center:after {
  display: none;
}
.widget.widget-products-tabs .widget-content.carousel {
  margin-bottom: -40px;
}
.widget.widget-products-tabs .widget-content.carousel .slick-list {
  padding-bottom: 40px;
}

.widget.widget-products-deal {
  margin: 0;
}
.widget.widget-products-deal .widget-title {
  padding: 0 0 10px;
  margin-bottom: 25px;
}
.widget.widget-products-deal .slick-carousel-top .slick-arrow {
  top: -60px;
}
.widget.widget-products-deal .apus-countdown-dark .times > div > span {
  color: #252525;
}

.tab-content.loading {
  min-height: 400px;
  position: relative;
}
.tab-content.loading:before {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  content: '';
  background: url("../images/loading-quick.gif") center 100px no-repeat rgba(255, 255, 255, 0.9);
}

.widget.widget-tab-style_center .widget-title {
  font-size: 36px;
  text-align: center;
  margin: 0 0 10px;
  color: #252525;
  padding: 0;
  border: none;
}
.widget.widget-tab-style_center .widget-title:before {
  display: none;
}

@keyframes pulsate {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0.1);
    -ms-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
  }
  50% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
@-webkit-keyframes pulsate {
  0% {
    opacity: 0;
    filter: alpha(opacity=0);
    -webkit-transform: scale(0.1);
    -ms-transform: scale(0.1);
    -o-transform: scale(0.1);
    transform: scale(0.1);
  }
  50% {
    opacity: 1;
    filter: alpha(opacity=100);
  }
  100% {
    -webkit-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
    opacity: 0;
    filter: alpha(opacity=0);
  }
}
.apus-lookbook .mapper-pin-wrapper > a {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  background: #f43434;
  position: relative;
}
.apus-lookbook .mapper-pin-wrapper > a:before {
  content: '';
  width: 40px;
  height: 40px;
  background: rgba(244, 52, 52, 0.2);
  position: absolute;
  top: 0;
  left: 0;
  margin-top: -12px;
  margin-left: -12px;
  z-index: 2;
  border-radius: 50%;
  -webkit-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  animation: 1s ease-out 0s normal none infinite running pulsate;
  -webkit-animation: 1s ease-out 0s normal none infinite running pulsate;
}
.apus-lookbook .mapper-pin-wrapper .image img {
  width: 100%;
}
.apus-lookbook .mapper-popup:before {
  content: '';
  width: 40px;
  height: 40px;
  position: absolute;
  top: 50%;
  left: 100%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
.apus-lookbook .mapper-popup:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 100%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
  width: 30px;
  height: 24px;
  border-width: 12px 15px;
  border-style: solid;
  border-color: transparent transparent transparent #fff;
}

.widget.widget-recent_viewed .slick-carousel,
.widget.upsells .slick-carousel,
.widget.related .slick-carousel {
  margin-bottom: 0;
}
.widget.widget-recent_viewed .slick-list,
.widget.upsells .slick-list,
.widget.related .slick-list {
  padding-bottom: 0;
  overflow: visible;
}

.cross-sells {
  margin-top: 30px;
}
.cross-sells > h2 {
  margin: 0 0 20px;
  font-size: 22px;
}
@media (min-width: 992px) {
  .cross-sells > h2 {
    margin: 0 0 30px;
  }
}

.slick-carousel .product-block.grid {
  margin-bottom: 0;
}
