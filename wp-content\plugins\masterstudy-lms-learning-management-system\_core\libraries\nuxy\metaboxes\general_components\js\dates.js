(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);throw new Error("Cannot find module '"+o+"'")}var f=n[o]={exports:{}};t[o][0].call(f.exports,function(e){var n=t[o][1][e];return s(n?n:e)},f,f.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
"use strict";

Vue.component('date-picker', DatePicker["default"]);
Vue.component('wpcfto_dates', {
  props: ['fields', 'field_label', 'field_name', 'field_id', 'field_value'],
  data: function data() {
    return {
      value: [],
      saveValue: []
    };
  },
  mounted: function mounted() {
    if (typeof this.field_value !== 'undefined') {
      if (typeof this.field_value[0] !== 'undefined') {
        this.saveValue.push(this.field_value[0]);
        this.value.push(new Date(parseInt(this.field_value[0])));
      }

      if (typeof this.field_value[1] !== 'undefined') {
        this.saveValue.push(this.field_value[1]);
        this.value.push(new Date(parseInt(this.field_value[1])));
      }
    }
  },
  template: "\n        <div class=\"wpcfto_generic_field wpcfto_generic_field_flex_input wpcfto_generic_field__date\">\n\n            <wpcfto_fields_aside_before :fields=\"fields\" :field_label=\"field_label\"></wpcfto_fields_aside_before>\n\n            <div class=\"wpcfto-field-content\">\n            \n                <div class=\"wpcfto_datepicker\">\n                    <date-picker v-model=\"value\" range lang=\"en\" @change=\"dateChanged(value)\"></date-picker>\n                </div>\n    \n                <input type=\"hidden\" v-bind:name=\"field_name\" v-model=\"saveValue\" />\n                <input type=\"hidden\" v-bind:name=\"field_name + '_start'\" v-model=\"saveValue[0]\" />\n                <input type=\"hidden\" v-bind:name=\"field_name + '_end'\" v-model=\"saveValue[1]\" />\n                \n            </div>\n\n            <wpcfto_fields_aside_after :fields=\"fields\"></wpcfto_fields_aside_after>\n            \n        </div>\n    ",
  methods: {
    dateChanged: function dateChanged(newDate) {
      var customDate = [];
      customDate.push(new Date(newDate[0]).getTime());
      customDate.push(new Date(newDate[1]).getTime());
      this.$emit('wpcfto-get-value', customDate);
      this.$set(this, 'saveValue', customDate);
    }
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
},{}]},{},[1])