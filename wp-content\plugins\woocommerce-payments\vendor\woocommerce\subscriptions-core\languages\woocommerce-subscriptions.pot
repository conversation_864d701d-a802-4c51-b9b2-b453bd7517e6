# Copyright (C) 2021 WooCommerce
# This file is distributed under the same license as the WooCommerce Subscriptions plugin.
msgid ""
msgstr ""
"Project-Id-Version: WooCommerce Subscriptions 3.1.1\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/woocommerce-subscriptions\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2021-05-19T05:09:40+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.3.0\n"
"X-Domain: woocommerce-subscriptions\n"

#. Plugin Name of the plugin
#: includes/privacy/class-wcs-privacy.php:40
msgid "WooCommerce Subscriptions"
msgstr ""

#. Plugin URI of the plugin
msgid "https://www.woocommerce.com/products/woocommerce-subscriptions/"
msgstr ""

#. Description of the plugin
msgid "Sell products and services with recurring payments in your WooCommerce Store."
msgstr ""

#. Author of the plugin
#: includes/admin/class-wcs-admin-reports.php:104
#: includes/admin/reports/class-wcs-report-cache-manager.php:262
msgid "WooCommerce"
msgstr ""

#. Author URI of the plugin
msgid "https://woocommerce.com/"
msgstr ""

#. translators: 1: relation type, 2: list of valid relation types.
#: includes/abstracts/abstract-wcs-related-order-store.php:148
msgid "Invalid relation type: %1$s. Order relationship type must be one of: %2$s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:204
msgid "Simple subscription"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:205
msgid "Variable subscription"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:226
msgid "Downloadable"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:227
msgid "Virtual"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:291
msgid "Choose the subscription price, billing interval and period."
msgstr ""

#. translators: placeholder is trial period validation message if passed an invalid value (e.g. "Trial period can not exceed 4 weeks")
#: includes/admin/class-wc-subscriptions-admin.php:293
msgctxt "Trial period field tooltip on Edit Product administration screen"
msgid "An optional period of time to wait before charging the first recurring payment. Any sign up fee will still be charged at the outset of the subscription. %s"
msgstr ""

#. translators: %s: currency symbol.
#. translators: placeholder is a currency symbol / code
#: includes/admin/class-wc-subscriptions-admin.php:307
#: templates/admin/html-variation-price.php:44
msgid "Subscription price (%s)"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:311
msgctxt "example price"
msgid "e.g. 5.90"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:312
msgid "Subscription interval"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:318
#: includes/admin/class-wc-subscriptions-admin.php:456
msgid "Subscription period"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:333
#: includes/admin/class-wc-subscriptions-admin.php:457
#: templates/admin/html-variation-price.php:66
msgid "Expire after"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:336
msgid "Automatically expire the subscription after this length of time. This length is in addition to any free trial or amount of time provided before a synchronised first renewal date."
msgstr ""

#. translators: %s is a currency symbol / code
#: includes/admin/class-wc-subscriptions-admin.php:346
#: templates/admin/html-variation-price.php:20
msgid "Sign-up fee (%s)"
msgstr ""

#. translators: %s is a currency symbol / code
#. translators: placeholder is a currency symbol / code
#: includes/admin/class-wc-subscriptions-admin.php:347
#: templates/admin/deprecated/html-variation-price.php:31
#: templates/admin/deprecated/html-variation-price.php:86
#: templates/admin/html-variation-price.php:21
#: templates/admin/html-variation-price.php:47
msgctxt "example price"
msgid "e.g. 9.90"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:348
msgid "Optionally include an amount to be charged at the outset of the subscription. The sign-up fee will be charged immediately, even if the product has a free trial or the payment dates are synced."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:360
#: includes/class-wc-subscriptions-cart.php:2446
#: templates/admin/html-variation-price.php:25
msgid "Free trial"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:363
#: templates/admin/deprecated/html-variation-price.php:115
msgid "Subscription Trial Period"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:395
msgid "One time shipping"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:396
msgid "Shipping for subscription products is normally charged on the initial order and all renewal orders. Enable this to only charge shipping once on the initial order. Note: for this setting to be enabled the subscription must not have a free trial or a synced renewal date."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:453
msgid "Subscription pricing"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:454
msgid "Subscription sign-up fee"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:455
msgid "Subscription billing interval"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:458
msgid "Free trial length"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:459
msgid "Free trial period"
msgstr ""

#. translators: %s: subscription status.
#: includes/admin/class-wc-subscriptions-admin.php:784
msgid "Unable to change subscription status to \"%s\". Please assign a customer to the subscription to activate it."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:826
msgid "Trashing this order will also trash the subscriptions purchased with the order."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:839
msgid "Enter the new period, either day, week, month or year:"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:840
msgid "Enter a new length (e.g. 5):"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:841
msgid "Enter a new interval as a single number (e.g. to charge every 2nd month, enter 2):"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:842
msgid "Delete all variations without a subscription"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:849
msgid ""
"You are about to trash one or more orders which contain a subscription.\n"
"\n"
"Trashing the orders will also trash the subscriptions purchased with these orders."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:862
msgid ""
"WARNING: Bad things are about to happen!\n"
"\n"
"The payment gateway used to purchase this subscription does not support modifying a subscription's details.\n"
"\n"
"Changes to the billing period, recurring discount, recurring tax or recurring total may not be reflected in the amount charged by the payment gateway."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:863
msgid "You are deleting a subscription item. You will also need to manually cancel and trash the subscription on the Manage Subscriptions screen."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:870
msgid ""
"Warning: Deleting a user will also delete the user's subscriptions. The user's orders will remain but be reassigned to the 'Guest' user.\n"
"\n"
"Do you want to continue to delete this user and any associated subscriptions?"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:874
msgid "PayPal Standard has a number of limitations and does not support all subscription features."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:874
msgid "Because of this, it is not recommended as a payment method for Subscriptions unless it is the only available option for your country."
msgstr ""

#. translators: placeholders are for HTML tags. They are 1$: "<h3>", 2$: "</h3>", 3$: "<p>", 4$: "<em>", 5$: "</em>", 6$: "<em>", 7$: "</em>", 8$: "</p>"
#: includes/admin/class-wc-subscriptions-admin.php:892
msgctxt "used in admin pointer script params in javascript as type pointer content"
msgid "%1$sChoose Subscription%2$s%3$sThe WooCommerce Subscriptions extension adds two new subscription product types - %4$sSimple subscription%5$s and %6$sVariable subscription%7$s.%8$s"
msgstr ""

#. translators: placeholders are for HTML tags. They are 1$: "<h3>", 2$: "</h3>", 3$: "<p>", 4$: "</p>"
#: includes/admin/class-wc-subscriptions-admin.php:894
msgctxt "used in admin pointer script params in javascript as price pointer content"
msgid "%1$sSet a Price%2$s%3$sSubscription prices are a little different to other product prices. For a subscription, you can set a billing period, length, sign-up fee and free trial.%4$s"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:949
msgid "Active subscriber?"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:992
msgid "Manage Subscriptions"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:996
#: woocommerce-subscriptions.php:305
msgid "Search Subscriptions"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1018
#: includes/admin/class-wc-subscriptions-admin.php:1170
#: includes/admin/class-wcs-admin-reports.php:46
#: includes/admin/class-wcs-admin-system-status.php:56
#: includes/admin/class-wcs-wc-admin-manager.php:38
#: includes/admin/class-wcs-wc-admin-manager.php:80
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:917
#: includes/class-wcs-query.php:108
#: includes/class-wcs-query.php:133
#: includes/class-wcs-query.php:287
#: includes/privacy/class-wcs-privacy-exporters.php:51
#: woocommerce-subscriptions.php:296
#: woocommerce-subscriptions.php:309
msgid "Subscriptions"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1210
msgid "Button Text"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1217
msgid "Add to Cart Button Text"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1218
msgid "A product displays a button with the text \"Add to cart\". By default, a subscription changes this to \"Sign up now\". You can customise the button text for subscriptions here."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1222
#: includes/admin/class-wc-subscriptions-admin.php:1225
#: includes/admin/class-wc-subscriptions-admin.php:1234
#: includes/admin/class-wc-subscriptions-admin.php:1237
#: includes/class-wc-product-subscription-variation.php:98
#: includes/class-wc-product-variable-subscription.php:73
#: includes/class-wc-subscriptions-product.php:1196
#: includes/class-wc-subscriptions-product.php:1214
#: woocommerce-subscriptions.php:650
msgid "Sign up now"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1229
msgid "Place Order Button Text"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1230
msgid "Use this field to customise the text displayed on the checkout button when an order contains a subscription. Normally the checkout submission button displays \"Place order\". When the cart contains a subscription, this is changed to \"Sign up now\"."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1246
msgid "Roles"
msgstr ""

#. translators: placeholders are <em> tags
#: includes/admin/class-wc-subscriptions-admin.php:1249
msgid "Choose the default roles to assign to active and inactive subscribers. For record keeping purposes, a user account must be created for subscribers. Users with the %1$sadministrator%2$s role, such as yourself, will never be allocated these roles to prevent locking out administrators."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1254
msgid "Subscriber Default Role"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1255
msgid "When a subscription is activated, either manually or after a successful purchase, new users will be assigned this role."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1266
msgid "Inactive Subscriber Role"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1267
msgid "If a subscriber's subscription is manually cancelled or expires, she will be assigned this role."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1283
msgctxt "option section heading"
msgid "Renewals"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1290
msgid "Manual Renewal Payments"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1291
msgid "Accept Manual Renewals"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/admin/class-wc-subscriptions-admin.php:1296
msgid "With manual renewals, a customer's subscription is put on-hold until they login and pay to renew it. %1$sLearn more%2$s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1302
msgid "Turn off Automatic Payments"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/admin/class-wc-subscriptions-admin.php:1307
msgid "If you don't want new subscription purchases to automatically charge renewal payments, you can turn off automatic payments. Existing automatic subscriptions will continue to charge customers automatically. %1$sLearn more%2$s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1318
msgctxt "options section heading"
msgid "Miscellaneous"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1325
msgid "Customer Suspensions"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1326
msgctxt "there's a number immediately in front of this text"
msgid "suspensions per billing period."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1332
msgid "Set a maximum number of times a customer can suspend their account for each billing period. For example, for a value of 3 and a subscription billed yearly, if the customer has suspended their account 3 times, they will not be presented with the option to suspend their account until the next year. Store managers will always be able to suspend an active subscription. Set this to 0 to turn off the customer suspension feature completely."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1336
msgid "Mixed Checkout"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1337
msgid "Allow multiple subscriptions and products to be purchased simultaneously."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1341
msgid "Allow a subscription product to be purchased with other products and subscriptions in the same transaction."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1345
msgid "$0 Initial Checkout"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1346
msgid "Allow $0 initial checkout without a payment method."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1350
msgid "Allow a subscription product with a $0 initial payment to be purchased without providing a payment method. The customer will be required to provide a payment method at the end of the initial period to keep the subscription active."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1354
#: includes/upgrades/templates/wcs-about-2-0.php:108
msgid "Drip Downloadable Content"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1355
msgid "Enable dripping for downloadable content on subscription products."
msgstr ""

#. translators: %s is a line break.
#: includes/admin/class-wc-subscriptions-admin.php:1360
msgid "Enabling this grants access to new downloadable files added to a product only after the next renewal is processed.%sBy default, access to new downloadable files added to a product is granted immediately to any customer that has an active subscription with that product."
msgstr ""

#. translators: $1-$2: opening and closing <strong> tags, $3-$4: opening and closing <em> tags
#: includes/admin/class-wc-subscriptions-admin.php:1401
msgid "%1$sWooCommerce Subscriptions Installed%2$s &#8211; %3$sYou're ready to start selling subscriptions!%4$s"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1419
msgid "Add a Subscription Product"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1420
#: includes/upgrades/templates/wcs-about-2-0.php:35
#: includes/upgrades/templates/wcs-about.php:34
#: woocommerce-subscriptions.php:1201
msgid "Settings"
msgstr ""

#. translators: placeholder is a number
#: includes/admin/class-wc-subscriptions-admin.php:1503
msgid "We can't find a subscription with ID #%d. Perhaps it was deleted?"
msgstr ""

#. translators: Placeholders are opening and closing link tags.
#: includes/admin/class-wc-subscriptions-admin.php:1547
msgid "We weren't able to locate the set of report results you requested. Please regenerate the link from the %1$sSubscription Reports screen%2$s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1601
msgid "We can't find a paid subscription order for this user."
msgstr ""

#. translators: placeholders are opening link tag, ID of sub, and closing link tag
#: includes/admin/class-wc-subscriptions-admin.php:1633
#: includes/admin/class-wc-subscriptions-admin.php:1638
msgid "Showing orders for %1$sSubscription %2$s%3$s"
msgstr ""

#. translators: number of 1$: days, 2$: weeks, 3$: months, 4$: years
#: includes/admin/class-wc-subscriptions-admin.php:1662
msgid "The trial period can not exceed: %1$s, %2$s, %3$s or %4$s."
msgstr ""

#. translators: placeholder is a time period (e.g. "4 weeks")
#: includes/admin/class-wc-subscriptions-admin.php:1667
msgid "The trial period can not exceed %s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1729
#: includes/admin/class-wcs-admin-system-status.php:93
msgctxt "label that indicates whether debugging is turned on for the plugin"
msgid "WCS_DEBUG"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1730
#: includes/admin/class-wc-subscriptions-admin.php:1797
#: includes/admin/class-wcs-admin-system-status.php:95
#: includes/admin/reports/class-wcs-report-cache-manager.php:316
msgid "Yes"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1730
#: includes/admin/class-wcs-admin-system-status.php:95
#: includes/admin/reports/class-wcs-report-cache-manager.php:316
msgid "No"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1735
#: includes/admin/class-wcs-admin-system-status.php:107
msgctxt "Live or Staging, Label on WooCommerce -> System Status page"
msgid "Subscriptions Mode"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1736
#: includes/admin/class-wcs-admin-system-status.php:109
msgctxt "refers to staging site"
msgid "Staging"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1736
#: includes/admin/class-wcs-admin-system-status.php:109
msgctxt "refers to live site"
msgid "Live"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1766
msgid "Automatic Recurring Payments"
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1797
msgid "Supports automatic renewal payments with the WooCommerce Subscriptions extension."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1895
msgid "Subscription items can no longer be edited."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1899
msgid "This subscription is no longer editable because the payment gateway does not allow modification of recurring amounts."
msgstr ""

#. translators: $1-2: opening and closing tags of a link that takes to Woo marketplace / Stripe product page
#: includes/admin/class-wc-subscriptions-admin.php:1918
msgid "No payment gateways capable of processing automatic subscription payments are enabled. If you would like to process automatic payments, we recommend the %1$sfree Stripe extension%2$s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:1923
msgid "Recurring Payments"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/admin/class-wc-subscriptions-admin.php:1931
msgid "Payment gateways which don't support automatic recurring payments can be used to process %1$smanual subscription renewal payments%2$s."
msgstr ""

#. translators: $1-$2: opening and closing tags. Link to documents->payment gateways, 3$-4$: opening and closing tags. Link to WooCommerce extensions shop page
#: includes/admin/class-wc-subscriptions-admin.php:1938
msgid "Find new gateways that %1$ssupport automatic subscription payments%2$s in the official %3$sWooCommerce Marketplace%4$s."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:2042
msgid "Note that purchasing a subscription still requires an account."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:2056
msgid "The product type can not be changed because this product is associated with subscriptions."
msgstr ""

#: includes/admin/class-wc-subscriptions-admin.php:2110
#: includes/admin/class-wc-subscriptions-admin.php:2111
msgid "Allow subscription customers to create an account during checkout"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:77
msgctxt "meta box title"
msgid "Subscription Data"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:79
msgctxt "meta box title"
msgid "Schedule"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:83
#: includes/admin/class-wcs-admin-meta-boxes.php:87
msgid "Related Orders"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:127
msgid "Please enter a start date in the past."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:128
msgid "Please enter a date at least 2 minutes into the future."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:128
msgid "Please enter a date at least one hour into the future."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:129
msgid "Please enter a date after the trial end."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:130
#: includes/admin/class-wcs-admin-meta-boxes.php:131
msgid "Please enter a date after the start date."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:132
msgid "Please enter a date before the next payment."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:133
msgid "Please enter a date after the next payment."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:134
msgid ""
"Are you sure you want to process a renewal?\n"
"\n"
"This will charge the customer and email them the renewal order (if emails are enabled)."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:148
msgid ""
"Are you sure you want to retry payment for this renewal order?\n"
"\n"
"This will attempt to charge the customer and send renewal order emails (if emails are enabled)."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:181
msgid "Process renewal"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:185
msgid "Create pending renewal order"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:187
msgid "Create pending parent order"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:191
msgid "Retry Renewal Payment"
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:204
msgid "Process renewal order action requested by admin."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:215
msgid "Create pending renewal order requested by admin action."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:257
msgid "Create pending parent order requested by admin action."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:288
msgid "Retry renewal payment action requested by admin."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:384
msgid "This order contains line items with prices above the current product price. To override the product's live price when the customer pays for this order, lock in the manual price increases."
msgstr ""

#: includes/admin/class-wcs-admin-meta-boxes.php:388
msgid "Lock manual price increases"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:214
msgid "Search for a product&hellip;"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:258
msgctxt "an action on a subscription"
msgid "Activate"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:259
msgctxt "an action on a subscription"
msgid "Put on-hold"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:260
#: includes/admin/class-wcs-admin-post-types.php:473
#: includes/class-wc-subscriptions-manager.php:1854
#: includes/wcs-user-functions.php:354
#: templates/myaccount/related-orders.php:78
msgctxt "an action on a subscription"
msgid "Cancel"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:335
msgctxt "Used in order note. Reason why status changed."
msgid "Subscription status changed by bulk edit:"
msgstr ""

#. translators: placeholder is the number of subscriptions updated
#: includes/admin/class-wcs-admin-post-types.php:391
msgid "%s subscription status changed."
msgid_plural "%s subscription statuses changed."
msgstr[0] ""
msgstr[1] ""

#. translators: 1$: is the number of subscriptions not updated, 2$: is the error message
#: includes/admin/class-wcs-admin-post-types.php:398
msgid "%1$s subscription could not be updated: %2$s"
msgid_plural "%1$s subscriptions could not be updated: %2$s"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/class-wcs-admin-post-types.php:422
#: includes/admin/meta-boxes/views/html-related-orders-table.php:20
#: templates/myaccount/my-subscriptions.php:22
#: templates/myaccount/my-subscriptions.php:37
#: templates/myaccount/related-orders.php:24
#: templates/myaccount/related-orders.php:50
#: templates/myaccount/related-subscriptions.php:22
#: templates/myaccount/related-subscriptions.php:36
#: templates/myaccount/subscription-details.php:18
msgid "Status"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:423
#: templates/emails/cancelled-subscription.php:21
#: templates/emails/expired-subscription.php:21
#: templates/emails/on-hold-subscription.php:21
#: templates/myaccount/my-subscriptions.php:21
#: templates/myaccount/related-subscriptions.php:21
#: woocommerce-subscriptions.php:297
msgid "Subscription"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:424
msgid "Items"
msgstr ""

#. Translators: %1$s is a date.
#: includes/admin/class-wcs-admin-post-types.php:425
#: build/index.js:8
#: build/index.js:14
msgid "Total"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:426
msgid "Start Date"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:427
msgid "Trial End"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:428
msgid "Next Payment"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:429
msgid "Last Order Date"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:430
msgid "End Date"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:431
msgctxt "number of orders linked to a subscription"
msgid "Orders"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:471
#: includes/wcs-user-functions.php:338
msgid "Reactivate"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:472
#: includes/wcs-user-functions.php:333
msgid "Suspend"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:474
#: includes/admin/class-wcs-admin-post-types.php:489
msgid "Trash"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:475
#: includes/admin/class-wcs-admin-post-types.php:493
msgid "Delete Permanently"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:487
#: includes/class-wc-subscriptions-product.php:766
msgid "Restore this item from the Trash"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:487
#: includes/class-wc-subscriptions-product.php:767
msgid "Restore"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:489
msgid "Move this item to the Trash"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:493
msgid "Delete this item permanently"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:499
msgid "Cancel Now"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:527
msgctxt "meaning billing address"
msgid "Billing:"
msgstr ""

#. translators: placeholder is customer's billing email
#: includes/admin/class-wcs-admin-post-types.php:532
msgid "Email: %s"
msgstr ""

#. translators: placeholder is customer's billing phone number
#: includes/admin/class-wcs-admin-post-types.php:537
msgid "Tel: %s"
msgstr ""

#. translators: $1: is opening link, $2: is subscription order number, $3: is closing link tag, $4: is user's name
#: includes/admin/class-wcs-admin-post-types.php:565
msgctxt "Subscription title on admin table. (e.g.: #211 for John Doe)"
msgid "%1$s#%2$s%3$s for %4$s"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:569
msgid "Show more details"
msgstr ""

#. translators: %d: item count.
#: includes/admin/class-wcs-admin-post-types.php:590
msgid "%d item"
msgid_plural "%d items"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is the display name of a payment gateway a subscription was paid by
#. translators: %s: payment method.
#: includes/admin/class-wcs-admin-post-types.php:609
#: includes/class-wc-subscription.php:2025
msgid "Via %s"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:651
msgid "Y/m/d g:i:s A"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:654
msgid "This date should be treated as an estimate only. The payment gateway for this subscription controls when payments are processed."
msgstr ""

#. translators: placeholder is previous post title
#: includes/admin/class-wcs-admin-post-types.php:907
#: includes/admin/class-wcs-admin-post-types.php:910
#: includes/admin/class-wcs-admin-post-types.php:913
msgid "Subscription updated."
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:908
msgid "Custom field updated."
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:909
msgid "Custom field deleted."
msgstr ""

#. translators: placeholder is previous post title
#: includes/admin/class-wcs-admin-post-types.php:912
msgctxt "used in post updated messages"
msgid "Subscription restored to revision from %s"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:914
msgid "Subscription saved."
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:915
msgid "Subscription submitted."
msgstr ""

#. translators: php date string
#: includes/admin/class-wcs-admin-post-types.php:917
msgid "Subscription scheduled for: %1$s."
msgstr ""

#. translators: php date string
#: includes/admin/class-wcs-admin-post-types.php:917
msgctxt "used in \"Subscription scheduled for <date>\""
msgid "M j, Y @ G:i"
msgstr ""

#. translators: php date string
#: includes/admin/class-wcs-admin-post-types.php:918
msgid "Subscription draft updated."
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:954
msgid "Any Payment Method"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:955
msgid "None"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:961
#: includes/class-wc-subscription.php:2007
#: includes/class-wcs-change-payment-method-admin.php:168
msgid "Manual Renewal"
msgstr ""

#. translators: 1: user display name 2: user ID 3: user email
#: includes/admin/class-wcs-admin-post-types.php:1107
msgid "%1$s (#%2$s &ndash; %3$s)"
msgstr ""

#: includes/admin/class-wcs-admin-post-types.php:1114
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:85
msgid "Search for a customer&hellip;"
msgstr ""

#: includes/admin/class-wcs-admin-product-import-export-manager.php:32
msgid "Subscription variations"
msgstr ""

#: includes/admin/class-wcs-admin-reports.php:49
msgid "Subscription Events by Date"
msgstr ""

#: includes/admin/class-wcs-admin-reports.php:55
msgid "Upcoming Recurring Revenue"
msgstr ""

#: includes/admin/class-wcs-admin-reports.php:61
msgid "Retention Rate"
msgstr ""

#: includes/admin/class-wcs-admin-reports.php:67
msgid "Subscriptions by Product"
msgstr ""

#: includes/admin/class-wcs-admin-reports.php:73
msgid "Subscriptions by Customer"
msgstr ""

#: includes/admin/class-wcs-admin-reports.php:83
msgid "Failed Payment Retries"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:57
msgid "This section shows any information about Subscriptions."
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:61
msgid "Store Setup"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:62
msgid "This section shows general information about the store."
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:66
msgid "Subscriptions by Payment Gateway"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:67
msgid "This section shows information about Subscription payment methods."
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:71
msgid "Payment Gateway Support"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:72
msgid "This section shows information about payment gateway feature support."
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:119
msgctxt "Live URL, Label on WooCommerce -> System Status page"
msgid "Subscriptions Live URL"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:135
msgctxt "label for the system status page"
msgid "Subscriptions Template Theme Overrides"
msgstr ""

#. translators: placeholders are opening/closing tags linking to documentation on outdated templates.
#: includes/admin/class-wcs-admin-system-status.php:145
msgid "%1$sLearn how to update%2$s"
msgstr ""

#. translators: %1$s is the file version, %2$s is the core version
#: includes/admin/class-wcs-admin-system-status.php:191
msgid "version %1$s is out of date. The core version is %2$s"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:212
msgctxt "label for the system status page"
msgid "Subscription Statuses"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:233
msgctxt "label for the system status page"
msgid "WooCommerce Account Connected"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:256
msgctxt "label for the system status page"
msgid "Active Product Key"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:274
msgctxt "label for the system status page"
msgid "Other"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:308
msgctxt "label for the system status page"
msgid "PayPal Reference Transactions Enabled"
msgstr ""

#: includes/admin/class-wcs-admin-system-status.php:336
msgctxt "label for the system status page"
msgid "Country / State"
msgstr ""

#: includes/admin/class-wcs-wc-admin-manager.php:49
msgid "Add New"
msgstr ""

#: includes/admin/class-wcs-wc-admin-manager.php:59
msgid "Edit Subscription"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:77
msgctxt "relation to order"
msgid "Subscription"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:82
msgctxt "relation to order"
msgid "Initial Subscription"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:93
msgctxt "relation to order"
msgid "Renewal Order"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:96
msgctxt "relation to order"
msgid "Parent Order"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:99
msgctxt "relation to order"
msgid "Resubscribed Subscription"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:99
msgctxt "relation to order"
msgid "Resubscribe Order"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-related-orders.php:102
msgctxt "relation to order"
msgid "Unknown Order Type"
msgstr ""

#. translators: placeholder is the ID of the subscription
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:48
msgctxt "edit subscription header"
msgid "Subscription #%s details"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:52
msgid "General"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:55
msgid "Customer:"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:64
msgid "View other subscriptions &rarr;"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:69
msgid "Profile &rarr;"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:93
msgid "Subscription status:"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:110
msgid "Parent order: "
msgstr ""

#. translators: placeholder is an order number.
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:114
msgid "#%1$s"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:121
msgid "Parent order:"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:127
msgid "Select an order&hellip;"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:137
msgid "Billing"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:138
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:229
#: includes/payment-retry/class-wcs-retry-post-store.php:40
msgid "Edit"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:140
msgid "Load billing address"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:148
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:150
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:240
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:242
msgid "Address"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:150
msgid "No billing address set."
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:170
#: includes/class-wcs-change-payment-method-admin.php:38
#: includes/class-wcs-change-payment-method-admin.php:51
msgid "Payment Method"
msgstr ""

#. translators: %s: gateway ID.
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:175
#: includes/class-wcs-change-payment-method-admin.php:53
msgctxt "The gateway ID displayed on the Edit Subscriptions screen when editing payment method."
msgid "Gateway ID: [%s]"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:212
msgid "Customer change payment method page &rarr;"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:214
msgid "Customer add payment method page &rarr;"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:228
#: includes/class-wc-subscriptions-extend-store-endpoint.php:256
#: build/index.js:3
msgid "Shipping"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:231
msgid "Load shipping address"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:232
msgid "Copy billing address"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:242
msgid "No shipping address set."
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:264
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:294
msgid "Customer Provided Note"
msgstr ""

#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:295
msgid "Customer's notes about the order"
msgstr ""

#. translators: %s: parent order number (linked to its details screen).
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:387
msgctxt "subscription note after linking to a parent order"
msgid "Subscription linked to parent order %s via admin."
msgstr ""

#. translators: placeholder is error message from the payment gateway or subscriptions when updating the status
#: includes/admin/meta-boxes/class-wcs-meta-box-subscription-data.php:401
msgid "Error updating some information: %s"
msgstr ""

#. translators: placeholder is an order number.
#. translators: placeholder is an order ID.
#. translators: %s: order number.
#. translators: %s: order ID.
#: includes/admin/meta-boxes/views/html-related-orders-row.php:21
#: includes/admin/meta-boxes/views/html-unknown-related-orders-row.php:18
#: includes/class-wc-subscriptions-renewal-order.php:158
#: includes/early-renewal/class-wcs-cart-early-renewal.php:310
#: includes/early-renewal/wcs-early-renewal-functions.php:162
#: templates/myaccount/my-subscriptions.php:34
#: templates/myaccount/related-orders.php:44
#: templates/myaccount/related-subscriptions.php:33
msgctxt "hash before order number"
msgid "#%s"
msgstr ""

#. translators: php date format
#: includes/admin/meta-boxes/views/html-related-orders-row.php:33
#: includes/admin/meta-boxes/views/html-retries-table.php:44
msgctxt "post date"
msgid "Y/m/d g:i:s A"
msgstr ""

#: includes/admin/meta-boxes/views/html-related-orders-row.php:36
#: includes/admin/meta-boxes/views/html-retries-table.php:47
msgid "Unpublished"
msgstr ""

#: includes/admin/meta-boxes/views/html-related-orders-table.php:17
#: templates/myaccount/related-orders.php:42
msgid "Order Number"
msgstr ""

#: includes/admin/meta-boxes/views/html-related-orders-table.php:18
msgid "Relationship"
msgstr ""

#: includes/admin/meta-boxes/views/html-related-orders-table.php:19
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:776
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:195
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:209
#: templates/myaccount/related-orders.php:23
#: templates/myaccount/related-orders.php:47
msgid "Date"
msgstr ""

#: includes/admin/meta-boxes/views/html-related-orders-table.php:21
#: templates/myaccount/my-subscriptions.php:24
#: templates/myaccount/related-orders.php:25
#: templates/myaccount/related-subscriptions.php:24
#: templates/myaccount/subscription-totals-table.php:22
msgctxt "table heading"
msgid "Total"
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:17
msgid "Retry Date"
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:19
msgid "Retry Status"
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:20
msgid "The status of the automatic payment retry: pending means the retry will be processed in the future, failed means the payment was not successful when retried and completed means the payment succeeded when retried."
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:23
msgid "Status of Order"
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:24
msgid "The status applied to the order for the time between when the renewal payment failed or last retry occurred and when this retry was processed."
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:27
msgid "Status of Subscription"
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:28
msgid "The status applied to the subscription for the time between when the renewal payment failed or last retry occurred and when this retry was processed."
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:31
msgid "Email"
msgstr ""

#: includes/admin/meta-boxes/views/html-retries-table.php:32
msgid "The email sent to the customer when the renewal payment or payment retry failed to notify them that the payment would be retried."
msgstr ""

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:23
#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:72
msgid "Payment:"
msgstr ""

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:34
#: templates/admin/deprecated/html-variation-price.php:57
msgid "Billing Period"
msgstr ""

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:43
msgid "Recurring:"
msgstr ""

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:63
msgid "Timezone:"
msgstr ""

#: includes/admin/meta-boxes/views/html-subscription-schedule.php:63
msgid "Error: unable to find timezone of your browser."
msgstr ""

#: includes/admin/reports/class-wcs-report-cache-manager.php:265
msgid "Please note: data for this report is cached. The data displayed may be out of date by up to 24 hours. The cache is updated each morning at 4am in your site's timezone."
msgstr ""

#: includes/admin/reports/class-wcs-report-cache-manager.php:314
msgctxt "Whether the Report Cache has been enabled"
msgid "Report Cache Enabled"
msgstr ""

#: includes/admin/reports/class-wcs-report-cache-manager.php:320
msgid "Cache Update Failures"
msgstr ""

#. translators: %d refers to the number of times we have detected cache update failures
#: includes/admin/reports/class-wcs-report-cache-manager.php:323
msgid "%d failures"
msgid_plural "%d failure"
msgstr[0] ""
msgstr[1] ""

#. translators: 1$: count, 2$ and 3$ are opening and closing strong tags, respectively.
#: includes/admin/reports/class-wcs-report-dashboard.php:216
msgid "%2$s%1$s signup%3$s subscription signups this month"
msgid_plural "%2$s%1$s signups%3$s subscription signups this month"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-dashboard.php:224
msgid "%s signup revenue this month"
msgstr ""

#. translators: 1$: count, 2$ and 3$ are opening and closing strong tags, respectively.
#: includes/admin/reports/class-wcs-report-dashboard.php:232
msgid "%2$s%1$s renewal%3$s subscription renewals this month"
msgid_plural "%2$s%1$s renewals%3$s subscription renewals this month"
msgstr[0] ""
msgstr[1] ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-dashboard.php:240
msgid "%s renewal revenue this month"
msgstr ""

#. translators: 1$: count, 2$ and 3$ are opening and closing strong tags, respectively.
#: includes/admin/reports/class-wcs-report-dashboard.php:248
msgid "%2$s%1$s cancellation%3$s subscription cancellations this month"
msgid_plural "%2$s%1$s cancellations%3$s subscription cancellations this month"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/reports/class-wcs-report-retention-rate.php:156
msgctxt "X axis label on retention rate graph"
msgid "Number of days after sign-up"
msgstr ""

#: includes/admin/reports/class-wcs-report-retention-rate.php:159
msgctxt "X axis label on retention rate graph"
msgid "Number of weeks after sign-up"
msgstr ""

#: includes/admin/reports/class-wcs-report-retention-rate.php:162
msgctxt "X axis label on retention rate graph"
msgid "Number of months after sign-up"
msgstr ""

#: includes/admin/reports/class-wcs-report-retention-rate.php:226
msgid "Unended Subscription Count"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:22
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:97
msgid "Customer"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:23
msgid "Customers"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:32
msgid "No customers found."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:43
msgid "Customer Totals"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:44
msgid "Total Subscribers"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:44
msgid "The number of unique customers with a subscription of any status other than pending or trashed."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:45
msgid "Active Subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:45
msgid "The total number of subscriptions with a status of active or pending cancellation."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:46
msgid "Total Subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:46
msgid "The total number of subscriptions with a status other than pending or trashed."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:47
msgid "Total Subscription Orders"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:47
msgid "The total number of sign-up, switch and renewal orders placed with your store with a paid status (i.e. processing or complete)."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:48
msgid "Average Lifetime Value"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:50
msgid "The average value of all customers' sign-up, switch and renewal orders."
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:99
msgid "Active Subscriptions %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:99
msgid "The number of subscriptions this customer has with a status of active or pending cancellation."
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:101
msgid "Total Subscriptions %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:101
msgid "The number of subscriptions this customer has with a status other than pending or trashed."
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:103
msgid "Total Subscription Orders %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:103
msgid "The number of sign-up, switch and renewal orders this customer has placed with your store with a paid status (i.e. processing or complete)."
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:105
msgid "Lifetime Value from Subscriptions %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-customer.php:105
msgid "The total value of this customer's sign-up, switch and renewal orders."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:20
msgid "Product"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:21
msgid "Products"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:30
msgid "No products found."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:89
msgid "Subscription Product"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:91
msgid "Subscription Count %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:91
msgid "The number of subscriptions that include this product as a line item and have a status other than pending or trashed."
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:93
msgid "Average Recurring Line Total %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:93
msgid "The average line total for this product on each subscription."
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:95
msgid "Average Lifetime Value %s"
msgstr ""

#. translators: %s: help tip.
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:95
msgid "The average line total on all orders for this product line item."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-by-product.php:300
#: includes/admin/reports/class-wcs-report-subscription-by-product.php:340
msgid "subscriptions"
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:488
msgid "%s signup revenue in this period"
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:489
msgid "The sum of all subscription parent orders, including other items, fees, tax and shipping."
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:496
msgid "%s renewal revenue in this period"
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:497
msgid "The sum of all renewal orders including tax and shipping."
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:504
msgid "%s resubscribe revenue in this period"
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:505
msgid "The sum of all resubscribe orders including tax and shipping."
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:512
msgid "%s switch revenue in this period"
msgstr ""

#. translators: %s: formatted total amount.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:513
msgid "The sum of all switch orders including tax and shipping."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:521
msgid "%2$s %1$s new subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:535
msgid "The number of subscriptions created during this period, either by being manually created, imported or a customer placing an order. This includes orders pending payment."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:543
msgid "%2$s %1$s subscription signups"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:557
msgid "The number of subscriptions purchased in parent orders created during this period. This represents the new subscriptions created by customers placing an order via checkout."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:565
msgid "%2$s %1$s subscription resubscribes"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:579
msgid "The number of resubscribe orders processed during this period."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:587
msgid "%2$s %1$s subscription renewals"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:601
msgid "The number of renewal orders processed during this period."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:609
msgid "%2$s %1$s subscription switches"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:623
msgid "The number of subscriptions upgraded, downgraded or cross-graded during this period."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:631
msgid "%2$s %1$s subscription cancellations"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:645
msgid "The number of subscriptions cancelled by the customer or store manager during this period.  The pre-paid term may not yet have ended during this period."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:653
msgid "%2$s %1$s ended subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:667
msgid "The number of subscriptions which have either expired or reached the end of the prepaid term if it was previously cancelled."
msgstr ""

#. translators: 2: link opening tag, 1: subscription count and closing tag.
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:678
msgid "%2$s %1$s current subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:693
msgid "The number of subscriptions during this period with an end date in the future and a status other than pending."
msgstr ""

#. translators: %s: subscription net gain (with percentage).
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:710
msgid "%s net subscription gain"
msgstr ""

#. translators: %s: subscription net loss (with percentage).
#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:713
msgid "%s net subscription loss"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:718
msgid "Change in subscriptions between the start and end of the period."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:732
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:159
msgid "Year"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:733
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:160
msgid "Last Month"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:734
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:161
msgid "This Month"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:735
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:162
msgid "Last 7 Days"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:780
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:199
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:213
msgid "Export CSV"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:839
msgid "Switched subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:855
msgid "New Subscriptions"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:871
msgid "Subscriptions signups"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:886
msgid "Number of resubscribes"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:901
msgid "Number of renewals"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:933
msgid "Subscriptions Ended"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:949
msgid "Cancellations"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:964
msgid "Signup Totals"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:984
msgid "Resubscribe Totals"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:1004
msgid "Renewal Totals"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-events-by-date.php:1024
msgid "Switch Totals"
msgstr ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:113
msgid "%s renewal revenue recovered"
msgstr ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:114
msgid "The total amount of revenue, including tax and shipping, recovered with the failed payment retry system for renewal orders with a failed payment."
msgstr ""

#. translators: %s: renewal count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:121
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:96
msgid "%s renewal orders"
msgstr ""

#. translators: %s: renewal count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:122
msgid "The number of renewal orders which had a failed payment use the retry system."
msgstr ""

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:128
msgid "%s retry attempts succeeded"
msgstr ""

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:129
msgid "The number of renewal payment retries for this period which were able to process the payment which had previously failed one or more times."
msgstr ""

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:136
msgid "%s retry attempts failed"
msgstr ""

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:137
msgid "The number of renewal payment retries for this period which did not result in a successful payment."
msgstr ""

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:144
msgid "%s retry attempts pending"
msgstr ""

#. translators: %s: retry count.
#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:145
msgid "The number of renewal payment retries not yet processed."
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:247
msgid "Successful retries"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:263
msgid "Failed retries"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:279
msgid "Pending retries"
msgstr ""

#: includes/admin/reports/class-wcs-report-subscription-payment-retry.php:295
msgid "Recovered Renewal Revenue"
msgstr ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:89
msgid "%s renewal income in this period"
msgstr ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:90
msgid "The sum of all the upcoming renewal orders, including items, fees, tax and shipping, for currently active subscriptions."
msgstr ""

#. translators: %s: renewal count.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:97
msgid "The number of upcoming renewal orders, for currently active subscriptions."
msgstr ""

#. translators: %s: formatted amount.
#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:103
msgid "%s average renewal amount"
msgstr ""

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:179
msgid "Next 12 Months"
msgstr ""

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:180
msgid "Next 30 Days"
msgstr ""

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:181
msgid "Next Month"
msgstr ""

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:182
msgid "Next 7 Days"
msgstr ""

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:247
msgid "Renewals count"
msgstr ""

#: includes/admin/reports/class-wcs-report-upcoming-recurring-revenue.php:256
msgid "Renewals amount"
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:34
#: includes/class-wcs-staging.php:55
msgid "staging"
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:34
msgid "live"
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:79
msgid "Subscriptions."
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:85
msgid "WCS debug constant."
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:91
msgid "Subscriptions Mode"
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:97
msgid "Subscriptions Live Site URL"
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:104
msgid "Subscriptions broken down by status."
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:113
msgid "Whether the Report Cache is enabled."
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:119
msgid "Number of report cache failures."
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:125
msgid "Subscriptions by Payment Gateway."
msgstr ""

#: includes/api/class-wc-rest-subscription-system-status-manager.php:134
msgid "Payment Gateway Feature Support."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:155
msgid "Invalid subscription ID."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:161
msgid "Failed to load subscription object with the ID %d."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:321
msgid "Subscription dates could not be set. Error message: %s"
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:349
msgid "Subscription status."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:352
msgid "Where the subscription was created."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:353
msgid "Currency the subscription was created with, in ISO format."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:354
msgid "The date the subscription was created, in the site's timezone."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:355
msgid "The date the subscription was created, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:356
msgid "The date the subscription was last modified, in the site's timezone."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:357
msgid "The date the subscription was last modified, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:358
msgid "User ID who owns the subscription."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:368
msgid "The status to transition a subscription to."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:374
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:350
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:508
#: includes/class-wc-subscriptions-extend-store-endpoint.php:144
#: includes/class-wc-subscriptions-extend-store-endpoint.php:421
msgid "The number of billing periods between subscription renewals."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:379
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:355
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:513
#: includes/class-wc-subscriptions-extend-store-endpoint.php:137
#: includes/class-wc-subscriptions-extend-store-endpoint.php:414
msgid "Billing period for the subscription."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:385
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:361
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:519
msgid "Subscription payment details."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:390
msgid "Payment method meta and token in a post_meta_key: token format."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:395
msgid "Payment method meta and token in a user_meta_key : token format."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:402
msgid "The subscription's start date, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:407
msgid "The subscription's trial date, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:412
msgid "The subscription's next payment date, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:417
msgid "The subscription's cancelled date, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:422
msgid "The subscription's end date, as GMT."
msgstr ""

#: includes/api/class-wc-rest-subscriptions-controller.php:441
msgid "Limit result set to subscriptions which have specific statuses."
msgstr ""

#. translators: placeholder is the payment method ID.
#: includes/api/class-wc-rest-subscriptions-controller.php:485
msgid "The %s payment gateway does not support admin changing the payment method."
msgstr ""

#. translators: 1$: gateway id, 2$: error message
#: includes/api/class-wc-rest-subscriptions-controller.php:502
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:336
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:407
msgid "Subscription payment method could not be set to %1$s with error message: %2$s"
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:102
#: wcs-functions.php:178
msgid "Invalid subscription status given."
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:124
msgid "You do not have permission to read the subscriptions count"
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:173
msgid "You do not have permission to create subscriptions"
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:248
msgid "The requested subscription cannot be edited."
msgstr ""

#. translators: placeholder is error message
#: includes/api/legacy/class-wc-api-subscriptions.php:276
msgctxt "API error message when editing the order failed"
msgid "Edit subscription failed with error: %s"
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:314
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:306
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:369
msgid "Gateway does not support admin changing the payment method on a Subscription."
msgstr ""

#. translators: 1$: gateway id, 2$: error message
#: includes/api/legacy/class-wc-api-subscriptions.php:352
msgid "Subscription payment method could not be set to %1$s and has been set to manual with error message: %2$s"
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:387
#: wcs-functions.php:152
msgid "Invalid subscription billing interval given. Must be an integer greater than 0."
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:398
#: wcs-functions.php:147
msgid "Invalid subscription billing period given."
msgstr ""

#: includes/api/legacy/class-wc-api-subscriptions.php:613
msgctxt "API response confirming order note deleted from a subscription"
msgid "Permanently deleted subscription note"
msgstr ""

#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:172
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:294
msgid "Invalid subscription id."
msgstr ""

#. translators: placeholder is an error message.
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:242
msgid "Cannot create subscription: %s."
msgstr ""

#. translators: placeholder is an error message.
#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:287
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:477
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:783
msgid "Updating subscription dates errored with message: %s"
msgstr ""

#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:366
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:524
msgid "Payment gateway ID."
msgstr ""

#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:373
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:531
msgid "The subscription's start date."
msgstr ""

#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:378
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:536
msgid "The subscription's trial date"
msgstr ""

#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:383
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:541
#: includes/class-wc-subscriptions-extend-store-endpoint.php:408
msgid "The subscription's next payment date."
msgstr ""

#: includes/api/legacy/class-wc-rest-subscriptions-controller.php:388
#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:546
msgid "The subscription's end date."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:185
msgid "Customer ID is invalid."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:502
msgid "The status to transition the subscription to. Unlike the \"status\" param, this will calculate and update the subscription dates."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:551
msgid "The subscription's original subscription ID if this is a resubscribed subscription."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:557
msgid "The subscription's resubscribed subscription ID."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:563
msgid "The date the subscription's latest order was completed, in GMT."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:569
msgid "The date the subscription's latest order was paid, in GMT."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:575
msgid "Removed line items data."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:582
msgid "Item ID."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:588
msgid "Product name."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:594
msgid "Product SKU."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:600
msgid "Product ID."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:605
msgid "Variation ID, if applicable."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:610
msgid "Quantity ordered."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:615
msgid "Tax class of product."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:621
msgid "Product price."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:627
msgid "Line subtotal (before discounts)."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:632
msgid "Line subtotal tax (before discounts)."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:637
msgid "Line total (after discounts)."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:642
msgid "Line total tax (after discounts)."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:647
msgid "Line taxes."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:655
msgid "Tax rate ID."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:661
msgid "Tax total."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:667
msgid "Tax subtotal."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:676
msgid "Removed line item meta data."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:684
msgid "Meta key."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:690
msgid "Meta label."
msgstr ""

#: includes/api/v1/class-wc-rest-subscriptions-v1-controller.php:696
msgid "Meta value."
msgstr ""

#: includes/class-wc-product-subscription.php:73
msgid "Read more"
msgstr ""

#. translators: %s: subscription status.
#: includes/class-wc-subscription.php:423
msgid "Unable to change subscription status to \"%s\"."
msgstr ""

#. translators: 1: subscription status, 2: error message.
#: includes/class-wc-subscription.php:546
msgid "Unable to change subscription status to \"%1$s\". Exception: %2$s"
msgstr ""

#. translators: 1: old subscription status 2: new subscription status
#: includes/class-wc-subscription.php:576
msgid "Status changed from %1$s to %2$s."
msgstr ""

#. translators: %s: new order status
#: includes/class-wc-subscription.php:590
msgid "Status set to %s."
msgstr ""

#: includes/class-wc-subscription.php:604
msgid "Error during subscription status transition."
msgstr ""

#. translators: placeholder is human time diff (e.g. "3 weeks")
#: includes/class-wc-subscription.php:1200
#: includes/class-wc-subscriptions-manager.php:2304
msgid "In %s"
msgstr ""

#. translators: placeholder is human time diff (e.g. "3 weeks")
#: includes/class-wc-subscription.php:1203
#: includes/wcs-formatting-functions.php:246
msgid "%s ago"
msgstr ""

#: includes/class-wc-subscription.php:1210
msgid "Not yet ended"
msgstr ""

#: includes/class-wc-subscription.php:1213
msgid "Not cancelled"
msgstr ""

#: includes/class-wc-subscription.php:1218
msgctxt "original denotes there is no date to display"
msgid "-"
msgstr ""

#: includes/class-wc-subscription.php:1326
msgid "The creation date of a subscription can not be deleted, only updated."
msgstr ""

#: includes/class-wc-subscription.php:1329
msgid "The start date of a subscription can not be deleted, only updated."
msgstr ""

#. translators: %s: date type (e.g. "trial_end").
#: includes/class-wc-subscription.php:1334
msgid "The %s date of a subscription can not be deleted. You must delete the order."
msgstr ""

#. translators: %d: subscription ID.
#. translators: %d: order ID.
#: includes/class-wc-subscription.php:1343
#: includes/class-wc-subscription.php:2448
msgid "Subscription #%d: "
msgstr ""

#: includes/class-wc-subscription.php:1757
msgid "Payment status marked complete."
msgstr ""

#: includes/class-wc-subscription.php:1785
msgid "Payment failed."
msgstr ""

#: includes/class-wc-subscription.php:1790
msgid "Subscription Cancelled: maximum number of failed payments reached."
msgstr ""

#: includes/class-wc-subscription.php:1900
msgid "The \"all\" value for $order_type parameter is deprecated. It was a misnomer, as it did not return resubscribe orders. It was also inconsistent with order type values accepted by wcs_get_subscription_orders(). Use array( \"parent\", \"renewal\", \"switch\" ) to maintain previous behaviour, or \"any\" to receive all order types, including switch and resubscribe."
msgstr ""

#: includes/class-wc-subscription.php:2104
#: wcs-functions.php:828
msgid "Payment method meta must be an array."
msgstr ""

#: includes/class-wc-subscription.php:2340
msgid "Invalid format. First parameter needs to be an array."
msgstr ""

#: includes/class-wc-subscription.php:2344
msgid "Invalid data. First parameter was empty when passed to update_dates()."
msgstr ""

#: includes/class-wc-subscription.php:2351
msgid "Invalid data. First parameter has a date that is not in the registered date types."
msgstr ""

#. translators: placeholder is date type (e.g. "end", "next_payment"...)
#: includes/class-wc-subscription.php:2378
msgctxt "appears in an error message if date is wrong format"
msgid "Invalid %s date. The date must be of the format: \"Y-m-d H:i:s\"."
msgstr ""

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2416
msgid "The %s date must occur after the cancellation date."
msgstr ""

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2422
msgid "The %s date must occur after the last payment date."
msgstr ""

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2427
msgid "The %s date must occur after the next payment date."
msgstr ""

#. translators: %s: date type (e.g. "end").
#: includes/class-wc-subscription.php:2433
msgid "The %s date must occur after the trial end date."
msgstr ""

#. translators: %s: date type (e.g. "next_payment").
#: includes/class-wc-subscription.php:2438
msgid "The %s date must occur after the start date."
msgstr ""

#: includes/class-wc-subscription.php:2468
#: includes/class-wc-subscriptions-checkout.php:332
msgid "Backordered"
msgstr ""

#: includes/class-wc-subscriptions-addresses.php:64
msgid "Change address"
msgstr ""

#: includes/class-wc-subscriptions-addresses.php:106
msgid "Both the shipping address used for the subscription and your default shipping address for future purchases will be updated."
msgstr ""

#. translators: $1: address type (Shipping Address / Billing Address), $2: opening <strong> tag, $3: closing </strong> tag
#: includes/class-wc-subscriptions-addresses.php:119
msgid "Update the %1$s used for %2$sall%3$s of my active subscriptions"
msgstr ""

#. translators: %s: subscription ID.
#. translators: %s: order number.
#. translators: placeholder is a subscription ID.
#: includes/class-wc-subscriptions-addresses.php:243
#: includes/class-wc-subscriptions-change-payment-gateway.php:763
#: includes/class-wcs-query.php:101
msgctxt "hash before order number"
msgid "Subscription #%s"
msgstr ""

#. translators: %s: address type (eg. 'billing' or 'shipping').
#: includes/class-wc-subscriptions-addresses.php:249
msgctxt "change billing or shipping address"
msgid "Change %s address"
msgstr ""

#: includes/class-wc-subscriptions-cart-validator.php:56
#: woocommerce-subscriptions.php:560
msgid "A subscription renewal has been removed from your cart. Multiple subscriptions can not be purchased at the same time."
msgstr ""

#: includes/class-wc-subscriptions-cart-validator.php:62
#: woocommerce-subscriptions.php:566
msgid "A subscription has been removed from your cart. Due to payment gateway restrictions, different subscription products can not be purchased at the same time."
msgstr ""

#: includes/class-wc-subscriptions-cart-validator.php:68
#: woocommerce-subscriptions.php:572
msgid "A subscription has been removed from your cart. Products and subscriptions can not be purchased at the same time."
msgstr ""

#: includes/class-wc-subscriptions-cart-validator.php:111
msgid "Your cart has been emptied of subscription products. Only one subscription product can be purchased at a time."
msgstr ""

#: includes/class-wc-subscriptions-cart-validator.php:136
#: includes/class-wc-subscriptions-cart.php:1506
msgid "That subscription product can not be added to your cart as it already contains a subscription renewal."
msgstr ""

#: includes/class-wc-subscriptions-cart.php:486
msgid "Initial Shipment"
msgstr ""

#: includes/class-wc-subscriptions-cart.php:936
msgid "Please enter a valid postcode/ZIP."
msgstr ""

#: includes/class-wc-subscriptions-cart.php:1172
msgid "Invalid recurring shipping method."
msgstr ""

#: includes/class-wc-subscriptions-cart.php:2182
msgid "now"
msgstr ""

#. translators: placeholder is a number of days.
#: includes/class-wc-subscriptions-cart.php:2341
#: includes/wcs-time-functions.php:58
msgid "%s day"
msgid_plural "%s days"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is a number of weeks.
#: includes/class-wc-subscriptions-cart.php:2345
#: includes/wcs-time-functions.php:60
msgid "%s week"
msgid_plural "%s weeks"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is a number of months.
#: includes/class-wc-subscriptions-cart.php:2349
#: includes/wcs-time-functions.php:62
msgid "%s month"
msgid_plural "%s months"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is a number of years.
#: includes/class-wc-subscriptions-cart.php:2353
#: includes/wcs-time-functions.php:64
msgid "%s year"
msgid_plural "%s years"
msgstr[0] ""
msgstr[1] ""

#. translators: 1$: day of the week (e.g. "every Wednesday").
#: includes/class-wc-subscriptions-cart.php:2375
msgid "every %1$s"
msgstr ""

#. translators: 1$: period, 2$: day of the week (e.g. "every 2nd week on Wednesday").
#: includes/class-wc-subscriptions-cart.php:2379
msgid "every %1$s on %2$s"
msgstr ""

#: includes/class-wc-subscriptions-cart.php:2388
msgid "on the last day of each month"
msgstr ""

#. translators: 1$: day of the month (e.g. "23rd") (e.g. "every 23rd of each month").
#: includes/class-wc-subscriptions-cart.php:2392
msgid "on the %1$s of each month"
msgstr ""

#. translators: 1$: interval (e.g. "3rd") (e.g. "on the last day of every 3rd month").
#: includes/class-wc-subscriptions-cart.php:2400
msgid "on the last day of every %1$s month"
msgstr ""

#. translators: on the, 1$: <date> day of every, 2$: <interval> month (e.g. "on the 23rd day of every 2nd month").
#: includes/class-wc-subscriptions-cart.php:2406
msgid "on the %1$s day of every %2$s month"
msgstr ""

#. translators: on, 1$: <date>, 2$: <month> each year (e.g. "on March 15th each year").
#: includes/class-wc-subscriptions-cart.php:2417
msgid "on %1$s %2$s each year"
msgstr ""

#. translators: 1$: month (e.g. "March"), 2$: day of the month (e.g. "23rd), 3$: interval year (r.g  March 23rd every 2nd year").
#: includes/class-wc-subscriptions-cart.php:2424
msgid "on %1$s %2$s every %3$s year"
msgstr ""

#: includes/class-wc-subscriptions-cart.php:2456
msgid "Sign up fee"
msgstr ""

#: includes/class-wc-subscriptions-cart.php:2466
msgid "Renews"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:181
msgid "Sorry, this subscription change payment method request is invalid and cannot be processed."
msgstr ""

#. translators: placeholder is next payment's date
#: includes/class-wc-subscriptions-change-payment-gateway.php:205
msgid " Next payment is due %s."
msgstr ""

#. translators: placeholder is either empty or "Next payment is due..."
#: includes/class-wc-subscriptions-change-payment-gateway.php:211
msgid "Choose a new payment method.%s"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:247
#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:106
msgid "There was an error with your request. Please try again."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:250
#: includes/class-wcs-template-loader.php:35
#: includes/wcs-helper-functions.php:286
msgid "Invalid Subscription."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:253
#: includes/class-wcs-cart-resubscribe.php:78
#: includes/class-wcs-cart-resubscribe.php:129
#: includes/class-wcs-user-change-status-handler.php:111
#: includes/early-renewal/class-wcs-cart-early-renewal.php:96
msgid "That doesn't appear to be one of your subscriptions."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:256
#: includes/class-wcs-query.php:243
msgid "The payment method can not be changed for that subscription."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:259
msgid "Invalid order."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:277
msgctxt "label on button, imperative"
msgid "Change payment"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:279
msgctxt "label on button, imperative"
msgid "Add payment"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:340
msgid "Payment method updated."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:340
msgid "Payment method added."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:380
#: includes/class-wc-subscriptions-change-payment-gateway.php:382
msgid "Payment method updated for all your current subscriptions."
msgstr ""

#. translators: 1: old payment title, 2: new payment title.
#: includes/class-wc-subscriptions-change-payment-gateway.php:536
msgctxt "%1$s: old payment title, %2$s: new payment title"
msgid "Payment method changed from \"%1$s\" to \"%2$s\" by the subscriber from their account page."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:547
msgid "An error occurred updating your subscription's payment method. Please contact us for assistance."
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:555
msgid "%1$sError:%2$s %3$s"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:731
#: includes/class-wc-subscriptions-change-payment-gateway.php:769
msgctxt "the page title of the change payment method form"
msgid "Change payment method"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:733
#: includes/class-wc-subscriptions-change-payment-gateway.php:774
msgctxt "the page title of the add payment method form"
msgid "Add payment method"
msgstr ""

#: includes/class-wc-subscriptions-change-payment-gateway.php:819
msgid "Please log in to your account below to choose a new payment method for your subscription."
msgstr ""

#. translators: placeholder is an internal error number
#: includes/class-wc-subscriptions-checkout.php:196
#: includes/class-wc-subscriptions-checkout.php:376
msgid "Error %d: Unable to create subscription. Please try again."
msgstr ""

#. translators: placeholder is an internal error number
#: includes/class-wc-subscriptions-checkout.php:213
msgid "Error %d: Unable to add tax to subscription. Please try again."
msgstr ""

#. translators: placeholder is an internal error number
#: includes/class-wc-subscriptions-checkout.php:225
msgid "Error %d: Unable to create order. Please try again."
msgstr ""

#. Translators: Placeholders are opening and closing strong and link tags.
#: includes/class-wc-subscriptions-checkout.php:505
msgid "Purchasing a subscription product requires an account. Please go to the %1$sMy Account%2$s page to login or register."
msgstr ""

#. Translators: Placeholders are opening and closing strong and link tags.
#: includes/class-wc-subscriptions-checkout.php:508
msgid "Purchasing a subscription product requires an account. Please go to the %1$sMy Account%2$s page to login or contact us if you need assistance."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:164
msgid "Sign Up Fee Discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:165
msgid "Sign Up Fee % Discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:166
msgid "Recurring Product Discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:167
msgid "Recurring Product % Discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:466
msgid "Sorry, this coupon is only valid for an initial payment and the cart does not require an initial payment."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:472
msgid "Sorry, this coupon is only valid for new subscriptions."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:477
msgid "Sorry, this coupon is only valid for subscription products."
msgstr ""

#. translators: 1$: coupon code that is being removed
#: includes/class-wc-subscriptions-coupon.php:483
msgid "Sorry, the \"%1$s\" coupon is only valid for renewals."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:488
msgid "Sorry, this coupon is only valid for subscription products with a sign-up fee."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:514
msgid "Sorry, recurring coupons can only be applied to subscriptions or subscription orders."
msgstr ""

#. translators: placeholder is coupon code
#: includes/class-wc-subscriptions-coupon.php:518
msgid "Sorry, \"%s\" can only be applied to subscription parent orders which contain a product with signup fees."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:521
msgid "Sorry, only recurring coupons can be applied to subscriptions."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:702
msgid "Renewal % discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:703
msgid "Renewal product discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:704
msgid "Renewal cart discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:705
msgid "Initial payment discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:722
msgid "Renewal Discount"
msgstr ""

#. translators: %1$s is the price of the product. %2$s is the separator used e.g "every" or "/", %3$d is the length, %4$s is week, month, year
#: includes/class-wc-subscriptions-coupon.php:725
#: build/index.js:3
msgid "Discount"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:941
msgid "Sorry, it seems there are no available payment methods which support the recurring coupon you are using. Please contact us if you require assistance or wish to make alternate arrangements."
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:956
msgid "Active for x payments"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:957
msgid "Unlimited payments"
msgstr ""

#: includes/class-wc-subscriptions-coupon.php:958
msgid "Coupon will be limited to the given number of payments. It will then be automatically removed from the subscription. \"Payments\" also includes the initial subscription payment."
msgstr ""

#. translators: %1$s is the coupon code, %2$d is the number of payment usages
#: includes/class-wc-subscriptions-coupon.php:1055
msgid "Limited use coupon \"%1$s\" removed from subscription. It has been used %2$d time."
msgid_plural "Limited use coupon \"%1$s\" removed from subscription. It has been used %2$d times."
msgstr[0] ""
msgstr[1] ""

#. translators: %d refers to the number of payments the coupon can be used for.
#: includes/class-wc-subscriptions-coupon.php:1090
msgid "Active for %d payment"
msgid_plural "Active for %d payments"
msgstr[0] ""
msgstr[1] ""

#: includes/class-wc-subscriptions-coupon.php:1094
msgid "Active for unlimited payments"
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:150
msgid "Subscription Product length."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:156
msgid "Subscription Product trial period."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:163
msgid "Subscription Product trial interval."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:169
msgid "Subscription Product signup fees."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:175
msgid "Subscription Product signup fees taxes."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:181
msgid "Indicates whether this product is being used to resubscribe the customer to an existing, expired subscription."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:190
msgid "Indicates whether this product a subscription update, downgrade, cross grade or none of the above."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:199
msgid "Synchronization data for the subscription."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:203
msgid "Synchronization day if subscription is annual."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:207
msgid "Synchronization month if subscription is annual."
msgstr ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:262
msgid "Shipment every %d year"
msgid_plural "Shipment every %d years"
msgstr[0] ""
msgstr[1] ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:262
msgid "Yearly Shipment"
msgstr ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:266
msgid "Shipment every %d month"
msgid_plural "Shipment every %d months"
msgstr[0] ""
msgstr[1] ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:266
msgid "Monthly Shipment"
msgstr ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:270
msgid "Shipment every %d week"
msgid_plural "Shipment every %d weeks"
msgstr[0] ""
msgstr[1] ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:270
msgid "Weekly Shipment"
msgstr ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:274
msgid "Shipment every %d day"
msgid_plural "Shipment every %d days"
msgstr[0] ""
msgstr[1] ""

#. translators: %d subscription interval.
#: includes/class-wc-subscriptions-extend-store-endpoint.php:274
msgid "Daily Shipment"
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:402
msgid "Subscription key"
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:427
msgid "Subscription length."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:433
msgid "Cart total amounts provided using the smallest unit of the currency."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:439
msgid "Total price of items in the cart."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:445
msgid "Total tax on items in the cart."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:451
msgid "Total price of any applied fees."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:457
msgid "Total tax on fees."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:463
msgid "Total discount from applied coupons."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:469
msgid "Total tax removed due to discount from applied coupons."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:475
msgid "Total price of shipping. If shipping has not been calculated, a null response will be sent."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:481
msgid "Total tax on shipping. If shipping has not been calculated, a null response will be sent."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:487
msgid "Total price the customer will pay."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:493
msgid "Total tax applied to items and shipping."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:499
msgid "Lines of taxes applied to items and shipping."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:507
msgid "The name of the tax."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:513
msgid "The amount of tax charged."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:522
msgid "Currency code (in ISO format) for returned prices."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:528
msgid "Currency symbol for the currency which can be used to format returned prices."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:534
msgid "Currency minor unit (number of digits after the decimal separator) for returned prices."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:540
msgid "Decimal separator for the currency which can be used to format returned prices."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:546
msgid "Thousand separator for the currency which can be used to format returned prices."
msgstr ""

#: includes/class-wc-subscriptions-extend-store-endpoint.php:552
#: includes/class-wc-subscriptions-extend-store-endpoint.php:558
msgid "Price prefix for the currency which can be used to format returned prices."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:87
#: includes/class-wc-subscriptions-manager.php:1918
#: includes/class-wc-subscriptions-manager.php:1936
msgctxt "used in order note as reason for why subscription status changed"
msgid "Subscription renewal payment due:"
msgstr ""

#. translators: placeholder is an order note.
#: includes/class-wc-subscriptions-manager.php:124
msgid "Error: Unable to create renewal order with note \"%s\""
msgstr ""

#: includes/class-wc-subscriptions-manager.php:134
msgid "Manual renewal order awaiting customer payment."
msgstr ""

#. translators: placeholder is a subscription ID.
#. translators: %d: subscription ID.
#: includes/class-wc-subscriptions-manager.php:167
#: includes/gateways/class-wc-subscriptions-payment-gateways.php:246
msgid "Subscription doesn't exist in scheduled action: %d"
msgstr ""

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:306
msgid "Failed to activate subscription status for order #%1$s: %2$s"
msgstr ""

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:334
msgid "Failed to update subscription status after order #%1$s was put on-hold: %2$s"
msgstr ""

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:362
msgid "Failed to cancel subscription after order #%1$s was cancelled: %2$s"
msgstr ""

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:390
msgid "Failed to set subscription as expired for order #%1$s: %2$s"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:416
msgid "Subscription sign up failed."
msgstr ""

#. translators: $1: order number, $2: error message
#: includes/class-wc-subscriptions-manager.php:426
msgid "Failed to process failed payment on subscription for order #%1$s: %2$s"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:500
msgid "Error: Unable to create subscription. Please try again."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:522
msgid "Error: Unable to add product to created subscription. Please try again."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:567
msgid "Pending subscription created."
msgstr ""

#. Translators: 1: The subscription ID number. 2: The current user's username.
#: includes/class-wc-subscriptions-manager.php:914
msgid "The related subscription #%1$s has been deleted after the customer was deleted by %2$s."
msgstr ""

#. Translators: Placeholder is the subscription ID number.
#: includes/class-wc-subscriptions-manager.php:917
msgid "The related subscription #%s has been deleted after the customer was deleted."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1063
#: wcs-functions.php:233
msgctxt "Subscription status"
msgid "Active"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1066
#: wcs-functions.php:235
msgctxt "Subscription status"
msgid "Cancelled"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1069
#: wcs-functions.php:237
msgctxt "Subscription status"
msgid "Expired"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1072
#: wcs-functions.php:232
msgctxt "Subscription status"
msgid "Pending"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1075
msgctxt "Subscription status"
msgid "Failed"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1079
msgctxt "Subscription status"
msgid "On-hold"
msgstr ""

#. translators: 1$: month number (e.g. "01"), 2$: month abbreviation (e.g. "Jan")
#: includes/class-wc-subscriptions-manager.php:1831
msgctxt "used in a select box"
msgid "%1$s-%2$s"
msgstr ""

#. translators: all fields are full html nodes: 1$: month input, 2$: day input, 3$: year input, 4$: hour input, 5$: minute input. Change the order if you'd like
#: includes/class-wc-subscriptions-manager.php:1844
msgid "%1$s%2$s, %3$s @ %4$s : %5$s"
msgstr ""

#. translators: all fields are full html nodes: 1$: month input, 2$: day input, 3$: year input. Change the order if you'd like
#: includes/class-wc-subscriptions-manager.php:1848
msgid "%1$s%2$s, %3$s"
msgstr ""

#: includes/class-wc-subscriptions-manager.php:1853
msgid "Change"
msgstr ""

#. translators: placeholder is subscription ID
#: includes/class-wc-subscriptions-manager.php:2186
msgid "Failed sign-up for subscription %s."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:2277
msgid "Invalid security token, please reload the page and try again."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:2281
msgid "Only store managers can edit payment dates."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:2285
msgid "Please enter all date fields."
msgstr ""

#: includes/class-wc-subscriptions-manager.php:2310
msgid "Date Changed"
msgstr ""

#: includes/class-wc-subscriptions-order.php:379
msgid "Your subscription will be activated when payment clears."
msgid_plural "Your subscriptions will be activated when payment clears."
msgstr[0] ""
msgstr[1] ""

#. translators: placeholders are opening and closing link tags
#: includes/class-wc-subscriptions-order.php:386
msgid "View the status of your subscription in %1$syour account%2$s."
msgid_plural "View the status of your subscriptions in %1$syour account%2$s."
msgstr[0] ""
msgstr[1] ""

#: includes/class-wc-subscriptions-order.php:449
msgid "Subscription Relationship"
msgstr ""

#: includes/class-wc-subscriptions-order.php:469
msgid "Renewal Order"
msgstr ""

#: includes/class-wc-subscriptions-order.php:471
msgid "Resubscribe Order"
msgstr ""

#: includes/class-wc-subscriptions-order.php:473
msgid "Parent Order"
msgstr ""

#: includes/class-wc-subscriptions-order.php:517
msgid "Payment completed on order after subscription was cancelled."
msgstr ""

#: includes/class-wc-subscriptions-order.php:745
msgid "All orders types"
msgstr ""

#: includes/class-wc-subscriptions-order.php:748
msgctxt "An order type"
msgid "Original"
msgstr ""

#: includes/class-wc-subscriptions-order.php:749
msgctxt "An order type"
msgid "Subscription Parent"
msgstr ""

#: includes/class-wc-subscriptions-order.php:750
msgctxt "An order type"
msgid "Subscription Renewal"
msgstr ""

#: includes/class-wc-subscriptions-order.php:751
msgctxt "An order type"
msgid "Subscription Resubscribe"
msgstr ""

#: includes/class-wc-subscriptions-order.php:752
msgctxt "An order type"
msgid "Subscription Switch"
msgstr ""

#: includes/class-wc-subscriptions-order.php:753
msgctxt "An order type"
msgid "Non-subscription"
msgstr ""

#. translators: $1: opening link tag, $2: order number, $3: closing link tag
#: includes/class-wc-subscriptions-order.php:1056
msgid "Subscription cancelled for refunded order %1$s#%2$s%3$s."
msgstr ""

#. translators: %1$s refers to the price. This string is meant to prefix another string below, e.g. "$5 now, and $5 on March 15th each year"
#: includes/class-wc-subscriptions-product.php:291
msgid "%1$s now, and "
msgstr ""

#. translators: 1$: recurring amount string, 2$: day of the week (e.g. "$10 every Wednesday").
#. translators: 1$: recurring amount string, 2$: day of the week (e.g. "$10 every Wednesday")
#. translators: %1$: recurring amount (e.g. "$15"), %2$: subscription period (e.g. "month") (e.g. "$15 every 2nd month")
#: includes/class-wc-subscriptions-product.php:300
#: includes/wcs-formatting-functions.php:116
#: includes/wcs-formatting-functions.php:201
msgid "%1$s every %2$s"
msgstr ""

#. translators: 1$: recurring amount string, 2$: period, 3$: day of the week (e.g. "$10 every 2nd week on Wednesday").
#. translators: 1$: recurring amount string, 2$: period, 3$: day of the week (e.g. "$10 every 2nd week on Wednesday")
#: includes/class-wc-subscriptions-product.php:304
#: includes/wcs-formatting-functions.php:125
msgid "%1$s every %2$s on %3$s"
msgstr ""

#. translators: placeholder is recurring amount.
#. translators: placeholder is recurring amount
#: includes/class-wc-subscriptions-product.php:315
#: includes/wcs-formatting-functions.php:143
msgid "%s on the last day of each month"
msgstr ""

#. translators: 1$: recurring amount, 2$: day of the month (e.g. "23rd") (e.g. "$5 every 23rd of each month").
#. translators: 1$: recurring amount, 2$: day of the month (e.g. "23rd") (e.g. "$5 every 23rd of each month")
#: includes/class-wc-subscriptions-product.php:319
#: includes/wcs-formatting-functions.php:146
msgid "%1$s on the %2$s of each month"
msgstr ""

#. translators: 1$: recurring amount, 2$: interval (e.g. "3rd") (e.g. "$10 on the last day of every 3rd month").
#. translators: 1$: recurring amount, 2$: interval (e.g. "3rd") (e.g. "$10 on the last day of every 3rd month")
#: includes/class-wc-subscriptions-product.php:328
#: includes/wcs-formatting-functions.php:162
msgid "%1$s on the last day of every %2$s month"
msgstr ""

#. translators: 1$: <price> on the, 2$: <date> day of every, 3$: <interval> month (e.g. "$10 on the 23rd day of every 2nd month").
#. translators: 1$: recurring amount, 2$: day of the month (e.g. "23rd") (e.g. "$5 every 23rd of each month")
#: includes/class-wc-subscriptions-product.php:335
#: includes/wcs-formatting-functions.php:165
msgid "%1$s on the %2$s day of every %3$s month"
msgstr ""

#. translators: 1$: <price> on, 2$: <date>, 3$: <month> each year (e.g. "$15 on March 15th each year").
#. translators: 1$: recurring amount, 2$: month (e.g. "March"), 3$: day of the month (e.g. "23rd") (e.g. "$15 on March 15th every 3rd year")
#: includes/class-wc-subscriptions-product.php:347
#: includes/wcs-formatting-functions.php:178
msgid "%1$s on %2$s %3$s each year"
msgstr ""

#. translators: 1$: recurring amount, 2$: month (e.g. "March"), 3$: day of the month (e.g. "23rd").
#. translators: 1$: recurring amount, 2$: month (e.g. "March"), 3$: day of the month (e.g. "23rd") (e.g. "$15 on March 15th every 3rd year")
#: includes/class-wc-subscriptions-product.php:355
#: includes/wcs-formatting-functions.php:187
msgid "%1$s on %2$s %3$s every %4$s year"
msgstr ""

#. translators: 1$: recurring amount, 2$: subscription period (e.g. "month" or "3 months") (e.g. "$15 / month" or "$15 every 2nd month").
#. translators: 1$: recurring amount, 2$: subscription period (e.g. "month" or "3 months") (e.g. "$15 / month" or "$15 every 2nd month")
#: includes/class-wc-subscriptions-product.php:367
#: includes/wcs-formatting-functions.php:198
msgid "%1$s / %2$s"
msgid_plural "%1$s every %2$s"
msgstr[0] ""
msgstr[1] ""

#. translators: billing period (e.g. "every week").
#: includes/class-wc-subscriptions-product.php:377
msgid "every %s"
msgstr ""

#. translators: 1$: subscription string (e.g. "$10 up front then $5 on March 23rd every 3rd year"), 2$: length (e.g. "4 years").
#. translators: 1$: subscription string (e.g. "$10 up front then $5 on March 23rd every 3rd year"), 2$: length (e.g. "4 years")
#: includes/class-wc-subscriptions-product.php:387
#: includes/wcs-formatting-functions.php:209
msgid "%1$s for %2$s"
msgstr ""

#. translators: 1$: subscription string (e.g. "$15 on March 15th every 3 years for 6 years"), 2$: trial length (e.g.: "with 4 months free trial").
#: includes/class-wc-subscriptions-product.php:393
msgid "%1$s with %2$s free trial"
msgstr ""

#. translators: 1$: subscription string (e.g. "$15 on March 15th every 3 years for 6 years with 2 months free trial"), 2$: signup fee price (e.g. "and a $30 sign-up fee").
#: includes/class-wc-subscriptions-product.php:398
msgid "%1$s and a %2$s sign-up fee"
msgstr ""

#: includes/class-wc-subscriptions-product.php:969
msgid "This variation can not be removed because it is associated with active subscriptions. To remove this variation, please cancel and delete the subscriptions for it."
msgstr ""

#. translators: placeholder is order ID
#: includes/class-wc-subscriptions-renewal-order.php:161
msgid "Order %s created to record renewal."
msgstr ""

#: includes/class-wc-subscriptions-renewal-order.php:181
msgid "Subscription renewal orders cannot be cancelled."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:195
msgid "You have a subscription to this product. Choosing a new subscription will replace your existing subscription."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:197
msgid "Choose a new subscription."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:237
#: includes/class-wc-subscriptions-switcher.php:1211
msgid "Your cart contained an invalid subscription switch request. It has been removed."
msgid_plural "Your cart contained invalid subscription switch requests. They have been removed."
msgstr[0] ""
msgstr[1] ""

#: includes/class-wc-subscriptions-switcher.php:279
msgid "You have already subscribed to this product and it is limited to one per customer. You can not purchase the product again."
msgstr ""

#. translators: 1$: is the "You have already subscribed to this product" notice, 2$-4$: opening/closing link tags, 3$: an order number
#: includes/class-wc-subscriptions-switcher.php:288
msgid "%1$s Complete payment on %2$sOrder %3$s%4$s to be able to change your subscription."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:383
msgid "Switching"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/class-wc-subscriptions-switcher.php:386
msgid "Allow subscribers to switch (upgrade or downgrade) between different subscriptions. %1$sLearn more%2$s."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:396
msgid "Prorate Recurring Payment"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:397
msgid "When switching to a subscription with a different recurring payment or billing period, should the price paid for the existing billing period be prorated when switching to the new subscription?"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:404
#: includes/class-wc-subscriptions-switcher.php:438
msgctxt "when to allow a setting"
msgid "Never"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:405
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades of Virtual Subscription Products Only"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:406
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades of All Subscription Products"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:407
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades & Downgrades of Virtual Subscription Products Only"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:408
msgctxt "when to prorate recurring fee when switching"
msgid "For Upgrades & Downgrades of All Subscription Products"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:414
msgid "Prorate Sign up Fee"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:415
msgid "When switching to a subscription with a sign up fee, you can require the customer pay only the gap between the existing subscription's sign up fee and the new subscription's sign up fee (if any)."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:422
msgctxt "when to prorate signup fee when switching"
msgid "Never (do not charge a sign up fee)"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:423
msgctxt "when to prorate signup fee when switching"
msgid "Never (charge the full sign up fee)"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:424
msgctxt "when to prorate signup fee when switching"
msgid "Always"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:430
msgid "Prorate Subscription Length"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:431
msgid "When switching to a subscription with a length, you can take into account the payments already completed by the customer when determining how many payments the subscriber needs to make for the new subscription."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:439
#: includes/class-wc-subscriptions-synchroniser.php:235
msgctxt "when to prorate first payment / subscription length"
msgid "For Virtual Subscription Products Only"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:440
#: includes/class-wc-subscriptions-synchroniser.php:236
msgctxt "when to prorate first payment / subscription length"
msgid "For All Subscription Products"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:446
msgid "Switch Button Text"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:447
msgid "Customise the text displayed on the button next to the subscription on the subscriber's account page. The default is \"Switch Subscription\", but you may wish to change this to \"Upgrade\" or \"Change Subscription\"."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:451
#: includes/class-wc-subscriptions-switcher.php:549
#: includes/class-wc-subscriptions-switcher.php:2592
msgid "Upgrade or Downgrade"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:486
msgid "Allow Switching"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:493
msgctxt "when to allow switching"
msgid "Between Subscription Variations"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:497
msgctxt "when to allow switching"
msgid "Between Grouped Subscriptions"
msgstr ""

#. translators: %s: order number.
#: includes/class-wc-subscriptions-switcher.php:1134
msgid "Switch order cancelled due to a new switch order being created #%s."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1339
msgid "You can only switch to a subscription product."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1345
msgid "We can not find your old subscription item."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1367
msgid "You can not switch to the same subscription."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1414
msgid "You can not switch this subscription. It appears you do not own the subscription."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1455
msgid "There was an error locating the switch details."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1817
msgctxt "a switch type"
msgid "Downgrade"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1820
msgctxt "a switch type"
msgid "Upgrade"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1823
msgctxt "a switch type"
msgid "Crossgrade"
msgstr ""

#. translators: %1: product subtotal, %2: HTML span tag, %3: direction (upgrade, downgrade, crossgrade), %4: closing HTML span tag
#: includes/class-wc-subscriptions-switcher.php:1828
msgctxt "product subtotal string"
msgid "%1$s %2$s(%3$s)%4$s"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1945
msgid "The original subscription item being switched cannot be found."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:1947
msgid "The item on the switch order cannot be found."
msgstr ""

#. translators: 1$: old item, 2$: new item when switching
#: includes/class-wc-subscriptions-switcher.php:1958
msgctxt "used in order notes"
msgid "Customer switched from: %1$s to %2$s."
msgstr ""

#. translators: %s: new item name.
#: includes/class-wc-subscriptions-switcher.php:1961
msgctxt "used in order notes"
msgid "Customer added %s."
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:2316
#: includes/class-wc-subscriptions-switcher.php:2869
msgid "Switch Order"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:2331
#: includes/class-wc-subscriptions-switcher.php:2884
msgid "Switched Subscription"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:2549
msgctxt "add to cart button text while switching a subscription"
msgid "Switch subscription"
msgstr ""

#: includes/class-wc-subscriptions-switcher.php:2733
#: wcs-functions.php:236
msgctxt "Subscription status"
msgid "Switched"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:48
msgid "Synchronise renewals"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:49
msgid "Align the payment date for all customers who purchase this subscription to a specific day of the week or month."
msgstr ""

#. translators: placeholder is a year (e.g. "2016")
#: includes/class-wc-subscriptions-synchroniser.php:51
msgctxt "used in subscription product edit screen"
msgid "Align the payment date for this subscription to a specific day of the year. If the date has already taken place this year, the first payment will be processed in %s. Set the day to 0 to disable payment syncing for this product."
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:210
msgid "Synchronisation"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/class-wc-subscriptions-synchroniser.php:213
msgctxt "used in the general subscription options page"
msgid "Align subscription renewal to a specific day of the week, month or year. For example, the first day of the month. %1$sLearn more%2$s."
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:219
msgid "Align Subscription Renewal Day"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:226
msgid "Prorate First Renewal"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:227
msgid "If a subscription is synchronised to a specific day of the week, month or year, charge a prorated amount for the subscription at the time of sign up."
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:233
msgctxt "when to prorate first payment / subscription length"
msgid "Never (do not charge any recurring amount)"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:234
msgctxt "when to prorate first payment / subscription length"
msgid "Never (charge the full recurring amount at sign-up)"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:242
msgid "Sign-up grace period"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:243
msgctxt "there's a number immediately in front of this text"
msgid "days prior to Renewal Day"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:247
msgid "Subscriptions created within this many days prior to the Renewal Day will not be charged at sign-up. Set to zero for all new Subscriptions to be charged the full recurring amount. Must be a positive number."
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:311
msgid "Month for Synchronisation"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:319
#: templates/admin/deprecated/html-variation-synchronisation.php:36
#: templates/admin/html-variation-synchronisation.php:42
msgctxt "input field placeholder for day field for annual subscriptions"
msgid "Day"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:750
#: includes/class-wc-subscriptions-synchroniser.php:767
msgid "Do not synchronise"
msgstr ""

#. translators: placeholder is a day of the week
#: includes/class-wc-subscriptions-synchroniser.php:775
msgid "%s each week"
msgstr ""

#. translators: placeholder is a number of day with language specific suffix applied (e.g. "1st", "3rd", "5th", etc...)
#: includes/class-wc-subscriptions-synchroniser.php:781
msgid "%s day of the month"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:783
msgid "Last day of the month"
msgstr ""

#: includes/class-wc-subscriptions-synchroniser.php:831
msgid "Today!"
msgstr ""

#. translators: placeholder is a date
#: includes/class-wc-subscriptions-synchroniser.php:838
msgid "First payment prorated. Next payment: %s"
msgstr ""

#. translators: placeholder is a date
#: includes/class-wc-subscriptions-synchroniser.php:841
msgid "First payment: %s"
msgstr ""

#: includes/class-wcs-auth.php:39
msgid "View subscriptions"
msgstr ""

#: includes/class-wcs-auth.php:42
msgid "Create subscriptions"
msgstr ""

#: includes/class-wcs-auth.php:45
msgid "View and manage subscriptions"
msgstr ""

#: includes/class-wcs-cached-data-manager.php:79
msgid "Related order caching is now handled by %1$s."
msgstr ""

#: includes/class-wcs-cached-data-manager.php:86
msgid "Customer subscription caching is now handled by %1$s."
msgstr ""

#: includes/class-wcs-cached-data-manager.php:110
#: includes/class-wcs-cached-data-manager.php:240
msgid "Customer subscription caching is now handled by %1$s and %2$s."
msgstr ""

#: includes/class-wcs-cached-data-manager.php:127
msgid "new related order methods in WCS_Related_Order_Store"
msgstr ""

#: includes/class-wcs-cached-data-manager.php:225
msgid "Weekly"
msgstr ""

#: includes/class-wcs-cart-initial-payment.php:65
#: includes/class-wcs-cart-renewal.php:199
msgid "That doesn't appear to be your order."
msgstr ""

#: includes/class-wcs-cart-renewal.php:214
msgid "This order can no longer be paid because the corresponding subscription does not require payment at this time."
msgstr ""

#: includes/class-wcs-cart-renewal.php:235
msgid "Complete checkout to renew your subscription."
msgstr ""

#. translators: placeholder is an item name
#: includes/class-wcs-cart-renewal.php:318
msgid "The %s product has been deleted and can no longer be renewed. Please choose a new product or contact us for assistance."
msgstr ""

#. translators: %s is subscription's number
#: includes/class-wcs-cart-renewal.php:353
msgid "Subscription #%s has not been added to the cart."
msgstr ""

#. translators: %s is order's number
#: includes/class-wcs-cart-renewal.php:356
msgid "Order #%s has not been added to the cart."
msgstr ""

#: includes/class-wcs-cart-renewal.php:395
msgid "We couldn't find the original subscription for an item in your cart. The item was removed."
msgid_plural "We couldn't find the original subscriptions for items in your cart. The items were removed."
msgstr[0] ""
msgstr[1] ""

#: includes/class-wcs-cart-renewal.php:402
msgid "We couldn't find the original renewal order for an item in your cart. The item was removed."
msgid_plural "We couldn't find the original renewal orders for items in your cart. The items were removed."
msgstr[0] ""
msgstr[1] ""

#: includes/class-wcs-cart-renewal.php:682
msgid "All linked subscription items have been removed from the cart."
msgstr ""

#: includes/class-wcs-cart-renewal.php:711
msgctxt "Used in WooCommerce by removed item notification: \"_All linked subscription items were_ removed. Undo?\" Filter for item title."
msgid "All linked subscription items were"
msgstr ""

#: includes/class-wcs-cart-renewal.php:1475
msgctxt "The place order button text while renewing a subscription"
msgid "Renew subscription"
msgstr ""

#: includes/class-wcs-cart-resubscribe.php:70
msgid "There was an error with your request to resubscribe. Please try again."
msgstr ""

#: includes/class-wcs-cart-resubscribe.php:74
#: includes/early-renewal/class-wcs-cart-early-renewal.php:92
msgid "That subscription does not exist. Has it been deleted?"
msgstr ""

#: includes/class-wcs-cart-resubscribe.php:82
msgid "You can not resubscribe to that subscription. Please contact us if you need assistance."
msgstr ""

#: includes/class-wcs-cart-resubscribe.php:91
#: includes/class-wcs-cart-resubscribe.php:119
msgid "Complete checkout to resubscribe."
msgstr ""

#. translators: %s: order number.
#: includes/class-wcs-cart-resubscribe.php:320
msgid "Customer resubscribed in order #%s"
msgstr ""

#: includes/class-wcs-cart-resubscribe.php:338
msgctxt "The place order button text while resubscribing to a subscription"
msgid "Resubscribe"
msgstr ""

#: includes/class-wcs-cart-switch.php:179
msgctxt "The place order button text while switching a subscription"
msgid "Switch subscription"
msgstr ""

#: includes/class-wcs-change-payment-method-admin.php:122
msgid "Please choose a valid payment gateway to change to."
msgstr ""

#: includes/class-wcs-failed-scheduled-action-manager.php:158
msgid "Ignore this error"
msgstr ""

#: includes/class-wcs-failed-scheduled-action-manager.php:163
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:112
msgid "Learn more"
msgstr ""

#: includes/class-wcs-limiter.php:46
msgid "Limit subscription"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/class-wcs-limiter.php:48
msgid "Only allow a customer to have one subscription to this product. %1$sLearn more%2$s."
msgstr ""

#: includes/class-wcs-limiter.php:50
msgid "Do not limit"
msgstr ""

#: includes/class-wcs-limiter.php:51
msgid "Limit to one active subscription"
msgstr ""

#: includes/class-wcs-limiter.php:52
msgid "Limit to one of any status"
msgstr ""

#: includes/class-wcs-my-account-auto-renew-toggle.php:153
msgid "Auto Renewal Toggle"
msgstr ""

#: includes/class-wcs-my-account-auto-renew-toggle.php:154
msgid "Display the auto renewal toggle"
msgstr ""

#: includes/class-wcs-my-account-auto-renew-toggle.php:155
msgid "Allow customers to turn on and off automatic renewals from their View Subscription page."
msgstr ""

#: includes/class-wcs-my-account-payment-methods.php:110
msgid "The deleted payment method was used for automatic subscription payments, we couldn't find an alternative token payment method token to change your subscriptions to."
msgstr ""

#. translators: 1: deleted token, 2: new token.
#: includes/class-wcs-my-account-payment-methods.php:128
msgctxt "used in subscription note"
msgid "Payment method meta updated after customer deleted a token from their My Account page. Payment meta changed from %1$s to %2$s"
msgstr ""

#. translators: $1: the token/credit card label, 2$-3$: opening and closing strong and link tags
#: includes/class-wcs-my-account-payment-methods.php:133
msgid "The deleted payment method was used for automatic subscription payments. To avoid failed renewal payments in future the subscriptions using this payment method have been updated to use your %1$s. To change the payment method of individual subscriptions go to your %2$sMy Account > Subscriptions%3$s page."
msgstr ""

#. translators: 1: token display name, 2: opening link tag, 4: closing link tag, 3: opening link tag.
#: includes/class-wcs-my-account-payment-methods.php:188
msgid "Would you like to update your subscriptions to use this new payment method - %1$s?%2$sYes%4$s | %3$sNo%4$s"
msgstr ""

#. translators: 1: previous token, 2: new token.
#: includes/class-wcs-my-account-payment-methods.php:229
msgctxt "used in subscription note"
msgid "Payment method meta updated after customer changed their default token and opted to update their subscriptions. Payment meta changed from %1$s to %2$s"
msgstr ""

#. translators: 1$-2$: opening and closing <strong> tags.
#: includes/class-wcs-permalink-manager.php:91
msgid "Error saving Subscriptions endpoints: %1$sSubscriptions%2$s, %1$sView subscription%2$s and %1$sSubscription payment method%2$s cannot be the same. The changes have been reverted."
msgstr ""

#. translators: %s: invalid type of update argument.
#: includes/class-wcs-post-meta-cache-manager.php:199
msgid "Invalid update type: %s. Post update types supported are \"add\" or \"delete\". Updates are done on post meta directly."
msgstr ""

#. translators: placeholder is a page number.
#: includes/class-wcs-query.php:106
msgid "Subscriptions (page %d)"
msgstr ""

#: includes/class-wcs-query.php:131
msgid "My Subscription"
msgstr ""

#: includes/class-wcs-query.php:288
msgid "Endpoint for the My Account &rarr; Subscriptions page"
msgstr ""

#: includes/class-wcs-query.php:296
msgid "View subscription"
msgstr ""

#: includes/class-wcs-query.php:297
msgid "Endpoint for the My Account &rarr; View Subscription page"
msgstr ""

#: includes/class-wcs-query.php:305
msgid "Subscription payment method"
msgstr ""

#: includes/class-wcs-query.php:306
msgid "Endpoint for the My Account &rarr; Change Subscription Payment Method page"
msgstr ""

#. translators: %d: subscription ID.
#: includes/class-wcs-remove-item.php:79
msgctxt "hash before subscription ID"
msgid "Subscription #%d does not exist."
msgstr ""

#. translators: 1$: product name, 2$: product id
#: includes/class-wcs-remove-item.php:114
msgctxt "used in order note"
msgid "Customer added \"%1$s\" (Product ID: #%2$d) via the My Account page."
msgstr ""

#: includes/class-wcs-remove-item.php:119
msgid "Your request to undo your previous action was unsuccessful."
msgstr ""

#. translators: 1$: product name, 2$: product id
#: includes/class-wcs-remove-item.php:137
msgctxt "used in order note"
msgid "Customer removed \"%1$s\" (Product ID: #%2$d) via the My Account page."
msgstr ""

#. translators: placeholders are 1$: item name, and, 2$: opening and, 3$: closing link tags
#: includes/class-wcs-remove-item.php:140
msgid "You have successfully removed \"%1$s\" from your subscription. %2$sUndo?%3$s"
msgstr ""

#: includes/class-wcs-remove-item.php:176
#: includes/class-wcs-user-change-status-handler.php:107
msgid "Security error. Please contact us if you need assistance."
msgstr ""

#: includes/class-wcs-remove-item.php:180
msgid "You cannot modify a subscription that does not belong to you."
msgstr ""

#: includes/class-wcs-remove-item.php:184
msgid "You cannot remove an item that does not exist. "
msgstr ""

#: includes/class-wcs-remove-item.php:188
msgid "The item was not removed because this Subscription's payment method does not support removing an item."
msgstr ""

#: includes/class-wcs-retry-manager.php:120
msgctxt "table heading"
msgid "Renewal Payment Retry"
msgstr ""

#: includes/class-wcs-retry-manager.php:230
msgctxt "used in order note as reason for why status changed"
msgid "Retry rule applied:"
msgstr ""

#: includes/class-wcs-retry-manager.php:268
msgctxt "used in order note as reason for why status changed"
msgid "Retry rule reapplied:"
msgstr ""

#: includes/class-wcs-retry-manager.php:324
msgctxt "used in order note as reason for why order status changed"
msgid "Subscription renewal payment retry:"
msgstr ""

#: includes/class-wcs-retry-manager.php:328
msgctxt "used in order note as reason for why subscription status changed"
msgid "Subscription renewal payment retry:"
msgstr ""

#: includes/class-wcs-retry-manager.php:344
msgid "Payment retry attempted on renewal order with multiple related subscriptions with no payment method in common."
msgstr ""

#. translators: 1-2: opening/closing <a> tags - linked to staging site, 3: link to live site.
#: includes/class-wcs-staging.php:40
msgid "Payment processing skipped - renewal order created on %1$sstaging site%2$s under staging site lock. Live site is at %3$s"
msgstr ""

#: includes/class-wcs-staging.php:83
msgid "Subscription locked to Manual Renewal while the store is in staging mode. Payment method changes will take effect in live mode."
msgstr ""

#. translators: placeholder is a payment method title.
#: includes/class-wcs-staging.php:97
msgid "Subscription locked to Manual Renewal while the store is in staging mode. Live payment method: %s"
msgstr ""

#. translators: placeholder is a switch type.
#: includes/class-wcs-switch-cart-item.php:309
msgid "Invalid switch type \"%s\". Switch must be one of: \"upgrade\", \"downgrade\" or \"crossgrade\"."
msgstr ""

#: includes/class-wcs-switch-totals-calculator.php:196
msgid "Your cart contained an invalid subscription switch request. It has been removed from your cart."
msgstr ""

#: includes/class-wcs-template-loader.php:35
msgid "My Account"
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:57
msgctxt "order note left on subscription after user action"
msgid "Subscription reactivated by the subscriber from their account page."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:58
msgctxt "Notice displayed to user confirming their action."
msgid "Your subscription has been reactivated."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:61
msgid "You can not reactivate that subscription until paying to renew it. Please contact us if you need assistance."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:67
msgctxt "order note left on subscription after user action"
msgid "Subscription put on hold by the subscriber from their account page."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:68
msgctxt "Notice displayed to user confirming their action."
msgid "Your subscription has been put on hold."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:71
msgid "You can not suspend that subscription - the suspension limit has been reached. Please contact us if you need assistance."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:76
msgctxt "order note left on subscription after user action"
msgid "Subscription cancelled by the subscriber from their account page."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:77
msgctxt "Notice displayed to user confirming their action."
msgid "Your subscription has been cancelled."
msgstr ""

#: includes/class-wcs-user-change-status-handler.php:103
msgid "That subscription does not exist. Please contact us if you need assistance."
msgstr ""

#. translators: placeholder is subscription's new status, translated
#: includes/class-wcs-user-change-status-handler.php:116
msgid "That subscription can not be changed to %s. Please contact us if you need assistance."
msgstr ""

#: includes/class-wcs-webhooks.php:110
msgid " Subscription created"
msgstr ""

#: includes/class-wcs-webhooks.php:111
msgid " Subscription updated"
msgstr ""

#: includes/class-wcs-webhooks.php:112
msgid " Subscription deleted"
msgstr ""

#: includes/class-wcs-webhooks.php:113
msgid " Subscription switched"
msgstr ""

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:56
msgid "Generate Customer Subscription Cache"
msgstr ""

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:56
msgid "This will generate the persistent cache for linking users with subscriptions. The caches will be generated overtime in the background (via Action Scheduler)."
msgstr ""

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:57
msgid "Delete Customer Subscription Cache"
msgstr ""

#: includes/data-stores/class-wcs-customer-store-cached-cpt.php:57
msgid "This will clear the persistent cache of all of subscriptions stored against users in your store. Expect slower performance of checkout, renewal and other subscription related functions after taking this action. The caches will be regenerated overtime as queries to find a given user's subscriptions are run."
msgstr ""

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:69
msgid "Generate Related Order Cache"
msgstr ""

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:69
msgid "This will generate the persistent cache of all renewal, switch, resubscribe and other order types for all subscriptions in your store. The caches will be generated overtime in the background (via Action Scheduler)."
msgstr ""

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:70
msgid "Delete Related Order Cache"
msgstr ""

#: includes/data-stores/class-wcs-related-order-store-cached-cpt.php:70
msgid "This will clear the persistent cache of all renewal, switch, resubscribe and other order types for all subscriptions in your store. Expect slower performance of checkout, renewal and other subscription related functions after taking this action. The caches will be regenerated overtime as related order queries are run."
msgstr ""

#: includes/early-renewal/class-wcs-cart-early-renewal.php:72
msgid "Renew now"
msgstr ""

#: includes/early-renewal/class-wcs-cart-early-renewal.php:100
msgid "You can not renew this subscription early. Please contact us if you need assistance."
msgstr ""

#: includes/early-renewal/class-wcs-cart-early-renewal.php:106
msgid "Complete checkout to renew now."
msgstr ""

#: includes/early-renewal/class-wcs-cart-early-renewal.php:151
#: includes/early-renewal/class-wcs-cart-early-renewal.php:199
msgctxt "used in order note as reason for why subscription status changed"
msgid "Customer requested to renew early:"
msgstr ""

#. translators: %s: order ID (linked to details page).
#: includes/early-renewal/class-wcs-cart-early-renewal.php:313
msgid "Order %s created to record early renewal."
msgstr ""

#: includes/early-renewal/class-wcs-cart-early-renewal.php:368
msgid "Cancel"
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-manager.php:53
msgid "Early Renewal"
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-manager.php:54
msgid "Accept Early Renewal Payments"
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-manager.php:55
msgid "With early renewals enabled, customers can renew their subscriptions before the next payment date."
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-manager.php:63
msgid "Accept Early Renewal Payments via a Modal"
msgstr ""

#. translators: 1-2: opening/closing <strong> tags , 2-3: opening/closing tags for a link to docs on early renewal.
#: includes/early-renewal/class-wcs-early-renewal-manager.php:66
msgid "Allow customers to bypass the checkout and renew their subscription early from their %1$sMy Account > View Subscription%2$s page. %3$sLearn more.%4$s"
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:39
msgid "Pay now"
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:56
msgid "Renew early"
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:113
msgid "We were unable to locate that subscription, please try again."
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:118
msgid "You can't renew the subscription at this time. Please try again."
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:128
msgid "We couldn't create a renewal order for your subscription, please try again."
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:145
msgid "Payment for the renewal order was unsuccessful with your payment method on file, please try again."
msgstr ""

#: includes/early-renewal/class-wcs-early-renewal-modal-handler.php:153
msgid "Your early renewal order was successful."
msgstr ""

#. translators: placeholder contains a link to the order's edit screen.
#: includes/early-renewal/wcs-early-renewal-functions.php:169
msgid "Customer successfully renewed early with order %s."
msgstr ""

#. translators: placeholder contains a link to the order's edit screen.
#: includes/early-renewal/wcs-early-renewal-functions.php:172
msgid "Failed to update subscription dates after customer renewed early with order %s."
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:26
msgid "Cancelled Subscription"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:27
msgid "Cancelled Subscription emails are sent when a customer's subscription is cancelled (either by a store manager, or the customer)."
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:29
msgid "Subscription Cancelled"
msgstr ""

#. translators: placeholder is {blogname}, a variable that will be substituted when email is sent out
#: includes/emails/class-wcs-email-cancelled-subscription.php:31
msgctxt "default email subject for cancelled emails sent to the admin"
msgid "[%s] Subscription Cancelled"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:143
#: includes/emails/class-wcs-email-customer-renewal-invoice.php:210
#: includes/emails/class-wcs-email-expired-subscription.php:141
#: includes/emails/class-wcs-email-on-hold-subscription.php:141
msgctxt "an email notification"
msgid "Enable/Disable"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:145
#: includes/emails/class-wcs-email-customer-renewal-invoice.php:212
#: includes/emails/class-wcs-email-expired-subscription.php:143
#: includes/emails/class-wcs-email-on-hold-subscription.php:143
msgid "Enable this email notification"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:149
#: includes/emails/class-wcs-email-expired-subscription.php:147
#: includes/emails/class-wcs-email-on-hold-subscription.php:147
msgctxt "of an email"
msgid "Recipient(s)"
msgstr ""

#. translators: placeholder is admin email
#: includes/emails/class-wcs-email-cancelled-subscription.php:152
#: includes/emails/class-wcs-email-expired-subscription.php:150
#: includes/emails/class-wcs-email-on-hold-subscription.php:150
msgid "Enter recipients (comma separated) for this email. Defaults to %s."
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:157
#: includes/emails/class-wcs-email-expired-subscription.php:155
#: includes/emails/class-wcs-email-on-hold-subscription.php:155
msgctxt "of an email"
msgid "Subject"
msgstr ""

#. translators: %s: default e-mail subject.
#: includes/emails/class-wcs-email-cancelled-subscription.php:160
#: includes/emails/class-wcs-email-expired-subscription.php:158
#: includes/emails/class-wcs-email-on-hold-subscription.php:158
msgid "This controls the email subject line. Leave blank to use the default subject: %s."
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:165
#: includes/emails/class-wcs-email-expired-subscription.php:163
#: includes/emails/class-wcs-email-on-hold-subscription.php:163
msgctxt "Name the setting that controls the main heading contained within the email notification"
msgid "Email Heading"
msgstr ""

#. translators: %s: default e-mail heading.
#: includes/emails/class-wcs-email-cancelled-subscription.php:168
msgid "This controls the main heading contained within the email notification. Leave blank to use the default heading: %s."
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:173
#: includes/emails/class-wcs-email-expired-subscription.php:171
#: includes/emails/class-wcs-email-on-hold-subscription.php:171
msgctxt "text, html or multipart"
msgid "Email type"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:175
#: includes/emails/class-wcs-email-expired-subscription.php:173
#: includes/emails/class-wcs-email-on-hold-subscription.php:173
msgid "Choose which format of email to send."
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:179
#: includes/emails/class-wcs-email-expired-subscription.php:177
#: includes/emails/class-wcs-email-on-hold-subscription.php:177
msgctxt "email type"
msgid "Plain text"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:180
#: includes/emails/class-wcs-email-expired-subscription.php:178
#: includes/emails/class-wcs-email-on-hold-subscription.php:178
msgctxt "email type"
msgid "HTML"
msgstr ""

#: includes/emails/class-wcs-email-cancelled-subscription.php:181
#: includes/emails/class-wcs-email-expired-subscription.php:179
#: includes/emails/class-wcs-email-on-hold-subscription.php:179
msgctxt "email type"
msgid "Multipart"
msgstr ""

#: includes/emails/class-wcs-email-completed-renewal-order.php:25
msgid "Completed Renewal Order"
msgstr ""

#: includes/emails/class-wcs-email-completed-renewal-order.php:26
msgid "Renewal order complete emails are sent to the customer when a subscription renewal order is marked complete and usually indicates that the item for that renewal period has been shipped."
msgstr ""

#: includes/emails/class-wcs-email-completed-renewal-order.php:29
msgctxt "Default email heading for email to customer on completed renewal order"
msgid "Your renewal order is complete"
msgstr ""

#. translators: $1: {blogname}, $2: {order_date}, variables that will be substituted when email is sent out
#: includes/emails/class-wcs-email-completed-renewal-order.php:31
msgctxt "Default email subject for email to customer on completed renewal order"
msgid "Your %1$s renewal order from %2$s is complete"
msgstr ""

#: includes/emails/class-wcs-email-completed-renewal-order.php:38
msgctxt "Default email heading for email with downloadable files in it"
msgid "Your subscription renewal order is complete - download your files"
msgstr ""

#. translators: $1: {blogname}, $2: {order_date}, variables will be substituted when email is sent out
#: includes/emails/class-wcs-email-completed-renewal-order.php:40
msgctxt "Default email subject for email with downloadable files in it"
msgid "Your %1$s subscription renewal order from %2$s is complete - download your files"
msgstr ""

#: includes/emails/class-wcs-email-completed-switch-order.php:26
msgid "Subscription Switch Complete"
msgstr ""

#: includes/emails/class-wcs-email-completed-switch-order.php:27
msgid "Subscription switch complete emails are sent to the customer when a subscription is switched successfully."
msgstr ""

#: includes/emails/class-wcs-email-completed-switch-order.php:30
msgid "Your subscription change is complete"
msgstr ""

#: includes/emails/class-wcs-email-completed-switch-order.php:31
msgid "Your {blogname} subscription change from {order_date} is complete"
msgstr ""

#: includes/emails/class-wcs-email-completed-switch-order.php:38
msgid "Your subscription change is complete - download your files"
msgstr ""

#: includes/emails/class-wcs-email-completed-switch-order.php:39
msgid "Your {blogname} subscription change from {order_date} is complete - download your files"
msgstr ""

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:23
msgid "On-hold Renewal Order"
msgstr ""

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:24
msgid "This is an order notification sent to customers containing order details after a renewal order is placed on-hold."
msgstr ""

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:25
msgid "Your {blogname} renewal order has been received!"
msgstr ""

#: includes/emails/class-wcs-email-customer-on-hold-renewal-order.php:26
msgid "Thank you for your renewal order"
msgstr ""

#: includes/emails/class-wcs-email-customer-payment-retry.php:24
msgid "Customer Payment Retry"
msgstr ""

#: includes/emails/class-wcs-email-customer-payment-retry.php:25
msgid "Sent to a customer when an attempt to automatically process a subscription renewal payment has failed and a retry rule has been applied to retry the payment in the future. The email contains the renewal order information, date of the scheduled retry and payment links to allow the customer to pay for the renewal order manually instead of waiting for the automatic retry."
msgstr ""

#: includes/emails/class-wcs-email-customer-payment-retry.php:32
msgid "Automatic payment failed for {order_number}, we will retry {retry_time}"
msgstr ""

#: includes/emails/class-wcs-email-customer-payment-retry.php:33
msgid "Automatic payment failed for order {order_number}"
msgstr ""

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:40
msgid "Customer Renewal Invoice"
msgstr ""

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:41
msgid "Sent to a customer when the subscription is due for renewal and the renewal requires a manual payment, either because it uses manual renewals or the automatic recurring payment failed for the initial attempt and all automatic retries (if any). The email contains renewal order information and payment links."
msgstr ""

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:48
msgid "Invoice for renewal order {order_number} from {order_date}"
msgstr ""

#: includes/emails/class-wcs-email-customer-renewal-invoice.php:49
msgid "Invoice for renewal order {order_number}"
msgstr ""

#: includes/emails/class-wcs-email-expired-subscription.php:26
msgid "Expired Subscription"
msgstr ""

#: includes/emails/class-wcs-email-expired-subscription.php:27
msgid "Expired Subscription emails are sent when a customer's subscription expires."
msgstr ""

#: includes/emails/class-wcs-email-expired-subscription.php:29
msgid "Subscription Expired"
msgstr ""

#. translators: placeholder is {blogname}, a variable that will be substituted when email is sent out
#: includes/emails/class-wcs-email-expired-subscription.php:31
msgctxt "default email subject for expired emails sent to the admin"
msgid "[%s] Subscription Expired"
msgstr ""

#: includes/emails/class-wcs-email-expired-subscription.php:78
#: includes/emails/class-wcs-email-on-hold-subscription.php:78
msgid "Subscription argument passed in is not an object."
msgstr ""

#. translators: %s: default e-mail heading.
#: includes/emails/class-wcs-email-expired-subscription.php:166
#: includes/emails/class-wcs-email-on-hold-subscription.php:166
msgid "This controls the main heading contained within the email notification. Leave blank to use the default heading: <code>%s</code>."
msgstr ""

#: includes/emails/class-wcs-email-new-renewal-order.php:22
msgid "New Renewal Order"
msgstr ""

#: includes/emails/class-wcs-email-new-renewal-order.php:23
msgid "New renewal order emails are sent when a subscription renewal payment is processed."
msgstr ""

#: includes/emails/class-wcs-email-new-renewal-order.php:25
msgid "New subscription renewal order"
msgstr ""

#: includes/emails/class-wcs-email-new-renewal-order.php:26
msgid "[{blogname}] New subscription renewal order ({order_number}) - {order_date}"
msgstr ""

#: includes/emails/class-wcs-email-new-switch-order.php:22
#: includes/emails/class-wcs-email-new-switch-order.php:25
msgid "Subscription Switched"
msgstr ""

#: includes/emails/class-wcs-email-new-switch-order.php:23
msgid "Subscription switched emails are sent when a customer switches a subscription."
msgstr ""

#: includes/emails/class-wcs-email-new-switch-order.php:26
msgid "[{blogname}] Subscription Switched ({order_number}) - {order_date}"
msgstr ""

#: includes/emails/class-wcs-email-on-hold-subscription.php:26
msgid "Suspended Subscription"
msgstr ""

#: includes/emails/class-wcs-email-on-hold-subscription.php:27
msgid "Suspended Subscription emails are sent when a customer manually suspends their subscription."
msgstr ""

#: includes/emails/class-wcs-email-on-hold-subscription.php:29
msgid "Subscription Suspended"
msgstr ""

#. translators: placeholder is {blogname}, a variable that will be substituted when email is sent out
#: includes/emails/class-wcs-email-on-hold-subscription.php:31
msgctxt "default email subject for suspended emails sent to the admin"
msgid "[%s] Subscription Suspended"
msgstr ""

#: includes/emails/class-wcs-email-payment-retry.php:26
msgid "Payment Retry"
msgstr ""

#: includes/emails/class-wcs-email-payment-retry.php:27
msgid "Payment retry emails are sent to chosen recipient(s) when an attempt to automatically process a subscription renewal payment has failed and a retry rule has been applied to retry the payment in the future."
msgstr ""

#: includes/emails/class-wcs-email-payment-retry.php:29
msgid "Automatic renewal payment failed"
msgstr ""

#: includes/emails/class-wcs-email-payment-retry.php:30
msgid "[{site_title}] Automatic payment failed for {order_number}, retry scheduled to run {retry_time}"
msgstr ""

#: includes/emails/class-wcs-email-processing-renewal-order.php:24
msgid "Processing Renewal order"
msgstr ""

#: includes/emails/class-wcs-email-processing-renewal-order.php:25
msgid "This is an order notification sent to the customer after payment for a subscription renewal order is completed. It contains the renewal order details."
msgstr ""

#: includes/emails/class-wcs-email-processing-renewal-order.php:28
msgid "Thank you for your order"
msgstr ""

#: includes/emails/class-wcs-email-processing-renewal-order.php:29
msgid "Your {blogname} renewal order receipt from {order_date}"
msgstr ""

#. translators: 1-2: opening/closing tags - link to documentation.
#: includes/gateways/class-wc-subscriptions-payment-gateways.php:166
msgid "Sorry, it seems there are no available payment methods which support subscriptions. Please see %1$sEnabling Payment Gateways for Subscriptions%2$s if you require assistance."
msgstr ""

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:168
msgid "Sorry, it seems there are no available payment methods which support subscriptions. Please contact us if you require assistance or wish to make alternate arrangements."
msgstr ""

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:303
msgid "Supported features:"
msgstr ""

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:306
msgid "Subscription features:"
msgstr ""

#: includes/gateways/class-wc-subscriptions-payment-gateways.php:310
msgid "Change payment features:"
msgstr ""

#: includes/gateways/paypal/class-wcs-paypal.php:220
msgid "Unable to find order for PayPal billing agreement."
msgstr ""

#: includes/gateways/paypal/class-wcs-paypal.php:282
msgid "An error occurred, please try again or try an alternate form of payment."
msgstr ""

#: includes/gateways/paypal/class-wcs-paypal.php:362
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:208
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:320
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:336
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:365
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:384
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:150
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:156
msgctxt "hash before the order number. Used as a character to remove from the actual order number"
msgid "#"
msgstr ""

#. translators: placeholders are PayPal API error code and PayPal API error message
#: includes/gateways/paypal/class-wcs-paypal.php:386
msgid "PayPal API error: (%1$d) %2$s"
msgstr ""

#. translators: placeholder is PayPal transaction status message
#: includes/gateways/paypal/class-wcs-paypal.php:391
msgid "PayPal Transaction Held: %s"
msgstr ""

#. translators: placeholder is PayPal transaction status message
#: includes/gateways/paypal/class-wcs-paypal.php:403
msgid "PayPal payment declined: %s"
msgstr ""

#. translators: placeholder is a transaction ID.
#: includes/gateways/paypal/class-wcs-paypal.php:407
msgid "PayPal payment approved (ID: %s)"
msgstr ""

#: includes/gateways/paypal/class-wcs-paypal.php:460
msgid ""
"Are you sure you want to change the payment method from PayPal standard?\n"
"\n"
"This will suspend the subscription at PayPal."
msgstr ""

#: includes/gateways/paypal/class-wcs-paypal.php:626
msgctxt "used in User Agent data sent to PayPal to help identify where a payment came from"
msgid "WooCommerce Subscriptions PayPal"
msgstr ""

#. translators: $1 and $2 are opening and closing strong tags, respectively.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:63
msgid "It is %1$sstrongly recommended you do not change the Receiver Email address%2$s if you have active subscriptions with PayPal. Doing so can break existing subscriptions."
msgstr ""

#. translators: placeholders are opening and closing link tags. 1$-2$: to docs on woocommerce, 3$-4$ to gateway settings on the site
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:110
msgid "PayPal is inactive for subscription transactions. Please %1$sset up the PayPal IPN%2$s and %3$senter your API credentials%4$s to enable PayPal for Subscriptions."
msgstr ""

#. translators: opening/closing tags - links to documentation.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:122
msgid "%1$sPayPal Reference Transactions are not enabled on your account%2$s, some subscription management features are not enabled. Please contact PayPal and request they %3$senable PayPal Reference Transactions%4$s on your account. %5$sCheck PayPal Account%6$s  %3$sLearn more %7$s"
msgstr ""

#. translators: opening/closing tags - links to documentation.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:126
msgid "%1$sPayPal Reference Transactions are not enabled on your account%2$s. If you wish to use PayPal Reference Transactions with Subscriptions, please contact PayPal and request they %3$senable PayPal Reference Transactions%4$s on your account. %5$sCheck PayPal Account%6$s  %3$sLearn more %7$s"
msgstr ""

#. translators: placeholders are opening and closing strong tags.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:148
msgid "%1$sPayPal Reference Transactions are enabled on your account%2$s. All subscription management features are now enabled. Happy selling!"
msgstr ""

#. translators: placeholders are link opening and closing tags. 1$-2$: to gateway settings, 3$-4$: support docs on woocommerce.com
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:159
msgid "There is a problem with PayPal. Your API credentials may be incorrect. Please update your %1$sAPI credentials%2$s. %3$sLearn more%4$s."
msgstr ""

#. translators: placeholders are opening and closing link tags. 1$-2$: docs on woocommerce, 3$-4$: dismiss link
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:172
msgid "There is a problem with PayPal. Your PayPal account is issuing out-of-date subscription IDs. %1$sLearn more%2$s. %3$sDismiss%4$s."
msgstr ""

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:194
msgid "Ignore this error (not recommended)"
msgstr ""

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:199
msgid "Open a ticket"
msgstr ""

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:286
msgid "PayPal Subscription ID:"
msgstr ""

#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:312
msgid "Enable PayPal Standard for Subscriptions"
msgstr ""

#. translators: Placeholders are the opening and closing link tags.
#: includes/gateways/paypal/includes/admin/class-wcs-paypal-admin.php:320
msgid "Before enabling PayPal Standard for Subscriptions, please note, when using PayPal Standard, customers are locked into using PayPal Standard for the life of their subscription, and PayPal Standard has a number of limitations. Please read the guide on %1$swhy we don't recommend PayPal Standard%2$s for Subscriptions before choosing to enable this option."
msgstr ""

#. translators: placeholder is blogname
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:71
msgctxt "data sent to paypal"
msgid "Orders with %s"
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:279
msgid "Total Discount"
msgstr ""

#. translators: placeholder is blogname
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:306
msgid "%s - Order"
msgstr ""

#. translators: 1$: new status (e.g. "Cancel"), 2$: blog name
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:416
msgctxt "data sent to paypal"
msgid "%1$s subscription event triggered at %2$s"
msgstr ""

#. translators: %s: product SKU.
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-request.php:536
msgid "SKU: %s"
msgstr ""

#. translators: placeholder is localised datetime
#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response-payment.php:119
msgid "expected clearing date %s"
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response.php:136
msgctxt "used in api error message if there is no severity code from PayPal"
msgid "Error"
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response.php:138
msgctxt "used in api error message if there is no long message"
msgid "Unknown error"
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-api-response.php:150
#: templates/admin/deprecated/order-shipping-html.php:14
#: templates/admin/deprecated/order-tax-html.php:9
msgctxt "no information about something"
msgid "N/A"
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-reference-transaction-ipn-handler.php:94
msgid "Billing agreement cancelled at PayPal."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:275
msgctxt "when it is a payment change, and there is a subscr_signup message, this will be a confirmation message that PayPal accepted it being the new payment method"
msgid "IPN subscription payment method changed to PayPal."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:279
msgid "IPN subscription sign up completed."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:332
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:417
msgid "IPN subscription payment completed."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:379
msgid "IPN subscription failing payment method changed."
msgstr ""

#. translators: placeholder is payment status (e.g. "completed")
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:427
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:436
msgctxt "used in order note"
msgid "IPN subscription payment %s."
msgstr ""

#. translators: 1: payment status (e.g. "completed"), 2: pending reason.
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:440
msgctxt "used in order note"
msgid "IPN subscription payment %1$s for reason: %2$s."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:469
msgid "IPN subscription suspended."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:492
msgid "IPN subscription cancelled."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:508
msgid "IPN subscription payment failure."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-ipn-handler.php:646
msgid "Invalid PayPal IPN Payload: unable to find matching subscription."
msgstr ""

#. translators: 1$: subscription ID, 2$: names of items, comma separated
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:75
msgctxt "item name sent to paypal"
msgid "Subscription %1$s - %2$s"
msgstr ""

#. translators: 1$: subscription ID, 2$: order ID, 3$: names of items, comma separated
#: includes/gateways/paypal/includes/class-wcs-paypal-standard-request.php:78
msgctxt "item name sent to paypal"
msgid "Subscription %1$s (Order %2$s) - %3$s"
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-standard-switcher.php:266
msgid "Subscription changed from PayPal Standard to PayPal Reference Transactions via customer initiated switch. The PayPal Standard subscription has been suspended."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:42
msgid "Subscription cancelled with PayPal."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:53
msgid "Subscription suspended with PayPal."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:66
msgid "Subscription reactivated with PayPal."
msgstr ""

#: includes/gateways/paypal/includes/class-wcs-paypal-status-manager.php:113
msgid "PayPal API error - credentials are incorrect."
msgstr ""

#. translators: $1 and $2 are opening link tags, $3 is a closing link tag.
#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:18
msgid "A fatal error has occurred while processing a recent subscription payment with PayPal. Please %1$sopen a new ticket at WooCommerce Support%3$s immediately to get this resolved. %2$sLearn more &raquo;%3$s"
msgstr ""

#. translators: $1 and $2 are opening link tags, $3 is a closing link tag.
#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:29
msgid "To resolve this as quickly as possible, please create a %1$stemporary administrator account%3$s with the <NAME_EMAIL> and share the credentials with us via %2$sQuickForget.com%3$s."
msgstr ""

#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:36
msgid "Last recorded error:"
msgstr ""

#. translators: $1 is the log file name. $2 and $3 are opening and closing link tags, respectively.
#: includes/gateways/paypal/includes/templates/html-ipn-failure-notice.php:46
msgid "To see the full error, view the %1$s log file from the %2$sWooCommerce logs screen.%3$s."
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:46
msgid "Automatic Failed Payment Retries"
msgstr ""

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:100
msgid "%d Pending Payment Retry"
msgid_plural "%d Pending Payment Retries"
msgstr[0] ""
msgstr[1] ""

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:104
msgid "%d Processing Payment Retry"
msgid_plural "%d Processing Payment Retries"
msgstr[0] ""
msgstr[1] ""

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:108
msgid "%d Failed Payment Retry"
msgid_plural "%d Failed Payment Retries"
msgstr[0] ""
msgstr[1] ""

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:112
msgid "%d Successful Payment Retry"
msgid_plural "%d Successful Payment Retries"
msgstr[0] ""
msgstr[1] ""

#. translators: %d: retry count.
#: includes/payment-retry/class-wcs-retry-admin.php:116
msgid "%d Cancelled Payment Retry"
msgid_plural "%d Cancelled Payment Retries"
msgstr[0] ""
msgstr[1] ""

#: includes/payment-retry/class-wcs-retry-admin.php:144
msgid "Retry Failed Payments"
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:145
msgid "Enable automatic retry of failed recurring payments"
msgstr ""

#. translators: 1,2: opening/closing link tags (to documentation).
#: includes/payment-retry/class-wcs-retry-admin.php:150
msgid "Attempt to recover recurring revenue that would otherwise be lost due to payment methods being declined only temporarily. %1$sLearn more%2$s."
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:172
msgctxt "label for the system status page"
msgid "Custom Retry Rules"
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:180
msgctxt "label for the system status page"
msgid "Custom Retry Rule Class"
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:188
msgctxt "label for the system status page"
msgid "Custom Raw Retry Rule"
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:196
msgctxt "label for the system status page"
msgid "Custom Retry Rule"
msgstr ""

#: includes/payment-retry/class-wcs-retry-admin.php:204
msgctxt "label for the system status page"
msgid "Retries Migration Status"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:25
msgid "Payment retry posts store details about the automatic retry of failed renewal payments."
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:35
msgctxt "Post type name"
msgid "Renewal Payment Retries"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:36
msgid "Renewal Payment Retry"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:37
msgctxt "Admin menu name"
msgid "Renewal Payment Retries"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:38
msgid "Add"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:39
msgid "Add New Retry"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:41
msgid "Edit Retry"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:42
msgid "New Retry"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:43
#: includes/payment-retry/class-wcs-retry-post-store.php:44
msgid "View Retry"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:45
msgid "Search Renewal Payment Retries"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:46
msgid "No retries found"
msgstr ""

#: includes/payment-retry/class-wcs-retry-post-store.php:47
msgid "No retries found in trash"
msgstr ""

#. Translators: %s subscription number.
#: includes/privacy/class-wcs-privacy-erasers.php:71
msgid "Removed personal data from subscription %s."
msgstr ""

#. Translators: %s subscription number.
#: includes/privacy/class-wcs-privacy-erasers.php:75
msgid "Personal data within subscription %s has been retained."
msgstr ""

#: includes/privacy/class-wcs-privacy-erasers.php:189
msgid "Personal data removed."
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:77
msgid "Subscription Number"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:78
msgid "Created Date"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:79
msgid "Recurring Total"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:80
msgid "Subscription Items"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:81
msgid "IP Address"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:82
msgid "Browser User Agent"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:83
#: wcs-functions.php:284
msgid "Billing Address"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:84
#: wcs-functions.php:283
msgid "Shipping Address"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:85
msgid "Phone Number"
msgstr ""

#: includes/privacy/class-wcs-privacy-exporters.php:86
msgid "Email Address"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:43
#: includes/privacy/class-wcs-privacy.php:44
msgid "Subscriptions Data"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:92
msgid "By using WooCommerce Subscriptions, you may be storing personal data and depending on which third-party payment processors you’re using to take subscription payments, you may be sharing personal data with external sources."
msgstr ""

#. translators: placeholders are opening and closing link tags, linking to additional privacy policy documentation.
#: includes/privacy/class-wcs-privacy.php:94
msgid "What we collect and store"
msgstr ""

#. translators: placeholders are opening and closing link tags, linking to additional privacy policy documentation.
#: includes/privacy/class-wcs-privacy.php:95
msgid "For the purposes of processing recurring subscription payments, we store the customer's name, billing address, shipping address, email address, phone number and credit card/payment details."
msgstr ""

#: includes/privacy/class-wcs-privacy.php:96
msgid "What we share with others"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:97
msgid "What personal information your store shares with external sources depends on which third-party payment processor plugins you are using to collect subscription payments. We recommend that you consult with their privacy policies to inform this section of your privacy policy."
msgstr ""

#. translators: placeholders are opening and closing link tags, linking to additional privacy policy documentation.
#: includes/privacy/class-wcs-privacy.php:99
msgid "If you are using PayPal Standard or PayPal Reference transactions please see the %1$sPayPal Privacy Policy%2$s for more details."
msgstr ""

#: includes/privacy/class-wcs-privacy.php:109
msgid "Cancel and remove personal data"
msgstr ""

#. translators: %d: number of subscriptions affected.
#: includes/privacy/class-wcs-privacy.php:176
msgid "Removed personal data from %d subscription."
msgid_plural "Removed personal data from %d subscriptions."
msgstr[0] ""
msgstr[1] ""

#. translators: placeholders are opening and closing tags.
#: includes/privacy/class-wcs-privacy.php:195
msgid "%1$sNote:%2$s Orders which are related to subscriptions will not be included in the orders affected by these settings."
msgstr ""

#: includes/privacy/class-wcs-privacy.php:215
msgid "account erasure request"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:221
msgid "Remove personal data from subscriptions"
msgstr ""

#. Translators: %s URL to erasure request screen.
#: includes/privacy/class-wcs-privacy.php:223
msgid "When handling an %s, should personal data within subscriptions be retained or removed?"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:232
msgid "Retain ended subscriptions"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:233
msgid "Retain ended subscriptions and their related orders for a specified duration before anonymizing the personal data within them."
msgstr ""

#: includes/privacy/class-wcs-privacy.php:236
msgid "N/A"
msgstr ""

#: includes/privacy/class-wcs-privacy.php:277
msgid "Customers with a subscription are excluded from this setting."
msgstr ""

#. translators: placeholder is a list of version numbers (e.g. "1.3 & 1.4 & 1.5")
#: includes/upgrades/class-wc-subscriptions-upgrader.php:356
msgid "Database updated to version %s"
msgstr ""

#. translators: placeholder is number of upgraded subscriptions
#: includes/upgrades/class-wc-subscriptions-upgrader.php:364
msgctxt "used in the subscriptions upgrader"
msgid "Marked %s subscription products as \"sold individually\"."
msgstr ""

#. translators: 1$: number of action scheduler hooks upgraded, 2$: "{execution_time}", will be replaced on front end with actual time
#: includes/upgrades/class-wc-subscriptions-upgrader.php:373
msgid "Migrated %1$s subscription related hooks to the new scheduler (in %2$s seconds)."
msgstr ""

#. translators: 1$: number of subscriptions upgraded, 2$: "{execution_time}", will be replaced on front end with actual time it took
#: includes/upgrades/class-wc-subscriptions-upgrader.php:385
msgid "Migrated %1$s subscriptions to the new structure (in %2$s seconds)."
msgstr ""

#. translators: placeholder is "{time_left}", will be replaced on front end with actual time
#: includes/upgrades/class-wc-subscriptions-upgrader.php:388
#: includes/upgrades/class-wc-subscriptions-upgrader.php:434
msgctxt "Message that gets sent to front end."
msgid "Estimated time left (minutes:seconds): %s"
msgstr ""

#. translators: 1$: error message, 2$: opening link tag, 3$: closing link tag, 4$: break tag
#: includes/upgrades/class-wc-subscriptions-upgrader.php:398
msgid "Unable to upgrade subscriptions.%4$sError: %1$s%4$sPlease refresh the page and try again. If problem persists, %2$scontact support%3$s."
msgstr ""

#. translators: placeholder is the number of subscriptions repaired
#: includes/upgrades/class-wc-subscriptions-upgrader.php:413
msgctxt "Repair message that gets sent to front end."
msgid "Repaired %d subscriptions with incorrect dates, line tax data or missing customer notes."
msgstr ""

#. translators: placeholder is number of subscriptions that were checked and did not need repairs. There's a space at the beginning!
#: includes/upgrades/class-wc-subscriptions-upgrader.php:419
msgctxt "Repair message that gets sent to front end."
msgid " %d other subscription was checked and did not need any repairs."
msgid_plural "%d other subscriptions were checked and did not need any repairs."
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is "{execution_time}", which will be replaced on front end with actual time
#: includes/upgrades/class-wc-subscriptions-upgrader.php:423
msgctxt "Repair message that gets sent to front end."
msgid "(in %s seconds)"
msgstr ""

#. translators: $1: "Repaired x subs with incorrect dates...", $2: "X others were checked and no repair needed", $3: "(in X seconds)". Ordering for RTL languages.
#: includes/upgrades/class-wc-subscriptions-upgrader.php:426
msgctxt "The assembled repair message that gets sent to front end."
msgid "%1$s%2$s %3$s"
msgstr ""

#. translators: 1$: error message, 2$: opening link tag, 3$: closing link tag, 4$: break tag
#: includes/upgrades/class-wc-subscriptions-upgrader.php:445
msgctxt "Error message that gets sent to front end when upgrading Subscriptions"
msgid "Unable to repair subscriptions.%4$sError: %1$s%4$sPlease refresh the page and try again. If problem persists, %2$scontact support%3$s."
msgstr ""

#: includes/upgrades/class-wc-subscriptions-upgrader.php:649
msgid "Welcome to WooCommerce Subscriptions 2.1"
msgstr ""

#: includes/upgrades/class-wc-subscriptions-upgrader.php:649
msgid "About WooCommerce Subscriptions"
msgstr ""

#. translators: 1-2: opening/closing <strong> tags, 3: active version of Subscriptions, 4: current version of Subscriptions, 5-6: opening/closing tags linked to ticket form, 7-8: opening/closing tags linked to documentation.
#: includes/upgrades/class-wc-subscriptions-upgrader.php:831
msgid "%1$sWarning!%2$s It appears that you have downgraded %1$sWooCommerce Subscriptions%2$s from %3$s to %4$s. Downgrading the plugin in this way may cause issues. Please update to %3$s or higher, or %5$sopen a new support ticket%6$s for further assistance. %7$sLearn more &raquo;%8$s"
msgstr ""

#. translators: 1-2: opening/closing <strong> tags, 3-4: opening/closing tags linked to ticket form.
#: includes/upgrades/class-wc-subscriptions-upgrader.php:905
msgid "%1$sWarning!%2$s We discovered an issue in %1$sWooCommerce Subscriptions 2.3.0 - 2.3.2%2$s that may cause your subscription renewal order and customer subscription caches to contain invalid data. For information about how to update the cached data, please %3$sopen a new support ticket%4$s."
msgstr ""

#: includes/upgrades/class-wcs-repair-suspended-paypal-subscriptions.php:51
msgid "Subscription suspended by Database repair script. This subscription was suspended via PayPal."
msgstr ""

#: includes/upgrades/class-wcs-upgrade-2-2-7.php:60
msgid "Subscription end date in the past"
msgstr ""

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:80
msgctxt "plugin version number used in admin notice"
msgid "3.1"
msgstr ""

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:85
msgid "v3 REST API endpoint support"
msgstr ""

#. translators: 1-3: opening/closing <a> tags - link to documentation.
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:88
msgid "Webhook and REST API users can now use v3 subscription endpoints. Click here to %1$slearn more%2$s about the REST API and check out the technical API docs %3$shere%2$s."
msgstr ""

#: includes/upgrades/class-wcs-upgrade-notice-manager.php:95
msgid "WooCommerce checkout and cart blocks integration"
msgstr ""

#. translators: 1-2: opening/closing <a> tags - link to documentation.
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:98
msgid "Subscriptions is now compatible with the WooCommerce cart and checkout blocks. You can learn more about the compatibility status of the cart & checkout blocks %1$shere%2$s."
msgstr ""

#. translators: placeholder is Subscription version string ('3.1')
#: includes/upgrades/class-wcs-upgrade-notice-manager.php:105
msgid "Welcome to WooCommerce Subscriptions %s!"
msgstr ""

#. translators: placeholder is Subscription version string ('2.3')
#: includes/upgrades/templates/update-welcome-notice.php:2
#: includes/upgrades/templates/wcs-about-2-0.php:23
#: includes/upgrades/templates/wcs-about.php:22
msgid "Thank you for updating to the latest version of WooCommerce Subscriptions."
msgstr ""

#. translators: placeholder $1 is the Subscription version string ('2.3'), $2-3 are opening and closing <em> tags
#: includes/upgrades/templates/update-welcome-notice.php:5
msgid "Version %1$s brings some new improvements requested by store managers just like you (and possibly even by %2$syou%3$s)."
msgstr ""

#: includes/upgrades/templates/update-welcome-notice.php:6
#: includes/upgrades/templates/wcs-about-2-0.php:25
#: includes/upgrades/templates/wcs-about.php:24
msgid "We hope you enjoy it!"
msgstr ""

#: includes/upgrades/templates/update-welcome-notice.php:8
msgid "What's new?"
msgstr ""

#. translators: placeholder is Subscription version string ('2.3')
#: includes/upgrades/templates/update-welcome-notice.php:16
msgid "Want to know more about Subscriptions %s?"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:20
msgid "Welcome to Subscriptions 2.0"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:24
msgid "Version 2.0 has been in development for more than a year. We've reinvented the extension to take into account 3 years of feedback from store managers."
msgstr ""

#. translators: placeholder is version number
#: includes/upgrades/templates/wcs-about-2-0.php:31
#: includes/upgrades/templates/wcs-about.php:30
msgid "Version %s"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:36
#: woocommerce-subscriptions.php:1202
msgctxt "short for documents"
msgid "Docs"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:42
#: includes/upgrades/templates/wcs-about.php:41
msgid "Check Out What's New"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:51
msgid "Multiple Subscriptions"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:52
msgid "It's now easier for your customers to buy more subscriptions!"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:53
msgid "Customers can now purchase different subscription products in one transaction. The products can bill on any schedule and have any combination of sign-up fees and/or free trials."
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/upgrades/templates/wcs-about-2-0.php:56
msgid "Learn more about the new %smultiple subscriptions%s feature."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:68
msgid "New Add/Edit Subscription Screen"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:69
msgid "Subscriptions v2.0 introduces a new administration interface to add or edit a subscription. You can make all the familiar changes, like modifying recurring totals or subscription status. You can also make some new modifications, like changing the expiration date, adding a shipping cost or adding a product line item."
msgstr ""

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:72
msgid "The new interface is also built on the existing %sEdit Order%s screen. If you've ever modified an order, you already know how to modify a subscription."
msgstr ""

#. translators: placeholers are link tags: 1$-2$ new subscription page, 3$-4$: docs on woocommerce.com
#: includes/upgrades/templates/wcs-about-2-0.php:76
msgid "%1$sAdd a subscription%2$s now or %3$slearn more%4$s about the new interface."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:87
msgid "New View Subscription Page"
msgstr ""

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:91
msgid "Your customers can now view the full details of a subscription, including line items, billing and shipping address, billing schedule and renewal orders, from a special %sMy Account > View Subscription%s page."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:93
msgid "This new page is also where the customer can suspend or cancel their subscription, change payment method, change shipping address or upgrade/downgrade an item."
msgstr ""

#. translators: placeholders are opening and closing link tags
#: includes/upgrades/templates/wcs-about-2-0.php:97
msgid "Learn more about the new %sView Subscription page%s."
msgstr ""

#. translators: placeholders are for opening and closing link (<a>) tags
#: includes/upgrades/templates/wcs-about-2-0.php:111
msgid "By default, adding new files to an existing subscription product will automatically provide active subscribers with access to the new files. However, now you can enable a %snew content dripping setting%s to provide subscribers with access to new files only after the next renewal payment."
msgstr ""

#. translators: placeholders are for opening and closing link (<a>) tags
#. translators: placeholders are opening and closing anchor tags linking to documentation
#: includes/upgrades/templates/wcs-about-2-0.php:115
#: includes/upgrades/templates/wcs-about-2-0.php:128
#: includes/upgrades/templates/wcs-about-2-0.php:141
#: includes/upgrades/templates/wcs-about.php:120
#: includes/upgrades/templates/wcs-about.php:131
#: includes/upgrades/templates/wcs-about.php:142
#: includes/upgrades/templates/wcs-about.php:170
#: includes/upgrades/templates/wcs-about.php:191
msgid "%sLearn more &raquo;%s"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:121
msgctxt "h3 on the About Subscriptions page for this new feature"
msgid "Change Payment Method"
msgstr ""

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:124
msgid "For a store manager to change a subscription from automatic to manual renewal payments (or manual to automatic) with Subscriptions v1.5, the database needed to be modified directly. Subscriptions now provides a way for payment gateways to allow you to change that from the new %sEdit Subscription%s interface."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:134
msgid "Change Trial and End Dates"
msgstr ""

#. translators: placeholders are opening and closing <strong> tags
#: includes/upgrades/templates/wcs-about-2-0.php:137
msgid "It was already possible to change a subscription's next payment date, but some store managers wanted to provide a customer with an extended free trial or add an extra month to the expiration date. Now you can change all of these dates from the %sEdit Subscription%s screen."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:150
msgid "And much more..."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:157
#: includes/upgrades/templates/wcs-about.php:151
msgid "Peek Under the Hood for Developers"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:158
msgid "Subscriptions 2.0 introduces a new architecture built on the WooCommerce Custom Order Types API."
msgstr ""

#. translators: placeholders are opening and closing code tags
#: includes/upgrades/templates/wcs-about-2-0.php:164
msgid "New %sshop_subscription%s Post Type"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:166
msgid "By making a subscription a Custom Order Type, a subscription is also now a custom post type. This makes it faster to query subscriptions and it uses a database schema that is as scalable as WordPress posts and pages."
msgstr ""

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about-2-0.php:169
msgid "Developers can also now use all the familiar WordPress functions, like %sget_posts()%s, to query or modify subscription data."
msgstr ""

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about-2-0.php:175
msgid "New %sWC_Subscription%s Object"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:177
msgid "Subscriptions 2.0 introduces a new object for working with a subscription at the application level. The cumbersome APIs for retrieving or modifying a subscription's data are gone!"
msgstr ""

#. translators: all placeholders are opening and closing <code> tags, no need to order them
#: includes/upgrades/templates/wcs-about-2-0.php:180
msgid "Because the %sWC_Subscription%s class extends %sWC_Order%s, you can use its familiar methods, like %s$subscription->update_status()%s or %s$subscription->get_total()%s."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:184
msgid "REST API Endpoints"
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:185
msgid "We didn't just improve interfaces for humans, we also improved them for computers. Your applications can now create, read, update or delete subscriptions via RESTful API endpoints."
msgstr ""

#. translators: all placeholders are opening and closing <code> tags, no need to order them
#: includes/upgrades/templates/wcs-about-2-0.php:188
msgid "Want to list all the subscriptions on a site? Get %sexample.com/wc-api/v2/subscriptions/%s. Want the details of a specific subscription? Get %s/wc-api/v2/subscriptions/<id>/%s."
msgstr ""

#: includes/upgrades/templates/wcs-about-2-0.php:194
msgid "Go to WooCommerce Subscriptions Settings"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:19
msgid "Welcome to Subscriptions 2.1!"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:23
msgid "Version 2.1 introduces some great new features requested by store managers just like you (and possibly even by %syou%s)."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:35
msgctxt "short for documents"
msgid "Documentation"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:49
msgid "Subscription Reports"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:50
msgid "How many customers stay subscribed for more than 6 months? What is the average lifetime value of your subscribers? How much renewal revenue will your store earn next month?"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:51
msgid "These are important questions for any subscription commerce business."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:52
msgid "Prior to Subscriptions 2.1, they were not easy to answer. Subscriptions 2.1 introduces new reports to answer these questions, and many more."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:54
msgid "View Reports"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:55
msgctxt "learn more link to subscription reports documentation"
msgid "Learn More"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:67
msgid "Automatic Failed Payment Retry"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:68
msgid "Failed recurring payments can now be retried automatically. This helps recover revenue that would otherwise be lost due to payment methods being declined only temporarily."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:69
msgid "By default, Subscriptions will retry the payment 5 times over 7 days. The rules that control the retry system can be modified to customise:"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:71
msgid "the total number of retry attempts"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:72
msgid "how long to wait between retry attempts"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:73
msgid "emails sent to the customer and store manager"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:74
msgid "the status applied to the renewal order and subscription"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:76
msgid "The retry system is disabled by default. To enable it, visit the Subscriptions settings administration screen."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:78
msgid "Enable Automatic Retry"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:79
msgctxt "learn more link to failed payment retry documentation"
msgid "Learn More"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:90
msgid "New Subscription Emails"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:91
msgid "Subscriptions 2.1 also introduces a number of new emails to notify you when:"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:93
msgid "a customer suspends a subscription"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:94
msgid "an automatic payment fails"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:95
msgid "a subscription expires"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:97
msgid "These emails can be enabled, disabled and customised under the %sWooCommerce > Settings > Emails%s administration screen."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:99
msgid "View Email Settings"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:100
msgctxt "learn more link to subscription emails documentation"
msgid "Learn More"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:108
msgid "But wait, there's more!"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:109
msgid "That's not all we've working on for the last 12 months when it comes to Subscriptions. We've also released mini-extensions to help you get the most from your subscription store."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:115
msgid "Subscription Gifting"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:116
msgid "What happens when a customer wants to purchase a subscription product for someone else?"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:117
msgid "The Gifting extension makes it possible for one person to purchase a subscription product for someone else. It then shares control of the subscription between the purchaser and recipient, allowing both to manage the subscription over its lifecycle."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:126
msgctxt "h3 on the About Subscriptions page for this new feature"
msgid "Import/Export Subscriptions"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:127
msgid "Import subscriptions to WooCommerce via CSV, or export your subscriptions from WooCommerce to a CSV with the WooCommerce Subscriptions Importer/Exporter extension."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:128
msgid "This free extension makes it possible to migrate subscribers from 3rd party systems to WooCommerce. It also makes it possible to export your subscription data for analysis in spreadsheet tools or 3rd party apps."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:137
msgid "Subscribe All the Things"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:138
msgid "Want your customers to be able to subscribe to non-subscription products?"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:139
msgid "With WooCommerce Subscribe All the Things, they can! This experimental extension is exploring how to convert any product, including Product Bundles and Composite Products, into a subscription product. It also offers customers a way to subscribe to a cart of non-subscription products."
msgstr ""

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about.php:157
msgid "Customise Retry Rules"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:159
msgid "The best part about the new automatic retry system is that the retry rules are completely customisable."
msgstr ""

#. translators: all placeholders are opening and closing <code> tags, no need to order them
#: includes/upgrades/templates/wcs-about.php:162
msgid "With the %s'wcs_default_retry_rules'%s filter, you can define a set of default rules to apply to all failed payments in your store."
msgstr ""

#. translators: all placeholders are opening and closing <code> tags, no need to order them
#: includes/upgrades/templates/wcs-about.php:166
msgid "To apply a specific rule based on certain conditions, like high value orders or an infrequent renewal schedule, you can use the retry specific %s'wcs_get_retry_rule'%s filter. This provides the ID of the renewal order for the failed payment, which can be used to find information about the products, subscription and totals to which the failed payment relates."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:174
msgid "WP REST API Endpoints"
msgstr ""

#. translators: $1: opening <a> tag linking to WC API docs, $2: closing <a> tag, $3: opening <a> tag linking to WP API docs, $4: closing <a> tag
#: includes/upgrades/templates/wcs-about.php:177
msgid "WooCommerce 2.6 added support for %1$sREST API%2$s endpoints built on WordPress core's %3$sREST API%4$s infrastructure."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:179
msgid "Subscriptions 2.1 adds support for subscription data to this infrastructure."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:180
msgid "Your applications can now create, read, update or delete subscriptions via RESTful API endpoints with the same design as the latest version of WooCommerce's REST API endpoints."
msgstr ""

#. translators: all placeholders are opening and closing <code> tags, no need to order them
#: includes/upgrades/templates/wcs-about.php:183
msgid "Want to list all the subscriptions on a site? Get %s/wp-json/wc/v1/subscriptions%s."
msgstr ""

#. translators: all placeholders are opening and closing <code> tags, no need to order them
#: includes/upgrades/templates/wcs-about.php:187
msgid "Want the details of a specific subscription? Get %s/wp-json/wc/v1/subscriptions/<id>/%s."
msgstr ""

#. translators: placeholders are opening and closing code tags
#: includes/upgrades/templates/wcs-about.php:197
msgid "Honour Renewal Order Data"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:199
msgid "In previous versions of Subscriptions, the subscription total was passed to payment gateways as the amount to charge for automatic renewal payments. This made it unnecessarily complicated to add one-time fees or discounts to a renewal."
msgstr ""

#. translators: placeholders are opening and closing <code> tags
#: includes/upgrades/templates/wcs-about.php:202
msgid "Subscriptions 2.1 now passes the renewal order's total, making it possible to add a fee or discount to the renewal order with simple one-liners like %s$order->add_fee()%s or %s$order->add_coupon()%s."
msgstr ""

#. translators: placeholders are opening and closing <a> tags
#: includes/upgrades/templates/wcs-about.php:206
msgid "Subscriptions also now uses the renewal order to setup the cart for %smanual renewals%s, making it easier to add products or discounts to a single renewal paid manually."
msgstr ""

#: includes/upgrades/templates/wcs-about.php:212
msgid "See the full guide to What's New in Subscriptions version 2.1 &raquo;"
msgstr ""

#: includes/upgrades/templates/wcs-about.php:213
msgid "Go to WooCommerce Subscriptions Settings &raquo;"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:24
msgid "WooCommerce Subscriptions Update in Progress"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:30
msgid "The Upgrade is in Progress"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:31
msgid "The WooCommerce Subscriptions plugin is currently running its database upgrade routine."
msgstr ""

#. translators: placeholder is number of seconds
#: includes/upgrades/templates/wcs-upgrade-in-progress.php:34
msgid "If you received a server error and reloaded the page to find this notice, please refresh the page in %s seconds and the upgrade routine will recommence without issues."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade-in-progress.php:36
msgid "Rest assured, although the update process may take a little while, it is coded to prevent defects, your site is safe and will be up and running again, faster than ever, shortly."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:19
msgid "WooCommerce Subscriptions Update"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:29
msgid "Database Update Required"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:30
msgid "The WooCommerce Subscriptions plugin has been updated!"
msgstr ""

#. translators: placeholders are opening and closing tags
#: includes/upgrades/templates/wcs-upgrade.php:33
msgid "Before we send you on your way, we need to update your database to the newest version. If you do not have a recent backup of your site, %snow is the time to create one%s."
msgstr ""

#. translators: 1$: number of subscriptions on site, 2$, lower estimate (minutes), 3$: upper estimate
#: includes/upgrades/templates/wcs-upgrade.php:38
msgid "The full update process for the %1$d subscriptions on your site will take between %2$d and %3$d minutes."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:41
msgid "The update process may take a little while, so please be patient."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:43
msgid "Customers and other non-administrative users can browse and purchase from your store without interruption while the update is in progress."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:45
msgctxt "text on submit button"
msgid "Update Database"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:49
msgid "Update in Progress"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:50
msgid "This page will display the results of the process as each batch of subscriptions is updated."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:51
msgid "Please keep this page open until the update process completes. No need to refresh or restart the process."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:53
msgid "Remember, although the update process may take a while, customers and other non-administrative users can browse and purchase from your store without interruption while the update is in progress."
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:61
msgid "Update Complete"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:62
msgid "Your database has been updated successfully!"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:63
msgid "Continue"
msgstr ""

#. translators: $1: placeholder is number of weeks, 2$: path to the file
#: includes/upgrades/templates/wcs-upgrade.php:66
msgid "To record the progress of the update a new log file was created. This file will be automatically deleted in %1$d weeks. If you would like to delete it sooner, you can find it here: %2$s"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:71
msgid "Update Error"
msgstr ""

#: includes/upgrades/templates/wcs-upgrade.php:72
msgid "There was an error with the update. Please refresh the page and try again."
msgstr ""

#. translators: %s: shipping method label.
#: includes/wcs-cart-functions.php:98
#: includes/wcs-cart-functions.php:104
msgid "Shipping via %s"
msgstr ""

#: includes/wcs-cart-functions.php:239
msgctxt "shipping method price"
msgid "Free"
msgstr ""

#: includes/wcs-cart-functions.php:264
msgid "[Remove]"
msgstr ""

#: includes/wcs-cart-functions.php:294
msgid "Free shipping coupon"
msgstr ""

#. translators: placeholder is price string, denotes tax included in cart/order total
#: includes/wcs-cart-functions.php:332
msgctxt "includes tax"
msgid "(includes %s)"
msgstr ""

#. translators: placeholder is a date
#: includes/wcs-cart-functions.php:407
msgid "First renewal: %s"
msgstr ""

#. translators: placeholder is either subscription key or a subscription id, or, failing that, empty (e.g. "145_21" or "145")
#: includes/wcs-deprecated-functions.php:180
msgid "Could not get subscription. Most likely the subscription key does not refer to a subscription. The key was: \"%s\"."
msgstr ""

#: includes/wcs-formatting-functions.php:41
msgctxt "initial payment on a subscription"
msgid "up front"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount string (e.g. "£10 / month" )
#: includes/wcs-formatting-functions.php:99
msgid "%1$s %2$s then %3$s"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount string, 4$: payment day of the week (e.g. "$15 up front, then $10 every Wednesday")
#: includes/wcs-formatting-functions.php:113
msgid "%1$s %2$s then %3$s every %4$s"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front" ), 3$: recurring amount, 4$: interval (e.g. "2nd week"), 5$: day of the week (e.g. "Thursday"); (e.g. "$10 up front, then $20 every 2nd week on Wednesday")
#: includes/wcs-formatting-functions.php:122
msgid "%1$s %2$s then %3$s every %4$s on %5$s"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount; (e.g. "$10 up front then $30 on the last day of each month")
#: includes/wcs-formatting-functions.php:135
msgid "%1$s %2$s then %3$s on the last day of each month"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount, 4$: day of the month (e.g. "23rd"); (e.g. "$10 up front then $40 on the 23rd of each month")
#: includes/wcs-formatting-functions.php:138
msgid "%1$s %2$s then %3$s on the %4$s of each month"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount, 4$: interval (e.g. "3rd")
#: includes/wcs-formatting-functions.php:154
msgid "%1$s %2$s then %3$s on the last day of every %4$s month"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount, 4$: day of the month (e.g. "23rd"), 5$: interval (e.g. "3rd")
#: includes/wcs-formatting-functions.php:157
msgid "%1$s %2$s then %3$s on the %4$s day of every %5$s month"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount, 4$: month of year (e.g. "March"), 5$: day of the month (e.g. "23rd")
#: includes/wcs-formatting-functions.php:175
msgid "%1$s %2$s then %3$s on %4$s %5$s each year"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount, 4$: month (e.g. "March"), 5$: day of the month (e.g. "23rd"), 6$: interval (e.g. "3rd")
#: includes/wcs-formatting-functions.php:184
msgid "%1$s %2$s then %3$s on %4$s %5$s every %6$s year"
msgstr ""

#. translators: 1$: initial amount, 2$: initial description (e.g. "up front"), 3$: recurring amount, 4$: subscription period (e.g. "month" or "3 months")
#: includes/wcs-formatting-functions.php:194
msgid "%1$s %2$s then %3$s / %4$s"
msgid_plural "%1$s %2$s then %3$s every %4$s"
msgstr[0] ""
msgstr[1] ""

#. translators: 1$: subscription string (e.g. "$10 up front then $5 on March 23rd every 3rd year"), 2$: trial length (e.g. "3 weeks")
#: includes/wcs-formatting-functions.php:216
msgid "%1$s after %2$s free trial"
msgstr ""

#. translators: 1$: trial length (e.g. "3 weeks"), 2$: subscription string (e.g. "$10 up front then $5 on March 23rd every 3rd year")
#: includes/wcs-formatting-functions.php:219
msgid "%1$s free trial then %2$s"
msgstr ""

#. translators: placeholder is human time diff (e.g. "3 weeks")
#: includes/wcs-formatting-functions.php:243
msgid "in %s"
msgstr ""

#. translators: placeholder is a localized date and time (e.g. "February 1, 2018 10:20 PM")
#: includes/wcs-formatting-functions.php:251
msgctxt "wcs_get_human_time_diff"
msgid "%s"
msgstr ""

#. translators: date placeholder for input, javascript format
#: includes/wcs-helper-functions.php:40
msgid "YYYY-MM-DD"
msgstr ""

#. translators: hour placeholder for time input, javascript format
#: includes/wcs-helper-functions.php:45
msgid "HH"
msgstr ""

#. translators: minute placeholder for time input, javascript format
#: includes/wcs-helper-functions.php:48
msgid "MM"
msgstr ""

#. translators: 1) passed sort order type argument, 2) 'ascending', 3) 'descending'.
#: includes/wcs-helper-functions.php:266
msgid "Invalid sort order type: %1$s. The $sort_order argument must be %2$s or %3$s."
msgstr ""

#: includes/wcs-order-functions.php:148
msgctxt "In wcs_copy_order_meta error message. Refers to origin and target order objects."
msgid "Invalid data. Orders expected aren't orders."
msgstr ""

#: includes/wcs-order-functions.php:152
msgctxt "Refers to the type of the copy being performed: \"copy_order\", \"subscription\", \"renewal_order\", \"resubscribe_order\""
msgid "Invalid data. Type of copy is not a string."
msgstr ""

#. translators: placeholders are strftime() strings.
#. translators: Order date parsed by strftime
#: includes/wcs-order-functions.php:296
#: wcs-functions.php:161
msgctxt "Used in subscription post title. \"Subscription renewal order - <this>\""
msgid "%b %d, %Y @ %I:%M %p"
msgstr ""

#. translators: placeholder is a date.
#: includes/wcs-order-functions.php:301
msgid "Subscription Renewal Order &ndash; %s"
msgstr ""

#. translators: placeholder is a date.
#: includes/wcs-order-functions.php:305
msgid "Resubscribe Order &ndash; %s"
msgstr ""

#: includes/wcs-order-functions.php:324
msgid "$type passed to the function was not a string."
msgstr ""

#. translators: placeholder is an order type.
#: includes/wcs-order-functions.php:329
msgid "\"%s\" is not a valid new order type."
msgstr ""

#: includes/wcs-order-functions.php:519
msgid "Invalid data. No valid subscription / order was passed in."
msgstr ""

#: includes/wcs-order-functions.php:523
msgid "Invalid data. No valid item id was passed in."
msgstr ""

#. translators: placeholder is number of days. (e.g. "Bill this every day / 4 days")
#: includes/wcs-time-functions.php:31
msgctxt "Subscription billing period."
msgid "day"
msgid_plural "%s days"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is number of weeks. (e.g. "Bill this every week / 4 weeks")
#: includes/wcs-time-functions.php:33
msgctxt "Subscription billing period."
msgid "week"
msgid_plural "%s weeks"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is number of months. (e.g. "Bill this every month / 4 months")
#: includes/wcs-time-functions.php:35
msgctxt "Subscription billing period."
msgid "month"
msgid_plural "%s months"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is number of years. (e.g. "Bill this every year / 4 years")
#: includes/wcs-time-functions.php:37
msgctxt "Subscription billing period."
msgid "year"
msgid_plural "%s years"
msgstr[0] ""
msgstr[1] ""

#: includes/wcs-time-functions.php:88
msgctxt "Subscription length"
msgid "Never expire"
msgstr ""

#: includes/wcs-time-functions.php:93
msgctxt "Subscription lengths. e.g. \"For 1 day...\""
msgid "1 day"
msgstr ""

#: includes/wcs-time-functions.php:97
msgctxt "Subscription lengths. e.g. \"For 1 week...\""
msgid "1 week"
msgstr ""

#: includes/wcs-time-functions.php:101
msgctxt "Subscription lengths. e.g. \"For 1 month...\""
msgid "1 month"
msgstr ""

#: includes/wcs-time-functions.php:105
msgctxt "Subscription lengths. e.g. \"For 1 year...\""
msgid "1 year"
msgstr ""

#: includes/wcs-time-functions.php:159
msgctxt "period interval (eg \"$10 _every_ 2 weeks\")"
msgid "every"
msgstr ""

#. translators: period interval, placeholder is ordinal (eg "$10 every _2nd/3rd/4th_", etc)
#: includes/wcs-time-functions.php:163
msgctxt "period interval with ordinal number (e.g. \"every 2nd\""
msgid "every %s"
msgstr ""

#: includes/wcs-time-functions.php:188
msgctxt "Used in the trial period dropdown. Number is in text field. 0, 2+ will need plural, 1 will need singular."
msgid "day"
msgid_plural "days"
msgstr[0] ""
msgstr[1] ""

#: includes/wcs-time-functions.php:189
msgctxt "Used in the trial period dropdown. Number is in text field. 0, 2+ will need plural, 1 will need singular."
msgid "week"
msgid_plural "weeks"
msgstr[0] ""
msgstr[1] ""

#: includes/wcs-time-functions.php:190
msgctxt "Used in the trial period dropdown. Number is in text field. 0, 2+ will need plural, 1 will need singular."
msgid "month"
msgid_plural "months"
msgstr[0] ""
msgstr[1] ""

#: includes/wcs-time-functions.php:191
msgctxt "Used in the trial period dropdown. Number is in text field. 0, 2+ will need plural, 1 will need singular."
msgid "year"
msgid_plural "years"
msgstr[0] ""
msgstr[1] ""

#: includes/wcs-time-functions.php:210
msgctxt "no trial period"
msgid "no"
msgstr ""

#: includes/wcs-user-functions.php:345
#: templates/single-product/add-to-cart/subscription.php:30
#: templates/single-product/add-to-cart/variable-subscription.php:28
msgid "Resubscribe"
msgstr ""

#. translators: placeholder is a currency symbol / code
#: templates/admin/deprecated/html-variation-price.php:20
#: templates/admin/deprecated/html-variation-price.php:30
msgid "Subscription Price (%s)"
msgstr ""

#: templates/admin/deprecated/html-variation-price.php:46
msgid "Subscription Periods"
msgstr ""

#: templates/admin/deprecated/html-variation-price.php:59
msgctxt "Edit product screen, between the Billing Period and Subscription Length dropdowns"
msgid "for"
msgstr ""

#: templates/admin/deprecated/html-variation-price.php:69
msgid "Subscription Length"
msgstr ""

#: templates/admin/deprecated/html-variation-price.php:85
msgid "Sign-up Fee (%s)"
msgstr ""

#: templates/admin/deprecated/html-variation-price.php:97
#: templates/admin/deprecated/html-variation-price.php:104
msgid "Free Trial"
msgstr ""

#: templates/admin/deprecated/html-variation-price.php:105
msgctxt "example number of days / weeks / months"
msgid "e.g. 3"
msgstr ""

#. translators: placeholder is trial period validation message if passed an invalid value (e.g. "Trial period can not exceed 4 weeks")
#: templates/admin/deprecated/html-variation-price.php:118
#: templates/admin/html-variation-price.php:27
msgctxt "Trial period dropdown's description in pricing fields"
msgid "An optional period of time to wait before charging the first recurring payment. Any sign up fee will still be charged at the outset of the subscription. %s"
msgstr ""

#: templates/admin/deprecated/html-variation-synchronisation.php:30
msgid "Synchronise Renewals"
msgstr ""

#: templates/admin/deprecated/order-shipping-html.php:8
msgid "Label"
msgstr ""

#: templates/admin/deprecated/order-shipping-html.php:13
msgid "Shipping Method"
msgstr ""

#: templates/admin/deprecated/order-shipping-html.php:34
#: templates/admin/deprecated/order-shipping-html.php:36
msgid "Other"
msgstr ""

#: templates/admin/deprecated/order-tax-html.php:17
msgid "Recurring Sales Tax:"
msgstr ""

#: templates/admin/deprecated/order-tax-html.php:21
msgid "Shipping Tax:"
msgstr ""

#: templates/admin/html-failed-scheduled-action-notice.php:21
msgid "An error has occurred while processing a recent subscription related event. For steps on how to fix the affected subscription and to learn more about the possible causes of this error, please read our guide %1$shere%2$s."
msgid_plural "An error has occurred while processing recent subscription related events. For steps on how to fix the affected subscriptions and to learn more about the possible causes of this error, please read our guide %1$shere%2$s."
msgstr[0] ""
msgstr[1] ""

#: templates/admin/html-failed-scheduled-action-notice.php:31
msgid "Affected event:"
msgid_plural "Affected events:"
msgstr[0] ""
msgstr[1] ""

#. translators: $1 the log file name $2 and $3 are opening and closing link tags, respectively.
#: templates/admin/html-failed-scheduled-action-notice.php:38
msgid "To see further details about these errors, view the %1$s log file from the %2$sWooCommerce logs screen.%2$s"
msgstr ""

#: templates/admin/html-variation-price.php:31
msgid "Subscription trial period:"
msgstr ""

#: templates/admin/html-variation-price.php:49
msgid "Billing interval:"
msgstr ""

#: templates/admin/html-variation-price.php:56
msgid "Billing Period:"
msgstr ""

#: templates/admin/html-variation-price.php:67
msgctxt "Subscription Length dropdown's description in pricing fields"
msgid "Automatically expire the subscription after this length of time. This length is in addition to any free trial or amount of time provided before a synchronised first renewal date."
msgstr ""

#: templates/cart/cart-recurring-shipping.php:32
msgid "Shipping costs will be calculated once you have provided your address."
msgstr ""

#: templates/cart/cart-recurring-shipping.php:34
msgid "There are no shipping methods available. Please double check your address, or contact us if you need any help."
msgstr ""

#: templates/checkout/form-change-payment-method.php:20
#: templates/emails/email-order-details.php:36
#: templates/myaccount/subscription-totals-table.php:21
msgctxt "table headings in notification email"
msgid "Product"
msgstr ""

#: templates/checkout/form-change-payment-method.php:21
#: templates/emails/email-order-details.php:37
msgctxt "table headings in notification email"
msgid "Quantity"
msgstr ""

#: templates/checkout/form-change-payment-method.php:22
msgctxt "table headings in notification email"
msgid "Totals"
msgstr ""

#: templates/checkout/form-change-payment-method.php:47
msgctxt "text on button on checkout page"
msgid "Change payment method"
msgstr ""

#: templates/checkout/form-change-payment-method.php:49
msgctxt "text on button on checkout page"
msgid "Add payment method"
msgstr ""

#: templates/checkout/form-change-payment-method.php:82
msgid "Sorry, it seems no payment gateways support changing the recurring payment method. Please contact us if you require assistance or to make alternate arrangements."
msgstr ""

#. translators: $1: opening <strong> tag, $2: closing </strong> tag
#: templates/checkout/form-change-payment-method.php:91
msgid "Update the payment method used for %1$sall%2$s of my current subscriptions"
msgstr ""

#: templates/checkout/recurring-subscription-totals.php:18
#: templates/checkout/recurring-subscription-totals.php:19
msgid "Recurring total"
msgstr ""

#: templates/checkout/recurring-subtotals.php:18
#: templates/checkout/recurring-subtotals.php:19
msgid "Subtotal"
msgstr ""

#: templates/checkout/recurring-totals.php:17
msgid "Recurring totals"
msgstr ""

#. translators: placeholder is the subscription order number wrapped in <strong> tags
#: templates/checkout/subscription-receipt.php:18
#: templates/emails/plain/email-order-details.php:19
msgid "Subscription Number: %s"
msgstr ""

#. translators: placeholder is the subscription's next payment date (either human readable or normal date) wrapped in <strong> tags
#: templates/checkout/subscription-receipt.php:24
msgid "Next Payment Date: %s"
msgstr ""

#. translators: placeholder is the formatted total to be paid for the subscription wrapped in <strong> tags
#: templates/checkout/subscription-receipt.php:30
msgid "Total: %s"
msgstr ""

#. translators: placeholder is the display name of the payment method
#: templates/checkout/subscription-receipt.php:37
msgid "Payment Method: %s"
msgstr ""

#. translators: $1: customer's billing first name and last name
#: templates/emails/admin-new-renewal-order.php:16
#: templates/emails/plain/admin-new-renewal-order.php:16
msgctxt "Used in admin email: new renewal order"
msgid "You have received a subscription renewal order from %1$s. Their order is as follows:"
msgstr ""

#. translators: $1: customer's first name and last name, $2: how many subscriptions customer switched
#: templates/emails/admin-new-switch-order.php:18
#: templates/emails/plain/admin-new-switch-order.php:18
msgctxt "Used in switch notification admin email"
msgid "Customer %1$s has switched their subscription. The details of their new subscription are as follows:"
msgid_plural "Customer %1$s has switched %2$d of their subscriptions. The details of their new subscriptions are as follows:"
msgstr[0] ""
msgstr[1] ""

#: templates/emails/admin-new-switch-order.php:20
msgid "Switch Order Details"
msgstr ""

#: templates/emails/admin-new-switch-order.php:28
#: templates/emails/customer-completed-switch-order.php:26
msgid "New subscription details"
msgstr ""

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s: lowercase human time diff in the form returned by wcs_get_human_time_diff(), e.g. 'in 12 hours'
#: templates/emails/admin-payment-retry.php:23
msgctxt "In customer renewal invoice email"
msgid "The automatic recurring payment for order #%d from %s has failed. The payment will be retried %3$s."
msgstr ""

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s: lowercase human time diff in the form returned by wcs_get_human_time_diff(), e.g. 'in 12 hours'
#: templates/emails/admin-payment-retry.php:24
#: templates/emails/plain/admin-payment-retry.php:21
msgid "The renewal order is as follows:"
msgstr ""

#. translators: $1: customer's billing first name and last name
#: templates/emails/cancelled-subscription.php:16
#: templates/emails/plain/cancelled-subscription.php:16
msgid "A subscription belonging to %1$s has been cancelled. Their subscription's details are as follows:"
msgstr ""

#: templates/emails/cancelled-subscription.php:22
#: templates/emails/email-order-details.php:38
#: templates/emails/expired-subscription.php:22
#: templates/emails/on-hold-subscription.php:22
msgctxt "table headings in notification email"
msgid "Price"
msgstr ""

#: templates/emails/cancelled-subscription.php:23
#: templates/emails/expired-subscription.php:23
#: templates/emails/on-hold-subscription.php:23
#: wcs-functions.php:306
msgctxt "table heading"
msgid "Last Order Date"
msgstr ""

#: templates/emails/cancelled-subscription.php:24
msgctxt "table headings in notification email"
msgid "End of Prepaid Term"
msgstr ""

#: templates/emails/cancelled-subscription.php:41
#: templates/emails/expired-subscription.php:41
#: templates/emails/on-hold-subscription.php:41
msgid "-"
msgstr ""

#. translators: %s: Customer first name
#: templates/emails/customer-completed-renewal-order.php:17
#: templates/emails/customer-completed-switch-order.php:17
#: templates/emails/customer-on-hold-renewal-order.php:17
#: templates/emails/customer-payment-retry.php:16
#: templates/emails/customer-processing-renewal-order.php:17
#: templates/emails/customer-renewal-invoice.php:16
#: templates/emails/plain/customer-completed-renewal-order.php:16
#: templates/emails/plain/customer-completed-switch-order.php:16
#: templates/emails/plain/customer-on-hold-renewal-order.php:16
#: templates/emails/plain/customer-payment-retry.php:16
#: templates/emails/plain/customer-processing-renewal-order.php:16
#: templates/emails/plain/customer-renewal-invoice.php:16
msgid "Hi %s,"
msgstr ""

#: templates/emails/customer-completed-renewal-order.php:18
#: templates/emails/plain/customer-completed-renewal-order.php:17
msgid "We have finished processing your subscription renewal order."
msgstr ""

#: templates/emails/customer-completed-switch-order.php:18
#: templates/emails/plain/customer-completed-switch-order.php:17
msgid "You have successfully changed your subscription items. Your new order and subscription details are shown below for your reference:"
msgstr ""

#: templates/emails/customer-on-hold-renewal-order.php:18
#: templates/emails/plain/customer-on-hold-renewal-order.php:17
msgid "Thanks for your renewal order. It’s on-hold until we confirm that payment has been received. In the meantime, here’s a reminder of your order:"
msgstr ""

#. translators: %s: lowercase human time diff in the form returned by wcs_get_human_time_diff(), e.g. 'in 12 hours'
#: templates/emails/customer-payment-retry.php:18
#: templates/emails/plain/customer-payment-retry.php:18
msgctxt "In customer renewal invoice email"
msgid "The automatic payment to renew your subscription has failed. We will retry the payment %s."
msgstr ""

#. translators: %1$s %2$s: link markup to checkout payment url, note: no full stop due to url at the end
#: templates/emails/customer-payment-retry.php:21
msgctxt "In customer renewal invoice email"
msgid "To reactivate the subscription now, you can also log in and pay for the renewal from your account page: %1$sPay Now &raquo;%2$s"
msgstr ""

#. translators: %s: Order number
#: templates/emails/customer-processing-renewal-order.php:19
#: templates/emails/plain/customer-processing-renewal-order.php:18
msgid "Just to let you know &mdash; we've received your subscription renewal order #%s, and it is now being processed:"
msgstr ""

#. translators: %1$s: name of the blog, %2$s: link to checkout payment url, note: no full stop due to url at the end
#: templates/emails/customer-renewal-invoice.php:22
#: templates/emails/plain/customer-renewal-invoice.php:20
msgctxt "In customer renewal invoice email"
msgid "An order has been created for you to renew your subscription on %1$s. To pay for this invoice please use the following link: %2$s"
msgstr ""

#: templates/emails/customer-renewal-invoice.php:24
#: templates/emails/customer-renewal-invoice.php:33
msgid "Pay Now &raquo;"
msgstr ""

#. translators: %1$s: name of the blog, %2$s: link to checkout payment url, note: no full stop due to url at the end
#: templates/emails/customer-renewal-invoice.php:31
#: templates/emails/plain/customer-renewal-invoice.php:23
msgctxt "In customer renewal invoice email"
msgid "The automatic payment to renew your subscription with %1$s has failed. To reactivate the subscription, please log in and pay for the renewal from your account page: %2$s"
msgstr ""

#. translators: $1-$2: opening and closing <a> tags $3: order's order number $4: date of order in <time> element
#: templates/emails/email-order-details.php:24
msgctxt "Used in email notification"
msgid "%1$sOrder #%3$s%2$s (%4$s)"
msgstr ""

#. translators: $1-$3: opening and closing <a> tags $2: subscription's order number
#: templates/emails/email-order-details.php:27
msgctxt "Used in email notification"
msgid "Subscription %1$s#%2$s%3$s"
msgstr ""

#: templates/emails/email-order-details.php:61
msgid "Note:"
msgstr ""

#. translators: $1: customer's billing first name and last name
#: templates/emails/expired-subscription.php:16
#: templates/emails/plain/expired-subscription.php:16
msgid "A subscription belonging to %1$s has expired. Their subscription's details are as follows:"
msgstr ""

#: templates/emails/expired-subscription.php:24
msgctxt "table headings in notification email"
msgid "End Date"
msgstr ""

#. translators: $1: customer's billing first name and last name
#: templates/emails/on-hold-subscription.php:16
#: templates/emails/plain/on-hold-subscription.php:16
msgid "A subscription belonging to %1$s has been suspended by the user. Their subscription's details are as follows:"
msgstr ""

#: templates/emails/on-hold-subscription.php:24
msgctxt "table headings in notification email"
msgid "Date Suspended"
msgstr ""

#. translators: %1$s: an order number, %2$s: the customer's full name, %3$s: lowercase human time diff in the form returned by wcs_get_human_time_diff(), e.g. 'in 12 hours'
#: templates/emails/plain/admin-payment-retry.php:20
msgctxt "In customer renewal invoice email"
msgid "The automatic recurring payment for order #%1$s from %2$s has failed. The payment will be retried %3$s."
msgstr ""

#. translators: placeholder is last time subscription was paid
#: templates/emails/plain/cancelled-subscription.php:32
#: templates/emails/plain/expired-subscription.php:32
msgid "Last Order Date: %s"
msgstr ""

#. translators: placeholder is localised date string
#: templates/emails/plain/cancelled-subscription.php:39
msgid "End of Prepaid Term: %s"
msgstr ""

#: templates/emails/plain/cancelled-subscription.php:44
#: templates/emails/plain/expired-subscription.php:44
#: templates/emails/plain/on-hold-subscription.php:40
msgctxt "in plain emails for subscription information"
msgid "View Subscription: %s"
msgstr ""

#. translators: placeholder is order's view url
#: templates/emails/plain/customer-completed-switch-order.php:24
msgid "View your order: %s"
msgstr ""

#. translators: placeholder is subscription's view url
#: templates/emails/plain/customer-completed-switch-order.php:35
msgid "View your subscription: %s"
msgstr ""

#. translators: %1$s: link to checkout payment url, note: no full stop due to url at the end
#: templates/emails/plain/customer-payment-retry.php:21
msgctxt "In customer renewal invoice email"
msgid "To reactivate the subscription now, you can also log in and pay for the renewal from your account page: %1$s"
msgstr ""

#: templates/emails/plain/email-order-details.php:16
msgid "Order number: %s"
msgstr ""

#: templates/emails/plain/email-order-details.php:17
msgid "Order date: %s"
msgstr ""

#. translators: placeholder is localised date string
#: templates/emails/plain/expired-subscription.php:39
msgid "End Date: %s"
msgstr ""

#. translators: placeholder is last time subscription was paid
#: templates/emails/plain/on-hold-subscription.php:32
msgid "Last Order: %s"
msgstr ""

#. translators: placeholder is localised date string
#: templates/emails/plain/on-hold-subscription.php:36
msgid "Date Suspended: %s"
msgstr ""

#: templates/emails/plain/subscription-info.php:20
#: templates/emails/subscription-info.php:21
msgid "Subscription information"
msgstr ""

#. translators: placeholder is subscription's number
#: templates/emails/plain/subscription-info.php:25
msgctxt "in plain emails for subscription information"
msgid "Subscription: %s"
msgstr ""

#. translators: placeholder is either view or edit url for the subscription
#: templates/emails/plain/subscription-info.php:27
msgctxt "in plain emails for subscription information"
msgid "View subscription: %s"
msgstr ""

#. translators: placeholder is localised start date
#: templates/emails/plain/subscription-info.php:29
msgctxt "in plain emails for subscription information"
msgid "Start date: %s"
msgstr ""

#: templates/emails/plain/subscription-info.php:31
msgctxt "Used as end date for an indefinite subscription"
msgid "When Cancelled"
msgstr ""

#. translators: placeholder is localised end date, or "when cancelled"
#: templates/emails/plain/subscription-info.php:33
msgctxt "in plain emails for subscription information"
msgid "End date: %s"
msgstr ""

#. translators: placeholder is the formatted order total for the subscription
#: templates/emails/plain/subscription-info.php:35
msgctxt "in plain emails for subscription information"
msgid "Recurring price: %s"
msgstr ""

#: templates/emails/plain/subscription-info.php:38
#: templates/emails/subscription-info.php:42
msgid "Next payment: %s"
msgstr ""

#. Translators: Placeholder is the My Account URL.
#: templates/emails/plain/subscription-info.php:52
msgid "This subscription is set to renew automatically using your payment method on file. You can manage or cancel this subscription from your my account page. %s"
msgid_plural "These subscriptions are set to renew automatically using your payment method on file. You can manage or cancel your subscriptions from your my account page. %s"
msgstr[0] ""
msgstr[1] ""

#: templates/emails/subscription-info.php:25
msgctxt "subscription ID table heading"
msgid "ID"
msgstr ""

#: templates/emails/subscription-info.php:26
msgctxt "table heading"
msgid "Start date"
msgstr ""

#: templates/emails/subscription-info.php:27
msgctxt "table heading"
msgid "End date"
msgstr ""

#: templates/emails/subscription-info.php:28
msgctxt "table heading"
msgid "Recurring total"
msgstr ""

#: templates/emails/subscription-info.php:35
msgctxt "subscription number in email table. (eg: #106)"
msgid "#%s"
msgstr ""

#: templates/emails/subscription-info.php:37
msgctxt "Used as end date for an indefinite subscription"
msgid "When cancelled"
msgstr ""

#. Translators: Placeholders are opening and closing My Account link tags.
#: templates/emails/subscription-info.php:58
msgid "This subscription is set to renew automatically using your payment method on file. You can manage or cancel this subscription from your %smy account page%s."
msgid_plural "These subscriptions are set to renew automatically using your payment method on file. You can manage or cancel your subscriptions from your %smy account page%s."
msgstr[0] ""
msgstr[1] ""

#: templates/html-early-renewal-modal-content.php:23
msgid "By renewing your subscription early your next payment will be %s."
msgstr ""

#: templates/html-early-renewal-modal-content.php:28
msgid "By renewing your subscription early, your scheduled next payment on %s will be cancelled."
msgstr ""

#: templates/html-early-renewal-modal-content.php:34
msgid "Want to renew early via the checkout? Click %shere.%s"
msgstr ""

#: templates/myaccount/my-subscriptions.php:23
#: templates/myaccount/related-subscriptions.php:23
#: templates/myaccount/related-subscriptions.php:39
msgctxt "table heading"
msgid "Next payment"
msgstr ""

#: templates/myaccount/my-subscriptions.php:33
#: templates/myaccount/related-subscriptions.php:31
msgid "ID"
msgstr ""

#: templates/myaccount/my-subscriptions.php:40
#: wcs-functions.php:305
msgctxt "table heading"
msgid "Next Payment"
msgstr ""

#: templates/myaccount/my-subscriptions.php:46
#: templates/myaccount/related-orders.php:53
#: templates/myaccount/related-subscriptions.php:42
msgctxt "Used in data attribute. Escaped"
msgid "Total"
msgstr ""

#: templates/myaccount/my-subscriptions.php:50
#: templates/myaccount/related-orders.php:84
#: templates/myaccount/related-subscriptions.php:46
msgctxt "view a subscription"
msgid "View"
msgstr ""

#: templates/myaccount/my-subscriptions.php:61
msgid "Previous"
msgstr ""

#: templates/myaccount/my-subscriptions.php:65
msgid "Next"
msgstr ""

#: templates/myaccount/my-subscriptions.php:72
msgid "You have reached the end of subscriptions. Go to the %sfirst page%s."
msgstr ""

#: templates/myaccount/my-subscriptions.php:74
msgid "You have no active subscriptions."
msgstr ""

#: templates/myaccount/my-subscriptions.php:77
msgid "Browse products"
msgstr ""

#: templates/myaccount/related-orders.php:15
msgid "Related orders"
msgstr ""

#: templates/myaccount/related-orders.php:22
msgid "Order"
msgstr ""

#. translators: $1: formatted order total for the order, $2: number of items bought
#: templates/myaccount/related-orders.php:56
msgid "%1$s for %2$d item"
msgid_plural "%1$s for %2$d items"
msgstr[0] ""
msgstr[1] ""

#: templates/myaccount/related-orders.php:65
msgctxt "pay for a subscription"
msgid "Pay"
msgstr ""

#: templates/myaccount/related-subscriptions.php:15
msgid "Related subscriptions"
msgstr ""

#: templates/myaccount/subscription-details.php:24
msgctxt "customer subscription table header"
msgid "Start date"
msgstr ""

#: templates/myaccount/subscription-details.php:25
msgctxt "customer subscription table header"
msgid "Last order date"
msgstr ""

#: templates/myaccount/subscription-details.php:26
msgctxt "customer subscription table header"
msgid "Next payment date"
msgstr ""

#: templates/myaccount/subscription-details.php:27
msgctxt "customer subscription table header"
msgid "End date"
msgstr ""

#: templates/myaccount/subscription-details.php:28
msgctxt "customer subscription table header"
msgid "Trial end date"
msgstr ""

#: templates/myaccount/subscription-details.php:42
msgid "Auto renew"
msgstr ""

#: templates/myaccount/subscription-details.php:50
msgid "Enable auto renew"
msgstr ""

#: templates/myaccount/subscription-details.php:57
msgid "Disable auto renew"
msgstr ""

#: templates/myaccount/subscription-details.php:62
msgid "Using the auto-renewal toggle is disabled while in staging mode."
msgstr ""

#: templates/myaccount/subscription-details.php:71
msgid "Payment"
msgstr ""

#: templates/myaccount/subscription-details.php:81
msgid "Actions"
msgstr ""

#: templates/myaccount/subscription-details.php:94
msgid "Subscription updates"
msgstr ""

#: templates/myaccount/subscription-details.php:100
msgctxt "date on subscription updates list. Will be localized"
msgid "l jS \\o\\f F Y, h:ia"
msgstr ""

#: templates/myaccount/subscription-totals-table.php:35
msgid "Are you sure you want remove this item from your subscription?"
msgstr ""

#: templates/myaccount/subscription-totals.php:23
msgid "Subscription totals"
msgstr ""

#: templates/single-product/add-to-cart/subscription.php:32
#: templates/single-product/add-to-cart/variable-subscription.php:30
msgid "You have an active subscription to this product already."
msgstr ""

#: templates/single-product/add-to-cart/variable-subscription.php:23
msgid "This product is currently out of stock and unavailable."
msgstr ""

#: templates/single-product/add-to-cart/variable-subscription.php:34
msgid "You have added a variation of this product to the cart already."
msgstr ""

#: templates/single-product/add-to-cart/variable-subscription.php:45
msgid "Clear"
msgstr ""

#: wcs-functions.php:130
msgctxt "Error message while creating a subscription"
msgid "Invalid created date. The date must be a string and of the format: \"Y-m-d H:i:s\"."
msgstr ""

#: wcs-functions.php:132
msgctxt "Error message while creating a subscription"
msgid "Subscription created date must be before current day."
msgstr ""

#: wcs-functions.php:137
msgctxt "Error message while creating a subscription"
msgid "Invalid date. The date must be a string and of the format: \"Y-m-d H:i:s\"."
msgstr ""

#: wcs-functions.php:142
msgctxt "Error message while creating a subscription"
msgid "Invalid subscription customer_id."
msgstr ""

#. translators: placeholder is order date parsed by strftime
#: wcs-functions.php:163
msgctxt "The post title for the new subscription"
msgid "Subscription &ndash; %s"
msgstr ""

#: wcs-functions.php:234
msgctxt "Subscription status"
msgid "On hold"
msgstr ""

#: wcs-functions.php:238
msgctxt "Subscription status"
msgid "Pending Cancellation"
msgstr ""

#: wcs-functions.php:254
msgid "Can not get status name. Status is not a string."
msgstr ""

#: wcs-functions.php:277
msgid "Can not get address type display name. Address type is not a string."
msgstr ""

#: wcs-functions.php:303
msgctxt "table heading"
msgid "Start Date"
msgstr ""

#: wcs-functions.php:304
msgctxt "table heading"
msgid "Trial End"
msgstr ""

#: wcs-functions.php:307
msgctxt "table heading"
msgid "Cancelled Date"
msgstr ""

#: wcs-functions.php:308
msgctxt "table heading"
msgid "End Date"
msgstr ""

#: wcs-functions.php:343
msgid "Date type is not a string."
msgstr ""

#: wcs-functions.php:345
msgid "Date type can not be an empty string."
msgstr ""

#: woocommerce-subscriptions.php:298
msgctxt "custom post type setting"
msgid "Add Subscription"
msgstr ""

#: woocommerce-subscriptions.php:299
msgctxt "custom post type setting"
msgid "Add New Subscription"
msgstr ""

#: woocommerce-subscriptions.php:300
msgctxt "custom post type setting"
msgid "Edit"
msgstr ""

#: woocommerce-subscriptions.php:301
msgctxt "custom post type setting"
msgid "Edit Subscription"
msgstr ""

#: woocommerce-subscriptions.php:302
msgctxt "custom post type setting"
msgid "New Subscription"
msgstr ""

#: woocommerce-subscriptions.php:303
#: woocommerce-subscriptions.php:304
msgctxt "custom post type setting"
msgid "View Subscription"
msgstr ""

#: woocommerce-subscriptions.php:307
msgctxt "custom post type setting"
msgid "No Subscriptions found in trash"
msgstr ""

#: woocommerce-subscriptions.php:308
msgctxt "custom post type setting"
msgid "Parent Subscriptions"
msgstr ""

#: woocommerce-subscriptions.php:311
msgid "This is where subscriptions are stored."
msgstr ""

#: woocommerce-subscriptions.php:356
msgid "No Subscriptions found"
msgstr ""

#: woocommerce-subscriptions.php:358
msgid "Subscriptions will appear here for you to view and manage once purchased by a customer."
msgstr ""

#. translators: placeholders are opening and closing link tags
#: woocommerce-subscriptions.php:360
msgid "%1$sLearn more about managing subscriptions &raquo;%2$s"
msgstr ""

#. translators: placeholders are opening and closing link tags
#: woocommerce-subscriptions.php:362
msgid "%1$sAdd a subscription product &raquo;%2$s"
msgstr ""

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:379
msgctxt "post status label including post count"
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:381
msgctxt "post status label including post count"
msgid "Switched <span class=\"count\">(%s)</span>"
msgid_plural "Switched <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:383
msgctxt "post status label including post count"
msgid "Expired <span class=\"count\">(%s)</span>"
msgid_plural "Expired <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#. translators: placeholder is a post count.
#: woocommerce-subscriptions.php:385
msgctxt "post status label including post count"
msgid "Pending Cancellation <span class=\"count\">(%s)</span>"
msgid_plural "Pending Cancellation <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: woocommerce-subscriptions.php:429
msgid "To enable automatic renewals for this subscription, you will first need to add a payment method."
msgstr ""

#: woocommerce-subscriptions.php:429
msgid "Would you like to add a payment method now?"
msgstr ""

#. translators: placeholder is a number, this is for the teens
#. translators: placeholder is a number, numbers ending in 4-9, 0
#: woocommerce-subscriptions.php:721
#: woocommerce-subscriptions.php:738
msgid "%sth"
msgstr ""

#. translators: placeholder is a number, numbers ending in 1
#: woocommerce-subscriptions.php:726
msgid "%sst"
msgstr ""

#. translators: placeholder is a number, numbers ending in 2
#: woocommerce-subscriptions.php:730
msgid "%snd"
msgstr ""

#. translators: placeholder is a number, numbers ending in 3
#: woocommerce-subscriptions.php:734
msgid "%srd"
msgstr ""

#. translators: 1$-2$: opening and closing <strong> tags, 3$-4$: link tags, takes to woocommerce plugin on wp.org, 5$-6$: opening and closing link tags, leads to plugins.php in admin
#: woocommerce-subscriptions.php:773
msgid "%1$sWooCommerce Subscriptions is inactive.%2$s The %3$sWooCommerce plugin%4$s must be active for WooCommerce Subscriptions to work. Please %5$sinstall & activate WooCommerce &raquo;%6$s"
msgstr ""

#. translators: 1$-2$: opening and closing <strong> tags, 3$: minimum supported WooCommerce version, 4$-5$: opening and closing link tags, leads to plugin admin
#: woocommerce-subscriptions.php:776
msgid "%1$sWooCommerce Subscriptions is inactive.%2$s This version of Subscriptions requires WooCommerce %3$s or newer. Please %4$supdate WooCommerce to version %3$s or newer &raquo;%5$s"
msgstr ""

#: woocommerce-subscriptions.php:807
msgid "Variable Subscription"
msgstr ""

#. translators: 1-2: opening/closing <b> tags, 3: Subscriptions version.
#: woocommerce-subscriptions.php:904
msgid "%1$sWarning!%2$s We can see the %1$sWooCommerce Subscriptions Early Renewal%2$s plugin is active. Version %3$s of %1$sWooCommerce Subscriptions%2$s comes with that plugin's functionality packaged into the core plugin. Please deactivate WooCommerce Subscriptions Early Renewal to avoid any conflicts."
msgstr ""

#: woocommerce-subscriptions.php:908
msgid "Installed Plugins"
msgstr ""

#. translators: 1$-2$: opening and closing <strong> tags. 3$-4$: opening and closing link tags for learn more. Leads to duplicate site article on docs. 5$-6$: Opening and closing link to production URL. 7$: Production URL .
#: woocommerce-subscriptions.php:978
msgid "It looks like this site has moved or is a duplicate site. %1$sWooCommerce Subscriptions%2$s has disabled automatic payments and subscription related emails on this site to prevent duplicate payments from a staging or test environment. %1$sWooCommerce Subscriptions%2$s considers %5$s%7$s%6$s to be the site's URL. %3$sLearn more &raquo;%4$s."
msgstr ""

#: woocommerce-subscriptions.php:991
msgid "Quit nagging me (but don't enable automatic payments)"
msgstr ""

#: woocommerce-subscriptions.php:996
msgid "Enable automatic payments"
msgstr ""

#: woocommerce-subscriptions.php:1203
msgid "Support"
msgstr ""

#. translators: placeholders are opening and closing tags. Leads to docs on version 2
#: woocommerce-subscriptions.php:1286
msgid "Warning! Version 2.0 is a major update to the WooCommerce Subscriptions extension. Before updating, please create a backup, update all WooCommerce extensions and test all plugins, custom code and payment gateways with version 2.0 on a staging site. %1$sLearn more about the changes in version 2.0 &raquo;%2$s"
msgstr ""

#. translators: placeholder is Subscriptions version number.
#: woocommerce-subscriptions.php:1302
msgid "Warning! You are running version %s of WooCommerce Subscriptions plugin code but your database has been upgraded to Subscriptions version 2.0. This will cause major problems on your store."
msgstr ""

#. translators: opening/closing <a> tags - linked to ticket form.
#: woocommerce-subscriptions.php:1304
msgid "Please upgrade the WooCommerce Subscriptions plugin to version 2.0 or newer immediately. If you need assistance, after upgrading to Subscriptions v2.0, please %1$sopen a support ticket%2$s."
msgstr ""

#: assets/src/js/utils/index.js:8
#: build/index.js:1
msgctxt "Used in recurring totals section in Cart. 2+ will need plural, 1 will need singular."
msgid "day"
msgid_plural "days"
msgstr[0] ""
msgstr[1] ""

#: assets/src/js/utils/index.js:15
#: build/index.js:1
msgctxt "Used in recurring totals section in Cart. 2+ will need plural, 1 will need singular."
msgid "week"
msgid_plural "weeks"
msgstr[0] ""
msgstr[1] ""

#: assets/src/js/utils/index.js:22
#: build/index.js:1
msgctxt "Used in recurring totals section in Cart. 2+ will need plural, 1 will need singular."
msgid "month"
msgid_plural "months"
msgstr[0] ""
msgstr[1] ""

#: assets/src/js/utils/index.js:29
#: build/index.js:1
msgctxt "Used in recurring totals section in Cart. 2+ will need plural, 1 will need singular."
msgid "year"
msgid_plural "years"
msgstr[0] ""
msgstr[1] ""

#: assets/src/js/utils/index.js:63
#: build/index.js:8
msgid "Daily recurring total"
msgstr ""

#: assets/src/js/utils/index.js:68
#: build/index.js:8
msgid "Weekly recurring total"
msgstr ""

#: assets/src/js/utils/index.js:73
#: build/index.js:8
msgid "Monthly recurring total"
msgstr ""

#: assets/src/js/utils/index.js:78
#: build/index.js:8
msgid "Yearly recurring total"
msgstr ""

#. translators: %1$s is week, month, year
#: assets/src/js/utils/index.js:87
#: build/index.js:10
msgid "Recurring total every 2nd %1$s"
msgstr ""

#. Translators: %1$s is week, month, year
#: assets/src/js/utils/index.js:97
#: build/index.js:12
msgid "Recurring total every 3rd %1$s"
msgstr ""

#. Translators: %1$d is number of weeks, months, days, years. %2$s is week, month, year
#: assets/src/js/utils/index.js:106
#: build/index.js:14
msgid "Recurring total every %1$dth %2$s"
msgstr ""

#. translators: %1$s Product name, %2$s Switch type (upgraded, downgraded, or crossgraded).
#: assets/src/js/utils/index.js:178
#: build/index.js:19
msgid "Upgrade"
msgstr ""

#: assets/src/js/utils/index.js:181
#: build/index.js:19
msgid "Downgrade"
msgstr ""

#: assets/src/js/utils/index.js:184
#: build/index.js:19
msgid "Crossgrade"
msgstr ""

#. translators: %1$s is the price of the product. %2$s is the separator used e.g "every" or "/", %3$d is the length, %4$s is week, month, year
#: build/index.js:3
msgid "%1$s %2$s %3$d %4$s"
msgstr ""

#. translators: %s selected shipping rate (ex: flat rate)
#: build/index.js:4
msgid "via %s"
msgstr ""

#. Translators: %1$s is a date.
#: build/index.js:6
msgid "Due: %1$s"
msgstr ""

#. Translators: %1$s is a date.
#: build/index.js:8
msgid "Starting: %1$s"
msgstr ""

#. Translators: %1$d is number of weeks, months, days, years. %2$s is week, month, year
#: build/index.js:14
msgid "Details"
msgstr ""

#: build/index.js:14
msgid "Total due today"
msgstr ""

#. translators: the word used to describe billing frequency, e.g. "fo1" 1 day or "for" 1 month.
#: build/index.js:15
msgid "for 1"
msgstr ""

#. translators: the word used to describe billing frequency, e.g. "for" 6 days or "for" 2 weeks.
#: build/index.js:16
msgid "for"
msgstr ""

#. translators: the word used to describe billing frequency, e.g. "every" 6 days or "every" 2 weeks.
#: build/index.js:17
msgid "every"
msgstr ""

#. translators: %s Product name.
#: build/index.js:18
msgid "%s (resubscription)"
msgstr ""

#. translators: %1$s Product name, %2$s Switch type (upgraded, downgraded, or crossgraded).
#: build/index.js:19
msgid "%1$s (%2$s)"
msgstr ""

#. translators: %s is the subscription price to pay immediately (ie: $10).
#: build/index.js:21
msgid "Due today %s"
msgstr ""

#. translators: %s is the subscription price to pay immediately (ie: $10).
#: build/index.js:23
msgid "%s due today"
msgstr ""
