msgid ""
msgstr ""
"Project-Id-Version: NUXY\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-05-23 06:46+0000\n"
"PO-Revision-Date: 2024-12-23 13:28+0000\n"
"Last-Translator: \n"
"Language-Team: Turkish\n"
"Language: tr_TR\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Loco https://localise.biz/\n"
"X-Loco-Version: 2.6.10; wp-6.5.4\n"
"X-Domain: nuxy"

#: taxonomy_meta/fields/image.php:10
msgid "Add image"
msgstr "Resim ekle"

#: metaboxes/metabox.php:248
msgid "Backup Font Family"
msgstr "Yedek Font Ailesi"

#: metaboxes/google_fonts.php:22
msgid "Black 900"
msgstr "Siyah 900"

#: metaboxes/google_fonts.php:23
msgid "Black 900 italic"
msgstr "Siyah 900 italik"

#: metaboxes/google_fonts.php:20
msgid "Bold 700"
msgstr "Kalın 700"

#: metaboxes/google_fonts.php:21
msgid "Bold 700 italic"
msgstr "Kalın 700 italik"

#: metaboxes/google_fonts.php:53
msgid "Capitalize"
msgstr "Büyük harfle yazmak"

#: metaboxes/google_fonts.php:43
msgid "Center"
msgstr "Merkez"

#: settings/settings.php:94
msgid "Choose Page"
msgstr "Sayfa Seç"

#: metaboxes/metabox.php:42
msgid "Choose User"
msgstr "Kullanıcı Seç"

#: metaboxes/fields/text.php:28
msgid "Copied"
msgstr "Kopyalandı"

#: metaboxes/metabox.php:254
msgid "Copy settings"
msgstr "Ayarları kopyala"

#: metaboxes/metabox.php:258
msgid "Couldn't copy settings"
msgstr "Ayarlar kopyalanamıyor"

#: metaboxes/google_fonts.php:29
msgid "Cyrillic"
msgstr "Kiril alfabesi"

#: metaboxes/google_fonts.php:30
msgid "Cyrillic ext"
msgstr "Kiril uzantısı"

#: metaboxes/fields/duration.php:35
msgid "Days"
msgstr "Günler"

#: metaboxes/google_fonts.php:41
msgid "Default"
msgstr "Varsayılan"

#: metaboxes/metabox.php:301
msgid "Delete"
msgstr "Silmek"

#: taxonomy_meta/fields/image.php:13
msgid "Delete image"
msgstr "Resimi sil"

#: metaboxes/metabox.php:266
msgid ""
"Download and store Google Fonts locally. Set the fonts in the typography."
msgstr ""
"Google Fonts'u yerel olarak indirin ve saklayın. Tipografideki yazı "
"tiplerini ayarlayın."

#: metaboxes/metabox.php:265
msgid "Download Google Fonts"
msgstr "Google Fontlarını İndirin"

#: metaboxes/fields/duration.php:24
msgid "duration"
msgstr "süre"

#: metaboxes/fields/repeater.php:25 metaboxes/fields/textarea.php:25
#: metaboxes/fields/text.php:27
msgid "Enter"
msgstr "Girin"

#: metaboxes/fields/number.php:22
msgid "Enter numbers..."
msgstr "Numaraları girin..."

#: helpers/file_upload.php:21
msgid "Error occurred, please try again"
msgstr "Hata oluştu, lütfen yeniden deneyin"

#: metaboxes/metabox.php:259
msgid "Export options"
msgstr "Dışa aktarma seçenekleri"

#: metaboxes/metabox.php:252
msgid "Font Color"
msgstr "Font Rengi"

#: metaboxes/metabox.php:247
msgid "Font Family"
msgstr "Font Ailesi"

#: metaboxes/metabox.php:243
msgid "Font size"
msgstr "Font boyutu"

#: metaboxes/metabox.php:250
msgid "Font Subsets"
msgstr "Font Alt Kümeleri"

#: metaboxes/metabox.php:263
msgid "Font Synchronize"
msgstr "Yazı Tipi Senkronizasyonu"

#: metaboxes/metabox.php:249
msgid "Font Weignt & Style"
msgstr "Font Ağırlığı ve Stili"

#: metaboxes/google_fonts.php:31
msgid "Greek"
msgstr "Yunan"

#: metaboxes/google_fonts.php:32
msgid "Greek ext"
msgstr "Yunanca uzantısı"

#: metaboxes/fields/duration.php:34
msgid "Hours"
msgstr "Saatler"

#. Author URI of the plugin
msgid "https://stylemixthemes.com"
msgstr "https://stylemixthemes.com"

#: metaboxes/fields/image.php:24
msgid "Image URL"
msgstr "Resim URLsi"

#: metaboxes/metabox.php:260
msgid "Import options"
msgstr "İçe aktarma seçenekleri"

#: metaboxes/metabox.php:255
msgid "Import settings"
msgstr "İçe aktarma ayarları"

#: metaboxes/metabox-display.php:27
msgid "Import/Export"
msgstr "İçe/Dışa aktarma"

#: helpers/file_upload.php:65
msgid "Invalid file extension"
msgstr "Geçersiz dosya uzantısı"

#: metaboxes/google_fonts.php:33
msgid "Latin"
msgstr "Latince"

#: metaboxes/google_fonts.php:34
msgid "Latin ext"
msgstr "Latince uzantısı"

#: metaboxes/google_fonts.php:42
msgid "Left"
msgstr "Sol"

#: metaboxes/metabox.php:246
msgid "Letter spacing"
msgstr "Harf boşluğu"

#: metaboxes/google_fonts.php:14
msgid "Light 300"
msgstr "Hafif 300"

#: metaboxes/google_fonts.php:15
msgid "Light 300 italic"
msgstr "Hafif 300 italik"

#: metaboxes/metabox.php:244
msgid "Line height"
msgstr "Çizgi kalınlığı"

#: metaboxes/google_fonts.php:52
msgid "Lowercase"
msgstr "Küçük harf"

#: metaboxes/google_fonts.php:18
msgid "Medium 500"
msgstr "Orta 500"

#: metaboxes/google_fonts.php:19
msgid "Medium 500 italic"
msgstr "Orta 500 italik"

#: metaboxes/fields/duration.php:33
msgid "Minutes"
msgstr "Dakika"

#: metaboxes/google_fonts.php:50
msgid "Normal"
msgstr "Normal"

#. Name of the plugin
msgid "NUXY"
msgstr "NUXY"

#: settings/settings.php:162
msgid "Oops, something went wrong"
msgstr "Oops, bir şeyler ters gitti"

#: helpers/file_upload.php:53
msgid "Please, select file"
msgstr "Lütfen dosya seçin"

#: metaboxes/metabox.php:302
msgid "Preview"
msgstr "Önizleme"

#: metaboxes/google_fonts.php:16
msgid "Regular 400"
msgstr "Normal 400"

#: metaboxes/google_fonts.php:17
msgid "Regular 400 italic"
msgstr "Normal 400 italik"

#: metaboxes/fields/image.php:27
msgid "Remove"
msgstr "Kaldır"

#: metaboxes/fields/image.php:26
msgid "Replace"
msgstr "Değiştir"

#: metaboxes/google_fonts.php:44
msgid "Right"
msgstr "Sağ"

#: settings/view/header.php:82
msgid "Save Settings"
msgstr "Ayarları Kaydet"

#: settings/settings.php:158
msgid "Saved!"
msgstr "Kaydedildi!"

#: settings/view/header.php:47
msgid "Search"
msgstr "Ara"

#: taxonomy_meta/fields/image.php:45
msgid "Select or Upload Media Of Your Chosen Persuasion"
msgstr "Seçtiğiniz İnanç Medyasını Ayıklayın veya Yükleyin"

#: settings/settings.php:159
msgid "Settings are changed"
msgstr "Ayarlar değiştirildi"

#: settings/settings.php:163
msgid "Settings are not changed"
msgstr "Ayarlar değiştirilmedi"

#: metaboxes/metabox.php:257
msgid "Settings copied to buffer"
msgstr "Ayarlar ara belleğe kopyalandı"

#: metaboxes/metabox.php:261
msgid "Sorry, no matching options."
msgstr "Üzgünüz, eşleşen seçenek yok."

#. Author of the plugin
msgid "StylemixThemes"
msgstr "StylemixThemes"

#: metaboxes/metabox.php:264
msgid ""
"Sync and update your fonts if they are displayed incorrectly on your website."
msgstr ""
"Yazı tipleriniz web sitenizde yanlış görüntüleniyorsa onları senkronize edin "
"ve güncelleyin."

#: metaboxes/metabox.php:262
msgid "Synchronize"
msgstr "Senkronize et"

#: metaboxes/metabox.php:251
msgid "Text Align"
msgstr "Metin Hizalama"

#: metaboxes/metabox.php:253
msgid "Text transform"
msgstr "Metin dönüştürme"

#: metaboxes/google_fonts.php:12
msgid "Thin 100"
msgstr "İnce 100"

#: metaboxes/google_fonts.php:13
msgid "Thin 100 italic"
msgstr "İnce 100 italik"

#: metaboxes/fields/image.php:25
msgid "Upload"
msgstr "Yükle"

#: metaboxes/google_fonts.php:51
msgid "Uppercase"
msgstr "Büyük harf"

#: taxonomy_meta/fields/image.php:47
msgid "Use this media"
msgstr "Bu medyayı kullanın"

#: metaboxes/google_fonts.php:35
msgid "Vietnamese"
msgstr "Vietnam"

#: metaboxes/metabox.php:256
msgid ""
"WARNING! This will overwrite all existing option values, please proceed with "
"caution!"
msgstr "UYARI! Bu, mevcut tüm seçenek değerlerinin üzerine yazacaktır "

#: metaboxes/metabox.php:245
msgid "Word spacing"
msgstr "Kelime aralığı"

#. Description of the plugin
msgid "WordPress Custom Fields & Theme Options with Vue.js."
msgstr "Vue.js ile WordPress Özel Alanlar ve Tema Seçenekleri."
