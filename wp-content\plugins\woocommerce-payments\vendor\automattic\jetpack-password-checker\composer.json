{"name": "automattic/jetpack-password-checker", "description": "Password Checker.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "automattic/wordbless": "@dev", "yoast/phpunit-polyfills": "1.0.4"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"], "post-install-cmd": "WorDBless\\Composer\\InstallDropin::copy", "post-update-cmd": "WorDBless\\Composer\\InstallDropin::copy"}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-password-checker", "textdomain": "jetpack-password-checker", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-password-checker/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "0.2.x-dev"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}