@font-face {
  font-family: 'stmlms';
  src:  url('fonts/stmlms.eot?4eslc4');
  src:  url('fonts/stmlms.eot?4eslc4#iefix') format('embedded-opentype'),
    url('fonts/stmlms.ttf?4eslc4') format('truetype'),
    url('fonts/stmlms.woff?4eslc4') format('woff'),
    url('fonts/stmlms.svg?4eslc4#stmlms') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="stmlms-"], [class*=" stmlms-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'stmlms' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.stmlms-grades:before {
  content: "\e9fd";
}
.stmlms-my-grades:before {
  content: "\e9fe";
}
.stmlms-grade-sheet:before {
  content: "\e9fc";
}
.stmlms-quiz-type:before {
  content: "\e9fa";
  color: #ffa800;
}
.stmlms-assignment-type:before {
  content: "\e9fb";
  color: #ff3945;
}
.stmlms-print:before {
  content: "\e9f9";
}
.stmlms-list-check:before {
  content: "\e9f3";
  color: #195ec8;
}
.stmlms-progress-wheel:before {
  content: "\e9f4";
  color: #ffa800;
}
.stmlms-done:before {
  content: "\e9f5";
  color: #227aff;
}
.stmlms-pause:before {
  content: "\e9f6";
  color: #4d5e6f;
}
.stmlms-edit:before {
  content: "\e9f7";
  color: #4d5e6f;
}
.stmlms-chart:before {
  content: "\e9f8";
  color: #4d5e6f;
}
.stmlms-mailchimp:before {
  content: "\e9f1";
}
.stmlms-posts:before {
  content: "\e9f2";
}
.stmlms-quotes:before {
  content: "\e9ef";
}
.stmlms-ellipse-square:before {
  content: "\e9f0";
}
.stmlms-testimonial:before {
  content: "\e9ee";
}
.stmlms-lesson:before {
  content: "\e9ec";
}
.stmlms-quiz-new:before {
  content: "\e9ed";
}
.stmlms-eye:before {
  content: "\e9dc";
}
.stmlms-instructor:before {
  content: "\e9dd";
}
.stmlms-logic:before {
  content: "\e9de";
}
.stmlms-medal:before {
  content: "\e9df";
}
.stmlms-menu-dots:before {
  content: "\e9e0";
}
.stmlms-money:before {
  content: "\e9e1";
}
.stmlms-settings:before {
  content: "\e9e2";
}
.stmlms-timer:before {
  content: "\e9e3";
}
.stmlms-user:before {
  content: "\e9e4";
}
.stmlms-check-2:before {
  content: "\e9e5";
}
.stmlms-book:before {
  content: "\e9e6";
}
.stmlms-calendar:before {
  content: "\e9e7";
}
.stmlms-cancel:before {
  content: "\e9e8";
}
.stmlms-cart:before {
  content: "\e9e9";
}
.stmlms-check:before {
  content: "\e9ea";
}
.stmlms-chevron_down:before {
  content: "\e9eb";
}
.stmlms-order:before {
  content: "\e9db";
}
.stmlms-refresh:before {
  content: "\e9da";
  color: #227aff;
}
.stmlms-medal-converted:before {
  content: "\e9d2";
  color: #195ec8;
}
.stmlms-users:before {
  content: "\e9d3";
  color: #195ec8;
}
.stmlms-star-2:before {
  content: "\e9d4";
  color: #195ec8;
}
.stmlms-cup:before {
  content: "\e9d5";
  color: #195ec8;
}
.stmlms-pen:before {
  content: "\e9d6";
  color: #ffa800;
}
.stmlms-stats:before {
  content: "\e9d7";
  color: #4d5e6f;
}
.stmlms-Layers:before {
  content: "\e9d8";
  color: #195ec8;
}
.stmlms-list-check-1:before {
  content: "\e9d9";
  color: #195ec8;
}
.stmlms-facebook:before {
  content: "\e9cd";
  color: #fff;
}
.stmlms-twitter:before {
  content: "\e9ce";
  color: #fff;
}
.stmlms-copy-link:before {
  content: "\e9cf";
  color: #fff;
}
.stmlms-linkedin:before {
  content: "\e9d0";
  color: #fff;
}
.stmlms-telegram:before {
  content: "\e9d1";
  color: #fff;
}
.stmlms-share:before {
  content: "\e9cc";
}
.stmlms-certificate-icon:before {
  content: "\e9c1";
  color: #4d5e6f;
}
.stmlms-duration-icon:before {
  content: "\e9c2";
  color: #4d5e6f;
}
.stmlms-enrolled-icon:before {
  content: "\e9c3";
  color: #4d5e6f;
}
.stmlms-lectures:before {
  content: "\e9c4";
  color: #4d5e6f;
}
.stmlms-level-icon:before {
  content: "\e9c5";
  color: #4d5e6f;
}
.stmlms-testings:before {
  content: "\e9c6";
  color: #4d5e6f;
}
.stmlms-updated:before {
  content: "\e9c7";
  color: #4d5e6f;
}
.stmlms-video-icon:before {
  content: "\e9c8";
  color: #4d5e6f;
}
.stmlms-access-on-mobile--TV:before {
  content: "\e9c9";
  color: #4d5e6f;
}
.stmlms-assignments:before {
  content: "\e9ca";
  color: #4d5e6f;
}
.stmlms-category-icon:before {
  content: "\e9cb";
  color: #4d5e6f;
}
.stmlms-heart-full:before {
  content: "\e9be";
  color: #ff3945;
}
.stmlms-star:before {
  content: "\e9bf";
  color: #ffa800;
}
.stmlms-heart:before {
  content: "\e9c0";
  color: #4d5e6f;
}
.stmlms-filter:before {
  content: "\e9bd";
}
.stmlms-envelope:before {
  content: "\e9bc";
  color: #227aff;
}
.stmlms-download-alt:before {
  content: "\e9bb";
  color: #fff;
}
.stmlms-upload-alt:before {
  content: "\e9ba";
  color: #227aff;
}
.stmlms-lnr-bullhorn:before {
  content: "\e9b9";
}
.stmlms-cloud-upload:before {
  content: "\e9ab";
}
.stmlms-heart11:before {
  content: "\e9ac";
}
.stmlms-license2:before {
  content: "\e9ad";
}
.stmlms-play1:before {
  content: "\e9ae";
}
.stmlms-book2:before {
  content: "\e9af";
}
.stmlms-laptop-phone:before {
  content: "\e9b0";
}
.stmlms-ghost:before {
  content: "\e9b1";
}
.stmlms-clock3:before {
  content: "\e9b2";
}
.stmlms-magnifier:before {
  content: "\e9b3";
}
.stmlms-check11:before {
  content: "\e9b4";
}
.stmlms-text-format:before {
  content: "\e9b5";
}
.stmlms-sort-amount-asc:before {
  content: "\e9b6";
}
.stmlms-pointer-right:before {
  content: "\e9b7";
}
.stmlms-pencil3:before {
  content: "\e9aa";
}
.stmlms-pencil:before {
  content: "\e90b";
}
.stmlms-pencil4:before {
  content: "\e977";
}
.stmlms-cog:before {
  content: "\e978";
}
.stmlms-trash:before {
  content: "\e979";
}
.stmlms-eye1:before {
  content: "\e97b";
}
.stmlms-user1:before {
  content: "\e97d";
}
.stmlms-chart-growth:before {
  content: "\e97e";
}
.stmlms-undo:before {
  content: "\e989";
}
.stmlms-undo2:before {
  content: "\e990";
}
.stmlms-sync:before {
  content: "\e991";
}
.stmlms-sync2:before {
  content: "\e992";
}
.stmlms-download2:before {
  content: "\e993";
}
.stmlms-cross:before {
  content: "\e994";
}
.stmlms-list:before {
  content: "\e995";
}
.stmlms-list2:before {
  content: "\e996";
}
.stmlms-list3:before {
  content: "\e997";
}
.stmlms-list4:before {
  content: "\e998";
}
.stmlms-cross2:before {
  content: "\e999";
}
.stmlms-chevron-up:before {
  content: "\e99a";
}
.stmlms-chevron-down:before {
  content: "\e99b";
}
.stmlms-chevron-left:before {
  content: "\e99c";
}
.stmlms-chevron-right:before {
  content: "\e99d";
}
.stmlms-arrow-left:before {
  content: "\e99e";
}
.stmlms-arrow-right:before {
  content: "\e99f";
}
.stmlms-arrow-return:before {
  content: "\e9a0";
}
.stmlms-checkmark-circle:before {
  content: "\e9a1";
}
.stmlms-cross-circle:before {
  content: "\e9a2";
}
.stmlms-plus-circle:before {
  content: "\e9a3";
}
.stmlms-chevron-up-circle:before {
  content: "\e9a4";
}
.stmlms-chevron-down-circle:before {
  content: "\e9a5";
}
.stmlms-grid:before {
  content: "\e9a6";
}
.stmlms-layers:before {
  content: "\e9a7";
}
.stmlms-chevron-up-square:before {
  content: "\e9a8";
}
.stmlms-chevron-down-square:before {
  content: "\e9a9";
}
.stmlms-linkedin-in:before {
  content: "\e9b8";
}
.stmlms-image:before {
  content: "\e975";
}
.stmlms-text:before {
  content: "\e976";
}
.stmlms-course-duration:before {
  content: "\e987";
}
.stmlms-current-date:before {
  content: "\e972";
}
.stmlms-details:before {
  content: "\e97a";
}
.stmlms-end-date:before {
  content: "\e97c";
}
.stmlms-instructor1:before {
  content: "\e97f";
}
.stmlms-progress:before {
  content: "\e980";
}
.stmlms-qr:before {
  content: "\e98a";
}
.stmlms-start-date:before {
  content: "\e98b";
}
.stmlms-student-code:before {
  content: "\e98c";
}
.stmlms-student-name:before {
  content: "\e98d";
}
.stmlms-co-instructor:before {
  content: "\e98e";
}
.stmlms-course-name:before {
  content: "\e98f";
}
.stmlms-shape:before {
  content: "\e988";
}
.stmlms-editor:before {
  content: "\e986";
  color: #4d5e6f;
}
.stmlms-link:before {
  content: "\e985";
  color: #fff;
}
.stmlms-pencil1:before {
  content: "\e984";
  color: #4d5e6f;
}
.stmlms-cert-horizontal:before {
  content: "\e982";
  color: #227aff;
}
.stmlms-cert-vertical:before {
  content: "\e983";
  color: #227aff;
}
.stmlms-delete-basket:before {
  content: "\e981";
  color: #fff;
}
.stmlms-number:before {
  content: "\e974";
}
.stmlms-plus:before {
  content: "\e973";
  color: #fff;
}
.stmlms-success:before {
  content: "\e971";
  color: #227aff;
}
.stmlms-social-goolge-plus:before {
  content: "\e96f";
}
.stmlms-social-x:before {
  content: "\e96d";
  color: #808c98;
}
.stmlms-social-facebook:before {
  content: "\e96e";
  color: #808c98;
}
.stmlms-social-instagram:before {
  content: "\e970";
  color: #808c98;
}
.stmlms-authorization:before {
  content: "\e96c";
}
.stmlms-arrow-down:before {
  content: "\e96b";
  color: #4d5e6f;
}
.stmlms-warning:before {
  content: "\e968";
  color: #ff3945;
}
.stmlms-camera:before {
  content: "\e969";
  color: #227aff;
}
.stmlms-mic:before {
  content: "\e96a";
  color: #227aff;
}
.stmlms-close-eye:before {
  content: "\e965";
  color: #808c98;
}
.stmlms-open-eye:before {
  content: "\e966";
  color: #808c98;
}
.stmlms-back-arrow:before {
  content: "\e967";
  color: #808c98;
}
.stmlms-arrow_top:before {
  content: "\e961";
}
.stmlms-arrow_right:before {
  content: "\e962";
}
.stmlms-arrow_bottom:before {
  content: "\e963";
}
.stmlms-arrow_left:before {
  content: "\e964";
}
.stmlms-check1:before {
  content: "\e95e";
  color: #227aff;
}
.stmlms-time:before {
  content: "\e95f";
  color: #227aff;
}
.stmlms-question_new:before {
  content: "\e960";
  color: #227aff;
}
.stmlms-lock:before {
  content: "\e95d";
  color: #fff;
}
.stmlms-sun:before {
  content: "\e95b";
  color: #4d5e6f;
}
.stmlms-moon:before {
  content: "\e95c";
  color: #fff;
}
.stmlms-loader:before {
  content: "\e95a";
  color: #227aff;
}
.stmlms-drag:before {
  content: "\e959";
  color: #b3bac2;
}
.stmlms-upload:before {
  content: "\e958";
  color: #227aff;
}
.stmlms-explain:before {
  content: "\e957";
  color: #4d5e6f;
}
.stmlms-trash1:before {
  content: "\e956";
  color: #4d5e6f;
}
.stmlms-play:before {
  content: "\e955";
  color: #227aff;
}
.stmlms-search:before {
  content: "\e953";
  color: #4d5e6f;
}
.stmlms-send:before {
  content: "\e954";
  color: #fff;
}
.stmlms-btn-add:before {
  content: "\e952";
}
.stmlms-chevron_up:before {
  content: "\e951";
  color: #fff;
}
.stmlms-checked:before {
  content: "\e94e";
  color: #fff;
}
.stmlms-chevron-left1:before {
  content: "\e94f";
  color: #4d5e6f;
}
.stmlms-chevron-right1:before {
  content: "\e950";
  color: #4d5e6f;
}
.stmlms-settings1:before {
  content: "\e94d";
  color: #4d5e6f;
}
.stmlms-back:before {
  content: "\e94b";
  color: #808c98;
}
.stmlms-chat:before {
  content: "\e94c";
  color: #4d5e6f;
}
.stmlms-close:before {
  content: "\e94a";
  color: #4d5e6f;
}
.stmlms-toggler:before {
  content: "\e949";
  color: #4d5e6f;
}
.stmlms-download:before {
  content: "\e941";
  color: #227aff;
}
.stmlms-ms-slider:before {
  content: "\e93b";
}
.stmlms-featured-teacher-old:before {
  content: "\e939";
}
.stmlms-instructors-carousel-old:before {
  content: "\e93a";
}
.stmlms-authlinks-old:before {
  content: "\e93c";
}
.stmlms-recent-courses-old:before {
  content: "\e93d";
}
.stmlms-single-course-carousel-old:before {
  content: "\e93e";
}
.stmlms-testimonials-old:before {
  content: "\e93f";
}
.stmlms-zoom-meeting-old:before {
  content: "\e940";
}
.stmlms-zoom-webinar-old:before {
  content: "\e942";
}
.stmlms-certificate-checker-old:before {
  content: "\e943";
}
.stmlms-course-search-box-old:before {
  content: "\e944";
}
.stmlms-courses-carousel-old:before {
  content: "\e945";
}
.stmlms-courses-categories-old:before {
  content: "\e946";
}
.stmlms-courses-grid-old:before {
  content: "\e947";
}
.stmlms-cta-old:before {
  content: "\e948";
}
.stmlms-courses-carousel:before {
  content: "\e92d";
}
.stmlms-courses-categories:before {
  content: "\e92e";
}
.stmlms-courses-grid:before {
  content: "\e92f";
}
.stmlms-featured-teacher:before {
  content: "\e930";
}
.stmlms-membership-plans:before {
  content: "\e931";
}
.stmlms-recent-courses:before {
  content: "\e932";
}
.stmlms-single-course-carousel:before {
  content: "\e933";
}
.stmlms-zoom-meeting:before {
  content: "\e934";
}
.stmlms-zoom-meetings:before {
  content: "\e935";
}
.stmlms-zoom-webinar:before {
  content: "\e936";
}
.stmlms-call-to-action:before {
  content: "\e937";
}
.stmlms-certificate-checker:before {
  content: "\e938";
}
.stmlms-subscription:before {
  content: "\e927";
  color: #4d5e6f;
}
.stmlms-members:before {
  content: "\e928";
  color: #4d5e6f;
}
.stmlms-levels:before {
  content: "\e929";
  color: #4d5e6f;
}
.stmlms-views:before {
  content: "\e92a";
  color: #4d5e6f;
}
.stmlms-list1:before {
  content: "\e92b";
  color: #4d5e6f;
}
.stmlms-duration:before {
  content: "\e92c";
}
.stmlms-authlinks:before {
  content: "\e926";
}
.stmlms-course-search-box:before {
  content: "\e922";
}
.stmlms-testimonials:before {
  content: "\e923";
}
.stmlms-cta:before {
  content: "\e924";
}
.stmlms-instructors-carousel:before {
  content: "\e925";
}
.stmlms-arrow-left1:before {
  content: "\e920";
  color: #fff;
}
.stmlms-arrow-right1:before {
  content: "\e921";
  color: #fff;
}
.stmlms-star1:before {
  content: "\e91e";
  color: #b3bac2;
}
.stmlms-star_filled:before {
  content: "\e91f";
  color: #ffa800;
}
.stmlms-reset:before {
  content: "\e91d";
}
.stmlms-not_found_courses:before {
  content: "\e91c";
}
.stmlms-camera1:before {
  content: "\e914";
}
.stmlms-cooperation:before {
  content: "\e915";
}
.stmlms-disk-schedule:before {
  content: "\e916";
}
.stmlms-headphones:before {
  content: "\e917";
}
.stmlms-heart1:before {
  content: "\e918";
}
.stmlms-money-bag:before {
  content: "\e919";
}
.stmlms-mouse:before {
  content: "\e91a";
}
.stmlms-presentation:before {
  content: "\e91b";
}
.stmlms-question:before {
  content: "\e913";
}
.stmlms-pplc_cl:before {
  content: "\e90e";
}
.stmlms-clock_cl:before {
  content: "\e90f";
}
.stmlms-books_cl:before {
  content: "\e910";
}
.stmlms-playvideo:before {
  content: "\e911";
}
.stmlms-ladder:before {
  content: "\e912";
}
.stmlms-slides:before {
  content: "\e90c";
}
.stmlms-quiz:before {
  content: "\e90d";
}
.stmlms-qanda:before {
  content: "\e900";
}
.stmlms-lms-clocks:before {
  content: "\e901";
}
.stmlms-cats:before {
  content: "\e902";
}
.stmlms-level:before {
  content: "\e903";
}
.stmlms-hamburger:before {
  content: "\e904";
}
.stmlms-calendar1:before {
  content: "\e905";
}
.stmlms-screen:before {
  content: "\e906";
}
.stmlms-world:before {
  content: "\e907";
}
.stmlms-user11:before {
  content: "\e908";
}
.stmlms-case:before {
  content: "\e909";
}
.stmlms-horn:before {
  content: "\e90a";
}
