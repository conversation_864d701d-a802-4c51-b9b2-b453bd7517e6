/**
 * jQuery Unveil
 * A very lightweight jQuery plugin to lazy load images
 * http://luis-almeida.github.com/unveil
 *
 * Licensed under the MIT license.
 * Copyright 2013 Luís <PERSON>
 * https://github.com/luis-almeida
 */

;(function($) {
  "use strict";
  $.fn.unveil = function(threshold, callback) {

    var $w = $(window),
        th = threshold || 0,
        retina = window.devicePixelRatio > 1,
        attrib = retina? "data-src-retina" : "data-src",
        attrsrcset = "data-srcset",
        attrsizes = "data-sizes",
        images = this,
        loaded;

    this.one("unveil", function() {
      var source = this.getAttribute(attrib);
      source = source || this.getAttribute("data-src");
      if (source) {
        this.setAttribute("src", source);

        var source_srcset = this.getAttribute(attrsrcset);
        source_srcset = source_srcset || this.getAttribute("data-srcset");
        if (source_srcset) {
          this.setAttribute("srcset", source_srcset);
        }

        var source_sizes = this.getAttribute(attrsizes);
        source_sizes = source_sizes || this.getAttribute("data-sizes");
        if (source_sizes) {
          this.setAttribute("sizes", source_sizes);
        }

        if (typeof callback === "function") callback.call(this);
      }
    });

    function unveil() {
      var inview = images.filter(function() {
        var $e = $(this);
        if ($e.is(":hidden")) return;

        var wt = $w.scrollTop(),
            wb = wt + $w.height(),
            et = $e.offset().top,
            eb = et + $e.height();

        return eb >= wt - th && et <= wb + th;
      });

      loaded = inview.trigger("unveil");
      images = images.not(loaded);
    }

    $w.on("scroll.unveil resize.unveil lookup.unveil", unveil);

    unveil();

    return this;

  };

})(window.jQuery || window.Zepto);
