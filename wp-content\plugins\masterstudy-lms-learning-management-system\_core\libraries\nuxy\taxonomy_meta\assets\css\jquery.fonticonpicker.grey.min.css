/* Grey Theme file for fontIconPicker {@link https://github.com/micc83/fontIconPicker} */
.fip-grey.icons-selector{font-size:16px}.fip-grey.icons-selector .selector{border:1px solid #EDEDED;background-color:#fff}.fip-grey.icons-selector .selector-button{background-color:#F4F4F4;border-left:1px solid #E1E1E1}.fip-grey.icons-selector .selector-button:hover{background-color:#f1f1f1}.fip-grey.icons-selector .selector-button:hover i{color:#999}.fip-grey.icons-selector .selector-button i{color:#aaa;text-shadow:0 1px 0 #FFF}.fip-grey.icons-selector .selected-icon i{color:#404040}.fip-grey.icons-selector .selector-popup{-moz-box-shadow:0 1px 1px rgba(0,0,0,.04);-webkit-box-shadow:0 1px 1px rgba(0,0,0,.04);box-shadow:0 1px 1px rgba(0,0,0,.04);border:1px solid #E5E5E5}.fip-grey.icons-selector .selector-category select,.fip-grey.icons-selector .selector-search input[type=text]{border:1px solid #EDEDED;color:#404040;-moz-box-shadow:none;-webkit-box-shadow:none;box-shadow:none;outline:0}.fip-grey.icons-selector input::-webkit-input-placeholder{color:#ddd}.fip-grey.icons-selector input:-moz-placeholder{color:#ddd}.fip-grey.icons-selector input::-moz-placeholder{color:#ddd}.fip-grey.icons-selector input:-ms-input-placeholder{color:#ddd!important}.fip-grey.icons-selector .selector-search i{color:#eee}.fip-grey.icons-selector .fip-icons-container{background-color:#fff;border:1px solid #EDEDED}.fip-grey.icons-selector .fip-icons-container .loading{color:#eee}.fip-grey.icons-selector .fip-box{border:1px solid #EFEFEF}.fip-grey.icons-selector .fip-box:hover{background-color:#f6f6f6}.fip-grey.icons-selector .selector-footer,.fip-grey.icons-selector .selector-footer i{color:#ddd}.fip-grey.icons-selector .selector-arrows i:hover{color:#777}.fip-grey.icons-selector span.current-icon,.fip-grey.icons-selector span.current-icon:hover{background-color:#2EA2CC;color:#fff;border:1px solid #298CBA}.fip-grey.icons-selector .icons-picker-error i:before{color:#eee}
