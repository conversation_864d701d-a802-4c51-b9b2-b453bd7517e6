
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to phpMyAdmin’s documentation! &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="Introduction" href="intro.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             accesskey="N">next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Welcome to phpMyAdmin’s documentation!</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="welcome-to-phpmyadmin-s-documentation">
<h1>Welcome to phpMyAdmin’s documentation!<a class="headerlink" href="#welcome-to-phpmyadmin-s-documentation" title="Permalink to this headline">¶</a></h1>
<p>Contents:</p>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="intro.html">Introduction</a><ul>
<li class="toctree-l2"><a class="reference internal" href="intro.html#supported-features">Supported features</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#shortcut-keys">Shortcut keys</a></li>
<li class="toctree-l2"><a class="reference internal" href="intro.html#a-word-about-users">A word about users</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="require.html">Requirements</a><ul>
<li class="toctree-l2"><a class="reference internal" href="require.html#web-server">Web server</a></li>
<li class="toctree-l2"><a class="reference internal" href="require.html#php">PHP</a></li>
<li class="toctree-l2"><a class="reference internal" href="require.html#database">Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="require.html#web-browser">Web browser</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="setup.html">Installation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="setup.html#linux-distributions">Linux distributions</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#installing-on-windows">Installing on Windows</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#installing-from-git">Installing from Git</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#installing-using-composer">Installing using Composer</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#installing-using-docker">Installing using Docker</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#ibm-cloud">IBM Cloud</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#quick-install">Quick Install</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#verifying-phpmyadmin-releases">Verifying phpMyAdmin releases</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#phpmyadmin-configuration-storage">phpMyAdmin configuration storage</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#upgrading-from-an-older-version">Upgrading from an older version</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#using-authentication-modes">Using authentication modes</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#securing-your-phpmyadmin-installation">Securing your phpMyAdmin installation</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#using-ssl-for-connection-to-database-server">Using SSL for connection to database server</a></li>
<li class="toctree-l2"><a class="reference internal" href="setup.html#known-issues">Known issues</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="config.html">Configuration</a><ul>
<li class="toctree-l2"><a class="reference internal" href="config.html#basic-settings">Basic settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#server-connection-settings">Server connection settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#generic-settings">Generic settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#cookie-authentication-options">Cookie authentication options</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#navigation-panel-setup">Navigation panel setup</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#main-panel">Main panel</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#database-structure">Database structure</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#browse-mode">Browse mode</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#editing-mode">Editing mode</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#export-and-import-settings">Export and import settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#tabs-display-settings">Tabs display settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#pdf-options">PDF Options</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#languages">Languages</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#web-server-settings">Web server settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#theme-settings">Theme settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#design-customization">Design customization</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#text-fields">Text fields</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#sql-query-box-settings">SQL query box settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#web-server-upload-save-import-directories">Web server upload/save/import directories</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#various-display-setting">Various display setting</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#page-titles">Page titles</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#theme-manager-settings">Theme manager settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#default-queries">Default queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#mysql-settings">MySQL settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#default-options-for-transformations">Default options for Transformations</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#console-settings">Console settings</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#developer">Developer</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html#examples">Examples</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="user.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="settings.html">Configuring phpMyAdmin</a></li>
<li class="toctree-l2"><a class="reference internal" href="two_factor.html">Two-factor authentication</a></li>
<li class="toctree-l2"><a class="reference internal" href="transformations.html">Transformations</a></li>
<li class="toctree-l2"><a class="reference internal" href="bookmarks.html">Bookmarks</a></li>
<li class="toctree-l2"><a class="reference internal" href="privileges.html">User management</a></li>
<li class="toctree-l2"><a class="reference internal" href="relations.html">Relations</a></li>
<li class="toctree-l2"><a class="reference internal" href="charts.html">Charts</a></li>
<li class="toctree-l2"><a class="reference internal" href="import_export.html">Import and export</a></li>
<li class="toctree-l2"><a class="reference internal" href="themes.html">Custom Themes</a></li>
<li class="toctree-l2"><a class="reference internal" href="other.html">Other sources of information</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="faq.html">FAQ - Frequently Asked Questions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="faq.html#server">Server</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#configuration">Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#known-limitations">Known limitations</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#isps-multi-user-installations">ISPs, multi-user installations</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#browsers-or-client-os">Browsers or client OS</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#using-phpmyadmin">Using phpMyAdmin</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#phpmyadmin-project">phpMyAdmin project</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#security">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="faq.html#synchronization">Synchronization</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="developers.html">Developers Information</a></li>
<li class="toctree-l1"><a class="reference internal" href="security.html">Security policy</a><ul>
<li class="toctree-l2"><a class="reference internal" href="security.html#typical-vulnerabilities">Typical vulnerabilities</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html#reporting-security-issues">Reporting security issues</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="vendors.html">Distributing and packaging phpMyAdmin</a><ul>
<li class="toctree-l2"><a class="reference internal" href="vendors.html#external-libraries">External libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="vendors.html#specific-files-licenses">Specific files LICENSES</a></li>
<li class="toctree-l2"><a class="reference internal" href="vendors.html#licenses-for-vendors">Licenses for vendors</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="copyright.html">Copyright</a><ul>
<li class="toctree-l2"><a class="reference internal" href="copyright.html#third-party-licenses">Third party licenses</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="credits.html">Credits</a><ul>
<li class="toctree-l2"><a class="reference internal" href="credits.html#credits-in-chronological-order">Credits, in chronological order</a></li>
<li class="toctree-l2"><a class="reference internal" href="credits.html#translators">Translators</a></li>
<li class="toctree-l2"><a class="reference internal" href="credits.html#documentation-translators">Documentation translators</a></li>
<li class="toctree-l2"><a class="reference internal" href="credits.html#original-credits-of-version-2-1-0">Original Credits of Version 2.1.0</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="glossary.html">Glossary</a></li>
</ul>
</div>
</div>
<div class="section" id="indices-and-tables">
<h1>Indices and tables<a class="headerlink" href="#indices-and-tables" title="Permalink to this headline">¶</a></h1>
<ul class="simple">
<li><p><a class="reference internal" href="genindex.html"><span class="std std-ref">Index</span></a></p></li>
<li><p><a class="reference internal" href="search.html"><span class="std std-ref">Search Page</span></a></p></li>
<li><p><a class="reference internal" href="glossary.html#glossary"><span class="std std-ref">Glossary</span></a></p></li>
</ul>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="#">Table of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Welcome to phpMyAdmin’s documentation!</a></li>
<li><a class="reference internal" href="#indices-and-tables">Indices and tables</a></li>
</ul>

  <h4>Next topic</h4>
  <p class="topless"><a href="intro.html"
                        title="next chapter">Introduction</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/index.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="intro.html" title="Introduction"
             >next</a> |</li>
        <li class="nav-item nav-item-0"><a href="#">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Welcome to phpMyAdmin’s documentation!</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>