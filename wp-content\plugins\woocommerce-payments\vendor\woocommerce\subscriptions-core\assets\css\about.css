/*------------------------------------------------------------------------------
  Subscriptions About Page CSS
------------------------------------------------------------------------------*/
ul {
	list-style-type: disc;
	padding-left: 1.6em;
	margin-top: 0;
	margin-bottom: 1.6em;
}
.wcs-badge::before {
	font-family: 'WooCommerce' !important;
	content: '\e03d';
	color: #fff;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 80px;
	font-weight: normal;
	width: 165px;
	height: 165px;
	line-height: 165px;
	text-align: center;
	position: absolute;
	top: 0;
	left: 0;
	margin: 0;
	vertical-align: middle;
}
.wcs-badge {
	position: relative;
	background: #9c5d90;
	text-rendering: optimizeLegibility;
	padding-top: 150px;
	height: 52px;
	width: 165px;
	font-weight: 600;
	font-size: 14px;
	text-align: center;
	color: #ddc8d9;
	margin: 5px 0 0 0;
	-webkit-box-shadow: 0 1px 3px rgba( 0, 0, 0, 0.2 );
	box-shadow: 0 1px 3px rgba( 0, 0, 0, 0.2 );
}
.about-wrap h2 {
	border-bottom: 1px solid #ddd;
	margin: 0;
	padding: 1em 0;
}
.about-wrap .changelog {
	margin-bottom: 0;
}
.about-wrap .feature-section {
	padding-top: 1.6em;
	padding-bottom: 0.4em;
	border-bottom: 1px solid #ddd;
}
.about-wrap .still-more .feature-section {
	padding-top: 0;
	padding-bottom: 0.4em;
	border-bottom: 1px solid #ddd;
}
.about-wrap .under-the-hood {
	padding-top: 0;
}
.about-wrap .wcs-badge {
	position: absolute;
	top: 0;
	right: 0;
}
.about-wrap .wcs-feature {
	overflow: visible !important;
	*zoom: 1;
}
.about-wrap .wcs-feature::before,
.about-wrap .wcs-feature::after {
	content: ' ';
	display: table;
}
.about-wrap .wcs-feature::after {
	clear: both;
}
.about-wrap .two-col .feature-right {
	float: right;
}
.about-wrap .two-col .feature-copy,
.about-wrap .two-col .feature-image {
	margin-top: 2em;
}
.about-wrap div.icon {
	width: 0 !important;
	padding: 0;
	margin: 0;
}
.woocommerce-message {
	position: relative;
	border-left-color: #cc99c2 !important;
	overflow: hidden;
}

.woocommerce-message a.button-primary,
p.woocommerce-actions a.button-primary,
.woocommerce-message a.button-primary:focus,
p.woocommerce-actions a.button-primary:focus,
.woocommerce-message a.button-primary:active,
p.woocommerce-actions a.button-primary:active {
	background: #b366a4;
	border-color: #b366a4;
	-webkit-box-shadow: inset 0 1px 0 rgba( 255, 255, 255, 0.25 ),
		0 1px 0 rgba( 0, 0, 0, 0.15 );
	box-shadow: inset 0 1px 0 rgba( 255, 255, 255, 0.25 ),
		0 1px 0 rgba( 0, 0, 0, 0.15 );
	text-shadow: 0 -1px 1px #b366a4, 1px 0 1px #b366a4, 0 1px 1px #b366a4,
		-1px 0 1px #b366a4;
	color: #fff;
	text-decoration: none;
}

.woocommerce-message a.button-primary:hover,
p.woocommerce-actions a.button-primary:hover {
	background: #bb77ae;
	border-color: #aa559a;
	-webkit-box-shadow: inset 0 1px 0 rgba( 255, 255, 255, 0.25 ),
		0 1px 0 rgba( 0, 0, 0, 0.15 );
	box-shadow: inset 0 1px 0 rgba( 255, 255, 255, 0.25 ),
		0 1px 0 rgba( 0, 0, 0, 0.15 );
}

.woocommerce-message a.button-primary:active,
p.woocommerce-actions a.button-primary:active {
	background: #aa559a;
	border-color: #aa559a;
}

.woocommerce-message a.skip,
p.woocommerce-actions a.skip {
	opacity: 0.7;
}

.woocommerce-message .twitter-share-button,
p.woocommerce-actions .twitter-share-button {
	vertical-align: middle;
	margin-left: 3px;
}

p.woocommerce-actions {
	margin-bottom: 2em;
}

.woocommerce-about-text {
	margin-bottom: 1em !important;
}

.about-wrap .feature-section.three-col .col {
	width: 29.95%;
	margin-right: 4.999999999%;
	float: left;
}
.about-wrap .feature-section.two-col .col {
	float: left;
}
.about-wrap .feature-section .col {
	margin-top: 1em;
}
.about-wrap .feature-section .col.last-feature {
	margin-right: 0;
}
.about-wrap .feature-section .col.feature-right {
	margin-right: 0;
	float: right !important;
}

@media only screen and ( max-width: 1200px ) {
	.about-wrap .two-col .feature-copy {
		margin-top: 0;
	}
}

@media only screen and ( max-width: 781px ) {
	.about-wrap .two-col .feature-copy,
	.about-wrap .feature-section {
		padding-bottom: 1em;
	}
	.about-wrap .two-col .feature-image,
	.about-wrap .changelog {
		border-bottom: 0;
		margin-bottom: 0;
		padding-bottom: 0;
	}
	.about-wrap .feature-section.three-col .col {
		width: 100%;
		margin: 40px 0 0;
		padding: 0 0 40px;
		border-bottom: 1px solid rgba( 0, 0, 0, 0.1 );
	}
}

@media only screen and ( max-width: 500px ) {
	.about-wrap .wcs-badge::before {
		width: 100%;
	}
	.about-wrap .wcs-badge {
		position: relative;
		margin-bottom: 1.5em;
		width: 100%;
	}
	.about-wrap .three-col .col {
		width: 100% !important;
		float: none !important;
	}
}
