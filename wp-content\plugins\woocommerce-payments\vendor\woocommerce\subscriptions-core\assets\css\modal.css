body.wcs-modal-open {
	overflow: hidden;
}

.wcs-modal {
	position: fixed;
	top: 0;
	left: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 0;
	background-color: transparent;
	overflow: hidden;
	transition: background-color 0.25s ease;
	z-index: 1000;
}

.wcs-modal.open {
	position: fixed;
	width: 100%;
	height: 100vh;
	background-color: rgba( 0, 0, 0, 0.5 );
	transition: background-color 0.25s;
}

.wcs-modal.open > .content-wrapper {
	transform: scale( 1 );
	min-width: 30%;
	max-width: 80%;
}

.wcs-modal .content-wrapper {
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	margin: 0;
	padding: 2em;
	background-color: #fff;
	border-radius: 0.3em;
	transform: scale( 0 );
	transition: transform 0.25s;
	transition-delay: 0.15s;
}

.wcs-modal .content-wrapper .close {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 50;
}

.wcs-modal .content-wrapper .modal-header {
	position: relative;
	display: block;
	height: 5%;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	margin: 0;
}

.modal-header > h2 {
	font-size: 1.5em;
}

.wcs-modal .content-wrapper .content {
	position: relative;
	min-width: 100%;
	height: 90%;
	font-size: 0.875rem;
}

.wcs-modal .content-wrapper .content p {
	line-height: 1.75;
}

.wcs-modal .content-wrapper .modal-footer {
	position: relative;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	width: 100%;
	margin: 0;
}

.wcs-modal .content-wrapper .modal-footer .action {
	position: relative;
	margin-left: 0.625rem;
}

.wcs-modal footer > a:not( :first-child ) {
	margin-left: 0.8em;
}

/*
 * Mobile Display Styles
 */
@media only screen and ( max-width: 414px ) {
	.wcs-modal.open > .content-wrapper {
		max-width: none;
		width: 100%;
		height: 100%;
		padding: 0.8em;
		border-radius: 0;
	}

	.wcs-modal.open > .content-wrapper > .content {
		width: 100%;
		height: 75%; /* WooCommerce has a nav at the bottom of mobile displays so we need to account for it */
	}

	.wcs-modal.open .order_details {
		font-size: 0.85em;
	}
}

@media only screen and ( max-width: 320px ) {
	.wcs-modal.open .content-wrapper .modal-header {
		height: 7%;
	}

	.wcs-modal.open > .content-wrapper > .content {
		width: 100%;
		height: 65%; /* WooCommerce has a nav at the bottom of mobile displays so we need to account for it */
	}
}

@media only screen and ( max-width: 768px ) {
	.wcs-modal.open > .content-wrapper {
		min-width: 60%;
	}
}
