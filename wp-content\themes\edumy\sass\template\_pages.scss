// layout for woocommerce
@media(min-width:1501px) {
    .col-md-cl-5{
        width: 20%;
        &.first{
        	clear: both;
        }
    }
}
@media(min-width: 1200px) and (max-width:1500px) {
	.col-md-cl-5{
        width: 25%;
        &.first:nth-child(4n + 1) {
        	clear: both;
        }
    }
    div.product.col-md-cl-5:nth-child(4n + 1) {
    	clear: both;
    }
}
.main-page {
	.page-links{
		clear: both;
		overflow: hidden;
		padding:$theme-padding 0;
		margin:0;
	}
	#respond{
		.comment-reply-title{
			margin:0 !important;
		}
	} 	
	#comments{
		margin-top:30px;
		clear: both;
	}
}
//----------------------------------
// layout page
.wrapper-80{
	padding-left:50px;
	padding-right:50px;
}
@media(min-width:1200px) {
	.wrapper-80{
		padding-left:80px;
		padding-right:80px;
	}
	.wrapper-50{
		padding-left:50px;
		padding-right:50px;
	}
}
div.wpcf7-validation-errors{
	margin: 0;
	padding: 15px;
}
.contact-form-content{
	padding: $theme-padding;
	background: $contact-bg;
	min-height: 260px;
	.rounded{
		@include rtl-margin-right(10px);
		color: $white;
		@include size(40px,40px);
		background: darken($body-bg, 20%);
		.fa,.icon{
			@include font-size(font-size,16px);
			margin: 13px;
		}
	}
	small{
		color: darken($text-color, 20%);
	}
}
.user{
	> div{
		display: none;
		&.active{
			display: block;
		}
	}
	max-width:570px;
	text-align: center;
	margin-left: auto;
	margin-right: auto;
	form{
		margin-bottom: 25px !important;
	}
	.title{
		font-size: 30px;
		margin:0 0 20px;
		@media(min-width: 1200px) {
			font-size: 48px;
		}
	}
	.create {
		margin-bottom: 30px;
		@media(min-width: 1200px) {
			margin-bottom: 50px;
		}
		a{
			font-size: 12px;
			text-decoration: underline;
			text-transform: uppercase;
		}
	}
}
// Page Not Found
//----------------------------------
.page-404{
   .not-found{
   		padding: 54px 0 100px;
   }
   .icon-error{
   		font-size:60px;
   }
   .return-home{
   		font-size:16px;
   		text-decoration: underline;
   }
   .description{
		font-size:16px;
		line-height: 34px;
		margin-bottom:$theme-padding + 15px;
		font-family: $headings-font-family;
   	h5{
   		margin: 0px 0px 15px 0px;
   		font-size: 35px;      		
   		text-transform: uppercase;
   	}
   }
   .title-big{
		margin: 0px;
		font-size: 200px;
		text-transform: uppercase;   	   	
		font-weight: 700;
		line-height: 230px;
   }
   .widget-search{
   		margin: auto;
		max-width: 500px;		
   }
   .page-content{   		
		margin-bottom: 50px;
		margin-left: auto;
		margin-right: auto;
		width: 890px;
    }
    .return{
    	margin: 40px 0 0 0;
    	.btn-to-back{
    		position: relative;
    		&:before{
    			content: "\f107";
    			font-family: $icon-font-family;
    			@include rtl-right(-25px);
	    		@include vertical-align(absolute);
    		}
    	}
    }
    .widget-search{
		.btn{
			border: 0;
	    	color: $white;	
			background-color: $theme-color;	    	
			@include opacity(1);	    	
	    	@include square(46px);
	    	@include border-radius(5px);
	    	@include rtl-margin(0, 2px, 0, 0);	    	   
	    	@include hover-focus-active() {
	    		color: $white;
	    		background-color: $theme-color;	    		
	    	} 		    	
	    	i{
	    		&:before{
	    			margin: 0;
	    			font-size: 14px;	    			
	    			content: "\f107";	    		    					
	    			@include opacity(1);			    			
	    		}	    		
	    	}
	    }	    
	    .form-control{
	    	padding-top: 16px;
	    	padding-bottom: 16px;
	    }	    
    }    
}
.box-relative{
	.elementor-row{
		position: relative;
		.elementor-widget,
		.elementor-column,
		.elementor-column-wrap, 
		.elementor-widget-wrap{
			position: static;
		}
	}
}
body.elementor-editor-active {
	.box-relative{
		.elementor-row{
			.elementor-widget,
			.elementor-column,
			.elementor-column-wrap, 
			.elementor-widget-wrap{
				position:relative;
			}
		}
	}
}