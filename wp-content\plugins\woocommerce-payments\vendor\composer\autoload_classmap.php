<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Automattic\\Jetpack\\A8c_Mc_Stats' => $vendorDir . '/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php',
    'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => $vendorDir . '/automattic/jetpack-admin-ui/src/class-admin-menu.php',
    'Automattic\\Jetpack\\Assets' => $vendorDir . '/automattic/jetpack-assets/src/class-assets.php',
    'Automattic\\Jetpack\\Assets\\Logo' => $vendorDir . '/automattic/jetpack-logo/src/class-logo.php',
    'Automattic\\Jetpack\\Assets\\Semver' => $vendorDir . '/automattic/jetpack-assets/src/class-semver.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php',
    'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php',
    'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php',
    'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php',
    'Automattic\\Jetpack\\Config' => $vendorDir . '/automattic/jetpack-config/src/class-config.php',
    'Automattic\\Jetpack\\Connection\\Client' => $vendorDir . '/automattic/jetpack-connection/src/class-client.php',
    'Automattic\\Jetpack\\Connection\\Connection_Notice' => $vendorDir . '/automattic/jetpack-connection/src/class-connection-notice.php',
    'Automattic\\Jetpack\\Connection\\Error_Handler' => $vendorDir . '/automattic/jetpack-connection/src/class-error-handler.php',
    'Automattic\\Jetpack\\Connection\\Initial_State' => $vendorDir . '/automattic/jetpack-connection/src/class-initial-state.php',
    'Automattic\\Jetpack\\Connection\\Manager' => $vendorDir . '/automattic/jetpack-connection/src/class-manager.php',
    'Automattic\\Jetpack\\Connection\\Manager_Interface' => $vendorDir . '/automattic/jetpack-connection/src/interface-manager.php',
    'Automattic\\Jetpack\\Connection\\Nonce_Handler' => $vendorDir . '/automattic/jetpack-connection/src/class-nonce-handler.php',
    'Automattic\\Jetpack\\Connection\\Package_Version' => $vendorDir . '/automattic/jetpack-connection/src/class-package-version.php',
    'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => $vendorDir . '/automattic/jetpack-connection/src/class-package-version-tracker.php',
    'Automattic\\Jetpack\\Connection\\Plugin' => $vendorDir . '/automattic/jetpack-connection/src/class-plugin.php',
    'Automattic\\Jetpack\\Connection\\Plugin_Storage' => $vendorDir . '/automattic/jetpack-connection/src/class-plugin-storage.php',
    'Automattic\\Jetpack\\Connection\\REST_Connector' => $vendorDir . '/automattic/jetpack-connection/src/class-rest-connector.php',
    'Automattic\\Jetpack\\Connection\\Rest_Authentication' => $vendorDir . '/automattic/jetpack-connection/src/class-rest-authentication.php',
    'Automattic\\Jetpack\\Connection\\Secrets' => $vendorDir . '/automattic/jetpack-connection/src/class-secrets.php',
    'Automattic\\Jetpack\\Connection\\Server_Sandbox' => $vendorDir . '/automattic/jetpack-connection/src/class-server-sandbox.php',
    'Automattic\\Jetpack\\Connection\\Tokens' => $vendorDir . '/automattic/jetpack-connection/src/class-tokens.php',
    'Automattic\\Jetpack\\Connection\\Urls' => $vendorDir . '/automattic/jetpack-connection/src/class-urls.php',
    'Automattic\\Jetpack\\Connection\\Utils' => $vendorDir . '/automattic/jetpack-connection/src/class-utils.php',
    'Automattic\\Jetpack\\Connection\\Webhooks' => $vendorDir . '/automattic/jetpack-connection/src/class-webhooks.php',
    'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => $vendorDir . '/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php',
    'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-async-call.php',
    'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-connector.php',
    'Automattic\\Jetpack\\Constants' => $vendorDir . '/automattic/jetpack-constants/src/class-constants.php',
    'Automattic\\Jetpack\\CookieState' => $vendorDir . '/automattic/jetpack-status/src/class-cookiestate.php',
    'Automattic\\Jetpack\\Errors' => $vendorDir . '/automattic/jetpack-status/src/class-errors.php',
    'Automattic\\Jetpack\\Files' => $vendorDir . '/automattic/jetpack-status/src/class-files.php',
    'Automattic\\Jetpack\\Heartbeat' => $vendorDir . '/automattic/jetpack-connection/src/class-heartbeat.php',
    'Automattic\\Jetpack\\IP\\Utils' => $vendorDir . '/automattic/jetpack-ip/src/class-utils.php',
    'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => $vendorDir . '/automattic/jetpack-identity-crisis/src/class-rest-endpoints.php',
    'Automattic\\Jetpack\\IdentityCrisis\\UI' => $vendorDir . '/automattic/jetpack-identity-crisis/src/class-ui.php',
    'Automattic\\Jetpack\\Identity_Crisis' => $vendorDir . '/automattic/jetpack-identity-crisis/src/class-identity-crisis.php',
    'Automattic\\Jetpack\\Modules' => $vendorDir . '/automattic/jetpack-status/src/class-modules.php',
    'Automattic\\Jetpack\\Password_Checker' => $vendorDir . '/automattic/jetpack-password-checker/src/class-password-checker.php',
    'Automattic\\Jetpack\\Paths' => $vendorDir . '/automattic/jetpack-status/src/class-paths.php',
    'Automattic\\Jetpack\\Redirect' => $vendorDir . '/automattic/jetpack-redirect/src/class-redirect.php',
    'Automattic\\Jetpack\\Roles' => $vendorDir . '/automattic/jetpack-roles/src/class-roles.php',
    'Automattic\\Jetpack\\Status' => $vendorDir . '/automattic/jetpack-status/src/class-status.php',
    'Automattic\\Jetpack\\Status\\Cache' => $vendorDir . '/automattic/jetpack-status/src/class-cache.php',
    'Automattic\\Jetpack\\Status\\Host' => $vendorDir . '/automattic/jetpack-status/src/class-host.php',
    'Automattic\\Jetpack\\Status\\Visitor' => $vendorDir . '/automattic/jetpack-status/src/class-visitor.php',
    'Automattic\\Jetpack\\Sync\\Actions' => $vendorDir . '/automattic/jetpack-sync/src/class-actions.php',
    'Automattic\\Jetpack\\Sync\\Codec_Interface' => $vendorDir . '/automattic/jetpack-sync/src/interface-codec.php',
    'Automattic\\Jetpack\\Sync\\Data_Settings' => $vendorDir . '/automattic/jetpack-sync/src/class-data-settings.php',
    'Automattic\\Jetpack\\Sync\\Dedicated_Sender' => $vendorDir . '/automattic/jetpack-sync/src/class-dedicated-sender.php',
    'Automattic\\Jetpack\\Sync\\Default_Filter_Settings' => $vendorDir . '/automattic/jetpack-sync/src/class-default-filter-settings.php',
    'Automattic\\Jetpack\\Sync\\Defaults' => $vendorDir . '/automattic/jetpack-sync/src/class-defaults.php',
    'Automattic\\Jetpack\\Sync\\Functions' => $vendorDir . '/automattic/jetpack-sync/src/class-functions.php',
    'Automattic\\Jetpack\\Sync\\Health' => $vendorDir . '/automattic/jetpack-sync/src/class-health.php',
    'Automattic\\Jetpack\\Sync\\JSON_Deflate_Array_Codec' => $vendorDir . '/automattic/jetpack-sync/src/class-json-deflate-array-codec.php',
    'Automattic\\Jetpack\\Sync\\Listener' => $vendorDir . '/automattic/jetpack-sync/src/class-listener.php',
    'Automattic\\Jetpack\\Sync\\Lock' => $vendorDir . '/automattic/jetpack-sync/src/class-lock.php',
    'Automattic\\Jetpack\\Sync\\Main' => $vendorDir . '/automattic/jetpack-sync/src/class-main.php',
    'Automattic\\Jetpack\\Sync\\Modules' => $vendorDir . '/automattic/jetpack-sync/src/class-modules.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Attachments' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-attachments.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Callables' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-callables.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Comments' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-comments.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Constants' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-constants.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-full-sync.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync_Immediately' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-full-sync-immediately.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Import' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-import.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Menus' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-menus.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Meta' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-meta.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Module' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-module.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Network_Options' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-network-options.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Options' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-options.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Plugins' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-plugins.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Posts' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-posts.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Protect' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-protect.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Search' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-search.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Stats' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-stats.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Term_Relationships' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-term-relationships.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Terms' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-terms.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Themes' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-themes.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Updates' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-updates.php',
    'Automattic\\Jetpack\\Sync\\Modules\\Users' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-users.php',
    'Automattic\\Jetpack\\Sync\\Modules\\WP_Super_Cache' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-wp-super-cache.php',
    'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce' => $vendorDir . '/automattic/jetpack-sync/src/modules/class-woocommerce.php',
    'Automattic\\Jetpack\\Sync\\Package_Version' => $vendorDir . '/automattic/jetpack-sync/src/class-package-version.php',
    'Automattic\\Jetpack\\Sync\\Queue' => $vendorDir . '/automattic/jetpack-sync/src/class-queue.php',
    'Automattic\\Jetpack\\Sync\\Queue_Buffer' => $vendorDir . '/automattic/jetpack-sync/src/class-queue-buffer.php',
    'Automattic\\Jetpack\\Sync\\REST_Endpoints' => $vendorDir . '/automattic/jetpack-sync/src/class-rest-endpoints.php',
    'Automattic\\Jetpack\\Sync\\REST_Sender' => $vendorDir . '/automattic/jetpack-sync/src/class-rest-sender.php',
    'Automattic\\Jetpack\\Sync\\Replicastore' => $vendorDir . '/automattic/jetpack-sync/src/class-replicastore.php',
    'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum' => $vendorDir . '/automattic/jetpack-sync/src/replicastore/class-table-checksum.php',
    'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Usermeta' => $vendorDir . '/automattic/jetpack-sync/src/replicastore/class-table-checksum-usermeta.php',
    'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Users' => $vendorDir . '/automattic/jetpack-sync/src/replicastore/class-table-checksum-users.php',
    'Automattic\\Jetpack\\Sync\\Replicastore_Interface' => $vendorDir . '/automattic/jetpack-sync/src/interface-replicastore.php',
    'Automattic\\Jetpack\\Sync\\Sender' => $vendorDir . '/automattic/jetpack-sync/src/class-sender.php',
    'Automattic\\Jetpack\\Sync\\Server' => $vendorDir . '/automattic/jetpack-sync/src/class-server.php',
    'Automattic\\Jetpack\\Sync\\Settings' => $vendorDir . '/automattic/jetpack-sync/src/class-settings.php',
    'Automattic\\Jetpack\\Sync\\Simple_Codec' => $vendorDir . '/automattic/jetpack-sync/src/class-simple-codec.php',
    'Automattic\\Jetpack\\Sync\\Users' => $vendorDir . '/automattic/jetpack-sync/src/class-users.php',
    'Automattic\\Jetpack\\Sync\\Utils' => $vendorDir . '/automattic/jetpack-sync/src/class-utils.php',
    'Automattic\\Jetpack\\Terms_Of_Service' => $vendorDir . '/automattic/jetpack-connection/src/class-terms-of-service.php',
    'Automattic\\Jetpack\\Tracking' => $vendorDir . '/automattic/jetpack-connection/src/class-tracking.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Composer\\Installers\\AglInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php',
    'Composer\\Installers\\AimeosInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php',
    'Composer\\Installers\\AnnotateCmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php',
    'Composer\\Installers\\AsgardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php',
    'Composer\\Installers\\AttogramInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php',
    'Composer\\Installers\\BaseInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php',
    'Composer\\Installers\\BitrixInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php',
    'Composer\\Installers\\BonefishInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php',
    'Composer\\Installers\\CakePHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php',
    'Composer\\Installers\\ChefInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php',
    'Composer\\Installers\\CiviCrmInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php',
    'Composer\\Installers\\ClanCatsFrameworkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php',
    'Composer\\Installers\\CockpitInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php',
    'Composer\\Installers\\CodeIgniterInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php',
    'Composer\\Installers\\Concrete5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php',
    'Composer\\Installers\\CraftInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php',
    'Composer\\Installers\\CroogoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php',
    'Composer\\Installers\\DecibelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php',
    'Composer\\Installers\\DframeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php',
    'Composer\\Installers\\DokuWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php',
    'Composer\\Installers\\DolibarrInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php',
    'Composer\\Installers\\DrupalInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php',
    'Composer\\Installers\\ElggInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php',
    'Composer\\Installers\\EliasisInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php',
    'Composer\\Installers\\ExpressionEngineInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php',
    'Composer\\Installers\\EzPlatformInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php',
    'Composer\\Installers\\FuelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php',
    'Composer\\Installers\\FuelphpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php',
    'Composer\\Installers\\GravInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php',
    'Composer\\Installers\\HuradInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php',
    'Composer\\Installers\\ImageCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php',
    'Composer\\Installers\\Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php',
    'Composer\\Installers\\ItopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php',
    'Composer\\Installers\\JoomlaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php',
    'Composer\\Installers\\KanboardInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php',
    'Composer\\Installers\\KirbyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php',
    'Composer\\Installers\\KnownInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php',
    'Composer\\Installers\\KodiCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php',
    'Composer\\Installers\\KohanaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php',
    'Composer\\Installers\\LanManagementSystemInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php',
    'Composer\\Installers\\LaravelInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php',
    'Composer\\Installers\\LavaLiteInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php',
    'Composer\\Installers\\LithiumInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php',
    'Composer\\Installers\\MODULEWorkInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php',
    'Composer\\Installers\\MODXEvoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php',
    'Composer\\Installers\\MagentoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php',
    'Composer\\Installers\\MajimaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php',
    'Composer\\Installers\\MakoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php',
    'Composer\\Installers\\MantisBTInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php',
    'Composer\\Installers\\MauticInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php',
    'Composer\\Installers\\MayaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php',
    'Composer\\Installers\\MediaWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php',
    'Composer\\Installers\\MicroweberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php',
    'Composer\\Installers\\ModxInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php',
    'Composer\\Installers\\MoodleInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php',
    'Composer\\Installers\\OctoberInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php',
    'Composer\\Installers\\OntoWikiInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php',
    'Composer\\Installers\\OsclassInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php',
    'Composer\\Installers\\OxidInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php',
    'Composer\\Installers\\PPIInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php',
    'Composer\\Installers\\PhiftyInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php',
    'Composer\\Installers\\PhpBBInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php',
    'Composer\\Installers\\PimcoreInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php',
    'Composer\\Installers\\PiwikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php',
    'Composer\\Installers\\PlentymarketsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php',
    'Composer\\Installers\\Plugin' => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php',
    'Composer\\Installers\\PortoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php',
    'Composer\\Installers\\PrestashopInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php',
    'Composer\\Installers\\ProcessWireInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php',
    'Composer\\Installers\\PuppetInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php',
    'Composer\\Installers\\PxcmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php',
    'Composer\\Installers\\RadPHPInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php',
    'Composer\\Installers\\ReIndexInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php',
    'Composer\\Installers\\Redaxo5Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php',
    'Composer\\Installers\\RedaxoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php',
    'Composer\\Installers\\RoundcubeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php',
    'Composer\\Installers\\SMFInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php',
    'Composer\\Installers\\ShopwareInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php',
    'Composer\\Installers\\SilverStripeInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php',
    'Composer\\Installers\\SiteDirectInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php',
    'Composer\\Installers\\StarbugInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php',
    'Composer\\Installers\\SyDESInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php',
    'Composer\\Installers\\SyliusInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php',
    'Composer\\Installers\\Symfony1Installer' => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php',
    'Composer\\Installers\\TYPO3CmsInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php',
    'Composer\\Installers\\TYPO3FlowInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php',
    'Composer\\Installers\\TaoInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php',
    'Composer\\Installers\\TheliaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php',
    'Composer\\Installers\\TuskInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php',
    'Composer\\Installers\\UserFrostingInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php',
    'Composer\\Installers\\VanillaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php',
    'Composer\\Installers\\VgmcpInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php',
    'Composer\\Installers\\WHMCSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php',
    'Composer\\Installers\\WolfCMSInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php',
    'Composer\\Installers\\WordPressInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php',
    'Composer\\Installers\\YawikInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php',
    'Composer\\Installers\\ZendInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php',
    'Composer\\Installers\\ZikulaInstaller' => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php',
    'Jetpack_IXR_Client' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php',
    'Jetpack_IXR_ClientMulticall' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php',
    'Jetpack_Options' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-options.php',
    'Jetpack_Signature' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-signature.php',
    'Jetpack_Tracks_Client' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php',
    'Jetpack_Tracks_Event' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php',
    'Jetpack_XMLRPC_Server' => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php',
    'WCPay\\MultiCurrency\\AdminNotices' => $baseDir . '/includes/multi-currency/AdminNotices.php',
    'WCPay\\MultiCurrency\\Analytics' => $baseDir . '/includes/multi-currency/Analytics.php',
    'WCPay\\MultiCurrency\\BackendCurrencies' => $baseDir . '/includes/multi-currency/BackendCurrencies.php',
    'WCPay\\MultiCurrency\\Compatibility' => $baseDir . '/includes/multi-currency/Compatibility.php',
    'WCPay\\MultiCurrency\\Compatibility\\BaseCompatibility' => $baseDir . '/includes/multi-currency/Compatibility/BaseCompatibility.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceBookings' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceBookings.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceDeposits' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceDeposits.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceFedEx' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceFedEx.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceNameYourPrice' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceNameYourPrice.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommercePointsAndRewards' => $baseDir . '/includes/multi-currency/Compatibility/WooCommercePointsAndRewards.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommercePreOrders' => $baseDir . '/includes/multi-currency/Compatibility/WooCommercePreOrders.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceProductAddOns' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceProductAddOns.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceSubscriptions' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceSubscriptions.php',
    'WCPay\\MultiCurrency\\Compatibility\\WooCommerceUPS' => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceUPS.php',
    'WCPay\\MultiCurrency\\CountryFlags' => $baseDir . '/includes/multi-currency/CountryFlags.php',
    'WCPay\\MultiCurrency\\Currency' => $baseDir . '/includes/multi-currency/Currency.php',
    'WCPay\\MultiCurrency\\CurrencySwitcherBlock' => $baseDir . '/includes/multi-currency/CurrencySwitcherBlock.php',
    'WCPay\\MultiCurrency\\CurrencySwitcherWidget' => $baseDir . '/includes/multi-currency/CurrencySwitcherWidget.php',
    'WCPay\\MultiCurrency\\Exceptions\\InvalidCurrencyException' => $baseDir . '/includes/multi-currency/Exceptions/InvalidCurrencyException.php',
    'WCPay\\MultiCurrency\\Exceptions\\InvalidCurrencyRateException' => $baseDir . '/includes/multi-currency/Exceptions/InvalidCurrencyRateException.php',
    'WCPay\\MultiCurrency\\FrontendCurrencies' => $baseDir . '/includes/multi-currency/FrontendCurrencies.php',
    'WCPay\\MultiCurrency\\FrontendPrices' => $baseDir . '/includes/multi-currency/FrontendPrices.php',
    'WCPay\\MultiCurrency\\Geolocation' => $baseDir . '/includes/multi-currency/Geolocation.php',
    'WCPay\\MultiCurrency\\Helpers\\OrderMetaHelper' => $baseDir . '/includes/multi-currency/Helpers/OrderMetaHelper.php',
    'WCPay\\MultiCurrency\\MultiCurrency' => $baseDir . '/includes/multi-currency/MultiCurrency.php',
    'WCPay\\MultiCurrency\\Notes\\NoteMultiCurrencyAvailable' => $baseDir . '/includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php',
    'WCPay\\MultiCurrency\\PaymentMethodsCompatibility' => $baseDir . '/includes/multi-currency/PaymentMethodsCompatibility.php',
    'WCPay\\MultiCurrency\\RestController' => $baseDir . '/includes/multi-currency/RestController.php',
    'WCPay\\MultiCurrency\\Settings' => $baseDir . '/includes/multi-currency/Settings.php',
    'WCPay\\MultiCurrency\\SettingsOnboardCta' => $baseDir . '/includes/multi-currency/SettingsOnboardCta.php',
    'WCPay\\MultiCurrency\\StorefrontIntegration' => $baseDir . '/includes/multi-currency/StorefrontIntegration.php',
    'WCPay\\MultiCurrency\\Tracking' => $baseDir . '/includes/multi-currency/Tracking.php',
    'WCPay\\MultiCurrency\\UserSettings' => $baseDir . '/includes/multi-currency/UserSettings.php',
    'WCPay\\MultiCurrency\\Utils' => $baseDir . '/includes/multi-currency/Utils.php',
);
