/*! For license information please see index.js.LICENSE.txt */
(()=>{var e={5235:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function s(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var a=typeof n;if("string"===a||"number"===a)e.push(n);else if(Array.isArray(n)){if(n.length){var i=s.apply(null,n);i&&e.push(i)}}else if("object"===a)if(n.toString===Object.prototype.toString)for(var o in n)r.call(n,o)&&n[o]&&e.push(o);else e.push(n.toString())}}return e.join(" ")}e.exports?(s.default=s,e.exports=s):void 0===(n=function(){return s}.apply(t,[]))||(e.exports=n)}()},951:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,s=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(s=r))})),t.splice(s,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG);return e},t.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(1741)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},1741:(e,t,n)=>{e.exports=function(e){function t(e){let n,s,a,i=null;function o(...e){if(!o.enabled)return;const r=o,s=Number(new Date),a=s-(n||s);r.diff=a,r.prev=n,r.curr=s,n=s,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,s)=>{if("%%"===n)return"%";i++;const a=t.formatters[s];if("function"==typeof a){const t=e[i];n=a.call(r,t),e.splice(i,1),i--}return n})),t.formatArgs.call(r,e);(r.log||t.log).apply(r,e)}return o.namespace=e,o.useColors=t.useColors(),o.color=t.selectColor(e),o.extend=r,o.destroy=t.destroy,Object.defineProperty(o,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(s!==t.namespaces&&(s=t.namespaces,a=t.enabled(e)),a),set:e=>{i=e}}),"function"==typeof t.init&&t.init(o),o}function r(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names.map(s),...t.skips.map(s).map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){let n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),s=r.length;for(n=0;n<s;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(3171),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},1683:()=>{},6611:()=>{},7724:()=>{},310:()=>{},3171:e=>{var t=1e3,n=60*t,r=60*n,s=24*r,a=7*s,i=365.25*s;function o(e,t,n,r){var s=t>=1.5*n;return Math.round(e/n)+" "+r+(s?"s":"")}e.exports=function(e,c){c=c||{};var l=typeof e;if("string"===l&&e.length>0)return function(e){if((e=String(e)).length>100)return;var o=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!o)return;var c=parseFloat(o[1]);switch((o[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*i;case"weeks":case"week":case"w":return c*a;case"days":case"day":case"d":return c*s;case"hours":case"hour":case"hrs":case"hr":case"h":return c*r;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(e);if("number"===l&&isFinite(e))return c.long?function(e){var a=Math.abs(e);if(a>=s)return o(e,a,s,"day");if(a>=r)return o(e,a,r,"hour");if(a>=n)return o(e,a,n,"minute");if(a>=t)return o(e,a,t,"second");return e+" ms"}(e):function(e){var a=Math.abs(e);if(a>=s)return Math.round(e/s)+"d";if(a>=r)return Math.round(e/r)+"h";if(a>=n)return Math.round(e/n)+"m";if(a>=t)return Math.round(e/t)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},469:(e,t,n)=>{"use strict";var r=n(758);function s(){}function a(){}a.resetWarningCache=s,e.exports=function(){function e(e,t,n,s,a,i){if(i!==r){var o=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:s};return n.PropTypes=n,n}},5162:(e,t,n)=>{e.exports=n(469)()},758:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},6975:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(951);const s=n.n(r)()("dops:analytics");let a,i;window._tkq=window._tkq||[],window.ga=window.ga||function(){(window.ga.q=window.ga.q||[]).push(arguments)},window.ga.l=+new Date;const o={initialize:function(e,t,n){o.setUser(e,t),o.setSuperProps(n),o.identifyUser()},setGoogleAnalyticsEnabled:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.googleAnalyticsEnabled=e,this.googleAnalyticsKey=t},setMcAnalyticsEnabled:function(e){this.mcAnalyticsEnabled=e},setUser:function(e,t){i={ID:e,username:t}},setSuperProps:function(e){a=e},assignSuperProps:function(e){a=Object.assign(a||{},e)},mc:{bumpStat:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&x_"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);s("Bumping stats %o",e)}else n="&x_"+encodeURIComponent(e)+"="+encodeURIComponent(t),s('Bumping stat "%s" in group "%s"',t,e);return n}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom-no-pv"+n+"&t="+Math.random())},bumpStatWithPageView:function(e,t){const n=function(e,t){let n="";if("object"==typeof e){for(const t in e)n+="&"+encodeURIComponent(t)+"="+encodeURIComponent(e[t]);s("Built stats %o",e)}else n="&"+encodeURIComponent(e)+"="+encodeURIComponent(t),s('Built stat "%s" in group "%s"',t,e);return n}(e,t);o.mcAnalyticsEnabled&&((new Image).src=document.location.protocol+"//pixel.wp.com/g.gif?v=wpcom"+n+"&t="+Math.random())}},pageView:{record:function(e,t){o.tracks.recordPageView(e),o.ga.recordPageView(e,t)}},purchase:{record:function(e,t,n,r,s,a,i){o.ga.recordPurchase(e,t,n,r,s,a,i)}},tracks:{recordEvent:function(e,t){t=t||{},0===e.indexOf("akismet_")||0===e.indexOf("jetpack_")?(a&&(s("- Super Props: %o",a),t=Object.assign(t,a)),s('Record event "%s" called with props %s',e,JSON.stringify(t)),window._tkq.push(["recordEvent",e,t])):s('- Event name must be prefixed by "akismet_" or "jetpack_"')},recordJetpackClick:function(e){const t="object"==typeof e?e:{target:e};o.tracks.recordEvent("jetpack_wpa_click",t)},recordPageView:function(e){o.tracks.recordEvent("akismet_page_view",{path:e})},setOptOut:function(e){s("Pushing setOptOut: %o",e),window._tkq.push(["setOptOut",e])}},ga:{initialized:!1,initialize:function(){let e={};o.ga.initialized||(i&&(e={userId:"u-"+i.ID}),window.ga("create",this.googleAnalyticsKey,"auto",e),o.ga.initialized=!0)},recordPageView:function(e,t){o.ga.initialize(),s("Recording Page View ~ [URL: "+e+"] [Title: "+t+"]"),this.googleAnalyticsEnabled&&(window.ga("set","page",e),window.ga("send",{hitType:"pageview",page:e,title:t}))},recordEvent:function(e,t,n,r){o.ga.initialize();let a="Recording Event ~ [Category: "+e+"] [Action: "+t+"]";void 0!==n&&(a+=" [Option Label: "+n+"]"),void 0!==r&&(a+=" [Option Value: "+r+"]"),s(a),this.googleAnalyticsEnabled&&window.ga("send","event",e,t,n,r)},recordPurchase:function(e,t,n,r,s,a,i){window.ga("require","ecommerce"),window.ga("ecommerce:addTransaction",{id:e,revenue:r,currency:i}),window.ga("ecommerce:addItem",{id:e,name:t,sku:n,price:s,quantity:a}),window.ga("ecommerce:send")}},identifyUser:function(){i&&window._tkq.push(["identifyUser",i.ID,i.username])},setProperties:function(e){window._tkq.push(["setProperties",e])},clearedIdentity:function(){window._tkq.push(["clearIdentity"])}},c=o},4743:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>u});var r=n(1132),s=n(6483);function a(e){class t extends Error{constructor(){super(...arguments),this.name=e}}return t}const i=a("JsonParseError"),o=a("JsonParseAfterRedirectError"),c=a("Api404Error"),l=a("Api404AfterRedirectError"),d=a("FetchNetworkError");const u=new function(e,t){let n=e,a=e,i={"X-WP-Nonce":t},o={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})},l=function(e){const t=e.split("?"),n=t.length>1?t[1]:"",r=n.length?n.split("&"):[];return r.push("_cacheBuster="+(new Date).getTime()),t[0]+"?"+r.join("&")};const d={setApiRoot(e){n=e},setWpcomOriginApiUrl(e){a=e},setApiNonce(e){i={"X-WP-Nonce":e},o={credentials:"same-origin",headers:i},c={method:"post",credentials:"same-origin",headers:Object.assign({},i,{"Content-type":"application/json"})}},setCacheBusterCallback:e=>{l=e},registerSite:(e,t)=>{const s={registration_nonce:e,no_iframe:!0};return(0,r.jetpackConfigHas)("consumer_slug")&&(s.plugin_slug=(0,r.jetpackConfigGet)("consumer_slug")),null!==t&&(s.redirect_uri=t),h(`${n}jetpack/v4/connection/register`,c,{body:JSON.stringify(s)}).then(p).then(m)},fetchAuthorizationUrl:e=>u((0,s.addQueryArgs)(`${n}jetpack/v4/connection/authorize_url`,{no_iframe:"1",redirect_uri:e}),o).then(p).then(m),fetchSiteConnectionData:()=>u(`${n}jetpack/v4/connection/data`,o).then(m),fetchSiteConnectionStatus:()=>u(`${n}jetpack/v4/connection`,o).then(m),fetchSiteConnectionTest:()=>u(`${n}jetpack/v4/connection/test`,o).then(p).then(m),fetchUserConnectionData:()=>u(`${n}jetpack/v4/connection/data`,o).then(m),fetchUserTrackingSettings:()=>u(`${n}jetpack/v4/tracking/settings`,o).then(p).then(m),updateUserTrackingSettings:e=>h(`${n}jetpack/v4/tracking/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),disconnectSite:()=>h(`${n}jetpack/v4/connection`,c,{body:JSON.stringify({isActive:!1})}).then(p).then(m),fetchConnectUrl:()=>u(`${n}jetpack/v4/connection/url`,o).then(p).then(m),unlinkUser:()=>h(`${n}jetpack/v4/connection/user`,c,{body:JSON.stringify({linked:!1})}).then(p).then(m),reconnect:()=>h(`${n}jetpack/v4/connection/reconnect`,c).then(p).then(m),fetchConnectedPlugins:()=>u(`${n}jetpack/v4/connection/plugins`,o).then(p).then(m),setHasSeenWCConnectionModal:()=>h(`${n}jetpack/v4/seen-wc-connection-modal`,c).then(p).then(m),fetchModules:()=>u(`${n}jetpack/v4/module/all`,o).then(p).then(m),fetchModule:e=>u(`${n}jetpack/v4/module/${e}`,o).then(p).then(m),activateModule:e=>h(`${n}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!0})}).then(p).then(m),deactivateModule:e=>h(`${n}jetpack/v4/module/${e}/active`,c,{body:JSON.stringify({active:!1})}),updateModuleOptions:(e,t)=>h(`${n}jetpack/v4/module/${e}`,c,{body:JSON.stringify(t)}).then(p).then(m),updateSettings:e=>h(`${n}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),getProtectCount:()=>u(`${n}jetpack/v4/module/protect/data`,o).then(p).then(m),resetOptions:e=>h(`${n}jetpack/v4/options/${e}`,c,{body:JSON.stringify({reset:!0})}).then(p).then(m),activateVaultPress:()=>h(`${n}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"vaultpress",status:"active"})}).then(p).then(m),getVaultPressData:()=>u(`${n}jetpack/v4/module/vaultpress/data`,o).then(p).then(m),installPlugin:(e,t)=>{const r={slug:e,status:"active"};return t&&(r.source=t),h(`${n}jetpack/v4/plugins`,c,{body:JSON.stringify(r)}).then(p).then(m)},activateAkismet:()=>h(`${n}jetpack/v4/plugins`,c,{body:JSON.stringify({slug:"akismet",status:"active"})}).then(p).then(m),getAkismetData:()=>u(`${n}jetpack/v4/module/akismet/data`,o).then(p).then(m),checkAkismetKey:()=>u(`${n}jetpack/v4/module/akismet/key/check`,o).then(p).then(m),checkAkismetKeyTyped:e=>h(`${n}jetpack/v4/module/akismet/key/check`,c,{body:JSON.stringify({api_key:e})}).then(p).then(m),fetchStatsData:e=>u(function(e){let t=`${n}jetpack/v4/module/stats/data`;-1!==t.indexOf("?")?t+=`&range=${encodeURIComponent(e)}`:t+=`?range=${encodeURIComponent(e)}`;return t}(e),o).then(p).then(m).then(f),getPluginUpdates:()=>u(`${n}jetpack/v4/updates/plugins`,o).then(p).then(m),getPlans:()=>u(`${n}jetpack/v4/plans`,o).then(p).then(m),fetchSettings:()=>u(`${n}jetpack/v4/settings`,o).then(p).then(m),updateSetting:e=>h(`${n}jetpack/v4/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),fetchSiteData:()=>u(`${n}jetpack/v4/site`,o).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteFeatures:()=>u(`${n}jetpack/v4/site/features`,o).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteProducts:()=>u(`${n}jetpack/v4/site/products`,o).then(p).then(m),fetchSitePurchases:()=>u(`${n}jetpack/v4/site/purchases`,o).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteBenefits:()=>u(`${n}jetpack/v4/site/benefits`,o).then(p).then(m).then((e=>JSON.parse(e.data))),fetchSiteDiscount:()=>u(`${n}jetpack/v4/site/discount`,o).then(p).then(m).then((e=>e.data)),fetchSetupQuestionnaire:()=>u(`${n}jetpack/v4/setup/questionnaire`,o).then(p).then(m),fetchRecommendationsData:()=>u(`${n}jetpack/v4/recommendations/data`,o).then(p).then(m),fetchRecommendationsProductSuggestions:()=>u(`${n}jetpack/v4/recommendations/product-suggestions`,o).then(p).then(m),fetchRecommendationsUpsell:()=>u(`${n}jetpack/v4/recommendations/upsell`,o).then(p).then(m),fetchRecommendationsConditional:()=>u(`${n}jetpack/v4/recommendations/conditional`,o).then(p).then(m),saveRecommendationsData:e=>h(`${n}jetpack/v4/recommendations/data`,c,{body:JSON.stringify({data:e})}).then(p),fetchProducts:()=>u(`${n}jetpack/v4/products`,o).then(p).then(m),fetchRewindStatus:()=>u(`${n}jetpack/v4/rewind`,o).then(p).then(m).then((e=>JSON.parse(e.data))),fetchScanStatus:()=>u(`${n}jetpack/v4/scan`,o).then(p).then(m).then((e=>JSON.parse(e.data))),dismissJetpackNotice:e=>h(`${n}jetpack/v4/notice/${e}`,c,{body:JSON.stringify({dismissed:!0})}).then(p).then(m),fetchPluginsData:()=>u(`${n}jetpack/v4/plugins`,o).then(p).then(m),fetchIntroOffers:()=>u(`${n}jetpack/v4/intro-offers`,o).then(p).then(m),fetchVerifySiteGoogleStatus:e=>u(null!==e?`${n}jetpack/v4/verify-site/google/${e}`:`${n}jetpack/v4/verify-site/google`,o).then(p).then(m),verifySiteGoogle:e=>h(`${n}jetpack/v4/verify-site/google`,c,{body:JSON.stringify({keyring_id:e})}).then(p).then(m),submitSurvey:e=>h(`${n}jetpack/v4/marketing/survey`,c,{body:JSON.stringify(e)}).then(p).then(m),saveSetupQuestionnaire:e=>h(`${n}jetpack/v4/setup/questionnaire`,c,{body:JSON.stringify(e)}).then(p).then(m),updateLicensingError:e=>h(`${n}jetpack/v4/licensing/error`,c,{body:JSON.stringify(e)}).then(p).then(m),updateLicenseKey:e=>h(`${n}jetpack/v4/licensing/set-license`,c,{body:JSON.stringify({license:e})}).then(p).then(m),getUserLicensesCounts:()=>u(`${n}jetpack/v4/licensing/user/counts`,o).then(p).then(m),getUserLicenses:()=>u(`${n}jetpack/v4/licensing/user/licenses`,o).then(p).then(m),updateLicensingActivationNoticeDismiss:e=>h(`${n}jetpack/v4/licensing/user/activation-notice-dismiss`,c,{body:JSON.stringify({last_detached_count:e})}).then(p).then(m),updateRecommendationsStep:e=>h(`${n}jetpack/v4/recommendations/step`,c,{body:JSON.stringify({step:e})}).then(p),confirmIDCSafeMode:()=>h(`${n}jetpack/v4/identity-crisis/confirm-safe-mode`,c).then(p),startIDCFresh:e=>h(`${n}jetpack/v4/identity-crisis/start-fresh`,c,{body:JSON.stringify({redirect_uri:e})}).then(p).then(m),migrateIDC:()=>h(`${n}jetpack/v4/identity-crisis/migrate`,c).then(p),attachLicenses:e=>h(`${n}jetpack/v4/licensing/attach-licenses`,c,{body:JSON.stringify({licenses:e})}).then(p).then(m),fetchSearchPlanInfo:()=>u(`${a}jetpack/v4/search/plan`,o).then(p).then(m),fetchSearchSettings:()=>u(`${a}jetpack/v4/search/settings`,o).then(p).then(m),updateSearchSettings:e=>h(`${a}jetpack/v4/search/settings`,c,{body:JSON.stringify(e)}).then(p).then(m),fetchSearchStats:()=>u(`${a}jetpack/v4/search/stats`,o).then(p).then(m),fetchWafSettings:()=>u(`${n}jetpack/v4/waf`,o).then(p).then(m),updateWafSettings:e=>h(`${n}jetpack/v4/waf`,c,{body:JSON.stringify(e)}).then(p).then(m),fetchWordAdsSettings:()=>u(`${n}jetpack/v4/wordads/settings`,o).then(p).then(m),updateWordAdsSettings:e=>h(`${n}jetpack/v4/wordads/settings`,c,{body:JSON.stringify(e)}),fetchSearchPricing:()=>u(`${a}jetpack/v4/search/pricing`,o).then(p).then(m),fetchMigrationStatus:()=>u(`${n}jetpack/v4/migration/status`,o).then(p).then(m)};function u(e,t){return fetch(l(e),t)}function h(e,t,n){return fetch(e,Object.assign({},t,n)).catch(g)}function f(e){return e.general&&void 0===e.general.response||e.week&&void 0===e.week.response||e.month&&void 0===e.month.response?e:{}}Object.assign(this,d)};function p(e){return e.status>=200&&e.status<300?e:404===e.status?new Promise((()=>{throw e.redirected?new l(e.redirected):new c})):e.json().catch((e=>h(e))).then((t=>{const n=new Error(`${t.message} (Status ${e.status})`);throw n.response=t,n.name="ApiError",n}))}function m(e){return e.json().catch((t=>h(t,e.redirected,e.url)))}function h(e,t,n){throw t?new o(n):new i}function g(){throw new d}},8868:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var r=n(2674),s=n.n(r),a=n(5736),i=n(5235),o=n.n(i),c=n(9196),l=n.n(c);const __=a.__,d=e=>{let{logoColor:t="#069e08",showText:n=!0,className:r,height:a=32,...i}=e;const c=n?"0 0 118 32":"0 0 32 32";return l().createElement("svg",s()({xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:c,className:o()("jetpack-logo",r),"aria-labelledby":"jetpack-logo-title",height:a},i),l().createElement("desc",{id:"jetpack-logo-title"},__("Jetpack Logo","jetpack-idc")),l().createElement("path",{fill:t,d:"M16,0C7.2,0,0,7.2,0,16s7.2,16,16,16s16-7.2,16-16S24.8,0,16,0z M15,19H7l8-16V19z M17,29V13h8L17,29z"}),n&&l().createElement(l().Fragment,null,l().createElement("path",{d:"M41.3,26.6c-0.5-0.7-0.9-1.4-1.3-2.1c2.3-1.4,3-2.5,3-4.6V8h-3V6h6v13.4C46,22.8,45,24.8,41.3,26.6z"}),l().createElement("path",{d:"M65,18.4c0,1.1,0.8,1.3,1.4,1.3c0.5,0,2-0.2,2.6-0.4v2.1c-0.9,0.3-2.5,0.5-3.7,0.5c-1.5,0-3.2-0.5-3.2-3.1V12H60v-2h2.1V7.1 H65V10h4v2h-4V18.4z"}),l().createElement("path",{d:"M71,10h3v1.3c1.1-0.8,1.9-1.3,3.3-1.3c2.5,0,4.5,1.8,4.5,5.6s-2.2,6.3-5.8,6.3c-0.9,0-1.3-0.1-2-0.3V28h-3V10z M76.5,12.3 c-0.8,0-1.6,0.4-2.5,1.2v5.9c0.6,0.1,0.9,0.2,1.8,0.2c2,0,3.2-1.3,3.2-3.9C79,13.4,78.1,12.3,76.5,12.3z"}),l().createElement("path",{d:"M93,22h-3v-1.5c-0.9,0.7-1.9,1.5-3.5,1.5c-1.5,0-3.1-1.1-3.1-3.2c0-2.9,2.5-3.4,4.2-3.7l2.4-0.3v-0.3c0-1.5-0.5-2.3-2-2.3 c-0.7,0-2.3,0.5-3.7,1.1L84,11c1.2-0.4,3-1,4.4-1c2.7,0,4.6,1.4,4.6,4.7L93,22z M90,16.4l-2.2,0.4c-0.7,0.1-1.4,0.5-1.4,1.6 c0,0.9,0.5,1.4,1.3,1.4s1.5-0.5,2.3-1V16.4z"}),l().createElement("path",{d:"M104.5,21.3c-1.1,0.4-2.2,0.6-3.5,0.6c-4.2,0-5.9-2.4-5.9-5.9c0-3.7,2.3-6,6.1-6c1.4,0,2.3,0.2,3.2,0.5V13 c-0.8-0.3-2-0.6-3.2-0.6c-1.7,0-3.2,0.9-3.2,3.6c0,2.9,1.5,3.8,3.3,3.8c0.9,0,1.9-0.2,3.2-0.7V21.3z"}),l().createElement("path",{d:"M110,15.2c0.2-0.3,0.2-0.8,3.8-5.2h3.7l-4.6,5.7l5,6.3h-3.7l-4.2-5.8V22h-3V6h3V15.2z"}),l().createElement("path",{d:"M58.5,21.3c-1.5,0.5-2.7,0.6-4.2,0.6c-3.6,0-5.8-1.8-5.8-6c0-3.1,1.9-5.9,5.5-5.9s4.9,2.5,4.9,4.9c0,0.8,0,1.5-0.1,2h-7.3 c0.1,2.5,1.5,2.8,3.6,2.8c1.1,0,2.2-0.3,3.4-0.7C58.5,19,58.5,21.3,58.5,21.3z M56,15c0-1.4-0.5-2.9-2-2.9c-1.4,0-2.3,1.3-2.4,2.9 C51.6,15,56,15,56,15z"})))}},5033:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(5162),s=n.n(r),a=n(9196),i=n.n(a);n(1683);const o=e=>{const t=e.className+" jp-components-spinner",n={width:e.size,height:e.size,fontSize:e.size,borderTopColor:e.color},r={borderTopColor:e.color,borderRightColor:e.color};return i().createElement("div",{className:t},i().createElement("div",{className:"jp-components-spinner__outer",style:n},i().createElement("div",{className:"jp-components-spinner__inner",style:r})))};o.propTypes={color:s().string,className:s().string,size:s().number},o.defaultProps={color:"#FFFFFF",className:"",size:20};const c=o},6895:(e,t,n)=>{"use strict";function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n={};let r;if("undefined"!=typeof window&&(r=window.Initial_State?.calypsoEnv),0===e.search("https://")){const t=new URL(e);e=`https://${t.host}${t.pathname}`,n.url=encodeURIComponent(e)}else n.source=encodeURIComponent(e);Object.keys(t).map((e=>{n[e]=encodeURIComponent(t[e])})),!Object.keys(n).includes("site")&&"undefined"!=typeof jetpack_redirects&&jetpack_redirects.hasOwnProperty("currentSiteRawUrl")&&(n.site=jetpack_redirects.currentSiteRawUrl),r&&(n.calypso_env=r);return"https://jetpack.com/redirect/?"+Object.keys(n).map((e=>e+"="+n[e])).join("&")}n.d(t,{Z:()=>r})},1132:(e,t,n)=>{let r={};try{r=n(8510)}catch{console.error("jetpackConfig is missing in your webpack config file. See @automattic/jetpack-config"),r={missingConfig:!0}}const s=e=>r.hasOwnProperty(e);e.exports={jetpackConfigHas:s,jetpackConfigGet:e=>{if(!s(e))throw'This app requires the "'+e+'" Jetpack Config to be defined in your webpack configuration file. See details in @automattic/jetpack-config package docs.';return r[e]}}},9477:(e,t,n)=>{"use strict";n.d(t,{Z:()=>k});var r=n(6895),s=n(5033),a=n(5609),i=n(9818),o=n(9307),c=n(5736),l=n(5162),d=n.n(l),u=n(9196),p=n.n(u),m=n(2678),h=n(7144),g=n(2115),f=n(6637);const __=c.__,y=e=>{const{isStartingFresh:t,startFreshCallback:n,customContent:l,hasError:d}=e,u=(0,g.Z)(e.wpcomHomeUrl),h=(0,g.Z)(e.currentUrl),y=(0,i.useSelect)((e=>e(m.t).getIsActionInProgress()),[]),k=l.startFreshButtonLabel||__("Create a fresh connection","jetpack-idc");return p().createElement("div",{className:"jp-idc__idc-screen__card-action-base"+(d?" jp-idc__idc-screen__card-action-error":"")},p().createElement("div",{className:"jp-idc__idc-screen__card-action-top"},p().createElement("h4",null,l.startFreshCardTitle?(0,o.createInterpolateElement)(l.startFreshCardTitle,{em:p().createElement("em",null)}):__("Treat each site as independent sites","jetpack-idc")),p().createElement("p",null,(0,o.createInterpolateElement)(l.startFreshCardBodyText||(0,c.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("<hostname>%1$s</hostname> settings, stats, and subscribers will start fresh. <hostname>%2$s</hostname> will keep its data as is.","jetpack-idc"),h,u),{hostname:p().createElement("strong",null),em:p().createElement("em",null),strong:p().createElement("strong",null)}))),p().createElement("div",{className:"jp-idc__idc-screen__card-action-bottom"},p().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},u),p().createElement(a.Dashicon,{icon:"minus",className:"jp-idc__idc-screen__card-action-separator"}),p().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},h),p().createElement(a.Button,{className:"jp-idc__idc-screen__card-action-button",label:k,onClick:n,disabled:y},t?p().createElement(s.Z,null):k),d&&(v=l.supportURL,p().createElement(f.Z,null,(0,o.createInterpolateElement)(__("Could not create the connection. Retry or find out more <a>here</a>.","jetpack-idc"),{a:p().createElement("a",{href:v||(0,r.Z)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})))));var v};y.propTypes={wpcomHomeUrl:d().string.isRequired,currentUrl:d().string.isRequired,isStartingFresh:d().bool.isRequired,startFreshCallback:d().func.isRequired,customContent:d().shape(h.Z),hasError:d().bool.isRequired},y.defaultProps={isStartingFresh:!1,startFreshCallback:()=>{},customContent:{},hasError:!1};const k=y},6596:(e,t,n)=>{"use strict";n.d(t,{Z:()=>k});var r=n(6895),s=n(5033),a=n(5609),i=n(9818),o=n(9307),c=n(5736),l=n(5162),d=n.n(l),u=n(9196),p=n.n(u),m=n(2678),h=n(7144),g=n(2115),f=n(6637);const __=c.__,y=e=>{const t=(0,g.Z)(e.wpcomHomeUrl),n=(0,g.Z)(e.currentUrl),l=(0,i.useSelect)((e=>e(m.t).getIsActionInProgress()),[]),{isMigrating:d,migrateCallback:u,customContent:h,hasError:y}=e,k=h.migrateButtonLabel||__("Move your settings","jetpack-idc");return p().createElement("div",{className:"jp-idc__idc-screen__card-action-base"+(y?" jp-idc__idc-screen__card-action-error":"")},p().createElement("div",{className:"jp-idc__idc-screen__card-action-top"},p().createElement("h4",null,h.migrateCardTitle?(0,o.createInterpolateElement)(h.migrateCardTitle,{em:p().createElement("em",null)}):__("Move Jetpack data","jetpack-idc")),p().createElement("p",null,(0,o.createInterpolateElement)(h.migrateCardBodyText||(0,c.sprintf)(/* translators: %1$s: The current site domain name. %2$s: The original site domain name. */
__("Move all your settings, stats and subscribers to your other URL, <hostname>%1$s</hostname>. <hostname>%2$s</hostname> will be disconnected from Jetpack.","jetpack-idc"),n,t),{hostname:p().createElement("strong",null),em:p().createElement("em",null),strong:p().createElement("strong",null)}))),p().createElement("div",{className:"jp-idc__idc-screen__card-action-bottom"},p().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},t),p().createElement(a.Dashicon,{icon:"arrow-down-alt",className:"jp-idc__idc-screen__card-action-separator"}),p().createElement("div",{className:"jp-idc__idc-screen__card-action-sitename"},n),p().createElement(a.Button,{className:"jp-idc__idc-screen__card-action-button",label:k,onClick:u,disabled:l},d?p().createElement(s.Z,null):k),y&&(v=h.supportURL,p().createElement(f.Z,null,(0,o.createInterpolateElement)(__("Could not move your settings. Retry or find out more <a>here</a>.","jetpack-idc"),{a:p().createElement("a",{href:v||(0,r.Z)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})})))));var v};y.propTypes={wpcomHomeUrl:d().string.isRequired,currentUrl:d().string.isRequired,isMigrating:d().bool.isRequired,migrateCallback:d().func.isRequired,customContent:d().shape(h.Z),hasError:d().bool.isRequired},y.defaultProps={isMigrating:!1,migrateCallback:()=>{},customContent:{},hasError:!1};const k=y},5489:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(9196),s=n.n(r);const a=()=>s().createElement("svg",{className:"error-gridicon",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",height:24},s().createElement("rect",{x:"0",fill:"none",width:"24",height:"24"}),s().createElement("g",null,s().createElement("path",{d:"M12 4c4.411 0 8 3.589 8 8s-3.589 8-8 8-8-3.589-8-8 3.589-8 8-8m0-2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm1 13h-2v2h2v-2zm-2-2h2l.5-6h-3l.5 6z"})))},6637:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(9196),s=n.n(r),a=n(5489);n(6611);const i=e=>{const{children:t}=e;return s().createElement("div",{className:"jp-idc__error-message"},s().createElement(a.Z,null),s().createElement("span",null,t))}},7381:(e,t,n)=>{"use strict";n.d(t,{Z:()=>y});var r=n(4743),s=n(9818),a=n(5162),i=n.n(a),o=n(9196),c=n.n(o),l=n(970),d=n(1272),u=n(3105),p=n(2678),m=n(7144),h=n(6521),g=n(567);const f=e=>{const{logo:t,customContent:n,wpcomHomeUrl:a,currentUrl:i,apiNonce:m,apiRoot:f,redirectUri:y,tracksUserData:k,tracksEventData:v,isAdmin:C,possibleDynamicSiteUrlDetected:_}=e,[b,E]=(0,o.useState)(!1),w=(0,s.useSelect)((e=>e(p.t).getErrorType()),[]),{isMigrating:j,migrateCallback:S}=(0,l.Z)((0,o.useCallback)((()=>{E(!0)}),[E])),{isStartingFresh:F,startFreshCallback:I}=(0,u.Z)(y),{isFinishingMigration:U,finishMigrationCallback:R}=(0,d.Z)();return(0,o.useEffect)((()=>{r.ZP.setApiRoot(f),r.ZP.setApiNonce(m),(0,h.M)(v,k),v&&(v.hasOwnProperty("isAdmin")&&v.isAdmin?(0,h.Z)("notice_view"):(0,h.Z)("non_admin_notice_view",{page:!!v.hasOwnProperty("currentScreen")&&v.currentScreen}))}),[f,m,k,v]),c().createElement(g.Z,{logo:t,customContent:n,wpcomHomeUrl:a,currentUrl:i,redirectUri:y,isMigrating:j,migrateCallback:S,isMigrated:b,finishMigrationCallback:R,isFinishingMigration:U,isStartingFresh:F,startFreshCallback:I,isAdmin:C,hasStaySafeError:"safe-mode"===w,hasFreshError:"start-fresh"===w,hasMigrateError:"migrate"===w,possibleDynamicSiteUrlDetected:_})};f.propTypes={logo:i().object,customContent:i().shape(m.Z),wpcomHomeUrl:i().string.isRequired,currentUrl:i().string.isRequired,redirectUri:i().string.isRequired,apiRoot:i().string.isRequired,apiNonce:i().string.isRequired,tracksUserData:i().object,tracksEventData:i().object,isAdmin:i().bool.isRequired,possibleDynamicSiteUrlDetected:i().bool},f.defaultProps={customContent:{}};const y=f},5533:(e,t,n)=>{"use strict";n.d(t,{Z:()=>g});var r=n(6895),s=n(9307),a=n(5736),i=n(5162),o=n.n(i),c=n(9196),l=n.n(c),d=n(7144),u=n(9477),p=n(6596),m=n(2533);const __=a.__,h=e=>{const{wpcomHomeUrl:t,currentUrl:n,isMigrating:a,migrateCallback:i,isStartingFresh:o,startFreshCallback:c,customContent:d,hasMigrateError:h,hasFreshError:g,hasStaySafeError:f,possibleDynamicSiteUrlDetected:y}=e;return l().createElement(l().Fragment,null,l().createElement("h2",null,d.mainTitle?(0,s.createInterpolateElement)(d.mainTitle,{em:l().createElement("em",null)}):__("Safe Mode has been activated","jetpack-idc")),l().createElement("p",null,(0,s.createInterpolateElement)(d.mainBodyText||__("Your site is in Safe Mode because you have 2 Jetpack-powered sites that appear to be duplicates. 2 sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>","jetpack-idc"),{safeModeLink:l().createElement("a",{href:d.supportURL||(0,r.Z)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"}),em:l().createElement("em",null),strong:l().createElement("strong",null)})),y&&l().createElement("p",null,(0,s.createInterpolateElement)(d.dynamicSiteUrlText||__("<strong>Notice:</strong> It appears that your 'wp-config.php' file might be using dynamic site URL values. Dynamic site URLs could cause Jetpack to enter Safe Mode. <dynamicSiteUrlSupportLink>Learn how to set a static site URL.</dynamicSiteUrlSupportLink>","jetpack-idc"),{dynamicSiteUrlSupportLink:l().createElement("a",{href:d.dynamicSiteUrlSupportLink||(0,r.Z)("jetpack-idcscreen-dynamic-site-urls"),rel:"noopener noreferrer",target:"_blank"}),em:l().createElement("em",null),strong:l().createElement("strong",null)})),l().createElement("h3",null,__("Please select an option","jetpack-idc")),l().createElement("div",{className:"jp-idc__idc-screen__cards"+(h||g?" jp-idc__idc-screen__cards-error":"")},l().createElement(p.Z,{wpcomHomeUrl:t,currentUrl:n,isMigrating:a,migrateCallback:i,customContent:d,hasError:h}),l().createElement("div",{className:"jp-idc__idc-screen__cards-separator"},"or"),l().createElement(u.Z,{wpcomHomeUrl:t,currentUrl:n,isStartingFresh:o,startFreshCallback:c,customContent:d,hasError:g})),l().createElement(m.Z,{hasError:f,customContent:d}))};h.propTypes={wpcomHomeUrl:o().string.isRequired,currentUrl:o().string.isRequired,isMigrating:o().bool.isRequired,migrateCallback:o().func,isStartingFresh:o().bool.isRequired,startFreshCallback:o().func,customContent:o().shape(d.Z),hasMigrateError:o().bool.isRequired,hasFreshError:o().bool.isRequired,hasStaySafeError:o().bool.isRequired,possibleDynamicSiteUrlDetected:o().bool},h.defaultProps={isMigrating:!1,isStartingFresh:!1,customContent:{},hasMigrateError:!1,hasFreshError:!1,hasStaySafeError:!1,possibleDynamicSiteUrlDetected:!1};const g=h},1052:(e,t,n)=>{"use strict";n.d(t,{Z:()=>h});var r=n(5033),s=n(5609),a=n(9307),i=n(5736),o=n(5162),c=n.n(o),l=n(9196),d=n.n(l),u=n(7144),p=n(2115);const __=i.__,m=e=>{const{finishCallback:t,isFinishing:n,customContent:o}=e,c=(0,p.Z)(e.wpcomHomeUrl),l=(0,p.Z)(e.currentUrl),u=__("Got it, thanks","jetpack-idc");return d().createElement(d().Fragment,null,d().createElement("h2",null,o.migratedTitle?(0,a.createInterpolateElement)(o.migratedTitle,{em:d().createElement("em",null)}):__("Your Jetpack settings have migrated successfully","jetpack-idc")),d().createElement("p",null,(0,a.createInterpolateElement)(o.migratedBodyText||(0,i.sprintf)(/* translators: %1$s: The current site domain name. */
__("Safe Mode has been switched off for <hostname>%1$s</hostname> website and Jetpack is fully functional.","jetpack-idc"),l),{hostname:d().createElement("strong",null),em:d().createElement("em",null),strong:d().createElement("strong",null)})),d().createElement("div",{className:"jp-idc__idc-screen__card-migrated"},d().createElement("div",{className:"jp-idc__idc-screen__card-migrated-hostname"},c),d().createElement(s.Dashicon,{icon:"arrow-down-alt",className:"jp-idc__idc-screen__card-migrated-separator"}),d().createElement(s.Dashicon,{icon:"arrow-right-alt",className:"jp-idc__idc-screen__card-migrated-separator-wide"}),d().createElement("div",{className:"jp-idc__idc-screen__card-migrated-hostname"},l)),d().createElement(s.Button,{className:"jp-idc__idc-screen__card-action-button jp-idc__idc-screen__card-action-button-migrated",onClick:t,label:u},n?d().createElement(r.Z,null):u))};m.propTypes={wpcomHomeUrl:c().string.isRequired,currentUrl:c().string.isRequired,finishCallback:c().func,isFinishing:c().bool.isRequired,customContent:c().shape(u.Z)},m.defaultProps={finishCallback:()=>{},isFinishing:!1,customContent:{}};const h=m},1064:(e,t,n)=>{"use strict";n.d(t,{Z:()=>p});var r=n(6895),s=n(9307),a=n(5736),i=n(5162),o=n.n(i),c=n(9196),l=n.n(c),d=n(7144);const __=a.__,u=e=>{const{customContent:t}=e;return l().createElement(l().Fragment,null,l().createElement("h2",null,t.nonAdminTitle?(0,s.createInterpolateElement)(t.nonAdminTitle,{em:l().createElement("em",null)}):__("Safe Mode has been activated","jetpack-idc")),l().createElement("p",null,(0,s.createInterpolateElement)(t.nonAdminBodyText||__("This site is in Safe Mode because there are 2 Jetpack-powered sites that appear to be duplicates. 2 sites that are telling Jetpack they’re the same site. <safeModeLink>Learn more about safe mode.</safeModeLink>","jetpack-idc"),{safeModeLink:l().createElement("a",{href:t.supportURL||(0,r.Z)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"}),em:l().createElement("em",null),strong:l().createElement("strong",null)})),t.nonAdminBodyText?"":l().createElement("p",null,__("An administrator of this site can take Jetpack out of Safe Mode.","jetpack-idc")))};u.propTypes={customContent:o().shape(d.Z)},u.defaultProps={customContent:{}};const p=u},567:(e,t,n)=>{"use strict";n.d(t,{Z:()=>g});var r=n(8868),s=n(9307),a=n(5736),i=n(5162),o=n.n(i),c=n(9196),l=n.n(c),d=n(7144),u=n(5533),p=n(1052),m=n(1064);n(7724);const __=a.__,h=e=>{const{logo:t,customContent:n,wpcomHomeUrl:r,currentUrl:a,redirectUri:i,isMigrating:o,migrateCallback:c,isMigrated:d,finishMigrationCallback:h,isFinishingMigration:g,isStartingFresh:f,startFreshCallback:y,isAdmin:k,hasMigrateError:v,hasFreshError:C,hasStaySafeError:_,possibleDynamicSiteUrlDetected:b}=e,E=k?"":l().createElement(m.Z,{customContent:n});let w="";return k&&(w=d?l().createElement(p.Z,{wpcomHomeUrl:r,currentUrl:a,finishCallback:h,isFinishing:g,customContent:n}):l().createElement(u.Z,{wpcomHomeUrl:r,currentUrl:a,redirectUri:i,customContent:n,isMigrating:o,migrateCallback:c,isStartingFresh:f,startFreshCallback:y,hasMigrateError:v,hasFreshError:C,hasStaySafeError:_,possibleDynamicSiteUrlDetected:b})),l().createElement("div",{className:"jp-idc__idc-screen"+(d?" jp-idc__idc-screen__success":"")},l().createElement("div",{className:"jp-idc__idc-screen__header"},l().createElement("div",{className:"jp-idc__idc-screen__logo"},((e,t)=>"string"==typeof e||e instanceof String?l().createElement("img",{src:e,alt:t,className:"jp-idc__idc-screen__logo-image"}):e)(t,n.logoAlt||"")),l().createElement("div",{className:"jp-idc__idc-screen__logo-label"},n.headerText?(0,s.createInterpolateElement)(n.headerText,{em:l().createElement("em",null),strong:l().createElement("strong",null)}):__("Safe Mode","jetpack-idc"))),E,w)};h.propTypes={logo:o().object.isRequired,customContent:o().shape(d.Z),wpcomHomeUrl:o().string.isRequired,currentUrl:o().string.isRequired,redirectUri:o().string.isRequired,isMigrating:o().bool.isRequired,migrateCallback:o().func,isMigrated:o().bool.isRequired,finishMigrationCallback:o().func,isFinishingMigration:o().bool.isRequired,isStartingFresh:o().bool.isRequired,startFreshCallback:o().func,isAdmin:o().bool.isRequired,hasMigrateError:o().bool.isRequired,hasFreshError:o().bool.isRequired,hasStaySafeError:o().bool.isRequired,possibleDynamicSiteUrlDetected:o().bool},h.defaultProps={logo:l().createElement(r.Z,{height:24}),isMigrated:!1,isFinishingMigration:!1,isMigrating:!1,isStartingFresh:!1,customContent:{},hasMigrateError:!1,hasFreshError:!1,hasStaySafeError:!1,possibleDynamicSiteUrlDetected:!1};const g=h},2533:(e,t,n)=>{"use strict";n.d(t,{Z:()=>_});var r=n(4743),s=n(5033),a=n(6895),i=n(5609),o=n(4333),c=n(9818),l=n(9307),d=n(5736),u=n(6483),p=n(5162),m=n.n(p),h=n(9196),g=n.n(h),f=n(2678),y=n(7144),k=n(6521),v=n(6637);n(310);const __=d.__,C=e=>{const{isActionInProgress:t,setIsActionInProgress:n,setErrorType:o,clearErrorType:c,hasError:d,customContent:p}=e,[m,f]=(0,h.useState)(!1),y=(0,h.useCallback)((()=>{t||(f(!0),n(!0),c(),(0,k.Z)("confirm_safe_mode"),r.ZP.confirmIDCSafeMode().then((()=>{window.location.href=(0,u.removeQueryArgs)(window.location.href,"jetpack_idc_clear_confirmation","_wpnonce")})).catch((e=>{throw n(!1),f(!1),o("safe-mode"),e})))}),[t,n,o,c]);return g().createElement("div",{className:"jp-idc__safe-mode"},m?g().createElement("div",{className:"jp-idc__safe-mode__staying-safe"},g().createElement(s.Z,{color:"black"}),g().createElement("span",null,__("Finishing setting up Safe mode…","jetpack-idc"))):(_=y,b=t,(0,l.createInterpolateElement)(__("Or decide later and stay in <button>Safe mode</button>","jetpack-idc"),{button:g().createElement(i.Button,{label:__("Safe mode","jetpack-idc"),variant:"link",onClick:_,disabled:b})})),d&&(C=p.supportURL,g().createElement(v.Z,null,(0,l.createInterpolateElement)(__("Could not stay in safe mode. Retry or find out more <a>here</a>.","jetpack-idc"),{a:g().createElement("a",{href:C||(0,a.Z)("jetpack-support-safe-mode"),rel:"noopener noreferrer",target:"_blank"})}))));var C,_,b};C.propTypes={isActionInProgress:m().bool,setIsActionInProgress:m().func.isRequired,setErrorType:m().func.isRequired,clearErrorType:m().func.isRequired,hasError:m().bool.isRequired,customContent:m().shape(y.Z)},C.defaultProps={hasError:!1};const _=(0,o.compose)([(0,c.withSelect)((e=>({isActionInProgress:e(f.t).getIsActionInProgress()}))),(0,c.withDispatch)((e=>({setIsActionInProgress:e(f.t).setIsActionInProgress,setErrorType:e(f.t).setErrorType,clearErrorType:e(f.t).clearErrorType})))])(C)},1272:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(9196);const s=()=>{const[e,t]=(0,r.useState)(!1),n=(0,r.useCallback)((()=>{e||(t(!0),window.location.reload())}),[e,t]);return{isFinishingMigration:e,finishMigrationCallback:n}}},970:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(4743),s=n(9818),a=n(9196),i=n(2678),o=n(6521);const c=e=>{const[t,n]=(0,a.useState)(!1),c=(0,s.useSelect)((e=>e(i.t).getIsActionInProgress()),[]),{setIsActionInProgress:l,setErrorType:d,clearErrorType:u}=(0,s.useDispatch)(i.t);return{isMigrating:t,migrateCallback:(0,a.useCallback)((()=>{c||((0,o.Z)("migrate"),l(!0),n(!0),u(),r.ZP.migrateIDC().then((()=>{n(!1),e&&"[object Function]"==={}.toString.call(e)&&e()})).catch((e=>{throw l(!1),n(!1),d("migrate"),e})))}),[n,e,c,l,d,u])}}},3105:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(4743),s=n(9818),a=n(9196),i=n(2678),o=n(6521);const c=e=>{const[t,n]=(0,a.useState)(!1),c=(0,s.useSelect)((e=>e(i.t).getIsActionInProgress()),[]),{setIsActionInProgress:l,setErrorType:d,clearErrorType:u}=(0,s.useDispatch)(i.t);return{isStartingFresh:t,startFreshCallback:(0,a.useCallback)((()=>{c||((0,o.Z)("start_fresh"),l(!0),n(!0),u(),r.ZP.startIDCFresh(e).then((e=>{window.location.href=e+"&from=idc-notice"})).catch((e=>{throw l(!1),n(!1),d("start-fresh"),e})))}),[n,c,l,e,d,u])}}},136:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>i,hG:()=>r,jk:()=>a,vC:()=>s});const r="SET_IS_ACTION_IN_PROGRESS",s="SET_ERROR_TYPE",a="CLEAR_ERROR_TYPE",i={setIsActionInProgress:e=>({type:r,isInProgress:e}),setErrorType:e=>({type:s,errorType:e}),clearErrorType:()=>({type:a})}},7773:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(9818),s=n(136);const a=(0,r.combineReducers)({isActionInProgress:function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1?arguments[1]:void 0;return t.type===s.hG?t.isInProgress:e},errorType:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case s.vC:return t.errorType;case s.jk:return null}return e}})},9416:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r={getIsActionInProgress:e=>e.isActionInProgress||!1,getErrorType:e=>e.errorType||null}},5943:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(17),s=n.n(r),a=n(9818);class i{static mayBeInit(e,t){null===i.store&&(i.store=(0,a.createReduxStore)(e,t),(0,a.register)(i.store))}}s()(i,"store",null);const o=i},2678:(e,t,n)=>{"use strict";n.d(t,{t:()=>o});var r=n(136),s=n(7773),a=n(9416),i=n(5943);const o="jetpack-idc";i.Z.mayBeInit(o,{reducer:s.Z,actions:r.ZP,selectors:a.Z})},7144:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(5162),s=n.n(r);const a={headerText:s().string,logoAlt:s().string,mainTitle:s().string,mainBodyText:s().string,migratedTitle:s().string,migratedBodyText:s().string,migrateCardTitle:s().string,migrateButtonLabel:s().string,migrateCardBodyText:s().string,startFreshCardTitle:s().string,startFreshCardBodyText:s().string,startFreshButtonLabel:s().string,nonAdminTitle:s().string,nonAdminBodyText:s().string,supportURL:s().string}},2115:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r=e=>/^https?:\/\//.test(e)?new URL(e).hostname:e.replace(/\/$/,"")},6521:(e,t,n)=>{"use strict";n.d(t,{M:()=>s,Z:()=>a});var r=n(6975);function s(e,t){t&&t.hasOwnProperty("userid")&&t.hasOwnProperty("username")&&r.Z.initialize(t.userid,t.username),e&&(e.hasOwnProperty("blogID")&&r.Z.assignSuperProps({blog_id:e.blogID}),e.hasOwnProperty("platform")&&r.Z.assignSuperProps({platform:e.platform})),r.Z.setMcAnalyticsEnabled(!0)}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};void 0!==t&&"object"==typeof t||(t={}),e&&e.length&&void 0!==r.Z&&r.Z.tracks&&r.Z.mc&&(e=0!==(e=e.replace(/-/g,"_")).indexOf("jetpack_idc_")?"jetpack_idc_"+e:e,r.Z.tracks.recordEvent(e,t),e=(e=e.replace("jetpack_idc_","")).replace(/_/g,"-"),r.Z.mc.bumpStat("jetpack-idc",e))}},8510:e=>{"use strict";if(void 0==={consumer_slug:"identity_crisis"}){var t=new Error('Cannot find module \'{"consumer_slug":"identity_crisis"}\'');throw t.code="MODULE_NOT_FOUND",t}e.exports={consumer_slug:"identity_crisis"}},9196:e=>{"use strict";e.exports=window.React},5609:e=>{"use strict";e.exports=window.wp.components},4333:e=>{"use strict";e.exports=window.wp.compose},9818:e=>{"use strict";e.exports=window.wp.data},9307:e=>{"use strict";e.exports=window.wp.element},5736:e=>{"use strict";e.exports=window.wp.i18n},6483:e=>{"use strict";e.exports=window.wp.url},17:(e,t,n)=>{var r=n(6930);e.exports=function(e,t,n){return(t=r(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports},2674:e=>{function t(){return e.exports=t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,t.apply(this,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports},5061:(e,t,n)=>{var r=n(3522).default;e.exports=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!==r(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},6930:(e,t,n)=>{var r=n(3522).default,s=n(5061);e.exports=function(e){var t=s(e,"string");return"symbol"===r(t)?t:String(t)},e.exports.__esModule=!0,e.exports.default=e.exports},3522:e=>{function t(n){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function n(r){var s=t[r];if(void 0!==s)return s.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(7381),t=n(9307),r=n(9196),s=n.n(r);!function(){const n=document.getElementById("jp-identity-crisis-container");if(null===n||!window.hasOwnProperty("JP_IDENTITY_CRISIS__INITIAL_STATE"))return;const{WP_API_root:r,WP_API_nonce:a,wpcomHomeUrl:i,currentUrl:o,redirectUri:c,tracksUserData:l,tracksEventData:d,isSafeModeConfirmed:u,consumerData:p,isAdmin:m,possibleDynamicSiteUrlDetected:h}=window.JP_IDENTITY_CRISIS__INITIAL_STATE;if(!u){const u=s().createElement(e.Z,{wpcomHomeUrl:i,currentUrl:o,apiRoot:r,apiNonce:a,redirectUri:c,tracksUserData:l||{},tracksEventData:d,customContent:p.hasOwnProperty("customContent")?p.customContent:{},isAdmin:m,logo:p.hasOwnProperty("logo")?p.logo:void 0,possibleDynamicSiteUrlDetected:h});t.createRoot?t.createRoot(n).render(u):t.render(u,n)}}()})()})();