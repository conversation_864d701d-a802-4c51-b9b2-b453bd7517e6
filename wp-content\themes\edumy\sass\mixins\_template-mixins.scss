// Box Size
// -------------------------
@mixin box-size($background, $padding-top,$padding-bottom){
    background: $background;
    padding-top: $padding-top;
    padding-bottom: $padding-bottom;
}
// Button
// -------------------------
  
@mixin button-3d($suffixclass,$horizontal ,$height3d , $color3d, $h-shadow:0, $v-shadow:0){
  border: 0;
  @if ($suffixclass == "empty") {
      box-shadow: $horizontal $height3d $h-shadow $v-shadow $color3d inset;
      -o-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -moz-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -webkit-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -ms-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
  }
  @else {
  &.btn-#{$suffixclass}{
     box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -o-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -moz-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -webkit-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
      -ms-box-shadow: $horizontal $height3d $h-shadow $v-shadow  $color3d inset;
    }
  }
}

@mixin btn-gradient-hover($color-start,$color-end){
  &:hover{
      @include gradient-vertical($color-start,$color-end);
  }
}

@mixin button-inverse( $suffixclass,  $color ,$background ){
  &.btn-#{$suffixclass}{
      &:hover{ 
        color:$color;
        background:$background;
      }
  }
}

@mixin button-outline( $suffixclass, $color, $hovercolor ){
  &.btn-#{$suffixclass}{
      background:transparent;
      border-color:$color;
      color:$color;
      &:hover{
        color:$hovercolor;
        border-color:$color;
        background:$color;
      }
  }
}
/// button variant outline
@mixin button-variant-outline($color, $background, $border, $colorhover, $bghover, $borderhover ) {
  color: $color;
  background-color: $background;
  border-color: $border;

  &:hover,
  &:focus,
  &:active,
  &.active {
    color: $colorhover;
    background-color: $bghover;
        border-color: $borderhover ;
  }
  .open & { &.dropdown-toggle {
    color: $colorhover;
    background-color: $bghover;
        border-color: $borderhover ;
  } }
  &:active,
  &.active {
    background-image: none;
  }
  .open & { &.dropdown-toggle {
    background-image: none;
  } }
  &.disabled,
  &[disabled],
  fieldset[disabled] & {
    &,
    &:hover,
    &:focus,
    &:active,
    &.active {
      background-color: $background;
          border-color: $border;
    }
  }

  .badge {
    color: $background;
    background-color: $color;
  }
}
// icon variant inverse

@mixin icons-inverse( $suffixclass,  $color ,$background ){
 
  &.icons-#{$suffixclass}{
      &:hover{ 
        color:$color;
        background:transparent;
      }
  }
}
// icon variant outline

@mixin icons-outline( $suffixclass, $color, $hovercolor ){
  &.icons-#{$suffixclass}{
  background:transparent;
      color:$color;
      &:hover{
          color:$hovercolor;
      }
  }
}

// Block
// -------------------------
@mixin block-elements-styles($border, $heading-text-color, $heading-bg-color, $heading-border, $text-color, $text-color-primary){
    border-color: $border;
    background: $heading-bg-color;
    & .#{$block-heading-selector} {
        color: $heading-text-color;
        background-color: $heading-bg-color;
        border-color: $heading-border;
        + .#{$block-prefix}-collapse .#{$block-content-selector} {
            border-top-color: $border;
        }
    }
    & > .#{$block-prefix}-footer {
        + .#{$block-prefix}-collapse .#{$block-prefix}-body {
            border-bottom-color: $border;
        }
    }
}
@mixin block-variant($heading-text-color, $heading-bg-color, $heading-border) {
    > .#{$block-heading-selector} {
        color: $heading-text-color;
        background-color: $heading-bg-color;
        border-color: $heading-border;
        font-size: 16px;
        + *{
          border-color:$border-color;
          border-style:solid;
          border-width:0 1px 1px 1px;
        }  
    }  
}
@mixin block-variant-footer() {
    border-color: transparent;
    & > .#{$block-heading-selector} {
        background-color: transparent;
        border-color: transparent;
         &:before{
            border-top: 0 solid transparent;
            border-right: 0 solid transparent ;
            border-left: 0 solid transparent;
            position: absolute;
            bottom: 0;
            left: 0;
            content: "";
        }
    }  
}
/****/
@mixin container-layout-variant($color, $background, $linkcolor,  $topbar-link-color-hover ){
    background: $background;
    color: $color;
    a{
        color:$linkcolor;
    }
    a:hover{
        color: $topbar-link-color-hover;
    }
}

//== Inline block
//==========================================
@mixin inline-block ($haslayout : true){
    display: inline-block;
    vertical-align: middle;
    @if $haslayout == true {
        .lt-ie8 & {
            display: inline;
            zoom: 1;
        }
    }
}

@mixin hover {
  &:hover {
    @content;
  }  
}

@mixin hover-active {
  &:hover {
    @content;
  }
  &:active {
    @content;
  }
}

@mixin hover-focus {
  &:hover {
    @content;
  }
  &:focus {
    @content;
  }
}

@mixin hover-focus-active {
  &:hover {
    @content;
  }
  &:focus {
    @content;
  }
  &:active {
    @content;
  }
}

//== vertical block
//==========================================
@mixin vertical-center( $width: 100px, $height: 100px) {
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
    width: $width;
    height: $height;
    position: absolute;
}

@mixin vertical-align($position: relative) {
    position: $position;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    transform: translateY(-50%);
}

@mixin center-align($position: relative) {
    position: $position;
    left: 50%;
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
}

@mixin center-box($position: relative) {    
    top: 50%;
    left: 50%;
    position: $position;
    -webkit-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    -moz-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
}

//== Translate X - Y - Z
//==========================================
@mixin translateX($x) {
    -webkit-transform: translateX($x);
    -ms-transform: translateX($x); // IE9 only
    -o-transform: translateX($x);
    transform: translateX($x);
}

@mixin translateY($y) {
    -webkit-transform: translateY($y);
    -ms-transform: translateY($y); // IE9 only
    -o-transform: translateY($y);
    transform: translateY($y);
}

@mixin translateZ($z) {
    -webkit-transform: translateZ($z);
    -ms-transform: translateZ($z); // IE9 only
    -o-transform: translateZ($z);
    transform: translateZ($z);
}

//== Transform
//==========================================
@mixin transform($argument){
    -webkit-transform: ($argument);
    -moz-transform: ($argument);
    -ms-transform: ($argument);
    -o-transform: ($argument);
    transform: ($argument);
}

//== Transform
//==========================================
@mixin transition-delay($time1,$time2){
    -webkit-transition-delay: ($time1,$time2);
    -moz-transition-delay: ($time1,$time2);
    -ms-transition-delay: ($time1,$time2);
    -o-transition-delay: ($time1,$time2);
    transition-delay: ($time1,$time2);
}

@mixin transition-all($what: all, $time: 0.2s, $how: ease-in-out) {
    -webkit-transition: $what $time $how;
    -moz-transition:    $what $time $how;
    -ms-transition:     $what $time $how;
    -o-transition:      $what $time $how;
    transition:         $what $time $how;
}

//== Background Size
//==========================================
@mixin background-size($size1,$size2) {
    -webkit-background-size: ($size1,$size2);
    -moz-background-size: ($size1,$size2);
    -ms-background-size: ($size1,$size2);
    -o-background-size: ($size1,$size2);
    background-size: ($size1,$size2);
}

//== Background origin
//==========================================
@mixin background-origin($value1,$value2){
    -webkit-background-origin: ($value1,$value2);
    -moz-background-origin: ($value1,$value2);
    -ms-background-origin: ($value1,$value2);
    -o-background-origin: ($value1,$value2);
    background-origin: ($value1,$value2);
}

//== Border radius
//==========================================
@mixin border-radius($radius) {
    border-radius         : $radius;
    -webkit-border-radius : $radius;
    -moz-border-radius    : $radius;
    -ms-border-radius     : $radius;
    -o-border-radius      : $radius;
}

@mixin border-radius-separate($topLeftRadius: 5px, $topRightRadius: 5px, $bottomLeftRadius: 5px, $bottomRightRadius: 5px) {
    -webkit-border-top-left-radius:     $topLeftRadius;
    -webkit-border-top-right-radius:    $topRightRadius;
    -webkit-border-bottom-right-radius: $bottomRightRadius;
    -webkit-border-bottom-left-radius:  $bottomLeftRadius;

    -moz-border-radius-topleft:     $topLeftRadius;
    -moz-border-radius-topright:    $topRightRadius;
    -moz-border-radius-bottomright: $bottomRightRadius;
    -moz-border-radius-bottomleft:  $bottomLeftRadius;

    border-top-left-radius:     $topLeftRadius;
    border-top-right-radius:    $topRightRadius;
    border-bottom-right-radius: $bottomRightRadius;
    border-bottom-left-radius:  $bottomLeftRadius;
}


//== Text Shadow
//==========================================
@mixin text-shadow($shadow) {
    text-shadow         : $shadow;
    -webkit-text-shadow : $shadow;
    -moz-text-shadow    : $shadow;
    -ms-text-shadow     : $shadow;
    -o-text-shadow      : $shadow;
}

//== Transform Origin
//==========================================
@mixin transform-origin($originX,$originY) {
    -webkit-transform-origin : $originX $originY;
    -moz-transform-origin    : $originX $originY;
    -ms-transform-origin     : $originX $originY; // IE9 only
    transform-origin         : $originX $originY;
}

//== appearance
//==========================================
@mixin appearance() {
    -webkit-appearance : none;
    -moz-appearance    : none;
    -o-appearance      : none;
    -ms-appearance     : none;
    appearance         : none;
}

//== selection
//==========================================
$prefixes: ("-moz-", "");
@mixin selection($color, $background) {
    @each $prefix in $prefixes {
        ::#{$prefix}selection {
            color: $color;
            background: $background;
        }
    }
}

//== animation fill mode
//==========================================
@mixin animation-fill-mode($fill) {
    -webkit-animation-fill-mode: $fill;
    -moz-animation-fill-mode: $fill;
    -o-animation-fill-mode: $fill;
    animation-fill-mode: $fill;
}

// Name Animation Underline
@mixin animation-underline($color,$height) {    
    position: relative;                      
    &:after{
        bottom: 0;
        content: "";
        background-color: $color;
        position: absolute;                    
        @include size(100%,$height);
        @include rtl-left(0);                    
        @include transform(scaleX(0));                    
        @include opacity(0);
        @include transform-origin(100%, 50%);
        @include transition(transform .3s cubic-bezier(.77,0,.175,1),opacity .5s); 
    }
    &:hover, &:focus, &:active{                    
        background-color: transparent;
        &:after{
            @include opacity(1);                        
            @include transform-origin(0, 50%);
            @include transform(scaleX(1)); 
            @include transition(transform .3s cubic-bezier(.77,0,.175,1),opacity .3s);
        }                  
    }
}

.animated-underline {
  @include animation-underline($theme-color,1px);
}

@mixin animation-underline-center($color,$height) {
  position: relative;    
  &:before,
  &:after {
    content: '';
    position: absolute;
    @include transition(transform 0.2s ease);    
  }
  &:before {
    left: 0;
    bottom: 0;
    width: 100%;
    height: $height;
    background: $color;    
    @include scaleX(0);
  }
  &:hover{
    &:before {
      @include scaleX(1);
    }
  }  
}

.animated-underline-center {
  @include animation-underline-center($theme-color,1px);
}

//== filter
//==========================================
@mixin filter($argument){
    filter         : $argument;
    -webkit-filter : $argument;
    -moz-filter    : $argument;
    -o-filter      : $argument;
    -ms-filter     : $argument;
}

// Clear Lists
// -------------------------
@mixin clear-list(){
    padding    : 0;
    margin     : 0;
    list-style : none;
}

// Formart lists widget
// -------------------------
@mixin lists-style() {
    ul,ol{
        @include clear-list();
        li{
            &:first-child{

            }
            &:last-child{
                border-bottom: 0;
                padding-bottom: 0;
            }
            .children{
                > li{
                    &:before{
                        top: 24px;
                    }
                }
            }
        }
        ul{
            li:first-child{
                padding-top: 14px;
                background-position: 0 24px;
            }
        }
    }
}
@mixin box-shadow-market(){
    @include box-shadow(0 2px 2px -1px rgba(0, 0, 0, 0.1));
    border-bottom: rgba(0, 0, 0, 0.3);
}  


// Position mixin
//==========================================
// @param [string] $position: position type
// @param [list] $args: list of offsets and values
//==========================================
@mixin position($position, $args) {
    @each $o in top right bottom left {
        $i: index($args, $o);
        @if $i
            and $i + 1 <= length($args)
            and type-of( nth($args, $i + 1) ) == number {
            #{$o}: nth($args, $i + 1);
        }
    }
    position: $position;
}


// Absolute positioning mixin
//==========================================
// @param [list] $args: list of offsets and values
//==========================================
@mixin absolute($args) {
    @include position(absolute, $args);
}

// Arrow mixin
//==========================================
// @param [string] $direction: arrow direction
// @param [list] $position: list of offsets and values
// @param [color] $color (inherit): arrow color
// @param [number] $size (1em): arrow size
//==========================================
@mixin triangle($direction, $position, $color: currentColor, $size: 1em) {
    // Make sure the direction is valid
    @if not index(top right bottom left, $direction) {
        @warn "Direction must be one of top, right, bottom or left.";
    }

    @else {
        @include absolute($position); // Position
        @include square(0); // Size
        content: '';
        z-index: 2;

        border-#{opposite-position($direction)}: $size * 1.5 solid $color;
        $perpendicular-borders: $size solid transparent;

        @if $direction == top or $direction == bottom {
            border-left:   $perpendicular-borders;
            border-right:  $perpendicular-borders;
        }

        @else if $direction == right or $direction == left {
            border-bottom: $perpendicular-borders;
            border-top:    $perpendicular-borders;
        }
    }
}


//== margin - padding has value - not rtl ^.^
//==========================================
@mixin margin($margin, $value){
    margin-#{$margin}: $value !important;
}

@for $i from 1 through 80 {
    @each $margin in top, left, bottom, right {
        .margin-#{$margin}-#{$i}{
            @include margin($margin, 1px * $i)
        }
    }
}

@mixin padding($padding, $value){
    padding-#{$padding}: $value !important;
}

@for $i from 1 through 80 {
    @each $padding in top, left, bottom, right {
        .padding-#{$padding}-#{$i}{
            @include padding($padding, 1px * $i)
        }
    }
}


// Font size - rem
//==========================================

@function parseInt($n) {  
  @return $n / ($n * 0 + 1);
}

@mixin font-size($property, $values) {
  $px : ();  
  $rem: ();  

  @each $value in $values {  

      $unit: unit($value);     
      $val: parseInt($value);  

      @if $unit == "px" or $unit == "" {  
        $px : append($px,  $value);
        $rem: append($rem, ($val / 10 + rem));
      }
      @if $unit == "rem" { 
        $px : append($px,  ($val * 10 + px));
        $rem: append($rem, $value);
      }
  }

 
  #{$property}: $px; 
 
}


//== Border
//==========================================
@mixin border( $coordinates: 0 0 0 0, $colour: $border-color, $style: solid ) {
    $top: nth($coordinates, 1);
    $right: nth($coordinates, 2);
    $bottom: nth($coordinates, 3);
    $left: nth($coordinates, 4);
    @if not(unitless($top)) {
        border-top: $top $style $colour;
    }
    @if not(unitless($right)) {
        border-right: $right $style $colour;
    }
    @if not(unitless($bottom)) {
        border-bottom: $bottom $style $colour;
    }
    @if not(unitless($left)) {
        border-left: $left $style $colour;
    }
}

// State and hover
//==========================================
@mixin state-hover-default($time, $background, $border-color){
    @include transition(all $time);
    &:hover{
      background: $background!important;
      border-color: $border-color!important;
    }
}

@mixin state-hover($time,$height,$color){
    @include box-shadow(inset 0 0 0 0 $color);
    @include transition(all $time cubic-bezier(0.8,0,0,1));
    &:hover{
        @include transition(all $time cubic-bezier(0.8,0,0,1));
        @include box-shadow(inset 0 (-$height) 0 0 $color);
    }
}

@mixin state-hover-2($background){
    position: relative;
    z-index: 10;
    &:after{
        content: "";
        display: block;
        z-index: -50;
        background-color: $background;
        visibility: hidden;
        @include scale(0);
        @include vertical-center(100%,100%);
        @include opacity(0);
        @include transition-delay(0.3s,0s);
        @include transition(transform 0s cubic-bezier(0.19,1,0.22,1) 0.3s,opacity 0.3s cubic-bezier(0.19,1,0.22,1));
    }
    &:hover{
        &:after{
            visibility: visible;
            @include scale(1);
            @include opacity(1);
            @include transition(transform 0.6s cubic-bezier(0.19,1,0.22,1),opacity 0.5s cubic-bezier(0.19,1,0.22,1));
        }
    }
}



//== Flexible Layout
//==========================================

@mixin flexbox {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
}

%flexbox {
    @include flexbox;
}

@mixin inline-flex {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: -moz-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex;
}

%inline-flex {
    @include inline-flex;
}

@mixin flex-direction($value: row) {
    @if $value == row-reverse {
        -webkit-box-direction: reverse;
        -webkit-box-orient: horizontal;
    } @else if $value == column {
        -webkit-box-direction: normal;
        -webkit-box-orient: vertical;
    } @else if $value == column-reverse {
        -webkit-box-direction: reverse;
        -webkit-box-orient: vertical;
    } @else {
        -webkit-box-direction: normal;
        -webkit-box-orient: horizontal;
    }
    -webkit-flex-direction: $value;
    -moz-flex-direction: $value;
    -ms-flex-direction: $value;
    flex-direction: $value;
}
    // Shorter version:
    @mixin flex-dir($args...) { @include flex-direction($args...); }


@mixin flex-wrap($value: nowrap) {
    // No Webkit Box fallback.
    -webkit-flex-wrap: $value;
    -moz-flex-wrap: $value;
    @if $value == nowrap {
        -ms-flex-wrap: none;
    } @else { 
        -ms-flex-wrap: $value; 
    }
    flex-wrap: $value;
}

@mixin flex-flow($values: (row nowrap)) {
    // No Webkit Box fallback.
    -webkit-flex-flow: $values;
    -moz-flex-flow: $values;
    -ms-flex-flow: $values;
    flex-flow: $values;
}

@mixin order($int: 0) {
    -webkit-box-ordinal-group: $int + 1;
    -webkit-order: $int;
    -moz-order: $int;
    -ms-flex-order: $int;
    order: $int;
}

@mixin flex-grow($int: 0) {
    -webkit-box-flex: $int;
    -webkit-flex-grow: $int;
    -moz-flex-grow: $int;
    -ms-flex-positive: $int;
    flex-grow: $int;
}

@mixin flex-shrink($int: 1) {
    -webkit-flex-shrink: $int;
    -moz-flex-shrink: $int;
    -ms-flex-negative: $int;
    flex-shrink: $int;
}

@mixin flex-basis($value: auto) {
    -webkit-flex-basis: $value;
    -moz-flex-basis: $value;
    -ms-flex-preferred-size: $value;
    flex-basis: $value;
}

@mixin flex($fg: 1, $fs: null, $fb: null) {
    
    // Set a variable to be used by box-flex properties
    $fg-boxflex: $fg;

    // Box-Flex only supports a flex-grow value so let's grab the
    // first item in the list and just return that.
    @if type-of($fg) == 'list' {
        $fg-boxflex: nth($fg, 1);
    }

    -webkit-box-flex: $fg-boxflex;
    -webkit-flex: $fg $fs $fb;
    -moz-box-flex: $fg-boxflex;
    -moz-flex: $fg $fs $fb;
    -ms-flex: $fg $fs $fb;
    flex: $fg $fs $fb;
}

@mixin justify-content($value: flex-start) {
    @if $value == flex-start {
        -webkit-box-pack: start;
        -ms-flex-pack: start;
    } @else if $value == flex-end {
        -webkit-box-pack: end;
        -ms-flex-pack: end;
    } @else if $value == space-between {
        -webkit-box-pack: justify;
        -ms-flex-pack: justify;
    } @else if $value == space-around {
        -ms-flex-pack: distribute;      
    } @else {
        -webkit-box-pack: $value;
        -ms-flex-pack: $value;
    }
    -webkit-justify-content: $value;
    -moz-justify-content: $value;
    justify-content: $value;
}
    // Shorter version:
    @mixin flex-just($args...) { @include justify-content($args...); }

//----------------------------------------------------------------------

@mixin align-items($value: stretch) {
    @if $value == flex-start {
        -webkit-box-align: start;
        -ms-flex-align: start;
    } @else if $value == flex-end {
        -webkit-box-align: end;
        -ms-flex-align: end;
    } @else {
        -webkit-box-align: $value;
        -ms-flex-align: $value;
    }
    -webkit-align-items: $value;
    -moz-align-items: $value;
    align-items: $value;
}

@mixin align-self($value: auto) {
    // No Webkit Box Fallback.
    -webkit-align-self: $value;
    -moz-align-self: $value;
    @if $value == flex-start {
        -ms-flex-item-align: start;
    } @else if $value == flex-end {
        -ms-flex-item-align: end;
    } @else {
        -ms-flex-item-align: $value;
    }
    align-self: $value;
}

//----------------------------------------------------------------------

// Flexbox Align Content

@mixin align-content($value: stretch) {
    // No Webkit Box Fallback.
    -webkit-align-content: $value;
    -moz-align-content: $value;
    @if $value == flex-start {
        -ms-flex-line-pack: start;
    } @else if $value == flex-end {
        -ms-flex-line-pack: end;
    } @else {
        -ms-flex-line-pack: $value;
    }
    align-content: $value;
}

@mixin appearance ($value) {
  -webkit-appearance: $value;
     -moz-appearance: $value;
        -ms-appearance: $value;
          appearance: $value;
}

// Retina Sprite Mixins

@mixin retina-sprite-background($url,$position,$width,$height){
    background-repeat: no-repeat;
    background-image: url($url);
    background-position: $position;
    width:$width;
    height:$height;
}
@mixin make-grid-mobile-columns($i: 1, $list: ".col-xs-#{$i}, .col-sm-#{$i}, .col-md-#{$i}, .col-lg-#{$i}") {
  $grid-gutter-mobile-width : 20px;
  @for $i from (1 + 1) through $grid-columns {
    $list: "#{$list}, .col-xs-#{$i}, .col-sm-#{$i}, .col-md-#{$i}, .col-lg-#{$i}";
  }
  #{$list} {
    padding-left:  ($grid-gutter-mobile-width / 2);
    padding-right: ($grid-gutter-mobile-width / 2);
  }
}
@mixin container-mobile-fixed($gutter: $grid-gutter-width) {
  $grid-gutter-mobile-width : 20px;
  padding-left:  ($grid-gutter-mobile-width / 2);
  padding-right: ($grid-gutter-mobile-width / 2);
}
@mixin make-mobile-row($gutter: $grid-gutter-width) {
  $gutter-mobile : 20px;
  margin-left:  ($gutter-mobile / -2);
  margin-right: ($gutter-mobile / -2);
  @include clearfix();
}
@mixin loop-delay($item){
  @for $i from 1 through 10 {
    #{$item}:nth-child(2n+#{$i}) {
      -webkit-transition-delay:#{$i/10}s; /* Safari */
      transition-delay:#{$i/10}s;
    }
  }
}

@-webkit-keyframes updow {
  50% {
    @include translateY(-10px);
  }
  0%, 100% {
    @include translateY(0px);
  }
}

@keyframes updow {
  50% {
    @include translateY(-10px);
  }
  0%, 100% {
    @include translateY(0px);
  }
}

@-webkit-keyframes fadeleft {
  from {
    @include opacity(1);
  }
  to {
    @include opacity(0);
    @include translate(-15px,0);
  }
}
@keyframes fadeleft {
  from {
    @include opacity(1);
  }
  to {
    @include opacity(0);
    @include translate(-15px,0);
  }
}

@-webkit-keyframes faderight {
  from {
    @include opacity(0);
    @include translate(15px,0);
  }
  to {
    @include opacity(1);
    @include translate(0,0);
  }
}
@keyframes faderight {
  from {
    @include opacity(0);
    @include translate(15px,0);
  }
  to {
    @include opacity(1);
    @include translate(0,0);
  }
}

// Gradient Text
@mixin text-gradient($dir, $from1, $from2, $to) {
  background-image: -webkit-linear-gradient($dir, $from1, $from2, $to);
  background-image: linear-gradient($dir, $from1, $from2, $to);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@-webkit-keyframes autofill {
    to {
        color: $text-second;
        background: transparent;
    }
}

@mixin media-query($value) {
    @if(type-of($value) == number) {      
      @media (max-width: $value) { @content; }
    }
}

// Hover Zoom
@mixin hover-zoom() {
  position: relative;      
  overflow: hidden;      
  img {
    max-width: 100%;                
    @include transition-all();
  }   
  &:hover{
    img {                        
      @include scale(1.1);
    }
  } 
  @media (max-width: $screen-sm-max){                 
    img {               
      @include transition(none);     
    }    
    &:hover{
      img {        
        @include scale(1);
      }
    } 
  }       
}

@mixin hover-zoom-bg() {
    position: relative;      
    overflow: hidden;    
    @media (min-width: 1200px) {
        > div{
            @include transition(all .2s);
        }
        &:hover{
            > div{
                @include scale(1.1);
            }
        } 
    }
    @media (min-width: 768px) and (max-width: 1024px) {                 
        > div{
            @include transition(none);     
        }
        &:hover{
            > div{
                @include scale(1);
            }
        } 
    }
}

.hover-zoom{
    @include hover-zoom();
    > *{
        overflow: hidden;
    }
}

.hover-zoom-bg{
    @include hover-zoom-bg();    
}