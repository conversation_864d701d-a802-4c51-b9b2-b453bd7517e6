(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("chart.js"));
	else if(typeof define === 'function' && define.amd)
		define("VueChartJs", ["chart.js"], factory);
	else if(typeof exports === 'object')
		exports["VueChartJs"] = factory(require("chart.js"));
	else
		root["VueChartJs"] = factory(root["Chart"]);
})(typeof self !== 'undefined' ? self : this, function(__WEBPACK_EXTERNAL_MODULE_3__) {
	return  (function(modules) { // webpackBootstrap
		 	// The module cache
		 	var installedModules = {};

		 	// The require function
		 	function __webpack_require__(moduleId) {

			 		// Check if module is in cache
			 		if(installedModules[moduleId]) {
				 			return installedModules[moduleId].exports;
				 		}
			 		// Create a new module (and put it into the cache)
			 		var module = installedModules[moduleId] = {
				 			i: moduleId,
				 			l: false,
				 			exports: {}
				 		};

			 		// Execute the module function
			 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);

			 		// Flag the module as loaded
			 		module.l = true;

			 		// Return the exports of the module
			 		return module.exports;
			 	}


		 	// expose the modules object (__webpack_modules__)
		 	__webpack_require__.m = modules;

		 	// expose the module cache
		 	__webpack_require__.c = installedModules;

		 	// define getter function for harmony exports
		 	__webpack_require__.d = function(exports, name, getter) {
			 		if(!__webpack_require__.o(exports, name)) {
				 			Object.defineProperty(exports, name, {
					 				configurable: false,
					 				enumerable: true,
					 				get: getter
					 			});
				 		}
			 	};

		 	// getDefaultExport function for compatibility with non-harmony modules
		 	__webpack_require__.n = function(module) {
			 		var getter = module && module.__esModule ?
				 			function getDefault() { return module['default']; } :
				 			function getModuleExports() { return module; };
			 		__webpack_require__.d(getter, 'a', getter);
			 		return getter;
			 	};

		 	// Object.prototype.hasOwnProperty.call
		 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };

		 	// __webpack_public_path__
		 	__webpack_require__.p = "";

		 	// Load entry module and return exports
		 	return __webpack_require__(__webpack_require__.s = 0);
		 })
	/************************************************************************/
	 ([
		/* 0 */
		 (function(module, __webpack_exports__, __webpack_require__) {

			"use strict";
			Object.defineProperty(__webpack_exports__, "__esModule", { value: true });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "VueCharts", function() { return VueCharts; });
			/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0__mixins_index_js__ = __webpack_require__(1);
			/* harmony import */ var __WEBPACK_IMPORTED_MODULE_1__BaseCharts__ = __webpack_require__(2);
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Bar", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["a"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "HorizontalBar", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["d"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Doughnut", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["c"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Line", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["e"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Pie", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["f"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "PolarArea", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["g"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Radar", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["h"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Bubble", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["b"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "Scatter", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["i"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "mixins", function() { return __WEBPACK_IMPORTED_MODULE_0__mixins_index_js__["a"]; });
			/* harmony reexport (binding) */ __webpack_require__.d(__webpack_exports__, "generateChart", function() { return __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["j"]; });


			var VueCharts = {
				Bar: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["a" /* Bar */],
				HorizontalBar: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["d" /* HorizontalBar */],
				Doughnut: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["c" /* Doughnut */],
				Line: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["e" /* Line */],
				Pie: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["f" /* Pie */],
				PolarArea: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["g" /* PolarArea */],
				Radar: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["h" /* Radar */],
				Bubble: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["b" /* Bubble */],
				Scatter: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["i" /* Scatter */],
				mixins: __WEBPACK_IMPORTED_MODULE_0__mixins_index_js__["a" /* default */],
				generateChart: __WEBPACK_IMPORTED_MODULE_1__BaseCharts__["j" /* generateChart */],
				render: function render() {
					return console.error('[vue-chartjs]: This is not a vue component. It is the whole object containing all vue components. Please import the named export or access the components over the dot notation. For more info visit https://vue-chartjs.org/#/home?id=quick-start');
				}
			};
			/* harmony default export */ __webpack_exports__["default"] = (VueCharts);


			 }),
		/* 1 */
		 (function(module, __webpack_exports__, __webpack_require__) {

			"use strict";
			/* unused harmony export reactiveData */
			/* unused harmony export reactiveProp */
			function dataHandler(newData, oldData) {
				if (oldData) {
					var chart = this.$data._chart;
					var newDatasetLabels = newData.datasets.map(function (dataset) {
						return dataset.label;
					});
					var oldDatasetLabels = oldData.datasets.map(function (dataset) {
						return dataset.label;
					});
					var oldLabels = JSON.stringify(oldDatasetLabels);
					var newLabels = JSON.stringify(newDatasetLabels);

					if (newLabels === oldLabels && oldData.datasets.length === newData.datasets.length) {
						newData.datasets.forEach(function (dataset, i) {
							var oldDatasetKeys = Object.keys(oldData.datasets[i]);
							var newDatasetKeys = Object.keys(dataset);
							var deletionKeys = oldDatasetKeys.filter(function (key) {
								return key !== '_meta' && newDatasetKeys.indexOf(key) === -1;
							});
							deletionKeys.forEach(function (deletionKey) {
								delete chart.data.datasets[i][deletionKey];
							});

							for (var attribute in dataset) {
								if (dataset.hasOwnProperty(attribute)) {
									chart.data.datasets[i][attribute] = dataset[attribute];
								}
							}
						});

						if (newData.hasOwnProperty('labels')) {
							chart.data.labels = newData.labels;
							this.$emit('labels:update');
						}

						if (newData.hasOwnProperty('xLabels')) {
							chart.data.xLabels = newData.xLabels;
							this.$emit('xlabels:update');
						}

						if (newData.hasOwnProperty('yLabels')) {
							chart.data.yLabels = newData.yLabels;
							this.$emit('ylabels:update');
						}

						chart.update();
						this.$emit('chart:update');
					} else {
						if (chart) {
							chart.destroy();
							this.$emit('chart:destroy');
						}

						this.renderChart(this.chartData, this.options);
						this.$emit('chart:render');
					}
				} else {
					if (this.$data._chart) {
						this.$data._chart.destroy();

						this.$emit('chart:destroy');
					}

					this.renderChart(this.chartData, this.options);
					this.$emit('chart:render');
				}
			}

			var reactiveData = {
				data: function data() {
					return {
						chartData: null
					};
				},
				watch: {
					'chartData': dataHandler
				}
			};
			var reactiveProp = {
				props: {
					chartData: {
						type: Object,
						required: true,
						default: function _default() {}
					}
				},
				watch: {
					'chartData': dataHandler
				}
			};
			/* harmony default export */ __webpack_exports__["a"] = ({
				reactiveData: reactiveData,
				reactiveProp: reactiveProp
			});

			 }),
		/* 2 */
		 (function(module, __webpack_exports__, __webpack_require__) {

			"use strict";
			/* harmony export (immutable) */ __webpack_exports__["j"] = generateChart;
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "a", function() { return Bar; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "d", function() { return HorizontalBar; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "c", function() { return Doughnut; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "e", function() { return Line; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "f", function() { return Pie; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "g", function() { return PolarArea; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "h", function() { return Radar; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "b", function() { return Bubble; });
			/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "i", function() { return Scatter; });
			/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_chart_js__ = __webpack_require__(3);
			/* harmony import */ var __WEBPACK_IMPORTED_MODULE_0_chart_js___default = __webpack_require__.n(__WEBPACK_IMPORTED_MODULE_0_chart_js__);

			function generateChart(chartId, chartType) {
				return {
					render: function render(createElement) {
						return createElement('div', {
							style: this.styles,
							class: this.cssClasses
						}, [createElement('canvas', {
							attrs: {
								id: this.chartId,
								width: this.width,
								height: this.height
							},
							ref: 'canvas'
						})]);
					},
					props: {
						chartId: {
							default: chartId,
							type: String
						},
						width: {
							default: 400,
							type: Number
						},
						height: {
							default: 400,
							type: Number
						},
						cssClasses: {
							type: String,
							default: ''
						},
						styles: {
							type: Object
						},
						plugins: {
							type: Array,
							default: function _default() {
								return [];
							}
						}
					},
					data: function data() {
						return {
							_chart: null,
							_plugins: this.plugins
						};
					},
					methods: {
						addPlugin: function addPlugin(plugin) {
							this.$data._plugins.push(plugin);
						},
						generateLegend: function generateLegend() {
							if (this.$data._chart) {
								return this.$data._chart.generateLegend();
							}
						},
						renderChart: function renderChart(data, options) {
							if (this.$data._chart) this.$data._chart.destroy();
							this.$data._chart = new __WEBPACK_IMPORTED_MODULE_0_chart_js___default.a(this.$refs.canvas.getContext('2d'), {
								type: chartType,
								data: data,
								options: options,
								plugins: this.$data._plugins
							});
						}
					},
					beforeDestroy: function beforeDestroy() {
						if (this.$data._chart) {
							this.$data._chart.destroy();
						}
					}
				};
			}
			var Bar = generateChart('bar-chart', 'bar');
			var HorizontalBar = generateChart('horizontalbar-chart', 'horizontalBar');
			var Doughnut = generateChart('doughnut-chart', 'doughnut');
			var Line = generateChart('line-chart', 'line');
			var Pie = generateChart('pie-chart', 'pie');
			var PolarArea = generateChart('polar-chart', 'polarArea');
			var Radar = generateChart('radar-chart', 'radar');
			var Bubble = generateChart('bubble-chart', 'bubble');
			var Scatter = generateChart('scatter-chart', 'scatter');
			/* unused harmony default export */ var _unused_webpack_default_export = ({
				Bar: Bar,
				HorizontalBar: HorizontalBar,
				Doughnut: Doughnut,
				Line: Line,
				Pie: Pie,
				PolarArea: PolarArea,
				Radar: Radar,
				Bubble: Bubble,
				Scatter: Scatter
			});

			 }),
		/* 3 */
		 (function(module, exports) {

			module.exports = __WEBPACK_EXTERNAL_MODULE_3__;

			 })
		 ]);
});