{"name": "automattic/jetpack-logo", "description": "A logo for Jetpack", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {}, "require-dev": {"yoast/phpunit-polyfills": "1.0.4", "automattic/jetpack-changelogger": "^3.3.2"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-logo", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-logo/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.6.x-dev"}}}