<?php return array(
    'root' => array(
        'name' => 'woocommerce/payments',
        'pretty_version' => 'dev-release/6.2.2',
        'version' => 'dev-release/6.2.2',
        'reference' => 'e09300d5da1be51c1f4e628f98a5889f3259ae07',
        'type' => 'wordpress-plugin',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => false,
    ),
    'versions' => array(
        'automattic/jetpack-a8c-mc-stats' => array(
            'pretty_version' => 'v1.4.20',
            'version' => '1.4.20.0',
            'reference' => '6743d34fe7556455e17cbe1b7c90ed39a1f69089',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-a8c-mc-stats',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-admin-ui' => array(
            'pretty_version' => 'v0.2.20',
            'version' => '0.2.20.0',
            'reference' => '90f4de6c9d936bbf161f1c2356d98b00ba33576f',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-admin-ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-assets' => array(
            'pretty_version' => 'v1.18.4',
            'version' => '1.18.4.0',
            'reference' => 'fbb76cd3d88ef31ba65d3fa0d3bfed155c016d05',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-assets',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-autoloader' => array(
            'pretty_version' => 'v2.11.18',
            'version' => '2.11.18.0',
            'reference' => '53cbf0528fa6931c4fa6465bccd37514f9eda720',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../automattic/jetpack-autoloader',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-config' => array(
            'pretty_version' => 'v1.15.2',
            'version' => '1.15.2.0',
            'reference' => 'f1fa6e24a89192336a1499968bf8c68e173b6e34',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-connection' => array(
            'pretty_version' => 'v1.51.7',
            'version' => '1.51.7.0',
            'reference' => '4c4bae836858957d9aaf6854cf4e24c3261242c4',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-connection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-constants' => array(
            'pretty_version' => 'v1.6.22',
            'version' => '1.6.22.0',
            'reference' => '7b5c44d763c7b0dd7498be2b41a89bfefe84834c',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-constants',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-identity-crisis' => array(
            'pretty_version' => 'v0.8.43',
            'version' => '********',
            'reference' => '8a01e7ed271544d354c2192f575a6fe7dc2ba4d3',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-identity-crisis',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-ip' => array(
            'pretty_version' => 'v0.1.4',
            'version' => '*******',
            'reference' => 'fde10bea279aca8adbae9d7ae27d971da3a932e3',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-ip',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-logo' => array(
            'pretty_version' => 'v1.6.1',
            'version' => '*******',
            'reference' => '6a7b9e5602ca81c207e573dfed9e4fc1dd6a279b',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-logo',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-password-checker' => array(
            'pretty_version' => 'v0.2.13',
            'version' => '********',
            'reference' => '16b88d370ca2f59b38e6c44bc37fc43e72090dad',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-password-checker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-redirect' => array(
            'pretty_version' => 'v1.7.25',
            'version' => '1.7.25.0',
            'reference' => '67d7dce123d4af4fec4b4fe15e99aaad85308314',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-redirect',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-roles' => array(
            'pretty_version' => 'v1.4.23',
            'version' => '1.4.23.0',
            'reference' => 'f147b3e8061fc0de2a892ddc4f4156eb995545f9',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-roles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-status' => array(
            'pretty_version' => 'v1.17.1',
            'version' => '1.17.1.0',
            'reference' => '0032ee4bce1d4644722ba46858c702a0afa76cff',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-status',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'automattic/jetpack-sync' => array(
            'pretty_version' => 'v1.47.7',
            'version' => '1.47.7.0',
            'reference' => 'd37f35bf8bf43ab1e1c665af2831e30814354d27',
            'type' => 'jetpack-library',
            'install_path' => __DIR__ . '/../automattic/jetpack-sync',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v1.10.0',
            'version' => '1.10.0.0',
            'reference' => '1a0357fccad9d1cc1ea0c9a05b8847fbccccb78d',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'roundcube/plugin-installer' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'shama/baton' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'woocommerce/payments' => array(
            'pretty_version' => 'dev-release/6.2.2',
            'version' => 'dev-release/6.2.2',
            'reference' => 'e09300d5da1be51c1f4e628f98a5889f3259ae07',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'woocommerce/subscriptions-core' => array(
            'pretty_version' => '6.0.0',
            'version' => '*******',
            'reference' => 'b48c46a6a08b73d8afc7a0e686173c5b5c55ecbb',
            'type' => 'wordpress-plugin',
            'install_path' => __DIR__ . '/../woocommerce/subscriptions-core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
