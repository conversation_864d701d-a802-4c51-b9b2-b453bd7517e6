{"name": "automattic/jetpack-roles", "description": "Utilities, related with user roles and capabilities.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {}, "require-dev": {"brain/monkey": "2.6.1", "yoast/phpunit-polyfills": "1.0.4", "automattic/jetpack-changelogger": "^3.3.2"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-roles", "changelogger": {"link-template": "https://github.com/Automattic/jetpack-roles/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.4.x-dev"}}}