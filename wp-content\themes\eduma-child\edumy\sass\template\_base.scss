ol,ul{
    padding-left: 18px;
}
ul>li, ol>li, dl>li{
    margin-bottom: 5px;
}

code, kbd, tt, var {
    font-family: Monaco,Consolas,"Andale Mono","DejaVu Sans Mono",monospace;
    font-size: 15px; 
    padding: 2px 4px;
    background: #e3e7e8;
    color: $text-color;
}

figcaption {
  margin-top: 15px !important;
  display: block;
}

table > thead > tr > th, 
table > thead > tr > td, 
.table-bordered > thead > tr > th, 
.table-bordered > thead > tr > td{
    border-bottom-width: 1px;
}

.body-display-mode-list{
    background-color: #f9fafc;
    .sidebar .widget, 
    .apus-sidebar .widget{
        border: 0;
        padding: 0;
    }
}

.page{
    .apus-footer{
        &.footer-default-wrapper{
            margin-top: 60px;
        }
    }
}

#main-container{    
    padding-top: 0;
    padding-bottom: 0;
    &.main-content{
        padding-bottom: 60px;
    }
    &.home-page-default{
        padding-top: 60px;                
    }
}

.elementor-button-icon{
    [class^="flaticon-"]:before, 
    [class*=" flaticon-"]:before, 
    [class^="flaticon-"]:after, 
    [class*=" flaticon-"]:after{
        margin-left: 5px;
    }
}

.video-container {
    padding-top: 0;
    margin: 0 0 60px 0;
    position: relative;
    padding-bottom: 56.25%;
    padding-top: 25px;
    height: 0;
    iframe{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
.wp-block-embed{
    .video-container{
        position: relative;
        padding-bottom: 56.25%;
        padding-top: 25px;
        height: 0;
        iframe{
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
    }
} 

//---------------------------------
// logo

.logo{
    &.logo-college{
        max-width: 200px;
    }
    &.logo-kindergarten{
        max-width: 316px;
    }
    &.footer-logo-university{
        width: 279px;
        max-width: 279px;
        position: relative;        
        &:after{
            content: "";
            background: #383838;                        
            display: block;
            @include vertical-align(absolute);
            @include rtl-right(-30px);
            @include size(1px, 40px);            
        }
    }
    &.footer-logo-college{
        max-width: 246px;
        position: relative;
        &:after{
            content: "";
            background: #383838;                        
            display: block;
            @include vertical-align(absolute);
            @include rtl-right(-30px);
            @include size(1px, 40px);            
        }
    }
    &.footer-logo-kindergarten{
        max-width: 316px;
        margin: 0 auto;
    }
}

.mfp-container {
    overflow: hidden;
}

a:hover, a:focus{
    outline: none;
    color: $theme-color;
}

cite{
    display: inline-block;
    font-size: 18px;
    color: $text-color;
    &:before{
        content: '\2014 \0020';
    }
}

fieldset {
    clear: both;
    overflow: hidden;
}

.woocommerce{
    input.button {
        outline: none;
    }
} 

.logo {
    &.custom-logo {      
        width: 143px;  
        position: relative;
        &.footer-logo{
            position: relative;
            width: 173px;
            @include rtl-margin-right(15px);
            @include rtl-padding-right(30px);
            &:after{
                content: '';                
                background-color: #383838;
                @include size(1px,40px);               
                @include vertical-align(absolute);
                @include rtl-right(0);
            }
        }
    }
    &.custom-logo-2{
        width: 276px;
    }
    .transparent-logo{
        display: none;
    }
    .logo-main{
        display: block;
    }
}

.site-logo {
    display: inline-block;
    line-height: 1;
    font-size: 33px;
    font-weight: 500;
    margin: 0;
    padding: 0;

    a {
        display: inline-block;
        color: #000 !important;
        line-height: 1;
    }
}

.logo-theme {
    width: 143px;
    img {
        outline: none;
    }
}

.post-password-form {
    input {
        height: 43px;
        padding: 5px 10px;
        &[type="submit"] {
            background: $theme-color;
            color: $white;
            border: none;
            padding: 5px 25px;
            height: 50px;
            margin: -5px 0 0 0;
            &:hover,
            &:active {
                color: $white;
                background: darken($theme-color, 10%);
            }
        }
    }
}

.btn-search-top {
    cursor: pointer;
    display: inline-block;
}

.input-group {
    .btn {
        border-width: 1px;
    }
}

a:focus,
.btn:focus {
    outline: none !important;
}

.list,
.list-no {
    list-style: none;
    padding: 0;
    margin: 0;
}

.media-body {
    width: 10000px;
}

.btn,
button {
    outline: none !important;
}

@include selection($white, $theme-color-second);

.pswp__item {
    cursor: move;
}

.no-border {
    border: none !important;

    &:before {
        display: none !important;
    }
}

.wpb_widgetised_column {
    margin: 0;
}

.topmenu-menu-line {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
        display: inline-block;
        vertical-align: middle;

        .space {
            margin: 0 3px;
        }
    }
}

// top menu mobile
.top-menu-mobile {
    .title {
        font-size: 20px;
        padding: 0 15px;
        margin: 0 0 15px;
    }

    .navbar-nav>li>a {
        padding: 2px 15px;
    }
}

// quick view

.mfp-content{    
    margin: 0;    
    height: 100%;
}

.slick-slide{
    outline: none;
    .slick-slide-inner{
        outline: none;
    }
}

.apus-quickview .mfp-inline-holder .mfp-content {
    position: relative;
    max-width: 100%;
    width: 90%;
    margin: 0 auto;
    background: $white;

    @media(min-width: 1200px) {
        width: 1200px;
        min-height: 400px;
    }

    .details-product {
        padding: 35px 20px 20px;
        overflow: hidden;
        margin: 0 !important;

        @media(min-width: 992px) {
            padding: 0;
        }
    }

    .woocommerce-product-details__short-description-wrapper {
        overflow: auto;
    }

    .information-wrapper {
        padding-top: 30px;
        padding-bottom: 30px;
        overflow: hidden;
    }

    @media(min-width: 992px) {
        .wrapper-img-main {
            @include rtl-base-toprightbottomleft(padding, $theme-margin, 0, $theme-margin, $theme-margin);
        }

        .information {
            @include rtl-base-toprightbottomleft(padding, 0, $theme-margin, 0, 0);
        }
    }

    @media(max-width: 767px) {
        .details-product .information {
            padding-top: 10px;
        }
    }

    // button close
    .mfp-close {
        background: transparent !important;
        color: $text-color;
        font-size: 30px;
        @include transition(all 0.2s ease-in-out 0s);

        &:hover,
        &:active {
            color: $brand-danger;
        }
    }

    .wrapper-thumbs {
        margin-top: 16px;
    }

    .slick-carousel {
        margin-left: -8px;
        margin-right: -8px;

        .slick-slide {
            padding-left: 8px;
            padding-right: 8px;
        }
    }
}

.action {
    .caret {
        @include size(8px, 8px);
        position: relative;
    }

    &[aria-expanded="true"] {
        b {
            &:before {
                font-family: FontAwesome;
                content: "\f00d";
                position: absolute;
                top: 0;
                left: 0;
                font-size: 12px;
            }

            border:none;
        }
    }
}

ins {
    text-decoration: none;
}

img {
    border: 0;
    vertical-align: top;
    max-width: 100%;
    height: auto;
}

.video-responsive {
    height: 0;
    padding-top: 0;
    padding-bottom: 56.25%;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;

    embed,
    iframe,
    object,
    video {
        top: 0;
        left: 0;
        position: absolute;
        @include square(percentage(1));
    }
}

.audio-responsive {
    iframe {
        @include size(percentage(1), 126px);
    }
}

ul.list-square {
    padding: 0;
    margin: 0;
    list-style: none;

    >li {
        line-height: 35px;
        font-size: 14px;
        margin: 0;

        &.active,
        &:hover {
            >a {
                color: $theme-color;

                &:before {
                    background: $theme-color;
                }
            }
        }

        >a {
            display: block;
            @include rtl-padding-left(21px);
            position: relative;

            &:before {
                content: '';
                background: $link-color;
                @include size(8px, 8px);
                @include rtl-left(0);
                position: absolute;
                top: 50%;
                @include translateY(-50%);
            }
        }
    }
}

// breadcrumb
.breadcrumb>a+li:before,
.breadcrumb>li+a:before,
.breadcrumb>li+li:before {
    color: $white;
    padding: 0px;
}

// saerch
.search-form {
    input,
    .btn {
        background: #ebedee;
        border-color: #ebedee;
        color: $link-color;
    }
    .btn {
        padding: $padding-base-vertical 15px;
    }
}

.ui-autocomplete.ui-widget-content {
    padding: 15px;
    margin: 0;
    list-style: none;
    width: 293px !important;
    background: $white;

    li {
        padding-bottom: 15px;
        margin-bottom: 15px;
        border-bottom: 1px solid $border-color;

        &:last-child {
            border: none;
            margin: 0;
            padding: 0;
        }

        img {
            width: 60px;
        }
    }
}

.main-content {
    display: block;
    padding-bottom: 60px;
    padding-top: 60px;
}

.bg-dark {
    background: #272727;
    color: $white;
}

.text-purple {
    color: #6c58bd !important;
}

.text-red {
    color: #c32322 !important;
}

.text-red-1 {
    color: #ff0000 !important;
}

.text-orange {
    color: #f66000 !important;
}

.text-yellow {
    color: #e2d951 !important;
}

.text-black {
    color: #000 !important;
}

.text-white,
.text-light {
    color: $white !important;
}

.text-darker {
    color: #333 !important;
}

.text-uppercase {
    text-transform: uppercase;
}

/*Radius
------------------------------------*/
.radius-0 {
    border-radius: 0 !important;
}

.radius-x {
    border-radius: 50% !important;
}

.radius-1x {
    border-radius: 10px !important;
}

.radius-2x {
    border-radius: 20px !important;
}

.radius-3x {
    border-radius: 30px !important;
}

.radius-4x {
    border-radius: 40px !important;
}

.radius-5x {
    border-radius: 5px !important;
}

.radius-6x {
    border-radius: 3px !important;
}

.owl-carousel-play {
    position: relative;

    .owl-item {
        &>div {}

        &:last-child .product-wrapper {
            border: none
        }
    }
}

.carousel-md {
    @include size($bo-carousel-md-width, $bo-carousel-md-height);
    line-height: $bo-carousel-md-height;
}

.carousel-sm {
    @include size($bo-carousel-sm-width, $bo-carousel-sm-height);
    line-height: $bo-carousel-sm-height;
    font-size: 14px;
}

.carousel-xs {
    @include size($bo-carousel-xs-width, $bo-carousel-xs-height);
    line-height: $bo-carousel-xs-height;
    font-size: 14px;
}

//back to top
.add-fix-top {
    position: fixed;
    z-index: 9;
    bottom: 50px;
    text-align: center;      
    border: 0;  
    color: $white !important;
    visibility: hidden;
    background-color: $theme-color;
    @include border-radius(50px);    
    @include rtl-right(70px);
    @include transition-all();
    @include translateX(100%);    
    @include flexbox();
    @include align-items(center);
    @include justify-content(center);
    @include square(45px);  
    @include opacity(0);     
    @include hover-focus-active() {
        @include opacity(1);
    }         
    [class*="icon"] {
        &:before,&:after{
            margin: 0;
            font-size: 18px;
            color: $white;
        }
    }
    &.active {
        visibility: visible;
        @include translateX(0);        
        @include opacity(1);
        @include hover-focus-active() {
            color: $white !important;
            @include opacity(1);
            visibility: visible;
        }
    }
}

.menu {
    padding: 0;
    margin: 0;

    li {
        list-style: none;
        margin-bottom: 8px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    ul {
        @include rtl-padding-left(15px);
        margin: 0;
    }
}

.sidebar{
    .menu {       
        li {            
            line-height: 40px;
            margin-bottom: 0;
            a{
                color: $text-second;
                font-size: 15px;                
                &:hover{
                    color: $theme-color;
                }            
            }            
        }        
    }
}

// loading
.apus-body-loading {
    overflow: hidden;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes -webkit-spin {
    0% {
        -webkit-transform: rotate(0deg);
    }

    100% {
        -webkit-transform: rotate(360deg);
    }
}

.apus-page-loading {
    top: 0;
    left: 0;
    position: fixed;
    @include size(100%, 100%);
    background: $white;
    z-index: 9999;
}

.apus-loader-inner {
    margin: 0 auto;
    @include size(150px, 75px);
    text-align: center;
    font-size: 10px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateY(-50%) translateX(-50%);
    transform: translateY(-50%) translateX(-50%);
    background-size: cover;
    background-repeat: no-repeat;

    >div {
        @include size(8px, 100%);
        display: inline-block;
        float: left;
        margin-left: 2px;
        -webkit-animation: delay 0.8s infinite ease-in-out;
        animation: delay 0.8s infinite ease-in-out;
    }

    .loader1 {
        background-color: #e39505;
    }

    .loader2 {
        background-color: #ff5395;
        -webkit-animation-delay: -0.7s;
        animation-delay: -0.7s;
    }

    .loader3 {
        background-color: #84b813;
        -webkit-animation-delay: -0.6s;
        animation-delay: -0.6s;
    }

    .loader4 {
        background-color: #f38ca3;
        -webkit-animation-delay: -0.5s;
        animation-delay: -0.5s;
    }

    .loader5 {
        background-color: #da5800;
        -webkit-animation-delay: -0.4s;
        animation-delay: -0.4s;
    }
}

@-webkit-keyframes delay {

    0%,
    40%,
    100% {
        -webkit-transform: scaleY(0.05);
        transform: scaleY(0.05);
    }

    20% {
        -webkit-transform: scaleY(1);
        transform: scaleY(1);
    }
}

@keyframes delay {

    0%,
    40%,
    100% {
        transform: scaleY(0.05);
        -webkit-transform: scaleY(0.05);
    }

    20% {
        transform: scaleY(1);
        -webkit-transform: scaleY(1);
    }
}

// tab version
.tab-v1 {
    .tabs-list {
        border: none;
        text-align: center;
        padding: 30px 0 50px;

        li {
            padding: 0 22px;
            margin: 0;
            display: inline-block;
            float: none;

            a {
                padding: 5px 0;
                color: #777777;
                border-width: 0 0 1px !important;
                border-color: #cccccc;
                border-style: dashed;
                font-size: 16px;
                font-weight: 300;
                text-transform: uppercase;
                background: transparent;
            }

            &.active,
            &:hover {
                >a:active,
                >a:focus,
                >a {
                    border-color: transparent;
                    color: $theme-color;
                    background: transparent;
                }
            }
        }
    }

    &.style1 {
        .tabs-list {
            padding: 10px 0 70px;
            @include rtl-text-align-right();

            li {

                &.active,
                &:hover {
                    @include box-shadow(0 5px 5px 0 rgba(0, 0, 0, 0.1));

                    >a:active,
                    >a:focus,
                    >a {
                        color: $link-color;
                    }
                }
            }
        }
    }

    @media(min-width:992px) {
        &.style1 {
            padding: 0 70px;
        }
    }
}

.tab-product-center {
    .nav-tabs {
        border: none;
        margin: 0 0 30px;
        text-align: center;

        >li {
            display: inline-block;
            float: none;
            margin: 0 !important;

            >a {
                border: none !important;
                margin: 0;
                font-size: 16px;
                font-weight: 500;
                padding: 0 30px;
                color: $link-color;
                outline: none !important;
            }

            &:hover,
            &.active {
                a {
                    color: $theme-color;
                }
            }
        }
    }

    .tab-content {
        position: relative;

        &.loading:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            z-index: 99;
            @include size(100%, 100%);
            background:rgba(255, 255, 255, 0.9) url(#{$image-theme-path}loading-quick.gif) no-repeat scroll center 100px / 50px auto;
        }
    }
}

// pagination
.page-links {
    overflow: hidden;
    margin: 0 0 30px;
    .page-links-title {
        font-weight: normal;
        color: $headings-color;
    }
    > span:not(.page-links-title),
    > a {        
        line-height: 45px;     
        margin: 0 8px;
        text-align: center;
        padding: 0;
        border: 0;
        color: $text-color;
        @include inline-block();
        @include square(45px);
        @include border-radius(50px);
        @include transition-all();
        &:hover,
        &:active {
            color: $white;
            background: $theme-color;
            border-color: $theme-color;
        }
    }
    > span:not(.page-links-title) {
        color: $white;
        background: $theme-color;
        border-color: $theme-color;
    }
}

option {
    padding: 5px;
}

@media(min-width:992px) {
    .space-padding-left-30 {
        padding-left: 30px !important;
    }
}

// account
.woocommerce-MyAccount-navigation {
    >ul {
        list-style: none;
        padding: 0;
        margin: 0;
        line-height: 35px;
        text-align: center;
    }
}

.woocommerce-error, 
.woocommerce-info, 
.woocommerce-message{
    line-height: 43px;
}

// overide of compare
.yith_woocompare_colorbox #cboxWrapper {

    #cboxMiddleLeft,
    #cboxMiddleRight,
    #cboxTopLeft,
    #cboxTopCenter,
    #cboxTopRight,
    #cboxBottomLeft,
    #cboxBottomCenter,
    #cboxBottomRight {}

    #cboxContent {
        position: relative;

        #cboxClose {
            position: absolute;
            top: -5px;
            @include rtl-right(-5px);
            padding: 5px;
            color: $white;
            background: $brand-danger;
            text-transform: capitalize;
            font-size: 14px;
            @include size(80px, 36px);
            text-indent: 0;
            @include transition(all 0.2s ease-in-out 0s);

            &:hover,
            &:active {
                background: darken($brand-danger, 5%);
            }
        }
    }
}

// loading imgaes
.image-lazy-loading .image-wrapper {
    background-position: center center;
    background-repeat: no-repeat;
    background-image: url("data:image/svg+xml,%3Csvg xmlns=\"http://www.w3.org/2000/svg\" width=\"38\" height=\"38\" viewBox=\"0 0 38 38\" stroke=\"rgba(102,102,102,0.25)\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg transform=\"translate(1 1)\" stroke-width=\"2\"%3E%3Ccircle stroke-opacity=\".55\" cx=\"18\" cy=\"18\" r=\"18\"/%3E%3Cpath d=\"M36 18c0-9.94-8.06-18-18-18\"%3E%3CanimateTransform attributeName=\"transform\" type=\"rotate\" from=\"0 18 18\" to=\"360 18 18\" dur=\"1s\" repeatCount=\"indefinite\"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    max-height: 100%;

    img {
        @include opacity(0);
        @include transition(all 0.3s ease-in-out 0s);
    }

    &.image-loaded {
        background: none;

        img {
            @include opacity(1);
        }
    }
}

.scrollbar {
    @include size(102px, 102px);
    @include border-radius(50%);
    overflow: hidden;
    line-height: 90px;
    text-align: center;
    color: $white;
    @include gradient-horizontal($theme-color-second, $theme-color);
    font-size: 50px;
    display: inline-block;
    border: 6px solid $white;
    @include translateY(55px);
    position: relative;
    z-index: 9;

    &:hover,
    &:active {
        color: $white;
        @include gradient-horizontal($theme-color, $theme-color-second);
    }
}

.rtl {
    .navbar-nav {
        float: right;
    }

    .apus-topcart {
        .dropdown-menu-right {
            right: auto;
            left: 0;
        }
    }

    .dropdown-menu {
        text-align: right;
    }  

    .main-search {
        .media-left,
        .media>.pull-left {
            padding: 0 0 0 15px;
        }
    }

    .widget-process .proces-item .line-space {
        left: inherit;
        right: 100%;
        @include translateX(50%);
    }

    .form-edumy-ppp select,
    .apus-filter select {
        background-position: left 10px center;
    }
}

// wpml
.show-search {
    cursor: pointer;
    line-height: 1;
    font-size: 16px;

    &:hover,
    &:active,
    &:focus {
        color: $theme-color;
    }
}

// style for language
.apus-header {
    .wpml-ls-legacy-dropdown a.wpml-ls-item-toggle {
        border: none !important;
        padding: 4px 25px 6px 0;
        background: transparent !important;
        color: $topbar-link-color !important;
    }

    .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
        background: $white;
        border: none;
        border: 1px solid $border-color;
        min-width: 114px;

        li {
            border-bottom: 1px solid $border-color;
            padding: 9px 10px;

            a {
                border: none !important;
                background: transparent !important;
                padding: 0;
                color: $link-color;

                &:hover,
                &:focus {
                    color: $link-hover-color;
                }
            }

            &:last-child {
                border: none;
            }
        }
    }

    .wpml-ls-legacy-dropdown {
        width: auto;
    }

    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency,
    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency {
        padding: 0;
        border: none;
        background: transparent;
    }

    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency {
        padding: 5px 10px;
        border-bottom: 1px solid $border-color;

        &:last-child {
            border: none;
        }
    }

    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency {
        font-weight: 400;
        color: $topbar-link-color;
    }

    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency a:hover,
    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency .wmc-currency.active a {
        font-weight: 400;
        color: $theme-color;
    }

    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper .wmc-sub-currency {
        min-width: 80px;
        text-align: inherit;
        z-index: 99;
    }

    .woo-multi-currency.shortcode.plain-vertical .wmc-currency-wrapper span.wmc-current-currency::after {
        font-size: 11px;
    }

    .apus-topbar {
        .wpml-ls-legacy-dropdown .wpml-ls-sub-menu {
            min-width: 155px;
        }
    }
}

.search-header {
    position: fixed;
    z-index: 9;
    @include opacity(0);
    visibility: hidden;
    top: 0;
    left: 0;
    @include size(100%, 0);
    background: #f7f7f7;
    @include transition(all 0.2s ease-in-out 0s);

    &.active {
        @include opacity(1);
        height: 100%;
        visibility: visible;
    }
}

.social-top {
    list-style: none;
    padding: 0 30px;

    li {
        display: inline-block;
        @include rtl-margin-right(10px);

        &:last-child {
            margin: 0;
        }
    }
}

.apus-widget-text-editor{
    > li{
        text-align: center;
        font-size: 14px;
        line-height: 30px;
        a{
            outline: none;
        }
    }
}

// search
.select2-container {
    outline: none !important;
}

.select2-search--dropdown {
    padding: 10px 15px;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border-color: $border-color;
    @include border-radius(4px);
    height: 33px;
    font-size: 14px;
    padding: 5px 10px;
}

.select2-results__option {
    padding: 5px 10px;
    font-size: 14px;
    outline: none !important;
}

.select2-dropdown {
    border: none;
    margin-top: 1px;
    @include box-shadow(0 0 2px 0 rgba(0, 0, 0, 0.3));
}

.select2-results {
    padding: 0 15px 15px;
}

abbr, abbr[title] {
    border-bottom: 1px dotted $headings-color;
    cursor: help;
    text-decoration: none;
}

.widget_meta{
    li abbr {
        border-bottom: 1px dashed;
    }
} 

.space-bottom-10{
    margin-bottom: 10px !important;
}

.space-bottom-20{
    margin-bottom: 20px !important;
}

.space-bottom-30{
    margin-bottom: 30px !important;
}

.space-bottom-40{
    margin-bottom: 40px !important;
}

.space-bottom-50{
    margin-bottom: 50px !important;
}

.social-share{
    .label{
        display: none;
    }
}

.mfp-bg{
    background-color: $black !important;
}

.elementor-widget-text-editor{
    .wpcf7-form{    
        max-width: 582px;
        width: 582px;
        margin: 0;
        .pcf7-form-contact{
            label{
                cursor: pointer;
                color: $headings-color;
                font-size: 14px;
                font-weight: 400;
                margin-bottom: 10px;
                @include inline-block();
            }
            .wpcf7-submit{
                background-color: transparent;
                border: 2px solid $theme-color;                
                color: $theme-color;
                margin: 10px 0 0 0;
                @include border-radius(50px);                
            }              
            textarea,
            input[type='text'],
            input[type='email'],
            input[type='password']{
                cursor: pointer;
                padding: 8px 20px;
                color: $input-color;
                border: 1px solid $border-color;
                @include border-radius(5px);                
                @include placeholder($headings-color);                
            }
            textarea{
                padding: 20px;
                max-height: 120px;
            } 
            .form-group{
                margin-bottom: 20px;
            }            
        }    
    }
}

.wp-video, 
video.wp-video-shortcode, 
.mejs-container, 
.mejs-overlay.load {
    width: 100% !important;
    height: 100% !important;
}
.mejs-container {
    padding-top: 56.25%;
}
.wp-video, 
video.wp-video-shortcode {
    max-width: 100% !important;
}
video.wp-video-shortcode {
    position: relative;
}
.mejs-mediaelement {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
.mejs-controls {
    display: none;
}
.mejs-overlay-play {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: auto !important;
    height: auto !important;
}