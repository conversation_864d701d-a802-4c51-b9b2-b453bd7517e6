/*!
  * OpenLayers v6.15.1 (https://openlayers.org/)
  * Copyright 2005-present, OpenLayers Contributors All rights reserved.
  * Licensed under BSD 2-Clause License (https://github.com/openlayers/openlayers/blob/main/LICENSE.md)
  *
  * @license BSD-2-Clause
  */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.ol=e():t.ol=e()}(self,(function(){return function(){var t={582:function(t){t.exports=function(){"use strict";function t(t,n,o,r,s){!function t(i,n,o,r,s){for(;r>o;){if(r-o>600){var a=r-o+1,l=n-o+1,h=Math.log(a),u=.5*Math.exp(2*h/3),c=.5*Math.sqrt(h*u*(a-u)/a)*(l-a/2<0?-1:1);t(i,n,Math.max(o,Math.floor(n-l*u/a+c)),Math.min(r,Math.floor(n+(a-l)*u/a+c)),s)}var p=i[n],f=o,d=r;for(e(i,o,n),s(i[r],p)>0&&e(i,o,r);f<d;){for(e(i,f,d),f++,d--;s(i[f],p)<0;)f++;for(;s(i[d],p)>0;)d--}0===s(i[o],p)?e(i,o,d):e(i,++d,r),d<=n&&(o=d+1),n<=d&&(r=d-1)}}(t,n,o||0,r||t.length-1,s||i)}function e(t,e,i){var n=t[e];t[e]=t[i],t[i]=n}function i(t,e){return t<e?-1:t>e?1:0}var n=function(t){void 0===t&&(t=9),this._maxEntries=Math.max(4,t),this._minEntries=Math.max(2,Math.ceil(.4*this._maxEntries)),this.clear()};function o(t,e,i){if(!i)return e.indexOf(t);for(var n=0;n<e.length;n++)if(i(t,e[n]))return n;return-1}function r(t,e){s(t,0,t.children.length,e,t)}function s(t,e,i,n,o){o||(o=d(null)),o.minX=1/0,o.minY=1/0,o.maxX=-1/0,o.maxY=-1/0;for(var r=e;r<i;r++){var s=t.children[r];a(o,t.leaf?n(s):s)}return o}function a(t,e){return t.minX=Math.min(t.minX,e.minX),t.minY=Math.min(t.minY,e.minY),t.maxX=Math.max(t.maxX,e.maxX),t.maxY=Math.max(t.maxY,e.maxY),t}function l(t,e){return t.minX-e.minX}function h(t,e){return t.minY-e.minY}function u(t){return(t.maxX-t.minX)*(t.maxY-t.minY)}function c(t){return t.maxX-t.minX+(t.maxY-t.minY)}function p(t,e){return t.minX<=e.minX&&t.minY<=e.minY&&e.maxX<=t.maxX&&e.maxY<=t.maxY}function f(t,e){return e.minX<=t.maxX&&e.minY<=t.maxY&&e.maxX>=t.minX&&e.maxY>=t.minY}function d(t){return{children:t,height:1,leaf:!0,minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}}function g(e,i,n,o,r){for(var s=[i,n];s.length;)if(!((n=s.pop())-(i=s.pop())<=o)){var a=i+Math.ceil((n-i)/o/2)*o;t(e,a,i,n,r),s.push(i,a,a,n)}}return n.prototype.all=function(){return this._all(this.data,[])},n.prototype.search=function(t){var e=this.data,i=[];if(!f(t,e))return i;for(var n=this.toBBox,o=[];e;){for(var r=0;r<e.children.length;r++){var s=e.children[r],a=e.leaf?n(s):s;f(t,a)&&(e.leaf?i.push(s):p(t,a)?this._all(s,i):o.push(s))}e=o.pop()}return i},n.prototype.collides=function(t){var e=this.data;if(!f(t,e))return!1;for(var i=[];e;){for(var n=0;n<e.children.length;n++){var o=e.children[n],r=e.leaf?this.toBBox(o):o;if(f(t,r)){if(e.leaf||p(t,r))return!0;i.push(o)}}e=i.pop()}return!1},n.prototype.load=function(t){if(!t||!t.length)return this;if(t.length<this._minEntries){for(var e=0;e<t.length;e++)this.insert(t[e]);return this}var i=this._build(t.slice(),0,t.length-1,0);if(this.data.children.length)if(this.data.height===i.height)this._splitRoot(this.data,i);else{if(this.data.height<i.height){var n=this.data;this.data=i,i=n}this._insert(i,this.data.height-i.height-1,!0)}else this.data=i;return this},n.prototype.insert=function(t){return t&&this._insert(t,this.data.height-1),this},n.prototype.clear=function(){return this.data=d([]),this},n.prototype.remove=function(t,e){if(!t)return this;for(var i,n,r,s=this.data,a=this.toBBox(t),l=[],h=[];s||l.length;){if(s||(s=l.pop(),n=l[l.length-1],i=h.pop(),r=!0),s.leaf){var u=o(t,s.children,e);if(-1!==u)return s.children.splice(u,1),l.push(s),this._condense(l),this}r||s.leaf||!p(s,a)?n?(i++,s=n.children[i],r=!1):s=null:(l.push(s),h.push(i),i=0,n=s,s=s.children[0])}return this},n.prototype.toBBox=function(t){return t},n.prototype.compareMinX=function(t,e){return t.minX-e.minX},n.prototype.compareMinY=function(t,e){return t.minY-e.minY},n.prototype.toJSON=function(){return this.data},n.prototype.fromJSON=function(t){return this.data=t,this},n.prototype._all=function(t,e){for(var i=[];t;)t.leaf?e.push.apply(e,t.children):i.push.apply(i,t.children),t=i.pop();return e},n.prototype._build=function(t,e,i,n){var o,s=i-e+1,a=this._maxEntries;if(s<=a)return r(o=d(t.slice(e,i+1)),this.toBBox),o;n||(n=Math.ceil(Math.log(s)/Math.log(a)),a=Math.ceil(s/Math.pow(a,n-1))),(o=d([])).leaf=!1,o.height=n;var l=Math.ceil(s/a),h=l*Math.ceil(Math.sqrt(a));g(t,e,i,h,this.compareMinX);for(var u=e;u<=i;u+=h){var c=Math.min(u+h-1,i);g(t,u,c,l,this.compareMinY);for(var p=u;p<=c;p+=l){var f=Math.min(p+l-1,c);o.children.push(this._build(t,p,f,n-1))}}return r(o,this.toBBox),o},n.prototype._chooseSubtree=function(t,e,i,n){for(;n.push(e),!e.leaf&&n.length-1!==i;){for(var o=1/0,r=1/0,s=void 0,a=0;a<e.children.length;a++){var l=e.children[a],h=u(l),c=(p=t,f=l,(Math.max(f.maxX,p.maxX)-Math.min(f.minX,p.minX))*(Math.max(f.maxY,p.maxY)-Math.min(f.minY,p.minY))-h);c<r?(r=c,o=h<o?h:o,s=l):c===r&&h<o&&(o=h,s=l)}e=s||e.children[0]}var p,f;return e},n.prototype._insert=function(t,e,i){var n=i?t:this.toBBox(t),o=[],r=this._chooseSubtree(n,this.data,e,o);for(r.children.push(t),a(r,n);e>=0&&o[e].children.length>this._maxEntries;)this._split(o,e),e--;this._adjustParentBBoxes(n,o,e)},n.prototype._split=function(t,e){var i=t[e],n=i.children.length,o=this._minEntries;this._chooseSplitAxis(i,o,n);var s=this._chooseSplitIndex(i,o,n),a=d(i.children.splice(s,i.children.length-s));a.height=i.height,a.leaf=i.leaf,r(i,this.toBBox),r(a,this.toBBox),e?t[e-1].children.push(a):this._splitRoot(i,a)},n.prototype._splitRoot=function(t,e){this.data=d([t,e]),this.data.height=t.height+1,this.data.leaf=!1,r(this.data,this.toBBox)},n.prototype._chooseSplitIndex=function(t,e,i){for(var n,o,r,a,l,h,c,p=1/0,f=1/0,d=e;d<=i-e;d++){var g=s(t,0,d,this.toBBox),_=s(t,d,i,this.toBBox),y=(o=g,r=_,void 0,void 0,void 0,void 0,a=Math.max(o.minX,r.minX),l=Math.max(o.minY,r.minY),h=Math.min(o.maxX,r.maxX),c=Math.min(o.maxY,r.maxY),Math.max(0,h-a)*Math.max(0,c-l)),v=u(g)+u(_);y<p?(p=y,n=d,f=v<f?v:f):y===p&&v<f&&(f=v,n=d)}return n||i-e},n.prototype._chooseSplitAxis=function(t,e,i){var n=t.leaf?this.compareMinX:l,o=t.leaf?this.compareMinY:h;this._allDistMargin(t,e,i,n)<this._allDistMargin(t,e,i,o)&&t.children.sort(n)},n.prototype._allDistMargin=function(t,e,i,n){t.children.sort(n);for(var o=this.toBBox,r=s(t,0,e,o),l=s(t,i-e,i,o),h=c(r)+c(l),u=e;u<i-e;u++){var p=t.children[u];a(r,t.leaf?o(p):p),h+=c(r)}for(var f=i-e-1;f>=e;f--){var d=t.children[f];a(l,t.leaf?o(d):d),h+=c(l)}return h},n.prototype._adjustParentBBoxes=function(t,e,i){for(var n=i;n>=0;n--)a(e[n],t)},n.prototype._condense=function(t){for(var e=t.length-1,i=void 0;e>=0;e--)0===t[e].children.length?e>0?(i=t[e-1].children).splice(i.indexOf(t[e]),1):this.clear():r(t[e],this.toBBox)},n}()}},e={};function i(n){var o=e[n];if(void 0!==o)return o.exports;var r=e[n]={exports:{}};return t[n].call(r.exports,r,r.exports,i),r.exports}i.d=function(t,e){for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var n={};return function(){"use strict";i.d(n,{default:function(){return uh}});var t=function(){function t(t){this.propagationStopped,this.defaultPrevented,this.type=t,this.target=null}return t.prototype.preventDefault=function(){this.defaultPrevented=!0},t.prototype.stopPropagation=function(){this.propagationStopped=!0},t}(),e="propertychange",o=function(){function t(){this.disposed=!1}return t.prototype.dispose=function(){this.disposed||(this.disposed=!0,this.disposeInternal())},t.prototype.disposeInternal=function(){},t}();function r(t,e){return t>e?1:t<e?-1:0}function s(t,e,i){var n=t.length;if(t[0]<=e)return 0;if(e<=t[n-1])return n-1;var o=void 0;if(i>0){for(o=1;o<n;++o)if(t[o]<e)return o-1}else if(i<0){for(o=1;o<n;++o)if(t[o]<=e)return o}else for(o=1;o<n;++o){if(t[o]==e)return o;if(t[o]<e)return"function"==typeof i?i(e,t[o-1],t[o])>0?o-1:o:t[o-1]-e<e-t[o]?o-1:o}return n-1}function a(t,e,i){for(;e<i;){var n=t[e];t[e]=t[i],t[i]=n,++e,--i}}function l(t,e){for(var i=Array.isArray(e)?e:[e],n=i.length,o=0;o<n;o++)t[t.length]=i[o]}function h(t,e){var i=t.length;if(i!==e.length)return!1;for(var n=0;n<i;n++)if(t[n]!==e[n])return!1;return!0}function u(){return!0}function c(){return!1}function p(){}var f="function"==typeof Object.assign?Object.assign:function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var i=Object(t),n=1,o=arguments.length;n<o;++n){var r=arguments[n];if(null!=r)for(var s in r)r.hasOwnProperty(s)&&(i[s]=r[s])}return i};function d(t){for(var e in t)delete t[e]}var g="function"==typeof Object.values?Object.values:function(t){var e=[];for(var i in t)e.push(t[i]);return e};function _(t){var e;for(e in t)return!1;return!e}var y,v=(y=function(t,e){return y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},y(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}y(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),m=function(e){function i(t){var i=e.call(this)||this;return i.eventTarget_=t,i.pendingRemovals_=null,i.dispatching_=null,i.listeners_=null,i}return v(i,e),i.prototype.addEventListener=function(t,e){if(t&&e){var i=this.listeners_||(this.listeners_={}),n=i[t]||(i[t]=[]);-1===n.indexOf(e)&&n.push(e)}},i.prototype.dispatchEvent=function(e){var i="string"==typeof e,n=i?e:e.type,o=this.listeners_&&this.listeners_[n];if(o){var r=i?new t(e):e;r.target||(r.target=this.eventTarget_||this);var s,a=this.dispatching_||(this.dispatching_={}),l=this.pendingRemovals_||(this.pendingRemovals_={});n in a||(a[n]=0,l[n]=0),++a[n];for(var h=0,u=o.length;h<u;++h)if(!1===(s="handleEvent"in o[h]?o[h].handleEvent(r):o[h].call(this,r))||r.propagationStopped){s=!1;break}if(0==--a[n]){var c=l[n];for(delete l[n];c--;)this.removeEventListener(n,p);delete a[n]}return s}},i.prototype.disposeInternal=function(){this.listeners_&&d(this.listeners_)},i.prototype.getListeners=function(t){return this.listeners_&&this.listeners_[t]||void 0},i.prototype.hasListener=function(t){return!!this.listeners_&&(t?t in this.listeners_:Object.keys(this.listeners_).length>0)},i.prototype.removeEventListener=function(t,e){var i=this.listeners_&&this.listeners_[t];if(i){var n=i.indexOf(e);-1!==n&&(this.pendingRemovals_&&t in this.pendingRemovals_?(i[n]=p,++this.pendingRemovals_[t]):(i.splice(n,1),0===i.length&&delete this.listeners_[t]))}},i}(o),x="change",C="contextmenu",w="click",S="keydown",E="keypress",T="touchmove",b="wheel";function O(t,e,i,n,o){if(n&&n!==t&&(i=i.bind(n)),o){var r=i;i=function(){t.removeEventListener(e,i),r.apply(this,arguments)}}var s={target:t,type:e,listener:i};return t.addEventListener(e,i),s}function R(t,e,i,n){return O(t,e,i,n,!0)}function P(t){t&&t.target&&(t.target.removeEventListener(t.type,t.listener),d(t))}var I=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),M=function(t){function e(){var e=t.call(this)||this;return e.on=e.onInternal,e.once=e.onceInternal,e.un=e.unInternal,e.revision_=0,e}return I(e,t),e.prototype.changed=function(){++this.revision_,this.dispatchEvent(x)},e.prototype.getRevision=function(){return this.revision_},e.prototype.onInternal=function(t,e){if(Array.isArray(t)){for(var i=t.length,n=new Array(i),o=0;o<i;++o)n[o]=O(this,t[o],e);return n}return O(this,t,e)},e.prototype.onceInternal=function(t,e){var i;if(Array.isArray(t)){var n=t.length;i=new Array(n);for(var o=0;o<n;++o)i[o]=R(this,t[o],e)}else i=R(this,t,e);return e.ol_key=i,i},e.prototype.unInternal=function(t,e){var i=e.ol_key;if(i)!function(t){if(Array.isArray(t))for(var e=0,i=t.length;e<i;++e)P(t[e]);else P(t)}(i);else if(Array.isArray(t))for(var n=0,o=t.length;n<o;++n)this.removeEventListener(t[n],e);else this.removeEventListener(t,e)},e}(m);M.prototype.on,M.prototype.once,M.prototype.un;var F=M;function L(){return function(){throw new Error("Unimplemented abstract method.")}()}var A=0;function D(t){return t.ol_uid||(t.ol_uid=String(++A))}var k=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),j=function(t){function e(e,i,n){var o=t.call(this,e)||this;return o.key=i,o.oldValue=n,o}return k(e,t),e}(t),G=function(t){function i(e){var i=t.call(this)||this;return i.on,i.once,i.un,D(i),i.values_=null,void 0!==e&&i.setProperties(e),i}return k(i,t),i.prototype.get=function(t){var e;return this.values_&&this.values_.hasOwnProperty(t)&&(e=this.values_[t]),e},i.prototype.getKeys=function(){return this.values_&&Object.keys(this.values_)||[]},i.prototype.getProperties=function(){return this.values_&&f({},this.values_)||{}},i.prototype.hasProperties=function(){return!!this.values_},i.prototype.notify=function(t,i){var n;n="change:".concat(t),this.hasListener(n)&&this.dispatchEvent(new j(n,t,i)),n=e,this.hasListener(n)&&this.dispatchEvent(new j(n,t,i))},i.prototype.addChangeListener=function(t,e){this.addEventListener("change:".concat(t),e)},i.prototype.removeChangeListener=function(t,e){this.removeEventListener("change:".concat(t),e)},i.prototype.set=function(t,e,i){var n=this.values_||(this.values_={});if(i)n[t]=e;else{var o=n[t];n[t]=e,o!==e&&this.notify(t,o)}},i.prototype.setProperties=function(t,e){for(var i in t)this.set(i,t[i],e)},i.prototype.applyProperties=function(t){t.values_&&f(this.values_||(this.values_={}),t.values_)},i.prototype.unset=function(t,e){if(this.values_&&t in this.values_){var i=this.values_[t];delete this.values_[t],_(this.values_)&&(this.values_=null),e||this.notify(t,i)}},i}(F),z="postrender",W="loadstart",X="loadend",N="undefined"!=typeof navigator&&void 0!==navigator.userAgent?navigator.userAgent.toLowerCase():"",Y=-1!==N.indexOf("firefox"),B=(-1!==N.indexOf("safari")&&-1==N.indexOf("chrom")&&(N.indexOf("version/15.4")>=0||N.match(/cpu (os|iphone os) 15_4 like mac os x/)),-1!==N.indexOf("webkit")&&-1==N.indexOf("edge")),K=-1!==N.indexOf("macintosh"),Z="undefined"!=typeof devicePixelRatio?devicePixelRatio:1,V="undefined"!=typeof WorkerGlobalScope&&"undefined"!=typeof OffscreenCanvas&&self instanceof WorkerGlobalScope,U="undefined"!=typeof Image&&Image.prototype.decode,H=function(){var t=!1;try{var e=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("_",null,e),window.removeEventListener("_",null,e)}catch(t){}return t}();function q(t,e,i,n){var o;return o=i&&i.length?i.shift():V?new OffscreenCanvas(t||300,e||300):document.createElement("canvas"),t&&(o.width=t),e&&(o.height=e),o.getContext("2d",n)}function J(t){var e=t.canvas;e.width=1,e.height=1,t.clearRect(0,0,1,1)}function Q(t,e){var i=e.parentNode;i&&i.replaceChild(t,e)}function $(t){return t&&t.parentNode?t.parentNode.removeChild(t):null}var tt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),et=function(t){function e(e){var i=t.call(this)||this,n=e.element;return!n||e.target||n.style.pointerEvents||(n.style.pointerEvents="auto"),i.element=n||null,i.target_=null,i.map_=null,i.listenerKeys=[],e.render&&(i.render=e.render),e.target&&i.setTarget(e.target),i}return tt(e,t),e.prototype.disposeInternal=function(){$(this.element),t.prototype.disposeInternal.call(this)},e.prototype.getMap=function(){return this.map_},e.prototype.setMap=function(t){this.map_&&$(this.element);for(var e=0,i=this.listenerKeys.length;e<i;++e)P(this.listenerKeys[e]);this.listenerKeys.length=0,this.map_=t,t&&((this.target_?this.target_:t.getOverlayContainerStopEvent()).appendChild(this.element),this.render!==p&&this.listenerKeys.push(O(t,z,this.render,this)),t.render())},e.prototype.render=function(t){},e.prototype.setTarget=function(t){this.target_="string"==typeof t?document.getElementById(t):t},e}(G),it="ol-hidden",nt="ol-unselectable",ot="ol-control",rt="ol-collapsed",st=new RegExp(["^\\s*(?=(?:(?:[-a-z]+\\s*){0,2}(italic|oblique))?)","(?=(?:(?:[-a-z]+\\s*){0,2}(small-caps))?)","(?=(?:(?:[-a-z]+\\s*){0,2}(bold(?:er)?|lighter|[1-9]00 ))?)","(?:(?:normal|\\1|\\2|\\3)\\s*){0,3}((?:xx?-)?","(?:small|large)|medium|smaller|larger|[\\.\\d]+(?:\\%|in|[cem]m|ex|p[ctx]))","(?:\\s*\\/\\s*(normal|[\\.\\d]+(?:\\%|in|[cem]m|ex|p[ctx])?))","?\\s*([-,\\\"\\'\\sa-z]+?)\\s*$"].join(""),"i"),at=["style","variant","weight","size","lineHeight","family"],lt=function(t){var e=t.match(st);if(!e)return null;for(var i={lineHeight:"normal",size:"1.2em",style:"normal",weight:"normal",variant:"normal"},n=0,o=at.length;n<o;++n){var r=e[n+1];void 0!==r&&(i[at[n]]=r)}return i.families=i.family.split(/,\s?/),i},ht="opacity",ut="visible",ct="extent",pt="zIndex",ft="maxResolution",dt="minResolution",gt="maxZoom",_t="minZoom",yt="source",vt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),mt=function(t){function e(e){var i=this,n="Assertion failed. See https://openlayers.org/en/v"+"6.15.1".split("-")[0]+"/doc/errors/#"+e+" for details.";return(i=t.call(this,n)||this).code=e,i.name="AssertionError",i.message=n,i}return vt(e,t),e}(Error);function xt(t,e){if(!t)throw new mt(e)}function Ct(t,e,i){return Math.min(Math.max(t,e),i)}var wt="cosh"in Math?Math.cosh:function(t){var e=Math.exp(t);return(e+1/e)/2},St="log2"in Math?Math.log2:function(t){return Math.log(t)*Math.LOG2E};function Et(t,e,i,n,o,r){var s=o-i,a=r-n;if(0!==s||0!==a){var l=((t-i)*s+(e-n)*a)/(s*s+a*a);l>1?(i=o,n=r):l>0&&(i+=s*l,n+=a*l)}return Tt(t,e,i,n)}function Tt(t,e,i,n){var o=i-t,r=n-e;return o*o+r*r}function bt(t){return t*Math.PI/180}function Ot(t,e){var i=t%e;return i*e<0?i+e:i}function Rt(t,e,i){return t+i*(e-t)}function Pt(t,e){var i=Math.pow(10,e);return Math.round(t*i)/i}function It(t,e){return Math.floor(Pt(t,e))}function Mt(t,e){return Math.ceil(Pt(t,e))}var Ft=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Lt=function(t){function e(e){var i=t.call(this)||this;i.on,i.once,i.un,i.background_=e.background;var n=f({},e);return"object"==typeof e.properties&&(delete n.properties,f(n,e.properties)),n[ht]=void 0!==e.opacity?e.opacity:1,xt("number"==typeof n[ht],64),n[ut]=void 0===e.visible||e.visible,n[pt]=e.zIndex,n[ft]=void 0!==e.maxResolution?e.maxResolution:1/0,n[dt]=void 0!==e.minResolution?e.minResolution:0,n[_t]=void 0!==e.minZoom?e.minZoom:-1/0,n[gt]=void 0!==e.maxZoom?e.maxZoom:1/0,i.className_=void 0!==n.className?n.className:"ol-layer",delete n.className,i.setProperties(n),i.state_=null,i}return Ft(e,t),e.prototype.getBackground=function(){return this.background_},e.prototype.getClassName=function(){return this.className_},e.prototype.getLayerState=function(t){var e=this.state_||{layer:this,managed:void 0===t||t},i=this.getZIndex();return e.opacity=Ct(Math.round(100*this.getOpacity())/100,0,1),e.visible=this.getVisible(),e.extent=this.getExtent(),e.zIndex=void 0!==i||e.managed?i:1/0,e.maxResolution=this.getMaxResolution(),e.minResolution=Math.max(this.getMinResolution(),0),e.minZoom=this.getMinZoom(),e.maxZoom=this.getMaxZoom(),this.state_=e,e},e.prototype.getLayersArray=function(t){return L()},e.prototype.getLayerStatesArray=function(t){return L()},e.prototype.getExtent=function(){return this.get(ct)},e.prototype.getMaxResolution=function(){return this.get(ft)},e.prototype.getMinResolution=function(){return this.get(dt)},e.prototype.getMinZoom=function(){return this.get(_t)},e.prototype.getMaxZoom=function(){return this.get(gt)},e.prototype.getOpacity=function(){return this.get(ht)},e.prototype.getSourceState=function(){return L()},e.prototype.getVisible=function(){return this.get(ut)},e.prototype.getZIndex=function(){return this.get(pt)},e.prototype.setBackground=function(t){this.background_=t,this.changed()},e.prototype.setExtent=function(t){this.set(ct,t)},e.prototype.setMaxResolution=function(t){this.set(ft,t)},e.prototype.setMinResolution=function(t){this.set(dt,t)},e.prototype.setMaxZoom=function(t){this.set(gt,t)},e.prototype.setMinZoom=function(t){this.set(_t,t)},e.prototype.setOpacity=function(t){xt("number"==typeof t,64),this.set(ht,t)},e.prototype.setVisible=function(t){this.set(ut,t)},e.prototype.setZIndex=function(t){this.set(pt,t)},e.prototype.disposeInternal=function(){this.state_&&(this.state_.layer=null,this.state_=null),t.prototype.disposeInternal.call(this)},e}(G),At="precompose",Dt="rendercomplete",kt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function jt(t,e){if(!t.visible)return!1;var i=e.resolution;if(i<t.minResolution||i>=t.maxResolution)return!1;var n=e.zoom;return n>t.minZoom&&n<=t.maxZoom}var Gt=function(t){function e(e){var i=this,n=f({},e);delete n.source,(i=t.call(this,n)||this).on,i.once,i.un,i.mapPrecomposeKey_=null,i.mapRenderKey_=null,i.sourceChangeKey_=null,i.renderer_=null,i.rendered=!1,e.render&&(i.render=e.render),e.map&&i.setMap(e.map),i.addChangeListener(yt,i.handleSourcePropertyChange_);var o=e.source?e.source:null;return i.setSource(o),i}return kt(e,t),e.prototype.getLayersArray=function(t){var e=t||[];return e.push(this),e},e.prototype.getLayerStatesArray=function(t){var e=t||[];return e.push(this.getLayerState()),e},e.prototype.getSource=function(){return this.get(yt)||null},e.prototype.getRenderSource=function(){return this.getSource()},e.prototype.getSourceState=function(){var t=this.getSource();return t?t.getState():"undefined"},e.prototype.handleSourceChange_=function(){this.changed()},e.prototype.handleSourcePropertyChange_=function(){this.sourceChangeKey_&&(P(this.sourceChangeKey_),this.sourceChangeKey_=null);var t=this.getSource();t&&(this.sourceChangeKey_=O(t,x,this.handleSourceChange_,this)),this.changed()},e.prototype.getFeatures=function(t){return this.renderer_?this.renderer_.getFeatures(t):new Promise((function(t){return t([])}))},e.prototype.getData=function(t){return this.renderer_&&this.rendered?this.renderer_.getData(t):null},e.prototype.render=function(t,e){var i=this.getRenderer();if(i.prepareFrame(t))return this.rendered=!0,i.renderFrame(t,e)},e.prototype.unrender=function(){this.rendered=!1},e.prototype.setMapInternal=function(t){t||this.unrender(),this.set("map",t)},e.prototype.getMapInternal=function(){return this.get("map")},e.prototype.setMap=function(t){this.mapPrecomposeKey_&&(P(this.mapPrecomposeKey_),this.mapPrecomposeKey_=null),t||this.changed(),this.mapRenderKey_&&(P(this.mapRenderKey_),this.mapRenderKey_=null),t&&(this.mapPrecomposeKey_=O(t,At,(function(t){var e=t.frameState.layerStatesArray,i=this.getLayerState(!1);xt(!e.some((function(t){return t.layer===i.layer})),67),e.push(i)}),this),this.mapRenderKey_=O(this,x,t.render,t),this.changed())},e.prototype.setSource=function(t){this.set(yt,t)},e.prototype.getRenderer=function(){return this.renderer_||(this.renderer_=this.createRenderer()),this.renderer_},e.prototype.hasRenderer=function(){return!!this.renderer_},e.prototype.createRenderer=function(){return null},e.prototype.disposeInternal=function(){this.renderer_&&(this.renderer_.dispose(),delete this.renderer_),this.setSource(null),t.prototype.disposeInternal.call(this)},e}(Lt),zt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Wt=function(t){function e(e){var i=this,n=e||{};(i=t.call(this,{element:document.createElement("div"),render:n.render,target:n.target})||this).ulElement_=document.createElement("ul"),i.collapsed_=void 0===n.collapsed||n.collapsed,i.userCollapsed_=i.collapsed_,i.overrideCollapsible_=void 0!==n.collapsible,i.collapsible_=void 0===n.collapsible||n.collapsible,i.collapsible_||(i.collapsed_=!1);var o=void 0!==n.className?n.className:"ol-attribution",r=void 0!==n.tipLabel?n.tipLabel:"Attributions",s=void 0!==n.expandClassName?n.expandClassName:o+"-expand",a=void 0!==n.collapseLabel?n.collapseLabel:"›",l=void 0!==n.collapseClassName?n.collapseClassName:o+"-collapse";"string"==typeof a?(i.collapseLabel_=document.createElement("span"),i.collapseLabel_.textContent=a,i.collapseLabel_.className=l):i.collapseLabel_=a;var h=void 0!==n.label?n.label:"i";"string"==typeof h?(i.label_=document.createElement("span"),i.label_.textContent=h,i.label_.className=s):i.label_=h;var u=i.collapsible_&&!i.collapsed_?i.collapseLabel_:i.label_;i.toggleButton_=document.createElement("button"),i.toggleButton_.setAttribute("type","button"),i.toggleButton_.setAttribute("aria-expanded",String(!i.collapsed_)),i.toggleButton_.title=r,i.toggleButton_.appendChild(u),i.toggleButton_.addEventListener(w,i.handleClick_.bind(i),!1);var c=o+" "+nt+" "+ot+(i.collapsed_&&i.collapsible_?" "+rt:"")+(i.collapsible_?"":" ol-uncollapsible"),p=i.element;return p.className=c,p.appendChild(i.toggleButton_),p.appendChild(i.ulElement_),i.renderedAttributions_=[],i.renderedVisible_=!0,i}return zt(e,t),e.prototype.collectSourceAttributions_=function(t){for(var e={},i=[],n=!0,o=t.layerStatesArray,r=0,s=o.length;r<s;++r){var a=o[r];if(jt(a,t.viewState)){var l=a.layer.getSource();if(l){var h=l.getAttributions();if(h){var u=h(t);if(u)if(n=n&&!1!==l.getAttributionsCollapsible(),Array.isArray(u))for(var c=0,p=u.length;c<p;++c)u[c]in e||(i.push(u[c]),e[u[c]]=!0);else u in e||(i.push(u),e[u]=!0)}}}}return this.overrideCollapsible_||this.setCollapsible(n),i},e.prototype.updateElement_=function(t){if(t){var e=this.collectSourceAttributions_(t),i=e.length>0;if(this.renderedVisible_!=i&&(this.element.style.display=i?"":"none",this.renderedVisible_=i),!h(e,this.renderedAttributions_)){!function(t){for(;t.lastChild;)t.removeChild(t.lastChild)}(this.ulElement_);for(var n=0,o=e.length;n<o;++n){var r=document.createElement("li");r.innerHTML=e[n],this.ulElement_.appendChild(r)}this.renderedAttributions_=e}}else this.renderedVisible_&&(this.element.style.display="none",this.renderedVisible_=!1)},e.prototype.handleClick_=function(t){t.preventDefault(),this.handleToggle_(),this.userCollapsed_=this.collapsed_},e.prototype.handleToggle_=function(){this.element.classList.toggle(rt),this.collapsed_?Q(this.collapseLabel_,this.label_):Q(this.label_,this.collapseLabel_),this.collapsed_=!this.collapsed_,this.toggleButton_.setAttribute("aria-expanded",String(!this.collapsed_))},e.prototype.getCollapsible=function(){return this.collapsible_},e.prototype.setCollapsible=function(t){this.collapsible_!==t&&(this.collapsible_=t,this.element.classList.toggle("ol-uncollapsible"),this.userCollapsed_&&this.handleToggle_())},e.prototype.setCollapsed=function(t){this.userCollapsed_=t,this.collapsible_&&this.collapsed_!==t&&this.handleToggle_()},e.prototype.getCollapsed=function(){return this.collapsed_},e.prototype.render=function(t){this.updateElement_(t.frameState)},e}(et),Xt="pointermove",Nt="pointerdown",Yt={RADIANS:"radians",DEGREES:"degrees",FEET:"ft",METERS:"m",PIXELS:"pixels",TILE_PIXELS:"tile-pixels",USFEET:"us-ft"},Bt={};Bt[Yt.RADIANS]=6370997/(2*Math.PI),Bt[Yt.DEGREES]=2*Math.PI*6370997/360,Bt[Yt.FEET]=.3048,Bt[Yt.METERS]=1,Bt[Yt.USFEET]=1200/3937;var Kt=Yt,Zt=function(){function t(t){this.code_=t.code,this.units_=t.units,this.extent_=void 0!==t.extent?t.extent:null,this.worldExtent_=void 0!==t.worldExtent?t.worldExtent:null,this.axisOrientation_=void 0!==t.axisOrientation?t.axisOrientation:"enu",this.global_=void 0!==t.global&&t.global,this.canWrapX_=!(!this.global_||!this.extent_),this.getPointResolutionFunc_=t.getPointResolution,this.defaultTileGrid_=null,this.metersPerUnit_=t.metersPerUnit}return t.prototype.canWrapX=function(){return this.canWrapX_},t.prototype.getCode=function(){return this.code_},t.prototype.getExtent=function(){return this.extent_},t.prototype.getUnits=function(){return this.units_},t.prototype.getMetersPerUnit=function(){return this.metersPerUnit_||Bt[this.units_]},t.prototype.getWorldExtent=function(){return this.worldExtent_},t.prototype.getAxisOrientation=function(){return this.axisOrientation_},t.prototype.isGlobal=function(){return this.global_},t.prototype.setGlobal=function(t){this.global_=t,this.canWrapX_=!(!t||!this.extent_)},t.prototype.getDefaultTileGrid=function(){return this.defaultTileGrid_},t.prototype.setDefaultTileGrid=function(t){this.defaultTileGrid_=t},t.prototype.setExtent=function(t){this.extent_=t,this.canWrapX_=!(!this.global_||!t)},t.prototype.setWorldExtent=function(t){this.worldExtent_=t},t.prototype.setGetPointResolution=function(t){this.getPointResolutionFunc_=t},t.prototype.getPointResolutionFunc=function(){return this.getPointResolutionFunc_},t}(),Vt=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ut=6378137,Ht=Math.PI*Ut,qt=[-Ht,-Ht,Ht,Ht],Jt=[-180,-85,180,85],Qt=Ut*Math.log(Math.tan(Math.PI/2)),$t=function(t){function e(e){return t.call(this,{code:e,units:Kt.METERS,extent:qt,global:!0,worldExtent:Jt,getPointResolution:function(t,e){return t/wt(e[1]/Ut)}})||this}return Vt(e,t),e}(Zt),te=[new $t("EPSG:3857"),new $t("EPSG:102100"),new $t("EPSG:102113"),new $t("EPSG:900913"),new $t("http://www.opengis.net/def/crs/EPSG/0/3857"),new $t("http://www.opengis.net/gml/srs/epsg.xml#3857")];var ee=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ie=[-180,-90,180,90],ne=6378137*Math.PI/180,oe=function(t){function e(e,i){return t.call(this,{code:e,units:Kt.DEGREES,extent:ie,axisOrientation:i,global:!0,metersPerUnit:ne,worldExtent:ie})||this}return ee(e,t),e}(Zt),re=[new oe("CRS:84"),new oe("EPSG:4326","neu"),new oe("urn:ogc:def:crs:OGC:1.3:CRS84"),new oe("urn:ogc:def:crs:OGC:2:84"),new oe("http://www.opengis.net/def/crs/OGC/1.3/CRS84"),new oe("http://www.opengis.net/gml/srs/epsg.xml#4326","neu"),new oe("http://www.opengis.net/def/crs/EPSG/0/4326","neu")],se={},ae={};function le(t,e,i){var n=t.getCode(),o=e.getCode();n in ae||(ae[n]={}),ae[n][o]=i}function he(t){for(var e=[1/0,1/0,-1/0,-1/0],i=0,n=t.length;i<n;++i)Ce(e,t[i]);return e}function ue(t,e,i){return i?(i[0]=t[0]-e,i[1]=t[1]-e,i[2]=t[2]+e,i[3]=t[3]+e,i):[t[0]-e,t[1]-e,t[2]+e,t[3]+e]}function ce(t,e){return e?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):t.slice()}function pe(t,e,i){var n,o;return(n=e<t[0]?t[0]-e:t[2]<e?e-t[2]:0)*n+(o=i<t[1]?t[1]-i:t[3]<i?i-t[3]:0)*o}function fe(t,e){return ge(t,e[0],e[1])}function de(t,e){return t[0]<=e[0]&&e[2]<=t[2]&&t[1]<=e[1]&&e[3]<=t[3]}function ge(t,e,i){return t[0]<=e&&e<=t[2]&&t[1]<=i&&i<=t[3]}function _e(t,e){var i=t[0],n=t[1],o=t[2],r=t[3],s=e[0],a=e[1],l=0;return s<i?l|=16:s>o&&(l|=4),a<n?l|=8:a>r&&(l|=2),0===l&&(l=1),l}function ye(t,e,i,n,o){return o?(o[0]=t,o[1]=e,o[2]=i,o[3]=n,o):[t,e,i,n]}function ve(t){return ye(1/0,1/0,-1/0,-1/0,t)}function me(t,e,i,n,o){return we(ve(o),t,e,i,n)}function xe(t,e){return t[0]==e[0]&&t[2]==e[2]&&t[1]==e[1]&&t[3]==e[3]}function Ce(t,e){e[0]<t[0]&&(t[0]=e[0]),e[0]>t[2]&&(t[2]=e[0]),e[1]<t[1]&&(t[1]=e[1]),e[1]>t[3]&&(t[3]=e[1])}function we(t,e,i,n,o){for(;i<n;i+=o)Se(t,e[i],e[i+1]);return t}function Se(t,e,i){t[0]=Math.min(t[0],e),t[1]=Math.min(t[1],i),t[2]=Math.max(t[2],e),t[3]=Math.max(t[3],i)}function Ee(t,e){var i;return(i=e(be(t)))||(i=e(Oe(t)))||(i=e(De(t)))?i:(i=e(Ae(t)))||!1}function Te(t){var e=0;return Ge(t)||(e=ke(t)*Fe(t)),e}function be(t){return[t[0],t[1]]}function Oe(t){return[t[2],t[1]]}function Re(t){return[(t[0]+t[2])/2,(t[1]+t[3])/2]}function Pe(t,e){var i;return"bottom-left"===e?i=be(t):"bottom-right"===e?i=Oe(t):"top-left"===e?i=Ae(t):"top-right"===e?i=De(t):xt(!1,13),i}function Ie(t,e,i,n,o){var r=Me(t,e,i,n),s=r[0],a=r[1],l=r[2],h=r[3],u=r[4],c=r[5],p=r[6],f=r[7];return ye(Math.min(s,l,u,p),Math.min(a,h,c,f),Math.max(s,l,u,p),Math.max(a,h,c,f),o)}function Me(t,e,i,n){var o=e*n[0]/2,r=e*n[1]/2,s=Math.cos(i),a=Math.sin(i),l=o*s,h=o*a,u=r*s,c=r*a,p=t[0],f=t[1];return[p-l+c,f-h-u,p-l-c,f-h+u,p+l-c,f+h+u,p+l+c,f+h-u,p-l+c,f-h-u]}function Fe(t){return t[3]-t[1]}function Le(t,e,i){var n=i||[1/0,1/0,-1/0,-1/0];return je(t,e)?(t[0]>e[0]?n[0]=t[0]:n[0]=e[0],t[1]>e[1]?n[1]=t[1]:n[1]=e[1],t[2]<e[2]?n[2]=t[2]:n[2]=e[2],t[3]<e[3]?n[3]=t[3]:n[3]=e[3]):ve(n),n}function Ae(t){return[t[0],t[3]]}function De(t){return[t[2],t[3]]}function ke(t){return t[2]-t[0]}function je(t,e){return t[0]<=e[2]&&t[2]>=e[0]&&t[1]<=e[3]&&t[3]>=e[1]}function Ge(t){return t[2]<t[0]||t[3]<t[1]}function ze(t,e){var i=e.getExtent(),n=Re(t);if(e.canWrapX()&&(n[0]<i[0]||n[0]>=i[2])){var o=ke(i),r=Math.floor((n[0]-i[0])/o)*o;t[0]-=r,t[2]-=r}return t}function We(t,e){for(var i=!0,n=t.length-1;n>=0;--n)if(t[n]!=e[n]){i=!1;break}return i}function Xe(t,e){var i=Math.cos(e),n=Math.sin(e),o=t[0]*i-t[1]*n,r=t[1]*i+t[0]*n;return t[0]=o,t[1]=r,t}function Ne(t,e){if(e.canWrapX()){var i=ke(e.getExtent()),n=function(t,e,i){var n=e.getExtent(),o=0;if(e.canWrapX()&&(t[0]<n[0]||t[0]>n[2])){var r=i||ke(n);o=Math.floor((t[0]-n[0])/r)}return o}(t,e,i);n&&(t[0]-=n*i)}return t}function Ye(t,e,i){var n=i||6371008.8,o=bt(t[1]),r=bt(e[1]),s=(r-o)/2,a=bt(e[0]-t[0])/2,l=Math.sin(s)*Math.sin(s)+Math.sin(a)*Math.sin(a)*Math.cos(o)*Math.cos(r);return 2*n*Math.atan2(Math.sqrt(l),Math.sqrt(1-l))}var Be=!0;function Ke(t){Be=!(void 0===t||t)}function Ze(t,e,i){var n;if(void 0!==e){for(var o=0,r=t.length;o<r;++o)e[o]=t[o];n=e}else n=t.slice();return n}function Ve(t,e,i){if(void 0!==e&&t!==e){for(var n=0,o=t.length;n<o;++n)e[n]=t[n];t=e}return t}function Ue(t){!function(t,e){se[t]=e}(t.getCode(),t),le(t,t,Ze)}function He(t){return"string"==typeof t?se[e=t]||se[e.replace(/urn:(x-)?ogc:def:crs:EPSG:(.*:)?(\w+)$/,"EPSG:$3")]||null:t||null;var e}function qe(t,e,i,n){var o,r=(t=He(t)).getPointResolutionFunc();if(r)o=r(e,i),n&&n!==t.getUnits()&&(a=t.getMetersPerUnit())&&(o=o*a/Bt[n]);else{var s=t.getUnits();if(s==Kt.DEGREES&&!n||n==Kt.DEGREES)o=e;else{var a,l=ti(t,He("EPSG:4326"));if(l===Ve&&s!==Kt.DEGREES)o=e*t.getMetersPerUnit();else{var h=[i[0]-e/2,i[1],i[0]+e/2,i[1],i[0],i[1]-e/2,i[0],i[1]+e/2];o=(Ye((h=l(h,h,2)).slice(0,2),h.slice(2,4))+Ye(h.slice(4,6),h.slice(6,8)))/2}void 0!==(a=n?Bt[n]:t.getMetersPerUnit())&&(o/=a)}}return o}function Je(t){!function(t){t.forEach(Ue)}(t),t.forEach((function(e){t.forEach((function(t){e!==t&&le(e,t,Ze)}))}))}function Qe(t,e){return t?"string"==typeof t?He(t):t:He(e)}function $e(t,e){if(t===e)return!0;var i=t.getUnits()===e.getUnits();return(t.getCode()===e.getCode()||ti(t,e)===Ze)&&i}function ti(t,e){var i=function(t,e){var i;return t in ae&&e in ae[t]&&(i=ae[t][e]),i}(t.getCode(),e.getCode());return i||(i=Ve),i}function ei(t,e){return ti(He(t),He(e))}function ii(t,e,i){return ei(e,i)(t,void 0,t.length)}function ni(t,e,i,n){return function(t,e,i,n){var o=[];if(n>1)for(var r=t[2]-t[0],s=t[3]-t[1],a=0;a<n;++a)o.push(t[0]+r*a/n,t[1],t[2],t[1]+s*a/n,t[2]-r*a/n,t[3],t[0],t[3]-s*a/n);else o=[t[0],t[1],t[2],t[1],t[2],t[3],t[0],t[3]];e(o,o,2);for(var l=[],h=[],u=(a=0,o.length);a<u;a+=2)l.push(o[a]),h.push(o[a+1]);return function(t,e,i){return ye(Math.min.apply(null,t),Math.min.apply(null,e),Math.max.apply(null,t),Math.max.apply(null,e),i)}(l,h,i)}(t,ei(e,i),void 0,n)}var oi,ri,si,ai=null;function li(){return ai}function hi(t,e){return t}function ui(t,e){return Be&&!We(t,[0,0])&&t[0]>=-180&&t[0]<=180&&t[1]>=-90&&t[1]<=90&&(Be=!1,console.warn("Call useGeographic() from ol/proj once to work with [longitude, latitude] coordinates.")),t}function ci(t,e){return t}function pi(t,e){return t}function fi(t,e){return t}Je(te),Je(re),oi=te,ri=function(t,e,i){var n=t.length,o=i>1?i:2,r=e;void 0===r&&(r=o>2?t.slice():new Array(n));for(var s=0;s<n;s+=o){r[s]=Ht*t[s]/180;var a=Ut*Math.log(Math.tan(Math.PI*(+t[s+1]+90)/360));a>Qt?a=Qt:a<-Qt&&(a=-Qt),r[s+1]=a}return r},si=function(t,e,i){var n=t.length,o=i>1?i:2,r=e;void 0===r&&(r=o>2?t.slice():new Array(n));for(var s=0;s<n;s+=o)r[s]=180*t[s]/Ht,r[s+1]=360*Math.atan(Math.exp(t[s+1]/Ut))/Math.PI-90;return r},re.forEach((function(t){oi.forEach((function(e){le(t,e,ri),le(e,t,si)}))}));var di=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),gi="projection",_i="coordinateFormat",yi=function(t){function e(e){var i=this,n=e||{},o=document.createElement("div");o.className=void 0!==n.className?n.className:"ol-mouse-position",(i=t.call(this,{element:o,render:n.render,target:n.target})||this).on,i.once,i.un,i.addChangeListener(gi,i.handleProjectionChanged_),n.coordinateFormat&&i.setCoordinateFormat(n.coordinateFormat),n.projection&&i.setProjection(n.projection);var r=!0,s="&#160;";return"undefinedHTML"in n?(void 0!==n.undefinedHTML&&(s=n.undefinedHTML),r=!!s):"placeholder"in n&&(!1===n.placeholder?r=!1:s=String(n.placeholder)),i.placeholder_=s,i.renderOnMouseOut_=r,i.renderedHTML_=o.innerHTML,i.mapProjection_=null,i.transform_=null,i}return di(e,t),e.prototype.handleProjectionChanged_=function(){this.transform_=null},e.prototype.getCoordinateFormat=function(){return this.get(_i)},e.prototype.getProjection=function(){return this.get(gi)},e.prototype.handleMouseMove=function(t){var e=this.getMap();this.updateHTML_(e.getEventPixel(t))},e.prototype.handleMouseOut=function(t){this.updateHTML_(null)},e.prototype.setMap=function(e){if(t.prototype.setMap.call(this,e),e){var i=e.getViewport();this.listenerKeys.push(O(i,Xt,this.handleMouseMove,this)),this.renderOnMouseOut_&&this.listenerKeys.push(O(i,"pointerout",this.handleMouseOut,this)),this.updateHTML_(null)}},e.prototype.setCoordinateFormat=function(t){this.set(_i,t)},e.prototype.setProjection=function(t){this.set(gi,He(t))},e.prototype.updateHTML_=function(t){var e=this.placeholder_;if(t&&this.mapProjection_){if(!this.transform_){var i=this.getProjection();this.transform_=i?ti(this.mapProjection_,i):Ve}var n=this.getMap().getCoordinateFromPixelInternal(t);if(n){var o=li();o&&(this.transform_=ti(this.mapProjection_,o)),this.transform_(n,n);var r=this.getCoordinateFormat();e=r?r(n):n.toString()}}this.renderedHTML_&&e===this.renderedHTML_||(this.element.innerHTML=e,this.renderedHTML_=e)},e.prototype.render=function(t){var e=t.frameState;e?this.mapProjection_!=e.viewState.projection&&(this.mapProjection_=e.viewState.projection,this.transform_=null):this.mapProjection_=null},e}(et),vi=yi;function mi(t){return Math.pow(t,3)}function xi(t){return 1-mi(1-t)}function Ci(t){return 3*t*t-2*t*t*t}function wi(t){return t}var Si,Ei=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ti=function(t){function e(e){var i=this,n=e||{};i=t.call(this,{element:document.createElement("div"),target:n.target})||this;var o=void 0!==n.className?n.className:"ol-zoom",r=void 0!==n.delta?n.delta:1,s=void 0!==n.zoomInClassName?n.zoomInClassName:o+"-in",a=void 0!==n.zoomOutClassName?n.zoomOutClassName:o+"-out",l=void 0!==n.zoomInLabel?n.zoomInLabel:"+",h=void 0!==n.zoomOutLabel?n.zoomOutLabel:"–",u=void 0!==n.zoomInTipLabel?n.zoomInTipLabel:"Zoom in",c=void 0!==n.zoomOutTipLabel?n.zoomOutTipLabel:"Zoom out",p=document.createElement("button");p.className=s,p.setAttribute("type","button"),p.title=u,p.appendChild("string"==typeof l?document.createTextNode(l):l),p.addEventListener(w,i.handleClick_.bind(i,r),!1);var f=document.createElement("button");f.className=a,f.setAttribute("type","button"),f.title=c,f.appendChild("string"==typeof h?document.createTextNode(h):h),f.addEventListener(w,i.handleClick_.bind(i,-r),!1);var d=o+" "+nt+" "+ot,g=i.element;return g.className=d,g.appendChild(p),g.appendChild(f),i.duration_=void 0!==n.duration?n.duration:250,i}return Ei(e,t),e.prototype.handleClick_=function(t,e){e.preventDefault(),this.zoomByDelta_(t)},e.prototype.zoomByDelta_=function(t){var e=this.getMap().getView();if(e){var i=e.getZoom();if(void 0!==i){var n=e.getConstrainedZoom(i+t);this.duration_>0?(e.getAnimating()&&e.cancelAnimations(),e.animate({zoom:n,duration:this.duration_,easing:xi})):e.setZoom(n)}}},e}(et),bi="XY",Oi="XYM",Ri="XYZM";function Pi(t,e){var i=e[0],n=e[1];return e[0]=t[0]*i+t[2]*n+t[4],e[1]=t[1]*i+t[3]*n+t[5],e}function Ii(t,e,i,n,o,r,s,a){var l=Math.sin(r),h=Math.cos(r);return t[0]=n*h,t[1]=o*l,t[2]=-n*l,t[3]=o*h,t[4]=s*n*h-a*n*l+e,t[5]=s*o*l+a*o*h+i,t}function Mi(t,e){var i,n=(i=e)[0]*i[3]-i[1]*i[2];xt(0!==n,32);var o=e[0],r=e[1],s=e[2],a=e[3],l=e[4],h=e[5];return t[0]=a/n,t[1]=-r/n,t[2]=-s/n,t[3]=o/n,t[4]=(s*h-a*l)/n,t[5]=-(o*h-r*l)/n,t}function Fi(t){var e="matrix("+t.join(", ")+")";if(V)return e;var i=Si||(Si=document.createElement("div"));return i.style.transform=e,i.style.transform}function Li(t,e,i,n,o,r){for(var s=r||[],a=0,l=e;l<i;l+=n){var h=t[l],u=t[l+1];s[a++]=o[0]*h+o[2]*u+o[4],s[a++]=o[1]*h+o[3]*u+o[5]}return r&&s.length!=a&&(s.length=a),s}function Ai(t,e,i,n,o,r,s){for(var a=s||[],l=Math.cos(o),h=Math.sin(o),u=r[0],c=r[1],p=0,f=e;f<i;f+=n){var d=t[f]-u,g=t[f+1]-c;a[p++]=u+d*l-g*h,a[p++]=c+d*h+g*l;for(var _=f+2;_<f+n;++_)a[p++]=t[_]}return s&&a.length!=p&&(a.length=p),a}new Array(6);var Di=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ki=[1,0,0,1,0,0],ji=function(t){function e(){var e,i,n,o,r,s=t.call(this)||this;return s.extent_=[1/0,1/0,-1/0,-1/0],s.extentRevision_=-1,s.simplifiedGeometryMaxMinSquaredTolerance=0,s.simplifiedGeometryRevision=0,s.simplifyTransformedInternal=(e=function(t,e,i){if(!i)return this.getSimplifiedGeometry(e);var n=this.clone();return n.applyTransform(i),n.getSimplifiedGeometry(e)},r=!1,function(){var t=Array.prototype.slice.call(arguments);return r&&this===o&&h(t,n)||(r=!0,o=this,n=t,i=e.apply(this,arguments)),i}),s}return Di(e,t),e.prototype.simplifyTransformed=function(t,e){return this.simplifyTransformedInternal(this.getRevision(),t,e)},e.prototype.clone=function(){return L()},e.prototype.closestPointXY=function(t,e,i,n){return L()},e.prototype.containsXY=function(t,e){var i=this.getClosestPoint([t,e]);return i[0]===t&&i[1]===e},e.prototype.getClosestPoint=function(t,e){var i=e||[NaN,NaN];return this.closestPointXY(t[0],t[1],i,1/0),i},e.prototype.intersectsCoordinate=function(t){return this.containsXY(t[0],t[1])},e.prototype.computeExtent=function(t){return L()},e.prototype.getExtent=function(t){if(this.extentRevision_!=this.getRevision()){var e=this.computeExtent(this.extent_);(isNaN(e[0])||isNaN(e[1]))&&ve(e),this.extentRevision_=this.getRevision()}return function(t,e){return e?(e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e):t}(this.extent_,t)},e.prototype.rotate=function(t,e){L()},e.prototype.scale=function(t,e,i){L()},e.prototype.simplify=function(t){return this.getSimplifiedGeometry(t*t)},e.prototype.getSimplifiedGeometry=function(t){return L()},e.prototype.getType=function(){return L()},e.prototype.applyTransform=function(t){L()},e.prototype.intersectsExtent=function(t){return L()},e.prototype.translate=function(t,e){L()},e.prototype.transform=function(t,e){var i=He(t),n=i.getUnits()==Kt.TILE_PIXELS?function(t,n,o){var r=i.getExtent(),s=i.getWorldExtent(),a=Fe(s)/Fe(r);return Ii(ki,s[0],s[3],a,-a,0,0,0),Li(t,0,t.length,o,ki,n),ei(i,e)(t,n,o)}:ei(i,e);return this.applyTransform(n),this},e}(G),Gi=ji,zi=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Wi=function(t){function e(){var e=t.call(this)||this;return e.layout=bi,e.stride=2,e.flatCoordinates=null,e}return zi(e,t),e.prototype.computeExtent=function(t){return me(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t)},e.prototype.getCoordinates=function(){return L()},e.prototype.getFirstCoordinate=function(){return this.flatCoordinates.slice(0,this.stride)},e.prototype.getFlatCoordinates=function(){return this.flatCoordinates},e.prototype.getLastCoordinate=function(){return this.flatCoordinates.slice(this.flatCoordinates.length-this.stride)},e.prototype.getLayout=function(){return this.layout},e.prototype.getSimplifiedGeometry=function(t){if(this.simplifiedGeometryRevision!==this.getRevision()&&(this.simplifiedGeometryMaxMinSquaredTolerance=0,this.simplifiedGeometryRevision=this.getRevision()),t<0||0!==this.simplifiedGeometryMaxMinSquaredTolerance&&t<=this.simplifiedGeometryMaxMinSquaredTolerance)return this;var e=this.getSimplifiedGeometryInternal(t);return e.getFlatCoordinates().length<this.flatCoordinates.length?e:(this.simplifiedGeometryMaxMinSquaredTolerance=t,this)},e.prototype.getSimplifiedGeometryInternal=function(t){return this},e.prototype.getStride=function(){return this.stride},e.prototype.setFlatCoordinates=function(t,e){this.stride=Xi(t),this.layout=t,this.flatCoordinates=e},e.prototype.setCoordinates=function(t,e){L()},e.prototype.setLayout=function(t,e,i){var n;if(t)n=Xi(t);else{for(var o=0;o<i;++o){if(0===e.length)return this.layout=bi,void(this.stride=2);e=e[0]}t=function(t){var e;return 2==t?e=bi:3==t?e="XYZ":4==t&&(e=Ri),e}(n=e.length)}this.layout=t,this.stride=n},e.prototype.applyTransform=function(t){this.flatCoordinates&&(t(this.flatCoordinates,this.flatCoordinates,this.stride),this.changed())},e.prototype.rotate=function(t,e){var i=this.getFlatCoordinates();if(i){var n=this.getStride();Ai(i,0,i.length,n,t,e,i),this.changed()}},e.prototype.scale=function(t,e,i){var n=e;void 0===n&&(n=t);var o=i;o||(o=Re(this.getExtent()));var r=this.getFlatCoordinates();if(r){var s=this.getStride();!function(t,e,i,n,o,r,s,a){for(var l=a||[],h=s[0],u=s[1],c=0,p=0;p<i;p+=n){var f=t[p]-h,d=t[p+1]-u;l[c++]=h+o*f,l[c++]=u+r*d;for(var g=p+2;g<p+n;++g)l[c++]=t[g]}a&&l.length!=c&&(l.length=c)}(r,0,r.length,s,t,n,o,r),this.changed()}},e.prototype.translate=function(t,e){var i=this.getFlatCoordinates();if(i){var n=this.getStride();!function(t,e,i,n,o,r,s){for(var a=s||[],l=0,h=0;h<i;h+=n){a[l++]=t[h]+o,a[l++]=t[h+1]+r;for(var u=h+2;u<h+n;++u)a[l++]=t[u]}s&&a.length!=l&&(a.length=l)}(i,0,i.length,n,t,e,i),this.changed()}},e}(Gi);function Xi(t){var e;return t==bi?e=2:"XYZ"==t||t==Oi?e=3:t==Ri&&(e=4),e}var Ni=Wi;function Yi(t,e,i,n,o,r,s){var a,l=t[e],h=t[e+1],u=t[i]-l,c=t[i+1]-h;if(0===u&&0===c)a=e;else{var p=((o-l)*u+(r-h)*c)/(u*u+c*c);if(p>1)a=i;else{if(p>0){for(var f=0;f<n;++f)s[f]=Rt(t[e+f],t[i+f],p);return void(s.length=n)}a=e}}for(f=0;f<n;++f)s[f]=t[a+f];s.length=n}function Bi(t,e,i,n,o){var r=t[e],s=t[e+1];for(e+=n;e<i;e+=n){var a=t[e],l=t[e+1],h=Tt(r,s,a,l);h>o&&(o=h),r=a,s=l}return o}function Ki(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r){var a=i[r];o=Bi(t,e,a,n,o),e=a}return o}function Zi(t,e,i,n,o,r,s,a,l,h,u){if(e==i)return h;var c,p;if(0===o){if((p=Tt(s,a,t[e],t[e+1]))<h){for(c=0;c<n;++c)l[c]=t[e+c];return l.length=n,p}return h}for(var f=u||[NaN,NaN],d=e+n;d<i;)if(Yi(t,d-n,d,n,s,a,f),(p=Tt(s,a,f[0],f[1]))<h){for(h=p,c=0;c<n;++c)l[c]=f[c];l.length=n,d+=n}else d+=n*Math.max((Math.sqrt(p)-Math.sqrt(h))/o|0,1);if(r&&(Yi(t,i-n,e,n,s,a,f),(p=Tt(s,a,f[0],f[1]))<h)){for(h=p,c=0;c<n;++c)l[c]=f[c];l.length=n}return h}function Vi(t,e,i,n,o,r,s,a,l,h,u){for(var c=u||[NaN,NaN],p=0,f=i.length;p<f;++p){var d=i[p];h=Zi(t,e,d,n,o,r,s,a,l,h,c),e=d}return h}function Ui(t,e,i,n){for(var o=0,r=i.length;o<r;++o)for(var s=i[o],a=0;a<n;++a)t[e++]=s[a];return e}function Hi(t,e,i,n,o){for(var r=o||[],s=0,a=0,l=i.length;a<l;++a){var h=Ui(t,e,i[a],n);r[s++]=h,e=h}return r.length=s,r}function qi(t,e,i,n,o,r,s){var a=(i-e)/n;if(a<3){for(;e<i;e+=n)r[s++]=t[e],r[s++]=t[e+1];return s}var l=new Array(a);l[0]=1,l[a-1]=1;for(var h=[e,i-n],u=0;h.length>0;){for(var c=h.pop(),p=h.pop(),f=0,d=t[p],g=t[p+1],_=t[c],y=t[c+1],v=p+n;v<c;v+=n){var m=Et(t[v],t[v+1],d,g,_,y);m>f&&(u=v,f=m)}f>o&&(l[(u-e)/n]=1,p+n<u&&h.push(p,u),u+n<c&&h.push(u,c))}for(v=0;v<a;++v)l[v]&&(r[s++]=t[e+v*n],r[s++]=t[e+v*n+1]);return s}function Ji(t,e){return e*Math.round(t/e)}function Qi(t,e,i,n,o,r,s){if(e==i)return s;var a,l,h=Ji(t[e],o),u=Ji(t[e+1],o);e+=n,r[s++]=h,r[s++]=u;do{if(a=Ji(t[e],o),l=Ji(t[e+1],o),(e+=n)==i)return r[s++]=a,r[s++]=l,s}while(a==h&&l==u);for(;e<i;){var c=Ji(t[e],o),p=Ji(t[e+1],o);if(e+=n,c!=a||p!=l){var f=a-h,d=l-u,g=c-h,_=p-u;f*_==d*g&&(f<0&&g<f||f==g||f>0&&g>f)&&(d<0&&_<d||d==_||d>0&&_>d)?(a=c,l=p):(r[s++]=a,r[s++]=l,h=a,u=l,a=c,l=p)}}return r[s++]=a,r[s++]=l,s}function $i(t,e,i,n,o,r,s,a){for(var l=0,h=i.length;l<h;++l){var u=i[l];s=Qi(t,e,u,n,o,r,s),a.push(s),e=u}return s}function tn(t,e,i,n,o){var r;for(e+=n;e<i;e+=n)if(r=o(t.slice(e-n,e),t.slice(e,e+n)))return r;return!1}function en(t,e,i,n,o){for(var r=void 0!==o?o:[],s=0,a=e;a<i;a+=n)r[s++]=t.slice(a,a+n);return r.length=s,r}function nn(t,e,i,n,o){for(var r=void 0!==o?o:[],s=0,a=0,l=i.length;a<l;++a){var h=i[a];r[s++]=en(t,e,h,n,r[s]),e=h}return r.length=s,r}function on(t,e,i,n,o){for(var r=void 0!==o?o:[],s=0,a=0,l=i.length;a<l;++a){var h=i[a];r[s++]=nn(t,e,h,n,r[s]),e=h[h.length-1]}return r.length=s,r}function rn(t,e,i,n,o,s,a){var l,h,u=(i-e)/n;if(1===u)l=e;else if(2===u)l=e,h=o;else if(0!==u){for(var c=t[e],p=t[e+1],f=0,d=[0],g=e+n;g<i;g+=n){var _=t[g],y=t[g+1];f+=Math.sqrt((_-c)*(_-c)+(y-p)*(y-p)),d.push(f),c=_,p=y}var v=o*f,m=function(t,e,i){for(var n,o,s=r,a=0,l=t.length,h=!1;a<l;)(o=+s(t[n=a+(l-a>>1)],e))<0?a=n+1:(l=n,h=!o);return h?a:~a}(d,v);m<0?(h=(v-d[-m-2])/(d[-m-1]-d[-m-2]),l=e+(-m-2)*n):l=e+m*n}var x=a>1?a:2,C=s||new Array(x);for(g=0;g<x;++g)C[g]=void 0===l?NaN:void 0===h?t[l+g]:Rt(t[l+g],t[l+n+g],h);return C}function sn(t,e,i,n,o,r){if(i==e)return null;var s;if(o<t[e+n-1])return r?((s=t.slice(e,e+n))[n-1]=o,s):null;if(t[i-1]<o)return r?((s=t.slice(i-n,i))[n-1]=o,s):null;if(o==t[e+n-1])return t.slice(e,e+n);for(var a=e/n,l=i/n;a<l;){var h=a+l>>1;o<t[(h+1)*n-1]?l=h:a=h+1}var u=t[a*n-1];if(o==u)return t.slice((a-1)*n,(a-1)*n+n);var c=(o-u)/(t[(a+1)*n-1]-u);s=[];for(var p=0;p<n-1;++p)s.push(Rt(t[(a-1)*n+p],t[a*n+p],c));return s.push(o),s}function an(t,e,i,n,o){return!Ee(o,(function(o){return!ln(t,e,i,n,o[0],o[1])}))}function ln(t,e,i,n,o,r){for(var s=0,a=t[i-n],l=t[i-n+1];e<i;e+=n){var h=t[e],u=t[e+1];l<=r?u>r&&(h-a)*(r-l)-(o-a)*(u-l)>0&&s++:u<=r&&(h-a)*(r-l)-(o-a)*(u-l)<0&&s--,a=h,l=u}return 0!==s}function hn(t,e,i,n,o,r){if(0===i.length)return!1;if(!ln(t,e,i[0],n,o,r))return!1;for(var s=1,a=i.length;s<a;++s)if(ln(t,i[s-1],i[s],n,o,r))return!1;return!0}function un(t,e,i,n,o){var r=we([1/0,1/0,-1/0,-1/0],t,e,i,n);return!!je(o,r)&&(!!de(o,r)||r[0]>=o[0]&&r[2]<=o[2]||r[1]>=o[1]&&r[3]<=o[3]||tn(t,e,i,n,(function(t,e){return function(t,e,i){var n=!1,o=_e(t,e),r=_e(t,i);if(1===o||1===r)n=!0;else{var s=t[0],a=t[1],l=t[2],h=t[3],u=e[0],c=e[1],p=i[0],f=i[1],d=(f-c)/(p-u),g=void 0,_=void 0;2&r&&!(2&o)&&(n=(g=p-(f-h)/d)>=s&&g<=l),n||!(4&r)||4&o||(n=(_=f-(p-l)*d)>=a&&_<=h),n||!(8&r)||8&o||(n=(g=p-(f-a)/d)>=s&&g<=l),n||!(16&r)||16&o||(n=(_=f-(p-s)*d)>=a&&_<=h)}return n}(o,t,e)})))}function cn(t,e,i,n,o){return!!(un(t,e,i,n,o)||ln(t,e,i,n,o[0],o[1])||ln(t,e,i,n,o[0],o[3])||ln(t,e,i,n,o[2],o[1])||ln(t,e,i,n,o[2],o[3]))}function pn(t,e,i,n,o){if(!cn(t,e,i[0],n,o))return!1;if(1===i.length)return!0;for(var r=1,s=i.length;r<s;++r)if(an(t,i[r-1],i[r],n,o)&&!un(t,i[r-1],i[r],n,o))return!1;return!0}function fn(t,e,i,n){for(var o=t[e],r=t[e+1],s=0,a=e+n;a<i;a+=n){var l=t[a],h=t[a+1];s+=Math.sqrt((l-o)*(l-o)+(h-r)*(h-r)),o=l,r=h}return s}var dn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),gn=function(t){function e(e,i){var n=t.call(this)||this;return n.flatMidpoint_=null,n.flatMidpointRevision_=-1,n.maxDelta_=-1,n.maxDeltaRevision_=-1,void 0===i||Array.isArray(e[0])?n.setCoordinates(e,i):n.setFlatCoordinates(i,e),n}return dn(e,t),e.prototype.appendCoordinate=function(t){this.flatCoordinates?l(this.flatCoordinates,t):this.flatCoordinates=t.slice(),this.changed()},e.prototype.clone=function(){var t=new e(this.flatCoordinates.slice(),this.layout);return t.applyProperties(this),t},e.prototype.closestPointXY=function(t,e,i,n){return n<pe(this.getExtent(),t,e)?n:(this.maxDeltaRevision_!=this.getRevision()&&(this.maxDelta_=Math.sqrt(Bi(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,0)),this.maxDeltaRevision_=this.getRevision()),Zi(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,this.maxDelta_,!1,t,e,i,n))},e.prototype.forEachSegment=function(t){return tn(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t)},e.prototype.getCoordinateAtM=function(t,e){if(this.layout!=Oi&&this.layout!=Ri)return null;var i=void 0!==e&&e;return sn(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t,i)},e.prototype.getCoordinates=function(){return en(this.flatCoordinates,0,this.flatCoordinates.length,this.stride)},e.prototype.getCoordinateAt=function(t,e){return rn(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t,e,this.stride)},e.prototype.getLength=function(){return fn(this.flatCoordinates,0,this.flatCoordinates.length,this.stride)},e.prototype.getFlatMidpoint=function(){return this.flatMidpointRevision_!=this.getRevision()&&(this.flatMidpoint_=this.getCoordinateAt(.5,this.flatMidpoint_),this.flatMidpointRevision_=this.getRevision()),this.flatMidpoint_},e.prototype.getSimplifiedGeometryInternal=function(t){var i=[];return i.length=qi(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t,i,0),new e(i,bi)},e.prototype.getType=function(){return"LineString"},e.prototype.intersectsExtent=function(t){return un(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t)},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,1),this.flatCoordinates||(this.flatCoordinates=[]),this.flatCoordinates.length=Ui(this.flatCoordinates,0,t,this.stride),this.changed()},e}(Ni),_n=gn;function yn(t,e,i,n){for(var o=0,r=t[i-n],s=t[i-n+1];e<i;e+=n){var a=t[e],l=t[e+1];o+=s*a-r*l,r=a,s=l}return o/2}function vn(t,e,i,n){for(var o=0,r=0,s=i.length;r<s;++r){var a=i[r];o+=yn(t,e,a,n),e=a}return o}var mn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),xn=function(t){function e(e,i){var n=t.call(this)||this;return n.maxDelta_=-1,n.maxDeltaRevision_=-1,void 0===i||Array.isArray(e[0])?n.setCoordinates(e,i):n.setFlatCoordinates(i,e),n}return mn(e,t),e.prototype.clone=function(){return new e(this.flatCoordinates.slice(),this.layout)},e.prototype.closestPointXY=function(t,e,i,n){return n<pe(this.getExtent(),t,e)?n:(this.maxDeltaRevision_!=this.getRevision()&&(this.maxDelta_=Math.sqrt(Bi(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,0)),this.maxDeltaRevision_=this.getRevision()),Zi(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,this.maxDelta_,!0,t,e,i,n))},e.prototype.getArea=function(){return yn(this.flatCoordinates,0,this.flatCoordinates.length,this.stride)},e.prototype.getCoordinates=function(){return en(this.flatCoordinates,0,this.flatCoordinates.length,this.stride)},e.prototype.getSimplifiedGeometryInternal=function(t){var i=[];return i.length=qi(this.flatCoordinates,0,this.flatCoordinates.length,this.stride,t,i,0),new e(i,bi)},e.prototype.getType=function(){return"LinearRing"},e.prototype.intersectsExtent=function(t){return!1},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,1),this.flatCoordinates||(this.flatCoordinates=[]),this.flatCoordinates.length=Ui(this.flatCoordinates,0,t,this.stride),this.changed()},e}(Ni),Cn=xn,wn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Sn=function(t){function e(e,i,n){var o=t.call(this)||this;if(o.ends_=[],o.maxDelta_=-1,o.maxDeltaRevision_=-1,Array.isArray(e[0]))o.setCoordinates(e,i);else if(void 0!==i&&n)o.setFlatCoordinates(i,e),o.ends_=n;else{for(var r=o.getLayout(),s=e,a=[],h=[],u=0,c=s.length;u<c;++u){var p=s[u];0===u&&(r=p.getLayout()),l(a,p.getFlatCoordinates()),h.push(a.length)}o.setFlatCoordinates(r,a),o.ends_=h}return o}return wn(e,t),e.prototype.appendLineString=function(t){this.flatCoordinates?l(this.flatCoordinates,t.getFlatCoordinates().slice()):this.flatCoordinates=t.getFlatCoordinates().slice(),this.ends_.push(this.flatCoordinates.length),this.changed()},e.prototype.clone=function(){var t=new e(this.flatCoordinates.slice(),this.layout,this.ends_.slice());return t.applyProperties(this),t},e.prototype.closestPointXY=function(t,e,i,n){return n<pe(this.getExtent(),t,e)?n:(this.maxDeltaRevision_!=this.getRevision()&&(this.maxDelta_=Math.sqrt(Ki(this.flatCoordinates,0,this.ends_,this.stride,0)),this.maxDeltaRevision_=this.getRevision()),Vi(this.flatCoordinates,0,this.ends_,this.stride,this.maxDelta_,!1,t,e,i,n))},e.prototype.getCoordinateAtM=function(t,e,i){if(this.layout!=Oi&&this.layout!=Ri||0===this.flatCoordinates.length)return null;var n=void 0!==e&&e,o=void 0!==i&&i;return function(t,e,i,n,o,r,s){if(s)return sn(t,e,i[i.length-1],n,o,r);var a;if(o<t[n-1])return r?((a=t.slice(0,n))[n-1]=o,a):null;if(t[t.length-1]<o)return r?((a=t.slice(t.length-n))[n-1]=o,a):null;for(var l=0,h=i.length;l<h;++l){var u=i[l];if(e!=u){if(o<t[e+n-1])return null;if(o<=t[u-1])return sn(t,e,u,n,o,!1);e=u}}return null}(this.flatCoordinates,0,this.ends_,this.stride,t,n,o)},e.prototype.getCoordinates=function(){return nn(this.flatCoordinates,0,this.ends_,this.stride)},e.prototype.getEnds=function(){return this.ends_},e.prototype.getLineString=function(t){return t<0||this.ends_.length<=t?null:new _n(this.flatCoordinates.slice(0===t?0:this.ends_[t-1],this.ends_[t]),this.layout)},e.prototype.getLineStrings=function(){for(var t=this.flatCoordinates,e=this.ends_,i=this.layout,n=[],o=0,r=0,s=e.length;r<s;++r){var a=e[r],l=new _n(t.slice(o,a),i);n.push(l),o=a}return n},e.prototype.getFlatMidpoints=function(){for(var t=[],e=this.flatCoordinates,i=0,n=this.ends_,o=this.stride,r=0,s=n.length;r<s;++r){var a=n[r];l(t,rn(e,i,a,o,.5)),i=a}return t},e.prototype.getSimplifiedGeometryInternal=function(t){var i=[],n=[];return i.length=function(t,e,i,n,o,r,s,a){for(var l=0,h=i.length;l<h;++l){var u=i[l];s=qi(t,e,u,n,o,r,s),a.push(s),e=u}return s}(this.flatCoordinates,0,this.ends_,this.stride,t,i,0,n),new e(i,bi,n)},e.prototype.getType=function(){return"MultiLineString"},e.prototype.intersectsExtent=function(t){return function(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r){if(un(t,e,i[r],n,o))return!0;e=i[r]}return!1}(this.flatCoordinates,0,this.ends_,this.stride,t)},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,2),this.flatCoordinates||(this.flatCoordinates=[]);var i=Hi(this.flatCoordinates,0,t,this.stride,this.ends_);this.flatCoordinates.length=0===i.length?0:i[i.length-1],this.changed()},e}(Ni),En=Sn,Tn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),bn=function(t){function e(e,i){var n=t.call(this)||this;return n.setCoordinates(e,i),n}return Tn(e,t),e.prototype.clone=function(){var t=new e(this.flatCoordinates.slice(),this.layout);return t.applyProperties(this),t},e.prototype.closestPointXY=function(t,e,i,n){var o=this.flatCoordinates,r=Tt(t,e,o[0],o[1]);if(r<n){for(var s=this.stride,a=0;a<s;++a)i[a]=o[a];return i.length=s,r}return n},e.prototype.getCoordinates=function(){return this.flatCoordinates?this.flatCoordinates.slice():[]},e.prototype.computeExtent=function(t){return i=t,ye(n=(e=this.flatCoordinates)[0],o=e[1],n,o,i);var e,i,n,o},e.prototype.getType=function(){return"Point"},e.prototype.intersectsExtent=function(t){return ge(t,this.flatCoordinates[0],this.flatCoordinates[1])},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,0),this.flatCoordinates||(this.flatCoordinates=[]),this.flatCoordinates.length=function(t,e,i,n){for(var o=0,r=i.length;o<r;++o)t[e++]=i[o];return e}(this.flatCoordinates,0,t,this.stride),this.changed()},e}(Ni),On=bn,Rn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Pn=function(t){function e(e,i){var n=t.call(this)||this;return i&&!Array.isArray(e[0])?n.setFlatCoordinates(i,e):n.setCoordinates(e,i),n}return Rn(e,t),e.prototype.appendPoint=function(t){this.flatCoordinates?l(this.flatCoordinates,t.getFlatCoordinates()):this.flatCoordinates=t.getFlatCoordinates().slice(),this.changed()},e.prototype.clone=function(){var t=new e(this.flatCoordinates.slice(),this.layout);return t.applyProperties(this),t},e.prototype.closestPointXY=function(t,e,i,n){if(n<pe(this.getExtent(),t,e))return n;for(var o=this.flatCoordinates,r=this.stride,s=0,a=o.length;s<a;s+=r){var l=Tt(t,e,o[s],o[s+1]);if(l<n){n=l;for(var h=0;h<r;++h)i[h]=o[s+h];i.length=r}}return n},e.prototype.getCoordinates=function(){return en(this.flatCoordinates,0,this.flatCoordinates.length,this.stride)},e.prototype.getPoint=function(t){var e=this.flatCoordinates?this.flatCoordinates.length/this.stride:0;return t<0||e<=t?null:new On(this.flatCoordinates.slice(t*this.stride,(t+1)*this.stride),this.layout)},e.prototype.getPoints=function(){for(var t=this.flatCoordinates,e=this.layout,i=this.stride,n=[],o=0,r=t.length;o<r;o+=i){var s=new On(t.slice(o,o+i),e);n.push(s)}return n},e.prototype.getType=function(){return"MultiPoint"},e.prototype.intersectsExtent=function(t){for(var e=this.flatCoordinates,i=this.stride,n=0,o=e.length;n<o;n+=i)if(ge(t,e[n],e[n+1]))return!0;return!1},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,1),this.flatCoordinates||(this.flatCoordinates=[]),this.flatCoordinates.length=Ui(this.flatCoordinates,0,t,this.stride),this.changed()},e}(Ni),In=Pn;function Mn(t,e,i,n,o,s,a){for(var l,h,u,c,p,f,d,g=o[s+1],_=[],y=0,v=i.length;y<v;++y){var m=i[y];for(c=t[m-n],f=t[m-n+1],l=e;l<m;l+=n)p=t[l],d=t[l+1],(g<=f&&d<=g||f<=g&&g<=d)&&(u=(g-f)/(d-f)*(p-c)+c,_.push(u)),c=p,f=d}var x=NaN,C=-1/0;for(_.sort(r),c=_[0],l=1,h=_.length;l<h;++l){p=_[l];var w=Math.abs(p-c);w>C&&hn(t,e,i,n,u=(c+p)/2,g)&&(x=u,C=w),c=p}return isNaN(x)&&(x=o[s]),a?(a.push(x,g,C),a):[x,g,C]}function Fn(t,e,i,n){for(;e<i-n;){for(var o=0;o<n;++o){var r=t[e+o];t[e+o]=t[i-n+o],t[i-n+o]=r}e+=n,i-=n}}function Ln(t,e,i,n){for(var o=0,r=t[i-n],s=t[i-n+1];e<i;e+=n){var a=t[e],l=t[e+1];o+=(a-r)*(l+s),r=a,s=l}return 0===o?void 0:o>0}function An(t,e,i,n,o){for(var r=void 0!==o&&o,s=0,a=i.length;s<a;++s){var l=i[s],h=Ln(t,e,l,n);if(0===s){if(r&&h||!r&&!h)return!1}else if(r&&!h||!r&&h)return!1;e=l}return!0}function Dn(t,e,i,n,o){for(var r=void 0!==o&&o,s=0,a=i.length;s<a;++s){var l=i[s],h=Ln(t,e,l,n);(0===s?r&&h||!r&&!h:r&&!h||!r&&h)&&Fn(t,e,l,n),e=l}return e}function kn(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r)e=Dn(t,e,i[r],n,o);return e}var jn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Gn=function(t){function e(e,i,n){var o=t.call(this)||this;return o.ends_=[],o.flatInteriorPointRevision_=-1,o.flatInteriorPoint_=null,o.maxDelta_=-1,o.maxDeltaRevision_=-1,o.orientedRevision_=-1,o.orientedFlatCoordinates_=null,void 0!==i&&n?(o.setFlatCoordinates(i,e),o.ends_=n):o.setCoordinates(e,i),o}return jn(e,t),e.prototype.appendLinearRing=function(t){this.flatCoordinates?l(this.flatCoordinates,t.getFlatCoordinates()):this.flatCoordinates=t.getFlatCoordinates().slice(),this.ends_.push(this.flatCoordinates.length),this.changed()},e.prototype.clone=function(){var t=new e(this.flatCoordinates.slice(),this.layout,this.ends_.slice());return t.applyProperties(this),t},e.prototype.closestPointXY=function(t,e,i,n){return n<pe(this.getExtent(),t,e)?n:(this.maxDeltaRevision_!=this.getRevision()&&(this.maxDelta_=Math.sqrt(Ki(this.flatCoordinates,0,this.ends_,this.stride,0)),this.maxDeltaRevision_=this.getRevision()),Vi(this.flatCoordinates,0,this.ends_,this.stride,this.maxDelta_,!0,t,e,i,n))},e.prototype.containsXY=function(t,e){return hn(this.getOrientedFlatCoordinates(),0,this.ends_,this.stride,t,e)},e.prototype.getArea=function(){return vn(this.getOrientedFlatCoordinates(),0,this.ends_,this.stride)},e.prototype.getCoordinates=function(t){var e;return void 0!==t?Dn(e=this.getOrientedFlatCoordinates().slice(),0,this.ends_,this.stride,t):e=this.flatCoordinates,nn(e,0,this.ends_,this.stride)},e.prototype.getEnds=function(){return this.ends_},e.prototype.getFlatInteriorPoint=function(){if(this.flatInteriorPointRevision_!=this.getRevision()){var t=Re(this.getExtent());this.flatInteriorPoint_=Mn(this.getOrientedFlatCoordinates(),0,this.ends_,this.stride,t,0),this.flatInteriorPointRevision_=this.getRevision()}return this.flatInteriorPoint_},e.prototype.getInteriorPoint=function(){return new On(this.getFlatInteriorPoint(),Oi)},e.prototype.getLinearRingCount=function(){return this.ends_.length},e.prototype.getLinearRing=function(t){return t<0||this.ends_.length<=t?null:new Cn(this.flatCoordinates.slice(0===t?0:this.ends_[t-1],this.ends_[t]),this.layout)},e.prototype.getLinearRings=function(){for(var t=this.layout,e=this.flatCoordinates,i=this.ends_,n=[],o=0,r=0,s=i.length;r<s;++r){var a=i[r],l=new Cn(e.slice(o,a),t);n.push(l),o=a}return n},e.prototype.getOrientedFlatCoordinates=function(){if(this.orientedRevision_!=this.getRevision()){var t=this.flatCoordinates;An(t,0,this.ends_,this.stride)?this.orientedFlatCoordinates_=t:(this.orientedFlatCoordinates_=t.slice(),this.orientedFlatCoordinates_.length=Dn(this.orientedFlatCoordinates_,0,this.ends_,this.stride)),this.orientedRevision_=this.getRevision()}return this.orientedFlatCoordinates_},e.prototype.getSimplifiedGeometryInternal=function(t){var i=[],n=[];return i.length=$i(this.flatCoordinates,0,this.ends_,this.stride,Math.sqrt(t),i,0,n),new e(i,bi,n)},e.prototype.getType=function(){return"Polygon"},e.prototype.intersectsExtent=function(t){return pn(this.getOrientedFlatCoordinates(),0,this.ends_,this.stride,t)},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,2),this.flatCoordinates||(this.flatCoordinates=[]);var i=Hi(this.flatCoordinates,0,t,this.stride,this.ends_);this.flatCoordinates.length=0===i.length?0:i[i.length-1],this.changed()},e}(Ni),zn=Gn;function Wn(t){var e=t[0],i=t[1],n=t[2],o=t[3],r=[e,i,e,o,n,o,n,i,e,i];return new Gn(r,bi,[r.length])}var Xn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Nn=function(t){function e(e,i,n){var o=t.call(this)||this;if(o.endss_=[],o.flatInteriorPointsRevision_=-1,o.flatInteriorPoints_=null,o.maxDelta_=-1,o.maxDeltaRevision_=-1,o.orientedRevision_=-1,o.orientedFlatCoordinates_=null,!n&&!Array.isArray(e[0])){for(var r=o.getLayout(),s=e,a=[],h=[],u=0,c=s.length;u<c;++u){var p=s[u];0===u&&(r=p.getLayout());for(var f=a.length,d=p.getEnds(),g=0,_=d.length;g<_;++g)d[g]+=f;l(a,p.getFlatCoordinates()),h.push(d)}i=r,e=a,n=h}return void 0!==i&&n?(o.setFlatCoordinates(i,e),o.endss_=n):o.setCoordinates(e,i),o}return Xn(e,t),e.prototype.appendPolygon=function(t){var e;if(this.flatCoordinates){var i=this.flatCoordinates.length;l(this.flatCoordinates,t.getFlatCoordinates());for(var n=0,o=(e=t.getEnds().slice()).length;n<o;++n)e[n]+=i}else this.flatCoordinates=t.getFlatCoordinates().slice(),e=t.getEnds().slice(),this.endss_.push();this.endss_.push(e),this.changed()},e.prototype.clone=function(){for(var t=this.endss_.length,i=new Array(t),n=0;n<t;++n)i[n]=this.endss_[n].slice();var o=new e(this.flatCoordinates.slice(),this.layout,i);return o.applyProperties(this),o},e.prototype.closestPointXY=function(t,e,i,n){return n<pe(this.getExtent(),t,e)?n:(this.maxDeltaRevision_!=this.getRevision()&&(this.maxDelta_=Math.sqrt(function(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r){var a=i[r];o=Ki(t,e,a,n,o),e=a[a.length-1]}return o}(this.flatCoordinates,0,this.endss_,this.stride,0)),this.maxDeltaRevision_=this.getRevision()),function(t,e,i,n,o,r,s,a,l,h,u){for(var c=[NaN,NaN],p=0,f=i.length;p<f;++p){var d=i[p];h=Vi(t,e,d,n,o,true,s,a,l,h,c),e=d[d.length-1]}return h}(this.getOrientedFlatCoordinates(),0,this.endss_,this.stride,this.maxDelta_,0,t,e,i,n))},e.prototype.containsXY=function(t,e){return function(t,e,i,n,o,r){if(0===i.length)return!1;for(var s=0,a=i.length;s<a;++s){var l=i[s];if(hn(t,e,l,n,o,r))return!0;e=l[l.length-1]}return!1}(this.getOrientedFlatCoordinates(),0,this.endss_,this.stride,t,e)},e.prototype.getArea=function(){return function(t,e,i,n){for(var o=0,r=0,s=i.length;r<s;++r){var a=i[r];o+=vn(t,e,a,n),e=a[a.length-1]}return o}(this.getOrientedFlatCoordinates(),0,this.endss_,this.stride)},e.prototype.getCoordinates=function(t){var e;return void 0!==t?kn(e=this.getOrientedFlatCoordinates().slice(),0,this.endss_,this.stride,t):e=this.flatCoordinates,on(e,0,this.endss_,this.stride)},e.prototype.getEndss=function(){return this.endss_},e.prototype.getFlatInteriorPoints=function(){if(this.flatInteriorPointsRevision_!=this.getRevision()){var t=function(t,e,i,n){for(var o=[],r=[1/0,1/0,-1/0,-1/0],s=0,a=i.length;s<a;++s){var l=i[s];r=me(t,e,l[0],n),o.push((r[0]+r[2])/2,(r[1]+r[3])/2),e=l[l.length-1]}return o}(this.flatCoordinates,0,this.endss_,this.stride);this.flatInteriorPoints_=function(t,e,i,n,o){for(var r=[],s=0,a=i.length;s<a;++s){var l=i[s];r=Mn(t,e,l,n,o,2*s,r),e=l[l.length-1]}return r}(this.getOrientedFlatCoordinates(),0,this.endss_,this.stride,t),this.flatInteriorPointsRevision_=this.getRevision()}return this.flatInteriorPoints_},e.prototype.getInteriorPoints=function(){return new In(this.getFlatInteriorPoints().slice(),Oi)},e.prototype.getOrientedFlatCoordinates=function(){if(this.orientedRevision_!=this.getRevision()){var t=this.flatCoordinates;!function(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r){var a=i[r];if(!An(t,e,a,n,undefined))return!1;a.length&&(e=a[a.length-1])}return!0}(t,0,this.endss_,this.stride)?(this.orientedFlatCoordinates_=t.slice(),this.orientedFlatCoordinates_.length=kn(this.orientedFlatCoordinates_,0,this.endss_,this.stride)):this.orientedFlatCoordinates_=t,this.orientedRevision_=this.getRevision()}return this.orientedFlatCoordinates_},e.prototype.getSimplifiedGeometryInternal=function(t){var i=[],n=[];return i.length=function(t,e,i,n,o,r,s,a){for(var l=0,h=i.length;l<h;++l){var u=i[l],c=[];s=$i(t,e,u,n,o,r,s,c),a.push(c),e=u[u.length-1]}return s}(this.flatCoordinates,0,this.endss_,this.stride,Math.sqrt(t),i,0,n),new e(i,bi,n)},e.prototype.getPolygon=function(t){if(t<0||this.endss_.length<=t)return null;var e;if(0===t)e=0;else{var i=this.endss_[t-1];e=i[i.length-1]}var n=this.endss_[t].slice(),o=n[n.length-1];if(0!==e)for(var r=0,s=n.length;r<s;++r)n[r]-=e;return new zn(this.flatCoordinates.slice(e,o),this.layout,n)},e.prototype.getPolygons=function(){for(var t=this.layout,e=this.flatCoordinates,i=this.endss_,n=[],o=0,r=0,s=i.length;r<s;++r){var a=i[r].slice(),l=a[a.length-1];if(0!==o)for(var h=0,u=a.length;h<u;++h)a[h]-=o;var c=new zn(e.slice(o,l),t,a);n.push(c),o=l}return n},e.prototype.getType=function(){return"MultiPolygon"},e.prototype.intersectsExtent=function(t){return function(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r){var a=i[r];if(pn(t,e,a,n,o))return!0;e=a[a.length-1]}return!1}(this.getOrientedFlatCoordinates(),0,this.endss_,this.stride,t)},e.prototype.setCoordinates=function(t,e){this.setLayout(e,t,3),this.flatCoordinates||(this.flatCoordinates=[]);var i=function(t,e,i,n,o){for(var r=o||[],s=0,a=0,l=i.length;a<l;++a){var h=Hi(t,e,i[a],n,r[s]);r[s++]=h,e=h[h.length-1]}return r.length=s,r}(this.flatCoordinates,0,t,this.stride,this.endss_);if(0===i.length)this.flatCoordinates.length=0;else{var n=i[i.length-1];this.flatCoordinates.length=0===n.length?0:n[n.length-1]}this.changed()},e}(Ni),Yn=Nn,Bn="preload",Kn="useInterimTilesOnError",Zn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Vn=function(t){function e(e){var i=this,n=e||{},o=f({},n);return delete o.preload,delete o.useInterimTilesOnError,(i=t.call(this,o)||this).on,i.once,i.un,i.setPreload(void 0!==n.preload?n.preload:0),i.setUseInterimTilesOnError(void 0===n.useInterimTilesOnError||n.useInterimTilesOnError),i}return Zn(e,t),e.prototype.getPreload=function(){return this.get(Bn)},e.prototype.setPreload=function(t){this.set(Bn,t)},e.prototype.getUseInterimTilesOnError=function(){return this.get(Kn)},e.prototype.setUseInterimTilesOnError=function(t){this.set(Kn,t)},e.prototype.getData=function(e){return t.prototype.getData.call(this,e)},e}(Gt),Un=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Hn=function(t){function e(e){var i=t.call(this)||this;return i.ready=!0,i.boundHandleImageChange_=i.handleImageChange_.bind(i),i.layer_=e,i.declutterExecutorGroup=null,i}return Un(e,t),e.prototype.getFeatures=function(t){return L()},e.prototype.getData=function(t){return null},e.prototype.prepareFrame=function(t){return L()},e.prototype.renderFrame=function(t,e){return L()},e.prototype.loadedTileCallback=function(t,e,i){t[e]||(t[e]={}),t[e][i.tileCoord.toString()]=i},e.prototype.createLoadedTileFinder=function(t,e,i){return function(n,o){var r=this.loadedTileCallback.bind(this,i,n);return t.forEachLoadedTile(e,n,o,r)}.bind(this)},e.prototype.forEachFeatureAtCoordinate=function(t,e,i,n,o){},e.prototype.getDataAtPixel=function(t,e,i){return null},e.prototype.getLayer=function(){return this.layer_},e.prototype.handleFontsChanged=function(){},e.prototype.handleImageChange_=function(t){2===t.target.getState()&&this.renderIfReadyAndVisible()},e.prototype.loadImage=function(t){var e=t.getState();return 2!=e&&3!=e&&t.addEventListener(x,this.boundHandleImageChange_),0==e&&(t.load(),e=t.getState()),2==e},e.prototype.renderIfReadyAndVisible=function(){var t=this.getLayer();t&&t.getVisible()&&"ready"===t.getSourceState()&&t.changed()},e.prototype.disposeInternal=function(){delete this.layer_,t.prototype.disposeInternal.call(this)},e}(F),qn=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Jn=function(t){function e(e,i,n,o){var r=t.call(this,e)||this;return r.inversePixelTransform=i,r.frameState=n,r.context=o,r}return qn(e,t),e}(t),Qn=/^#([a-f0-9]{3}|[a-f0-9]{4}(?:[a-f0-9]{2}){0,2})$/i,$n=/^([a-z]*)$|^hsla?\(.*\)$/i;function to(t){return"string"==typeof t?t:oo(t)}var eo=function(){var t={},e=0;return function(i){var n;if(t.hasOwnProperty(i))n=t[i];else{if(e>=1024){var o=0;for(var r in t)0==(3&o++)&&(delete t[r],--e)}n=function(t){var e,i,n,o,r;if($n.exec(t)&&(t=function(t){var e=document.createElement("div");if(e.style.color=t,""!==e.style.color){document.body.appendChild(e);var i=getComputedStyle(e).color;return document.body.removeChild(e),i}return""}(t)),Qn.exec(t)){var s,a=t.length-1;s=a<=4?1:2;var l=4===a||8===a;e=parseInt(t.substr(1+0*s,s),16),i=parseInt(t.substr(1+1*s,s),16),n=parseInt(t.substr(1+2*s,s),16),o=l?parseInt(t.substr(1+3*s,s),16):255,1==s&&(e=(e<<4)+e,i=(i<<4)+i,n=(n<<4)+n,l&&(o=(o<<4)+o)),r=[e,i,n,o/255]}else 0==t.indexOf("rgba(")?no(r=t.slice(5,-1).split(",").map(Number)):0==t.indexOf("rgb(")?((r=t.slice(4,-1).split(",").map(Number)).push(1),no(r)):xt(!1,14);return r}(i),t[i]=n,++e}return n}}();function io(t){return Array.isArray(t)?t:eo(t)}function no(t){return t[0]=Ct(t[0]+.5|0,0,255),t[1]=Ct(t[1]+.5|0,0,255),t[2]=Ct(t[2]+.5|0,0,255),t[3]=Ct(t[3],0,1),t}function oo(t){var e=t[0];e!=(0|e)&&(e=e+.5|0);var i=t[1];i!=(0|i)&&(i=i+.5|0);var n=t[2];return n!=(0|n)&&(n=n+.5|0),"rgba("+e+","+i+","+n+","+(void 0===t[3]?1:Math.round(100*t[3])/100)+")"}var ro=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),so=[],ao=null,lo=function(t){function e(e){var i=t.call(this,e)||this;return i.container=null,i.renderedResolution,i.tempTransform=[1,0,0,1,0,0],i.pixelTransform=[1,0,0,1,0,0],i.inversePixelTransform=[1,0,0,1,0,0],i.context=null,i.containerReused=!1,i.pixelContext_=null,i.frameState=null,i}return ro(e,t),e.prototype.getImageData=function(t,e,i){var n,o;ao||((n=document.createElement("canvas")).width=1,n.height=1,ao=n.getContext("2d")),ao.clearRect(0,0,1,1);try{ao.drawImage(t,e,i,1,1,0,0,1,1),o=ao.getImageData(0,0,1,1).data}catch(t){return ao=null,null}return o},e.prototype.getBackground=function(t){var e=this.getLayer().getBackground();return"function"==typeof e&&(e=e(t.viewState.resolution)),e||void 0},e.prototype.useContainer=function(t,e,i){var n,o,r=this.getLayer().getClassName();if(t&&t.className===r&&(!i||t&&t.style.backgroundColor&&h(io(t.style.backgroundColor),io(i)))&&(a=t.firstElementChild)instanceof HTMLCanvasElement&&(o=a.getContext("2d")),o&&o.canvas.style.transform===e?(this.container=t,this.context=o,this.containerReused=!0):this.containerReused&&(this.container=null,this.context=null,this.containerReused=!1),!this.container){(n=document.createElement("div")).className=r;var s=n.style;s.position="absolute",s.width="100%",s.height="100%";var a=(o=q()).canvas;n.appendChild(a),(s=a.style).position="absolute",s.left="0",s.transformOrigin="top left",this.container=n,this.context=o}this.containerReused||!i||this.container.style.backgroundColor||(this.container.style.backgroundColor=i)},e.prototype.clipUnrotated=function(t,e,i){var n=Ae(i),o=De(i),r=Oe(i),s=be(i);Pi(e.coordinateToPixelTransform,n),Pi(e.coordinateToPixelTransform,o),Pi(e.coordinateToPixelTransform,r),Pi(e.coordinateToPixelTransform,s);var a=this.inversePixelTransform;Pi(a,n),Pi(a,o),Pi(a,r),Pi(a,s),t.save(),t.beginPath(),t.moveTo(Math.round(n[0]),Math.round(n[1])),t.lineTo(Math.round(o[0]),Math.round(o[1])),t.lineTo(Math.round(r[0]),Math.round(r[1])),t.lineTo(Math.round(s[0]),Math.round(s[1])),t.clip()},e.prototype.dispatchRenderEvent_=function(t,e,i){var n=this.getLayer();if(n.hasListener(t)){var o=new Jn(t,this.inversePixelTransform,i,e);n.dispatchEvent(o)}},e.prototype.preRender=function(t,e){this.frameState=e,this.dispatchRenderEvent_("prerender",t,e)},e.prototype.postRender=function(t,e){this.dispatchRenderEvent_("postrender",t,e)},e.prototype.getRenderTransform=function(t,e,i,n,o,r,s){var a=o/2,l=r/2,h=n/e,u=-h,c=-t[0]+s,p=-t[1];return Ii(this.tempTransform,a,l,h,u,-i,c,p)},e.prototype.getDataAtPixel=function(t,e,i){var n=Pi(this.inversePixelTransform,t.slice()),o=this.context,r=this.getLayer().getExtent();if(r&&!fe(r,Pi(e.pixelToCoordinateTransform,t.slice())))return null;var s,a=Math.round(n[0]),l=Math.round(n[1]),h=this.pixelContext_;if(!h){var u=document.createElement("canvas");u.width=1,u.height=1,h=u.getContext("2d"),this.pixelContext_=h}h.clearRect(0,0,1,1);try{h.drawImage(o.canvas,a,l,1,1,0,0,1,1),s=h.getImageData(0,0,1,1).data}catch(t){return"SecurityError"===t.name?(this.pixelContext_=null,new Uint8Array):s}return 0===s[3]?null:s},e.prototype.disposeInternal=function(){delete this.frameState,t.prototype.disposeInternal.call(this)},e}(Hn),ho=lo,uo=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),co=function(t){function e(e,i,n){var o=t.call(this)||this,r=n||{};return o.tileCoord=e,o.state=i,o.interimTile=null,o.key="",o.transition_=void 0===r.transition?250:r.transition,o.transitionStarts_={},o.interpolate=!!r.interpolate,o}return uo(e,t),e.prototype.changed=function(){this.dispatchEvent(x)},e.prototype.release=function(){},e.prototype.getKey=function(){return this.key+"/"+this.tileCoord},e.prototype.getInterimTile=function(){if(!this.interimTile)return this;var t=this.interimTile;do{if(2==t.getState())return this.transition_=0,t;t=t.interimTile}while(t);return this},e.prototype.refreshInterimChain=function(){if(this.interimTile){var t=this.interimTile,e=this;do{if(2==t.getState()){t.interimTile=null;break}1==t.getState()?e=t:0==t.getState()?e.interimTile=t.interimTile:e=t,t=e.interimTile}while(t)}},e.prototype.getTileCoord=function(){return this.tileCoord},e.prototype.getState=function(){return this.state},e.prototype.setState=function(t){if(3!==this.state&&this.state>t)throw new Error("Tile load sequence violation");this.state=t,this.changed()},e.prototype.load=function(){L()},e.prototype.getAlpha=function(t,e){if(!this.transition_)return 1;var i=this.transitionStarts_[t];if(i){if(-1===i)return 1}else i=e,this.transitionStarts_[t]=i;var n=e-i+1e3/60;return n>=this.transition_?1:mi(n/this.transition_)},e.prototype.inTransition=function(t){return!!this.transition_&&-1!==this.transitionStarts_[t]},e.prototype.endTransition=function(t){this.transition_&&(this.transitionStarts_[t]=-1)},e}(m),po=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),fo=function(t){function e(e,i,n,o){var r=t.call(this)||this;return r.extent=e,r.pixelRatio_=n,r.resolution=i,r.state=o,r}return po(e,t),e.prototype.changed=function(){this.dispatchEvent(x)},e.prototype.getExtent=function(){return this.extent},e.prototype.getImage=function(){return L()},e.prototype.getPixelRatio=function(){return this.pixelRatio_},e.prototype.getResolution=function(){return this.resolution},e.prototype.getState=function(){return this.state},e.prototype.load=function(){L()},e}(m),go=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function _o(t,e,i){var n=t,o=!0,r=!1,s=!1,a=[R(n,"load",(function(){s=!0,r||e()}))];return n.src&&U?(r=!0,n.decode().then((function(){o&&e()})).catch((function(t){o&&(s?e():i())}))):a.push(R(n,"error",i)),function(){o=!1,a.forEach(P)}}!function(t){function e(e,i,n,o,r,s){var a=t.call(this,e,i,n,0)||this;return a.src_=o,a.image_=new Image,null!==r&&(a.image_.crossOrigin=r),a.unlisten_=null,a.state=0,a.imageLoadFunction_=s,a}go(e,t),e.prototype.getImage=function(){return this.image_},e.prototype.handleImageError_=function(){this.state=3,this.unlistenImage_(),this.changed()},e.prototype.handleImageLoad_=function(){void 0===this.resolution&&(this.resolution=Fe(this.extent)/this.image_.height),this.state=2,this.unlistenImage_(),this.changed()},e.prototype.load=function(){0!=this.state&&3!=this.state||(this.state=1,this.changed(),this.imageLoadFunction_(this,this.src_),this.unlisten_=_o(this.image_,this.handleImageLoad_.bind(this),this.handleImageError_.bind(this)))},e.prototype.setImage=function(t){this.image_=t,this.resolution=Fe(this.extent)/this.image_.height},e.prototype.unlistenImage_=function(){this.unlisten_&&(this.unlisten_(),this.unlisten_=null)}}(fo);var yo,vo=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),mo=function(t){function e(e,i,n,o,r,s){var a=t.call(this,e,i,s)||this;return a.crossOrigin_=o,a.src_=n,a.key=n,a.image_=new Image,null!==o&&(a.image_.crossOrigin=o),a.unlisten_=null,a.tileLoadFunction_=r,a}return vo(e,t),e.prototype.getImage=function(){return this.image_},e.prototype.setImage=function(t){this.image_=t,this.state=2,this.unlistenImage_(),this.changed()},e.prototype.handleImageError_=function(){var t;this.state=3,this.unlistenImage_(),this.image_=((t=q(1,1)).fillStyle="rgba(0,0,0,0)",t.fillRect(0,0,1,1),t.canvas),this.changed()},e.prototype.handleImageLoad_=function(){var t=this.image_;t.naturalWidth&&t.naturalHeight?this.state=2:this.state=4,this.unlistenImage_(),this.changed()},e.prototype.load=function(){3==this.state&&(this.state=0,this.image_=new Image,null!==this.crossOrigin_&&(this.image_.crossOrigin=this.crossOrigin_)),0==this.state&&(this.state=1,this.changed(),this.tileLoadFunction_(this,this.src_),this.unlisten_=_o(this.image_,this.handleImageLoad_.bind(this),this.handleImageError_.bind(this)))},e.prototype.unlistenImage_=function(){this.unlisten_&&(this.unlisten_(),this.unlisten_=null)},e}(co),xo=function(){function t(t,e,i,n,o,r){this.sourceProj_=t,this.targetProj_=e;var s={},a=ei(this.targetProj_,this.sourceProj_);this.transformInv_=function(t){var e=t[0]+"/"+t[1];return s[e]||(s[e]=a(t)),s[e]},this.maxSourceExtent_=n,this.errorThresholdSquared_=o*o,this.triangles_=[],this.wrapsXInSource_=!1,this.canWrapXInSource_=this.sourceProj_.canWrapX()&&!!n&&!!this.sourceProj_.getExtent()&&ke(n)==ke(this.sourceProj_.getExtent()),this.sourceWorldWidth_=this.sourceProj_.getExtent()?ke(this.sourceProj_.getExtent()):null,this.targetWorldWidth_=this.targetProj_.getExtent()?ke(this.targetProj_.getExtent()):null;var l=Ae(i),h=De(i),u=Oe(i),c=be(i),p=this.transformInv_(l),f=this.transformInv_(h),d=this.transformInv_(u),g=this.transformInv_(c),_=10+(r?Math.max(0,Math.ceil(St(Te(i)/(r*r*256*256)))):0);if(this.addQuad_(l,h,u,c,p,f,d,g,_),this.wrapsXInSource_){var y=1/0;this.triangles_.forEach((function(t,e,i){y=Math.min(y,t.source[0][0],t.source[1][0],t.source[2][0])})),this.triangles_.forEach(function(t){if(Math.max(t.source[0][0],t.source[1][0],t.source[2][0])-y>this.sourceWorldWidth_/2){var e=[[t.source[0][0],t.source[0][1]],[t.source[1][0],t.source[1][1]],[t.source[2][0],t.source[2][1]]];e[0][0]-y>this.sourceWorldWidth_/2&&(e[0][0]-=this.sourceWorldWidth_),e[1][0]-y>this.sourceWorldWidth_/2&&(e[1][0]-=this.sourceWorldWidth_),e[2][0]-y>this.sourceWorldWidth_/2&&(e[2][0]-=this.sourceWorldWidth_);var i=Math.min(e[0][0],e[1][0],e[2][0]);Math.max(e[0][0],e[1][0],e[2][0])-i<this.sourceWorldWidth_/2&&(t.source=e)}}.bind(this))}s={}}return t.prototype.addTriangle_=function(t,e,i,n,o,r){this.triangles_.push({source:[n,o,r],target:[t,e,i]})},t.prototype.addQuad_=function(t,e,i,n,o,r,s,a,l){var h=he([o,r,s,a]),u=this.sourceWorldWidth_?ke(h)/this.sourceWorldWidth_:null,c=this.sourceWorldWidth_,p=this.sourceProj_.canWrapX()&&u>.5&&u<1,f=!1;if(l>0&&(this.targetProj_.isGlobal()&&this.targetWorldWidth_&&(f=ke(he([t,e,i,n]))/this.targetWorldWidth_>.25||f),!p&&this.sourceProj_.isGlobal()&&u&&(f=u>.25||f)),!(!f&&this.maxSourceExtent_&&isFinite(h[0])&&isFinite(h[1])&&isFinite(h[2])&&isFinite(h[3]))||je(h,this.maxSourceExtent_)){var d=0;if(!(f||isFinite(o[0])&&isFinite(o[1])&&isFinite(r[0])&&isFinite(r[1])&&isFinite(s[0])&&isFinite(s[1])&&isFinite(a[0])&&isFinite(a[1])))if(l>0)f=!0;else if(1!=(d=(isFinite(o[0])&&isFinite(o[1])?0:8)+(isFinite(r[0])&&isFinite(r[1])?0:4)+(isFinite(s[0])&&isFinite(s[1])?0:2)+(isFinite(a[0])&&isFinite(a[1])?0:1))&&2!=d&&4!=d&&8!=d)return;if(l>0){if(!f){var g=[(t[0]+i[0])/2,(t[1]+i[1])/2],_=this.transformInv_(g),y=void 0;y=p?(Ot(o[0],c)+Ot(s[0],c))/2-Ot(_[0],c):(o[0]+s[0])/2-_[0];var v=(o[1]+s[1])/2-_[1];f=y*y+v*v>this.errorThresholdSquared_}if(f){if(Math.abs(t[0]-i[0])<=Math.abs(t[1]-i[1])){var m=[(e[0]+i[0])/2,(e[1]+i[1])/2],x=this.transformInv_(m),C=[(n[0]+t[0])/2,(n[1]+t[1])/2],w=this.transformInv_(C);this.addQuad_(t,e,m,C,o,r,x,w,l-1),this.addQuad_(C,m,i,n,w,x,s,a,l-1)}else{var S=[(t[0]+e[0])/2,(t[1]+e[1])/2],E=this.transformInv_(S),T=[(i[0]+n[0])/2,(i[1]+n[1])/2],b=this.transformInv_(T);this.addQuad_(t,S,T,n,o,E,b,a,l-1),this.addQuad_(S,e,i,T,E,r,s,b,l-1)}return}}if(p){if(!this.canWrapXInSource_)return;this.wrapsXInSource_=!0}0==(11&d)&&this.addTriangle_(t,i,n,o,s,a),0==(14&d)&&this.addTriangle_(t,i,e,o,s,r),d&&(0==(13&d)&&this.addTriangle_(e,n,t,r,a,o),0==(7&d)&&this.addTriangle_(e,n,i,r,a,s))}},t.prototype.calculateSourceExtent=function(){var t=[1/0,1/0,-1/0,-1/0];return this.triangles_.forEach((function(e,i,n){var o=e.source;Ce(t,o[0]),Ce(t,o[1]),Ce(t,o[2])})),t},t.prototype.getTriangles=function(){return this.triangles_},t}(),Co={imageSmoothingEnabled:!1,msImageSmoothingEnabled:!1},wo={imageSmoothingEnabled:!0,msImageSmoothingEnabled:!0},So=[];function Eo(t,e,i,n,o){t.beginPath(),t.moveTo(0,0),t.lineTo(e,i),t.lineTo(n,o),t.closePath(),t.save(),t.clip(),t.fillRect(0,0,Math.max(e,n)+1,Math.max(i,o)),t.restore()}function To(t,e){return Math.abs(t[4*e]-210)>2||Math.abs(t[4*e+3]-191.25)>2}function bo(t,e,i,n){var o=ii(i,e,t),r=qe(e,n,i),s=e.getMetersPerUnit();void 0!==s&&(r*=s);var a=t.getMetersPerUnit();void 0!==a&&(r/=a);var l=t.getExtent();if(!l||fe(l,o)){var h=qe(t,r,o)/r;isFinite(h)&&h>0&&(r/=h)}return r}var Oo=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ro=function(t){function e(e,i,n,o,r,s,a,l,h,u,c,p){var f=t.call(this,r,0,{interpolate:!!p})||this;f.renderEdges_=void 0!==c&&c,f.pixelRatio_=a,f.gutter_=l,f.canvas_=null,f.sourceTileGrid_=i,f.targetTileGrid_=o,f.wrappedTileCoord_=s||r,f.sourceTiles_=[],f.sourcesListenerKeys_=null,f.sourceZ_=0;var d=o.getTileCoordExtent(f.wrappedTileCoord_),g=f.targetTileGrid_.getExtent(),_=f.sourceTileGrid_.getExtent(),y=g?Le(d,g):d;if(0===Te(y))return f.state=4,f;var v=e.getExtent();v&&(_=_?Le(_,v):v);var m=o.getResolution(f.wrappedTileCoord_[0]),x=function(t,e,i,n){var o=Re(i),r=bo(t,e,o,n);return(!isFinite(r)||r<=0)&&Ee(i,(function(i){return r=bo(t,e,i,n),isFinite(r)&&r>0})),r}(e,n,y,m);if(!isFinite(x)||x<=0)return f.state=4,f;var C=void 0!==u?u:.5;if(f.triangulation_=new xo(e,n,y,_,x*C,m),0===f.triangulation_.getTriangles().length)return f.state=4,f;f.sourceZ_=i.getZForResolution(x);var w=f.triangulation_.calculateSourceExtent();if(_&&(e.canWrapX()?(w[1]=Ct(w[1],_[1],_[3]),w[3]=Ct(w[3],_[1],_[3])):w=Le(w,_)),Te(w)){for(var S=i.getTileRangeForExtentAndZ(w,f.sourceZ_),E=S.minX;E<=S.maxX;E++)for(var T=S.minY;T<=S.maxY;T++){var b=h(f.sourceZ_,E,T,a);b&&f.sourceTiles_.push(b)}0===f.sourceTiles_.length&&(f.state=4)}else f.state=4;return f}return Oo(e,t),e.prototype.getImage=function(){return this.canvas_},e.prototype.reproject_=function(){var t=[];if(this.sourceTiles_.forEach(function(e,i,n){e&&2==e.getState()&&t.push({extent:this.sourceTileGrid_.getTileCoordExtent(e.tileCoord),image:e.getImage()})}.bind(this)),this.sourceTiles_.length=0,0===t.length)this.state=3;else{var e=this.wrappedTileCoord_[0],i=this.targetTileGrid_.getTileSize(e),n="number"==typeof i?i:i[0],o="number"==typeof i?i:i[1],r=this.targetTileGrid_.getResolution(e),s=this.sourceTileGrid_.getResolution(this.sourceZ_),a=this.targetTileGrid_.getTileCoordExtent(this.wrappedTileCoord_);this.canvas_=function(t,e,i,n,o,r,s,a,l,h,u,c){var p=q(Math.round(i*t),Math.round(i*e),So);if(c||f(p,Co),0===l.length)return p.canvas;function d(t){return Math.round(t*i)/i}p.scale(i,i),p.globalCompositeOperation="lighter";var g=[1/0,1/0,-1/0,-1/0];l.forEach((function(t,e,i){var n,o;n=g,(o=t.extent)[0]<n[0]&&(n[0]=o[0]),o[2]>n[2]&&(n[2]=o[2]),o[1]<n[1]&&(n[1]=o[1]),o[3]>n[3]&&(n[3]=o[3])}));var _=ke(g),y=Fe(g),v=q(Math.round(i*_/n),Math.round(i*y/n));c||f(v,Co);var m=i/n;l.forEach((function(t,e,i){var n=t.extent[0]-g[0],o=-(t.extent[3]-g[3]),r=ke(t.extent),s=Fe(t.extent);t.image.width>0&&t.image.height>0&&v.drawImage(t.image,h,h,t.image.width-2*h,t.image.height-2*h,n*m,o*m,r*m,s*m)}));var x=Ae(s);return a.getTriangles().forEach((function(t,e,o){var s=t.source,a=t.target,l=s[0][0],h=s[0][1],u=s[1][0],f=s[1][1],_=s[2][0],y=s[2][1],m=d((a[0][0]-x[0])/r),C=d(-(a[0][1]-x[1])/r),w=d((a[1][0]-x[0])/r),S=d(-(a[1][1]-x[1])/r),E=d((a[2][0]-x[0])/r),T=d(-(a[2][1]-x[1])/r),b=l,O=h;l=0,h=0;var R=function(t){for(var e=t.length,i=0;i<e;i++){for(var n=i,o=Math.abs(t[i][i]),r=i+1;r<e;r++){var s=Math.abs(t[r][i]);s>o&&(o=s,n=r)}if(0===o)return null;var a=t[n];t[n]=t[i],t[i]=a;for(var l=i+1;l<e;l++)for(var h=-t[l][i]/t[i][i],u=i;u<e+1;u++)i==u?t[l][u]=0:t[l][u]+=h*t[i][u]}for(var c=new Array(e),p=e-1;p>=0;p--){c[p]=t[p][e]/t[p][p];for(var f=p-1;f>=0;f--)t[f][e]-=t[f][p]*c[p]}return c}([[u-=b,f-=O,0,0,w-m],[_-=b,y-=O,0,0,E-m],[0,0,u,f,S-C],[0,0,_,y,T-C]]);if(R){if(p.save(),p.beginPath(),function(){if(void 0===yo){var t=document.createElement("canvas").getContext("2d");t.globalCompositeOperation="lighter",t.fillStyle="rgba(210, 0, 0, 0.75)",Eo(t,4,5,4,0),Eo(t,4,5,0,5);var e=t.getImageData(0,0,3,3).data;yo=To(e,0)||To(e,4)||To(e,8)}return yo}()||!c){p.moveTo(w,S);for(var P=m-w,I=C-S,M=0;M<4;M++)p.lineTo(w+d((M+1)*P/4),S+d(M*I/3)),3!=M&&p.lineTo(w+d((M+1)*P/4),S+d((M+1)*I/3));p.lineTo(E,T)}else p.moveTo(w,S),p.lineTo(m,C),p.lineTo(E,T);p.clip(),p.transform(R[0],R[2],R[1],R[3],m,C),p.translate(g[0]-b,g[3]-O),p.scale(n/i,-n/i),p.drawImage(v.canvas,0,0),p.restore()}})),u&&(p.save(),p.globalCompositeOperation="source-over",p.strokeStyle="black",p.lineWidth=1,a.getTriangles().forEach((function(t,e,i){var n=t.target,o=(n[0][0]-x[0])/r,s=-(n[0][1]-x[1])/r,a=(n[1][0]-x[0])/r,l=-(n[1][1]-x[1])/r,h=(n[2][0]-x[0])/r,u=-(n[2][1]-x[1])/r;p.beginPath(),p.moveTo(a,l),p.lineTo(o,s),p.lineTo(h,u),p.closePath(),p.stroke()})),p.restore()),p.canvas}(n,o,this.pixelRatio_,s,this.sourceTileGrid_.getExtent(),r,a,this.triangulation_,t,this.gutter_,this.renderEdges_,this.interpolate),this.state=2}this.changed()},e.prototype.load=function(){if(0==this.state){this.state=1,this.changed();var t=0;this.sourcesListenerKeys_=[],this.sourceTiles_.forEach(function(e,i,n){var o=e.getState();if(0==o||1==o){t++;var r=O(e,x,(function(i){var n=e.getState();2!=n&&3!=n&&4!=n||(P(r),0==--t&&(this.unlistenSources_(),this.reproject_()))}),this);this.sourcesListenerKeys_.push(r)}}.bind(this)),0===t?setTimeout(this.reproject_.bind(this),0):this.sourceTiles_.forEach((function(t,e,i){0==t.getState()&&t.load()}))}},e.prototype.unlistenSources_=function(){this.sourcesListenerKeys_.forEach(P),this.sourcesListenerKeys_=null},e.prototype.release=function(){this.canvas_&&(J(this.canvas_.getContext("2d")),So.push(this.canvas_),this.canvas_=null),t.prototype.release.call(this)},e}(co),Po=function(){function t(t,e,i,n){this.minX=t,this.maxX=e,this.minY=i,this.maxY=n}return t.prototype.contains=function(t){return this.containsXY(t[1],t[2])},t.prototype.containsTileRange=function(t){return this.minX<=t.minX&&t.maxX<=this.maxX&&this.minY<=t.minY&&t.maxY<=this.maxY},t.prototype.containsXY=function(t,e){return this.minX<=t&&t<=this.maxX&&this.minY<=e&&e<=this.maxY},t.prototype.equals=function(t){return this.minX==t.minX&&this.minY==t.minY&&this.maxX==t.maxX&&this.maxY==t.maxY},t.prototype.extend=function(t){t.minX<this.minX&&(this.minX=t.minX),t.maxX>this.maxX&&(this.maxX=t.maxX),t.minY<this.minY&&(this.minY=t.minY),t.maxY>this.maxY&&(this.maxY=t.maxY)},t.prototype.getHeight=function(){return this.maxY-this.minY+1},t.prototype.getSize=function(){return[this.getWidth(),this.getHeight()]},t.prototype.getWidth=function(){return this.maxX-this.minX+1},t.prototype.intersects=function(t){return this.minX<=t.maxX&&this.maxX>=t.minX&&this.minY<=t.maxY&&this.maxY>=t.minY},t}();function Io(t,e,i,n,o){return void 0!==o?(o.minX=t,o.maxX=e,o.minY=i,o.maxY=n,o):new Po(t,e,i,n)}var Mo=Po;function Fo(t){return t[0]>0&&t[1]>0}function Lo(t,e){return Array.isArray(t)?t:(void 0===e?e=[t,t]:(e[0]=t,e[1]=t),e)}var Ao=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Do=function(t){function e(e){var i=t.call(this,e)||this;return i.extentChanged=!0,i.renderedExtent_=null,i.renderedPixelRatio,i.renderedProjection=null,i.renderedRevision,i.renderedTiles=[],i.newTiles_=!1,i.tmpExtent=[1/0,1/0,-1/0,-1/0],i.tmpTileRange_=new Mo(0,0,0,0),i}return Ao(e,t),e.prototype.isDrawableTile=function(t){var e=this.getLayer(),i=t.getState(),n=e.getUseInterimTilesOnError();return 2==i||4==i||3==i&&!n},e.prototype.getTile=function(t,e,i,n){var o=n.pixelRatio,r=n.viewState.projection,s=this.getLayer(),a=s.getSource().getTile(t,e,i,o,r);return 3==a.getState()&&(s.getUseInterimTilesOnError()?s.getPreload()>0&&(this.newTiles_=!0):a.setState(2)),this.isDrawableTile(a)||(a=a.getInterimTile()),a},e.prototype.getData=function(t){var e=this.frameState;if(!e)return null;var i=this.getLayer(),n=Pi(e.pixelToCoordinateTransform,t.slice()),o=i.getExtent();if(o&&!fe(o,n))return null;for(var r=e.pixelRatio,s=e.viewState.projection,a=e.viewState,l=i.getRenderSource(),h=l.getTileGridForProjection(a.projection),u=l.getTilePixelRatio(e.pixelRatio),c=h.getZForResolution(a.resolution);c>=h.getMinZoom();--c){var p=h.getTileCoordForCoordAndZ(n,c),f=l.getTile(c,p[1],p[2],r,s);if(!(f instanceof mo||f instanceof Ro))return null;if(2===f.getState()){var d=h.getOrigin(c),g=Lo(h.getTileSize(c)),_=h.getResolution(c),y=Math.floor(u*((n[0]-d[0])/_-p[1]*g[0])),v=Math.floor(u*((d[1]-n[1])/_-p[2]*g[1])),m=Math.round(u*l.getGutterForProjection(a.projection));return this.getImageData(f.getImage(),y+m,v+m)}}return null},e.prototype.loadedTileCallback=function(e,i,n){return!!this.isDrawableTile(n)&&t.prototype.loadedTileCallback.call(this,e,i,n)},e.prototype.prepareFrame=function(t){return!!this.getLayer().getSource()},e.prototype.renderFrame=function(t,e){var i=t.layerStatesArray[t.layerIndex],n=t.viewState,o=n.projection,s=n.resolution,a=n.center,l=n.rotation,h=t.pixelRatio,u=this.getLayer(),c=u.getSource(),p=c.getRevision(),d=c.getTileGridForProjection(o),g=d.getZForResolution(s,c.zDirection),_=d.getResolution(g),y=t.extent,v=t.viewState.resolution,m=c.getTilePixelRatio(h),x=Math.round(ke(y)/v*h),C=Math.round(Fe(y)/v*h),w=i.extent&&pi(i.extent);w&&(y=Le(y,pi(i.extent)));var S=_*x/2/m,E=_*C/2/m,T=[a[0]-S,a[1]-E,a[0]+S,a[1]+E],b=d.getTileRangeForExtentAndZ(y,g),O={};O[g]={};var R=this.createLoadedTileFinder(c,o,O),P=this.tmpExtent,I=this.tmpTileRange_;this.newTiles_=!1;for(var M=l?Me(n.center,v,l,t.size):void 0,F=b.minX;F<=b.maxX;++F)for(var L=b.minY;L<=b.maxY;++L)if(!l||d.tileCoordIntersectsViewport([g,F,L],M)){var A=this.getTile(g,F,L,t);if(this.isDrawableTile(A)){var k=D(this);if(2==A.getState()&&(O[g][A.tileCoord.toString()]=A,(rt=A.inTransition(k))&&1!==i.opacity&&(A.endTransition(k),rt=!1),this.newTiles_||!rt&&-1!==this.renderedTiles.indexOf(A)||(this.newTiles_=!0)),1===A.getAlpha(k,t.time))continue}var j=d.getTileCoordChildTileRange(A.tileCoord,I,P),G=!1;j&&(G=R(g+1,j)),G||d.forEachTileCoordParentTileRange(A.tileCoord,R,I,P)}var z=_/s*h/m;Ii(this.pixelTransform,t.size[0]/2,t.size[1]/2,1/h,1/h,l,-x/2,-C/2);var W=Fi(this.pixelTransform);this.useContainer(e,W,this.getBackground(t));var X=this.context,N=X.canvas;Mi(this.inversePixelTransform,this.pixelTransform),Ii(this.tempTransform,x/2,C/2,z,z,0,-x/2,-C/2),N.width!=x||N.height!=C?(N.width=x,N.height=C):this.containerReused||X.clearRect(0,0,x,C),w&&this.clipUnrotated(X,t,w),c.getInterpolate()||f(X,Co),this.preRender(X,t),this.renderedTiles.length=0;var Y,B,K,Z=Object.keys(O).map(Number);Z.sort(r),1!==i.opacity||this.containerReused&&!c.getOpaque(t.viewState.projection)?(Y=[],B=[]):Z=Z.reverse();for(var V=Z.length-1;V>=0;--V){var U=Z[V],H=c.getTilePixelSize(U,h,o),q=d.getResolution(U)/_,J=H[0]*q*z,Q=H[1]*q*z,$=d.getTileCoordForCoordAndZ(Ae(T),U),tt=d.getTileCoordExtent($),et=Pi(this.tempTransform,[m*(tt[0]-T[0])/_,m*(T[3]-tt[3])/_]),it=m*c.getGutterForProjection(o),nt=O[U];for(var ot in nt){var rt,st=(A=nt[ot]).tileCoord,at=$[1]-st[1],lt=Math.round(et[0]-(at-1)*J),ht=$[2]-st[2],ut=Math.round(et[1]-(ht-1)*Q),ct=lt-(F=Math.round(et[0]-at*J)),pt=ut-(L=Math.round(et[1]-ht*Q)),ft=g===U,dt=!1;if(!(rt=ft&&1!==A.getAlpha(D(this),t.time)))if(Y){K=[F,L,F+ct,L,F+ct,L+pt,F,L+pt];for(var gt=0,_t=Y.length;gt<_t;++gt)if(g!==U&&U<B[gt]){var yt=Y[gt];je([F,L,F+ct,L+pt],[yt[0],yt[3],yt[4],yt[7]])&&(dt||(X.save(),dt=!0),X.beginPath(),X.moveTo(K[0],K[1]),X.lineTo(K[2],K[3]),X.lineTo(K[4],K[5]),X.lineTo(K[6],K[7]),X.moveTo(yt[6],yt[7]),X.lineTo(yt[4],yt[5]),X.lineTo(yt[2],yt[3]),X.lineTo(yt[0],yt[1]),X.clip())}Y.push(K),B.push(U)}else X.clearRect(F,L,ct,pt);this.drawTileImage(A,t,F,L,ct,pt,it,ft),Y&&!rt?(dt&&X.restore(),this.renderedTiles.unshift(A)):this.renderedTiles.push(A),this.updateUsedTiles(t.usedTiles,c,A)}}return this.renderedRevision=p,this.renderedResolution=_,this.extentChanged=!this.renderedExtent_||!xe(this.renderedExtent_,T),this.renderedExtent_=T,this.renderedPixelRatio=h,this.renderedProjection=o,this.manageTilePyramid(t,c,d,h,o,y,g,u.getPreload()),this.scheduleExpireCache(t,c),this.postRender(X,t),i.extent&&X.restore(),f(X,wo),W!==N.style.transform&&(N.style.transform=W),this.container},e.prototype.drawTileImage=function(t,e,i,n,o,r,s,a){var l=this.getTileImage(t);if(l){var h=D(this),u=e.layerStatesArray[e.layerIndex],c=u.opacity*(a?t.getAlpha(h,e.time):1),p=c!==this.context.globalAlpha;p&&(this.context.save(),this.context.globalAlpha=c),this.context.drawImage(l,s,s,l.width-2*s,l.height-2*s,i,n,o,r),p&&this.context.restore(),c!==u.opacity?e.animate=!0:a&&t.endTransition(h)}},e.prototype.getImage=function(){var t=this.context;return t?t.canvas:null},e.prototype.getTileImage=function(t){return t.getImage()},e.prototype.scheduleExpireCache=function(t,e){if(e.canExpireCache()){var i=function(t,e,i){var n=D(t);n in i.usedTiles&&t.expireCache(i.viewState.projection,i.usedTiles[n])}.bind(null,e);t.postRenderFunctions.push(i)}},e.prototype.updateUsedTiles=function(t,e,i){var n=D(e);n in t||(t[n]={}),t[n][i.getKey()]=!0},e.prototype.manageTilePyramid=function(t,e,i,n,o,r,s,a,l){var h=D(e);h in t.wantedTiles||(t.wantedTiles[h]={});var u,c,p,f,d,g,_=t.wantedTiles[h],y=t.tileQueue,v=i.getMinZoom(),m=t.viewState.rotation,x=m?Me(t.viewState.center,t.viewState.resolution,m,t.size):void 0,C=0;for(g=v;g<=s;++g)for(c=i.getTileRangeForExtentAndZ(r,g,c),p=i.getResolution(g),f=c.minX;f<=c.maxX;++f)for(d=c.minY;d<=c.maxY;++d)m&&!i.tileCoordIntersectsViewport([g,f,d],x)||(s-g<=a?(++C,0==(u=e.getTile(g,f,d,n,o)).getState()&&(_[u.getKey()]=!0,y.isKeyQueued(u.getKey())||y.enqueue([u,h,i.getTileCoordCenter(u.tileCoord),p])),void 0!==l&&l(u)):e.useTile(g,f,d,o));e.updateCacheSize(C,o)},e}(ho),ko=Do,jo=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Go=function(t){function e(e){return t.call(this,e)||this}return jo(e,t),e.prototype.createRenderer=function(){return new ko(this)},e}(Vn),zo=i(582),Wo=function(){function t(t){this.opacity_=t.opacity,this.rotateWithView_=t.rotateWithView,this.rotation_=t.rotation,this.scale_=t.scale,this.scaleArray_=Lo(t.scale),this.displacement_=t.displacement,this.declutterMode_=t.declutterMode}return t.prototype.clone=function(){var e=this.getScale();return new t({opacity:this.getOpacity(),scale:Array.isArray(e)?e.slice():e,rotation:this.getRotation(),rotateWithView:this.getRotateWithView(),displacement:this.getDisplacement().slice(),declutterMode:this.getDeclutterMode()})},t.prototype.getOpacity=function(){return this.opacity_},t.prototype.getRotateWithView=function(){return this.rotateWithView_},t.prototype.getRotation=function(){return this.rotation_},t.prototype.getScale=function(){return this.scale_},t.prototype.getScaleArray=function(){return this.scaleArray_},t.prototype.getDisplacement=function(){return this.displacement_},t.prototype.getDeclutterMode=function(){return this.declutterMode_},t.prototype.getAnchor=function(){return L()},t.prototype.getImage=function(t){return L()},t.prototype.getHitDetectionImage=function(){return L()},t.prototype.getPixelRatio=function(t){return 1},t.prototype.getImageState=function(){return L()},t.prototype.getImageSize=function(){return L()},t.prototype.getOrigin=function(){return L()},t.prototype.getSize=function(){return L()},t.prototype.setDisplacement=function(t){this.displacement_=t},t.prototype.setOpacity=function(t){this.opacity_=t},t.prototype.setRotateWithView=function(t){this.rotateWithView_=t},t.prototype.setRotation=function(t){this.rotation_=t},t.prototype.setScale=function(t){this.scale_=t,this.scaleArray_=Lo(t)},t.prototype.listenImageChange=function(t){L()},t.prototype.load=function(){L()},t.prototype.unlistenImageChange=function(t){L()},t}();function Xo(t){return Array.isArray(t)?oo(t):t}var No="10px sans-serif",Yo="#000",Bo="round",Ko=[],Zo="round",Vo="#000",Uo="center",Ho="middle",qo=[0,0,0,0],Jo=new G;(new m).setSize=function(){console.warn("labelCache is deprecated.")};var Qo,$o,tr=null,er={},ir=function(){var t,e,i="32px ",n=["monospace","serif"],o=n.length,r="wmytzilWMYTZIL@#/&?$%10";function s(t,s,a){for(var l=!0,h=0;h<o;++h){var u=n[h];if(e=rr(t+" "+s+" "+i+u,r),a!=u){var c=rr(t+" "+s+" "+i+a+","+u,r);l=l&&c!=e}}return!!l}function a(){for(var e=!0,i=Jo.getKeys(),n=0,o=i.length;n<o;++n){var r=i[n];Jo.get(r)<100&&(s.apply(this,r.split("\n"))?(d(er),tr=null,Qo=void 0,Jo.set(r,100)):(Jo.set(r,Jo.get(r)+1,!0),e=!1))}e&&(clearInterval(t),t=void 0)}return function(e){var i=lt(e);if(i)for(var n=i.families,o=0,r=n.length;o<r;++o){var l=n[o],h=i.style+"\n"+i.weight+"\n"+l;void 0===Jo.get(h)&&(Jo.set(h,100,!0),s(i.style,i.weight,l)||(Jo.set(h,0,!0),void 0===t&&(t=setInterval(a,32))))}}}(),nr=function(t){var e=er[t];if(null==e){if(V){var i=lt(t),n=or(t,"Žg");e=(isNaN(Number(i.lineHeight))?1.2:Number(i.lineHeight))*(n.actualBoundingBoxAscent+n.actualBoundingBoxDescent)}else $o||(($o=document.createElement("div")).innerHTML="M",$o.style.minHeight="0",$o.style.maxHeight="none",$o.style.height="auto",$o.style.padding="0",$o.style.border="none",$o.style.position="absolute",$o.style.display="block",$o.style.left="-99999px"),$o.style.font=t,document.body.appendChild($o),e=$o.offsetHeight,document.body.removeChild($o);er[t]=e}return e};function or(t,e){return tr||(tr=q(1,1)),t!=Qo&&(tr.font=t,Qo=tr.font),tr.measureText(e)}function rr(t,e){return or(t,e).width}function sr(t,e,i){if(e in i)return i[e];var n=e.split("\n").reduce((function(e,i){return Math.max(e,rr(t,i))}),0);return i[e]=n,n}var ar=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),lr=function(t){function e(e){var i=this,n=void 0!==e.rotateWithView&&e.rotateWithView;return(i=t.call(this,{opacity:1,rotateWithView:n,rotation:void 0!==e.rotation?e.rotation:0,scale:void 0!==e.scale?e.scale:1,displacement:void 0!==e.displacement?e.displacement:[0,0],declutterMode:e.declutterMode})||this).canvas_=void 0,i.hitDetectionCanvas_=null,i.fill_=void 0!==e.fill?e.fill:null,i.origin_=[0,0],i.points_=e.points,i.radius_=void 0!==e.radius?e.radius:e.radius1,i.radius2_=e.radius2,i.angle_=void 0!==e.angle?e.angle:0,i.stroke_=void 0!==e.stroke?e.stroke:null,i.size_=null,i.renderOptions_=null,i.render(),i}return ar(e,t),e.prototype.clone=function(){var t=this.getScale(),i=new e({fill:this.getFill()?this.getFill().clone():void 0,points:this.getPoints(),radius:this.getRadius(),radius2:this.getRadius2(),angle:this.getAngle(),stroke:this.getStroke()?this.getStroke().clone():void 0,rotation:this.getRotation(),rotateWithView:this.getRotateWithView(),scale:Array.isArray(t)?t.slice():t,displacement:this.getDisplacement().slice(),declutterMode:this.getDeclutterMode()});return i.setOpacity(this.getOpacity()),i},e.prototype.getAnchor=function(){var t=this.size_;if(!t)return null;var e=this.getDisplacement();return[t[0]/2-e[0],t[1]/2+e[1]]},e.prototype.getAngle=function(){return this.angle_},e.prototype.getFill=function(){return this.fill_},e.prototype.setFill=function(t){this.fill_=t,this.render()},e.prototype.getHitDetectionImage=function(){return this.hitDetectionCanvas_||this.createHitDetectionCanvas_(this.renderOptions_),this.hitDetectionCanvas_},e.prototype.getImage=function(t){var e=this.canvas_[t];if(!e){var i=this.renderOptions_,n=q(i.size*t,i.size*t);this.draw_(i,n,t),e=n.canvas,this.canvas_[t]=e}return e},e.prototype.getPixelRatio=function(t){return t},e.prototype.getImageSize=function(){return this.size_},e.prototype.getImageState=function(){return 2},e.prototype.getOrigin=function(){return this.origin_},e.prototype.getPoints=function(){return this.points_},e.prototype.getRadius=function(){return this.radius_},e.prototype.getRadius2=function(){return this.radius2_},e.prototype.getSize=function(){return this.size_},e.prototype.getStroke=function(){return this.stroke_},e.prototype.setStroke=function(t){this.stroke_=t,this.render()},e.prototype.listenImageChange=function(t){},e.prototype.load=function(){},e.prototype.unlistenImageChange=function(t){},e.prototype.calculateLineJoinSize_=function(t,e,i){if(0===e||this.points_===1/0||"bevel"!==t&&"miter"!==t)return e;var n=this.radius_,o=void 0===this.radius2_?n:this.radius2_;if(n<o){var r=n;n=o,o=r}var s=void 0===this.radius2_?this.points_:2*this.points_,a=2*Math.PI/s,l=o*Math.sin(a),h=n-Math.sqrt(o*o-l*l),u=Math.sqrt(l*l+h*h),c=u/l;if("miter"===t&&c<=i)return c*e;var p=e/2/c,f=e/2*(h/u),d=Math.sqrt((n+p)*(n+p)+f*f)-n;if(void 0===this.radius2_||"bevel"===t)return 2*d;var g=n*Math.sin(a),_=o-Math.sqrt(n*n-g*g),y=Math.sqrt(g*g+_*_)/g;if(y<=i){var v=y*e/2-o-n;return 2*Math.max(d,v)}return 2*d},e.prototype.createRenderOptions=function(){var t,e=Zo,i=0,n=null,o=0,r=0;this.stroke_&&(null===(t=this.stroke_.getColor())&&(t=Vo),t=Xo(t),void 0===(r=this.stroke_.getWidth())&&(r=1),n=this.stroke_.getLineDash(),o=this.stroke_.getLineDashOffset(),void 0===(e=this.stroke_.getLineJoin())&&(e=Zo),void 0===(i=this.stroke_.getMiterLimit())&&(i=10));var s=this.calculateLineJoinSize_(e,r,i),a=Math.max(this.radius_,this.radius2_||0);return{strokeStyle:t,strokeWidth:r,size:Math.ceil(2*a+s),lineDash:n,lineDashOffset:o,lineJoin:e,miterLimit:i}},e.prototype.render=function(){this.renderOptions_=this.createRenderOptions();var t=this.renderOptions_.size;this.canvas_={},this.size_=[t,t]},e.prototype.draw_=function(t,e,i){if(e.scale(i,i),e.translate(t.size/2,t.size/2),this.createPath_(e),this.fill_){var n=this.fill_.getColor();null===n&&(n=Yo),e.fillStyle=Xo(n),e.fill()}this.stroke_&&(e.strokeStyle=t.strokeStyle,e.lineWidth=t.strokeWidth,e.setLineDash&&t.lineDash&&(e.setLineDash(t.lineDash),e.lineDashOffset=t.lineDashOffset),e.lineJoin=t.lineJoin,e.miterLimit=t.miterLimit,e.stroke())},e.prototype.createHitDetectionCanvas_=function(t){if(this.fill_){var e=this.fill_.getColor(),i=0;if("string"==typeof e&&(e=io(e)),null===e?i=1:Array.isArray(e)&&(i=4===e.length?e[3]:1),0===i){var n=q(t.size,t.size);this.hitDetectionCanvas_=n.canvas,this.drawHitDetectionCanvas_(t,n)}}this.hitDetectionCanvas_||(this.hitDetectionCanvas_=this.getImage(1))},e.prototype.createPath_=function(t){var e=this.points_,i=this.radius_;if(e===1/0)t.arc(0,0,i,0,2*Math.PI);else{var n=void 0===this.radius2_?i:this.radius2_;void 0!==this.radius2_&&(e*=2);for(var o=this.angle_-Math.PI/2,r=2*Math.PI/e,s=0;s<e;s++){var a=o+s*r,l=s%2==0?i:n;t.lineTo(l*Math.cos(a),l*Math.sin(a))}t.closePath()}},e.prototype.drawHitDetectionCanvas_=function(t,e){e.translate(t.size/2,t.size/2),this.createPath_(e),e.fillStyle=Yo,e.fill(),this.stroke_&&(e.strokeStyle=t.strokeStyle,e.lineWidth=t.strokeWidth,t.lineDash&&(e.setLineDash(t.lineDash),e.lineDashOffset=t.lineDashOffset),e.lineJoin=t.lineJoin,e.miterLimit=t.miterLimit,e.stroke())},e}(Wo),hr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ur=function(t){function e(e){var i=e||{};return t.call(this,{points:1/0,fill:i.fill,radius:i.radius,stroke:i.stroke,scale:void 0!==i.scale?i.scale:1,rotation:void 0!==i.rotation?i.rotation:0,rotateWithView:void 0!==i.rotateWithView&&i.rotateWithView,displacement:void 0!==i.displacement?i.displacement:[0,0],declutterMode:i.declutterMode})||this}return hr(e,t),e.prototype.clone=function(){var t=this.getScale(),i=new e({fill:this.getFill()?this.getFill().clone():void 0,stroke:this.getStroke()?this.getStroke().clone():void 0,radius:this.getRadius(),scale:Array.isArray(t)?t.slice():t,rotation:this.getRotation(),rotateWithView:this.getRotateWithView(),displacement:this.getDisplacement().slice(),declutterMode:this.getDeclutterMode()});return i.setOpacity(this.getOpacity()),i},e.prototype.setRadius=function(t){this.radius_=t,this.render()},e}(lr),cr=function(){function t(t){var e=t||{};this.color_=void 0!==e.color?e.color:null}return t.prototype.clone=function(){var e=this.getColor();return new t({color:Array.isArray(e)?e.slice():e||void 0})},t.prototype.getColor=function(){return this.color_},t.prototype.setColor=function(t){this.color_=t},t}(),pr=function(){function t(t){var e=t||{};this.color_=void 0!==e.color?e.color:null,this.lineCap_=e.lineCap,this.lineDash_=void 0!==e.lineDash?e.lineDash:null,this.lineDashOffset_=e.lineDashOffset,this.lineJoin_=e.lineJoin,this.miterLimit_=e.miterLimit,this.width_=e.width}return t.prototype.clone=function(){var e=this.getColor();return new t({color:Array.isArray(e)?e.slice():e||void 0,lineCap:this.getLineCap(),lineDash:this.getLineDash()?this.getLineDash().slice():void 0,lineDashOffset:this.getLineDashOffset(),lineJoin:this.getLineJoin(),miterLimit:this.getMiterLimit(),width:this.getWidth()})},t.prototype.getColor=function(){return this.color_},t.prototype.getLineCap=function(){return this.lineCap_},t.prototype.getLineDash=function(){return this.lineDash_},t.prototype.getLineDashOffset=function(){return this.lineDashOffset_},t.prototype.getLineJoin=function(){return this.lineJoin_},t.prototype.getMiterLimit=function(){return this.miterLimit_},t.prototype.getWidth=function(){return this.width_},t.prototype.setColor=function(t){this.color_=t},t.prototype.setLineCap=function(t){this.lineCap_=t},t.prototype.setLineDash=function(t){this.lineDash_=t},t.prototype.setLineDashOffset=function(t){this.lineDashOffset_=t},t.prototype.setLineJoin=function(t){this.lineJoin_=t},t.prototype.setMiterLimit=function(t){this.miterLimit_=t},t.prototype.setWidth=function(t){this.width_=t},t}(),fr=function(){function t(t){var e=t||{};this.geometry_=null,this.geometryFunction_=_r,void 0!==e.geometry&&this.setGeometry(e.geometry),this.fill_=void 0!==e.fill?e.fill:null,this.image_=void 0!==e.image?e.image:null,this.renderer_=void 0!==e.renderer?e.renderer:null,this.hitDetectionRenderer_=void 0!==e.hitDetectionRenderer?e.hitDetectionRenderer:null,this.stroke_=void 0!==e.stroke?e.stroke:null,this.text_=void 0!==e.text?e.text:null,this.zIndex_=e.zIndex}return t.prototype.clone=function(){var e=this.getGeometry();return e&&"object"==typeof e&&(e=e.clone()),new t({geometry:e,fill:this.getFill()?this.getFill().clone():void 0,image:this.getImage()?this.getImage().clone():void 0,renderer:this.getRenderer(),stroke:this.getStroke()?this.getStroke().clone():void 0,text:this.getText()?this.getText().clone():void 0,zIndex:this.getZIndex()})},t.prototype.getRenderer=function(){return this.renderer_},t.prototype.setRenderer=function(t){this.renderer_=t},t.prototype.setHitDetectionRenderer=function(t){this.hitDetectionRenderer_=t},t.prototype.getHitDetectionRenderer=function(){return this.hitDetectionRenderer_},t.prototype.getGeometry=function(){return this.geometry_},t.prototype.getGeometryFunction=function(){return this.geometryFunction_},t.prototype.getFill=function(){return this.fill_},t.prototype.setFill=function(t){this.fill_=t},t.prototype.getImage=function(){return this.image_},t.prototype.setImage=function(t){this.image_=t},t.prototype.getStroke=function(){return this.stroke_},t.prototype.setStroke=function(t){this.stroke_=t},t.prototype.getText=function(){return this.text_},t.prototype.setText=function(t){this.text_=t},t.prototype.getZIndex=function(){return this.zIndex_},t.prototype.setGeometry=function(t){"function"==typeof t?this.geometryFunction_=t:"string"==typeof t?this.geometryFunction_=function(e){return e.get(t)}:t?void 0!==t&&(this.geometryFunction_=function(){return t}):this.geometryFunction_=_r,this.geometry_=t},t.prototype.setZIndex=function(t){this.zIndex_=t},t}(),dr=null;function gr(t,e){if(!dr){var i=new cr({color:"rgba(255,255,255,0.4)"}),n=new pr({color:"#3399CC",width:1.25});dr=[new fr({image:new ur({fill:i,stroke:n,radius:5}),fill:i,stroke:n})]}return dr}function _r(t){return t.getGeometry()}var yr=fr,vr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),mr="renderOrder",xr=function(t){function e(e){var i=this,n=e||{},o=f({},n);return delete o.style,delete o.renderBuffer,delete o.updateWhileAnimating,delete o.updateWhileInteracting,(i=t.call(this,o)||this).declutter_=void 0!==n.declutter&&n.declutter,i.renderBuffer_=void 0!==n.renderBuffer?n.renderBuffer:100,i.style_=null,i.styleFunction_=void 0,i.setStyle(n.style),i.updateWhileAnimating_=void 0!==n.updateWhileAnimating&&n.updateWhileAnimating,i.updateWhileInteracting_=void 0!==n.updateWhileInteracting&&n.updateWhileInteracting,i}return vr(e,t),e.prototype.getDeclutter=function(){return this.declutter_},e.prototype.getFeatures=function(e){return t.prototype.getFeatures.call(this,e)},e.prototype.getRenderBuffer=function(){return this.renderBuffer_},e.prototype.getRenderOrder=function(){return this.get(mr)},e.prototype.getStyle=function(){return this.style_},e.prototype.getStyleFunction=function(){return this.styleFunction_},e.prototype.getUpdateWhileAnimating=function(){return this.updateWhileAnimating_},e.prototype.getUpdateWhileInteracting=function(){return this.updateWhileInteracting_},e.prototype.renderDeclutter=function(t){t.declutterTree||(t.declutterTree=new zo(9)),this.getRenderer().renderDeclutter(t)},e.prototype.setRenderOrder=function(t){this.set(mr,t)},e.prototype.setStyle=function(t){this.style_=void 0!==t?t:gr,this.styleFunction_=null===t?void 0:function(t){var e,i;"function"==typeof t?e=t:(Array.isArray(t)?i=t:(xt("function"==typeof t.getZIndex,41),i=[t]),e=function(){return i});return e}(this.style_),this.changed()},e}(Gt),Cr={BEGIN_GEOMETRY:0,BEGIN_PATH:1,CIRCLE:2,CLOSE_PATH:3,CUSTOM:4,DRAW_CHARS:5,DRAW_IMAGE:6,END_GEOMETRY:7,FILL:8,MOVE_TO_LINE_TO:9,SET_FILL_STYLE:10,SET_STROKE_STYLE:11,STROKE:12},wr=[Cr.FILL],Sr=[Cr.STROKE],Er=[Cr.BEGIN_PATH],Tr=[Cr.CLOSE_PATH],br=Cr,Or=function(){function t(){}return t.prototype.drawCustom=function(t,e,i,n){},t.prototype.drawGeometry=function(t){},t.prototype.setStyle=function(t){},t.prototype.drawCircle=function(t,e){},t.prototype.drawFeature=function(t,e){},t.prototype.drawGeometryCollection=function(t,e){},t.prototype.drawLineString=function(t,e){},t.prototype.drawMultiLineString=function(t,e){},t.prototype.drawMultiPoint=function(t,e){},t.prototype.drawMultiPolygon=function(t,e){},t.prototype.drawPoint=function(t,e){},t.prototype.drawPolygon=function(t,e){},t.prototype.drawText=function(t,e){},t.prototype.setFillStrokeStyle=function(t,e){},t.prototype.setImageStyle=function(t,e){},t.prototype.setTextStyle=function(t,e){},t}(),Rr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Pr=function(t){function e(e,i,n,o){var r=t.call(this)||this;return r.tolerance=e,r.maxExtent=i,r.pixelRatio=o,r.maxLineWidth=0,r.resolution=n,r.beginGeometryInstruction1_=null,r.beginGeometryInstruction2_=null,r.bufferedMaxExtent_=null,r.instructions=[],r.coordinates=[],r.tmpCoordinate_=[],r.hitDetectionInstructions=[],r.state={},r}return Rr(e,t),e.prototype.applyPixelRatio=function(t){var e=this.pixelRatio;return 1==e?t:t.map((function(t){return t*e}))},e.prototype.appendFlatPointCoordinates=function(t,e){for(var i=this.getBufferedMaxExtent(),n=this.tmpCoordinate_,o=this.coordinates,r=o.length,s=0,a=t.length;s<a;s+=e)n[0]=t[s],n[1]=t[s+1],fe(i,n)&&(o[r++]=n[0],o[r++]=n[1]);return r},e.prototype.appendFlatLineCoordinates=function(t,e,i,n,o,r){var s=this.coordinates,a=s.length,l=this.getBufferedMaxExtent();r&&(e+=n);var h,u,c,p=t[e],f=t[e+1],d=this.tmpCoordinate_,g=!0;for(h=e+n;h<i;h+=n)d[0]=t[h],d[1]=t[h+1],(c=_e(l,d))!==u?(g&&(s[a++]=p,s[a++]=f,g=!1),s[a++]=d[0],s[a++]=d[1]):1===c?(s[a++]=d[0],s[a++]=d[1],g=!1):g=!0,p=d[0],f=d[1],u=c;return(o&&g||h===e+n)&&(s[a++]=p,s[a++]=f),a},e.prototype.drawCustomCoordinates_=function(t,e,i,n,o){for(var r=0,s=i.length;r<s;++r){var a=i[r],l=this.appendFlatLineCoordinates(t,e,a,n,!1,!1);o.push(l),e=a}return e},e.prototype.drawCustom=function(t,e,i,n){this.beginGeometry(t,e);var o,r,s,a,l,h=t.getType(),u=t.getStride(),c=this.coordinates.length;switch(h){case"MultiPolygon":o=t.getOrientedFlatCoordinates(),a=[];var p=t.getEndss();l=0;for(var f=0,d=p.length;f<d;++f){var g=[];l=this.drawCustomCoordinates_(o,l,p[f],u,g),a.push(g)}this.instructions.push([br.CUSTOM,c,a,t,i,on]),this.hitDetectionInstructions.push([br.CUSTOM,c,a,t,n||i,on]);break;case"Polygon":case"MultiLineString":s=[],o="Polygon"==h?t.getOrientedFlatCoordinates():t.getFlatCoordinates(),l=this.drawCustomCoordinates_(o,0,t.getEnds(),u,s),this.instructions.push([br.CUSTOM,c,s,t,i,nn]),this.hitDetectionInstructions.push([br.CUSTOM,c,s,t,n||i,nn]);break;case"LineString":case"Circle":o=t.getFlatCoordinates(),r=this.appendFlatLineCoordinates(o,0,o.length,u,!1,!1),this.instructions.push([br.CUSTOM,c,r,t,i,en]),this.hitDetectionInstructions.push([br.CUSTOM,c,r,t,n||i,en]);break;case"MultiPoint":o=t.getFlatCoordinates(),(r=this.appendFlatPointCoordinates(o,u))>c&&(this.instructions.push([br.CUSTOM,c,r,t,i,en]),this.hitDetectionInstructions.push([br.CUSTOM,c,r,t,n||i,en]));break;case"Point":o=t.getFlatCoordinates(),this.coordinates.push(o[0],o[1]),r=this.coordinates.length,this.instructions.push([br.CUSTOM,c,r,t,i]),this.hitDetectionInstructions.push([br.CUSTOM,c,r,t,n||i])}this.endGeometry(e)},e.prototype.beginGeometry=function(t,e){this.beginGeometryInstruction1_=[br.BEGIN_GEOMETRY,e,0,t],this.instructions.push(this.beginGeometryInstruction1_),this.beginGeometryInstruction2_=[br.BEGIN_GEOMETRY,e,0,t],this.hitDetectionInstructions.push(this.beginGeometryInstruction2_)},e.prototype.finish=function(){return{instructions:this.instructions,hitDetectionInstructions:this.hitDetectionInstructions,coordinates:this.coordinates}},e.prototype.reverseHitDetectionInstructions=function(){var t,e=this.hitDetectionInstructions;e.reverse();var i,n,o=e.length,r=-1;for(t=0;t<o;++t)(n=(i=e[t])[0])==br.END_GEOMETRY?r=t:n==br.BEGIN_GEOMETRY&&(i[2]=t,a(this.hitDetectionInstructions,r,t),r=-1)},e.prototype.setFillStrokeStyle=function(t,e){var i=this.state;if(t){var n=t.getColor();i.fillStyle=Xo(n||Yo)}else i.fillStyle=void 0;if(e){var o=e.getColor();i.strokeStyle=Xo(o||Vo);var r=e.getLineCap();i.lineCap=void 0!==r?r:Bo;var s=e.getLineDash();i.lineDash=s?s.slice():Ko;var a=e.getLineDashOffset();i.lineDashOffset=a||0;var l=e.getLineJoin();i.lineJoin=void 0!==l?l:Zo;var h=e.getWidth();i.lineWidth=void 0!==h?h:1;var u=e.getMiterLimit();i.miterLimit=void 0!==u?u:10,i.lineWidth>this.maxLineWidth&&(this.maxLineWidth=i.lineWidth,this.bufferedMaxExtent_=null)}else i.strokeStyle=void 0,i.lineCap=void 0,i.lineDash=null,i.lineDashOffset=void 0,i.lineJoin=void 0,i.lineWidth=void 0,i.miterLimit=void 0},e.prototype.createFill=function(t){var e=t.fillStyle,i=[br.SET_FILL_STYLE,e];return"string"!=typeof e&&i.push(!0),i},e.prototype.applyStroke=function(t){this.instructions.push(this.createStroke(t))},e.prototype.createStroke=function(t){return[br.SET_STROKE_STYLE,t.strokeStyle,t.lineWidth*this.pixelRatio,t.lineCap,t.lineJoin,t.miterLimit,this.applyPixelRatio(t.lineDash),t.lineDashOffset*this.pixelRatio]},e.prototype.updateFillStyle=function(t,e){var i=t.fillStyle;"string"==typeof i&&t.currentFillStyle==i||(void 0!==i&&this.instructions.push(e.call(this,t)),t.currentFillStyle=i)},e.prototype.updateStrokeStyle=function(t,e){var i=t.strokeStyle,n=t.lineCap,o=t.lineDash,r=t.lineDashOffset,s=t.lineJoin,a=t.lineWidth,l=t.miterLimit;(t.currentStrokeStyle!=i||t.currentLineCap!=n||o!=t.currentLineDash&&!h(t.currentLineDash,o)||t.currentLineDashOffset!=r||t.currentLineJoin!=s||t.currentLineWidth!=a||t.currentMiterLimit!=l)&&(void 0!==i&&e.call(this,t),t.currentStrokeStyle=i,t.currentLineCap=n,t.currentLineDash=o,t.currentLineDashOffset=r,t.currentLineJoin=s,t.currentLineWidth=a,t.currentMiterLimit=l)},e.prototype.endGeometry=function(t){this.beginGeometryInstruction1_[2]=this.instructions.length,this.beginGeometryInstruction1_=null,this.beginGeometryInstruction2_[2]=this.hitDetectionInstructions.length,this.beginGeometryInstruction2_=null;var e=[br.END_GEOMETRY,t];this.instructions.push(e),this.hitDetectionInstructions.push(e)},e.prototype.getBufferedMaxExtent=function(){if(!this.bufferedMaxExtent_&&(this.bufferedMaxExtent_=ce(this.maxExtent),this.maxLineWidth>0)){var t=this.resolution*(this.maxLineWidth+1)/2;ue(this.bufferedMaxExtent_,t,this.bufferedMaxExtent_)}return this.bufferedMaxExtent_},e}(Or),Ir=Pr,Mr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Fr=function(t){function e(e,i,n,o){var r=t.call(this,e,i,n,o)||this;return r.hitDetectionImage_=null,r.image_=null,r.imagePixelRatio_=void 0,r.anchorX_=void 0,r.anchorY_=void 0,r.height_=void 0,r.opacity_=void 0,r.originX_=void 0,r.originY_=void 0,r.rotateWithView_=void 0,r.rotation_=void 0,r.scale_=void 0,r.width_=void 0,r.declutterMode_=void 0,r.declutterImageWithText_=void 0,r}return Mr(e,t),e.prototype.drawPoint=function(t,e){if(this.image_){this.beginGeometry(t,e);var i=t.getFlatCoordinates(),n=t.getStride(),o=this.coordinates.length,r=this.appendFlatPointCoordinates(i,n);this.instructions.push([br.DRAW_IMAGE,o,r,this.image_,this.anchorX_*this.imagePixelRatio_,this.anchorY_*this.imagePixelRatio_,Math.ceil(this.height_*this.imagePixelRatio_),this.opacity_,this.originX_*this.imagePixelRatio_,this.originY_*this.imagePixelRatio_,this.rotateWithView_,this.rotation_,[this.scale_[0]*this.pixelRatio/this.imagePixelRatio_,this.scale_[1]*this.pixelRatio/this.imagePixelRatio_],Math.ceil(this.width_*this.imagePixelRatio_),this.declutterMode_,this.declutterImageWithText_]),this.hitDetectionInstructions.push([br.DRAW_IMAGE,o,r,this.hitDetectionImage_,this.anchorX_,this.anchorY_,this.height_,this.opacity_,this.originX_,this.originY_,this.rotateWithView_,this.rotation_,this.scale_,this.width_,this.declutterMode_,this.declutterImageWithText_]),this.endGeometry(e)}},e.prototype.drawMultiPoint=function(t,e){if(this.image_){this.beginGeometry(t,e);var i=t.getFlatCoordinates(),n=t.getStride(),o=this.coordinates.length,r=this.appendFlatPointCoordinates(i,n);this.instructions.push([br.DRAW_IMAGE,o,r,this.image_,this.anchorX_*this.imagePixelRatio_,this.anchorY_*this.imagePixelRatio_,Math.ceil(this.height_*this.imagePixelRatio_),this.opacity_,this.originX_*this.imagePixelRatio_,this.originY_*this.imagePixelRatio_,this.rotateWithView_,this.rotation_,[this.scale_[0]*this.pixelRatio/this.imagePixelRatio_,this.scale_[1]*this.pixelRatio/this.imagePixelRatio_],Math.ceil(this.width_*this.imagePixelRatio_),this.declutterMode_,this.declutterImageWithText_]),this.hitDetectionInstructions.push([br.DRAW_IMAGE,o,r,this.hitDetectionImage_,this.anchorX_,this.anchorY_,this.height_,this.opacity_,this.originX_,this.originY_,this.rotateWithView_,this.rotation_,this.scale_,this.width_,this.declutterMode_,this.declutterImageWithText_]),this.endGeometry(e)}},e.prototype.finish=function(){return this.reverseHitDetectionInstructions(),this.anchorX_=void 0,this.anchorY_=void 0,this.hitDetectionImage_=null,this.image_=null,this.imagePixelRatio_=void 0,this.height_=void 0,this.scale_=void 0,this.opacity_=void 0,this.originX_=void 0,this.originY_=void 0,this.rotateWithView_=void 0,this.rotation_=void 0,this.width_=void 0,t.prototype.finish.call(this)},e.prototype.setImageStyle=function(t,e){var i=t.getAnchor(),n=t.getSize(),o=t.getOrigin();this.imagePixelRatio_=t.getPixelRatio(this.pixelRatio),this.anchorX_=i[0],this.anchorY_=i[1],this.hitDetectionImage_=t.getHitDetectionImage(),this.image_=t.getImage(this.pixelRatio),this.height_=n[1],this.opacity_=t.getOpacity(),this.originX_=o[0],this.originY_=o[1],this.rotateWithView_=t.getRotateWithView(),this.rotation_=t.getRotation(),this.scale_=t.getScaleArray(),this.width_=n[0],this.declutterMode_=t.getDeclutterMode(),this.declutterImageWithText_=e},e}(Ir),Lr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ar=function(t){function e(e,i,n,o){return t.call(this,e,i,n,o)||this}return Lr(e,t),e.prototype.drawFlatCoordinates_=function(t,e,i,n){var o=this.coordinates.length,r=this.appendFlatLineCoordinates(t,e,i,n,!1,!1),s=[br.MOVE_TO_LINE_TO,o,r];return this.instructions.push(s),this.hitDetectionInstructions.push(s),i},e.prototype.drawLineString=function(t,e){var i=this.state,n=i.strokeStyle,o=i.lineWidth;if(void 0!==n&&void 0!==o){this.updateStrokeStyle(i,this.applyStroke),this.beginGeometry(t,e),this.hitDetectionInstructions.push([br.SET_STROKE_STYLE,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,Ko,0],Er);var r=t.getFlatCoordinates(),s=t.getStride();this.drawFlatCoordinates_(r,0,r.length,s),this.hitDetectionInstructions.push(Sr),this.endGeometry(e)}},e.prototype.drawMultiLineString=function(t,e){var i=this.state,n=i.strokeStyle,o=i.lineWidth;if(void 0!==n&&void 0!==o){this.updateStrokeStyle(i,this.applyStroke),this.beginGeometry(t,e),this.hitDetectionInstructions.push([br.SET_STROKE_STYLE,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,i.lineDashOffset],Er);for(var r=t.getEnds(),s=t.getFlatCoordinates(),a=t.getStride(),l=0,h=0,u=r.length;h<u;++h)l=this.drawFlatCoordinates_(s,l,r[h],a);this.hitDetectionInstructions.push(Sr),this.endGeometry(e)}},e.prototype.finish=function(){var e=this.state;return null!=e.lastStroke&&e.lastStroke!=this.coordinates.length&&this.instructions.push(Sr),this.reverseHitDetectionInstructions(),this.state=null,t.prototype.finish.call(this)},e.prototype.applyStroke=function(e){null!=e.lastStroke&&e.lastStroke!=this.coordinates.length&&(this.instructions.push(Sr),e.lastStroke=this.coordinates.length),e.lastStroke=0,t.prototype.applyStroke.call(this,e),this.instructions.push(Er)},e}(Ir),Dr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),kr=function(t){function e(e,i,n,o){return t.call(this,e,i,n,o)||this}return Dr(e,t),e.prototype.drawFlatCoordinatess_=function(t,e,i,n){var o=this.state,r=void 0!==o.fillStyle,s=void 0!==o.strokeStyle,a=i.length;this.instructions.push(Er),this.hitDetectionInstructions.push(Er);for(var l=0;l<a;++l){var h=i[l],u=this.coordinates.length,c=this.appendFlatLineCoordinates(t,e,h,n,!0,!s),p=[br.MOVE_TO_LINE_TO,u,c];this.instructions.push(p),this.hitDetectionInstructions.push(p),s&&(this.instructions.push(Tr),this.hitDetectionInstructions.push(Tr)),e=h}return r&&(this.instructions.push(wr),this.hitDetectionInstructions.push(wr)),s&&(this.instructions.push(Sr),this.hitDetectionInstructions.push(Sr)),e},e.prototype.drawCircle=function(t,e){var i=this.state,n=i.fillStyle,o=i.strokeStyle;if(void 0!==n||void 0!==o){this.setFillStrokeStyles_(),this.beginGeometry(t,e),void 0!==i.fillStyle&&this.hitDetectionInstructions.push([br.SET_FILL_STYLE,Yo]),void 0!==i.strokeStyle&&this.hitDetectionInstructions.push([br.SET_STROKE_STYLE,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,i.lineDashOffset]);var r=t.getFlatCoordinates(),s=t.getStride(),a=this.coordinates.length;this.appendFlatLineCoordinates(r,0,r.length,s,!1,!1);var l=[br.CIRCLE,a];this.instructions.push(Er,l),this.hitDetectionInstructions.push(Er,l),void 0!==i.fillStyle&&(this.instructions.push(wr),this.hitDetectionInstructions.push(wr)),void 0!==i.strokeStyle&&(this.instructions.push(Sr),this.hitDetectionInstructions.push(Sr)),this.endGeometry(e)}},e.prototype.drawPolygon=function(t,e){var i=this.state,n=i.fillStyle,o=i.strokeStyle;if(void 0!==n||void 0!==o){this.setFillStrokeStyles_(),this.beginGeometry(t,e),void 0!==i.fillStyle&&this.hitDetectionInstructions.push([br.SET_FILL_STYLE,Yo]),void 0!==i.strokeStyle&&this.hitDetectionInstructions.push([br.SET_STROKE_STYLE,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,i.lineDashOffset]);var r=t.getEnds(),s=t.getOrientedFlatCoordinates(),a=t.getStride();this.drawFlatCoordinatess_(s,0,r,a),this.endGeometry(e)}},e.prototype.drawMultiPolygon=function(t,e){var i=this.state,n=i.fillStyle,o=i.strokeStyle;if(void 0!==n||void 0!==o){this.setFillStrokeStyles_(),this.beginGeometry(t,e),void 0!==i.fillStyle&&this.hitDetectionInstructions.push([br.SET_FILL_STYLE,Yo]),void 0!==i.strokeStyle&&this.hitDetectionInstructions.push([br.SET_STROKE_STYLE,i.strokeStyle,i.lineWidth,i.lineCap,i.lineJoin,i.miterLimit,i.lineDash,i.lineDashOffset]);for(var r=t.getEndss(),s=t.getOrientedFlatCoordinates(),a=t.getStride(),l=0,h=0,u=r.length;h<u;++h)l=this.drawFlatCoordinatess_(s,l,r[h],a);this.endGeometry(e)}},e.prototype.finish=function(){this.reverseHitDetectionInstructions(),this.state=null;var e=this.tolerance;if(0!==e)for(var i=this.coordinates,n=0,o=i.length;n<o;++n)i[n]=Ji(i[n],e);return t.prototype.finish.call(this)},e.prototype.setFillStrokeStyles_=function(){var t=this.state;void 0!==t.fillStyle&&this.updateFillStyle(t,this.createFill),void 0!==t.strokeStyle&&this.updateStrokeStyle(t,this.applyStroke)},e}(Ir),jr=kr;function Gr(t,e,i,n,o){var r,s,a,l,h,u,c,p,f,d=i,g=i,_=0,y=0,v=i;for(r=i;r<n;r+=o){var m=e[r],x=e[r+1];void 0!==l&&(p=m-l,f=x-h,a=Math.sqrt(p*p+f*f),void 0!==u&&(y+=s,Math.acos((u*p+c*f)/(s*a))>t&&(y>_&&(_=y,d=v,g=r),y=0,v=r-o)),s=a,u=p,c=f),l=m,h=x}return(y+=a)>_?[v,r]:[d,g]}var zr=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Wr={left:0,end:0,center:.5,right:1,start:1,top:0,middle:.5,hanging:.2,alphabetic:.8,ideographic:.8,bottom:1},Xr=function(t){function e(e,i,n,o){var r=t.call(this,e,i,n,o)||this;return r.labels_=null,r.text_="",r.textOffsetX_=0,r.textOffsetY_=0,r.textRotateWithView_=void 0,r.textRotation_=0,r.textFillState_=null,r.fillStates={},r.textStrokeState_=null,r.strokeStates={},r.textState_={},r.textStates={},r.textKey_="",r.fillKey_="",r.strokeKey_="",r.declutterImageWithText_=void 0,r}return zr(e,t),e.prototype.finish=function(){var e=t.prototype.finish.call(this);return e.textStates=this.textStates,e.fillStates=this.fillStates,e.strokeStates=this.strokeStates,e},e.prototype.drawText=function(t,e){var i=this.textFillState_,n=this.textStrokeState_,o=this.textState_;if(""!==this.text_&&o&&(i||n)){var r=this.coordinates,s=r.length,a=t.getType(),l=null,h=t.getStride();if("line"!==o.placement||"LineString"!=a&&"MultiLineString"!=a&&"Polygon"!=a&&"MultiPolygon"!=a){var u=o.overflow?null:[];switch(a){case"Point":case"MultiPoint":l=t.getFlatCoordinates();break;case"LineString":l=t.getFlatMidpoint();break;case"Circle":l=t.getCenter();break;case"MultiLineString":l=t.getFlatMidpoints(),h=2;break;case"Polygon":l=t.getFlatInteriorPoint(),o.overflow||u.push(l[2]/this.resolution),h=3;break;case"MultiPolygon":var c=t.getFlatInteriorPoints();for(l=[],w=0,S=c.length;w<S;w+=3)o.overflow||u.push(c[w+2]/this.resolution),l.push(c[w],c[w+1]);if(0===l.length)return;h=2}if((I=this.appendFlatPointCoordinates(l,h))===s)return;if(u&&(I-s)/2!=l.length/h){var p=s/2;u=u.filter((function(t,e){var i=r[2*(p+e)]===l[e*h]&&r[2*(p+e)+1]===l[e*h+1];return i||--p,i}))}this.saveTextStates_(),(o.backgroundFill||o.backgroundStroke)&&(this.setFillStrokeStyle(o.backgroundFill,o.backgroundStroke),o.backgroundFill&&(this.updateFillStyle(this.state,this.createFill),this.hitDetectionInstructions.push(this.createFill(this.state))),o.backgroundStroke&&(this.updateStrokeStyle(this.state,this.applyStroke),this.hitDetectionInstructions.push(this.createStroke(this.state)))),this.beginGeometry(t,e);var f=o.padding;if(f!=qo&&(o.scale[0]<0||o.scale[1]<0)){var d=o.padding[0],g=o.padding[1],_=o.padding[2],y=o.padding[3];o.scale[0]<0&&(g=-g,y=-y),o.scale[1]<0&&(d=-d,_=-_),f=[d,g,_,y]}var v=this.pixelRatio;this.instructions.push([br.DRAW_IMAGE,s,I,null,NaN,NaN,NaN,1,0,0,this.textRotateWithView_,this.textRotation_,[1,1],NaN,void 0,this.declutterImageWithText_,f==qo?qo:f.map((function(t){return t*v})),!!o.backgroundFill,!!o.backgroundStroke,this.text_,this.textKey_,this.strokeKey_,this.fillKey_,this.textOffsetX_,this.textOffsetY_,u]);var m=1/v;this.hitDetectionInstructions.push([br.DRAW_IMAGE,s,I,null,NaN,NaN,NaN,1,0,0,this.textRotateWithView_,this.textRotation_,[m,m],NaN,void 0,this.declutterImageWithText_,f,!!o.backgroundFill,!!o.backgroundStroke,this.text_,this.textKey_,this.strokeKey_,this.fillKey_,this.textOffsetX_,this.textOffsetY_,u]),this.endGeometry(e)}else{if(!je(this.getBufferedMaxExtent(),t.getExtent()))return;var x=void 0;if(l=t.getFlatCoordinates(),"LineString"==a)x=[l.length];else if("MultiLineString"==a)x=t.getEnds();else if("Polygon"==a)x=t.getEnds().slice(0,1);else if("MultiPolygon"==a){var C=t.getEndss();x=[];for(var w=0,S=C.length;w<S;++w)x.push(C[w][0])}this.beginGeometry(t,e);for(var E=o.textAlign,T=0,b=void 0,O=0,R=x.length;O<R;++O){if(null==E){var P=Gr(o.maxAngle,l,T,x[O],h);T=P[0],b=P[1]}else b=x[O];for(w=T;w<b;w+=h)r.push(l[w],l[w+1]);var I=r.length;T=x[O],this.drawChars_(s,I),s=I}this.endGeometry(e)}}},e.prototype.saveTextStates_=function(){var t=this.textStrokeState_,e=this.textState_,i=this.textFillState_,n=this.strokeKey_;t&&(n in this.strokeStates||(this.strokeStates[n]={strokeStyle:t.strokeStyle,lineCap:t.lineCap,lineDashOffset:t.lineDashOffset,lineWidth:t.lineWidth,lineJoin:t.lineJoin,miterLimit:t.miterLimit,lineDash:t.lineDash}));var o=this.textKey_;o in this.textStates||(this.textStates[o]={font:e.font,textAlign:e.textAlign||Uo,justify:e.justify,textBaseline:e.textBaseline||Ho,scale:e.scale});var r=this.fillKey_;i&&(r in this.fillStates||(this.fillStates[r]={fillStyle:i.fillStyle}))},e.prototype.drawChars_=function(t,e){var i=this.textStrokeState_,n=this.textState_,o=this.strokeKey_,r=this.textKey_,s=this.fillKey_;this.saveTextStates_();var a=this.pixelRatio,l=Wr[n.textBaseline],h=this.textOffsetY_*a,u=this.text_,c=i?i.lineWidth*Math.abs(n.scale[0])/2:0;this.instructions.push([br.DRAW_CHARS,t,e,l,n.overflow,s,n.maxAngle,a,h,o,c*a,u,r,1]),this.hitDetectionInstructions.push([br.DRAW_CHARS,t,e,l,n.overflow,s,n.maxAngle,1,h,o,c,u,r,1/a])},e.prototype.setTextStyle=function(t,e){var i,n,o;if(t){var r=t.getFill();r?((n=this.textFillState_)||(n={},this.textFillState_=n),n.fillStyle=Xo(r.getColor()||Yo)):(n=null,this.textFillState_=n);var s=t.getStroke();if(s){(o=this.textStrokeState_)||(o={},this.textStrokeState_=o);var a=s.getLineDash(),l=s.getLineDashOffset(),h=s.getWidth(),u=s.getMiterLimit();o.lineCap=s.getLineCap()||Bo,o.lineDash=a?a.slice():Ko,o.lineDashOffset=void 0===l?0:l,o.lineJoin=s.getLineJoin()||Zo,o.lineWidth=void 0===h?1:h,o.miterLimit=void 0===u?10:u,o.strokeStyle=Xo(s.getColor()||Vo)}else o=null,this.textStrokeState_=o;i=this.textState_;var c=t.getFont()||No;ir(c);var p=t.getScaleArray();i.overflow=t.getOverflow(),i.font=c,i.maxAngle=t.getMaxAngle(),i.placement=t.getPlacement(),i.textAlign=t.getTextAlign(),i.justify=t.getJustify(),i.textBaseline=t.getTextBaseline()||Ho,i.backgroundFill=t.getBackgroundFill(),i.backgroundStroke=t.getBackgroundStroke(),i.padding=t.getPadding()||qo,i.scale=void 0===p?[1,1]:p;var f=t.getOffsetX(),d=t.getOffsetY(),g=t.getRotateWithView(),_=t.getRotation();this.text_=t.getText()||"",this.textOffsetX_=void 0===f?0:f,this.textOffsetY_=void 0===d?0:d,this.textRotateWithView_=void 0!==g&&g,this.textRotation_=void 0===_?0:_,this.strokeKey_=o?("string"==typeof o.strokeStyle?o.strokeStyle:D(o.strokeStyle))+o.lineCap+o.lineDashOffset+"|"+o.lineWidth+o.lineJoin+o.miterLimit+"["+o.lineDash.join()+"]":"",this.textKey_=i.font+i.scale+(i.textAlign||"?")+(i.justify||"?")+(i.textBaseline||"?"),this.fillKey_=n?"string"==typeof n.fillStyle?n.fillStyle:"|"+D(n.fillStyle):""}else this.text_="";this.declutterImageWithText_=e},e}(Ir),Nr={Circle:jr,Default:Ir,Image:Fr,LineString:Ar,Polygon:jr,Text:Xr},Yr=function(){function t(t,e,i,n){this.tolerance_=t,this.maxExtent_=e,this.pixelRatio_=n,this.resolution_=i,this.buildersByZIndex_={}}return t.prototype.finish=function(){var t={};for(var e in this.buildersByZIndex_){t[e]=t[e]||{};var i=this.buildersByZIndex_[e];for(var n in i){var o=i[n].finish();t[e][n]=o}}return t},t.prototype.getBuilder=function(t,e){var i=void 0!==t?t.toString():"0",n=this.buildersByZIndex_[i];void 0===n&&(n={},this.buildersByZIndex_[i]=n);var o=n[e];return void 0===o&&(o=new(0,Nr[e])(this.tolerance_,this.maxExtent_,this.resolution_,this.pixelRatio_),n[e]=o),o},t}();function Br(t,e,i,n,o,r,s,a,l,h,u,c){var p=t[e],f=t[e+1],d=0,g=0,_=0,y=0;function v(){d=p,g=f,p=t[e+=n],f=t[e+1],y+=_,_=Math.sqrt((p-d)*(p-d)+(f-g)*(f-g))}do{v()}while(e<i-n&&y+_<r);for(var m=0===_?0:(r-y)/_,x=Rt(d,p,m),C=Rt(g,f,m),w=e-n,S=y,E=r+a*l(h,o,u);e<i-n&&y+_<E;)v();var T,b=Rt(d,p,m=0===_?0:(E-y)/_),O=Rt(g,f,m);if(c){var R=[x,C,b,O];Ai(R,0,4,2,c,R,R),T=R[0]>R[2]}else T=x>b;var P,I=Math.PI,M=[],F=w+n===e;if(_=0,y=S,p=t[e=w],f=t[e+1],F){v(),P=Math.atan2(f-g,p-d),T&&(P+=P>0?-I:I);var L=(b+x)/2,A=(O+C)/2;return M[0]=[L,A,(E-r)/2,P,o],M}for(var D=0,k=(o=o.replace(/\n/g," ")).length;D<k;){v();var j=Math.atan2(f-g,p-d);if(T&&(j+=j>0?-I:I),void 0!==P){var G=j-P;if(G+=G>I?-2*I:G<-I?2*I:0,Math.abs(G)>s)return null}P=j;for(var z=D,W=0;D<k;++D){var X=a*l(h,o[T?k-D-1:D],u);if(e+n<i&&y+_<r+W+X/2)break;W+=X}if(D!==z){var N=T?o.substring(k-z,k-D):o.substring(z,D);L=Rt(d,p,m=0===_?0:(r+W/2-y)/_),A=Rt(g,f,m),M.push([L,A,W/2,j,N]),r+=W}}return M}var Kr=[1/0,1/0,-1/0,-1/0],Zr=[],Vr=[],Ur=[],Hr=[];function qr(t){return t[3].declutterBox}var Jr=new RegExp("["+String.fromCharCode(1425)+"-"+String.fromCharCode(2303)+String.fromCharCode(64285)+"-"+String.fromCharCode(65023)+String.fromCharCode(65136)+"-"+String.fromCharCode(65276)+String.fromCharCode(67584)+"-"+String.fromCharCode(69631)+String.fromCharCode(124928)+"-"+String.fromCharCode(126975)+"]");function Qr(t,e){return"start"!==e&&"end"!==e||Jr.test(t)||(e="start"===e?"left":"right"),Wr[e]}function $r(t,e,i){return i>0&&t.push("\n",""),t.push(e,""),t}var ts=function(){function t(t,e,i,n){this.overlaps=i,this.pixelRatio=e,this.resolution=t,this.alignFill_,this.instructions=n.instructions,this.coordinates=n.coordinates,this.coordinateCache_={},this.renderedTransform_=[1,0,0,1,0,0],this.hitDetectionInstructions=n.hitDetectionInstructions,this.pixelCoordinates_=null,this.viewRotation_=0,this.fillStates=n.fillStates||{},this.strokeStates=n.strokeStates||{},this.textStates=n.textStates||{},this.widths_={},this.labels_={}}return t.prototype.createLabel=function(t,e,i,n){var o=t+e+i+n;if(this.labels_[o])return this.labels_[o];var r=n?this.strokeStates[n]:null,s=i?this.fillStates[i]:null,a=this.textStates[e],l=this.pixelRatio,h=[a.scale[0]*l,a.scale[1]*l],u=Array.isArray(t),c=a.justify?Wr[a.justify]:Qr(Array.isArray(t)?t[0]:t,a.textAlign||Uo),p=n&&r.lineWidth?r.lineWidth:0,f=u?t:t.split("\n").reduce($r,[]),d=function(t,e){for(var i=[],n=[],o=[],r=0,s=0,a=0,l=0,h=0,u=e.length;h<=u;h+=2){var c=e[h];if("\n"!==c&&h!==u){var p=e[h+1]||t.font,f=rr(p,c);i.push(f),s+=f;var d=nr(p);n.push(d),l=Math.max(l,d)}else r=Math.max(r,s),o.push(s),s=0,a+=l}return{width:r,height:a,widths:i,heights:n,lineWidths:o}}(a,f),g=d.width,_=d.height,y=d.widths,v=d.heights,m=d.lineWidths,x=g+p,C=[],w=(x+2)*h[0],S=(_+p)*h[1],E={width:w<0?Math.floor(w):Math.ceil(w),height:S<0?Math.floor(S):Math.ceil(S),contextInstructions:C};1==h[0]&&1==h[1]||C.push("scale",h),n&&(C.push("strokeStyle",r.strokeStyle),C.push("lineWidth",p),C.push("lineCap",r.lineCap),C.push("lineJoin",r.lineJoin),C.push("miterLimit",r.miterLimit),(V?OffscreenCanvasRenderingContext2D:CanvasRenderingContext2D).prototype.setLineDash&&(C.push("setLineDash",[r.lineDash]),C.push("lineDashOffset",r.lineDashOffset))),i&&C.push("fillStyle",s.fillStyle),C.push("textBaseline","middle"),C.push("textAlign","center");for(var T,b=.5-c,O=c*x+b*p,R=[],P=[],I=0,M=0,F=0,L=0,A=0,D=f.length;A<D;A+=2){var k=f[A];if("\n"!==k){var j=f[A+1]||a.font;j!==T&&(n&&R.push("font",j),i&&P.push("font",j),T=j),I=Math.max(I,v[F]);var G=[k,O+b*y[F]+c*(y[F]-m[L]),.5*(p+I)+M];O+=y[F],n&&R.push("strokeText",G),i&&P.push("fillText",G),++F}else M+=I,I=0,O=c*x+b*p,++L}return Array.prototype.push.apply(C,R),Array.prototype.push.apply(C,P),this.labels_[o]=E,E},t.prototype.replayTextBackground_=function(t,e,i,n,o,r,s){t.beginPath(),t.moveTo.apply(t,e),t.lineTo.apply(t,i),t.lineTo.apply(t,n),t.lineTo.apply(t,o),t.lineTo.apply(t,e),r&&(this.alignFill_=r[2],this.fill_(t)),s&&(this.setStrokeStyle_(t,s),t.stroke())},t.prototype.calculateImageOrLabelDimensions_=function(t,e,i,n,o,r,s,a,l,h,u,c,p,f,d,g){var _,y=i-(s*=c[0]),v=n-(a*=c[1]),m=o+l>t?t-l:o,x=r+h>e?e-h:r,C=f[3]+m*c[0]+f[1],w=f[0]+x*c[1]+f[2],S=y-f[3],E=v-f[0];return(d||0!==u)&&(Zr[0]=S,Hr[0]=S,Zr[1]=E,Vr[1]=E,Vr[0]=S+C,Ur[0]=Vr[0],Ur[1]=E+w,Hr[1]=Ur[1]),0!==u?(Pi(_=Ii([1,0,0,1,0,0],i,n,1,1,u,-i,-n),Zr),Pi(_,Vr),Pi(_,Ur),Pi(_,Hr),ye(Math.min(Zr[0],Vr[0],Ur[0],Hr[0]),Math.min(Zr[1],Vr[1],Ur[1],Hr[1]),Math.max(Zr[0],Vr[0],Ur[0],Hr[0]),Math.max(Zr[1],Vr[1],Ur[1],Hr[1]),Kr)):ye(Math.min(S,S+C),Math.min(E,E+w),Math.max(S,S+C),Math.max(E,E+w),Kr),p&&(y=Math.round(y),v=Math.round(v)),{drawImageX:y,drawImageY:v,drawImageW:m,drawImageH:x,originX:l,originY:h,declutterBox:{minX:Kr[0],minY:Kr[1],maxX:Kr[2],maxY:Kr[3],value:g},canvasTransform:_,scale:c}},t.prototype.replayImageOrLabel_=function(t,e,i,n,o,r,s){var a=!(!r&&!s),l=n.declutterBox,h=t.canvas,u=s?s[2]*n.scale[0]/2:0;return l.minX-u<=h.width/e&&l.maxX+u>=0&&l.minY-u<=h.height/e&&l.maxY+u>=0&&(a&&this.replayTextBackground_(t,Zr,Vr,Ur,Hr,r,s),function(t,e,i,n,o,r,s,a,l,h,u){t.save(),1!==i&&(t.globalAlpha*=i),e&&t.setTransform.apply(t,e),n.contextInstructions?(t.translate(l,h),t.scale(u[0],u[1]),function(t,e){for(var i=t.contextInstructions,n=0,o=i.length;n<o;n+=2)Array.isArray(i[n+1])?e[i[n]].apply(e,i[n+1]):e[i[n]]=i[n+1]}(n,t)):u[0]<0||u[1]<0?(t.translate(l,h),t.scale(u[0],u[1]),t.drawImage(n,o,r,s,a,0,0,s,a)):t.drawImage(n,o,r,s,a,l,h,s*u[0],a*u[1]),t.restore()}(t,n.canvasTransform,o,i,n.originX,n.originY,n.drawImageW,n.drawImageH,n.drawImageX,n.drawImageY,n.scale)),!0},t.prototype.fill_=function(t){if(this.alignFill_){var e=Pi(this.renderedTransform_,[0,0]),i=512*this.pixelRatio;t.save(),t.translate(e[0]%i,e[1]%i),t.rotate(this.viewRotation_)}t.fill(),this.alignFill_&&t.restore()},t.prototype.setStrokeStyle_=function(t,e){t.strokeStyle=e[1],t.lineWidth=e[2],t.lineCap=e[3],t.lineJoin=e[4],t.miterLimit=e[5],t.setLineDash&&(t.lineDashOffset=e[7],t.setLineDash(e[6]))},t.prototype.drawLabelWithPointPlacement_=function(t,e,i,n){var o=this.textStates[e],r=this.createLabel(t,e,n,i),s=this.strokeStates[i],a=this.pixelRatio,l=Qr(Array.isArray(t)?t[0]:t,o.textAlign||Uo),h=Wr[o.textBaseline||Ho],u=s&&s.lineWidth?s.lineWidth:0;return{label:r,anchorX:l*(r.width/a-2*o.scale[0])+2*(.5-l)*u,anchorY:h*r.height/a+2*(.5-h)*u}},t.prototype.execute_=function(t,e,i,n,o,r,s,a){var l,u,c;this.pixelCoordinates_&&h(i,this.renderedTransform_)?l=this.pixelCoordinates_:(this.pixelCoordinates_||(this.pixelCoordinates_=[]),l=Li(this.coordinates,0,this.coordinates.length,2,i,this.pixelCoordinates_),c=i,(u=this.renderedTransform_)[0]=c[0],u[1]=c[1],u[2]=c[2],u[3]=c[3],u[4]=c[4],u[5]=c[5]);for(var p,f,d,g,_,y,v,m,x,C,w,S,E,T,b,O,R=0,P=n.length,I=0,M=0,F=0,L=null,A=null,D=this.coordinateCache_,k=this.viewRotation_,j=Math.round(1e12*Math.atan2(-i[1],i[0]))/1e12,G={context:t,pixelRatio:this.pixelRatio,resolution:this.resolution,rotation:k},z=this.instructions!=n||this.overlaps?0:200;R<P;){var W=n[R];switch(W[0]){case br.BEGIN_GEOMETRY:E=W[1],O=W[3],E.getGeometry()?void 0===s||je(s,O.getExtent())?++R:R=W[2]+1:R=W[2];break;case br.BEGIN_PATH:M>z&&(this.fill_(t),M=0),F>z&&(t.stroke(),F=0),M||F||(t.beginPath(),g=NaN,_=NaN),++R;break;case br.CIRCLE:var X=l[I=W[1]],N=l[I+1],Y=l[I+2]-X,B=l[I+3]-N,K=Math.sqrt(Y*Y+B*B);t.moveTo(X+K,N),t.arc(X,N,K,0,2*Math.PI,!0),++R;break;case br.CLOSE_PATH:t.closePath(),++R;break;case br.CUSTOM:I=W[1],p=W[2];var Z=W[3],V=W[4],U=6==W.length?W[5]:void 0;G.geometry=Z,G.feature=E,R in D||(D[R]=[]);var H=D[R];U?U(l,I,p,2,H):(H[0]=l[I],H[1]=l[I+1],H.length=2),V(H,G),++R;break;case br.DRAW_IMAGE:I=W[1],p=W[2],m=W[3],f=W[4],d=W[5];var q=W[6],J=W[7],Q=W[8],$=W[9],tt=W[10],et=W[11],it=W[12],nt=W[13],ot=W[14],rt=W[15];if(!m&&W.length>=20){x=W[19],C=W[20],w=W[21],S=W[22];var st=this.drawLabelWithPointPlacement_(x,C,w,S);m=st.label,W[3]=m;var at=W[23];f=(st.anchorX-at)*this.pixelRatio,W[4]=f;var lt=W[24];d=(st.anchorY-lt)*this.pixelRatio,W[5]=d,q=m.height,W[6]=q,nt=m.width,W[13]=nt}var ht=void 0;W.length>25&&(ht=W[25]);var ut=void 0,ct=void 0,pt=void 0;W.length>17?(ut=W[16],ct=W[17],pt=W[18]):(ut=qo,ct=!1,pt=!1),tt&&j?et+=k:tt||j||(et-=k);for(var ft=0;I<p;I+=2)if(!(ht&&ht[ft++]<nt/this.pixelRatio)){var dt=[t,e,m,Xt=this.calculateImageOrLabelDimensions_(m.width,m.height,l[I],l[I+1],nt,q,f,d,Q,$,et,it,o,ut,ct||pt,E),J,ct?L:null,pt?A:null];if(a){if("none"===ot)continue;if("obstacle"===ot){a.insert(Xt.declutterBox);continue}var gt=void 0,_t=void 0;if(rt){var yt=p-I;if(!rt[yt]){rt[yt]=dt;continue}if(gt=rt[yt],delete rt[yt],_t=qr(gt),a.collides(_t))continue}if(a.collides(Xt.declutterBox))continue;gt&&(a.insert(_t),this.replayImageOrLabel_.apply(this,gt)),a.insert(Xt.declutterBox)}this.replayImageOrLabel_.apply(this,dt)}++R;break;case br.DRAW_CHARS:var vt=W[1],mt=W[2],xt=W[3],Ct=W[4];S=W[5];var wt=W[6],St=W[7],Et=W[8];w=W[9];var Tt=W[10];x=W[11],C=W[12];var bt=[W[13],W[13]],Ot=this.textStates[C],Rt=Ot.font,Pt=[Ot.scale[0]*St,Ot.scale[1]*St],It=void 0;Rt in this.widths_?It=this.widths_[Rt]:(It={},this.widths_[Rt]=It);var Mt=fn(l,vt,mt,2),Ft=Math.abs(Pt[0])*sr(Rt,x,It);if(Ct||Ft<=Mt){var Lt=this.textStates[C].textAlign,At=Br(l,vt,mt,2,x,(Mt-Ft)*Wr[Lt],wt,Math.abs(Pt[0]),sr,Rt,It,j?0:this.viewRotation_);t:if(At){var Dt=[],kt=void 0,jt=void 0,Gt=void 0,zt=void 0,Wt=void 0;if(w)for(kt=0,jt=At.length;kt<jt;++kt){Gt=(Wt=At[kt])[4],zt=this.createLabel(Gt,C,"",w),f=Wt[2]+(Pt[0]<0?-Tt:Tt),d=xt*zt.height+2*(.5-xt)*Tt*Pt[1]/Pt[0]-Et;var Xt=this.calculateImageOrLabelDimensions_(zt.width,zt.height,Wt[0],Wt[1],zt.width,zt.height,f,d,0,0,Wt[3],bt,!1,qo,!1,E);if(a&&a.collides(Xt.declutterBox))break t;Dt.push([t,e,zt,Xt,1,null,null])}if(S)for(kt=0,jt=At.length;kt<jt;++kt){if(Gt=(Wt=At[kt])[4],zt=this.createLabel(Gt,C,S,""),f=Wt[2],d=xt*zt.height-Et,Xt=this.calculateImageOrLabelDimensions_(zt.width,zt.height,Wt[0],Wt[1],zt.width,zt.height,f,d,0,0,Wt[3],bt,!1,qo,!1,E),a&&a.collides(Xt.declutterBox))break t;Dt.push([t,e,zt,Xt,1,null,null])}a&&a.load(Dt.map(qr));for(var Nt=0,Yt=Dt.length;Nt<Yt;++Nt)this.replayImageOrLabel_.apply(this,Dt[Nt])}}++R;break;case br.END_GEOMETRY:if(void 0!==r){var Bt=r(E=W[1],O);if(Bt)return Bt}++R;break;case br.FILL:z?M++:this.fill_(t),++R;break;case br.MOVE_TO_LINE_TO:for(I=W[1],p=W[2],T=l[I],v=(b=l[I+1])+.5|0,(y=T+.5|0)===g&&v===_||(t.moveTo(T,b),g=y,_=v),I+=2;I<p;I+=2)y=(T=l[I])+.5|0,v=(b=l[I+1])+.5|0,I!=p-2&&y===g&&v===_||(t.lineTo(T,b),g=y,_=v);++R;break;case br.SET_FILL_STYLE:L=W,this.alignFill_=W[2],M&&(this.fill_(t),M=0,F&&(t.stroke(),F=0)),t.fillStyle=W[1],++R;break;case br.SET_STROKE_STYLE:A=W,F&&(t.stroke(),F=0),this.setStrokeStyle_(t,W),++R;break;case br.STROKE:z?F++:t.stroke(),++R;break;default:++R}}M&&this.fill_(t),F&&t.stroke()},t.prototype.execute=function(t,e,i,n,o,r){this.viewRotation_=n,this.execute_(t,e,i,this.instructions,o,void 0,void 0,r)},t.prototype.executeHitDetection=function(t,e,i,n,o){return this.viewRotation_=i,this.execute_(t,1,e,this.hitDetectionInstructions,!0,n,o)},t}(),es=ts,is=["Polygon","Circle","LineString","Image","Text","Default"],ns=function(){function t(t,e,i,n,o,r){this.maxExtent_=t,this.overlaps_=n,this.pixelRatio_=i,this.resolution_=e,this.renderBuffer_=r,this.executorsByZIndex_={},this.hitDetectionContext_=null,this.hitDetectionTransform_=[1,0,0,1,0,0],this.createExecutors_(o)}return t.prototype.clip=function(t,e){var i=this.getClipCoords(e);t.beginPath(),t.moveTo(i[0],i[1]),t.lineTo(i[2],i[3]),t.lineTo(i[4],i[5]),t.lineTo(i[6],i[7]),t.clip()},t.prototype.createExecutors_=function(t){for(var e in t){var i=this.executorsByZIndex_[e];void 0===i&&(i={},this.executorsByZIndex_[e]=i);var n=t[e];for(var o in n){var r=n[o];i[o]=new es(this.resolution_,this.pixelRatio_,this.overlaps_,r)}}},t.prototype.hasExecutors=function(t){for(var e in this.executorsByZIndex_)for(var i=this.executorsByZIndex_[e],n=0,o=t.length;n<o;++n)if(t[n]in i)return!0;return!1},t.prototype.forEachFeatureAtCoordinate=function(t,e,i,n,o,s){var a=2*(n=Math.round(n))+1,l=Ii(this.hitDetectionTransform_,n+.5,n+.5,1/e,-1/e,-i,-t[0],-t[1]),h=!this.hitDetectionContext_;h&&(this.hitDetectionContext_=q(a,a));var u,c=this.hitDetectionContext_;c.canvas.width!==a||c.canvas.height!==a?(c.canvas.width=a,c.canvas.height=a):h||c.clearRect(0,0,a,a),void 0!==this.renderBuffer_&&(Ce(u=[1/0,1/0,-1/0,-1/0],t),ue(u,e*(this.renderBuffer_+n),u));var p,f=function(t){if(void 0!==os[t])return os[t];for(var e=2*t+1,i=t*t,n=new Array(i+1),o=0;o<=t;++o)for(var r=0;r<=t;++r){var s=o*o+r*r;if(s>i)break;var a=n[s];a||(a=[],n[s]=a),a.push(4*((t+o)*e+(t+r))+3),o>0&&a.push(4*((t-o)*e+(t+r))+3),r>0&&(a.push(4*((t+o)*e+(t-r))+3),o>0&&a.push(4*((t-o)*e+(t-r))+3))}for(var l=[],h=(o=0,n.length);o<h;++o)n[o]&&l.push.apply(l,n[o]);return os[t]=l,l}(n);function d(t,e){for(var i=c.getImageData(0,0,a,a).data,r=0,l=f.length;r<l;r++)if(i[f[r]]>0){if(!s||"Image"!==p&&"Text"!==p||-1!==s.indexOf(t)){var h=(f[r]-3)/4,u=n-h%a,d=n-(h/a|0),g=o(t,e,u*u+d*d);if(g)return g}c.clearRect(0,0,a,a);break}}var g,_,y,v,m,x=Object.keys(this.executorsByZIndex_).map(Number);for(x.sort(r),g=x.length-1;g>=0;--g){var C=x[g].toString();for(y=this.executorsByZIndex_[C],_=is.length-1;_>=0;--_)if(void 0!==(v=y[p=is[_]])&&(m=v.executeHitDetection(c,l,i,d,u)))return m}},t.prototype.getClipCoords=function(t){var e=this.maxExtent_;if(!e)return null;var i=e[0],n=e[1],o=e[2],r=e[3],s=[i,n,i,r,o,r,o,n];return Li(s,0,8,2,t,s),s},t.prototype.isEmpty=function(){return _(this.executorsByZIndex_)},t.prototype.execute=function(t,e,i,n,o,s,a){var l=Object.keys(this.executorsByZIndex_).map(Number);l.sort(r),this.maxExtent_&&(t.save(),this.clip(t,i));var h,u,c,p,f,d,g=s||is;for(a&&l.reverse(),h=0,u=l.length;h<u;++h){var _=l[h].toString();for(f=this.executorsByZIndex_[_],c=0,p=g.length;c<p;++c)void 0!==(d=f[g[c]])&&d.execute(t,e,i,n,o,a)}this.maxExtent_&&t.restore()},t}(),os={},rs=ns,ss=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),as=function(t){function e(e,i,n,o,r,s,a){var l=t.call(this)||this;return l.context_=e,l.pixelRatio_=i,l.extent_=n,l.transform_=o,l.viewRotation_=r,l.squaredTolerance_=s,l.userTransform_=a,l.contextFillState_=null,l.contextStrokeState_=null,l.contextTextState_=null,l.fillState_=null,l.strokeState_=null,l.image_=null,l.imageAnchorX_=0,l.imageAnchorY_=0,l.imageHeight_=0,l.imageOpacity_=0,l.imageOriginX_=0,l.imageOriginY_=0,l.imageRotateWithView_=!1,l.imageRotation_=0,l.imageScale_=[0,0],l.imageWidth_=0,l.text_="",l.textOffsetX_=0,l.textOffsetY_=0,l.textRotateWithView_=!1,l.textRotation_=0,l.textScale_=[0,0],l.textFillState_=null,l.textStrokeState_=null,l.textState_=null,l.pixelCoordinates_=[],l.tmpLocalTransform_=[1,0,0,1,0,0],l}return ss(e,t),e.prototype.drawImages_=function(t,e,i,n){if(this.image_){var o=Li(t,e,i,n,this.transform_,this.pixelCoordinates_),r=this.context_,s=this.tmpLocalTransform_,a=r.globalAlpha;1!=this.imageOpacity_&&(r.globalAlpha=a*this.imageOpacity_);var l=this.imageRotation_;this.imageRotateWithView_&&(l+=this.viewRotation_);for(var h=0,u=o.length;h<u;h+=2){var c=o[h]-this.imageAnchorX_,p=o[h+1]-this.imageAnchorY_;if(0!==l||1!=this.imageScale_[0]||1!=this.imageScale_[1]){var f=c+this.imageAnchorX_,d=p+this.imageAnchorY_;Ii(s,f,d,1,1,l,-f,-d),r.setTransform.apply(r,s),r.translate(f,d),r.scale(this.imageScale_[0],this.imageScale_[1]),r.drawImage(this.image_,this.imageOriginX_,this.imageOriginY_,this.imageWidth_,this.imageHeight_,-this.imageAnchorX_,-this.imageAnchorY_,this.imageWidth_,this.imageHeight_),r.setTransform(1,0,0,1,0,0)}else r.drawImage(this.image_,this.imageOriginX_,this.imageOriginY_,this.imageWidth_,this.imageHeight_,c,p,this.imageWidth_,this.imageHeight_)}1!=this.imageOpacity_&&(r.globalAlpha=a)}},e.prototype.drawText_=function(t,e,i,n){if(this.textState_&&""!==this.text_){this.textFillState_&&this.setContextFillState_(this.textFillState_),this.textStrokeState_&&this.setContextStrokeState_(this.textStrokeState_),this.setContextTextState_(this.textState_);var o=Li(t,e,i,n,this.transform_,this.pixelCoordinates_),r=this.context_,s=this.textRotation_;for(this.textRotateWithView_&&(s+=this.viewRotation_);e<i;e+=n){var a=o[e]+this.textOffsetX_,l=o[e+1]+this.textOffsetY_;if(0!==s||1!=this.textScale_[0]||1!=this.textScale_[1]){var h=Ii(this.tmpLocalTransform_,a,l,1,1,s,-a,-l);r.setTransform.apply(r,h),r.translate(a,l),r.scale(this.textScale_[0],this.textScale_[1]),this.textStrokeState_&&r.strokeText(this.text_,0,0),this.textFillState_&&r.fillText(this.text_,0,0),r.setTransform(1,0,0,1,0,0)}else this.textStrokeState_&&r.strokeText(this.text_,a,l),this.textFillState_&&r.fillText(this.text_,a,l)}}},e.prototype.moveToLineTo_=function(t,e,i,n,o){var r=this.context_,s=Li(t,e,i,n,this.transform_,this.pixelCoordinates_);r.moveTo(s[0],s[1]);var a=s.length;o&&(a-=2);for(var l=2;l<a;l+=2)r.lineTo(s[l],s[l+1]);return o&&r.closePath(),i},e.prototype.drawRings_=function(t,e,i,n){for(var o=0,r=i.length;o<r;++o)e=this.moveToLineTo_(t,e,i[o],n,!0);return e},e.prototype.drawCircle=function(t){if(je(this.extent_,t.getExtent())){if(this.fillState_||this.strokeState_){this.fillState_&&this.setContextFillState_(this.fillState_),this.strokeState_&&this.setContextStrokeState_(this.strokeState_);var e=function(t,e,i){var n=t.getFlatCoordinates();if(n){var o=t.getStride();return Li(n,0,n.length,o,e,i)}return null}(t,this.transform_,this.pixelCoordinates_),i=e[2]-e[0],n=e[3]-e[1],o=Math.sqrt(i*i+n*n),r=this.context_;r.beginPath(),r.arc(e[0],e[1],o,0,2*Math.PI),this.fillState_&&r.fill(),this.strokeState_&&r.stroke()}""!==this.text_&&this.drawText_(t.getCenter(),0,2,2)}},e.prototype.setStyle=function(t){this.setFillStrokeStyle(t.getFill(),t.getStroke()),this.setImageStyle(t.getImage()),this.setTextStyle(t.getText())},e.prototype.setTransform=function(t){this.transform_=t},e.prototype.drawGeometry=function(t){switch(t.getType()){case"Point":this.drawPoint(t);break;case"LineString":this.drawLineString(t);break;case"Polygon":this.drawPolygon(t);break;case"MultiPoint":this.drawMultiPoint(t);break;case"MultiLineString":this.drawMultiLineString(t);break;case"MultiPolygon":this.drawMultiPolygon(t);break;case"GeometryCollection":this.drawGeometryCollection(t);break;case"Circle":this.drawCircle(t)}},e.prototype.drawFeature=function(t,e){var i=e.getGeometryFunction()(t);i&&je(this.extent_,i.getExtent())&&(this.setStyle(e),this.drawGeometry(i))},e.prototype.drawGeometryCollection=function(t){for(var e=t.getGeometriesArray(),i=0,n=e.length;i<n;++i)this.drawGeometry(e[i])},e.prototype.drawPoint=function(t){this.squaredTolerance_&&(t=t.simplifyTransformed(this.squaredTolerance_,this.userTransform_));var e=t.getFlatCoordinates(),i=t.getStride();this.image_&&this.drawImages_(e,0,e.length,i),""!==this.text_&&this.drawText_(e,0,e.length,i)},e.prototype.drawMultiPoint=function(t){this.squaredTolerance_&&(t=t.simplifyTransformed(this.squaredTolerance_,this.userTransform_));var e=t.getFlatCoordinates(),i=t.getStride();this.image_&&this.drawImages_(e,0,e.length,i),""!==this.text_&&this.drawText_(e,0,e.length,i)},e.prototype.drawLineString=function(t){if(this.squaredTolerance_&&(t=t.simplifyTransformed(this.squaredTolerance_,this.userTransform_)),je(this.extent_,t.getExtent())){if(this.strokeState_){this.setContextStrokeState_(this.strokeState_);var e=this.context_,i=t.getFlatCoordinates();e.beginPath(),this.moveToLineTo_(i,0,i.length,t.getStride(),!1),e.stroke()}if(""!==this.text_){var n=t.getFlatMidpoint();this.drawText_(n,0,2,2)}}},e.prototype.drawMultiLineString=function(t){this.squaredTolerance_&&(t=t.simplifyTransformed(this.squaredTolerance_,this.userTransform_));var e=t.getExtent();if(je(this.extent_,e)){if(this.strokeState_){this.setContextStrokeState_(this.strokeState_);var i=this.context_,n=t.getFlatCoordinates(),o=0,r=t.getEnds(),s=t.getStride();i.beginPath();for(var a=0,l=r.length;a<l;++a)o=this.moveToLineTo_(n,o,r[a],s,!1);i.stroke()}if(""!==this.text_){var h=t.getFlatMidpoints();this.drawText_(h,0,h.length,2)}}},e.prototype.drawPolygon=function(t){if(this.squaredTolerance_&&(t=t.simplifyTransformed(this.squaredTolerance_,this.userTransform_)),je(this.extent_,t.getExtent())){if(this.strokeState_||this.fillState_){this.fillState_&&this.setContextFillState_(this.fillState_),this.strokeState_&&this.setContextStrokeState_(this.strokeState_);var e=this.context_;e.beginPath(),this.drawRings_(t.getOrientedFlatCoordinates(),0,t.getEnds(),t.getStride()),this.fillState_&&e.fill(),this.strokeState_&&e.stroke()}if(""!==this.text_){var i=t.getFlatInteriorPoint();this.drawText_(i,0,2,2)}}},e.prototype.drawMultiPolygon=function(t){if(this.squaredTolerance_&&(t=t.simplifyTransformed(this.squaredTolerance_,this.userTransform_)),je(this.extent_,t.getExtent())){if(this.strokeState_||this.fillState_){this.fillState_&&this.setContextFillState_(this.fillState_),this.strokeState_&&this.setContextStrokeState_(this.strokeState_);var e=this.context_,i=t.getOrientedFlatCoordinates(),n=0,o=t.getEndss(),r=t.getStride();e.beginPath();for(var s=0,a=o.length;s<a;++s){var l=o[s];n=this.drawRings_(i,n,l,r)}this.fillState_&&e.fill(),this.strokeState_&&e.stroke()}if(""!==this.text_){var h=t.getFlatInteriorPoints();this.drawText_(h,0,h.length,2)}}},e.prototype.setContextFillState_=function(t){var e=this.context_,i=this.contextFillState_;i?i.fillStyle!=t.fillStyle&&(i.fillStyle=t.fillStyle,e.fillStyle=t.fillStyle):(e.fillStyle=t.fillStyle,this.contextFillState_={fillStyle:t.fillStyle})},e.prototype.setContextStrokeState_=function(t){var e=this.context_,i=this.contextStrokeState_;i?(i.lineCap!=t.lineCap&&(i.lineCap=t.lineCap,e.lineCap=t.lineCap),e.setLineDash&&(h(i.lineDash,t.lineDash)||e.setLineDash(i.lineDash=t.lineDash),i.lineDashOffset!=t.lineDashOffset&&(i.lineDashOffset=t.lineDashOffset,e.lineDashOffset=t.lineDashOffset)),i.lineJoin!=t.lineJoin&&(i.lineJoin=t.lineJoin,e.lineJoin=t.lineJoin),i.lineWidth!=t.lineWidth&&(i.lineWidth=t.lineWidth,e.lineWidth=t.lineWidth),i.miterLimit!=t.miterLimit&&(i.miterLimit=t.miterLimit,e.miterLimit=t.miterLimit),i.strokeStyle!=t.strokeStyle&&(i.strokeStyle=t.strokeStyle,e.strokeStyle=t.strokeStyle)):(e.lineCap=t.lineCap,e.setLineDash&&(e.setLineDash(t.lineDash),e.lineDashOffset=t.lineDashOffset),e.lineJoin=t.lineJoin,e.lineWidth=t.lineWidth,e.miterLimit=t.miterLimit,e.strokeStyle=t.strokeStyle,this.contextStrokeState_={lineCap:t.lineCap,lineDash:t.lineDash,lineDashOffset:t.lineDashOffset,lineJoin:t.lineJoin,lineWidth:t.lineWidth,miterLimit:t.miterLimit,strokeStyle:t.strokeStyle})},e.prototype.setContextTextState_=function(t){var e=this.context_,i=this.contextTextState_,n=t.textAlign?t.textAlign:Uo;i?(i.font!=t.font&&(i.font=t.font,e.font=t.font),i.textAlign!=n&&(i.textAlign=n,e.textAlign=n),i.textBaseline!=t.textBaseline&&(i.textBaseline=t.textBaseline,e.textBaseline=t.textBaseline)):(e.font=t.font,e.textAlign=n,e.textBaseline=t.textBaseline,this.contextTextState_={font:t.font,textAlign:n,textBaseline:t.textBaseline})},e.prototype.setFillStrokeStyle=function(t,e){var i=this;if(t){var n=t.getColor();this.fillState_={fillStyle:Xo(n||Yo)}}else this.fillState_=null;if(e){var o=e.getColor(),r=e.getLineCap(),s=e.getLineDash(),a=e.getLineDashOffset(),l=e.getLineJoin(),h=e.getWidth(),u=e.getMiterLimit(),c=s||Ko;this.strokeState_={lineCap:void 0!==r?r:Bo,lineDash:1===this.pixelRatio_?c:c.map((function(t){return t*i.pixelRatio_})),lineDashOffset:(a||0)*this.pixelRatio_,lineJoin:void 0!==l?l:Zo,lineWidth:(void 0!==h?h:1)*this.pixelRatio_,miterLimit:void 0!==u?u:10,strokeStyle:Xo(o||Vo)}}else this.strokeState_=null},e.prototype.setImageStyle=function(t){var e;if(t&&(e=t.getSize())){var i=t.getAnchor(),n=t.getOrigin();this.image_=t.getImage(this.pixelRatio_),this.imageAnchorX_=i[0]*this.pixelRatio_,this.imageAnchorY_=i[1]*this.pixelRatio_,this.imageHeight_=e[1]*this.pixelRatio_,this.imageOpacity_=t.getOpacity(),this.imageOriginX_=n[0],this.imageOriginY_=n[1],this.imageRotateWithView_=t.getRotateWithView(),this.imageRotation_=t.getRotation(),this.imageScale_=t.getScaleArray(),this.imageWidth_=e[0]*this.pixelRatio_}else this.image_=null},e.prototype.setTextStyle=function(t){if(t){var e=t.getFill();if(e){var i=e.getColor();this.textFillState_={fillStyle:Xo(i||Yo)}}else this.textFillState_=null;var n=t.getStroke();if(n){var o=n.getColor(),r=n.getLineCap(),s=n.getLineDash(),a=n.getLineDashOffset(),l=n.getLineJoin(),h=n.getWidth(),u=n.getMiterLimit();this.textStrokeState_={lineCap:void 0!==r?r:Bo,lineDash:s||Ko,lineDashOffset:a||0,lineJoin:void 0!==l?l:Zo,lineWidth:void 0!==h?h:1,miterLimit:void 0!==u?u:10,strokeStyle:Xo(o||Vo)}}else this.textStrokeState_=null;var c=t.getFont(),p=t.getOffsetX(),f=t.getOffsetY(),d=t.getRotateWithView(),g=t.getRotation(),_=t.getScaleArray(),y=t.getText(),v=t.getTextAlign(),m=t.getTextBaseline();this.textState_={font:void 0!==c?c:No,textAlign:void 0!==v?v:Uo,textBaseline:void 0!==m?m:Ho},this.text_=void 0!==y?Array.isArray(y)?y.reduce((function(t,e,i){return t+(i%2?" ":e)}),""):y:"",this.textOffsetX_=void 0!==p?this.pixelRatio_*p:0,this.textOffsetY_=void 0!==f?this.pixelRatio_*f:0,this.textRotateWithView_=void 0!==d&&d,this.textRotation_=void 0!==g?g:0,this.textScale_=[this.pixelRatio_*_[0],this.pixelRatio_*_[1]]}else this.text_=""},e}(Or),ls=as,hs="fraction",us="pixels",cs="bottom-left",ps="bottom-right",fs="top-left",ds="top-right";function gs(t,e,i){return e+":"+t+":"+(i?to(i):"null")}var _s=new(function(){function t(){this.cache_={},this.cacheSize_=0,this.maxCacheSize_=32}return t.prototype.clear=function(){this.cache_={},this.cacheSize_=0},t.prototype.canExpireCache=function(){return this.cacheSize_>this.maxCacheSize_},t.prototype.expire=function(){if(this.canExpireCache()){var t=0;for(var e in this.cache_){var i=this.cache_[e];0!=(3&t++)||i.hasListener()||(delete this.cache_[e],--this.cacheSize_)}}},t.prototype.get=function(t,e,i){var n=gs(t,e,i);return n in this.cache_?this.cache_[n]:null},t.prototype.set=function(t,e,i,n){var o=gs(t,e,i);this.cache_[o]=n,++this.cacheSize_},t.prototype.setSize=function(t){this.maxCacheSize_=t,this.expire()},t}()),ys=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),vs=null,ms=function(t){function e(e,i,n,o,r,s){var a=t.call(this)||this;return a.hitDetectionImage_=null,a.image_=e||new Image,null!==o&&(a.image_.crossOrigin=o),a.canvas_={},a.color_=s,a.unlisten_=null,a.imageState_=r,a.size_=n,a.src_=i,a.tainted_,a}return ys(e,t),e.prototype.isTainted_=function(){if(void 0===this.tainted_&&2===this.imageState_){vs||(vs=q(1,1)),vs.drawImage(this.image_,0,0);try{vs.getImageData(0,0,1,1),this.tainted_=!1}catch(t){vs=null,this.tainted_=!0}}return!0===this.tainted_},e.prototype.dispatchChangeEvent_=function(){this.dispatchEvent(x)},e.prototype.handleImageError_=function(){this.imageState_=3,this.unlistenImage_(),this.dispatchChangeEvent_()},e.prototype.handleImageLoad_=function(){this.imageState_=2,this.size_?(this.image_.width=this.size_[0],this.image_.height=this.size_[1]):this.size_=[this.image_.width,this.image_.height],this.unlistenImage_(),this.dispatchChangeEvent_()},e.prototype.getImage=function(t){return this.replaceColor_(t),this.canvas_[t]?this.canvas_[t]:this.image_},e.prototype.getPixelRatio=function(t){return this.replaceColor_(t),this.canvas_[t]?t:1},e.prototype.getImageState=function(){return this.imageState_},e.prototype.getHitDetectionImage=function(){if(!this.hitDetectionImage_)if(this.isTainted_()){var t=this.size_[0],e=this.size_[1],i=q(t,e);i.fillRect(0,0,t,e),this.hitDetectionImage_=i.canvas}else this.hitDetectionImage_=this.image_;return this.hitDetectionImage_},e.prototype.getSize=function(){return this.size_},e.prototype.getSrc=function(){return this.src_},e.prototype.load=function(){if(0==this.imageState_){this.imageState_=1;try{this.image_.src=this.src_}catch(t){this.handleImageError_()}this.unlisten_=_o(this.image_,this.handleImageLoad_.bind(this),this.handleImageError_.bind(this))}},e.prototype.replaceColor_=function(t){if(this.color_&&!this.canvas_[t]&&2===this.imageState_){var e=document.createElement("canvas");this.canvas_[t]=e,e.width=Math.ceil(this.image_.width*t),e.height=Math.ceil(this.image_.height*t);var i=e.getContext("2d");if(i.scale(t,t),i.drawImage(this.image_,0,0),i.globalCompositeOperation="multiply","multiply"===i.globalCompositeOperation||this.isTainted_())i.fillStyle=to(this.color_),i.fillRect(0,0,e.width/t,e.height/t),i.globalCompositeOperation="destination-in",i.drawImage(this.image_,0,0);else{for(var n=i.getImageData(0,0,e.width,e.height),o=n.data,r=this.color_[0]/255,s=this.color_[1]/255,a=this.color_[2]/255,l=this.color_[3],h=0,u=o.length;h<u;h+=4)o[h]*=r,o[h+1]*=s,o[h+2]*=a,o[h+3]*=l;i.putImageData(n,0,0)}}},e.prototype.unlistenImage_=function(){this.unlisten_&&(this.unlisten_(),this.unlisten_=null)},e}(m),xs=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Cs=function(t){function e(e){var i=this,n=e||{},o=void 0!==n.opacity?n.opacity:1,r=void 0!==n.rotation?n.rotation:0,s=void 0!==n.scale?n.scale:1,a=void 0!==n.rotateWithView&&n.rotateWithView;(i=t.call(this,{opacity:o,rotation:r,scale:s,displacement:void 0!==n.displacement?n.displacement:[0,0],rotateWithView:a,declutterMode:n.declutterMode})||this).anchor_=void 0!==n.anchor?n.anchor:[.5,.5],i.normalizedAnchor_=null,i.anchorOrigin_=void 0!==n.anchorOrigin?n.anchorOrigin:fs,i.anchorXUnits_=void 0!==n.anchorXUnits?n.anchorXUnits:hs,i.anchorYUnits_=void 0!==n.anchorYUnits?n.anchorYUnits:hs,i.crossOrigin_=void 0!==n.crossOrigin?n.crossOrigin:null;var l=void 0!==n.img?n.img:null;i.imgSize_=n.imgSize;var h=n.src;xt(!(void 0!==h&&l),4),xt(!l||l&&i.imgSize_,5),void 0!==h&&0!==h.length||!l||(h=l.src||D(l)),xt(void 0!==h&&h.length>0,6);var u=void 0!==n.src?0:2;return i.color_=void 0!==n.color?io(n.color):null,i.iconImage_=function(t,e,i,n,o,r){var s=_s.get(e,n,r);return s||(s=new ms(t,e,i,n,o,r),_s.set(e,n,r,s)),s}(l,h,void 0!==i.imgSize_?i.imgSize_:null,i.crossOrigin_,u,i.color_),i.offset_=void 0!==n.offset?n.offset:[0,0],i.offsetOrigin_=void 0!==n.offsetOrigin?n.offsetOrigin:fs,i.origin_=null,i.size_=void 0!==n.size?n.size:null,i}return xs(e,t),e.prototype.clone=function(){var t=this.getScale();return new e({anchor:this.anchor_.slice(),anchorOrigin:this.anchorOrigin_,anchorXUnits:this.anchorXUnits_,anchorYUnits:this.anchorYUnits_,color:this.color_&&this.color_.slice?this.color_.slice():this.color_||void 0,crossOrigin:this.crossOrigin_,imgSize:this.imgSize_,offset:this.offset_.slice(),offsetOrigin:this.offsetOrigin_,opacity:this.getOpacity(),rotateWithView:this.getRotateWithView(),rotation:this.getRotation(),scale:Array.isArray(t)?t.slice():t,size:null!==this.size_?this.size_.slice():void 0,src:this.getSrc(),displacement:this.getDisplacement().slice(),declutterMode:this.getDeclutterMode()})},e.prototype.getAnchor=function(){var t=this.normalizedAnchor_;if(!t){t=this.anchor_;var e=this.getSize();if(this.anchorXUnits_==hs||this.anchorYUnits_==hs){if(!e)return null;t=this.anchor_.slice(),this.anchorXUnits_==hs&&(t[0]*=e[0]),this.anchorYUnits_==hs&&(t[1]*=e[1])}if(this.anchorOrigin_!=fs){if(!e)return null;t===this.anchor_&&(t=this.anchor_.slice()),this.anchorOrigin_!=ds&&this.anchorOrigin_!=ps||(t[0]=-t[0]+e[0]),this.anchorOrigin_!=cs&&this.anchorOrigin_!=ps||(t[1]=-t[1]+e[1])}this.normalizedAnchor_=t}var i=this.getDisplacement();return[t[0]-i[0],t[1]+i[1]]},e.prototype.setAnchor=function(t){this.anchor_=t,this.normalizedAnchor_=null},e.prototype.getColor=function(){return this.color_},e.prototype.getImage=function(t){return this.iconImage_.getImage(t)},e.prototype.getPixelRatio=function(t){return this.iconImage_.getPixelRatio(t)},e.prototype.getImageSize=function(){return this.iconImage_.getSize()},e.prototype.getImageState=function(){return this.iconImage_.getImageState()},e.prototype.getHitDetectionImage=function(){return this.iconImage_.getHitDetectionImage()},e.prototype.getOrigin=function(){if(this.origin_)return this.origin_;var t=this.offset_;if(this.offsetOrigin_!=fs){var e=this.getSize(),i=this.iconImage_.getSize();if(!e||!i)return null;t=t.slice(),this.offsetOrigin_!=ds&&this.offsetOrigin_!=ps||(t[0]=i[0]-e[0]-t[0]),this.offsetOrigin_!=cs&&this.offsetOrigin_!=ps||(t[1]=i[1]-e[1]-t[1])}return this.origin_=t,this.origin_},e.prototype.getSrc=function(){return this.iconImage_.getSrc()},e.prototype.getSize=function(){return this.size_?this.size_:this.iconImage_.getSize()},e.prototype.listenImageChange=function(t){this.iconImage_.addEventListener(x,t)},e.prototype.load=function(){this.iconImage_.load()},e.prototype.unlistenImageChange=function(t){this.iconImage_.removeEventListener(x,t)},e}(Wo),ws=.5,Ss={Point:function(t,e,i,n,o){var r,s=i.getImage(),a=i.getText();if(s){if(2!=s.getImageState())return;var l=t;if(o){var h=s.getDeclutterMode();if("none"!==h)if(l=o,"obstacle"===h){var u=t.getBuilder(i.getZIndex(),"Image");u.setImageStyle(s,r),u.drawPoint(e,n)}else a&&a.getText()&&(r={})}var c=l.getBuilder(i.getZIndex(),"Image");c.setImageStyle(s,r),c.drawPoint(e,n)}if(a&&a.getText()){var p=t;o&&(p=o);var f=p.getBuilder(i.getZIndex(),"Text");f.setTextStyle(a,r),f.drawText(e,n)}},LineString:function(t,e,i,n,o){var r=i.getStroke();if(r){var s=t.getBuilder(i.getZIndex(),"LineString");s.setFillStrokeStyle(null,r),s.drawLineString(e,n)}var a=i.getText();if(a&&a.getText()){var l=(o||t).getBuilder(i.getZIndex(),"Text");l.setTextStyle(a),l.drawText(e,n)}},Polygon:function(t,e,i,n,o){var r=i.getFill(),s=i.getStroke();if(r||s){var a=t.getBuilder(i.getZIndex(),"Polygon");a.setFillStrokeStyle(r,s),a.drawPolygon(e,n)}var l=i.getText();if(l&&l.getText()){var h=(o||t).getBuilder(i.getZIndex(),"Text");h.setTextStyle(l),h.drawText(e,n)}},MultiPoint:function(t,e,i,n,o){var r,s=i.getImage(),a=i.getText();if(s){if(2!=s.getImageState())return;var l=t;if(o){var h=s.getDeclutterMode();if("none"!==h)if(l=o,"obstacle"===h){var u=t.getBuilder(i.getZIndex(),"Image");u.setImageStyle(s,r),u.drawMultiPoint(e,n)}else a&&a.getText()&&(r={})}var c=l.getBuilder(i.getZIndex(),"Image");c.setImageStyle(s,r),c.drawMultiPoint(e,n)}if(a&&a.getText()){var p=t;o&&(p=o);var f=p.getBuilder(i.getZIndex(),"Text");f.setTextStyle(a,r),f.drawText(e,n)}},MultiLineString:function(t,e,i,n,o){var r=i.getStroke();if(r){var s=t.getBuilder(i.getZIndex(),"LineString");s.setFillStrokeStyle(null,r),s.drawMultiLineString(e,n)}var a=i.getText();if(a&&a.getText()){var l=(o||t).getBuilder(i.getZIndex(),"Text");l.setTextStyle(a),l.drawText(e,n)}},MultiPolygon:function(t,e,i,n,o){var r=i.getFill(),s=i.getStroke();if(s||r){var a=t.getBuilder(i.getZIndex(),"Polygon");a.setFillStrokeStyle(r,s),a.drawMultiPolygon(e,n)}var l=i.getText();if(l&&l.getText()){var h=(o||t).getBuilder(i.getZIndex(),"Text");h.setTextStyle(l),h.drawText(e,n)}},GeometryCollection:function(t,e,i,n,o){var r,s,a=e.getGeometriesArray();for(r=0,s=a.length;r<s;++r)(0,Ss[a[r].getType()])(t,a[r],i,n,o)},Circle:function(t,e,i,n,o){var r=i.getFill(),s=i.getStroke();if(r||s){var a=t.getBuilder(i.getZIndex(),"Circle");a.setFillStrokeStyle(r,s),a.drawCircle(e,n)}var l=i.getText();if(l&&l.getText()){var h=(o||t).getBuilder(i.getZIndex(),"Text");h.setTextStyle(l),h.drawText(e,n)}}};function Es(t,e){return parseInt(D(t),10)-parseInt(D(e),10)}function Ts(t,e){return.5*t/e}function bs(t,e,i,n,o,r,s){var a=!1,l=i.getImage();if(l){var h=l.getImageState();2==h||3==h?l.unlistenImageChange(o):(0==h&&l.load(),l.listenImageChange(o),a=!0)}return function(t,e,i,n,o,r){var s=i.getGeometryFunction()(e);if(s){var a=s.simplifyTransformed(n,o);i.getRenderer()?Os(t,a,i,e):(0,Ss[a.getType()])(t,a,i,e,r)}}(t,e,i,n,r,s),a}function Os(t,e,i,n){if("GeometryCollection"!=e.getType())t.getBuilder(i.getZIndex(),"Default").drawCustom(e,n,i.getRenderer(),i.getHitDetectionRenderer());else for(var o=e.getGeometries(),r=0,s=o.length;r<s;++r)Os(t,o[r],i,n)}var Rs=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ps=function(t){function e(e){var i=t.call(this,e)||this;return i.boundHandleStyleImageChange_=i.handleStyleImageChange_.bind(i),i.animatingOrInteracting_,i.hitDetectionImageData_=null,i.renderedFeatures_=null,i.renderedRevision_=-1,i.renderedResolution_=NaN,i.renderedExtent_=[1/0,1/0,-1/0,-1/0],i.wrappedRenderedExtent_=[1/0,1/0,-1/0,-1/0],i.renderedRotation_,i.renderedCenter_=null,i.renderedProjection_=null,i.renderedRenderOrder_=null,i.replayGroup_=null,i.replayGroupChanged=!0,i.declutterExecutorGroup=null,i.clipping=!0,i.compositionContext_=null,i.opacity_=1,i}return Rs(e,t),e.prototype.renderWorlds=function(t,e,i){var n=e.extent,o=e.viewState,r=o.center,s=o.resolution,a=o.projection,l=o.rotation,h=a.getExtent(),u=this.getLayer().getSource(),c=e.pixelRatio,p=e.viewHints,f=!(p[0]||p[1]),d=this.compositionContext_,g=Math.round(e.size[0]*c),_=Math.round(e.size[1]*c),y=u.getWrapX()&&a.canWrapX(),v=y?ke(h):null,m=y?Math.ceil((n[2]-h[2])/v)+1:1,x=y?Math.floor((n[0]-h[0])/v):0;do{var C=this.getRenderTransform(r,s,l,c,g,_,x*v);t.execute(d,1,C,l,f,void 0,i)}while(++x<m)},e.prototype.setupCompositionContext_=function(){if(1!==this.opacity_){var t=q(this.context.canvas.width,this.context.canvas.height,so);this.compositionContext_=t}else this.compositionContext_=this.context},e.prototype.releaseCompositionContext_=function(){if(1!==this.opacity_){var t=this.context.globalAlpha;this.context.globalAlpha=this.opacity_,this.context.drawImage(this.compositionContext_.canvas,0,0),this.context.globalAlpha=t,J(this.compositionContext_),so.push(this.compositionContext_.canvas),this.compositionContext_=null}},e.prototype.renderDeclutter=function(t){this.declutterExecutorGroup&&(this.setupCompositionContext_(),this.renderWorlds(this.declutterExecutorGroup,t,t.declutterTree),this.releaseCompositionContext_())},e.prototype.renderFrame=function(t,e){var i=t.pixelRatio,n=t.layerStatesArray[t.layerIndex];!function(t,e,i){!function(t,e,i,n,o,r,s){t[0]=e,t[1]=i,t[2]=n,t[3]=o,t[4]=r,t[5]=s}(t,e,0,0,i,0,0)}(this.pixelTransform,1/i,1/i),Mi(this.inversePixelTransform,this.pixelTransform);var o=Fi(this.pixelTransform);this.useContainer(e,o,this.getBackground(t));var r=this.context,s=r.canvas,a=this.replayGroup_,l=this.declutterExecutorGroup;if((!a||a.isEmpty())&&(!l||l.isEmpty()))return null;var h=Math.round(t.size[0]*i),u=Math.round(t.size[1]*i);s.width!=h||s.height!=u?(s.width=h,s.height=u,s.style.transform!==o&&(s.style.transform=o)):this.containerReused||r.clearRect(0,0,h,u),this.preRender(r,t);var c=t.viewState;c.projection;this.opacity_=n.opacity,this.setupCompositionContext_();var p=!1,f=!0;if(n.extent&&this.clipping){var d=pi(n.extent);(p=(f=je(d,t.extent))&&!de(d,t.extent))&&this.clipUnrotated(this.compositionContext_,t,d)}return f&&this.renderWorlds(a,t),p&&this.compositionContext_.restore(),this.releaseCompositionContext_(),this.postRender(r,t),this.renderedRotation_!==c.rotation&&(this.renderedRotation_=c.rotation,this.hitDetectionImageData_=null),this.container},e.prototype.getFeatures=function(t){return new Promise(function(e){if(!this.hitDetectionImageData_&&!this.animatingOrInteracting_){var i=[this.context.canvas.width,this.context.canvas.height];Pi(this.pixelTransform,i);var n=this.renderedCenter_,o=this.renderedResolution_,s=this.renderedRotation_,a=this.renderedProjection_,l=this.wrappedRenderedExtent_,h=this.getLayer(),u=[],c=i[0]*ws,p=i[1]*ws;u.push(this.getRenderTransform(n,o,s,ws,c,p,0).slice());var f=h.getSource(),d=a.getExtent();if(f.getWrapX()&&a.canWrapX()&&!de(d,l)){for(var g=l[0],_=ke(d),y=0,v=void 0;g<d[0];)v=_*--y,u.push(this.getRenderTransform(n,o,s,ws,c,p,v).slice()),g+=_;for(y=0,g=l[2];g>d[2];)v=_*++y,u.push(this.getRenderTransform(n,o,s,ws,c,p,v).slice()),g-=_}this.hitDetectionImageData_=function(t,e,i,n,o,s,a){var l=q(t[0]*ws,t[1]*ws);l.imageSmoothingEnabled=!1;for(var h=l.canvas,u=new ls(l,ws,o,null,a),c=i.length,p=Math.floor(16777215/c),f={},d=1;d<=c;++d){var g=i[d-1],_=g.getStyleFunction()||n;if(n){var y=_(g,s);if(y){Array.isArray(y)||(y=[y]);for(var v="#"+("000000"+(d*p).toString(16)).slice(-6),m=0,x=y.length;m<x;++m){var C=y[m],w=C.getGeometryFunction()(g);if(w&&je(o,w.getExtent())){var S=C.clone(),E=S.getFill();E&&E.setColor(v);var T=S.getStroke();T&&(T.setColor(v),T.setLineDash(null)),S.setText(void 0);var b=C.getImage();if(b&&0!==b.getOpacity()){var O=b.getImageSize();if(!O)continue;var R=q(O[0],O[1],void 0,{alpha:!1}),P=R.canvas;R.fillStyle=v,R.fillRect(0,0,P.width,P.height),S.setImage(new Cs({img:P,imgSize:O,anchor:b.getAnchor(),anchorXUnits:us,anchorYUnits:us,offset:b.getOrigin(),opacity:1,size:b.getSize(),scale:b.getScale(),rotation:b.getRotation(),rotateWithView:b.getRotateWithView()}))}var I=S.getZIndex()||0;(L=f[I])||(L={},f[I]=L,L.Polygon=[],L.Circle=[],L.LineString=[],L.Point=[]),L[w.getType().replace("Multi","")].push(w,S)}}}}}for(var M=Object.keys(f).map(Number).sort(r),F=(d=0,M.length);d<F;++d){var L=f[M[d]];for(var A in L){var D=L[A];for(m=0,x=D.length;m<x;m+=2){u.setStyle(D[m+1]);for(var k=0,j=e.length;k<j;++k)u.setTransform(e[k]),u.drawGeometry(D[m])}}}return l.getImageData(0,0,h.width,h.height)}(i,u,this.renderedFeatures_,h.getStyleFunction(),l,o,s)}e(function(t,e,i){var n=[];if(i){var o=Math.floor(Math.round(t[0])*ws),r=Math.floor(Math.round(t[1])*ws),s=4*(Ct(o,0,i.width-1)+Ct(r,0,i.height-1)*i.width),a=i.data[s],l=i.data[s+1],h=i.data[s+2]+256*(l+256*a),u=Math.floor(16777215/e.length);h&&h%u==0&&n.push(e[h/u-1])}return n}(t,this.renderedFeatures_,this.hitDetectionImageData_))}.bind(this))},e.prototype.forEachFeatureAtCoordinate=function(t,e,i,n,o){var r=this;if(this.replayGroup_){var s,a=e.viewState.resolution,l=e.viewState.rotation,h=this.getLayer(),u={},c=function(t,e,i){var r=D(t),s=u[r];if(s){if(!0!==s&&i<s.distanceSq){if(0===i)return u[r]=!0,o.splice(o.lastIndexOf(s),1),n(t,h,e);s.geometry=e,s.distanceSq=i}}else{if(0===i)return u[r]=!0,n(t,h,e);o.push(u[r]={feature:t,layer:h,geometry:e,distanceSq:i,callback:n})}},p=[this.replayGroup_];return this.declutterExecutorGroup&&p.push(this.declutterExecutorGroup),p.some((function(n){return s=n.forEachFeatureAtCoordinate(t,a,l,i,c,n===r.declutterExecutorGroup&&e.declutterTree?e.declutterTree.all().map((function(t){return t.value})):null)})),s}},e.prototype.handleFontsChanged=function(){var t=this.getLayer();t.getVisible()&&this.replayGroup_&&t.changed()},e.prototype.handleStyleImageChange_=function(t){this.renderIfReadyAndVisible()},e.prototype.prepareFrame=function(t){var e=this.getLayer(),i=e.getSource();if(!i)return!1;var n=t.viewHints[0],o=t.viewHints[1],r=e.getUpdateWhileAnimating(),s=e.getUpdateWhileInteracting();if(this.ready&&!r&&n||!s&&o)return this.animatingOrInteracting_=!0,!0;this.animatingOrInteracting_=!1;var a=t.extent,l=t.viewState,u=l.projection,c=l.resolution,p=t.pixelRatio,f=e.getRevision(),d=e.getRenderBuffer(),g=e.getRenderOrder();void 0===g&&(g=Es);var _=l.center.slice(),y=ue(a,d*c),v=y.slice(),m=[y.slice()],x=u.getExtent();if(i.getWrapX()&&u.canWrapX()&&!de(x,t.extent)){var C=ke(x),w=Math.max(ke(y)/2,C);y[0]=x[0]-w,y[2]=x[2]+w,Ne(_,u);var S=ze(m[0],u);S[0]<x[0]&&S[2]<x[2]?m.push([S[0]+C,S[1],S[2]+C,S[3]]):S[0]>x[0]&&S[2]>x[2]&&m.push([S[0]-C,S[1],S[2]-C,S[3]])}if(this.ready&&this.renderedResolution_==c&&this.renderedRevision_==f&&this.renderedRenderOrder_==g&&de(this.wrappedRenderedExtent_,y))return h(this.renderedExtent_,v)||(this.hitDetectionImageData_=null,this.renderedExtent_=v),this.renderedCenter_=_,this.replayGroupChanged=!1,!0;this.replayGroup_=null;var E,T=new Yr(Ts(c,p),y,c,p);this.getLayer().getDeclutter()&&(E=new Yr(Ts(c,p),y,c,p));var b,O=li();if(O){for(var R=0,P=m.length;R<P;++R){var I=ci(m[R]);i.loadFeatures(I,fi(c),O)}b=ti(O,u)}else for(R=0,P=m.length;R<P;++R)i.loadFeatures(m[R],c,u);var M=function(t,e){var i=Ts(t,e);return i*i}(c,p),F=!0,L=function(t){var i,n=t.getStyleFunction()||e.getStyleFunction();if(n&&(i=n(t,c)),i){var o=this.renderFeature(t,M,i,T,b,E);F=F&&!o}}.bind(this),A=ci(y),D=i.getFeaturesInExtent(A);for(g&&D.sort(g),R=0,P=D.length;R<P;++R)L(D[R]);this.renderedFeatures_=D,this.ready=F;var k=T.finish(),j=new rs(y,c,p,i.getOverlaps(),k,e.getRenderBuffer());return E&&(this.declutterExecutorGroup=new rs(y,c,p,i.getOverlaps(),E.finish(),e.getRenderBuffer())),this.renderedResolution_=c,this.renderedRevision_=f,this.renderedRenderOrder_=g,this.renderedExtent_=v,this.wrappedRenderedExtent_=y,this.renderedCenter_=_,this.renderedProjection_=u,this.replayGroup_=j,this.hitDetectionImageData_=null,this.replayGroupChanged=!0,!0},e.prototype.renderFeature=function(t,e,i,n,o,r){if(!i)return!1;var s=!1;if(Array.isArray(i))for(var a=0,l=i.length;a<l;++a)s=bs(n,t,i[a],e,this.boundHandleStyleImageChange_,o,r)||s;else s=bs(n,t,i,e,this.boundHandleStyleImageChange_,o,r);return s},e}(ho),Is=Ps,Ms=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Fs=function(t){function e(e){return t.call(this,e)||this}return Ms(e,t),e.prototype.createRenderer=function(){return new Is(this)},e}(xr),Ls=function(){function t(t){this.highWaterMark=void 0!==t?t:2048,this.count_=0,this.entries_={},this.oldest_=null,this.newest_=null}return t.prototype.canExpireCache=function(){return this.highWaterMark>0&&this.getCount()>this.highWaterMark},t.prototype.expireCache=function(t){for(;this.canExpireCache();)this.pop()},t.prototype.clear=function(){this.count_=0,this.entries_={},this.oldest_=null,this.newest_=null},t.prototype.containsKey=function(t){return this.entries_.hasOwnProperty(t)},t.prototype.forEach=function(t){for(var e=this.oldest_;e;)t(e.value_,e.key_,this),e=e.newer},t.prototype.get=function(t,e){var i=this.entries_[t];return xt(void 0!==i,15),i===this.newest_||(i===this.oldest_?(this.oldest_=this.oldest_.newer,this.oldest_.older=null):(i.newer.older=i.older,i.older.newer=i.newer),i.newer=null,i.older=this.newest_,this.newest_.newer=i,this.newest_=i),i.value_},t.prototype.remove=function(t){var e=this.entries_[t];return xt(void 0!==e,15),e===this.newest_?(this.newest_=e.older,this.newest_&&(this.newest_.newer=null)):e===this.oldest_?(this.oldest_=e.newer,this.oldest_&&(this.oldest_.older=null)):(e.newer.older=e.older,e.older.newer=e.newer),delete this.entries_[t],--this.count_,e.value_},t.prototype.getCount=function(){return this.count_},t.prototype.getKeys=function(){var t,e=new Array(this.count_),i=0;for(t=this.newest_;t;t=t.older)e[i++]=t.key_;return e},t.prototype.getValues=function(){var t,e=new Array(this.count_),i=0;for(t=this.newest_;t;t=t.older)e[i++]=t.value_;return e},t.prototype.peekLast=function(){return this.oldest_.value_},t.prototype.peekLastKey=function(){return this.oldest_.key_},t.prototype.peekFirstKey=function(){return this.newest_.key_},t.prototype.peek=function(t){if(this.containsKey(t))return this.entries_[t].value_},t.prototype.pop=function(){var t=this.oldest_;return delete this.entries_[t.key_],t.newer&&(t.newer.older=null),this.oldest_=t.newer,this.oldest_||(this.newest_=null),--this.count_,t.value_},t.prototype.replace=function(t,e){this.get(t),this.entries_[t].value_=e},t.prototype.set=function(t,e){xt(!(t in this.entries_),16);var i={key_:t,newer:null,older:this.newest_,value_:e};this.newest_?this.newest_.newer=i:this.oldest_=i,this.newest_=i,this.entries_[t]=i,++this.count_},t.prototype.setSize=function(t){this.highWaterMark=t},t}();function As(t,e,i,n){return void 0!==n?(n[0]=t,n[1]=e,n[2]=i,n):[t,e,i]}function Ds(t,e,i){return t+"/"+e+"/"+i}function ks(t){return Ds(t[0],t[1],t[2])}var js=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Gs=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return js(e,t),e.prototype.expireCache=function(t){for(;this.canExpireCache()&&!(this.peekLast().getKey()in t);)this.pop().release()},e.prototype.pruneExceptNewestZ=function(){if(0!==this.getCount()){var t=this.peekFirstKey().split("/").map(Number)[0];this.forEach(function(e){e.tileCoord[0]!==t&&(this.remove(ks(e.tileCoord)),e.release())}.bind(this))}},e}(Ls),zs=Gs,Ws=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function Xs(t){return t?Array.isArray(t)?function(e){return t}:"function"==typeof t?t:function(e){return[t]}:null}var Ns=function(t){function e(e){var i=t.call(this)||this;i.projection=He(e.projection),i.attributions_=Xs(e.attributions),i.attributionsCollapsible_=void 0===e.attributionsCollapsible||e.attributionsCollapsible,i.loading=!1,i.state_=void 0!==e.state?e.state:"ready",i.wrapX_=void 0!==e.wrapX&&e.wrapX,i.interpolate_=!!e.interpolate,i.viewResolver=null,i.viewRejector=null;var n=i;return i.viewPromise_=new Promise((function(t,e){n.viewResolver=t,n.viewRejector=e})),i}return Ws(e,t),e.prototype.getAttributions=function(){return this.attributions_},e.prototype.getAttributionsCollapsible=function(){return this.attributionsCollapsible_},e.prototype.getProjection=function(){return this.projection},e.prototype.getResolutions=function(){return L()},e.prototype.getView=function(){return this.viewPromise_},e.prototype.getState=function(){return this.state_},e.prototype.getWrapX=function(){return this.wrapX_},e.prototype.getInterpolate=function(){return this.interpolate_},e.prototype.refresh=function(){this.changed()},e.prototype.setAttributions=function(t){this.attributions_=Xs(t),this.changed()},e.prototype.setState=function(t){this.state_=t,this.changed()},e}(G),Ys=[0,0,0],Bs=function(){function t(t){var e,i,n;if(this.minZoom=void 0!==t.minZoom?t.minZoom:0,this.resolutions_=t.resolutions,xt((e=this.resolutions_,!0,i=function(t,e){return e-t}||r,e.every((function(t,n){if(0===n)return!0;var o=i(e[n-1],t);return!(o>0||0===o)}))),17),!t.origins)for(var o=0,s=this.resolutions_.length-1;o<s;++o)if(n){if(this.resolutions_[o]/this.resolutions_[o+1]!==n){n=void 0;break}}else n=this.resolutions_[o]/this.resolutions_[o+1];this.zoomFactor_=n,this.maxZoom=this.resolutions_.length-1,this.origin_=void 0!==t.origin?t.origin:null,this.origins_=null,void 0!==t.origins&&(this.origins_=t.origins,xt(this.origins_.length==this.resolutions_.length,20));var a=t.extent;void 0===a||this.origin_||this.origins_||(this.origin_=Ae(a)),xt(!this.origin_&&this.origins_||this.origin_&&!this.origins_,18),this.tileSizes_=null,void 0!==t.tileSizes&&(this.tileSizes_=t.tileSizes,xt(this.tileSizes_.length==this.resolutions_.length,19)),this.tileSize_=void 0!==t.tileSize?t.tileSize:this.tileSizes_?null:256,xt(!this.tileSize_&&this.tileSizes_||this.tileSize_&&!this.tileSizes_,22),this.extent_=void 0!==a?a:null,this.fullTileRanges_=null,this.tmpSize_=[0,0],this.tmpExtent_=[0,0,0,0],void 0!==t.sizes?this.fullTileRanges_=t.sizes.map((function(t,e){var i=new Mo(Math.min(0,t[0]),Math.max(t[0]-1,-1),Math.min(0,t[1]),Math.max(t[1]-1,-1));if(a){var n=this.getTileRangeForExtentAndZ(a,e);i.minX=Math.max(n.minX,i.minX),i.maxX=Math.min(n.maxX,i.maxX),i.minY=Math.max(n.minY,i.minY),i.maxY=Math.min(n.maxY,i.maxY)}return i}),this):a&&this.calculateTileRanges_(a)}return t.prototype.forEachTileCoord=function(t,e,i){for(var n=this.getTileRangeForExtentAndZ(t,e),o=n.minX,r=n.maxX;o<=r;++o)for(var s=n.minY,a=n.maxY;s<=a;++s)i([e,o,s])},t.prototype.forEachTileCoordParentTileRange=function(t,e,i,n){var o,r,s=null,a=t[0]-1;for(2===this.zoomFactor_?(o=t[1],r=t[2]):s=this.getTileCoordExtent(t,n);a>=this.minZoom;){if(e(a,2===this.zoomFactor_?Io(o=Math.floor(o/2),o,r=Math.floor(r/2),r,i):this.getTileRangeForExtentAndZ(s,a,i)))return!0;--a}return!1},t.prototype.getExtent=function(){return this.extent_},t.prototype.getMaxZoom=function(){return this.maxZoom},t.prototype.getMinZoom=function(){return this.minZoom},t.prototype.getOrigin=function(t){return this.origin_?this.origin_:this.origins_[t]},t.prototype.getResolution=function(t){return this.resolutions_[t]},t.prototype.getResolutions=function(){return this.resolutions_},t.prototype.getTileCoordChildTileRange=function(t,e,i){if(t[0]<this.maxZoom){if(2===this.zoomFactor_){var n=2*t[1],o=2*t[2];return Io(n,n+1,o,o+1,e)}var r=this.getTileCoordExtent(t,i||this.tmpExtent_);return this.getTileRangeForExtentAndZ(r,t[0]+1,e)}return null},t.prototype.getTileRangeForTileCoordAndZ=function(t,e,i){if(e>this.maxZoom||e<this.minZoom)return null;var n=t[0],o=t[1],r=t[2];if(e===n)return Io(o,r,o,r,i);if(this.zoomFactor_){var s=Math.pow(this.zoomFactor_,e-n),a=Math.floor(o*s),l=Math.floor(r*s);return e<n?Io(a,a,l,l,i):Io(a,Math.floor(s*(o+1))-1,l,Math.floor(s*(r+1))-1,i)}var h=this.getTileCoordExtent(t,this.tmpExtent_);return this.getTileRangeForExtentAndZ(h,e,i)},t.prototype.getTileRangeExtent=function(t,e,i){var n=this.getOrigin(t),o=this.getResolution(t),r=Lo(this.getTileSize(t),this.tmpSize_),s=n[0]+e.minX*r[0]*o,a=n[0]+(e.maxX+1)*r[0]*o;return ye(s,n[1]+e.minY*r[1]*o,a,n[1]+(e.maxY+1)*r[1]*o,i)},t.prototype.getTileRangeForExtentAndZ=function(t,e,i){var n=Ys;this.getTileCoordForXYAndZ_(t[0],t[3],e,!1,n);var o=n[1],r=n[2];return this.getTileCoordForXYAndZ_(t[2],t[1],e,!0,n),Io(o,n[1],r,n[2],i)},t.prototype.getTileCoordCenter=function(t){var e=this.getOrigin(t[0]),i=this.getResolution(t[0]),n=Lo(this.getTileSize(t[0]),this.tmpSize_);return[e[0]+(t[1]+.5)*n[0]*i,e[1]-(t[2]+.5)*n[1]*i]},t.prototype.getTileCoordExtent=function(t,e){var i=this.getOrigin(t[0]),n=this.getResolution(t[0]),o=Lo(this.getTileSize(t[0]),this.tmpSize_),r=i[0]+t[1]*o[0]*n,s=i[1]-(t[2]+1)*o[1]*n;return ye(r,s,r+o[0]*n,s+o[1]*n,e)},t.prototype.getTileCoordForCoordAndResolution=function(t,e,i){return this.getTileCoordForXYAndResolution_(t[0],t[1],e,!1,i)},t.prototype.getTileCoordForXYAndResolution_=function(t,e,i,n,o){var r=this.getZForResolution(i),s=i/this.getResolution(r),a=this.getOrigin(r),l=Lo(this.getTileSize(r),this.tmpSize_),h=s*(t-a[0])/i/l[0],u=s*(a[1]-e)/i/l[1];return n?(h=Mt(h,5)-1,u=Mt(u,5)-1):(h=It(h,5),u=It(u,5)),As(r,h,u,o)},t.prototype.getTileCoordForXYAndZ_=function(t,e,i,n,o){var r=this.getOrigin(i),s=this.getResolution(i),a=Lo(this.getTileSize(i),this.tmpSize_),l=(t-r[0])/s/a[0],h=(r[1]-e)/s/a[1];return n?(l=Mt(l,5)-1,h=Mt(h,5)-1):(l=It(l,5),h=It(h,5)),As(i,l,h,o)},t.prototype.getTileCoordForCoordAndZ=function(t,e,i){return this.getTileCoordForXYAndZ_(t[0],t[1],e,!1,i)},t.prototype.getTileCoordResolution=function(t){return this.resolutions_[t[0]]},t.prototype.getTileSize=function(t){return this.tileSize_?this.tileSize_:this.tileSizes_[t]},t.prototype.getFullTileRange=function(t){return this.fullTileRanges_?this.fullTileRanges_[t]:this.extent_?this.getTileRangeForExtentAndZ(this.extent_,t):null},t.prototype.getZForResolution=function(t,e){return Ct(s(this.resolutions_,t,e||0),this.minZoom,this.maxZoom)},t.prototype.tileCoordIntersectsViewport=function(t,e){return cn(e,0,e.length,2,this.getTileCoordExtent(t))},t.prototype.calculateTileRanges_=function(t){for(var e=this.resolutions_.length,i=new Array(e),n=this.minZoom;n<e;++n)i[n]=this.getTileRangeForExtentAndZ(t,n);this.fullTileRanges_=i},t}();function Ks(t){var e=t.getDefaultTileGrid();return e||(e=function(t,e,i,n){return function(t,e,i,n){var o=Zs(t,undefined,i);return new Bs({extent:t,origin:Pe(t,"top-left"),resolutions:o,tileSize:i})}(Vs(t),0,void 0)}(t),t.setDefaultTileGrid(e)),e}function Zs(t,e,i,n){for(var o=void 0!==e?e:42,r=Fe(t),s=ke(t),a=Lo(void 0!==i?i:256),l=n>0?n:Math.max(s/a[0],r/a[1]),h=o+1,u=new Array(h),c=0;c<h;++c)u[c]=l/Math.pow(2,c);return u}function Vs(t){var e=(t=He(t)).getExtent();if(!e){var i=180*Bt[Kt.DEGREES]/t.getMetersPerUnit();e=ye(-i,-i,i,i)}return e}var Us=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Hs=function(t){function e(e){var i=t.call(this,{attributions:e.attributions,attributionsCollapsible:e.attributionsCollapsible,projection:e.projection,state:e.state,wrapX:e.wrapX,interpolate:e.interpolate})||this;return i.on,i.once,i.un,i.opaque_=void 0!==e.opaque&&e.opaque,i.tilePixelRatio_=void 0!==e.tilePixelRatio?e.tilePixelRatio:1,i.tileGrid=void 0!==e.tileGrid?e.tileGrid:null,i.tileGrid&&Lo(i.tileGrid.getTileSize(i.tileGrid.getMinZoom()),[256,256]),i.tileCache=new zs(e.cacheSize||0),i.tmpSize=[0,0],i.key_=e.key||"",i.tileOptions={transition:e.transition,interpolate:e.interpolate},i.zDirection=e.zDirection?e.zDirection:0,i}return Us(e,t),e.prototype.canExpireCache=function(){return this.tileCache.canExpireCache()},e.prototype.expireCache=function(t,e){var i=this.getTileCacheForProjection(t);i&&i.expireCache(e)},e.prototype.forEachLoadedTile=function(t,e,i,n){var o=this.getTileCacheForProjection(t);if(!o)return!1;for(var r,s,a,l=!0,h=i.minX;h<=i.maxX;++h)for(var u=i.minY;u<=i.maxY;++u)s=Ds(e,h,u),a=!1,o.containsKey(s)&&(a=2===(r=o.get(s)).getState())&&(a=!1!==n(r)),a||(l=!1);return l},e.prototype.getGutterForProjection=function(t){return 0},e.prototype.getKey=function(){return this.key_},e.prototype.setKey=function(t){this.key_!==t&&(this.key_=t,this.changed())},e.prototype.getOpaque=function(t){return this.opaque_},e.prototype.getResolutions=function(){return this.tileGrid?this.tileGrid.getResolutions():null},e.prototype.getTile=function(t,e,i,n,o){return L()},e.prototype.getTileGrid=function(){return this.tileGrid},e.prototype.getTileGridForProjection=function(t){return this.tileGrid?this.tileGrid:Ks(t)},e.prototype.getTileCacheForProjection=function(t){var e=this.getProjection();return xt(null===e||$e(e,t),68),this.tileCache},e.prototype.getTilePixelRatio=function(t){return this.tilePixelRatio_},e.prototype.getTilePixelSize=function(t,e,i){var n,o,r,s=this.getTileGridForProjection(i),a=this.getTilePixelRatio(e),l=Lo(s.getTileSize(t),this.tmpSize);return 1==a?l:(n=l,o=a,void 0===(r=this.tmpSize)&&(r=[0,0]),r[0]=n[0]*o+.5|0,r[1]=n[1]*o+.5|0,r)},e.prototype.getTileCoordForTileUrlFunction=function(t,e){var i=void 0!==e?e:this.getProjection(),n=this.getTileGridForProjection(i);return this.getWrapX()&&i.isGlobal()&&(t=function(t,e,i){var n=e[0],o=t.getTileCoordCenter(e),r=Vs(i);if(fe(r,o))return e;var s=ke(r),a=Math.ceil((r[0]-o[0])/s);return o[0]+=s*a,t.getTileCoordForCoordAndZ(o,n)}(n,t,i)),function(t,e){var i=t[0],n=t[1],o=t[2];if(e.getMinZoom()>i||i>e.getMaxZoom())return!1;var r=e.getFullTileRange(i);return!r||r.containsXY(n,o)}(t,n)?t:null},e.prototype.clear=function(){this.tileCache.clear()},e.prototype.refresh=function(){this.clear(),t.prototype.refresh.call(this)},e.prototype.updateCacheSize=function(t,e){var i=this.getTileCacheForProjection(e);t>i.highWaterMark&&(i.highWaterMark=t)},e.prototype.useTile=function(t,e,i,n){},e}(Ns),qs=function(t){function e(e,i){var n=t.call(this,e)||this;return n.tile=i,n}return Us(e,t),e}(t),Js=Hs;function Qs(t,e){var i=/\{z\}/g,n=/\{x\}/g,o=/\{y\}/g,r=/\{-y\}/g;return function(s,a,l){return s?t.replace(i,s[0].toString()).replace(n,s[1].toString()).replace(o,s[2].toString()).replace(r,(function(){var t=s[0],i=e.getFullTileRange(t);return xt(i,55),(i.getHeight()-s[2]-1).toString()})):void 0}}var $s=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ta=function(t){function e(i){var n=t.call(this,{attributions:i.attributions,cacheSize:i.cacheSize,opaque:i.opaque,projection:i.projection,state:i.state,tileGrid:i.tileGrid,tilePixelRatio:i.tilePixelRatio,wrapX:i.wrapX,transition:i.transition,interpolate:i.interpolate,key:i.key,attributionsCollapsible:i.attributionsCollapsible,zDirection:i.zDirection})||this;return n.generateTileUrlFunction_=n.tileUrlFunction===e.prototype.tileUrlFunction,n.tileLoadFunction=i.tileLoadFunction,i.tileUrlFunction&&(n.tileUrlFunction=i.tileUrlFunction),n.urls=null,i.urls?n.setUrls(i.urls):i.url&&n.setUrl(i.url),n.tileLoadingKeys_={},n}return $s(e,t),e.prototype.getTileLoadFunction=function(){return this.tileLoadFunction},e.prototype.getTileUrlFunction=function(){return Object.getPrototypeOf(this).tileUrlFunction===this.tileUrlFunction?this.tileUrlFunction.bind(this):this.tileUrlFunction},e.prototype.getUrls=function(){return this.urls},e.prototype.handleTileChange=function(t){var e,i=t.target,n=D(i),o=i.getState();1==o?(this.tileLoadingKeys_[n]=!0,e="tileloadstart"):n in this.tileLoadingKeys_&&(delete this.tileLoadingKeys_[n],e=3==o?"tileloaderror":2==o?"tileloadend":void 0),null!=e&&this.dispatchEvent(new qs(e,i))},e.prototype.setTileLoadFunction=function(t){this.tileCache.clear(),this.tileLoadFunction=t,this.changed()},e.prototype.setTileUrlFunction=function(t,e){this.tileUrlFunction=t,this.tileCache.pruneExceptNewestZ(),void 0!==e?this.setKey(e):this.changed()},e.prototype.setUrl=function(t){var e=function(t){var e=[],i=/\{([a-z])-([a-z])\}/.exec(t);if(i){var n=i[1].charCodeAt(0),o=i[2].charCodeAt(0),r=void 0;for(r=n;r<=o;++r)e.push(t.replace(i[0],String.fromCharCode(r)));return e}if(i=/\{(\d+)-(\d+)\}/.exec(t)){for(var s=parseInt(i[2],10),a=parseInt(i[1],10);a<=s;a++)e.push(t.replace(i[0],a.toString()));return e}return e.push(t),e}(t);this.urls=e,this.setUrls(e)},e.prototype.setUrls=function(t){this.urls=t;var e=t.join("\n");this.generateTileUrlFunction_?this.setTileUrlFunction(function(t,e){for(var i=t.length,n=new Array(i),o=0;o<i;++o)n[o]=Qs(t[o],e);return function(t){return 1===t.length?t[0]:function(e,i,n){if(e){var o=function(t){return(t[1]<<t[0])+t[2]}(e),r=Ot(o,t.length);return t[r](e,i,n)}}}(n)}(t,this.tileGrid),e):this.setKey(e)},e.prototype.tileUrlFunction=function(t,e,i){},e.prototype.useTile=function(t,e,i){var n=Ds(t,e,i);this.tileCache.containsKey(n)&&this.tileCache.get(n)},e}(Js),ea=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ia=function(t){function e(e){var i=this,n=void 0===e.imageSmoothing||e.imageSmoothing;return void 0!==e.interpolate&&(n=e.interpolate),(i=t.call(this,{attributions:e.attributions,cacheSize:e.cacheSize,opaque:e.opaque,projection:e.projection,state:e.state,tileGrid:e.tileGrid,tileLoadFunction:e.tileLoadFunction?e.tileLoadFunction:na,tilePixelRatio:e.tilePixelRatio,tileUrlFunction:e.tileUrlFunction,url:e.url,urls:e.urls,wrapX:e.wrapX,transition:e.transition,interpolate:n,key:e.key,attributionsCollapsible:e.attributionsCollapsible,zDirection:e.zDirection})||this).crossOrigin=void 0!==e.crossOrigin?e.crossOrigin:null,i.tileClass=void 0!==e.tileClass?e.tileClass:mo,i.tileCacheForProjection={},i.tileGridForProjection={},i.reprojectionErrorThreshold_=e.reprojectionErrorThreshold,i.renderReprojectionEdges_=!1,i}return ea(e,t),e.prototype.canExpireCache=function(){if(this.tileCache.canExpireCache())return!0;for(var t in this.tileCacheForProjection)if(this.tileCacheForProjection[t].canExpireCache())return!0;return!1},e.prototype.expireCache=function(t,e){var i=this.getTileCacheForProjection(t);for(var n in this.tileCache.expireCache(this.tileCache==i?e:{}),this.tileCacheForProjection){var o=this.tileCacheForProjection[n];o.expireCache(o==i?e:{})}},e.prototype.getGutterForProjection=function(t){return this.getProjection()&&t&&!$e(this.getProjection(),t)?0:this.getGutter()},e.prototype.getGutter=function(){return 0},e.prototype.getKey=function(){var e=t.prototype.getKey.call(this);return this.getInterpolate()||(e+=":disable-interpolation"),e},e.prototype.getOpaque=function(e){return!(this.getProjection()&&e&&!$e(this.getProjection(),e))&&t.prototype.getOpaque.call(this,e)},e.prototype.getTileGridForProjection=function(t){var e=this.getProjection();if(!this.tileGrid||e&&!$e(e,t)){var i=D(t);return i in this.tileGridForProjection||(this.tileGridForProjection[i]=Ks(t)),this.tileGridForProjection[i]}return this.tileGrid},e.prototype.getTileCacheForProjection=function(t){var e=this.getProjection();if(!e||$e(e,t))return this.tileCache;var i=D(t);return i in this.tileCacheForProjection||(this.tileCacheForProjection[i]=new zs(this.tileCache.highWaterMark)),this.tileCacheForProjection[i]},e.prototype.createTile_=function(t,e,i,n,o,r){var s=[t,e,i],a=this.getTileCoordForTileUrlFunction(s,o),l=a?this.tileUrlFunction(a,n,o):void 0,h=new this.tileClass(s,void 0!==l?0:4,void 0!==l?l:"",this.crossOrigin,this.tileLoadFunction,this.tileOptions);return h.key=r,h.addEventListener(x,this.handleTileChange.bind(this)),h},e.prototype.getTile=function(t,e,i,n,o){var r=this.getProjection();if(r&&o&&!$e(r,o)){var s=this.getTileCacheForProjection(o),a=[t,e,i],l=void 0,h=ks(a);s.containsKey(h)&&(l=s.get(h));var u=this.getKey();if(l&&l.key==u)return l;var c=this.getTileGridForProjection(r),p=this.getTileGridForProjection(o),f=this.getTileCoordForTileUrlFunction(a,o),d=new Ro(r,c,o,p,a,f,this.getTilePixelRatio(n),this.getGutter(),function(t,e,i,n){return this.getTileInternal(t,e,i,n,r)}.bind(this),this.reprojectionErrorThreshold_,this.renderReprojectionEdges_,this.getInterpolate());return d.key=u,l?(d.interimTile=l,d.refreshInterimChain(),s.replace(h,d)):s.set(h,d),d}return this.getTileInternal(t,e,i,n,r||o)},e.prototype.getTileInternal=function(t,e,i,n,o){var r=null,s=Ds(t,e,i),a=this.getKey();if(this.tileCache.containsKey(s)){if((r=this.tileCache.get(s)).key!=a){var l=r;r=this.createTile_(t,e,i,n,o,a),0==l.getState()?r.interimTile=l.interimTile:r.interimTile=l,r.refreshInterimChain(),this.tileCache.replace(s,r)}}else r=this.createTile_(t,e,i,n,o,a),this.tileCache.set(s,r);return r},e.prototype.setRenderReprojectionEdges=function(t){if(this.renderReprojectionEdges_!=t){for(var e in this.renderReprojectionEdges_=t,this.tileCacheForProjection)this.tileCacheForProjection[e].clear();this.changed()}},e.prototype.setTileGridForProjection=function(t,e){var i=He(t);if(i){var n=D(i);n in this.tileGridForProjection||(this.tileGridForProjection[n]=e)}},e}(ta);function na(t,e){t.getImage().src=e}var oa=ia,ra=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),sa=function(t){function e(e){var i=this,n=e||{},o=void 0===n.imageSmoothing||n.imageSmoothing;void 0!==n.interpolate&&(o=n.interpolate);var r=void 0!==n.projection?n.projection:"EPSG:3857",s=void 0!==n.tileGrid?n.tileGrid:function(t){var e=t||{},i=e.extent||He("EPSG:3857").getExtent(),n={extent:i,minZoom:e.minZoom,tileSize:e.tileSize,resolutions:Zs(i,e.maxZoom,e.tileSize,e.maxResolution)};return new Bs(n)}({extent:Vs(r),maxResolution:n.maxResolution,maxZoom:n.maxZoom,minZoom:n.minZoom,tileSize:n.tileSize});return(i=t.call(this,{attributions:n.attributions,cacheSize:n.cacheSize,crossOrigin:n.crossOrigin,interpolate:o,opaque:n.opaque,projection:r,reprojectionErrorThreshold:n.reprojectionErrorThreshold,tileGrid:s,tileLoadFunction:n.tileLoadFunction,tilePixelRatio:n.tilePixelRatio,tileUrlFunction:n.tileUrlFunction,url:n.url,urls:n.urls,wrapX:void 0===n.wrapX||n.wrapX,transition:n.transition,attributionsCollapsible:n.attributionsCollapsible,zDirection:n.zDirection})||this).gutter_=void 0!==n.gutter?n.gutter:0,i}return ra(e,t),e.prototype.getGutter=function(){return this.gutter_},e}(oa),aa=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),la=function(t){function e(e){var i,n=e||{},o=void 0===n.imageSmoothing||n.imageSmoothing;void 0!==n.interpolate&&(o=n.interpolate),i=void 0!==n.attributions?n.attributions:['&#169; <a href="https://www.openstreetmap.org/copyright" target="_blank">OpenStreetMap</a> contributors.'];var r=void 0!==n.crossOrigin?n.crossOrigin:"anonymous",s=void 0!==n.url?n.url:"https://{a-c}.tile.openstreetmap.org/{z}/{x}/{y}.png";return t.call(this,{attributions:i,attributionsCollapsible:!1,cacheSize:n.cacheSize,crossOrigin:r,interpolate:o,maxZoom:void 0!==n.maxZoom?n.maxZoom:19,opaque:void 0===n.opaque||n.opaque,reprojectionErrorThreshold:n.reprojectionErrorThreshold,tileLoadFunction:n.tileLoadFunction,transition:n.transition,url:s,wrapX:n.wrapX,zDirection:n.zDirection})||this}return aa(e,t),e}(sa),ha="add",ua="remove",ca=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),pa="length",fa=function(t){function e(e,i,n){var o=t.call(this,e)||this;return o.element=i,o.index=n,o}return ca(e,t),e}(t),da=function(t){function e(e,i){var n=t.call(this)||this;n.on,n.once,n.un;var o=i||{};if(n.unique_=!!o.unique,n.array_=e||[],n.unique_)for(var r=0,s=n.array_.length;r<s;++r)n.assertUnique_(n.array_[r],r);return n.updateLength_(),n}return ca(e,t),e.prototype.clear=function(){for(;this.getLength()>0;)this.pop()},e.prototype.extend=function(t){for(var e=0,i=t.length;e<i;++e)this.push(t[e]);return this},e.prototype.forEach=function(t){for(var e=this.array_,i=0,n=e.length;i<n;++i)t(e[i],i,e)},e.prototype.getArray=function(){return this.array_},e.prototype.item=function(t){return this.array_[t]},e.prototype.getLength=function(){return this.get(pa)},e.prototype.insertAt=function(t,e){this.unique_&&this.assertUnique_(e),this.array_.splice(t,0,e),this.updateLength_(),this.dispatchEvent(new fa(ha,e,t))},e.prototype.pop=function(){return this.removeAt(this.getLength()-1)},e.prototype.push=function(t){this.unique_&&this.assertUnique_(t);var e=this.getLength();return this.insertAt(e,t),this.getLength()},e.prototype.remove=function(t){for(var e=this.array_,i=0,n=e.length;i<n;++i)if(e[i]===t)return this.removeAt(i)},e.prototype.removeAt=function(t){var e=this.array_[t];return this.array_.splice(t,1),this.updateLength_(),this.dispatchEvent(new fa(ua,e,t)),e},e.prototype.setAt=function(t,e){var i=this.getLength();if(t<i){this.unique_&&this.assertUnique_(e,t);var n=this.array_[t];this.array_[t]=e,this.dispatchEvent(new fa(ua,n,t)),this.dispatchEvent(new fa(ha,e,t))}else{for(var o=i;o<t;++o)this.insertAt(o,void 0);this.insertAt(t,e)}},e.prototype.updateLength_=function(){this.set(pa,this.array_.length)},e.prototype.assertUnique_=function(t,e){for(var i=0,n=this.array_.length;i<n;++i)if(this.array_[i]===t&&i!==e)throw new mt(58)},e}(G),ga=function(){function t(t){this.rbush_=new zo(t),this.items_={}}return t.prototype.insert=function(t,e){var i={minX:t[0],minY:t[1],maxX:t[2],maxY:t[3],value:e};this.rbush_.insert(i),this.items_[D(e)]=i},t.prototype.load=function(t,e){for(var i=new Array(e.length),n=0,o=e.length;n<o;n++){var r=t[n],s=e[n],a={minX:r[0],minY:r[1],maxX:r[2],maxY:r[3],value:s};i[n]=a,this.items_[D(s)]=a}this.rbush_.load(i)},t.prototype.remove=function(t){var e=D(t),i=this.items_[e];return delete this.items_[e],null!==this.rbush_.remove(i)},t.prototype.update=function(t,e){var i=this.items_[D(e)];xe([i.minX,i.minY,i.maxX,i.maxY],t)||(this.remove(e),this.insert(t,e))},t.prototype.getAll=function(){return this.rbush_.all().map((function(t){return t.value}))},t.prototype.getInExtent=function(t){var e={minX:t[0],minY:t[1],maxX:t[2],maxY:t[3]};return this.rbush_.search(e).map((function(t){return t.value}))},t.prototype.forEach=function(t){return this.forEach_(this.getAll(),t)},t.prototype.forEachInExtent=function(t,e){return this.forEach_(this.getInExtent(t),e)},t.prototype.forEach_=function(t,e){for(var i,n=0,o=t.length;n<o;n++)if(i=e(t[n]))return i;return i},t.prototype.isEmpty=function(){return _(this.items_)},t.prototype.clear=function(){this.rbush_.clear(),this.items_={}},t.prototype.getExtent=function(t){var e=this.rbush_.toJSON();return ye(e.minX,e.minY,e.maxX,e.maxY,t)},t.prototype.concat=function(t){for(var e in this.rbush_.load(t.rbush_.all()),t.items_)this.items_[e]=t.items_[e]},t}(),_a="addfeature",ya="removefeature";function va(t,e){return[[-1/0,-1/0,1/0,1/0]]}function ma(t,e){return function(i,n,o,r,s){var a=this;!function(t,e,i,n,o,r,s){var a=new XMLHttpRequest;a.open("GET","function"==typeof t?t(i,n,o):t,!0),"arraybuffer"==e.getType()&&(a.responseType="arraybuffer"),a.withCredentials=false,a.onload=function(t){if(!a.status||a.status>=200&&a.status<300){var n=e.getType(),l=void 0;"json"==n||"text"==n?l=a.responseText:"xml"==n?(l=a.responseXML)||(l=(new DOMParser).parseFromString(a.responseText,"application/xml")):"arraybuffer"==n&&(l=a.response),l?r(e.readFeatures(l,{extent:i,featureProjection:o}),e.readProjection(l)):s()}else s()},a.onerror=s,a.send()}(t,e,i,n,o,(function(t,e){a.addFeatures(t),void 0!==r&&r(t)}),s||p)}}var xa=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ca=function(t){function e(e,i,n){var o=t.call(this,e)||this;return o.feature=i,o.features=n,o}return xa(e,t),e}(t),wa=function(t){function i(e){var i=this,n=e||{};(i=t.call(this,{attributions:n.attributions,interpolate:!0,projection:void 0,state:"ready",wrapX:void 0===n.wrapX||n.wrapX})||this).on,i.once,i.un,i.loader_=p,i.format_=n.format,i.overlaps_=void 0===n.overlaps||n.overlaps,i.url_=n.url,void 0!==n.loader?i.loader_=n.loader:void 0!==i.url_&&(xt(i.format_,7),i.loader_=ma(i.url_,i.format_)),i.strategy_=void 0!==n.strategy?n.strategy:va;var o,r,s=void 0===n.useSpatialIndex||n.useSpatialIndex;return i.featuresRtree_=s?new ga:null,i.loadedExtentsRtree_=new ga,i.loadingExtentsCount_=0,i.nullGeometryFeatures_={},i.idIndex_={},i.uidIndex_={},i.featureChangeKeys_={},i.featuresCollection_=null,Array.isArray(n.features)?r=n.features:n.features&&(r=(o=n.features).getArray()),s||void 0!==o||(o=new da(r)),void 0!==r&&i.addFeaturesInternal(r),void 0!==o&&i.bindFeaturesCollection_(o),i}return xa(i,t),i.prototype.addFeature=function(t){this.addFeatureInternal(t),this.changed()},i.prototype.addFeatureInternal=function(t){var e=D(t);if(this.addToIndex_(e,t)){this.setupChangeEvents_(e,t);var i=t.getGeometry();if(i){var n=i.getExtent();this.featuresRtree_&&this.featuresRtree_.insert(n,t)}else this.nullGeometryFeatures_[e]=t;this.dispatchEvent(new Ca(_a,t))}else this.featuresCollection_&&this.featuresCollection_.remove(t)},i.prototype.setupChangeEvents_=function(t,i){this.featureChangeKeys_[t]=[O(i,x,this.handleFeatureChange_,this),O(i,e,this.handleFeatureChange_,this)]},i.prototype.addToIndex_=function(t,e){var i=!0,n=e.getId();return void 0!==n&&(n.toString()in this.idIndex_?i=!1:this.idIndex_[n.toString()]=e),i&&(xt(!(t in this.uidIndex_),30),this.uidIndex_[t]=e),i},i.prototype.addFeatures=function(t){this.addFeaturesInternal(t),this.changed()},i.prototype.addFeaturesInternal=function(t){for(var e=[],i=[],n=[],o=0,r=t.length;o<r;o++){var s=D(l=t[o]);this.addToIndex_(s,l)&&i.push(l)}o=0;for(var a=i.length;o<a;o++){var l;s=D(l=i[o]),this.setupChangeEvents_(s,l);var h=l.getGeometry();if(h){var u=h.getExtent();e.push(u),n.push(l)}else this.nullGeometryFeatures_[s]=l}if(this.featuresRtree_&&this.featuresRtree_.load(e,n),this.hasListener(_a)){o=0;for(var c=i.length;o<c;o++)this.dispatchEvent(new Ca(_a,i[o]))}},i.prototype.bindFeaturesCollection_=function(t){var e=!1;this.addEventListener(_a,(function(i){e||(e=!0,t.push(i.feature),e=!1)})),this.addEventListener(ya,(function(i){e||(e=!0,t.remove(i.feature),e=!1)})),t.addEventListener(ha,function(t){e||(e=!0,this.addFeature(t.element),e=!1)}.bind(this)),t.addEventListener(ua,function(t){e||(e=!0,this.removeFeature(t.element),e=!1)}.bind(this)),this.featuresCollection_=t},i.prototype.clear=function(t){if(t){for(var e in this.featureChangeKeys_)this.featureChangeKeys_[e].forEach(P);this.featuresCollection_||(this.featureChangeKeys_={},this.idIndex_={},this.uidIndex_={})}else if(this.featuresRtree_){var i=function(t){this.removeFeatureInternal(t)}.bind(this);for(var n in this.featuresRtree_.forEach(i),this.nullGeometryFeatures_)this.removeFeatureInternal(this.nullGeometryFeatures_[n])}this.featuresCollection_&&this.featuresCollection_.clear(),this.featuresRtree_&&this.featuresRtree_.clear(),this.nullGeometryFeatures_={};var o=new Ca("clear");this.dispatchEvent(o),this.changed()},i.prototype.forEachFeature=function(t){if(this.featuresRtree_)return this.featuresRtree_.forEach(t);this.featuresCollection_&&this.featuresCollection_.forEach(t)},i.prototype.forEachFeatureAtCoordinateDirect=function(t,e){var i=[t[0],t[1],t[0],t[1]];return this.forEachFeatureInExtent(i,(function(i){return i.getGeometry().intersectsCoordinate(t)?e(i):void 0}))},i.prototype.forEachFeatureInExtent=function(t,e){if(this.featuresRtree_)return this.featuresRtree_.forEachInExtent(t,e);this.featuresCollection_&&this.featuresCollection_.forEach(e)},i.prototype.forEachFeatureIntersectingExtent=function(t,e){return this.forEachFeatureInExtent(t,(function(i){if(i.getGeometry().intersectsExtent(t)){var n=e(i);if(n)return n}}))},i.prototype.getFeaturesCollection=function(){return this.featuresCollection_},i.prototype.getFeatures=function(){var t;return this.featuresCollection_?t=this.featuresCollection_.getArray().slice(0):this.featuresRtree_&&(t=this.featuresRtree_.getAll(),_(this.nullGeometryFeatures_)||l(t,g(this.nullGeometryFeatures_))),t},i.prototype.getFeaturesAtCoordinate=function(t){var e=[];return this.forEachFeatureAtCoordinateDirect(t,(function(t){e.push(t)})),e},i.prototype.getFeaturesInExtent=function(t,e){var i=this;if(this.featuresRtree_){if(!(e&&e.canWrapX()&&this.getWrapX()))return this.featuresRtree_.getInExtent(t);var n=function(t,e){if(e.canWrapX()){var i=e.getExtent();if(!isFinite(t[0])||!isFinite(t[2]))return[[i[0],t[1],i[2],t[3]]];ze(t,e);var n=ke(i);if(ke(t)>n)return[[i[0],t[1],i[2],t[3]]];if(t[0]<i[0])return[[t[0]+n,t[1],i[2],t[3]],[i[0],t[1],t[2],t[3]]];if(t[2]>i[2])return[[t[0],t[1],i[2],t[3]],[i[0],t[1],t[2]-n,t[3]]]}return[t]}(t,e);return[].concat.apply([],n.map((function(t){return i.featuresRtree_.getInExtent(t)})))}return this.featuresCollection_?this.featuresCollection_.getArray().slice(0):[]},i.prototype.getClosestFeatureToCoordinate=function(t,e){var i=t[0],n=t[1],o=null,r=[NaN,NaN],s=1/0,a=[-1/0,-1/0,1/0,1/0],l=e||u;return this.featuresRtree_.forEachInExtent(a,(function(t){if(l(t)){var e=t.getGeometry(),h=s;if((s=e.closestPointXY(i,n,r,s))<h){o=t;var u=Math.sqrt(s);a[0]=i-u,a[1]=n-u,a[2]=i+u,a[3]=n+u}}})),o},i.prototype.getExtent=function(t){return this.featuresRtree_.getExtent(t)},i.prototype.getFeatureById=function(t){var e=this.idIndex_[t.toString()];return void 0!==e?e:null},i.prototype.getFeatureByUid=function(t){var e=this.uidIndex_[t];return void 0!==e?e:null},i.prototype.getFormat=function(){return this.format_},i.prototype.getOverlaps=function(){return this.overlaps_},i.prototype.getUrl=function(){return this.url_},i.prototype.handleFeatureChange_=function(t){var e=t.target,i=D(e),n=e.getGeometry();if(n){var o=n.getExtent();i in this.nullGeometryFeatures_?(delete this.nullGeometryFeatures_[i],this.featuresRtree_&&this.featuresRtree_.insert(o,e)):this.featuresRtree_&&this.featuresRtree_.update(o,e)}else i in this.nullGeometryFeatures_||(this.featuresRtree_&&this.featuresRtree_.remove(e),this.nullGeometryFeatures_[i]=e);var r=e.getId();if(void 0!==r){var s=r.toString();this.idIndex_[s]!==e&&(this.removeFromIdIndex_(e),this.idIndex_[s]=e)}else this.removeFromIdIndex_(e),this.uidIndex_[i]=e;this.changed(),this.dispatchEvent(new Ca("changefeature",e))},i.prototype.hasFeature=function(t){var e=t.getId();return void 0!==e?e in this.idIndex_:D(t)in this.uidIndex_},i.prototype.isEmpty=function(){return this.featuresRtree_?this.featuresRtree_.isEmpty()&&_(this.nullGeometryFeatures_):!this.featuresCollection_||0===this.featuresCollection_.getLength()},i.prototype.loadFeatures=function(t,e,i){for(var n=this.loadedExtentsRtree_,o=this.strategy_(t,e,i),r=function(t,r){var a=o[t];n.forEachInExtent(a,(function(t){return de(t.extent,a)}))||(++s.loadingExtentsCount_,s.dispatchEvent(new Ca("featuresloadstart")),s.loader_.call(s,a,e,i,function(t){--this.loadingExtentsCount_,this.dispatchEvent(new Ca("featuresloadend",void 0,t))}.bind(s),function(){--this.loadingExtentsCount_,this.dispatchEvent(new Ca("featuresloaderror"))}.bind(s)),n.insert(a,{extent:a.slice()}))},s=this,a=0,l=o.length;a<l;++a)r(a);this.loading=!(this.loader_.length<4)&&this.loadingExtentsCount_>0},i.prototype.refresh=function(){this.clear(!0),this.loadedExtentsRtree_.clear(),t.prototype.refresh.call(this)},i.prototype.removeLoadedExtent=function(t){var e,i=this.loadedExtentsRtree_;i.forEachInExtent(t,(function(i){if(xe(i.extent,t))return e=i,!0})),e&&i.remove(e)},i.prototype.removeFeature=function(t){if(t){var e=D(t);e in this.nullGeometryFeatures_?delete this.nullGeometryFeatures_[e]:this.featuresRtree_&&this.featuresRtree_.remove(t),this.removeFeatureInternal(t)&&this.changed()}},i.prototype.removeFeatureInternal=function(t){var e=D(t),i=this.featureChangeKeys_[e];if(i){i.forEach(P),delete this.featureChangeKeys_[e];var n=t.getId();return void 0!==n&&delete this.idIndex_[n.toString()],delete this.uidIndex_[e],this.dispatchEvent(new Ca(ya,t)),t}},i.prototype.removeFromIdIndex_=function(t){var e=!1;for(var i in this.idIndex_)if(this.idIndex_[i]===t){delete this.idIndex_[i],e=!0;break}return e},i.prototype.setLoader=function(t){this.loader_=t},i.prototype.setUrl=function(t){xt(this.format_,7),this.url_=t,this.setLoader(ma(t,this.format_))},i}(Ns),Sa=function(){function t(t){var e=t||{};this.font_=e.font,this.rotation_=e.rotation,this.rotateWithView_=e.rotateWithView,this.scale_=e.scale,this.scaleArray_=Lo(void 0!==e.scale?e.scale:1),this.text_=e.text,this.textAlign_=e.textAlign,this.justify_=e.justify,this.textBaseline_=e.textBaseline,this.fill_=void 0!==e.fill?e.fill:new cr({color:"#333"}),this.maxAngle_=void 0!==e.maxAngle?e.maxAngle:Math.PI/4,this.placement_=void 0!==e.placement?e.placement:"point",this.overflow_=!!e.overflow,this.stroke_=void 0!==e.stroke?e.stroke:null,this.offsetX_=void 0!==e.offsetX?e.offsetX:0,this.offsetY_=void 0!==e.offsetY?e.offsetY:0,this.backgroundFill_=e.backgroundFill?e.backgroundFill:null,this.backgroundStroke_=e.backgroundStroke?e.backgroundStroke:null,this.padding_=void 0===e.padding?null:e.padding}return t.prototype.clone=function(){var e=this.getScale();return new t({font:this.getFont(),placement:this.getPlacement(),maxAngle:this.getMaxAngle(),overflow:this.getOverflow(),rotation:this.getRotation(),rotateWithView:this.getRotateWithView(),scale:Array.isArray(e)?e.slice():e,text:this.getText(),textAlign:this.getTextAlign(),justify:this.getJustify(),textBaseline:this.getTextBaseline(),fill:this.getFill()?this.getFill().clone():void 0,stroke:this.getStroke()?this.getStroke().clone():void 0,offsetX:this.getOffsetX(),offsetY:this.getOffsetY(),backgroundFill:this.getBackgroundFill()?this.getBackgroundFill().clone():void 0,backgroundStroke:this.getBackgroundStroke()?this.getBackgroundStroke().clone():void 0,padding:this.getPadding()||void 0})},t.prototype.getOverflow=function(){return this.overflow_},t.prototype.getFont=function(){return this.font_},t.prototype.getMaxAngle=function(){return this.maxAngle_},t.prototype.getPlacement=function(){return this.placement_},t.prototype.getOffsetX=function(){return this.offsetX_},t.prototype.getOffsetY=function(){return this.offsetY_},t.prototype.getFill=function(){return this.fill_},t.prototype.getRotateWithView=function(){return this.rotateWithView_},t.prototype.getRotation=function(){return this.rotation_},t.prototype.getScale=function(){return this.scale_},t.prototype.getScaleArray=function(){return this.scaleArray_},t.prototype.getStroke=function(){return this.stroke_},t.prototype.getText=function(){return this.text_},t.prototype.getTextAlign=function(){return this.textAlign_},t.prototype.getJustify=function(){return this.justify_},t.prototype.getTextBaseline=function(){return this.textBaseline_},t.prototype.getBackgroundFill=function(){return this.backgroundFill_},t.prototype.getBackgroundStroke=function(){return this.backgroundStroke_},t.prototype.getPadding=function(){return this.padding_},t.prototype.setOverflow=function(t){this.overflow_=t},t.prototype.setFont=function(t){this.font_=t},t.prototype.setMaxAngle=function(t){this.maxAngle_=t},t.prototype.setOffsetX=function(t){this.offsetX_=t},t.prototype.setOffsetY=function(t){this.offsetY_=t},t.prototype.setPlacement=function(t){this.placement_=t},t.prototype.setRotateWithView=function(t){this.rotateWithView_=t},t.prototype.setFill=function(t){this.fill_=t},t.prototype.setRotation=function(t){this.rotation_=t},t.prototype.setScale=function(t){this.scale_=t,this.scaleArray_=Lo(void 0!==t?t:1)},t.prototype.setStroke=function(t){this.stroke_=t},t.prototype.setText=function(t){this.text_=t},t.prototype.setTextAlign=function(t){this.textAlign_=t},t.prototype.setJustify=function(t){this.justify_=t},t.prototype.setTextBaseline=function(t){this.textBaseline_=t},t.prototype.setBackgroundFill=function(t){this.backgroundFill_=t},t.prototype.setBackgroundStroke=function(t){this.backgroundStroke_=t},t.prototype.setPadding=function(t){this.padding_=t},t}(),Ea=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ta=function(t){function e(e){var i=t.call(this)||this;if(i.on,i.once,i.un,i.id_=void 0,i.geometryName_="geometry",i.style_=null,i.styleFunction_=void 0,i.geometryChangeKey_=null,i.addChangeListener(i.geometryName_,i.handleGeometryChanged_),e)if("function"==typeof e.getSimplifiedGeometry){var n=e;i.setGeometry(n)}else{var o=e;i.setProperties(o)}return i}return Ea(e,t),e.prototype.clone=function(){var t=new e(this.hasProperties()?this.getProperties():null);t.setGeometryName(this.getGeometryName());var i=this.getGeometry();i&&t.setGeometry(i.clone());var n=this.getStyle();return n&&t.setStyle(n),t},e.prototype.getGeometry=function(){return this.get(this.geometryName_)},e.prototype.getId=function(){return this.id_},e.prototype.getGeometryName=function(){return this.geometryName_},e.prototype.getStyle=function(){return this.style_},e.prototype.getStyleFunction=function(){return this.styleFunction_},e.prototype.handleGeometryChange_=function(){this.changed()},e.prototype.handleGeometryChanged_=function(){this.geometryChangeKey_&&(P(this.geometryChangeKey_),this.geometryChangeKey_=null);var t=this.getGeometry();t&&(this.geometryChangeKey_=O(t,x,this.handleGeometryChange_,this)),this.changed()},e.prototype.setGeometry=function(t){this.set(this.geometryName_,t)},e.prototype.setStyle=function(t){var e,i;this.style_=t,this.styleFunction_=t?"function"==typeof(e=t)?e:(Array.isArray(e)?i=e:(xt("function"==typeof e.getZIndex,41),i=[e]),function(){return i}):void 0,this.changed()},e.prototype.setId=function(t){this.id_=t,this.changed()},e.prototype.setGeometryName=function(t){this.removeChangeListener(this.geometryName_,this.handleGeometryChanged_),this.geometryName_=t,this.addChangeListener(this.geometryName_,this.handleGeometryChanged_),this.handleGeometryChanged_()},e}(G),ba=Ta,Oa=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ra=function(t){function e(e){var i=t.call(this)||this;return i.map_=e,i}return Oa(e,t),e.prototype.dispatchRenderEvent=function(t,e){L()},e.prototype.calculateMatrices2D=function(t){var e=t.viewState,i=t.coordinateToPixelTransform,n=t.pixelToCoordinateTransform;Ii(i,t.size[0]/2,t.size[1]/2,1/e.resolution,-1/e.resolution,-e.rotation,-e.center[0],-e.center[1]),Mi(n,i)},e.prototype.forEachFeatureAtCoordinate=function(t,e,i,n,o,r,s,a){var l,h=e.viewState;function u(t,e,i,n){return o.call(r,e,t?i:null,n)}var c=h.projection,p=Ne(t.slice(),c),f=[[0,0]];if(c.canWrapX()&&n){var d=ke(c.getExtent());f.push([-d,0],[d,0])}for(var g=e.layerStatesArray,_=g.length,y=[],v=[],m=0;m<f.length;m++)for(var x=_-1;x>=0;--x){var C=g[x],w=C.layer;if(w.hasRenderer()&&jt(C,h)&&s.call(a,w)){var S=w.getRenderer(),E=w.getSource();if(S&&E){var T=E.getWrapX()?p:t,b=u.bind(null,C.managed);v[0]=T[0]+f[m][0],v[1]=T[1]+f[m][1],l=S.forEachFeatureAtCoordinate(v,e,i,b,y)}if(l)return l}}if(0!==y.length){var O=1/y.length;return y.forEach((function(t,e){return t.distanceSq+=e*O})),y.sort((function(t,e){return t.distanceSq-e.distanceSq})),y.some((function(t){return l=t.callback(t.feature,t.layer,t.geometry)})),l}},e.prototype.forEachLayerAtPixel=function(t,e,i,n,o){return L()},e.prototype.hasFeatureAtCoordinate=function(t,e,i,n,o,r){return void 0!==this.forEachFeatureAtCoordinate(t,e,i,n,u,this,o,r)},e.prototype.getMap=function(){return this.map_},e.prototype.renderFrame=function(t){L()},e.prototype.scheduleExpireIconCache=function(t){_s.canExpireCache()&&t.postRenderFunctions.push(Pa)},e}(o);function Pa(t,e){_s.expire()}var Ia=Ra,Ma=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Fa=function(t){function i(i){var n=t.call(this,i)||this;n.fontChangeListenerKey_=O(Jo,e,i.redrawText.bind(i)),n.element_=document.createElement("div");var o=n.element_.style;o.position="absolute",o.width="100%",o.height="100%",o.zIndex="0",n.element_.className=nt+" ol-layers";var r=i.getViewport();return r.insertBefore(n.element_,r.firstChild||null),n.children_=[],n.renderedVisible_=!0,n}return Ma(i,t),i.prototype.dispatchRenderEvent=function(t,e){var i=this.getMap();if(i.hasListener(t)){var n=new Jn(t,void 0,e);i.dispatchEvent(n)}},i.prototype.disposeInternal=function(){P(this.fontChangeListenerKey_),this.element_.parentNode.removeChild(this.element_),t.prototype.disposeInternal.call(this)},i.prototype.renderFrame=function(t){if(t){this.calculateMatrices2D(t),this.dispatchRenderEvent(At,t);var e=t.layerStatesArray.sort((function(t,e){return t.zIndex-e.zIndex})),i=t.viewState;this.children_.length=0;for(var n=[],o=null,r=0,s=e.length;r<s;++r){var a=e[r];t.layerIndex=r;var l=a.layer,h=l.getSourceState();if(!jt(a,i)||"ready"!=h&&"undefined"!=h)l.unrender();else{var u=l.render(t,o);u&&(u!==o&&(this.children_.push(u),o=u),"getDeclutter"in l&&n.push(l))}}for(r=n.length-1;r>=0;--r)n[r].renderDeclutter(t);!function(t,e){for(var i=t.childNodes,n=0;;++n){var o=i[n],r=e[n];if(!o&&!r)break;o!==r&&(o?r?t.insertBefore(r,o):(t.removeChild(o),--n):t.appendChild(r))}}(this.element_,this.children_),this.dispatchRenderEvent("postcompose",t),this.renderedVisible_||(this.element_.style.display="",this.renderedVisible_=!0),this.scheduleExpireIconCache(t)}else this.renderedVisible_&&(this.element_.style.display="none",this.renderedVisible_=!1)},i.prototype.forEachLayerAtPixel=function(t,e,i,n,o){for(var r=e.viewState,s=e.layerStatesArray,a=s.length-1;a>=0;--a){var l=s[a],h=l.layer;if(h.hasRenderer()&&jt(l,r)&&o(h)){var u=h.getRenderer().getDataAtPixel(t,e,i);if(u){var c=n(h,u);if(c)return c}}}},i}(Ia),La=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Aa=function(t){function e(e,i){var n=t.call(this,e)||this;return n.layer=i,n}return La(e,t),e}(t),Da="layers",ka=function(t){function i(e){var i=this,n=e||{},o=f({},n);delete o.layers;var r=n.layers;return(i=t.call(this,o)||this).on,i.once,i.un,i.layersListenerKeys_=[],i.listenerKeys_={},i.addChangeListener(Da,i.handleLayersChanged_),r?Array.isArray(r)?r=new da(r.slice(),{unique:!0}):xt("function"==typeof r.getArray,43):r=new da(void 0,{unique:!0}),i.setLayers(r),i}return La(i,t),i.prototype.handleLayerChange_=function(){this.changed()},i.prototype.handleLayersChanged_=function(){this.layersListenerKeys_.forEach(P),this.layersListenerKeys_.length=0;var t=this.getLayers();for(var e in this.layersListenerKeys_.push(O(t,ha,this.handleLayersAdd_,this),O(t,ua,this.handleLayersRemove_,this)),this.listenerKeys_)this.listenerKeys_[e].forEach(P);d(this.listenerKeys_);for(var i=t.getArray(),n=0,o=i.length;n<o;n++){var r=i[n];this.registerLayerListeners_(r),this.dispatchEvent(new Aa("addlayer",r))}this.changed()},i.prototype.registerLayerListeners_=function(t){var n=[O(t,e,this.handleLayerChange_,this),O(t,x,this.handleLayerChange_,this)];t instanceof i&&n.push(O(t,"addlayer",this.handleLayerGroupAdd_,this),O(t,"removelayer",this.handleLayerGroupRemove_,this)),this.listenerKeys_[D(t)]=n},i.prototype.handleLayerGroupAdd_=function(t){this.dispatchEvent(new Aa("addlayer",t.layer))},i.prototype.handleLayerGroupRemove_=function(t){this.dispatchEvent(new Aa("removelayer",t.layer))},i.prototype.handleLayersAdd_=function(t){var e=t.element;this.registerLayerListeners_(e),this.dispatchEvent(new Aa("addlayer",e)),this.changed()},i.prototype.handleLayersRemove_=function(t){var e=t.element,i=D(e);this.listenerKeys_[i].forEach(P),delete this.listenerKeys_[i],this.dispatchEvent(new Aa("removelayer",e)),this.changed()},i.prototype.getLayers=function(){return this.get(Da)},i.prototype.setLayers=function(t){var e=this.getLayers();if(e)for(var i=e.getArray(),n=0,o=i.length;n<o;++n)this.dispatchEvent(new Aa("removelayer",i[n]));this.set(Da,t)},i.prototype.getLayersArray=function(t){var e=void 0!==t?t:[];return this.getLayers().forEach((function(t){t.getLayersArray(e)})),e},i.prototype.getLayerStatesArray=function(t){var e=void 0!==t?t:[],i=e.length;this.getLayers().forEach((function(t){t.getLayerStatesArray(e)}));var n=this.getLayerState(),o=n.zIndex;t||void 0!==n.zIndex||(o=0);for(var r=i,s=e.length;r<s;r++){var a=e[r];a.opacity*=n.opacity,a.visible=a.visible&&n.visible,a.maxResolution=Math.min(a.maxResolution,n.maxResolution),a.minResolution=Math.max(a.minResolution,n.minResolution),a.minZoom=Math.max(a.minZoom,n.minZoom),a.maxZoom=Math.min(a.maxZoom,n.maxZoom),void 0!==n.extent&&(void 0!==a.extent?a.extent=Le(a.extent,n.extent):a.extent=n.extent),void 0===a.zIndex&&(a.zIndex=o)}return e},i.prototype.getSourceState=function(){return"ready"},i}(Lt),ja=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ga=function(t){function e(e,i,n){var o=t.call(this,e)||this;return o.map=i,o.frameState=void 0!==n?n:null,o}return ja(e,t),e}(t),za=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Wa=function(t){function e(e,i,n,o,r,s){var a=t.call(this,e,i,r)||this;return a.originalEvent=n,a.pixel_=null,a.coordinate_=null,a.dragging=void 0!==o&&o,a.activePointers=s,a}return za(e,t),Object.defineProperty(e.prototype,"pixel",{get:function(){return this.pixel_||(this.pixel_=this.map.getEventPixel(this.originalEvent)),this.pixel_},set:function(t){this.pixel_=t},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"coordinate",{get:function(){return this.coordinate_||(this.coordinate_=this.map.getCoordinateFromPixel(this.pixel)),this.coordinate_},set:function(t){this.coordinate_=t},enumerable:!1,configurable:!0}),e.prototype.preventDefault=function(){t.prototype.preventDefault.call(this),"preventDefault"in this.originalEvent&&this.originalEvent.preventDefault()},e.prototype.stopPropagation=function(){t.prototype.stopPropagation.call(this),"stopPropagation"in this.originalEvent&&this.originalEvent.stopPropagation()},e}(Ga),Xa={SINGLECLICK:"singleclick",CLICK:w,DBLCLICK:"dblclick",POINTERDRAG:"pointerdrag",POINTERMOVE:"pointermove",POINTERDOWN:"pointerdown",POINTERUP:"pointerup",POINTEROVER:"pointerover",POINTEROUT:"pointerout",POINTERENTER:"pointerenter",POINTERLEAVE:"pointerleave",POINTERCANCEL:"pointercancel"},Na=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ya=function(t){function e(e,i){var n=t.call(this,e)||this;n.map_=e,n.clickTimeoutId_,n.emulateClicks_=!1,n.dragging_=!1,n.dragListenerKeys_=[],n.moveTolerance_=void 0===i?1:i,n.down_=null;var o=n.map_.getViewport();return n.activePointers_=[],n.trackedTouches_={},n.element_=o,n.pointerdownListenerKey_=O(o,Nt,n.handlePointerDown_,n),n.originalPointerMoveEvent_,n.relayedListenerKey_=O(o,Xt,n.relayMoveEvent_,n),n.boundHandleTouchMove_=n.handleTouchMove_.bind(n),n.element_.addEventListener(T,n.boundHandleTouchMove_,!!H&&{passive:!1}),n}return Na(e,t),e.prototype.emulateClick_=function(t){var e=new Wa(Xa.CLICK,this.map_,t);this.dispatchEvent(e),void 0!==this.clickTimeoutId_?(clearTimeout(this.clickTimeoutId_),this.clickTimeoutId_=void 0,e=new Wa(Xa.DBLCLICK,this.map_,t),this.dispatchEvent(e)):this.clickTimeoutId_=setTimeout(function(){this.clickTimeoutId_=void 0;var e=new Wa(Xa.SINGLECLICK,this.map_,t);this.dispatchEvent(e)}.bind(this),250)},e.prototype.updateActivePointers_=function(t){var e=t,i=e.pointerId;if(e.type==Xa.POINTERUP||e.type==Xa.POINTERCANCEL){for(var n in delete this.trackedTouches_[i],this.trackedTouches_)if(this.trackedTouches_[n].target!==e.target){delete this.trackedTouches_[n];break}}else e.type!=Xa.POINTERDOWN&&e.type!=Xa.POINTERMOVE||(this.trackedTouches_[i]=e);this.activePointers_=g(this.trackedTouches_)},e.prototype.handlePointerUp_=function(t){this.updateActivePointers_(t);var e=new Wa(Xa.POINTERUP,this.map_,t,void 0,void 0,this.activePointers_);this.dispatchEvent(e),this.emulateClicks_&&!e.defaultPrevented&&!this.dragging_&&this.isMouseActionButton_(t)&&this.emulateClick_(this.down_),0===this.activePointers_.length&&(this.dragListenerKeys_.forEach(P),this.dragListenerKeys_.length=0,this.dragging_=!1,this.down_=null)},e.prototype.isMouseActionButton_=function(t){return 0===t.button},e.prototype.handlePointerDown_=function(t){this.emulateClicks_=0===this.activePointers_.length,this.updateActivePointers_(t);var e=new Wa(Xa.POINTERDOWN,this.map_,t,void 0,void 0,this.activePointers_);for(var i in this.dispatchEvent(e),this.down_={},t){var n=t[i];this.down_[i]="function"==typeof n?p:n}if(0===this.dragListenerKeys_.length){var o=this.map_.getOwnerDocument();this.dragListenerKeys_.push(O(o,Xa.POINTERMOVE,this.handlePointerMove_,this),O(o,Xa.POINTERUP,this.handlePointerUp_,this),O(this.element_,Xa.POINTERCANCEL,this.handlePointerUp_,this)),this.element_.getRootNode&&this.element_.getRootNode()!==o&&this.dragListenerKeys_.push(O(this.element_.getRootNode(),Xa.POINTERUP,this.handlePointerUp_,this))}},e.prototype.handlePointerMove_=function(t){if(this.isMoving_(t)){this.updateActivePointers_(t),this.dragging_=!0;var e=new Wa(Xa.POINTERDRAG,this.map_,t,this.dragging_,void 0,this.activePointers_);this.dispatchEvent(e)}},e.prototype.relayMoveEvent_=function(t){this.originalPointerMoveEvent_=t;var e=!(!this.down_||!this.isMoving_(t));this.dispatchEvent(new Wa(Xa.POINTERMOVE,this.map_,t,e))},e.prototype.handleTouchMove_=function(t){var e=this.originalPointerMoveEvent_;e&&!e.defaultPrevented||"boolean"==typeof t.cancelable&&!0!==t.cancelable||t.preventDefault()},e.prototype.isMoving_=function(t){return this.dragging_||Math.abs(t.clientX-this.down_.clientX)>this.moveTolerance_||Math.abs(t.clientY-this.down_.clientY)>this.moveTolerance_},e.prototype.disposeInternal=function(){this.relayedListenerKey_&&(P(this.relayedListenerKey_),this.relayedListenerKey_=null),this.element_.removeEventListener(T,this.boundHandleTouchMove_),this.pointerdownListenerKey_&&(P(this.pointerdownListenerKey_),this.pointerdownListenerKey_=null),this.dragListenerKeys_.forEach(P),this.dragListenerKeys_.length=0,this.element_=null,t.prototype.disposeInternal.call(this)},e}(m),Ba="layergroup",Ka="size",Za="target",Va="view",Ua=1/0,Ha=function(){function t(t,e){this.priorityFunction_=t,this.keyFunction_=e,this.elements_=[],this.priorities_=[],this.queuedElements_={}}return t.prototype.clear=function(){this.elements_.length=0,this.priorities_.length=0,d(this.queuedElements_)},t.prototype.dequeue=function(){var t=this.elements_,e=this.priorities_,i=t[0];1==t.length?(t.length=0,e.length=0):(t[0]=t.pop(),e[0]=e.pop(),this.siftUp_(0));var n=this.keyFunction_(i);return delete this.queuedElements_[n],i},t.prototype.enqueue=function(t){xt(!(this.keyFunction_(t)in this.queuedElements_),31);var e=this.priorityFunction_(t);return e!=Ua&&(this.elements_.push(t),this.priorities_.push(e),this.queuedElements_[this.keyFunction_(t)]=!0,this.siftDown_(0,this.elements_.length-1),!0)},t.prototype.getCount=function(){return this.elements_.length},t.prototype.getLeftChildIndex_=function(t){return 2*t+1},t.prototype.getRightChildIndex_=function(t){return 2*t+2},t.prototype.getParentIndex_=function(t){return t-1>>1},t.prototype.heapify_=function(){var t;for(t=(this.elements_.length>>1)-1;t>=0;t--)this.siftUp_(t)},t.prototype.isEmpty=function(){return 0===this.elements_.length},t.prototype.isKeyQueued=function(t){return t in this.queuedElements_},t.prototype.isQueued=function(t){return this.isKeyQueued(this.keyFunction_(t))},t.prototype.siftUp_=function(t){for(var e=this.elements_,i=this.priorities_,n=e.length,o=e[t],r=i[t],s=t;t<n>>1;){var a=this.getLeftChildIndex_(t),l=this.getRightChildIndex_(t),h=l<n&&i[l]<i[a]?l:a;e[t]=e[h],i[t]=i[h],t=h}e[t]=o,i[t]=r,this.siftDown_(s,t)},t.prototype.siftDown_=function(t,e){for(var i=this.elements_,n=this.priorities_,o=i[e],r=n[e];e>t;){var s=this.getParentIndex_(e);if(!(n[s]>r))break;i[e]=i[s],n[e]=n[s],e=s}i[e]=o,n[e]=r},t.prototype.reprioritize=function(){var t,e,i,n=this.priorityFunction_,o=this.elements_,r=this.priorities_,s=0,a=o.length;for(e=0;e<a;++e)(i=n(t=o[e]))==Ua?delete this.queuedElements_[this.keyFunction_(t)]:(r[s]=i,o[s++]=t);o.length=s,r.length=s,this.heapify_()},t}(),qa=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Ja=function(t){function e(e,i){var n=t.call(this,(function(t){return e.apply(null,t)}),(function(t){return t[0].getKey()}))||this;return n.boundHandleTileChange_=n.handleTileChange.bind(n),n.tileChangeCallback_=i,n.tilesLoading_=0,n.tilesLoadingKeys_={},n}return qa(e,t),e.prototype.enqueue=function(e){var i=t.prototype.enqueue.call(this,e);return i&&e[0].addEventListener(x,this.boundHandleTileChange_),i},e.prototype.getTilesLoading=function(){return this.tilesLoading_},e.prototype.handleTileChange=function(t){var e=t.target,i=e.getState();if(2===i||3===i||4===i){e.removeEventListener(x,this.boundHandleTileChange_);var n=e.getKey();n in this.tilesLoadingKeys_&&(delete this.tilesLoadingKeys_[n],--this.tilesLoading_),this.tileChangeCallback_()}},e.prototype.loadMoreTiles=function(t,e){for(var i,n,o=0;this.tilesLoading_<t&&o<e&&this.getCount()>0;)n=(i=this.dequeue()[0]).getKey(),0!==i.getState()||n in this.tilesLoadingKeys_||(this.tilesLoadingKeys_[n]=!0,++this.tilesLoading_,++o,i.load())},e}(Ha),Qa={CENTER:"center",RESOLUTION:"resolution",ROTATION:"rotation"};function $a(t,e,i){return function(n,o,r,s,a){if(n){if(!o&&!e)return n;var l=e?0:r[0]*o,h=e?0:r[1]*o,u=a?a[0]:0,c=a?a[1]:0,p=t[0]+l/2+u,f=t[2]-l/2+u,d=t[1]+h/2+c,g=t[3]-h/2+c;p>f&&(f=p=(f+p)/2),d>g&&(g=d=(g+d)/2);var _=Ct(n[0],p,f),y=Ct(n[1],d,g);if(s&&i&&o){var v=30*o;_+=-v*Math.log(1+Math.max(0,p-n[0])/v)+v*Math.log(1+Math.max(0,n[0]-f)/v),y+=-v*Math.log(1+Math.max(0,d-n[1])/v)+v*Math.log(1+Math.max(0,n[1]-g)/v)}return[_,y]}}}function tl(t){return t}function el(t,e,i,n){var o=ke(e)/i[0],r=Fe(e)/i[1];return n?Math.min(t,Math.max(o,r)):Math.min(t,Math.min(o,r))}function il(t,e,i){var n=Math.min(t,e);return n*=Math.log(1+50*Math.max(0,t/e-1))/50+1,i&&(n=Math.max(n,i),n/=Math.log(1+50*Math.max(0,i/t-1))/50+1),Ct(n,i/2,2*e)}function nl(t,e,i,n,o){return function(r,s,a,l){if(void 0!==r){var h=n?el(t,n,a,o):t;return(void 0===i||i)&&l?il(r,h,e):Ct(r,e,h)}}}function ol(t){return void 0!==t?0:void 0}function rl(t){return void 0!==t?t:void 0}var sl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),al=function(t){function e(e){var i=t.call(this)||this;i.on,i.once,i.un;var n=f({},e);return i.hints_=[0,0],i.animations_=[],i.updateAnimationKey_,i.projection_=Qe(n.projection,"EPSG:3857"),i.viewportSize_=[100,100],i.targetCenter_=null,i.targetResolution_,i.targetRotation_,i.nextCenter_=null,i.nextResolution_,i.nextRotation_,i.cancelAnchor_=void 0,n.projection&&Ke(),n.center&&(n.center=ui(n.center,i.projection_)),n.extent&&(n.extent=pi(n.extent,i.projection_)),i.applyOptions_(n),i}return sl(e,t),e.prototype.applyOptions_=function(t){var e=f({},t);for(var i in Qa)delete e[i];this.setProperties(e,!0);var n=function(t){var e,i,n,o=void 0!==t.minZoom?t.minZoom:0,r=void 0!==t.maxZoom?t.maxZoom:28,a=void 0!==t.zoomFactor?t.zoomFactor:2,l=void 0!==t.multiWorld&&t.multiWorld,h=void 0===t.smoothResolutionConstraint||t.smoothResolutionConstraint,u=void 0!==t.showFullExtent&&t.showFullExtent,c=Qe(t.projection,"EPSG:3857"),p=c.getExtent(),f=t.constrainOnlyCenter,d=t.extent;if(l||d||!c.isGlobal()||(f=!1,d=p),void 0!==t.resolutions){var g=t.resolutions;i=g[o],n=void 0!==g[r]?g[r]:g[g.length-1],e=t.constrainResolution?function(t,e,i,n){return function(o,r,a,l){if(void 0!==o){var h=t[0],u=t[t.length-1],c=i?el(h,i,a,n):h;if(l)return void 0===e||e?il(o,c,u):Ct(o,u,c);var p=Math.min(c,o),f=Math.floor(s(t,p,r));return t[f]>c&&f<t.length-1?t[f+1]:t[f]}}}(g,h,!f&&d,u):nl(i,n,h,!f&&d,u)}else{var _=(p?Math.max(ke(p),Fe(p)):360*Bt[Kt.DEGREES]/c.getMetersPerUnit())/256/Math.pow(2,0),y=_/Math.pow(2,28);void 0!==(i=t.maxResolution)?o=0:i=_/Math.pow(a,o),void 0===(n=t.minResolution)&&(n=void 0!==t.maxZoom?void 0!==t.maxResolution?i/Math.pow(a,r):_/Math.pow(a,r):y),r=o+Math.floor(Math.log(i/n)/Math.log(a)),n=i/Math.pow(a,r-o),e=t.constrainResolution?function(t,e,i,n,o,r){return function(s,a,l,h){if(void 0!==s){var u=o?el(e,o,l,r):e,c=void 0!==i?i:0;if(h)return void 0===n||n?il(s,u,c):Ct(s,c,u);var p=Math.ceil(Math.log(e/u)/Math.log(t)-1e-9),f=-a*(.5-1e-9)+.5,d=Math.min(u,s),g=Math.floor(Math.log(e/d)/Math.log(t)+f),_=Math.max(p,g);return Ct(e/Math.pow(t,_),c,u)}}}(a,i,n,h,!f&&d,u):nl(i,n,h,!f&&d,u)}return{constraint:e,maxResolution:i,minResolution:n,minZoom:o,zoomFactor:a}}(t);this.maxResolution_=n.maxResolution,this.minResolution_=n.minResolution,this.zoomFactor_=n.zoomFactor,this.resolutions_=t.resolutions,this.padding_=t.padding,this.minZoom_=n.minZoom;var o=function(t){if(void 0!==t.extent){var e=void 0===t.smoothExtentConstraint||t.smoothExtentConstraint;return $a(t.extent,t.constrainOnlyCenter,e)}var i=Qe(t.projection,"EPSG:3857");if(!0!==t.multiWorld&&i.isGlobal()){var n=i.getExtent().slice();return n[0]=-1/0,n[2]=1/0,$a(n,!1,!1)}return tl}(t),r=n.constraint,a=function(t){var e,i,n;if(void 0===t.enableRotation||t.enableRotation){var o=t.constrainRotation;return void 0===o||!0===o?(n=bt(5),function(t,e){return e?t:void 0!==t?Math.abs(t)<=n?0:t:void 0}):!1===o?rl:"number"==typeof o?(e=o,i=2*Math.PI/e,function(t,e){return e?t:void 0!==t?t=Math.floor(t/i+.5)*i:void 0}):rl}return ol}(t);this.constraints_={center:o,resolution:r,rotation:a},this.setRotation(void 0!==t.rotation?t.rotation:0),this.setCenterInternal(void 0!==t.center?t.center:null),void 0!==t.resolution?this.setResolution(t.resolution):void 0!==t.zoom&&this.setZoom(t.zoom)},Object.defineProperty(e.prototype,"padding",{get:function(){return this.padding_},set:function(t){var e=this.padding_;this.padding_=t;var i=this.getCenter();if(i){var n=t||[0,0,0,0];e=e||[0,0,0,0];var o=this.getResolution(),r=o/2*(n[3]-e[3]+e[1]-n[1]),s=o/2*(n[0]-e[0]+e[2]-n[2]);this.setCenterInternal([i[0]+r,i[1]-s])}},enumerable:!1,configurable:!0}),e.prototype.getUpdatedOptions_=function(t){var e=this.getProperties();return void 0!==e.resolution?e.resolution=this.getResolution():e.zoom=this.getZoom(),e.center=this.getCenterInternal(),e.rotation=this.getRotation(),f({},e,t)},e.prototype.animate=function(t){this.isDef()&&!this.getAnimating()&&this.resolveConstraints(0);for(var e=new Array(arguments.length),i=0;i<e.length;++i){var n=arguments[i];n.center&&((n=f({},n)).center=ui(n.center,this.getProjection())),n.anchor&&((n=f({},n)).anchor=ui(n.anchor,this.getProjection())),e[i]=n}this.animateInternal.apply(this,e)},e.prototype.animateInternal=function(t){var e,i=arguments.length;i>1&&"function"==typeof arguments[i-1]&&(e=arguments[i-1],--i);for(var n=0;n<i&&!this.isDef();++n){var o=arguments[n];o.center&&this.setCenterInternal(o.center),void 0!==o.zoom?this.setZoom(o.zoom):o.resolution&&this.setResolution(o.resolution),void 0!==o.rotation&&this.setRotation(o.rotation)}if(n!==i){for(var r=Date.now(),s=this.targetCenter_.slice(),a=this.targetResolution_,l=this.targetRotation_,h=[];n<i;++n){var u=arguments[n],c={start:r,complete:!1,anchor:u.anchor,duration:void 0!==u.duration?u.duration:1e3,easing:u.easing||Ci,callback:e};if(u.center&&(c.sourceCenter=s,c.targetCenter=u.center.slice(),s=c.targetCenter),void 0!==u.zoom?(c.sourceResolution=a,c.targetResolution=this.getResolutionForZoom(u.zoom),a=c.targetResolution):u.resolution&&(c.sourceResolution=a,c.targetResolution=u.resolution,a=c.targetResolution),void 0!==u.rotation){c.sourceRotation=l;var p=Ot(u.rotation-l+Math.PI,2*Math.PI)-Math.PI;c.targetRotation=l+p,l=c.targetRotation}hl(c)?c.complete=!0:r+=c.duration,h.push(c)}this.animations_.push(h),this.setHint(0,1),this.updateAnimations_()}else e&&ll(e,!0)},e.prototype.getAnimating=function(){return this.hints_[0]>0},e.prototype.getInteracting=function(){return this.hints_[1]>0},e.prototype.cancelAnimations=function(){var t;this.setHint(0,-this.hints_[0]);for(var e=0,i=this.animations_.length;e<i;++e){var n=this.animations_[e];if(n[0].callback&&ll(n[0].callback,!1),!t)for(var o=0,r=n.length;o<r;++o){var s=n[o];if(!s.complete){t=s.anchor;break}}}this.animations_.length=0,this.cancelAnchor_=t,this.nextCenter_=null,this.nextResolution_=NaN,this.nextRotation_=NaN},e.prototype.updateAnimations_=function(){if(void 0!==this.updateAnimationKey_&&(cancelAnimationFrame(this.updateAnimationKey_),this.updateAnimationKey_=void 0),this.getAnimating()){for(var t=Date.now(),e=!1,i=this.animations_.length-1;i>=0;--i){for(var n=this.animations_[i],o=!0,r=0,s=n.length;r<s;++r){var a=n[r];if(!a.complete){var l=t-a.start,h=a.duration>0?l/a.duration:1;h>=1?(a.complete=!0,h=1):o=!1;var u=a.easing(h);if(a.sourceCenter){var c=a.sourceCenter[0],p=a.sourceCenter[1],f=a.targetCenter[0],d=a.targetCenter[1];this.nextCenter_=a.targetCenter;var g=c+u*(f-c),_=p+u*(d-p);this.targetCenter_=[g,_]}if(a.sourceResolution&&a.targetResolution){var y=1===u?a.targetResolution:a.sourceResolution+u*(a.targetResolution-a.sourceResolution);if(a.anchor){var v=this.getViewportSize_(this.getRotation()),m=this.constraints_.resolution(y,0,v,!0);this.targetCenter_=this.calculateCenterZoom(m,a.anchor)}this.nextResolution_=a.targetResolution,this.targetResolution_=y,this.applyTargetState_(!0)}if(void 0!==a.sourceRotation&&void 0!==a.targetRotation){var x=1===u?Ot(a.targetRotation+Math.PI,2*Math.PI)-Math.PI:a.sourceRotation+u*(a.targetRotation-a.sourceRotation);if(a.anchor){var C=this.constraints_.rotation(x,!0);this.targetCenter_=this.calculateCenterRotate(C,a.anchor)}this.nextRotation_=a.targetRotation,this.targetRotation_=x}if(this.applyTargetState_(!0),e=!0,!a.complete)break}}if(o){this.animations_[i]=null,this.setHint(0,-1),this.nextCenter_=null,this.nextResolution_=NaN,this.nextRotation_=NaN;var w=n[0].callback;w&&ll(w,!0)}}this.animations_=this.animations_.filter(Boolean),e&&void 0===this.updateAnimationKey_&&(this.updateAnimationKey_=requestAnimationFrame(this.updateAnimations_.bind(this)))}},e.prototype.calculateCenterRotate=function(t,e){var i,n,o,r=this.getCenterInternal();return void 0!==r&&(Xe(i=[r[0]-e[0],r[1]-e[1]],t-this.getRotation()),o=e,(n=i)[0]+=+o[0],n[1]+=+o[1]),i},e.prototype.calculateCenterZoom=function(t,e){var i,n=this.getCenterInternal(),o=this.getResolution();return void 0!==n&&void 0!==o&&(i=[e[0]-t*(e[0]-n[0])/o,e[1]-t*(e[1]-n[1])/o]),i},e.prototype.getViewportSize_=function(t){var e=this.viewportSize_;if(t){var i=e[0],n=e[1];return[Math.abs(i*Math.cos(t))+Math.abs(n*Math.sin(t)),Math.abs(i*Math.sin(t))+Math.abs(n*Math.cos(t))]}return e},e.prototype.setViewportSize=function(t){this.viewportSize_=Array.isArray(t)?t.slice():[100,100],this.getAnimating()||this.resolveConstraints(0)},e.prototype.getCenter=function(){var t=this.getCenterInternal();return t?hi(t,this.getProjection()):t},e.prototype.getCenterInternal=function(){return this.get(Qa.CENTER)},e.prototype.getConstraints=function(){return this.constraints_},e.prototype.getConstrainResolution=function(){return this.get("constrainResolution")},e.prototype.getHints=function(t){return void 0!==t?(t[0]=this.hints_[0],t[1]=this.hints_[1],t):this.hints_.slice()},e.prototype.calculateExtent=function(t){return ci(this.calculateExtentInternal(t),this.getProjection())},e.prototype.calculateExtentInternal=function(t){var e=t||this.getViewportSizeMinusPadding_(),i=this.getCenterInternal();xt(i,1);var n=this.getResolution();xt(void 0!==n,2);var o=this.getRotation();return xt(void 0!==o,3),Ie(i,n,o,e)},e.prototype.getMaxResolution=function(){return this.maxResolution_},e.prototype.getMinResolution=function(){return this.minResolution_},e.prototype.getMaxZoom=function(){return this.getZoomForResolution(this.minResolution_)},e.prototype.setMaxZoom=function(t){this.applyOptions_(this.getUpdatedOptions_({maxZoom:t}))},e.prototype.getMinZoom=function(){return this.getZoomForResolution(this.maxResolution_)},e.prototype.setMinZoom=function(t){this.applyOptions_(this.getUpdatedOptions_({minZoom:t}))},e.prototype.setConstrainResolution=function(t){this.applyOptions_(this.getUpdatedOptions_({constrainResolution:t}))},e.prototype.getProjection=function(){return this.projection_},e.prototype.getResolution=function(){return this.get(Qa.RESOLUTION)},e.prototype.getResolutions=function(){return this.resolutions_},e.prototype.getResolutionForExtent=function(t,e){return this.getResolutionForExtentInternal(pi(t,this.getProjection()),e)},e.prototype.getResolutionForExtentInternal=function(t,e){var i=e||this.getViewportSizeMinusPadding_(),n=ke(t)/i[0],o=Fe(t)/i[1];return Math.max(n,o)},e.prototype.getResolutionForValueFunction=function(t){var e=t||2,i=this.getConstrainedResolution(this.maxResolution_),n=this.minResolution_,o=Math.log(i/n)/Math.log(e);return function(t){return i/Math.pow(e,t*o)}},e.prototype.getRotation=function(){return this.get(Qa.ROTATION)},e.prototype.getValueForResolutionFunction=function(t){var e=Math.log(t||2),i=this.getConstrainedResolution(this.maxResolution_),n=this.minResolution_,o=Math.log(i/n)/e;return function(t){return Math.log(i/t)/e/o}},e.prototype.getViewportSizeMinusPadding_=function(t){var e=this.getViewportSize_(t),i=this.padding_;return i&&(e=[e[0]-i[1]-i[3],e[1]-i[0]-i[2]]),e},e.prototype.getState=function(){var t=this.getProjection(),e=this.getResolution(),i=this.getRotation(),n=this.getCenterInternal(),o=this.padding_;if(o){var r=this.getViewportSizeMinusPadding_();n=ul(n,this.getViewportSize_(),[r[0]/2+o[3],r[1]/2+o[0]],e,i)}return{center:n.slice(0),projection:void 0!==t?t:null,resolution:e,nextCenter:this.nextCenter_,nextResolution:this.nextResolution_,nextRotation:this.nextRotation_,rotation:i,zoom:this.getZoom()}},e.prototype.getZoom=function(){var t,e=this.getResolution();return void 0!==e&&(t=this.getZoomForResolution(e)),t},e.prototype.getZoomForResolution=function(t){var e,i,n=this.minZoom_||0;if(this.resolutions_){var o=s(this.resolutions_,t,1);n=o,e=this.resolutions_[o],i=o==this.resolutions_.length-1?2:e/this.resolutions_[o+1]}else e=this.maxResolution_,i=this.zoomFactor_;return n+Math.log(e/t)/Math.log(i)},e.prototype.getResolutionForZoom=function(t){if(this.resolutions_){if(this.resolutions_.length<=1)return 0;var e=Ct(Math.floor(t),0,this.resolutions_.length-2),i=this.resolutions_[e]/this.resolutions_[e+1];return this.resolutions_[e]/Math.pow(i,Ct(t-e,0,1))}return this.maxResolution_/Math.pow(this.zoomFactor_,t-this.minZoom_)},e.prototype.fit=function(t,e){var i;if(xt(Array.isArray(t)||"function"==typeof t.getSimplifiedGeometry,24),Array.isArray(t))xt(!Ge(t),25),i=Wn(n=pi(t,this.getProjection()));else if("Circle"===t.getType()){var n;(i=Wn(n=pi(t.getExtent(),this.getProjection()))).rotate(this.getRotation(),Re(n))}else{var o=li();i=o?t.clone().transform(o,this.getProjection()):t}this.fitInternal(i,e)},e.prototype.rotatedExtentForGeometry=function(t){for(var e=this.getRotation(),i=Math.cos(e),n=Math.sin(-e),o=t.getFlatCoordinates(),r=t.getStride(),s=1/0,a=1/0,l=-1/0,h=-1/0,u=0,c=o.length;u<c;u+=r){var p=o[u]*i-o[u+1]*n,f=o[u]*n+o[u+1]*i;s=Math.min(s,p),a=Math.min(a,f),l=Math.max(l,p),h=Math.max(h,f)}return[s,a,l,h]},e.prototype.fitInternal=function(t,e){var i=e||{},n=i.size;n||(n=this.getViewportSizeMinusPadding_());var o,r=void 0!==i.padding?i.padding:[0,0,0,0],s=void 0!==i.nearest&&i.nearest;o=void 0!==i.minResolution?i.minResolution:void 0!==i.maxZoom?this.getResolutionForZoom(i.maxZoom):0;var a=this.rotatedExtentForGeometry(t),l=this.getResolutionForExtentInternal(a,[n[0]-r[1]-r[3],n[1]-r[0]-r[2]]);l=isNaN(l)?o:Math.max(l,o),l=this.getConstrainedResolution(l,s?0:1);var h=this.getRotation(),u=Math.sin(h),c=Math.cos(h),f=Re(a);f[0]+=(r[1]-r[3])/2*l,f[1]+=(r[0]-r[2])/2*l;var d=f[0]*c-f[1]*u,g=f[1]*c+f[0]*u,_=this.getConstrainedCenter([d,g],l),y=i.callback?i.callback:p;void 0!==i.duration?this.animateInternal({resolution:l,center:_,duration:i.duration,easing:i.easing},y):(this.targetResolution_=l,this.targetCenter_=_,this.applyTargetState_(!1,!0),ll(y,!0))},e.prototype.centerOn=function(t,e,i){this.centerOnInternal(ui(t,this.getProjection()),e,i)},e.prototype.centerOnInternal=function(t,e,i){this.setCenterInternal(ul(t,e,i,this.getResolution(),this.getRotation()))},e.prototype.calculateCenterShift=function(t,e,i,n){var o,r=this.padding_;if(r&&t){var s=this.getViewportSizeMinusPadding_(-i),a=ul(t,n,[s[0]/2+r[3],s[1]/2+r[0]],e,i);o=[t[0]-a[0],t[1]-a[1]]}return o},e.prototype.isDef=function(){return!!this.getCenterInternal()&&void 0!==this.getResolution()},e.prototype.adjustCenter=function(t){var e=hi(this.targetCenter_,this.getProjection());this.setCenter([e[0]+t[0],e[1]+t[1]])},e.prototype.adjustCenterInternal=function(t){var e=this.targetCenter_;this.setCenterInternal([e[0]+t[0],e[1]+t[1]])},e.prototype.adjustResolution=function(t,e){var i=e&&ui(e,this.getProjection());this.adjustResolutionInternal(t,i)},e.prototype.adjustResolutionInternal=function(t,e){var i=this.getAnimating()||this.getInteracting(),n=this.getViewportSize_(this.getRotation()),o=this.constraints_.resolution(this.targetResolution_*t,0,n,i);e&&(this.targetCenter_=this.calculateCenterZoom(o,e)),this.targetResolution_*=t,this.applyTargetState_()},e.prototype.adjustZoom=function(t,e){this.adjustResolution(Math.pow(this.zoomFactor_,-t),e)},e.prototype.adjustRotation=function(t,e){e&&(e=ui(e,this.getProjection())),this.adjustRotationInternal(t,e)},e.prototype.adjustRotationInternal=function(t,e){var i=this.getAnimating()||this.getInteracting(),n=this.constraints_.rotation(this.targetRotation_+t,i);e&&(this.targetCenter_=this.calculateCenterRotate(n,e)),this.targetRotation_+=t,this.applyTargetState_()},e.prototype.setCenter=function(t){this.setCenterInternal(t?ui(t,this.getProjection()):t)},e.prototype.setCenterInternal=function(t){this.targetCenter_=t,this.applyTargetState_()},e.prototype.setHint=function(t,e){return this.hints_[t]+=e,this.changed(),this.hints_[t]},e.prototype.setResolution=function(t){this.targetResolution_=t,this.applyTargetState_()},e.prototype.setRotation=function(t){this.targetRotation_=t,this.applyTargetState_()},e.prototype.setZoom=function(t){this.setResolution(this.getResolutionForZoom(t))},e.prototype.applyTargetState_=function(t,e){var i=this.getAnimating()||this.getInteracting()||e,n=this.constraints_.rotation(this.targetRotation_,i),o=this.getViewportSize_(n),r=this.constraints_.resolution(this.targetResolution_,0,o,i),s=this.constraints_.center(this.targetCenter_,r,o,i,this.calculateCenterShift(this.targetCenter_,r,n,o));this.get(Qa.ROTATION)!==n&&this.set(Qa.ROTATION,n),this.get(Qa.RESOLUTION)!==r&&(this.set(Qa.RESOLUTION,r),this.set("zoom",this.getZoom(),!0)),s&&this.get(Qa.CENTER)&&We(this.get(Qa.CENTER),s)||this.set(Qa.CENTER,s),this.getAnimating()&&!t&&this.cancelAnimations(),this.cancelAnchor_=void 0},e.prototype.resolveConstraints=function(t,e,i){var n=void 0!==t?t:200,o=e||0,r=this.constraints_.rotation(this.targetRotation_),s=this.getViewportSize_(r),a=this.constraints_.resolution(this.targetResolution_,o,s),l=this.constraints_.center(this.targetCenter_,a,s,!1,this.calculateCenterShift(this.targetCenter_,a,r,s));if(0===n&&!this.cancelAnchor_)return this.targetResolution_=a,this.targetRotation_=r,this.targetCenter_=l,void this.applyTargetState_();var h=i||(0===n?this.cancelAnchor_:void 0);this.cancelAnchor_=void 0,this.getResolution()===a&&this.getRotation()===r&&this.getCenterInternal()&&We(this.getCenterInternal(),l)||(this.getAnimating()&&this.cancelAnimations(),this.animateInternal({rotation:r,center:l,resolution:a,duration:n,easing:xi,anchor:h}))},e.prototype.beginInteraction=function(){this.resolveConstraints(0),this.setHint(1,1)},e.prototype.endInteraction=function(t,e,i){var n=i&&ui(i,this.getProjection());this.endInteractionInternal(t,e,n)},e.prototype.endInteractionInternal=function(t,e,i){this.setHint(1,-1),this.resolveConstraints(t,e,i)},e.prototype.getConstrainedCenter=function(t,e){var i=this.getViewportSize_(this.getRotation());return this.constraints_.center(t,e||this.getResolution(),i)},e.prototype.getConstrainedZoom=function(t,e){var i=this.getResolutionForZoom(t);return this.getZoomForResolution(this.getConstrainedResolution(i,e))},e.prototype.getConstrainedResolution=function(t,e){var i=e||0,n=this.getViewportSize_(this.getRotation());return this.constraints_.resolution(t,i,n)},e}(G);function ll(t,e){setTimeout((function(){t(e)}),0)}function hl(t){return!(t.sourceCenter&&t.targetCenter&&!We(t.sourceCenter,t.targetCenter))&&t.sourceResolution===t.targetResolution&&t.sourceRotation===t.targetRotation}function ul(t,e,i,n,o){var r=Math.cos(-o),s=Math.sin(-o),a=t[0]*r-t[1]*s,l=t[1]*r+t[0]*s;return[(a+=(e[0]/2-i[0])*n)*r-(l+=(i[1]-e[1]/2)*n)*(s=-s),l*r+a*s]}var cl=al,pl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function fl(t){t instanceof Gt?t.setMapInternal(null):t instanceof ka&&t.getLayers().forEach(fl)}function dl(t,e){if(t instanceof Gt)t.setMapInternal(e);else if(t instanceof ka)for(var i=t.getLayers().getArray(),n=0,o=i.length;n<o;++n)dl(i[n],e)}var gl=function(t){function i(e){var i=t.call(this)||this;i.on,i.once,i.un;var n=function(t){var e=null;void 0!==t.keyboardEventTarget&&(e="string"==typeof t.keyboardEventTarget?document.getElementById(t.keyboardEventTarget):t.keyboardEventTarget);var i,n,o,r={},s=t.layers&&"function"==typeof t.layers.getLayers?t.layers:new ka({layers:t.layers});return r[Ba]=s,r[Za]=t.target,r[Va]=t.view instanceof cl?t.view:new cl,void 0!==t.controls&&(Array.isArray(t.controls)?i=new da(t.controls.slice()):(xt("function"==typeof t.controls.getArray,47),i=t.controls)),void 0!==t.interactions&&(Array.isArray(t.interactions)?n=new da(t.interactions.slice()):(xt("function"==typeof t.interactions.getArray,48),n=t.interactions)),void 0!==t.overlays?Array.isArray(t.overlays)?o=new da(t.overlays.slice()):(xt("function"==typeof t.overlays.getArray,49),o=t.overlays):o=new da,{controls:i,interactions:n,keyboardEventTarget:e,overlays:o,values:r}}(e);i.renderComplete_,i.loaded_=!0,i.boundHandleBrowserEvent_=i.handleBrowserEvent.bind(i),i.maxTilesLoading_=void 0!==e.maxTilesLoading?e.maxTilesLoading:16,i.pixelRatio_=void 0!==e.pixelRatio?e.pixelRatio:Z,i.postRenderTimeoutHandle_,i.animationDelayKey_,i.animationDelay_=function(){this.animationDelayKey_=void 0,this.renderFrame_(Date.now())}.bind(i),i.coordinateToPixelTransform_=[1,0,0,1,0,0],i.pixelToCoordinateTransform_=[1,0,0,1,0,0],i.frameIndex_=0,i.frameState_=null,i.previousExtent_=null,i.viewPropertyListenerKey_=null,i.viewChangeListenerKey_=null,i.layerGroupPropertyListenerKeys_=null,i.viewport_=document.createElement("div"),i.viewport_.className="ol-viewport"+("ontouchstart"in window?" ol-touch":""),i.viewport_.style.position="relative",i.viewport_.style.overflow="hidden",i.viewport_.style.width="100%",i.viewport_.style.height="100%",i.overlayContainer_=document.createElement("div"),i.overlayContainer_.style.position="absolute",i.overlayContainer_.style.zIndex="0",i.overlayContainer_.style.width="100%",i.overlayContainer_.style.height="100%",i.overlayContainer_.style.pointerEvents="none",i.overlayContainer_.className="ol-overlaycontainer",i.viewport_.appendChild(i.overlayContainer_),i.overlayContainerStopEvent_=document.createElement("div"),i.overlayContainerStopEvent_.style.position="absolute",i.overlayContainerStopEvent_.style.zIndex="0",i.overlayContainerStopEvent_.style.width="100%",i.overlayContainerStopEvent_.style.height="100%",i.overlayContainerStopEvent_.style.pointerEvents="none",i.overlayContainerStopEvent_.className="ol-overlaycontainer-stopevent",i.viewport_.appendChild(i.overlayContainerStopEvent_),i.mapBrowserEventHandler_=null,i.moveTolerance_=e.moveTolerance,i.keyboardEventTarget_=n.keyboardEventTarget,i.targetChangeHandlerKeys_=null,i.controls=n.controls||new da,i.interactions=n.interactions||new da,i.overlays_=n.overlays,i.overlayIdIndex_={},i.renderer_=null,i.postRenderFunctions_=[],i.tileQueue_=new Ja(i.getTilePriority.bind(i),i.handleTileChange_.bind(i)),i.addChangeListener(Ba,i.handleLayerGroupChanged_),i.addChangeListener(Va,i.handleViewChanged_),i.addChangeListener(Ka,i.handleSizeChanged_),i.addChangeListener(Za,i.handleTargetChanged_),i.setProperties(n.values);var o=i;return!e.view||e.view instanceof cl||e.view.then((function(t){o.setView(new cl(t))})),i.controls.addEventListener(ha,function(t){t.element.setMap(this)}.bind(i)),i.controls.addEventListener(ua,function(t){t.element.setMap(null)}.bind(i)),i.interactions.addEventListener(ha,function(t){t.element.setMap(this)}.bind(i)),i.interactions.addEventListener(ua,function(t){t.element.setMap(null)}.bind(i)),i.overlays_.addEventListener(ha,function(t){this.addOverlayInternal_(t.element)}.bind(i)),i.overlays_.addEventListener(ua,function(t){var e=t.element.getId();void 0!==e&&delete this.overlayIdIndex_[e.toString()],t.element.setMap(null)}.bind(i)),i.controls.forEach(function(t){t.setMap(this)}.bind(i)),i.interactions.forEach(function(t){t.setMap(this)}.bind(i)),i.overlays_.forEach(i.addOverlayInternal_.bind(i)),i}return pl(i,t),i.prototype.createRenderer=function(){throw new Error("Use a map type that has a createRenderer method")},i.prototype.addControl=function(t){this.getControls().push(t)},i.prototype.addInteraction=function(t){this.getInteractions().push(t)},i.prototype.addLayer=function(t){this.getLayerGroup().getLayers().push(t)},i.prototype.handleLayerAdd_=function(t){dl(t.layer,this)},i.prototype.addOverlay=function(t){this.getOverlays().push(t)},i.prototype.addOverlayInternal_=function(t){var e=t.getId();void 0!==e&&(this.overlayIdIndex_[e.toString()]=t),t.setMap(this)},i.prototype.disposeInternal=function(){this.controls.clear(),this.interactions.clear(),this.overlays_.clear(),this.setTarget(null),t.prototype.disposeInternal.call(this)},i.prototype.forEachFeatureAtPixel=function(t,e,i){if(this.frameState_&&this.renderer_){var n=this.getCoordinateFromPixelInternal(t),o=void 0!==(i=void 0!==i?i:{}).hitTolerance?i.hitTolerance:0,r=void 0!==i.layerFilter?i.layerFilter:u,s=!1!==i.checkWrapped;return this.renderer_.forEachFeatureAtCoordinate(n,this.frameState_,o,s,e,null,r,null)}},i.prototype.getFeaturesAtPixel=function(t,e){var i=[];return this.forEachFeatureAtPixel(t,(function(t){i.push(t)}),e),i},i.prototype.getAllLayers=function(){var t=[];return function e(i){i.forEach((function(i){i instanceof ka?e(i.getLayers()):t.push(i)}))}(this.getLayers()),t},i.prototype.forEachLayerAtPixel=function(t,e,i){if(this.frameState_&&this.renderer_){var n=i||{},o=void 0!==n.hitTolerance?n.hitTolerance:0,r=n.layerFilter||u;return this.renderer_.forEachLayerAtPixel(t,this.frameState_,o,e,r)}},i.prototype.hasFeatureAtPixel=function(t,e){if(!this.frameState_||!this.renderer_)return!1;var i=this.getCoordinateFromPixelInternal(t),n=void 0!==(e=void 0!==e?e:{}).layerFilter?e.layerFilter:u,o=void 0!==e.hitTolerance?e.hitTolerance:0,r=!1!==e.checkWrapped;return this.renderer_.hasFeatureAtCoordinate(i,this.frameState_,o,r,n,null)},i.prototype.getEventCoordinate=function(t){return this.getCoordinateFromPixel(this.getEventPixel(t))},i.prototype.getEventCoordinateInternal=function(t){return this.getCoordinateFromPixelInternal(this.getEventPixel(t))},i.prototype.getEventPixel=function(t){var e=this.viewport_.getBoundingClientRect(),i="changedTouches"in t?t.changedTouches[0]:t;return[i.clientX-e.left,i.clientY-e.top]},i.prototype.getTarget=function(){return this.get(Za)},i.prototype.getTargetElement=function(){var t=this.getTarget();return void 0!==t?"string"==typeof t?document.getElementById(t):t:null},i.prototype.getCoordinateFromPixel=function(t){return hi(this.getCoordinateFromPixelInternal(t),this.getView().getProjection())},i.prototype.getCoordinateFromPixelInternal=function(t){var e=this.frameState_;return e?Pi(e.pixelToCoordinateTransform,t.slice()):null},i.prototype.getControls=function(){return this.controls},i.prototype.getOverlays=function(){return this.overlays_},i.prototype.getOverlayById=function(t){var e=this.overlayIdIndex_[t.toString()];return void 0!==e?e:null},i.prototype.getInteractions=function(){return this.interactions},i.prototype.getLayerGroup=function(){return this.get(Ba)},i.prototype.setLayers=function(t){var e=this.getLayerGroup();if(t instanceof da)e.setLayers(t);else{var i=e.getLayers();i.clear(),i.extend(t)}},i.prototype.getLayers=function(){return this.getLayerGroup().getLayers()},i.prototype.getLoadingOrNotReady=function(){for(var t=this.getLayerGroup().getLayerStatesArray(),e=0,i=t.length;e<i;++e){var n=t[e];if(n.visible){var o=n.layer.getRenderer();if(o&&!o.ready)return!0;var r=n.layer.getSource();if(r&&r.loading)return!0}}return!1},i.prototype.getPixelFromCoordinate=function(t){var e=ui(t,this.getView().getProjection());return this.getPixelFromCoordinateInternal(e)},i.prototype.getPixelFromCoordinateInternal=function(t){var e=this.frameState_;return e?Pi(e.coordinateToPixelTransform,t.slice(0,2)):null},i.prototype.getRenderer=function(){return this.renderer_},i.prototype.getSize=function(){return this.get(Ka)},i.prototype.getView=function(){return this.get(Va)},i.prototype.getViewport=function(){return this.viewport_},i.prototype.getOverlayContainer=function(){return this.overlayContainer_},i.prototype.getOverlayContainerStopEvent=function(){return this.overlayContainerStopEvent_},i.prototype.getOwnerDocument=function(){var t=this.getTargetElement();return t?t.ownerDocument:document},i.prototype.getTilePriority=function(t,e,i,n){return function(t,e,i,n,o){if(!t||!(i in t.wantedTiles))return Ua;if(!t.wantedTiles[i][e.getKey()])return Ua;var r=t.viewState.center,s=n[0]-r[0],a=n[1]-r[1];return 65536*Math.log(o)+Math.sqrt(s*s+a*a)/o}(this.frameState_,t,e,i,n)},i.prototype.handleBrowserEvent=function(t,e){var i=e||t.type,n=new Wa(i,this,t);this.handleMapBrowserEvent(n)},i.prototype.handleMapBrowserEvent=function(t){if(this.frameState_){var e=t.originalEvent,i=e.type;if(i===Nt||i===b||i===S){var n=this.getOwnerDocument(),o=this.viewport_.getRootNode?this.viewport_.getRootNode():n,r=e.target;if(this.overlayContainerStopEvent_.contains(r)||!(o===n?n.documentElement:o).contains(r))return}if(t.frameState=this.frameState_,!1!==this.dispatchEvent(t))for(var s=this.getInteractions().getArray().slice(),a=s.length-1;a>=0;a--){var l=s[a];if(l.getMap()===this&&l.getActive()&&this.getTargetElement()&&(!l.handleEvent(t)||t.propagationStopped))break}}},i.prototype.handlePostRender=function(){var t=this.frameState_,e=this.tileQueue_;if(!e.isEmpty()){var i=this.maxTilesLoading_,n=i;if(t){var o=t.viewHints;if(o[0]||o[1]){var r=Date.now()-t.time>8;i=r?0:8,n=r?0:2}}e.getTilesLoading()<i&&(e.reprioritize(),e.loadMoreTiles(i,n))}t&&this.renderer_&&!t.animate&&(!0===this.renderComplete_?(this.hasListener(Dt)&&this.renderer_.dispatchRenderEvent(Dt,t),!1===this.loaded_&&(this.loaded_=!0,this.dispatchEvent(new Ga(X,this,t)))):!0===this.loaded_&&(this.loaded_=!1,this.dispatchEvent(new Ga(W,this,t))));for(var s=this.postRenderFunctions_,a=0,l=s.length;a<l;++a)s[a](this,t);s.length=0},i.prototype.handleSizeChanged_=function(){this.getView()&&!this.getView().getAnimating()&&this.getView().resolveConstraints(0),this.render()},i.prototype.handleTargetChanged_=function(){if(this.mapBrowserEventHandler_){for(var t=0,e=this.targetChangeHandlerKeys_.length;t<e;++t)P(this.targetChangeHandlerKeys_[t]);this.targetChangeHandlerKeys_=null,this.viewport_.removeEventListener(C,this.boundHandleBrowserEvent_),this.viewport_.removeEventListener(b,this.boundHandleBrowserEvent_),this.mapBrowserEventHandler_.dispose(),this.mapBrowserEventHandler_=null,$(this.viewport_)}var i=this.getTargetElement();if(i){for(var n in i.appendChild(this.viewport_),this.renderer_||(this.renderer_=this.createRenderer()),this.mapBrowserEventHandler_=new Ya(this,this.moveTolerance_),Xa)this.mapBrowserEventHandler_.addEventListener(Xa[n],this.handleMapBrowserEvent.bind(this));this.viewport_.addEventListener(C,this.boundHandleBrowserEvent_,!1),this.viewport_.addEventListener(b,this.boundHandleBrowserEvent_,!!H&&{passive:!1});var o=this.getOwnerDocument().defaultView,r=this.keyboardEventTarget_?this.keyboardEventTarget_:i;this.targetChangeHandlerKeys_=[O(r,S,this.handleBrowserEvent,this),O(r,E,this.handleBrowserEvent,this),O(o,"resize",this.updateSize,this)]}else this.renderer_&&(clearTimeout(this.postRenderTimeoutHandle_),this.postRenderTimeoutHandle_=void 0,this.postRenderFunctions_.length=0,this.renderer_.dispose(),this.renderer_=null),this.animationDelayKey_&&(cancelAnimationFrame(this.animationDelayKey_),this.animationDelayKey_=void 0);this.updateSize()},i.prototype.handleTileChange_=function(){this.render()},i.prototype.handleViewPropertyChanged_=function(){this.render()},i.prototype.handleViewChanged_=function(){this.viewPropertyListenerKey_&&(P(this.viewPropertyListenerKey_),this.viewPropertyListenerKey_=null),this.viewChangeListenerKey_&&(P(this.viewChangeListenerKey_),this.viewChangeListenerKey_=null);var t=this.getView();t&&(this.updateViewportSize_(),this.viewPropertyListenerKey_=O(t,e,this.handleViewPropertyChanged_,this),this.viewChangeListenerKey_=O(t,x,this.handleViewPropertyChanged_,this),t.resolveConstraints(0)),this.render()},i.prototype.handleLayerGroupChanged_=function(){this.layerGroupPropertyListenerKeys_&&(this.layerGroupPropertyListenerKeys_.forEach(P),this.layerGroupPropertyListenerKeys_=null);var t=this.getLayerGroup();t&&(this.handleLayerAdd_(new Aa("addlayer",t)),this.layerGroupPropertyListenerKeys_=[O(t,e,this.render,this),O(t,x,this.render,this),O(t,"addlayer",this.handleLayerAdd_,this),O(t,"removelayer",this.handleLayerRemove_,this)]),this.render()},i.prototype.isRendered=function(){return!!this.frameState_},i.prototype.renderSync=function(){this.animationDelayKey_&&cancelAnimationFrame(this.animationDelayKey_),this.animationDelay_()},i.prototype.redrawText=function(){for(var t=this.getLayerGroup().getLayerStatesArray(),e=0,i=t.length;e<i;++e){var n=t[e].layer;n.hasRenderer()&&n.getRenderer().handleFontsChanged()}},i.prototype.render=function(){this.renderer_&&void 0===this.animationDelayKey_&&(this.animationDelayKey_=requestAnimationFrame(this.animationDelay_))},i.prototype.removeControl=function(t){return this.getControls().remove(t)},i.prototype.removeInteraction=function(t){return this.getInteractions().remove(t)},i.prototype.removeLayer=function(t){return this.getLayerGroup().getLayers().remove(t)},i.prototype.handleLayerRemove_=function(t){fl(t.layer)},i.prototype.removeOverlay=function(t){return this.getOverlays().remove(t)},i.prototype.renderFrame_=function(t){var e=this,i=this.getSize(),n=this.getView(),o=this.frameState_,r=null;if(void 0!==i&&Fo(i)&&n&&n.isDef()){var s=n.getHints(this.frameState_?this.frameState_.viewHints:void 0),a=n.getState();if(r={animate:!1,coordinateToPixelTransform:this.coordinateToPixelTransform_,declutterTree:null,extent:Ie(a.center,a.resolution,a.rotation,i),index:this.frameIndex_++,layerIndex:0,layerStatesArray:this.getLayerGroup().getLayerStatesArray(),pixelRatio:this.pixelRatio_,pixelToCoordinateTransform:this.pixelToCoordinateTransform_,postRenderFunctions:[],size:i,tileQueue:this.tileQueue_,time:t,usedTiles:{},viewState:a,viewHints:s,wantedTiles:{},mapId:D(this),renderTargets:{}},a.nextCenter&&a.nextResolution){var l=isNaN(a.nextRotation)?a.rotation:a.nextRotation;r.nextExtent=Ie(a.nextCenter,a.nextResolution,l,i)}}this.frameState_=r,this.renderer_.renderFrame(r),r&&(r.animate&&this.render(),Array.prototype.push.apply(this.postRenderFunctions_,r.postRenderFunctions),o&&(!this.previousExtent_||!Ge(this.previousExtent_)&&!xe(r.extent,this.previousExtent_))&&(this.dispatchEvent(new Ga("movestart",this,o)),this.previousExtent_=ve(this.previousExtent_)),this.previousExtent_&&!r.viewHints[0]&&!r.viewHints[1]&&!xe(r.extent,this.previousExtent_)&&(this.dispatchEvent(new Ga("moveend",this,r)),ce(r.extent,this.previousExtent_))),this.dispatchEvent(new Ga(z,this,r)),this.renderComplete_=this.hasListener(W)||this.hasListener(X)||this.hasListener(Dt)?!this.tileQueue_.getTilesLoading()&&!this.tileQueue_.getCount()&&!this.getLoadingOrNotReady():void 0,this.postRenderTimeoutHandle_||(this.postRenderTimeoutHandle_=setTimeout((function(){e.postRenderTimeoutHandle_=void 0,e.handlePostRender()}),0))},i.prototype.setLayerGroup=function(t){var e=this.getLayerGroup();e&&this.handleLayerRemove_(new Aa("removelayer",e)),this.set(Ba,t)},i.prototype.setSize=function(t){this.set(Ka,t)},i.prototype.setTarget=function(t){this.set(Za,t)},i.prototype.setView=function(t){if(!t||t instanceof cl)this.set(Va,t);else{this.set(Va,new cl);var e=this;t.then((function(t){e.setView(new cl(t))}))}},i.prototype.updateSize=function(){var t=this.getTargetElement(),e=void 0;if(t){var i=getComputedStyle(t),n=t.offsetWidth-parseFloat(i.borderLeftWidth)-parseFloat(i.paddingLeft)-parseFloat(i.paddingRight)-parseFloat(i.borderRightWidth),o=t.offsetHeight-parseFloat(i.borderTopWidth)-parseFloat(i.paddingTop)-parseFloat(i.paddingBottom)-parseFloat(i.borderBottomWidth);isNaN(n)||isNaN(o)||!Fo(e=[n,o])&&(t.offsetWidth||t.offsetHeight||t.getClientRects().length)&&console.warn("No map visible because the map container's width or height are 0.")}this.setSize(e),this.updateViewportSize_()},i.prototype.updateViewportSize_=function(){var t=this.getView();if(t){var e=void 0,i=getComputedStyle(this.viewport_);i.width&&i.height&&(e=[parseInt(i.width,10),parseInt(i.height,10)]),t.setViewportSize(e)}},i}(G),_l=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),yl=function(t){function e(e){var i=this,n=e||{};i=t.call(this,{element:document.createElement("div"),render:n.render,target:n.target})||this;var o=void 0!==n.className?n.className:"ol-rotate",r=void 0!==n.label?n.label:"⇧",s=void 0!==n.compassClassName?n.compassClassName:"ol-compass";i.label_=null,"string"==typeof r?(i.label_=document.createElement("span"),i.label_.className=s,i.label_.textContent=r):(i.label_=r,i.label_.classList.add(s));var a=n.tipLabel?n.tipLabel:"Reset rotation",l=document.createElement("button");l.className=o+"-reset",l.setAttribute("type","button"),l.title=a,l.appendChild(i.label_),l.addEventListener(w,i.handleClick_.bind(i),!1);var h=o+" "+nt+" "+ot,u=i.element;return u.className=h,u.appendChild(l),i.callResetNorth_=n.resetNorth?n.resetNorth:void 0,i.duration_=void 0!==n.duration?n.duration:250,i.autoHide_=void 0===n.autoHide||n.autoHide,i.rotation_=void 0,i.autoHide_&&i.element.classList.add(it),i}return _l(e,t),e.prototype.handleClick_=function(t){t.preventDefault(),void 0!==this.callResetNorth_?this.callResetNorth_():this.resetNorth_()},e.prototype.resetNorth_=function(){var t=this.getMap().getView();if(t){var e=t.getRotation();void 0!==e&&(this.duration_>0&&e%(2*Math.PI)!=0?t.animate({rotation:0,duration:this.duration_,easing:xi}):t.setRotation(0))}},e.prototype.render=function(t){var e=t.frameState;if(e){var i=e.viewState.rotation;if(i!=this.rotation_){var n="rotate("+i+"rad)";if(this.autoHide_){var o=this.element.classList.contains(it);o||0!==i?o&&0!==i&&this.element.classList.remove(it):this.element.classList.add(it)}this.label_.style.transform=n}this.rotation_=i}},e}(et),vl=yl,ml="active",xl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function Cl(t,e,i,n){var o=t.getZoom();if(void 0!==o){var r=t.getConstrainedZoom(o+e),s=t.getResolutionForZoom(r);t.getAnimating()&&t.cancelAnimations(),t.animate({resolution:s,anchor:i,duration:void 0!==n?n:250,easing:xi})}}var wl=function(t){function e(e){var i=t.call(this)||this;return i.on,i.once,i.un,e&&e.handleEvent&&(i.handleEvent=e.handleEvent),i.map_=null,i.setActive(!0),i}return xl(e,t),e.prototype.getActive=function(){return this.get(ml)},e.prototype.getMap=function(){return this.map_},e.prototype.handleEvent=function(t){return!0},e.prototype.setActive=function(t){this.set(ml,t)},e.prototype.setMap=function(t){this.map_=t},e}(G),Sl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),El=function(t){function e(e){var i=t.call(this)||this,n=e||{};return i.delta_=n.delta?n.delta:1,i.duration_=void 0!==n.duration?n.duration:250,i}return Sl(e,t),e.prototype.handleEvent=function(t){var e=!1;if(t.type==Xa.DBLCLICK){var i=t.originalEvent,n=t.map,o=t.coordinate,r=i.shiftKey?-this.delta_:this.delta_;Cl(n.getView(),r,o,this.duration_),i.preventDefault(),e=!0}return!e},e}(wl),Tl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}();function bl(t){for(var e=t.length,i=0,n=0,o=0;o<e;o++)i+=t[o].clientX,n+=t[o].clientY;return[i/e,n/e]}var Ol=function(t){function e(e){var i=this,n=e||{};return i=t.call(this,n)||this,n.handleDownEvent&&(i.handleDownEvent=n.handleDownEvent),n.handleDragEvent&&(i.handleDragEvent=n.handleDragEvent),n.handleMoveEvent&&(i.handleMoveEvent=n.handleMoveEvent),n.handleUpEvent&&(i.handleUpEvent=n.handleUpEvent),n.stopDown&&(i.stopDown=n.stopDown),i.handlingDownUpSequence=!1,i.targetPointers=[],i}return Tl(e,t),e.prototype.getPointerCount=function(){return this.targetPointers.length},e.prototype.handleDownEvent=function(t){return!1},e.prototype.handleDragEvent=function(t){},e.prototype.handleEvent=function(t){if(!t.originalEvent)return!0;var e=!1;if(this.updateTrackedPointers_(t),this.handlingDownUpSequence){if(t.type==Xa.POINTERDRAG)this.handleDragEvent(t),t.originalEvent.preventDefault();else if(t.type==Xa.POINTERUP){var i=this.handleUpEvent(t);this.handlingDownUpSequence=i&&this.targetPointers.length>0}}else if(t.type==Xa.POINTERDOWN){var n=this.handleDownEvent(t);this.handlingDownUpSequence=n,e=this.stopDown(n)}else t.type==Xa.POINTERMOVE&&this.handleMoveEvent(t);return!e},e.prototype.handleMoveEvent=function(t){},e.prototype.handleUpEvent=function(t){return!1},e.prototype.stopDown=function(t){return t},e.prototype.updateTrackedPointers_=function(t){t.activePointers&&(this.targetPointers=t.activePointers)},e}(wl);function Rl(t){var e=arguments;return function(t){for(var i=!0,n=0,o=e.length;n<o&&(i=i&&e[n](t));++n);return i}}var Pl=function(t){var e=t.originalEvent;return e.altKey&&!(e.metaKey||e.ctrlKey)&&e.shiftKey},Il=function(t){return!t.map.getTargetElement().hasAttribute("tabindex")||function(t){var e=t.map.getTargetElement(),i=t.map.getOwnerDocument().activeElement;return e.contains(i)}(t)},Ml=u,Fl=function(t){var e=t.originalEvent;return 0==e.button&&!(B&&K&&e.ctrlKey)},Ll=function(t){var e=t.originalEvent;return!e.altKey&&!(e.metaKey||e.ctrlKey)&&!e.shiftKey},Al=function(t){var e=t.originalEvent;return!e.altKey&&!(e.metaKey||e.ctrlKey)&&e.shiftKey},Dl=function(t){var e=t.originalEvent,i=e.target.tagName;return"INPUT"!==i&&"SELECT"!==i&&"TEXTAREA"!==i&&!e.target.isContentEditable},kl=function(t){var e=t.originalEvent;return xt(void 0!==e,56),"mouse"==e.pointerType},jl=function(t){var e=t.originalEvent;return xt(void 0!==e,56),e.isPrimary&&0===e.button},Gl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),zl=function(t){function e(e){var i=t.call(this,{stopDown:c})||this,n=e||{};i.kinetic_=n.kinetic,i.lastCentroid=null,i.lastPointersCount_,i.panning_=!1;var o=n.condition?n.condition:Rl(Ll,jl);return i.condition_=n.onFocusOnly?Rl(Il,o):o,i.noKinetic_=!1,i}return Gl(e,t),e.prototype.handleDragEvent=function(t){this.panning_||(this.panning_=!0,this.getMap().getView().beginInteraction());var e,i,n=this.targetPointers,o=bl(n);if(n.length==this.lastPointersCount_){if(this.kinetic_&&this.kinetic_.update(o[0],o[1]),this.lastCentroid){var r=[this.lastCentroid[0]-o[0],o[1]-this.lastCentroid[1]],s=t.map.getView();e=r,i=s.getResolution(),e[0]*=i,e[1]*=i,Xe(r,s.getRotation()),s.adjustCenterInternal(r)}}else this.kinetic_&&this.kinetic_.begin();this.lastCentroid=o,this.lastPointersCount_=n.length,t.originalEvent.preventDefault()},e.prototype.handleUpEvent=function(t){var e=t.map,i=e.getView();if(0===this.targetPointers.length){if(!this.noKinetic_&&this.kinetic_&&this.kinetic_.end()){var n=this.kinetic_.getDistance(),o=this.kinetic_.getAngle(),r=i.getCenterInternal(),s=e.getPixelFromCoordinateInternal(r),a=e.getCoordinateFromPixelInternal([s[0]-n*Math.cos(o),s[1]-n*Math.sin(o)]);i.animateInternal({center:i.getConstrainedCenter(a),duration:500,easing:xi})}return this.panning_&&(this.panning_=!1,i.endInteraction()),!1}return this.kinetic_&&this.kinetic_.begin(),this.lastCentroid=null,!0},e.prototype.handleDownEvent=function(t){if(this.targetPointers.length>0&&this.condition_(t)){var e=t.map.getView();return this.lastCentroid=null,e.getAnimating()&&e.cancelAnimations(),this.kinetic_&&this.kinetic_.begin(),this.noKinetic_=this.targetPointers.length>1,!0}return!1},e}(Ol),Wl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Xl=function(t){function e(e){var i=this,n=e||{};return(i=t.call(this,{stopDown:c})||this).condition_=n.condition?n.condition:Pl,i.lastAngle_=void 0,i.duration_=void 0!==n.duration?n.duration:250,i}return Wl(e,t),e.prototype.handleDragEvent=function(t){if(kl(t)){var e=t.map,i=e.getView();if(i.getConstraints().rotation!==ol){var n=e.getSize(),o=t.pixel,r=Math.atan2(n[1]/2-o[1],o[0]-n[0]/2);if(void 0!==this.lastAngle_){var s=r-this.lastAngle_;i.adjustRotationInternal(-s)}this.lastAngle_=r}}},e.prototype.handleUpEvent=function(t){return!kl(t)||(t.map.getView().endInteraction(this.duration_),!1)},e.prototype.handleDownEvent=function(t){return!(!kl(t)||!Fl(t)||!this.condition_(t)||(t.map.getView().beginInteraction(),this.lastAngle_=void 0,0))},e}(Ol),Nl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Yl=function(t){function e(e){var i=t.call(this)||this;return i.geometry_=null,i.element_=document.createElement("div"),i.element_.style.position="absolute",i.element_.style.pointerEvents="auto",i.element_.className="ol-box "+e,i.map_=null,i.startPixel_=null,i.endPixel_=null,i}return Nl(e,t),e.prototype.disposeInternal=function(){this.setMap(null)},e.prototype.render_=function(){var t=this.startPixel_,e=this.endPixel_,i="px",n=this.element_.style;n.left=Math.min(t[0],e[0])+i,n.top=Math.min(t[1],e[1])+i,n.width=Math.abs(e[0]-t[0])+i,n.height=Math.abs(e[1]-t[1])+i},e.prototype.setMap=function(t){if(this.map_){this.map_.getOverlayContainer().removeChild(this.element_);var e=this.element_.style;e.left="inherit",e.top="inherit",e.width="inherit",e.height="inherit"}this.map_=t,this.map_&&this.map_.getOverlayContainer().appendChild(this.element_)},e.prototype.setPixels=function(t,e){this.startPixel_=t,this.endPixel_=e,this.createOrUpdateGeometry(),this.render_()},e.prototype.createOrUpdateGeometry=function(){var t=this.startPixel_,e=this.endPixel_,i=[t,[t[0],e[1]],e,[e[0],t[1]]].map(this.map_.getCoordinateFromPixelInternal,this.map_);i[4]=i[0].slice(),this.geometry_?this.geometry_.setCoordinates([i]):this.geometry_=new zn([i])},e.prototype.getGeometry=function(){return this.geometry_},e}(o),Bl=Yl,Kl=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Zl=function(t){function e(e,i,n){var o=t.call(this,e)||this;return o.coordinate=i,o.mapBrowserEvent=n,o}return Kl(e,t),e}(t),Vl=function(t){function e(e){var i=t.call(this)||this;i.on,i.once,i.un;var n=e||{};return i.box_=new Bl(n.className||"ol-dragbox"),i.minArea_=void 0!==n.minArea?n.minArea:64,n.onBoxEnd&&(i.onBoxEnd=n.onBoxEnd),i.startPixel_=null,i.condition_=n.condition?n.condition:Fl,i.boxEndCondition_=n.boxEndCondition?n.boxEndCondition:i.defaultBoxEndCondition,i}return Kl(e,t),e.prototype.defaultBoxEndCondition=function(t,e,i){var n=i[0]-e[0],o=i[1]-e[1];return n*n+o*o>=this.minArea_},e.prototype.getGeometry=function(){return this.box_.getGeometry()},e.prototype.handleDragEvent=function(t){this.box_.setPixels(this.startPixel_,t.pixel),this.dispatchEvent(new Zl("boxdrag",t.coordinate,t))},e.prototype.handleUpEvent=function(t){this.box_.setMap(null);var e=this.boxEndCondition_(t,this.startPixel_,t.pixel);return e&&this.onBoxEnd(t),this.dispatchEvent(new Zl(e?"boxend":"boxcancel",t.coordinate,t)),!1},e.prototype.handleDownEvent=function(t){return!!this.condition_(t)&&(this.startPixel_=t.pixel,this.box_.setMap(t.map),this.box_.setPixels(this.startPixel_,this.startPixel_),this.dispatchEvent(new Zl("boxstart",t.coordinate,t)),!0)},e.prototype.onBoxEnd=function(t){},e}(Ol),Ul=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Hl=function(t){function e(e){var i=this,n=e||{},o=n.condition?n.condition:Al;return(i=t.call(this,{condition:o,className:n.className||"ol-dragzoom",minArea:n.minArea})||this).duration_=void 0!==n.duration?n.duration:200,i.out_=void 0!==n.out&&n.out,i}return Ul(e,t),e.prototype.onBoxEnd=function(t){var e=this.getMap().getView(),i=this.getGeometry();if(this.out_){var n=e.rotatedExtentForGeometry(i),o=e.getResolutionForExtentInternal(n),r=e.getResolution()/o;(i=i.clone()).scale(r*r)}e.fitInternal(i,{duration:this.duration_,easing:xi})},e}(Vl),ql=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Jl=function(t){function e(e){var i=t.call(this)||this,n=e||{};return i.defaultCondition_=function(t){return Ll(t)&&Dl(t)},i.condition_=void 0!==n.condition?n.condition:i.defaultCondition_,i.duration_=void 0!==n.duration?n.duration:100,i.pixelDelta_=void 0!==n.pixelDelta?n.pixelDelta:128,i}return ql(e,t),e.prototype.handleEvent=function(t){var e=!1;if(t.type==S){var i=t.originalEvent,n=i.keyCode;if(this.condition_(t)&&(40==n||37==n||39==n||38==n)){var o=t.map.getView(),r=o.getResolution()*this.pixelDelta_,s=0,a=0;40==n?a=-r:37==n?s=-r:39==n?s=r:a=r;var l=[s,a];Xe(l,o.getRotation()),function(t,e,i){var n=t.getCenterInternal();if(n){var o=[n[0]+e[0],n[1]+e[1]];t.animateInternal({duration:void 0!==i?i:250,easing:wi,center:t.getConstrainedCenter(o)})}}(o,l,this.duration_),i.preventDefault(),e=!0}}return!e},e}(wl),Ql=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),$l=function(t){function e(e){var i=t.call(this)||this,n=e||{};return i.condition_=n.condition?n.condition:Dl,i.delta_=n.delta?n.delta:1,i.duration_=void 0!==n.duration?n.duration:100,i}return Ql(e,t),e.prototype.handleEvent=function(t){var e=!1;if(t.type==S||t.type==E){var i=t.originalEvent,n=i.charCode;if(this.condition_(t)&&(n=="+".charCodeAt(0)||n=="-".charCodeAt(0))){var o=t.map,r=n=="+".charCodeAt(0)?this.delta_:-this.delta_;Cl(o.getView(),r,void 0,this.duration_),i.preventDefault(),e=!0}}return!e},e}(wl),th=function(){function t(t,e,i){this.decay_=t,this.minVelocity_=e,this.delay_=i,this.points_=[],this.angle_=0,this.initialVelocity_=0}return t.prototype.begin=function(){this.points_.length=0,this.angle_=0,this.initialVelocity_=0},t.prototype.update=function(t,e){this.points_.push(t,e,Date.now())},t.prototype.end=function(){if(this.points_.length<6)return!1;var t=Date.now()-this.delay_,e=this.points_.length-3;if(this.points_[e+2]<t)return!1;for(var i=e-3;i>0&&this.points_[i+2]>t;)i-=3;var n=this.points_[e+2]-this.points_[i+2];if(n<1e3/60)return!1;var o=this.points_[e]-this.points_[i],r=this.points_[e+1]-this.points_[i+1];return this.angle_=Math.atan2(r,o),this.initialVelocity_=Math.sqrt(o*o+r*r)/n,this.initialVelocity_>this.minVelocity_},t.prototype.getDistance=function(){return(this.minVelocity_-this.initialVelocity_)/this.decay_},t.prototype.getAngle=function(){return this.angle_},t}(),eh=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ih="trackpad",nh=function(t){function e(e){var i=this,n=e||{};(i=t.call(this,n)||this).totalDelta_=0,i.lastDelta_=0,i.maxDelta_=void 0!==n.maxDelta?n.maxDelta:1,i.duration_=void 0!==n.duration?n.duration:250,i.timeout_=void 0!==n.timeout?n.timeout:80,i.useAnchor_=void 0===n.useAnchor||n.useAnchor,i.constrainResolution_=void 0!==n.constrainResolution&&n.constrainResolution;var o=n.condition?n.condition:Ml;return i.condition_=n.onFocusOnly?Rl(Il,o):o,i.lastAnchor_=null,i.startTime_=void 0,i.timeoutId_,i.mode_=void 0,i.trackpadEventGap_=400,i.trackpadTimeoutId_,i.deltaPerZoom_=300,i}return eh(e,t),e.prototype.endInteraction_=function(){this.trackpadTimeoutId_=void 0;var t=this.getMap();t&&t.getView().endInteraction(void 0,this.lastDelta_?this.lastDelta_>0?1:-1:0,this.lastAnchor_)},e.prototype.handleEvent=function(t){if(!this.condition_(t))return!0;if(t.type!==b)return!0;var e,i=t.map,n=t.originalEvent;if(n.preventDefault(),this.useAnchor_&&(this.lastAnchor_=t.coordinate),t.type==b&&(e=n.deltaY,Y&&n.deltaMode===WheelEvent.DOM_DELTA_PIXEL&&(e/=Z),n.deltaMode===WheelEvent.DOM_DELTA_LINE&&(e*=40)),0===e)return!1;this.lastDelta_=e;var o=Date.now();void 0===this.startTime_&&(this.startTime_=o),(!this.mode_||o-this.startTime_>this.trackpadEventGap_)&&(this.mode_=Math.abs(e)<4?ih:"wheel");var r=i.getView();if(this.mode_===ih&&!r.getConstrainResolution()&&!this.constrainResolution_)return this.trackpadTimeoutId_?clearTimeout(this.trackpadTimeoutId_):(r.getAnimating()&&r.cancelAnimations(),r.beginInteraction()),this.trackpadTimeoutId_=setTimeout(this.endInteraction_.bind(this),this.timeout_),r.adjustZoom(-e/this.deltaPerZoom_,this.lastAnchor_),this.startTime_=o,!1;this.totalDelta_+=e;var s=Math.max(this.timeout_-(o-this.startTime_),0);return clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout(this.handleWheelZoom_.bind(this,i),s),!1},e.prototype.handleWheelZoom_=function(t){var e=t.getView();e.getAnimating()&&e.cancelAnimations();var i=-Ct(this.totalDelta_,-this.maxDelta_*this.deltaPerZoom_,this.maxDelta_*this.deltaPerZoom_)/this.deltaPerZoom_;(e.getConstrainResolution()||this.constrainResolution_)&&(i=i?i>0?1:-1:0),Cl(e,i,this.lastAnchor_,this.duration_),this.mode_=void 0,this.totalDelta_=0,this.lastAnchor_=null,this.startTime_=void 0,this.timeoutId_=void 0},e.prototype.setMouseAnchor=function(t){this.useAnchor_=t,t||(this.lastAnchor_=null)},e}(wl),oh=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),rh=function(t){function e(e){var i=this,n=e||{},o=n;return o.stopDown||(o.stopDown=c),(i=t.call(this,o)||this).anchor_=null,i.lastAngle_=void 0,i.rotating_=!1,i.rotationDelta_=0,i.threshold_=void 0!==n.threshold?n.threshold:.3,i.duration_=void 0!==n.duration?n.duration:250,i}return oh(e,t),e.prototype.handleDragEvent=function(t){var e=0,i=this.targetPointers[0],n=this.targetPointers[1],o=Math.atan2(n.clientY-i.clientY,n.clientX-i.clientX);if(void 0!==this.lastAngle_){var r=o-this.lastAngle_;this.rotationDelta_+=r,!this.rotating_&&Math.abs(this.rotationDelta_)>this.threshold_&&(this.rotating_=!0),e=r}this.lastAngle_=o;var s=t.map,a=s.getView();if(a.getConstraints().rotation!==ol){var l=s.getViewport().getBoundingClientRect(),h=bl(this.targetPointers);h[0]-=l.left,h[1]-=l.top,this.anchor_=s.getCoordinateFromPixelInternal(h),this.rotating_&&(s.render(),a.adjustRotationInternal(e,this.anchor_))}},e.prototype.handleUpEvent=function(t){return!(this.targetPointers.length<2&&(t.map.getView().endInteraction(this.duration_),1))},e.prototype.handleDownEvent=function(t){if(this.targetPointers.length>=2){var e=t.map;return this.anchor_=null,this.lastAngle_=void 0,this.rotating_=!1,this.rotationDelta_=0,this.handlingDownUpSequence||e.getView().beginInteraction(),!0}return!1},e}(Ol),sh=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),ah=function(t){function e(e){var i=this,n=e||{},o=n;return o.stopDown||(o.stopDown=c),(i=t.call(this,o)||this).anchor_=null,i.duration_=void 0!==n.duration?n.duration:400,i.lastDistance_=void 0,i.lastScaleDelta_=1,i}return sh(e,t),e.prototype.handleDragEvent=function(t){var e=1,i=this.targetPointers[0],n=this.targetPointers[1],o=i.clientX-n.clientX,r=i.clientY-n.clientY,s=Math.sqrt(o*o+r*r);void 0!==this.lastDistance_&&(e=this.lastDistance_/s),this.lastDistance_=s;var a=t.map,l=a.getView();1!=e&&(this.lastScaleDelta_=e);var h=a.getViewport().getBoundingClientRect(),u=bl(this.targetPointers);u[0]-=h.left,u[1]-=h.top,this.anchor_=a.getCoordinateFromPixelInternal(u),a.render(),l.adjustResolutionInternal(e,this.anchor_)},e.prototype.handleUpEvent=function(t){if(this.targetPointers.length<2){var e=t.map.getView(),i=this.lastScaleDelta_>1?1:-1;return e.endInteraction(this.duration_,i),!1}return!0},e.prototype.handleDownEvent=function(t){if(this.targetPointers.length>=2){var e=t.map;return this.anchor_=null,this.lastDistance_=void 0,this.lastScaleDelta_=1,this.handlingDownUpSequence||e.getView().beginInteraction(),!0}return!1},e}(Ol),lh=function(){var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},t(e,i)};return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),hh=function(t){function e(e){return(e=f({},e)).controls||(e.controls=function(t){var e={},i=new da;return(void 0===e.zoom||e.zoom)&&i.push(new Ti(e.zoomOptions)),(void 0===e.rotate||e.rotate)&&i.push(new vl(e.rotateOptions)),(void 0===e.attribution||e.attribution)&&i.push(new Wt(e.attributionOptions)),i}()),e.interactions||(e.interactions=function(t){var e={onFocusOnly:!0}||{},i=new da,n=new th(-.005,.05,100);return(void 0===e.altShiftDragRotate||e.altShiftDragRotate)&&i.push(new Xl),(void 0===e.doubleClickZoom||e.doubleClickZoom)&&i.push(new El({delta:e.zoomDelta,duration:e.zoomDuration})),(void 0===e.dragPan||e.dragPan)&&i.push(new zl({onFocusOnly:e.onFocusOnly,kinetic:n})),(void 0===e.pinchRotate||e.pinchRotate)&&i.push(new rh),(void 0===e.pinchZoom||e.pinchZoom)&&i.push(new ah({duration:e.zoomDuration})),(void 0===e.keyboard||e.keyboard)&&(i.push(new Jl),i.push(new $l({delta:e.zoomDelta,duration:e.zoomDuration}))),(void 0===e.mouseWheelZoom||e.mouseWheelZoom)&&i.push(new nh({onFocusOnly:e.onFocusOnly,duration:e.zoomDuration})),(void 0===e.shiftDragZoom||e.shiftDragZoom)&&i.push(new Hl({duration:e.zoomDuration})),i}()),t.call(this,e)||this}return lh(e,t),e.prototype.createRenderer=function(){return new Fa(this)},e}(gl),uh={control:{Attribution:Wt,MousePosition:vi,Zoom:Ti},coordinate:{createStringXY:function(t){return function(e){return function(t,e){return function(t,e,i){return t?"{x}, {y}".replace("{x}",t[0].toFixed(i)).replace("{y}",t[1].toFixed(i)):""}(t,0,e)}(e,t)}}},extent:{boundingExtent:he},geom:{LineString:_n,LinearRing:Cn,MultiLineString:En,MultiPoint:In,MultiPolygon:Yn,Point:On,Polygon:zn},layer:{Tile:Go,Vector:Fs},proj:{fromLonLat:function(t,e){return Ke(),ii(t,"EPSG:4326",void 0!==e?e:"EPSG:3857")},get:He,transformExtent:ni},source:{OSM:la,Vector:wa},style:{Circle:ur,Fill:cr,Stroke:pr,Style:yr,Text:Sa},Feature:ba,Map:hh,View:cl}}(),n.default}()}));
//# sourceMappingURL=OpenLayers.js.map
