/*------------------------------------*\
    Topbar
\*------------------------------------*/
.wrapper-container{
    position:relative;
    word-break: break-word;
    overflow: hidden;
}
.wrapper-large{
    max-width:1770px;
    margin:auto;
    float:none;
}
.#{$app-prefix}-topbar{
    padding:$topbar-padding;
    margin: 0 !important;
    background: $topbar-bg;
    color: $topbar-color;
    @include font-size(font-size, $topbar-font-size);
    a{
        color: $topbar-link-color;
        @include hover-active() {
            color: $topbar-link-hover-color;
        }
    }
    .dropdown-menu{
        a{
            color: $link-color;
            @include hover-active() {
                color: $link-hover-color;
            }
        }
    }
    .topbar-left{
        .item{
            display:inline-block;
        }
        .item + .item{
            @include rtl-margin-left(40px);
        }
    }
    .topbar-right{
        margin-top: 5px;
        line-height: 1;
        > *{
            @include rtl-float-right();
            @include rtl-padding-left($theme-margin);
            @include rtl-margin-left(20px !important);
            @include rtl-border-left(1px solid #dcdcdc);
            &:last-child{
                border:none;
                padding:0;
                margin:0;
            }
        }
        .woocommerce-currency-switcher-form .dd-selected{
            padding:0 !important;
        }
    }
    .wrapper-account{
        @include rtl-margin-right(25px);
    }
    .drop-dow{
        font-size:16px;
        line-height:1;
        color:$link-color;
        &:hover,&:active{
            color:$theme-color;
        }
    }
}
/*------------------------------------*\
    apus-topbar
\*------------------------------------*/
.apus-topbar{
    .widget{
        margin: 0;
    }
}
.topbar-right-inner{
    > *{
        display: inline-block;
        vertical-align: middle;
    }
}
.mm-menu,
.topbar-right-inner{
    .wpml-ls-legacy-dropdown a{
        border:none !important;
        padding-top:0;
        padding-bottom:2px;
        background:transparent !important;
    }
    .wpml-ls-legacy-dropdown .wpml-ls-sub-menu{
        background:$white;
        border:none;
        padding:5px 0;
        @include box-shadow(0px 2px 5px 0px rgba(0,0,0,0.1));
    }
    .wpml-ls-legacy-dropdown .wpml-ls-sub-menu a{
        padding:5px 10px;
    }
    .wpml-ls-legacy-dropdown{
        width: auto;
        .wpml-ls-sub-menu{
            min-width: 140px;
        }
    }
}
.topbar-right{
    @include rtl-text-align-right();
    .elementor-widget:not(.elementor-widget-spacer) {
        line-height: 1.4;
        position:relative;
        vertical-align: middle;
        display: inline-block;
        @include rtl-padding-left(18px);
        @include rtl-margin-left(18px);
        @include rtl-border-left(1px solid $border-color);
        width: auto;
        &:first-child{
            margin:0;
            padding:0;
            border:0;
        }
    }
    .top-wrapper-menu .login i, 
    .top-wrapper-menu .drop-dow i{
        font-size: 14px;
    }
    .language-wrapper .dropdown-menu-right{
        right: -20px;
    }
}
.header-right{
    @include rtl-text-align-right();
    .elementor-widget:not(.elementor-widget-spacer) {
        position:relative;
        display: inline-block;
        @include rtl-margin-left(15px);
        max-width:75%;
        vertical-align: middle;
        width: auto;
        &:first-child{
            margin:0;
        }
    }
}
/*------------------------------------*\
    Header
\*------------------------------------*/

.logo img{
    vertical-align: middle;
}
.top-wrapper-menu{
    position:relative;    
    .login,
    .drop-dow{
        i{
            font-size: 22px;
        }
    }
    .login{
        outline: none;
        i{
            @include rtl-margin-right(14px);
        }
    }
    .inner-top-menu{
        margin-top: 10px;
        padding:10px 25px;
        position:absolute;
        top:100%;
        @include rtl-right(0);
        @include opacity(0);
        visibility:hidden;
        @include transition(all 0.3s ease-in-out 0s);
        @include translateY(10px);
        z-index:9;
    }
    &:hover{
        .inner-top-menu{
            visibility:visible;
            @include opacity(1);
            @include translateY(0px);
            background:$white;
            @include box-shadow(0 5px 10px -5px rgba(0,0,0,0.15));
        }
    }
    .header_customer_login{
        margin-top: 10px;
        position:absolute;
        top:100%;
        @include rtl-right(0);
        @include opacity(0);
        visibility:hidden;
        @include transition(all 0.3s ease-in-out 0s);
        min-width:320px;
        z-index: 9;
    }
    &:hover{
        .header_customer_login{
            visibility:visible;
            @include opacity(1);
            @include translateY(0px);
            background:$white;
            @include box-shadow(0 5px 10px -5px rgba(0,0,0,0.15)); 
        }
    }
}
.header_customer_login{
    padding:$theme-margin;
    .title{
        margin: 0 0 28px;
        font-size: 25px;
    }
}
.header-left{
    [class*="icon"]{
        font-size:16px;
    }
    .btn-search-top{
        margin-top:4px;
    }
    .icon-menu-top{
        [class*="icon"]{
            font-size:30px;
        }
    }
    > div{
        @include rtl-margin-right(22px);
        &:last-child{
           @include rtl-margin-right(0px);
        }
    }
}
.header-right{
    [class*="icon"]{
        &:before{
            font-size: 26px;
        }
    }
    > div{
        @include rtl-margin-left(20px);
        &:last-child{
           @include rtl-margin-left(0px);
        }
    }
}

.site-header{
    padding: 17px 0;
    border-bottom:1px solid $border-color;
    .megamenu{
        > li{        
            &:last-child{
                @include rtl-margin-right(0px);
            } 
        }
    } 
    .header-right {
        padding: 24px 0;
        @include rtl-margin-left(auto);
    }    
}

.btn-showsearch{        
    border: 0;
    color: $headings-color;
    @include flexbox();
    @include justify-content(center);
    @include align-items(center);
    @include square(30px);
    @include hover-focus-active() {
        color: $headings-color;
    }
    [class*="icon"]{
        &:before{
            margin: 0px;
            font-size: 24px;
        }
    }
}

.header-mobile{
    padding: 0;
    background: $white;
    .account-login{
        .login-account{
            li{
                a [class*="icon"]{
                    &:before {        
                        font-size: 26px;        
                    }
                }                
            }             
        }         
    }     
    .mini-cart{        
        @include hover-focus-active() {
            color: $headings-color;
        }
        [class*="icon"]{            
            &:before{
                font-size: 28px;
                margin: 0px;
            }
        }
    }
    .header-bottom-mobile{
        margin-top: 5px;
    }
    .mobile-vertical-menu-title{
        color: $white;
        background-color: $theme-color;        
        font-size: 16px;
        cursor: pointer;
        margin: 0 0 0 auto;
        line-height: normal;
        padding: 10px 25px;
        font-family: $headings-font-family; 
        @include rtl-float-left();
        @include inline-block();
        @include transition-all();
        @include border-radius(40px);
                
        @media only screen and (min-device-width : 320px) and (max-device-width : 374px) {
            padding: 7px 14px;
            font-size: 14px;
        }

        [class*="icon"]{
            &:before{
                @include rtl-margin(0px,0px,0px,10px);
                font-size: 12px;
            }
        }       
        &.active{
            outline: none;
        }
    }
    .setting-account-content{
        @include rtl-margin-left(0);
        @include flexbox();
    }
    .setting-account-intro{        
        @include rtl-margin-left(auto);
        > *{
            @include rtl-margin-left(20px);
            &:first-child{
                @include rtl-margin-left(0);
            }
        }
    }
    .btn-showmenu{        
        cursor: pointer;
        border: 0;
        outline: none;
        position: relative;
        @include flexbox();
        @include justify-content(center);
        @include align-items(center);
        @include square(30px);
        &:before{
            content: "";
            background-color: transparent;
            background-image: url('../images/menu.svg');
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
            @include square(100%);
        }
    }
    > .btn-offcanvas {
        position:absolute;top: 3px;
        left: 3px;
        z-index: 1;
    }
    .btn.dropdown-toggle,
    .btn.offcanvas{
        border:none;
        padding: 0;
        font-size: 18px;
        color: #000;
        background: transparent;
        @include hover-focus-active() {
            color: $theme-color;
            background: transparent;
        }        
    }
    .search-mobile{        
        @include hover-focus-active() {
            .apus-search-form{                
                display: block;
                visibility: visible;
                pointer-events: auto;
                @include scale(1);
                @include opacity(1);        
                @include backface-visibility(visible);    
                @include box-shadow(0px 0px 40px 0px rgba(32, 32, 32, 0.15))   
            }
        }
    }
    .apus-search-form{   
        width: 273px;
        visibility: hidden;
        pointer-events: none;
        position: absolute;        
        top: 20px;
        margin: 15px;
        z-index: 1;        
        @include rtl-right(-20px);
        @include opacity(0);                
        @include transition-all();  
        @include backface-visibility(hidden);  
        @include transition-all();
        @include box-shadow(none);
        @include scale(0);
        .select-category{
            display: none;
        }
        form{            
            position: relative;
            border: 1px solid $border-color;
            background-color: $white;
            @include border-radius(5px);            
            .btn{
                position: absolute;
                top: 0px;
                border: 0;
                background-color: transparent;                
                padding: 0;                
                text-align: center;
                line-height: 30px;
                color: $text-color;
                @include square(50px);
                @include rtl-right(0);    
                [class*="icon"]{
                    &:before{
                        margin: 0px;
                        font-size: 21px;
                    }
                }
                @include hover-focus-active() {    
                    color: $text-color !important;
                    [class*="icon"]{
                        &:before{
                            color: $text-color !important;
                        }
                    }                                    
                }
            }
            .form-control{
                color: $input-color !important;
                height: 50px;
                padding: 5px 20px 5px 20px;
                border: 0;
                width: 100%;
                font-family: $headings-font-family;                
                @include placeholder($input-color);
            }
        }
    }
    .top-cart{
        @include rtl-margin-right(5px);
    }
    .total-minicart{
        display:none;
    }
    .box-account{
        @include rtl-margin-left(auto);
    }
    // search
    .search-mobile{
        padding: 0px;
        position: relative;
    }
    .box-right{
        position: relative;        
    }
    .apus-search-form .tt-menu a.media{
        padding:12px;
    }
}
.table-visiable{
    display: table;
    width: 100%;
    > div{
        float: none;
        display: table-cell;
        vertical-align: middle;
    }
}
@media(min-width:992px) {
    .table-visiable-dk{
        display: table;
        width: 100%;
        > div{
            float: none;
            display: table-cell;
            vertical-align: middle;
        }
    }
}

.sticky-header{
    position: fixed !important;    
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;    
    background-color: #FFF;      
    @include translateY(0);
    @include box-shadow(0 4px 4px -2px rgba(0,0,0,.09));
    &.sticky-header-hidden{
        @include translateY(-110%);
    }    
    .vertical-wrapper{
        &.show-always{
            .content-vertical{
                visibility: hidden;
                @include opacity(0);
                @include transition(all 0.3s ease-in-out 0s);
            }
            &:hover{
                .content-vertical{
                    visibility: visible;
                    @include opacity(1);
                }
            }
        }
    }
}
.header_transparent{ 
    .logo{
        .transparent-logo{
            display: block;
        }
        .logo-main{
            display: none;
        }        
    }
    .sticky-header{
        .logo{
            .logo-main{
                display: block;
            }
            .transparent-logo{
                display: none;
            }
        }
    }
    .apus-header{
        position:absolute;
        top:0;
        left:0;
        width:100%;
        z-index:99;
        background-color: transparent;
    }
    &:not(.has-header-sticky) .apus-header,
    .main-sticky-header:not(.sticky-header){
        .account-login{
            .login-account{
                li{
                    a{
                        color: $white;
                    }
                }                
            }
        }
        .apus-language{
            .btn-link {
                color: $gray-smoke;
                @include hover-focus-active() {
                    color: $white;
                }
            }
        } 

        .setting-account{
            &.white{
                span,a{
                    color: $white;
                    &:hover{
                        color: $white;
                    }
                }
            }
        }

        .apus-search-form .show-search-form-btn {
            color: #fff;
        }

        .main-menu.apus-main-menu-2 .megamenu > li > a:hover .caret::after {
            color: #fff;
        }

        .apus-custom-toplink{
            &.gray{
                li{
                    a {
                        color: $gray-smoke;
                        @include hover-focus-active() {
                            color: $white;
                        }
                    }
                }                 
            }
            li{
                a {
                    color: #fff;            
                }
            }             
        } 

        .vertical-wrapper.style2 .title-vertical {
            background-color: #fff;
        }

        .apus-search-form.default.style1 .search-form-popup-wrapper {
            background-color: $white;
        }

        .header-button-woo{
            .mini-cart {
                color: $white;
            }
        }
        
        .apus_custom_menu{
            &.white{
                .menu li a{
                    color: $white;
                    @include hover-focus-active() {
                        color: $white;
                    }
                    &:before{
                        color: $white;
                    }
                }
            }
        }        
        .megamenu{
            > li{
                > a{
                    color: #fff;
                }
            }             
        }   
        section{
            &.elementor-element {
                background: transparent !important;
                &.has-bkg{
                    background-color: $theme-color !important;
                    .apus-language{
                        .btn-link{
                            color: $white;                            
                        }
                    }
                }                
            }
        }              
    }    
}

.has-bkg{
    .apus-custom-toplink{
        li{
            a {
                color: $white;
                @include hover-focus-active() {
                    color: $white;
                }
            }
        }         
    } 
    .apus_custom_menu{
        &.white{
            .menu{
                li{
                    a {
                        color: $white;
                        @include hover-focus-active() {
                            color: $white;
                            &:before {
                                color: $white;                            
                            }
                        }
                        &:before {
                            color: $white;                            
                        }
                    }
                }                 
            }             
        }         
    }
}

.no-breadcrumb{
    #main-container{
        &.main-content{
            padding-top: 60px;
        }
    }    
}

.apus-header{
    z-index: 10;
    position: relative;
    font-size: 14px;    
    border: 0;
    &[class*="header-default"]{        
        &:after{
            background-color: rgba($white, .2);            
            content: '';
            position: absolute;
            bottom: 0;
            @include rtl-left(0);
            @include size(100%,1px);
        }
    }
    .container-fluid{                
        @media only screen and (min-width : 1224px) {
            @include rtl-padding-right(68px);
            @include rtl-padding-left(68px);
        }       
    }
    .show-main-menu{
        font-size:30px;
        line-height:1;
        color:$link-color;
        &:hover,
        &:focus,
        &.active{
            color:$theme-color;
        }
    }
}
.header-button-woo{
    position: relative;
    &.header-button-woo-2{
        .apus-topcart{
            .dropdown-menu {                          
                @include rtl-right(-2px);
            }
        }         
    }
    &.white{
        color: $white;
        &:after{
            content: normal;
        }
        .cart {            
            @include rtl-padding-right(0);
            a{
                [class*="icon"]{
                    &:before{
                        margin: 0;
                    }
                }
            }
        }
    }
    &.dark{
        .mini-cart{
            color: $headings-color;
        }
    }
    &.border-left-right{
        position: relative;
        &:before,
        &:after{
            content: '';
            background: rgba($white, .3);
            @include size(1px,50px);
            @include vertical-align(absolute);            
        }
        &:after{
            @include rtl-right(-20px);
        }
        &:before{
            @include rtl-left(-20px);
        }
    }
    &:after{        
        content: '';
        background-color: rgba($white, .2);        
        position: absolute;
        top: -12px;
        @include size(1px,50px);
    }    
    &:after{
        @include rtl-right(0);
    }
    > div{
        @include rtl-margin-left(25px);
        &:last-child{
            @include rtl-margin-left(0);
        }
    }
    .cart{
        @include rtl-padding-left(0px);
        @include rtl-padding-right(20px);
        position: relative;
        &:before,&:after{
            content: '';
        }
    }
    .mini-cart{
        color: $headings-color;
        .count{
            position: absolute;
        }
        [class*="icon"]{
            &:before{
                font-size: 26px;
            }
        }
    }
    &.header-button-woo-2{
        @include size(auto,30px);
        .cart {
            padding-left: 20px;
            padding-right: 20px;
        }
        .mini-cart{                        
            [class*="icon"]{
                &:before {
                    font-size: 24px;
                    margin: 0;
                    color: $gray-smoke;
                }
            }            
        }         
        .count {
            color: $headings-color;
            background-color: $white !important;
        }
        &:before,&:after{            
            position: absolute;
            top: 0;
            content: '';
            background-color: rgba($white,.3);
            @include size(1px,100%);
        }
        &:before{
            @include rtl-left(0);            
            @include rtl-right(auto);            
        }
        &:after{
            @include rtl-left(auto);            
            @include rtl-right(0);            
        }
    }
}
.header-sidebar{
    position:fixed;
    width:100px;
    z-index:91;
    left:0;
    top:0;
    min-height:100vh;
    background:#000;
    color:$white;
    a{
        color:$white;
        &:hover,&:active{
            color:$theme-color;
        }
    }
    .show-main-menu{
        @include size(100px,100px);
        line-height:100px;
        border-width:0 0 1px;
        border-color:$border-color;
        color:$white;
        background:#000000;
    }
    .apus-topcart{
        position:absolute;
        bottom:0;
        left:0;
        width:100%;
        padding:20px 0;
        border-top:1px solid #333333;
        .count{
            color:$white;
            font-size:12px;
            text-transform:uppercase;
        }
        .dropdown-menu{
            bottom:0;
            top:inherit;
            left:100%;
        }
    }
    .service{
        color:#999999;
        white-space:nowrap;
        position:absolute;
        top:50%;
        z-index:9;
        text-transform:uppercase;
        letter-spacing:2px;
        font-size:14px;
        left:50px;
        @include rotate(-90deg);
        @include transform-origin(0 ,11px);
        > *{
            @include translateX(-50%);
        }
        p{
            margin:0;
        }
    }
}

.over-dark{    
    cursor: url('../images/close-icon.png'), auto; 
    display: none;
    position:fixed;
    top:0;
    z-index: 99;    
    background: rgba(0,0,0,0.8);
    @include size(100%,100%);
    @include rtl-right(0);
    &.active{
        display: block;
    }
}

// menu center
.menu-center{
    .navbar-nav.megamenu {
        float: none;
        text-align: center;
    }
}
/*------------------------------------*\
    Breadcrumb
\*------------------------------------*/
.#{$app-prefix}-breadcrumb{
    @include box-size($breadcrumb-layout-bg, $breadcrumb-padding-top, $breadcrumb-padding-bottom);
    @include border-radius(0);
    margin: $breadcrumb-margin;
    text-align: $breadcrumb-alignment;
    font-size: $breadcrumb-font-size ;
    .breadcrumb-title{
        color: $breadcrumb-color;
        margin: 0;
        font-weight: $breadcrumb-title-font-weight;
        @include font-size(font-size, $breadcrumb-title-font-size);
    }
    nav{
        @include rtl-text-align-left();
        line-height: $breadcrumb-line-height;
        a{
            color: $breadcrumb-link-color;
        } 
    }
}

.apus-breadscrumb {
    background-color: transparent;    
    background-repeat: no-repeat;
    background-position: 0 0;
    margin: 0 0 60px 0;    
    padding: 0;
    position: relative;  
    height: 400px;
    text-align: center;
    @include flexbox();
    @include align-items(center);    
    @include justify-content(center);
    &:before{
        content: '';
        top: 0;        
        position: absolute;        
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 0 0;
        @include gradient-horizontal($theme-color, $theme-color-second, 0%, 100%);
        @include square(100%);
        @include rtl-left(0);
        @include rtl-right(0);
        @include opacity(.8);
    }
    a{
        color: $white;
        @include hover-focus-active() {
            color: $white;
        }
    }
    i {
        @include rtl-margin-right(5px);
    }  
    .learn-press-breadcrumb,  
    .breadcrumb {
        background: transparent;
        margin: 5px 0 0 0;
        padding: 0;
        color: $white;
        font-size: 16px;
        font-family: $headings-font-family;        
        .active {
            color: $white;
        }
        a {
            color: $white;
            &:hover{
                color: $white;
            }
        }
    }
    .wrapper-breads {
        position: relative;
        padding: 0;
        margin: 0;
        border: 0;        
        display: block;
        color: $white;        
    }
    .bread-title {
        text-transform: uppercase;
        font-size: 35px;
        line-height: 45px;
        font-weight: 600;
        margin: 0;
        color: $white;
    }
    + #main-container{
        &.home-page-default{
            padding-top: 0;
        }        
    }
}

/*------------------------------------*\
    Container
\*------------------------------------*/
.#{$app-prefix}-mainbody{
    @include box-size($container-bg, $container-padding-top, $container-padding-bottom);
}
/*------------------------------------*\
    Content
\*------------------------------------*/
.#{$app-prefix}-content{
    background: $content-bg;
    @include clearfix();
}
/*------------------------------------*\
    Pagination
\*------------------------------------*/
.navigation {
    display: block;
    clear: both;
}
.#{$app-prefix}-pagination{
    padding: 0;
    margin: 0;
    text-align: center;    
    font-size: 16px;
    font-family: $font-family-second;
    .page-numbers,
    .pagination{
        li{
            display: inline-block;
            vertical-align: middle;
            margin:0;
            > span, > a{
                text-align: center;        
                font-weight: 400;
                font-size: 14px;
                padding: 0;
                float: none;
                color: $text-second;
                min-width: 45px;   
                border: 0;
                line-height: 45px;
                background-color: transparent;     
                font-family: $font-family-second;
                @include square(45px);
                @include inline-block();
                @include border-radius(50%);
                @include transition-all();        
                @include rtl-margin-right(5px);
                &:hover,
                &.current{
                    color: $white;
                    background: $theme-color;
                }
                &.current{
                    pointer-events: none;
                } 
                &.next,&.prev{
                    width: auto;
                    padding: 0 24px;
                    line-height: 40px;
                    position: relative;
                    border: 2px solid $border-color;
                    @include border-radius(30px); 
                    &:hover,&:focus,&:active{
                        color: $white;
                        background-color: $theme-color;
                        border-color: $theme-color;
                        &:after,&:before{
                            color: $white;
                        }
                    }
                }
                &.next{
                    @include rtl-padding-right(46px);                                
                    &:after{
                        font-size: 14px;                
                        content: "\f107";
                        color: $theme-color-second;
                        font-family: $icon-font-family;
                        @include rtl-right(22px);
                        @include vertical-align(absolute);
                        @include transition-all();
                    }
                }
                &.prev{
                    @include rtl-padding-left(46px);            
                    &:before{
                        font-size: 14px;                
                        content: "\f108";                
                        color: $theme-color-second;
                        font-family: $icon-font-family;
                        @include rtl-left(22px);
                        @include vertical-align(absolute);
                    }
                }
            }
        }
    }
    > span, > a{
        text-align: center;        
        font-weight: 400;
        font-size: 14px;
        padding: 0;
        float: none;
        color: $text-second;
        min-width: 45px;   
        border: 0;
        line-height: 45px;
        background-color: transparent;     
        font-family: $font-family-second;
        @include square(45px);
        @include inline-block();
        @include border-radius(50%);
        @include transition-all();        
        @include rtl-margin-right(5px);        
        &:last-child{
            @include rtl-margin-right(0);   
        }     
        &:hover,
        &.current{
            color: $white;
            background: $theme-color;
        }
        &.current{
            pointer-events: none;
        }        
        &.next,&.prev{
            width: auto;
            padding: 0 28px;
            line-height: 40px;
            position: relative;
            border: 2px solid $border-color;
            @include border-radius(30px);            
            @include hover-focus-active() {
                color: $white;
                background-color: $theme-color;
                border-color: $theme-color;
                &:before,&:after{
                    color: $white;
                }
            }
        }
        &.next{
            @include rtl-padding-right(58px);            
            &:after{
                font-size: 14px;                
                content: "\f107";
                color: $theme-color-second;
                font-family: $icon-font-family;
                @include rtl-right(30px);
                @include vertical-align(absolute);
            }
        }
        &.prev{
            @include rtl-padding-left(58px);            
            &:before{
                font-size: 14px;                
                content: "\f108";                
                color: $theme-color-second;
                font-family: $icon-font-family;
                @include rtl-left(30px);
                @include vertical-align(absolute);
            }
        }
    }
    ul.page-numbers{
        margin: 0;
        padding:0;
        list-style: none;
    }
}
/*------------------------------------*\
    Footer
\*------------------------------------*/
.apus-footer-mobile{
    display:none;
    outline: none;
}

// .home{
//     .apus-footer{
//         &.footer-default-wrapper {
//             margin: 60px 0 0 0;
//         }
//     }    
// }

.#{$app-prefix}-footer{
    position: relative;    
    border: 0;
    font-weight: 400;    
    color: $footer-color;
    font-size: 14px;
    a{
        color: $footer-link-color;
        @include hover-focus-active() {
            color: $footer-link-hover-color;
        }        
    }
    &.footer-light{
        background-color: transparent;        
        &:after{
            content: '';
            top: -70px;
            position: absolute;            
            background-color: transparent;
            background-image: url('../images/footer-lighter.png');
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
            z-index: -1;
            @include rtl-left(0);
            @include rtl-right(0);
            @include size(100%,100%);
        }
        .logo.custom-logo.footer-logo:after{
            background-color: #d5d6d8;
        }
    }
    &.footer-lighter{
        background-color: transparent;        
        .logo.custom-logo.footer-logo:after{
            background-color: #f0f0f0;
        }   
    }
    &.footer-home-8{
        font-size: 14px;
        background-color: transparent;        
    }    
}
.footer-default{
    outline: none;
}
.apus-footer.full-medium{
    margin-left:auto;
    margin-right:auto;
    max-width:1600px;
}
/*------------------------------------*\
    Copyright
\*------------------------------------*/
.#{$app-prefix}-copyright{
    color: $copyright-color;
    font-size: $copyright-font-size;
    font-weight: $copyright-font-weight;
    background:$copyright-bg;
    padding-top:30px;
    padding-bottom:30px;
    position: relative;
    border-top: 1px solid $border-color;
    a{
        @include hover-focus-active() {
            color: $copyright-link-hover-color;
        }        
        color: $copyright-link-color;
    }
    // social
    .widget-social {
        @include rtl-text-align-right();
        a{
          color: $copyright-color;
          &:hover{
            color: $theme-color;
          }  
        }
    }
}

.logo-in-theme{
    @include rtl-margin-right(100px);
}

/*------------------------------------*\
    Top bar
\*------------------------------------*/
.apus-offcanvas {
    .apus-offcanvas-body{
        position: relative;
    }
    visibility:hidden;
    @include opacity(0);
    @include translateX(-100%);
    @include transition(all 0.2s  ease-in-out 0s);
    position: fixed;
    top:0;
    left:0;
    z-index:1000;
    width:300px;
    background: $white;
    height: 100%;
    overflow-x: auto;
    display: -webkit-flex; /* Safari */
    display: flex;
    flex-direction:column;
    -webkit-flex-direction:column;
    .offcanvas-bottom,
    .offcanvas-top{
        height:20%;
    }
    .offcanvas-middle{
        height:60%;
        padding:20px 0;
        overflow-x:hidden;
    }
    &.active{
        @include translateX(0);
        @include opacity(1);
        visibility:visible;
        @include box-shadow(2px 0 5px 0 rgba(0,0,0,0.15));
    }
    .offcanvas-head{
        background: $theme-color-second;
        cursor: pointer;
        padding: 0;
        @include transition(all 0.2s ease-in-out 0s);
        @include hover-active() {
            background:darken($theme-color-second, 10%);
        }        
        i{
            @include rtl-margin-right(5px);
        }
        a{
            color:$white;
            font-size: 20px;
            font-family: $headings-font-family;
            font-weight: $headings-font-weight;
            position: relative;            
            width: 100%;
            cursor: pointer;
            @include inline-block();
            @include rtl-padding(10px, 30px, 10px, 65px);
            &:before{
                content: "";
                display: block;
                background-color: transparent;
                background-image: url('../images/close-icon.png');
                background-repeat: no-repeat;
                background-position: 0 0;
                background-size: cover;
                @include rtl-left(30px);
                @include square(20px);
                @include vertical-align(absolute);
            }
        }
    }
    .logo-in-theme{
        margin-bottom:30px;
    }
    .header-right{
        > div{
            @include rtl-margin-left(30px);
            &:last-child{
                @include rtl-margin-left(0px);
            }
        }
    }     
    .elementor-column{
        width: 100% !important;
        .elementor-column-wrap, .elementor-widget-wrap{
            padding:0 !important;
        }
        .widget{
            margin-bottom: 10px;
        }
    }
}
@media(max-width:991px) {
    .topbar-mobile  {
        padding: 10px;
        .btn{
            @include rtl-margin-right(10px);
            padding:6px 10px;
        }
        .top-cart .dropdown-menu{
            left: 0;
            right: inherit;
            &:after,
            &:before{
                display: none;
            }
        }
    }
}
// layout for sidebar
.open-text{
    font-size: 26px;
    color: $white;
    cursor: pointer;
    padding:6px 8px;
    background:$brand-success;
    display: inline-block;
    line-height: 1;
    @include transition(all 0.2s ease-in-out 0s);
    &:hover,&:active{
        color: $white;
        background:darken($brand-success, 5%);
    }
}
#mobile-offcanvas-sidebar{
    position: fixed;
    z-index: 999;
    top:0px;
    @include size(270px,100%);
    max-width: 80%;
    background:$white;
    &.mobile-offcanvas-left{
        left:0;
        @include translateX(-100%);
        > .mobile-sidebar-btn{
            left: 100%;
        }
    }
    &.mobile-offcanvas-right{
        right:0;
        @include translateX(100%);
        > .mobile-sidebar-btn{
            right: 100%;
        }
    }
    .mobile-sidebar-wrapper{
        display: none;
        height: 100%;
        width:100%;
        padding:0 15px;
    }
    &.active{
        > .mobile-sidebar-wrapper{
            display: block;
        }
    }
    > .mobile-sidebar-btn{
        position: absolute;
        top: 100px;
    }
}
.mobile-sidebar-panel-overlay{
    position:fixed;
    top: 0;
    left: 0;
    z-index:-10;
    background: rgba(0,0,0,0.15);
    @include square(100%);    
    @include opacity(0);
    @include transition(all 0.2s ease-in-out 0s);
    &.active{
        z-index: 998;
        @include opacity(1);                       
        cursor: url('../images/close-icon.png'), auto;        
    }
}
// footer mobile
.apus-footer-mobile{
    position:fixed;
    z-index: 999;
    background:rgba(255,255,255,0.9);
    padding:10px 20px;
    bottom:0;
    left:0;
    width:100%;
    @include box-shadow(0 0 1px 0 rgba(0,0,0,0.2));
    .footer-search-mobile{
        position:absolute;
        z-index: 999;
        left: 0;
        top:-60px;
        width: 100%;
        @include opacity(0);
        visibility: hidden;
        @include transition(all 0.2s ease-in-out 0s);
        &.active{
            visibility: visible;
            @include opacity(1);
        }
    }
    > ul{
        padding:0;
        margin:0;
        list-style:none;
        text-align: center;
        > li{
            text-transform: uppercase;
            display: inline-block;
            padding:0 25px;
            text-align: center;
            position:relative;
            span{
                display: block;
                font-size: 10px;
                line-height: 1;
            }
            .wishlist-icon, .mini-cart{
                line-height:$line-height-base;
            }
            .wrapper-morelink{
                @include opacity(0);
                visibility: hidden;
                position:absolute;
                right:0;
                bottom:40px;
                padding:20px;
                background:$white;
                @include box-shadow(0 0 4px 0 rgba(0,0,0,0.1));
                .footer-morelink{
                    list-style:none;
                    padding:0;
                    margin:0;
                    font-size: 10px;
                }
                li{
                    padding:3px 0;
                    white-space: nowrap;
                    display: block;
                    width: 100%;
                    @include rtl-text-align-left();
                }
                &.active{
                    visibility: visible;
                    @include opacity(1);
                }
            }
        }
    }
    .mini-cart i{
        font-size: 15px;
    }
    .mini-cart .count{
        top:2px;
    }
    .apus-search-form{
        min-width: 300px;
        padding: 10px 30px;
        background:rgba(255,255,255,0.9);
        .select-category{
          display: none;
        }
        form{
            border:none;
            margin:0;
            .form-control{
                border:1px solid $border-color;
            }
        }
    }
}
// fix sidebar
.close-sidebar-btn,
.mobile-sidebar-btn{
    cursor:pointer;
    font-weight:500;
    margin-bottom:10px;
    font-size:14px;    
    @include inline-block()
}
.mobile-sidebar-btn{    
    margin: 0 0 30px 0;
    font-size: 16px;
    color: $headings-color;
    font-family: $headings-font-family;
    font-weight: $headings-font-weight;
    [class*="icon"]{        
        background-color: transparent;
        background-image: url('../images/icon-sidebar.svg');
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: cover;
        @include square(20px);
        @include rtl-float-left();    
        @include rtl-margin(2px, 10px, 0, 0);    
    }
    @include hover-focus-active() {
        color: $headings-color;
    }
}
.close-sidebar-btn{
    &:active,
    &:hover{
        color:$brand-danger;
    }
}
.close-sidebar-btn{
    top: 0;
    color: transparent;
    position: absolute;
    cursor: pointer;
    margin-bottom: 0px;
    background-color: transparent;
    font-size: 0px;    
    @include square(50px);
    @include flexbox();
    @include justify-content(center);
    @include align-items(center); 
    @include rtl-right(8px);
    &:before{
        content: '';
        background-image: url('../images/close-big.svg');
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
        @include square(20px);        
    }
}

.sidebar{
    .widget{        
        &:last-child{
            margin-bottom: 0px;
        }
    }
    .widget-search{
        form{
            > div{
                &.input-group {
                    width: 100%;
                    margin: 0;
                }
            }            
        }         
    } 
    .close-sidebar-btn{
        @include justify-content(flex-start);
        @include rtl-left(0);
        @include rtl-right(0);
        @include size(100%, 52px);
        @include rtl-text-align-left();
        color: $white;
        background-color: $theme-color-second;
        &:before{
            background-image: url('../images/close-icon.png');            
            @include rtl-left(30px);
            @include square(20px);
            @include vertical-align(absolute);
        }
        span{
            position: relative;
            color: $white;
            font-size: 20px;
            font-family: $headings-font-family;
            font-weight: $headings-font-weight;            
            width: 100%;
            cursor: pointer;
            @include inline-block();                        
            @include rtl-padding(10px, 30px, 10px, 65px);
        }
    }
}

// main-menu-top
.apus-header{
    .wrapper-topmenu{
        &:before{
            content:'';
            position:absolute;
            top:100%;
            left:0;
            @include size(100%,10px);
            background:transparent;
            z-index:9;
        }
        .dropdown-menu-right{
            top:calc(100% + 10px);
        }
    }
}
.apus-topbar{
    .wrapper-topmenu{
        &:hover{
            > a{
                color:$white;
            }
        }
    }
}
// fix for add cart
.wrapper-top-cart{
    .overlay-dropdown-menu-right{
        position:fixed;
        @include transition(all 0.2s ease-in-out 0s);
        @include size(100%,100%);
        background:rgba(0,0,0,0.6);
        top:0;
        left:0;
        @include opacity(0);
        visibility:hidden;
        z-index:98;
        &.active{
            @include opacity(1);
            visibility:visible;
        }
    }
    > .dropdown-menu-right{
        max-width:70%;
        @include flexbox();
        flex-direction: column;
        -webkit-flex-direction: column;
        position:fixed;
        z-index:999;
        @include transition(all 0.2s ease-in-out 0s);
        top:0;
        right:0;
        background:$white;
        @include size(420px,100%);
        padding:$theme-margin;
        @include translateX(100%);
        .widget_shopping_cart_heading{
            @include flexbox();
            flex:0 0 auto;
            -webkit-flex:0 0 auto;
            > h3{
                margin:0 0 20px;
                font-size:22px;
                padding:0 0 20px;
                border-bottom:1px solid $border-color;
                width:100%;
                cursor:pointer;
                color:$brand-danger;
            }
        }
        .widget_shopping_cart_content_wrapper{
            @include flexbox();
            flex:1 1 auto;
            -webkit-flex:1 1 auto;
            overflow-x: hidden;
            overflow-y: auto;
        }
        .shopping_cart_content{
            @include flexbox();
            flex-direction: column; 
            -webkit-flex-direction: column;
            height:100%;
            .cart_list{
                flex:1 1 auto;
                -webkit-flex:1 1 auto;
                @include flexbox();
                flex-direction: column; 
                -webkit-flex-direction: column; 
            }
            .cart-bottom{
                flex-direction: column; 
                -webkit-flex-direction: column; 
                flex:0 0 auto;
                -webkit-flex:0 0 auto;
                @include flexbox();
            }
        }
        &.active{
            @include translateX(0);
        }
    }
    .cart_list {
        .variation{
            margin:0;
            > *{
                display:inline-block;
                vertical-align:middle;
                p{
                    margin:0;
                }
            }
        }
    }
    .buttons{
        .btn-block{
            margin-bottom:10px;
        }
    }
}
.rtl{
    .wrapper-top-cart{
        > .dropdown-menu-right{
            right:inherit;
            left:0;
            @include translateX(-100%);
            &.active{
                @include translateX(0);
            }
        }
    }
}

.account-login{
    .login-account{
        @include flexbox();
        @include align-items(center);
        li{    
            margin-bottom: 0;        
            a{
                color: $headings-color;                  
                font-size: 16px;
                font-family: $headings-font-family;                            
                [class*="icon"]{
                    &:before{           
                        margin: 0;             
                        font-size: 18px;                                                                        
                    }
                }
                @include hover-focus-active() {
                    color: $theme-color;
                }
            }
        }
        &.white{
            li{
                @include hover-focus-active() {
                    a{
                        color: $theme-color;
                    }                
                }
                a{
                    color: $headings-color;
                }
            }            
        }
        &.gray{
            li{
                @include hover-focus-active() {
                    a{
                        color: $white;                       
                    }                
                }
                a{
                    color: $gray-smoke;  
                }
            }            
        }
    }
}
.user-account{
    @include rtl-margin-left(20px);
    .user-log{
        list-style: none;
        margin: 0;
        padding: 0;
    }
    .list-line{
        list-style: none;
        margin: 0px;
        padding: 0px;
    }
}
.setting-account{
    font-size: 14px;    
    position: relative;
    font-family: $headings-font-family;    
    @include flexbox();
    @include align-items(center); 
    @include hover-focus-active() {
        .user-account{
            visibility: visible;
            pointer-events: auto;
            @include opacity(1);
            @include translateY(0);
        }
        &:after{
            @include opacity(1);
            visibility: visible;
            pointer-events: auto;            
        }
    }
    &:after{
        content: '';
        z-index: 2;        
        background-color: transparent;
        pointer-events: none;
        position: absolute;
        bottom: -23px;
        visibility: hidden;
        @include rtl-left(0);
        @include opacity(0);            
        @include size(100%,23px);
    }
    .profile-menus{
        cursor: pointer;        
    }
    .user-account{
        z-index: 99;
        background-color: $white;            
        margin: 15px 0 0 0;
        top: 100%;                    
        padding: 20px;
        position: absolute;
        width: 200px;       
        visibility: hidden;     
        pointer-events: none;
        @include opacity(0);
        @include rtl-left(0);        
        @include border-radius(5px);
        @include transition-all(); 
        @include translateY(20px);
        @include box-shadow(0 0 18px 0 rgba(0,0,0,0.1));    
        &:before{                       
            top: -8px;
            content: "";
            position: absolute;     
            background: $white;
            @include square(0);                             
            @include rtl-left(40px);                                                
            @include square(16px);                              
            @include rotate(45deg);     
            @include box-shadow(-2px -2px 10px -5px rgba(0, 0, 0, 0.2)); 
        }           
        li{
            line-height: 40px;
            margin-bottom: 0;            
            a{
                font-size: 16px;
                color: $headings-color !important;  
                @include hover-focus-active() {
                    color: $theme-color !important;
                } 
            }
        }
        .list-line{
            li{
                line-height: 40px;
                border-bottom: 1px solid $border-color;                
                a{
                    padding: 0px;
                    font-size: 16px;
                    color: $headings-color !important;                                                            
                    @include hover-focus-active() {
                        color: $theme-color !important;    
                    }
                }                    
            }
        }        
    }
    &.white{        
        .user-account{
            li{
                a{
                    color: $headings-color !important;
                    @include hover-focus-active() {
                        color: $theme-color !important;
                    }
                }
            }
        }
    }       
    span{   
        color: $headings-color;     
        text-transform: capitalize;     
    }
    a{        
        color: $headings-color;
        @include hover-focus-active() {
            color: $headings-color;
        }
    }
    &.gray{
        span{
            color: $gray-smoke;   
        }
        a{
            color: $gray-smoke;
            @include hover-focus-active() {
                color: $white;
            }
        }
    }
    &.white{
        color: $white;
        span,a{
            color: $headings-color;
            &:hover{
                color: $theme-color;
            }
        }
        .user-account{
            a{
                color: $white !important;
            }
        }
    } 
}
.profile-avarta{
    @include rtl-margin-right(12px);
    img{
        @include border-radius(100% !important);
    }
}

