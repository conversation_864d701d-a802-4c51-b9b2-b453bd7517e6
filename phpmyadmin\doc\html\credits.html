
<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Credits &#8212; phpMyAdmin 5.2.1 documentation</title>
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="_static/classic.css" type="text/css" />
    
    <script id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script src="_static/jquery.js"></script>
    <script src="_static/underscore.js"></script>
    <script src="_static/doctools.js"></script>
    
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="copyright" title="Copyright" href="copyright.html" />
    <link rel="next" title="Glossary" href="glossary.html" />
    <link rel="prev" title="Copyright" href="copyright.html" /> 
  </head><body>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             accesskey="I">index</a></li>
        <li class="right" >
          <a href="glossary.html" title="Glossary"
             accesskey="N">next</a> |</li>
        <li class="right" >
          <a href="copyright.html" title="Copyright"
             accesskey="P">previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Credits</a></li> 
      </ul>
    </div>  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          <div class="body" role="main">
            
  <div class="section" id="credits">
<span id="id1"></span><h1>Credits<a class="headerlink" href="#credits" title="Permalink to this headline">¶</a></h1>
<div class="section" id="credits-in-chronological-order">
<h2>Credits, in chronological order<a class="headerlink" href="#credits-in-chronological-order" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li><p>Tobias Ratschiller &lt;tobias_at_ratschiller.com&gt;</p>
<ul>
<li><p>creator of the phpMyAdmin project</p></li>
<li><p>maintainer from 1998 to summer 2000</p></li>
</ul>
</li>
<li><p>Marc Delisle &lt;marc_at_infomarc.info&gt;</p>
<ul>
<li><p>multi-language version in December 1998</p></li>
<li><p>various fixes and improvements</p></li>
<li><p>first version of the <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> analyser (most of it)</p></li>
<li><p>maintainer from 2001 to 2015</p></li>
</ul>
</li>
<li><p>Olivier Müller &lt;om_at_omnis.ch&gt;</p>
<ul>
<li><p>started SourceForge phpMyAdmin project in March 2001</p></li>
<li><p>sync’ed different existing CVS trees with new features and bugfixes</p></li>
<li><p>multi-language improvements, dynamic language selection</p></li>
<li><p>many bugfixes and improvements</p></li>
</ul>
</li>
<li><p>Loïc Chapeaux &lt;lolo_at_phpheaven.net&gt;</p>
<ul>
<li><p>rewrote and optimized JavaScript, DHTML and DOM stuff</p></li>
<li><p>rewrote the scripts so they fit the <a class="reference internal" href="glossary.html#term-PEAR"><span class="xref std std-term">PEAR</span></a> coding standards and
generate XHTML1.0 and CSS2 compliant codes</p></li>
<li><p>improved the language detection system</p></li>
<li><p>many bugfixes and improvements</p></li>
</ul>
</li>
<li><p>Robin Johnson &lt;robbat2_at_users.sourceforge.net&gt;</p>
<ul>
<li><p>database maintenance controls</p></li>
<li><p>table type code</p></li>
<li><p>Host authentication <a class="reference internal" href="glossary.html#term-IP"><span class="xref std std-term">IP</span></a> Allow/Deny</p></li>
<li><p>DB-based configuration (Not completed)</p></li>
<li><p><a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> parser and pretty-printer</p></li>
<li><p><a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> validator</p></li>
<li><p>many bugfixes and improvements</p></li>
</ul>
</li>
<li><p>Armel Fauveau &lt;armel.fauveau_at_globalis-ms.com&gt;</p>
<ul>
<li><p>bookmarks feature</p></li>
<li><p>multiple dump feature</p></li>
<li><p>gzip dump feature</p></li>
<li><p>zip dump feature</p></li>
</ul>
</li>
<li><p>Geert Lund &lt;glund_at_silversoft.dk&gt;</p>
<ul>
<li><p>various fixes</p></li>
<li><p>moderator of the phpMyAdmin former users forum at phpwizard.net</p></li>
</ul>
</li>
<li><p>Korakot Chaovavanich &lt;korakot_at_iname.com&gt;</p>
<ul>
<li><p>“insert as new row” feature</p></li>
</ul>
</li>
<li><p>Pete Kelly &lt;webmaster_at_trafficg.com&gt;</p>
<ul>
<li><p>rewrote and fix dump code</p></li>
<li><p>bugfixes</p></li>
</ul>
</li>
<li><p>Steve Alberty &lt;alberty_at_neptunlabs.de&gt;</p>
<ul>
<li><p>rewrote dump code for PHP4</p></li>
<li><p>mySQL table statistics</p></li>
<li><p>bugfixes</p></li>
</ul>
</li>
<li><p>Benjamin Gandon &lt;gandon_at_isia.cma.fr&gt;</p>
<ul>
<li><p>main author of the version *******</p></li>
<li><p>bugfixes</p></li>
</ul>
</li>
<li><p>Alexander M. Turek &lt;me_at_derrabus.de&gt;</p>
<ul>
<li><p>MySQL 4.0 / 4.1 / 5.0 compatibility</p></li>
<li><p>abstract database interface (PMA_DBI) with MySQLi support</p></li>
<li><p>privileges administration</p></li>
<li><p><a class="reference internal" href="glossary.html#term-XML"><span class="xref std std-term">XML</span></a> exports</p></li>
<li><p>various features and fixes</p></li>
<li><p>German language file updates</p></li>
</ul>
</li>
<li><p>Mike Beck &lt;mike.beck_at_web.de&gt;</p>
<ul>
<li><p>automatic joins in QBE</p></li>
<li><p>links column in printview</p></li>
<li><p>Relation view</p></li>
</ul>
</li>
<li><p>Michal Čihař &lt;michal_at_cihar.com&gt;</p>
<ul>
<li><p>enhanced index creation/display feature</p></li>
<li><p>feature to use a different charset for HTML than for MySQL</p></li>
<li><p>improvements of export feature</p></li>
<li><p>various features and fixes</p></li>
<li><p>Czech language file updates</p></li>
<li><p>created current website for phpMyAdmin</p></li>
</ul>
</li>
<li><p>Christophe Gesché from the “MySQL Form Generator for PHPMyAdmin”
(<a class="reference external" href="https://sourceforge.net/projects/phpmysqlformgen/">https://sourceforge.net/projects/phpmysqlformgen/</a>)</p>
<ul>
<li><p>suggested the patch for multiple table printviews</p></li>
</ul>
</li>
<li><p>Garvin Hicking &lt;me_at_supergarv.de&gt;</p>
<ul>
<li><p>built the patch for vertical display of table rows</p></li>
<li><p>built the Javascript based Query window + <a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> history</p></li>
<li><p>Improvement of column/db comments</p></li>
<li><p>(MIME)-Transformations for columns</p></li>
<li><p>Use custom alias names for Databases in left frame</p></li>
<li><p>hierarchical/nested table display</p></li>
<li><p><a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a>-scratchboard for WYSIWYG-
distribution of <a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a> relations</p></li>
<li><p>new icon sets</p></li>
<li><p>vertical display of column properties page</p></li>
<li><p>some bugfixes, features, support, German language additions</p></li>
</ul>
</li>
<li><p>Yukihiro Kawada &lt;kawada_at_den.fujifilm.co.jp&gt;</p>
<ul>
<li><p>japanese kanji encoding conversion feature</p></li>
</ul>
</li>
<li><p>Piotr Roszatycki &lt;d3xter_at_users.sourceforge.net&gt; and Dan Wilson</p>
<ul>
<li><p>the Cookie authentication mode</p></li>
</ul>
</li>
<li><p>Axel Sander &lt;n8falke_at_users.sourceforge.net&gt;</p>
<ul>
<li><p>table relation-links feature</p></li>
</ul>
</li>
<li><p>Maxime Delorme &lt;delorme.maxime_at_free.fr&gt;</p>
<ul>
<li><p><a class="reference internal" href="glossary.html#term-PDF"><span class="xref std std-term">PDF</span></a> schema output, thanks also to
Olivier Plathey for the “FPDF” library (see &lt;<a class="reference external" href="http://www.fpdf.org/">http://www.fpdf.org/</a>&gt;), Steven
Wittens for the “UFPDF” library and
Nicola Asuni for the “TCPDF” library (see &lt;<a class="reference external" href="https://tcpdf.org/">https://tcpdf.org/</a>&gt;).</p></li>
</ul>
</li>
<li><p>Olof Edlund &lt;olof.edlund_at_upright.se&gt;</p>
<ul>
<li><p><a class="reference internal" href="glossary.html#term-SQL"><span class="xref std std-term">SQL</span></a> validator server</p></li>
</ul>
</li>
<li><p>Ivan R. Lanin &lt;ivanlanin_at_users.sourceforge.net&gt;</p>
<ul>
<li><p>phpMyAdmin logo (until June 2004)</p></li>
</ul>
</li>
<li><p>Mike Cochrane &lt;mike_at_graftonhall.co.nz&gt;</p>
<ul>
<li><p>blowfish library from the Horde project (withdrawn in release 4.0)</p></li>
</ul>
</li>
<li><p>Marcel Tschopp &lt;ne0x_at_users.sourceforge.net&gt;</p>
<ul>
<li><p>mysqli support</p></li>
<li><p>many bugfixes and improvements</p></li>
</ul>
</li>
<li><p>Nicola Asuni (Tecnick.com)</p>
<ul>
<li><p>TCPDF library (&lt;<a class="reference external" href="https://tcpdf.org">https://tcpdf.org</a>&gt;)</p></li>
</ul>
</li>
<li><p>Michael Keck &lt;mkkeck_at_users.sourceforge.net&gt;</p>
<ul>
<li><p>redesign for 2.6.0</p></li>
<li><p>phpMyAdmin sailboat logo (June 2004)</p></li>
</ul>
</li>
<li><p>Mathias Landhäußer</p>
<ul>
<li><p>Representation at conferences</p></li>
</ul>
</li>
<li><p>Sebastian Mendel &lt;cybot_tm_at_users.sourceforge.net&gt;</p>
<ul>
<li><p>interface improvements</p></li>
<li><p>various bugfixes</p></li>
</ul>
</li>
<li><p>Ivan A Kirillov</p>
<ul>
<li><p>new relations Designer</p></li>
</ul>
</li>
<li><p>Raj Kissu Rajandran (Google Summer of Code 2008)</p>
<ul>
<li><p>BLOBstreaming support (withdrawn in release 4.0)</p></li>
</ul>
</li>
<li><p>Piotr Przybylski (Google Summer of Code 2008, 2010 and 2011)</p>
<ul>
<li><p>improved setup script</p></li>
<li><p>user preferences</p></li>
<li><p>Drizzle support</p></li>
</ul>
</li>
<li><p>Derek Schaefer (Google Summer of Code 2009)</p>
<ul>
<li><p>Improved the import system</p></li>
</ul>
</li>
<li><p>Alexander Rutkowski (Google Summer of Code 2009)</p>
<ul>
<li><p>Tracking mechanism</p></li>
</ul>
</li>
<li><p>Zahra Naeem (Google Summer of Code 2009)</p>
<ul>
<li><p>Synchronization feature (removed in release 4.0)</p></li>
</ul>
</li>
<li><p>Tomáš Srnka (Google Summer of Code 2009)</p>
<ul>
<li><p>Replication support</p></li>
</ul>
</li>
<li><p>Muhammad Adnan (Google Summer of Code 2010)</p>
<ul>
<li><p>Relation schema export to multiple formats</p></li>
</ul>
</li>
<li><p>Lori Lee (Google Summer of Code 2010)</p>
<ul>
<li><p>User interface improvements</p></li>
<li><p>ENUM/SET editor</p></li>
<li><p>Simplified interface for export/import</p></li>
</ul>
</li>
<li><p>Ninad Pundalik (Google Summer of Code 2010)</p>
<ul>
<li><p>AJAXifying the interface</p></li>
</ul>
</li>
<li><p>Martynas Mickevičius (Google Summer of Code 2010)</p>
<ul>
<li><p>Charts</p></li>
</ul>
</li>
<li><p>Barrie Leslie</p>
<ul>
<li><p>BLOBstreaming support with PBMS PHP extension (withdrawn in release
4.0)</p></li>
</ul>
</li>
<li><p>Ankit Gupta (Google Summer of Code 2010)</p>
<ul>
<li><p>Visual query builder</p></li>
</ul>
</li>
<li><p>Madhura Jayaratne (Google Summer of Code 2011)</p>
<ul>
<li><p>OpenGIS support</p></li>
</ul>
</li>
<li><p>Ammar Yasir (Google Summer of Code 2011)</p>
<ul>
<li><p>Zoom search</p></li>
</ul>
</li>
<li><p>Aris Feryanto (Google Summer of Code 2011)</p>
<ul>
<li><p>Browse-mode improvements</p></li>
</ul>
</li>
<li><p>Thilanka Kaushalya (Google Summer of Code 2011)</p>
<ul>
<li><p>AJAXification</p></li>
</ul>
</li>
<li><p>Tyron Madlener (Google Summer of Code 2011)</p>
<ul>
<li><p>Query statistics and charts for the status page</p></li>
</ul>
</li>
<li><p>Zarubin Stas (Google Summer of Code 2011)</p>
<ul>
<li><p>Automated testing</p></li>
</ul>
</li>
<li><p>Rouslan Placella (Google Summer of Code 2011 and 2012)</p>
<ul>
<li><p>Improved support for Stored Routines, Triggers and Events</p></li>
<li><p>Italian translation updates</p></li>
<li><p>Removal of frames, new navigation</p></li>
</ul>
</li>
<li><p>Dieter Adriaenssens</p>
<ul>
<li><p>Various bugfixes</p></li>
<li><p>Dutch translation updates</p></li>
</ul>
</li>
<li><p>Alex Marin (Google Summer of Code 2012)</p>
<ul>
<li><p>New plugins and properties system</p></li>
</ul>
</li>
<li><p>Thilina Buddika Abeyrathna (Google Summer of Code 2012)</p>
<ul>
<li><p>Refactoring</p></li>
</ul>
</li>
<li><p>Atul Pratap Singh  (Google Summer of Code 2012)</p>
<ul>
<li><p>Refactoring</p></li>
</ul>
</li>
<li><p>Chanaka Indrajith (Google Summer of Code 2012)</p>
<ul>
<li><p>Refactoring</p></li>
</ul>
</li>
<li><p>Yasitha Pandithawatta (Google Summer of Code 2012)</p>
<ul>
<li><p>Automated testing</p></li>
</ul>
</li>
<li><p>Jim Wigginton (phpseclib.sourceforge.net)</p>
<ul>
<li><p>phpseclib</p></li>
</ul>
</li>
<li><p>Bin Zu (Google Summer of Code 2013)</p>
<ul>
<li><p>Refactoring</p></li>
</ul>
</li>
<li><p>Supun Nakandala (Google Summer of Code 2013)</p>
<ul>
<li><p>Refactoring</p></li>
</ul>
</li>
<li><p>Mohamed Ashraf (Google Summer of Code 2013)</p>
<ul>
<li><p>AJAX error reporting</p></li>
</ul>
</li>
<li><p>Adam Kang (Google Summer of Code 2013)</p>
<ul>
<li><p>Automated testing</p></li>
</ul>
</li>
<li><p>Ayush Chaudhary (Google Summer of Code 2013)</p>
<ul>
<li><p>Automated testing</p></li>
</ul>
</li>
<li><p>Kasun Chathuranga (Google Summer of Code 2013)</p>
<ul>
<li><p>Interface improvements</p></li>
</ul>
</li>
<li><p>Hugues Peccatte</p>
<ul>
<li><p>Load/save query by example (database search bookmarks)</p></li>
</ul>
</li>
<li><p>Smita Kumari (Google Summer of Code 2014)</p>
<ul>
<li><p>Central list of columns</p></li>
<li><p>Improve table structure (normalization)</p></li>
</ul>
</li>
<li><p>Ashutosh Dhundhara (Google Summer of Code 2014)</p>
<ul>
<li><p>Interface improvements</p></li>
</ul>
</li>
<li><p>Dhananjay Nakrani (Google Summer of Code 2014)</p>
<ul>
<li><p>PHP error reporting</p></li>
</ul>
</li>
<li><p>Edward Cheng (Google Summer of Code 2014)</p>
<ul>
<li><p>SQL Query Console</p></li>
</ul>
</li>
<li><p>Kankanamge Bimal Yashodha (Google Summer of Code 2014)</p>
<ul>
<li><p>Refactoring: Designer/schema integration</p></li>
</ul>
</li>
<li><p>Chirayu Chiripal (Google Summer of Code 2014)</p>
<ul>
<li><p>Custom field handlers (Input based MIME transformations)</p></li>
<li><p>Export with table/column name changes</p></li>
</ul>
</li>
<li><p>Dan Ungureanu (Google Summer of Code 2015)</p>
<ul>
<li><p>New parser and analyzer</p></li>
</ul>
</li>
<li><p>Nisarg Jhaveri (Google Summer of Code 2015)</p>
<ul>
<li><p>Page-related settings</p></li>
<li><p>SQL debugging integration to the Console</p></li>
<li><p>Other UI improvements</p></li>
</ul>
</li>
<li><p>Deven Bansod (Google Summer of Code 2015)</p>
<ul>
<li><p>Print view using CSS</p></li>
<li><p>Other UI improvements and new features</p></li>
</ul>
</li>
<li><p>Deven Bansod (Google Summer of Code 2017)</p>
<ul>
<li><p>Improvements to the Error Reporting Server</p></li>
<li><p>Improved Selenium testing</p></li>
</ul>
</li>
<li><p>Manish Bisht (Google Summer of Code 2017)</p>
<ul>
<li><p>Mobile user interface</p></li>
<li><p>Remove inline JavaScript code</p></li>
<li><p>Other UI improvements</p></li>
</ul>
</li>
<li><p>Raghuram Vadapalli (Google Summer of Code 2017)</p>
<ul>
<li><p>Multi-table query interface</p></li>
<li><p>Allow Designer to work with tables from other databases</p></li>
<li><p>Other UI improvements</p></li>
</ul>
</li>
<li><p>Maurício Meneghini Fauth</p>
<ul>
<li><p>Major improvements and upgrades to the JavaScript core</p></li>
<li><p>Modernize JavaScript library functionality</p></li>
<li><p>Modernize templating and introduce Twig</p></li>
</ul>
</li>
<li><p>William Desportes</p>
<ul>
<li><p>Coding style improvements based on PHPStan</p></li>
<li><p>Improve links to external MySQL and MariaDB documentation</p></li>
<li><p>Numerous other bug fixes</p></li>
</ul>
</li>
<li><p>Emanuel Bronshtein</p>
<ul>
<li><p>Comprehensive security assessment and suggestions</p></li>
</ul>
</li>
<li><p>Lakshya Arora (Google Summer of Code 2018)</p>
<ul>
<li><p>Various improvements including:</p>
<ul>
<li><p>Integrate user preferences with local storage</p></li>
<li><p>Use a modal login after session expiration</p></li>
<li><p>Add support for CHECK CONSTRAINTS</p></li>
<li><p>and more!</p></li>
</ul>
</li>
</ul>
</li>
<li><p>Saksham Gupta (Google Summer of Code 2018)</p>
<ul>
<li><p>Automated theme generator tool</p></li>
</ul>
</li>
<li><p>Leonardo Strozzi (Google Summer of Code 2018)</p>
<ul>
<li><p>Refactoring Twig templates and other internal code improvements</p></li>
</ul>
</li>
<li><p>Piyush Vijay (Google Summer of Code 2018)</p>
<ul>
<li><p>Modernize the JavaScript code including introducing Webpack, Babel, and Yarn as well as eslint and Jsdoc</p></li>
</ul>
</li>
</ul>
<p>And also to the following people who have contributed minor changes,
enhancements, bugfixes or support for a new language since version
2.1.0:</p>
<p>Bora Alioglu, Ricardo ?, Sven-Erik Andersen, Alessandro Astarita,
Péter Bakondy, Borges Botelho, Olivier Bussier, Neil Darlow, Mats
Engstrom, Ian Davidson, Laurent Dhima, Kristof Hamann, Thomas Kläger,
Lubos Klokner, Martin Marconcini, Girish Nair, David Nordenberg,
Andreas Pauley, Bernard M. Piller, Laurent Haas, “Sakamoto”, Yuval
Sarna, www.securereality.com.au, Alexis Soulard, Alvar Soome, Siu Sun,
Peter Svec, Michael Tacelosky, Rachim Tamsjadi, Kositer Uros, Luís V.,
Martijn W. van der Lee, Algis Vainauskas, Daniel Villanueva, Vinay,
Ignacio Vazquez-Abrams, Chee Wai, Jakub Wilk, Thomas Michael
Winningham, Vilius Zigmantas, “Manuzhai”.</p>
</div>
<div class="section" id="translators">
<h2>Translators<a class="headerlink" href="#translators" title="Permalink to this headline">¶</a></h2>
<p>Following people have contributed to translation of phpMyAdmin:</p>
<ul>
<li><p>Albanian</p>
<blockquote>
<div><ul class="simple">
<li><p>Arben Çokaj &lt;acokaj_at_shkoder.net&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Arabic</p>
<blockquote>
<div><ul class="simple">
<li><p>Ahmed Saleh Abd El-Raouf Ismae &lt;a.saleh.ismael_at_gmail.com&gt;</p></li>
<li><p>Ahmed Saad &lt;egbrave_at_hotmail.com&gt;</p></li>
<li><p>hassan mokhtari &lt;persiste1_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Armenian</p>
<blockquote>
<div><ul class="simple">
<li><p>Andrey Aleksanyants &lt;aaleksanyants_at_yahoo.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Azerbaijani</p>
<blockquote>
<div><ul class="simple">
<li><p>Mircəlal &lt;01youknowme_at_gmail.com&gt;</p></li>
<li><p>Huseyn &lt;huseyn_esgerov_at_mail.ru&gt;</p></li>
<li><p>Sevdimali İsa &lt;sevdimaliisayev_at_mail.ru&gt;</p></li>
<li><p>Jafar &lt;sharifov_at_programmer.net&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Belarusian</p>
<blockquote>
<div><ul class="simple">
<li><p>Viktar Palstsiuk &lt;vipals_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Bulgarian</p>
<blockquote>
<div><ul class="simple">
<li><p>Boyan Kehayov &lt;bkehayov_at_gmail.com&gt;</p></li>
<li><p>Valter Georgiev &lt;blagynchy_at_gmail.com&gt;</p></li>
<li><p>Valentin Mladenov &lt;hudsonvsm_at_gmail.com&gt;</p></li>
<li><p>P &lt;plamen_mbx_at_yahoo.com&gt;</p></li>
<li><p>krasimir &lt;vip_at_krasio-valia.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Catalan</p>
<blockquote>
<div><ul class="simple">
<li><p>josep constanti &lt;jconstanti_at_yahoo.es&gt;</p></li>
<li><p>Xavier Navarro &lt;xvnavarro_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Chinese (China)</p>
<blockquote>
<div><ul class="simple">
<li><p>Vincent Lau &lt;3092849_at_qq.com&gt;</p></li>
<li><p>Zheng Dan &lt;clanboy_at_163.com&gt;</p></li>
<li><p>disorderman &lt;disorderman_at_qq.com&gt;</p></li>
<li><p>Rex Lee &lt;duguying2008_at_gmail.com&gt;</p></li>
<li><p>&lt;fundawang_at_gmail.com&gt;</p></li>
<li><p>popcorner &lt;memoword_at_163.com&gt;</p></li>
<li><p>Yizhou Qiang &lt;qyz.yswy_at_hotmail.com&gt;</p></li>
<li><p>zz &lt;tczzjin_at_gmail.com&gt;</p></li>
<li><p>Terry Weng &lt;wengshiyu_at_gmail.com&gt;</p></li>
<li><p>whh &lt;whhlcj_at_126.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Chinese (Taiwan)</p>
<blockquote>
<div><ul class="simple">
<li><p>Albert Song &lt;albb0920_at_gmail.com&gt;</p></li>
<li><p>Chien Wei Lin &lt;cwlin0416_at_gmail.com&gt;</p></li>
<li><p>Peter Dave Hello &lt;xs910203_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Colognian</p>
<blockquote>
<div><ul class="simple">
<li><p>Purodha &lt;publi_at_web.de&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Czech</p>
<blockquote>
<div><ul class="simple">
<li><p>Aleš Hakl &lt;ales_at_hakl.net&gt;</p></li>
<li><p>Dalibor Straka &lt;dalibor.straka3_at_gmail.com&gt;</p></li>
<li><p>Martin Vidner &lt;martin_at_vidner.net&gt;</p></li>
<li><p>Ondra Šimeček &lt;ondrasek.simecek_at_gmail.com&gt;</p></li>
<li><p>Jan Palider &lt;palider_at_seznam.cz&gt;</p></li>
<li><p>Petr Kateřiňák &lt;petr.katerinak_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Danish</p>
<blockquote>
<div><ul class="simple">
<li><p>Aputsiaĸ Niels Janussen &lt;aj_at_isit.gl&gt;</p></li>
<li><p>Dennis Jakobsen &lt;dennis.jakobsen_at_gmail.com&gt;</p></li>
<li><p>Jonas &lt;jonas.den.smarte_at_gmail.com&gt;</p></li>
<li><p>Claus Svalekjaer &lt;just.my.smtp.server_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Dutch</p>
<blockquote>
<div><ul class="simple">
<li><ol class="upperalpha simple">
<li><p>Voogt &lt;a.voogt_at_hccnet.nl&gt;</p></li>
</ol>
</li>
<li><p>dingo thirteen &lt;dingo13_at_gmail.com&gt;</p></li>
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
<li><p>Dieter Adriaenssens &lt;ruleant_at_users.sourceforge.net&gt;</p></li>
<li><p>Niko Strijbol &lt;strijbol.niko_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>English (United Kingdom)</p>
<blockquote>
<div><ul class="simple">
<li><p>Dries Verschuere &lt;dries.verschuere_at_outlook.com&gt;</p></li>
<li><p>Francisco Rocha &lt;j.francisco.o.rocha_at_zoho.com&gt;</p></li>
<li><p>Marc Delisle &lt;marc_at_infomarc.info&gt;</p></li>
<li><p>Marek Tomaštík &lt;tomastik.m_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Esperanto</p>
<blockquote>
<div><ul class="simple">
<li><p>Eliovir &lt;eliovir_at_gmail.com&gt;</p></li>
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Estonian</p>
<blockquote>
<div><ul class="simple">
<li><p>Kristjan Räts &lt;kristjanrats_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Finnish</p>
<blockquote>
<div><ul class="simple">
<li><p>Juha Remes &lt;jremes_at_outlook.com&gt;</p></li>
<li><p>Lari Oesch &lt;lari_at_oesch.me&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>French</p>
<blockquote>
<div><ul class="simple">
<li><p>Marc Delisle &lt;marc_at_infomarc.info&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Frisian</p>
<blockquote>
<div><ul class="simple">
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Galician</p>
<blockquote>
<div><ul class="simple">
<li><p>Xosé Calvo &lt;xosecalvo_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>German</p>
<blockquote>
<div><ul class="simple">
<li><p>Julian Ladisch &lt;github.com-t3if_at_ladisch.de&gt;</p></li>
<li><p>Jan Erik Zassenhaus &lt;jan.zassenhaus_at_jgerman.de&gt;</p></li>
<li><p>Lasse Goericke &lt;lasse_at_mydom.de&gt;</p></li>
<li><p>Matthias Bluthardt &lt;matthias_at_bluthardt.org&gt;</p></li>
<li><p>Michael Koch &lt;michael.koch_at_enough.de&gt;</p></li>
<li><p>Ann + J.M. &lt;phpMyAdmin_at_ZweiSteinSoft.de&gt;</p></li>
<li><p>&lt;pma_at_sebastianmendel.de&gt;</p></li>
<li><p>Phillip Rohmberger &lt;rohmberger_at_hotmail.de&gt;</p></li>
<li><p>Hauke Henningsen &lt;sqrt_at_entless.org&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Greek</p>
<blockquote>
<div><ul class="simple">
<li><p>Παναγιώτης Παπάζογλου &lt;papaz_p_at_yahoo.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Hebrew</p>
<blockquote>
<div><ul class="simple">
<li><p>Moshe Harush &lt;mmh15_at_windowslive.com&gt;</p></li>
<li><p>Yaron Shahrabani &lt;sh.yaron_at_gmail.com&gt;</p></li>
<li><p>Eyal Visoker &lt;visokereyal_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Hindi</p>
<blockquote>
<div><ul class="simple">
<li><p>Atul Pratap Singh &lt;atulpratapsingh05_at_gmail.com&gt;</p></li>
<li><p>Yogeshwar &lt;charanyogeshwar_at_gmail.com&gt;</p></li>
<li><p>Deven Bansod &lt;devenbansod.bits_at_gmail.com&gt;</p></li>
<li><p>Kushagra Pandey &lt;kushagra4296_at_gmail.com&gt;</p></li>
<li><p>Nisarg Jhaveri &lt;nisargjhaveri_at_gmail.com&gt;</p></li>
<li><p>Roohan Kazi &lt;roohan_cena_at_yahoo.co.in&gt;</p></li>
<li><p>Yugal Pantola &lt;yug.scorpio_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Hungarian</p>
<blockquote>
<div><ul class="simple">
<li><p>Akos Eros &lt;erosakos02_at_gmail.com&gt;</p></li>
<li><p>Dániel Tóth &lt;leedermeister_at_gmail.com&gt;</p></li>
<li><p>Szász Attila &lt;undernetangel_at_gmail.com&gt;</p></li>
<li><p>Balázs Úr &lt;urbalazs_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Indonesian</p>
<blockquote>
<div><ul class="simple">
<li><p>Deky Arifianto &lt;Deky40_at_gmail.com&gt;</p></li>
<li><p>Andika Triwidada &lt;andika_at_gmail.com&gt;</p></li>
<li><p>Dadan Setia &lt;da2n_s_at_yahoo.co.id&gt;</p></li>
<li><p>Dadan Setia &lt;dadan.setia_at_gmail.com&gt;</p></li>
<li><p>Yohanes Edwin &lt;edwin_at_yohanesedwin.com&gt;</p></li>
<li><p>Fadhiil Rachman &lt;fadhiilrachman_at_gmail.com&gt;</p></li>
<li><p>Benny &lt;tarzq28_at_gmail.com&gt;</p></li>
<li><p>Tommy Surbakti &lt;tommy_at_surbakti.net&gt;</p></li>
<li><p>Zufar Fathi Suhardi &lt;zufar.bogor_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Interlingua</p>
<blockquote>
<div><ul class="simple">
<li><p>Giovanni Sora &lt;g.sora_at_tiscali.it&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Italian</p>
<blockquote>
<div><ul class="simple">
<li><p>Francesco Saverio Giacobazzi &lt;francesco.giacobazzi_at_ferrania.it&gt;</p></li>
<li><p>Marco Pozzato &lt;ironpotts_at_gmail.com&gt;</p></li>
<li><p>Stefano Martinelli &lt;stefano.ste.martinelli_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Japanese</p>
<blockquote>
<div><ul class="simple">
<li><p>k725 &lt;alexalex.kobayashi_at_gmail.com&gt;</p></li>
<li><p>Hiroshi Chiyokawa &lt;hiroshi.chiyokawa_at_gmail.com&gt;</p></li>
<li><p>Masahiko HISAKAWA &lt;orzkun_at_ageage.jp&gt;</p></li>
<li><p>worldwideskier &lt;worldwideskier_at_yahoo.co.jp&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Kannada</p>
<blockquote>
<div><ul class="simple">
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
<li><p>Shameem Ahmed A Mulla &lt;shameem.sam_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Korean</p>
<blockquote>
<div><ul class="simple">
<li><p>Bumsoo Kim &lt;bskim45_at_gmail.com&gt;</p></li>
<li><p>Kyeong Su Shin &lt;cdac1234_at_gmail.com&gt;</p></li>
<li><p>Dongyoung Kim &lt;dckyoung_at_gmail.com&gt;</p></li>
<li><p>Myung-han Yu &lt;greatymh_at_gmail.com&gt;</p></li>
<li><p>JongDeok &lt;human.zion_at_gmail.com&gt;</p></li>
<li><p>Yong Kim &lt;kim_at_nhn.com&gt;</p></li>
<li><p>이경준 &lt;kyungjun2_at_gmail.com&gt;</p></li>
<li><p>Seongki Shin &lt;skshin_at_gmail.com&gt;</p></li>
<li><p>Yoon Bum-Jong &lt;virusyoon_at_gmail.com&gt;</p></li>
<li><p>Koo Youngmin &lt;youngminz.kr_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Kurdish Sorani</p>
<blockquote>
<div><ul class="simple">
<li><p>Alan Hilal &lt;alan.hilal94_at_gmail.com&gt;</p></li>
<li><p>Aso Naderi &lt;aso.naderi_at_gmail.com&gt;</p></li>
<li><p>muhammad &lt;esy_vb_at_yahoo.com&gt;</p></li>
<li><p>Zrng Abdulla &lt;zhyarabdulla94_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Latvian</p>
<blockquote>
<div><ul class="simple">
<li><p>Latvian TV &lt;dnighttv_at_gmail.com&gt;</p></li>
<li><p>Edgars Neimanis &lt;edgarsneims5092_at_inbox.lv&gt;</p></li>
<li><p>Ukko &lt;perkontevs_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Limburgish</p>
<blockquote>
<div><ul class="simple">
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Lithuanian</p>
<blockquote>
<div><ul class="simple">
<li><p>Vytautas Motuzas &lt;v.motuzas_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Malay</p>
<blockquote>
<div><ul class="simple">
<li><p>Amir Hamzah &lt;amir.overlord666_at_gmail.com&gt;</p></li>
<li><p>diprofinfiniti &lt;anonynuine-999_at_yahoo.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Nepali</p>
<blockquote>
<div><ul class="simple">
<li><p>Nabin Ghimire &lt;nnabinn_at_hotmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Norwegian Bokmål</p>
<blockquote>
<div><ul class="simple">
<li><p>Børge Holm-Wennberg &lt;borge947_at_gmail.com&gt;</p></li>
<li><p>Tor Stokkan &lt;danorse_at_gmail.com&gt;</p></li>
<li><p>Espen Frøyshov &lt;efroys_at_gmail.com&gt;</p></li>
<li><p>Kurt Eilertsen &lt;kurt_at_kheds.com&gt;</p></li>
<li><p>Christoffer Haugom &lt;ph3n1x.nobody_at_gmail.com&gt;</p></li>
<li><p>Sebastian &lt;sebastian_at_sgundersen.com&gt;</p></li>
<li><p>Tomas &lt;tomas_at_tomasruud.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Persian</p>
<blockquote>
<div><ul class="simple">
<li><p>ashkan shirian &lt;ashkan.shirian_at_gmail.com&gt;</p></li>
<li><p>HM &lt;goodlinuxuser_at_chmail.ir&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Polish</p>
<blockquote>
<div><ul class="simple">
<li><p>Andrzej &lt;andrzej_at_kynu.pl&gt;</p></li>
<li><p>Przemo &lt;info_at_opsbielany.waw.pl&gt;</p></li>
<li><p>Krystian Biesaga &lt;krystian4842_at_gmail.com&gt;</p></li>
<li><p>Maciej Gryniuk &lt;maciejka45_at_gmail.com&gt;</p></li>
<li><p>Michał VonFlynee &lt;vonflynee_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Portuguese</p>
<blockquote>
<div><ul class="simple">
<li><p>Alexandre Badalo &lt;alexandre.badalo_at_sapo.pt&gt;</p></li>
<li><p>João Rodrigues &lt;geral_at_jonilive.com&gt;</p></li>
<li><p>Pedro Ribeiro &lt;p.m42.ribeiro_at_gmail.com&gt;</p></li>
<li><p>Sandro Amaral &lt;sandro123iv_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Portuguese (Brazil)</p>
<blockquote>
<div><ul class="simple">
<li><p>Alex Rohleder &lt;alexrohleder96_at_outlook.com&gt;</p></li>
<li><p>bruno mendax &lt;brunomendax_at_gmail.com&gt;</p></li>
<li><p>Danilo GUia &lt;danilo.eng_at_globomail.com&gt;</p></li>
<li><p>Douglas Rafael Morais Kollar &lt;douglas.kollar_at_pg.df.gov.br&gt;</p></li>
<li><p>Douglas Eccker &lt;douglaseccker_at_hotmail.com&gt;</p></li>
<li><p>Ed Jr &lt;edjacobjunior_at_gmail.com&gt;</p></li>
<li><p>Guilherme Souza Silva &lt;g.szsilva_at_gmail.com&gt;</p></li>
<li><p>Guilherme Seibt &lt;gui_at_webseibt.net&gt;</p></li>
<li><p>Helder Santana &lt;helder.bs.santana_at_gmail.com&gt;</p></li>
<li><p>Junior Zancan &lt;jrzancan_at_hotmail.com&gt;</p></li>
<li><p>Luis &lt;luis.eduardo.braschi_at_outlook.com&gt;</p></li>
<li><p>Marcos Algeri &lt;malgeri_at_gmail.com&gt;</p></li>
<li><p>Marc Delisle &lt;marc_at_infomarc.info&gt;</p></li>
<li><p>Renato Rodrigues de Lima Júnio &lt;renatomdd_at_yahoo.com.br&gt;</p></li>
<li><p>Thiago Casotti &lt;thiago.casotti_at_uol.com.br&gt;</p></li>
<li><p>Victor Laureano &lt;victor.laureano_at_gmail.com&gt;</p></li>
<li><p>Vinícius Araújo &lt;vinipitta_at_gmail.com&gt;</p></li>
<li><p>Washington Bruno Rodrigues Cav &lt;washingtonbruno_at_msn.com&gt;</p></li>
<li><p>Yan Gabriel &lt;yansilvagabriel_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Punjabi</p>
<blockquote>
<div><ul class="simple">
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Romanian</p>
<blockquote>
<div><ul class="simple">
<li><p>Alex &lt;amihaita_at_yahoo.com&gt;</p></li>
<li><p>Costel Cocerhan &lt;costa1988sv_at_gmail.com&gt;</p></li>
<li><p>Ion Adrian-Ionut &lt;john_at_panevo.ro&gt;</p></li>
<li><p>Raul Molnar &lt;molnar.raul_at_wservices.eu&gt;</p></li>
<li><p>Deleted User &lt;noreply_at_weblate.org&gt;</p></li>
<li><p>Stefan Murariu &lt;stefan.murariu_at_yahoo.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Russian</p>
<blockquote>
<div><ul class="simple">
<li><p>Andrey Aleksanyants &lt;aaleksanyants_at_yahoo.com&gt;</p></li>
<li><p>&lt;ddrmoscow_at_gmail.com&gt;</p></li>
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
<li><p>Хомутов Иван Сергеевич &lt;khomutov.ivan_at_mail.ru&gt;</p></li>
<li><p>Alexey Rubinov &lt;orion1979_at_yandex.ru&gt;</p></li>
<li><p>Олег Карпов &lt;salvadoporjc_at_gmail.com&gt;</p></li>
<li><p>Egorov Artyom &lt;unlucky_at_inbox.ru&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Serbian</p>
<blockquote>
<div><ul class="simple">
<li><p>Smart Kid &lt;kidsmart33_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Sinhala</p>
<blockquote>
<div><ul class="simple">
<li><p>Madhura Jayaratne &lt;madhura.cj_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Slovak</p>
<blockquote>
<div><ul class="simple">
<li><p>Martin Lacina &lt;martin_at_whistler.sk&gt;</p></li>
<li><p>Patrik Kollmann &lt;parkourpotex_at_gmail.com&gt;</p></li>
<li><p>Jozef Pistej &lt;pistej2_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Slovenian</p>
<blockquote>
<div><ul class="simple">
<li><p>Domen &lt;mitenem_at_outlook.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Spanish</p>
<blockquote>
<div><ul class="simple">
<li><p>Luis García Sevillano &lt;floss.dev_at_gmail.com&gt;</p></li>
<li><p>Franco &lt;fulanodetal.github1_at_openaliasbox.org&gt;</p></li>
<li><p>Luis Ruiz &lt;luisan00_at_hotmail.com&gt;</p></li>
<li><p>Macofe &lt;macofe.languagetool_at_gmail.com&gt;</p></li>
<li><p>Matías Bellone &lt;matiasbellone+weblate_at_gmail.com&gt;</p></li>
<li><p>Rodrigo A. &lt;ra4_at_openmailbox.org&gt;</p></li>
<li><p>FAMMA TV NOTICIAS MEDIOS DE CO &lt;revistafammatvmusic.oficial_at_gmail.com&gt;</p></li>
<li><p>Ronnie Simon &lt;ronniesimonf_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Swedish</p>
<blockquote>
<div><ul class="simple">
<li><p>Anders Jonsson &lt;anders.jonsson_at_norsjovallen.se&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Tamil</p>
<blockquote>
<div><ul class="simple">
<li><p>கணேஷ் குமார் &lt;GANESHTHEONE_at_gmail.com&gt;</p></li>
<li><p>Achchuthan Yogarajah &lt;achch1990_at_gmail.com&gt;</p></li>
<li><p>Rifthy Ahmed &lt;rifthy456_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Thai</p>
<blockquote>
<div><ul class="simple">
<li><p>&lt;nontawat39_at_gmail.com&gt;</p></li>
<li><p>Somthanat W. &lt;somthanat_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Turkish</p>
<blockquote>
<div><ul class="simple">
<li><p>Burak Yavuz &lt;hitowerdigit_at_hotmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Ukrainian</p>
<blockquote>
<div><ul class="simple">
<li><p>Сергій Педько &lt;nitrotoll_at_gmail.com&gt;</p></li>
<li><p>Igor &lt;vmta_at_yahoo.com&gt;</p></li>
<li><p>Vitaliy Perekupka &lt;vperekupka_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Vietnamese</p>
<blockquote>
<div><ul class="simple">
<li><p>Bao Phan &lt;baophan94_at_icloud.com&gt;</p></li>
<li><p>Xuan Hung &lt;mr.hungdx_at_gmail.com&gt;</p></li>
<li><p>Bao trinh minh &lt;trinhminhbao_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>West Flemish</p>
<blockquote>
<div><ul class="simple">
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
</ul>
</div></blockquote>
</li>
</ul>
</div>
<div class="section" id="documentation-translators">
<h2>Documentation translators<a class="headerlink" href="#documentation-translators" title="Permalink to this headline">¶</a></h2>
<p>Following people have contributed to translation of phpMyAdmin documentation:</p>
<ul>
<li><p>Albanian</p>
<blockquote>
<div><ul class="simple">
<li><p>Arben Çokaj &lt;acokaj_at_shkoder.net&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Arabic</p>
<blockquote>
<div><ul class="simple">
<li><p>Ahmed El Azzabi &lt;ahmedtek1993_at_gmail.com&gt;</p></li>
<li><p>Omar Essam &lt;omar_2412_at_live.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Armenian</p>
<blockquote>
<div><ul class="simple">
<li><p>Andrey Aleksanyants &lt;aaleksanyants_at_yahoo.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Azerbaijani</p>
<blockquote>
<div><ul class="simple">
<li><p>Mircəlal &lt;01youknowme_at_gmail.com&gt;</p></li>
<li><p>Sevdimali İsa &lt;sevdimaliisayev_at_mail.ru&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Catalan</p>
<blockquote>
<div><ul class="simple">
<li><p>josep constanti &lt;jconstanti_at_yahoo.es&gt;</p></li>
<li><p>Joan Montané &lt;joan_at_montane.cat&gt;</p></li>
<li><p>Xavier Navarro &lt;xvnavarro_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Chinese (China)</p>
<blockquote>
<div><ul class="simple">
<li><p>Vincent Lau &lt;3092849_at_qq.com&gt;</p></li>
<li><p>罗攀登 &lt;6375lpd_at_gmail.com&gt;</p></li>
<li><p>disorderman &lt;disorderman_at_qq.com&gt;</p></li>
<li><p>ITXiaoPang &lt;djh1017555_at_126.com&gt;</p></li>
<li><p>tunnel213 &lt;tunnel213_at_aliyun.com&gt;</p></li>
<li><p>Terry Weng &lt;wengshiyu_at_gmail.com&gt;</p></li>
<li><p>whh &lt;whhlcj_at_126.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Chinese (Taiwan)</p>
<blockquote>
<div><ul class="simple">
<li><p>Chien Wei Lin &lt;cwlin0416_at_gmail.com&gt;</p></li>
<li><p>Peter Dave Hello &lt;xs910203_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Czech</p>
<blockquote>
<div><ul class="simple">
<li><p>Aleš Hakl &lt;ales_at_hakl.net&gt;</p></li>
<li><p>Michal Čihař &lt;michal_at_cihar.com&gt;</p></li>
<li><p>Jan Palider &lt;palider_at_seznam.cz&gt;</p></li>
<li><p>Petr Kateřiňák &lt;petr.katerinak_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Danish</p>
<blockquote>
<div><ul class="simple">
<li><p>Aputsiaĸ Niels Janussen &lt;aj_at_isit.gl&gt;</p></li>
<li><p>Claus Svalekjaer &lt;just.my.smtp.server_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Dutch</p>
<blockquote>
<div><ul class="simple">
<li><ol class="upperalpha simple">
<li><p>Voogt &lt;a.voogt_at_hccnet.nl&gt;</p></li>
</ol>
</li>
<li><p>dingo thirteen &lt;dingo13_at_gmail.com&gt;</p></li>
<li><p>Dries Verschuere &lt;dries.verschuere_at_outlook.com&gt;</p></li>
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
<li><p>Stefan Koolen &lt;nast3zz_at_gmail.com&gt;</p></li>
<li><p>Ray Borggreve &lt;ray_at_datahuis.net&gt;</p></li>
<li><p>Dieter Adriaenssens &lt;ruleant_at_users.sourceforge.net&gt;</p></li>
<li><p>Tom Hofman &lt;tom.hofman_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Estonian</p>
<blockquote>
<div><ul class="simple">
<li><p>Kristjan Räts &lt;kristjanrats_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Finnish</p>
<blockquote>
<div><ul class="simple">
<li><p>Juha &lt;jremes_at_outlook.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>French</p>
<blockquote>
<div><ul class="simple">
<li><p>Cédric Corazza &lt;cedric.corazza_at_wanadoo.fr&gt;</p></li>
<li><p>Étienne Gilli &lt;etienne.gilli_at_gmail.com&gt;</p></li>
<li><p>Marc Delisle &lt;marc_at_infomarc.info&gt;</p></li>
<li><p>Donavan_Martin &lt;mart.donavan_at_hotmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Frisian</p>
<blockquote>
<div><ul class="simple">
<li><p>Robin van der Vliet &lt;info_at_robinvandervliet.nl&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Galician</p>
<blockquote>
<div><ul class="simple">
<li><p>Xosé Calvo &lt;xosecalvo_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>German</p>
<blockquote>
<div><ul class="simple">
<li><p>Daniel &lt;d.gnauk89_at_googlemail.com&gt;</p></li>
<li><p>JH M &lt;janhenrikm_at_yahoo.de&gt;</p></li>
<li><p>Lasse Goericke &lt;lasse_at_mydom.de&gt;</p></li>
<li><p>Michael Koch &lt;michael.koch_at_enough.de&gt;</p></li>
<li><p>Ann + J.M. &lt;phpMyAdmin_at_ZweiSteinSoft.de&gt;</p></li>
<li><p>Niemand Jedermann &lt;predatorix_at_web.de&gt;</p></li>
<li><p>Phillip Rohmberger &lt;rohmberger_at_hotmail.de&gt;</p></li>
<li><p>Hauke Henningsen &lt;sqrt_at_entless.org&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Greek</p>
<blockquote>
<div><ul class="simple">
<li><p>Παναγιώτης Παπάζογλου &lt;papaz_p_at_yahoo.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Hungarian</p>
<blockquote>
<div><ul class="simple">
<li><p>Balázs Úr &lt;urbalazs_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Italian</p>
<blockquote>
<div><ul class="simple">
<li><p>Francesco Saverio Giacobazzi &lt;francesco.giacobazzi_at_ferrania.it&gt;</p></li>
<li><p>Marco Pozzato &lt;ironpotts_at_gmail.com&gt;</p></li>
<li><p>Stefano Martinelli &lt;stefano.ste.martinelli_at_gmail.com&gt;</p></li>
<li><p>TWS &lt;tablettws_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Japanese</p>
<blockquote>
<div><ul class="simple">
<li><p>Eshin Kunishima &lt;ek_at_luna.miko.im&gt;</p></li>
<li><p>Hiroshi Chiyokawa &lt;hiroshi.chiyokawa_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Lithuanian</p>
<blockquote>
<div><ul class="simple">
<li><p>Jur Kis &lt;atvejis_at_gmail.com&gt;</p></li>
<li><p>Dovydas &lt;dovy.buz_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Norwegian Bokmål</p>
<blockquote>
<div><ul class="simple">
<li><p>Tor Stokkan &lt;danorse_at_gmail.com&gt;</p></li>
<li><p>Kurt Eilertsen &lt;kurt_at_kheds.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Portuguese (Brazil)</p>
<blockquote>
<div><ul class="simple">
<li><p>Alexandre Moretti &lt;alemoretti2010_at_hotmail.com&gt;</p></li>
<li><p>Douglas Rafael Morais Kollar &lt;douglas.kollar_at_pg.df.gov.br&gt;</p></li>
<li><p>Guilherme Seibt &lt;gui_at_webseibt.net&gt;</p></li>
<li><p>Helder Santana &lt;helder.bs.santana_at_gmail.com&gt;</p></li>
<li><p>Michal Čihař &lt;michal_at_cihar.com&gt;</p></li>
<li><p>Michel Souza &lt;michel.ekio_at_gmail.com&gt;</p></li>
<li><p>Danilo Azevedo &lt;mrdaniloazevedo_at_gmail.com&gt;</p></li>
<li><p>Thiago Casotti &lt;thiago.casotti_at_uol.com.br&gt;</p></li>
<li><p>Vinícius Araújo &lt;vinipitta_at_gmail.com&gt;</p></li>
<li><p>Yan Gabriel &lt;yansilvagabriel_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Slovak</p>
<blockquote>
<div><ul class="simple">
<li><p>Martin Lacina &lt;martin_at_whistler.sk&gt;</p></li>
<li><p>Michal Čihař &lt;michal_at_cihar.com&gt;</p></li>
<li><p>Jozef Pistej &lt;pistej2_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Slovenian</p>
<blockquote>
<div><ul class="simple">
<li><p>Domen &lt;mitenem_at_outlook.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Spanish</p>
<blockquote>
<div><ul class="simple">
<li><p>Luis García Sevillano &lt;floss.dev_at_gmail.com&gt;</p></li>
<li><p>Franco &lt;fulanodetal.github1_at_openaliasbox.org&gt;</p></li>
<li><p>Matías Bellone &lt;matiasbellone+weblate_at_gmail.com&gt;</p></li>
<li><p>Ronnie Simon &lt;ronniesimonf_at_gmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
<li><p>Turkish</p>
<blockquote>
<div><ul class="simple">
<li><p>Burak Yavuz &lt;hitowerdigit_at_hotmail.com&gt;</p></li>
</ul>
</div></blockquote>
</li>
</ul>
</div>
<div class="section" id="original-credits-of-version-2-1-0">
<h2>Original Credits of Version 2.1.0<a class="headerlink" href="#original-credits-of-version-2-1-0" title="Permalink to this headline">¶</a></h2>
<p>This work is based on Peter Kuppelwieser’s MySQL-Webadmin. It was his
idea to create a web-based interface to MySQL using PHP3. Although I
have not used any of his source-code, there are some concepts I’ve
borrowed from him. phpMyAdmin was created because Peter told me he
wasn’t going to further develop his (great) tool.</p>
<p>Thanks go to</p>
<ul class="simple">
<li><p>Amalesh Kempf &lt;ak-lsml_at_living-source.com&gt; who contributed the
code for the check when dropping a table or database. He also
suggested that you should be able to specify the primary key on
tbl_create.php3. To version 1.1.1 he contributed the ldi_*.php3-set
(Import text-files) as well as a bug-report. Plus many smaller
improvements.</p></li>
<li><p>Jan Legenhausen &lt;jan_at_nrw.net&gt;: He made many of the changes that
were introduced in 1.3.0 (including quite significant ones like the
authentication). For 1.4.1 he enhanced the table-dump feature. Plus
bug-fixes and help.</p></li>
<li><p>Marc Delisle &lt;DelislMa_at_CollegeSherbrooke.qc.ca&gt; made phpMyAdmin
language-independent by outsourcing the strings to a separate file. He
also contributed the French translation.</p></li>
<li><p>Alexandr Bravo &lt;abravo_at_hq.admiral.ru&gt; who contributed
tbl_select.php3, a feature to display only some columns from a table.</p></li>
<li><p>Chris Jackson &lt;chrisj_at_ctel.net&gt; added support for MySQL functions
in tbl_change.php3. He also added the “Query by Example” feature in
2.0.</p></li>
<li><p>Dave Walton &lt;walton_at_nordicdms.com&gt; added support for multiple
servers and is a regular contributor for bug-fixes.</p></li>
<li><p>Gabriel Ash &lt;ga244_at_is8.nyu.edu&gt; contributed the random access
features for 2.0.6.</p></li>
</ul>
<p>The following people have contributed minor changes, enhancements,
bugfixes or support for a new language:</p>
<p>Jim Kraai, Jordi Bruguera, Miquel Obrador, Geert Lund, Thomas
Kleemann, Alexander Leidinger, Kiko Albiol, Daniel C. Chao, Pavel
Piankov, Sascha Kettler, Joe Pruett, Renato Lins, Mark Kronsbein,
Jannis Hermanns, G. Wieggers.</p>
<p>And thanks to everyone else who sent me email with suggestions, bug-
reports and or just some feedback.</p>
</div>
</div>


            <div class="clearer"></div>
          </div>
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
  <h3><a href="index.html">Table of Contents</a></h3>
  <ul>
<li><a class="reference internal" href="#">Credits</a><ul>
<li><a class="reference internal" href="#credits-in-chronological-order">Credits, in chronological order</a></li>
<li><a class="reference internal" href="#translators">Translators</a></li>
<li><a class="reference internal" href="#documentation-translators">Documentation translators</a></li>
<li><a class="reference internal" href="#original-credits-of-version-2-1-0">Original Credits of Version 2.1.0</a></li>
</ul>
</li>
</ul>

  <h4>Previous topic</h4>
  <p class="topless"><a href="copyright.html"
                        title="previous chapter">Copyright</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="glossary.html"
                        title="next chapter">Glossary</a></p>
  <div role="note" aria-label="source link">
    <h3>This Page</h3>
    <ul class="this-page-menu">
      <li><a href="_sources/credits.rst.txt"
            rel="nofollow">Show Source</a></li>
    </ul>
   </div>
<div id="searchbox" style="display: none" role="search">
  <h3 id="searchlabel">Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" aria-labelledby="searchlabel" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script>$('#searchbox').show(0);</script>
        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="related" role="navigation" aria-label="related navigation">
      <h3>Navigation</h3>
      <ul>
        <li class="right" style="margin-right: 10px">
          <a href="genindex.html" title="General Index"
             >index</a></li>
        <li class="right" >
          <a href="glossary.html" title="Glossary"
             >next</a> |</li>
        <li class="right" >
          <a href="copyright.html" title="Copyright"
             >previous</a> |</li>
        <li class="nav-item nav-item-0"><a href="index.html">phpMyAdmin 5.2.1 documentation</a> &#187;</li>
        <li class="nav-item nav-item-this"><a href="">Credits</a></li> 
      </ul>
    </div>
    <div class="footer" role="contentinfo">
        &#169; <a href="copyright.html">Copyright</a> 2012 - 2021, The phpMyAdmin devel team.
      Created using <a href="https://www.sphinx-doc.org/">Sphinx</a> 3.4.3.
    </div>
  </body>
</html>