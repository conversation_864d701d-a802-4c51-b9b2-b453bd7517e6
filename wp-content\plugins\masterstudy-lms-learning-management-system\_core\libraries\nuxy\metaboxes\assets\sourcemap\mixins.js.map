{"version": 3, "names": [], "mappings": "", "sources": ["mixins.js"], "sourcesContent": ["var wpcfto_get_image_mixin = {\n    data: function () {\n        return {\n            image_url: '',\n        }\n    },\n    created() {\n        if (this.$options.propsData.fields.type === 'image') {\n            this.get_image_url(this.$options.propsData.field_value);\n        }\n    },\n    methods: {\n        get_image_url(image_id) {\n            this.$http.get(stm_wpcfto_ajaxurl + '?action=wpcfto_get_image_url&nonce=' + stm_wpcfto_nonces['get_image_url'] + '&image_id=' + image_id).then(function (response) {\n                this.image_url = response.body;\n            });\n        },\n        wpcfto_checkURL(url) {\n            return (url.match(/\\.(jpeg|jpg|gif|png)$/) != null);\n        }\n    }\n};\n\nfunction WpcftoIsJsonString(str) {\n    try {\n        JSON.parse(str);\n    } catch (e) {\n        return false;\n    }\n    return true;\n}"], "file": "../js/mixins.js"}