## 4.4.29
- **Fix:** Added a fallback to handle missing "position" values.
## 4.4.28
- **Fix:** Fixed script error in WordPress Customizer.
## 4.4.27
- **Fix:** Add string translate for options.
## 4.4.26
- **Fix:** Fixed minor bug.
## 4.4.25
- **Fix:** Fixed minor bug.
## 4.4.24
- **Fix:** Fixed repeater field props.
## 4.4.23
- **Fix:** Added text variable for image upload button.
## 4.4.22
- **Fix:** Translation support for settings.
## 4.4.21
- **Fix:** Fixed minor bugs.
## 4.4.20
- **New:** Google fonts are uploaded on the server.
## 4.4.19
- **Fix:** Coding improvement.
## 4.4.18
- **Fix:** Added processing unslash to variable $_POST for field type 'editor' & 'curriculum'.
## 4.4.17
- **Fix:** Fixed minor bugs.
## 4.4.16
- **New:** Preview button in new field "data_select"
## 4.4.15
- **New:** Added new field "data_select"
## 4.4.14
- **Fix:** Frontend Enqueues removed
## 4.4.13
- **Fix:** Using Quotes in Label issues fixed
## 4.4.12
- **Fix:** Fixed missing authorization for better data protection.
## 4.4.11
- **Add:** Ukraine language translation file
## 4.4.10
- **Fix:** init hooks changed to admin_init (metaboxes/metabox.php, settings/settings.php, taxonomy_meta/metaboxes.php)
## 4.4.9
- **Fix:** Time component fallback is fixed
## 4.4.8
- **Fix:** Fixed minor bugs.
## 4.4.7
- **New:** Added Submenu URL handler to enable navigation submenus.
## 4.4.6
- **Fix:** Changed the text for the read-only option in the text field type from 'Copy' to 'Copied'.
## 4.4.5
- **Fix:** All shortcodes with text field type are copied after being clicked on.
## 4.4.4
- **Fix:** Fixed minor bugs.
## 4.4.3
- **New:** Added 'field_readonly' option to fields.
## 4.4.2
- **Fix:** Fixed safe_style_css filter with 1 argument.
## 4.4.1
- **New:** Added nonce to metaboxes to improve its security (CSRF)
## 4.4.0
- **New:** Added 'field_disabled' option to fields.
## 4.3.9
- **New:** Added 'disable' option to fields.
## 4.3.8
- **Fix:** Fixed sanitize for curriculum fields.
## 4.3.7
- **New:** Added framework version constant
## 4.3.6
- **New:** Color Picker buttons added to Advanced HTML Editor
## 4.3.5
- **New:** Added Trumbowyg HTML Editor
## 4.3.4
- **New:** Add disabled attribute for select element.
## 4.3.3
- **Fix:** Nuxy search has been removed.
## 4.3.2
- **Fix:** Icons adding issue with Icon Picker field type.
## 4.3.1
- **Fix:** Strings translation issue.
## 4.3.0
- **New:** Added save settings alert
## 4.2.9
- **Fix:** Added filter for data settings.
## 4.2.8
- **Fix:** Button List field improvement.
## 4.2.7
- **Fix:** Strings translation issue.
## 4.2.6
- **Fix:** Sortable field improvement.
## 4.2.5
- **Fix:** Sortable field identification improvement.
## 4.2.4
- **Fix:** Sortable field improvement.
## 4.2.3
- **Fix:** Bug with translations in settings tabs in metaboxes.

## 4.2.2
- **Fix:** Bug with remaining highlights for every visited settings tabs in metaboxes.

## 4.2.1
- **Fix:** Access to field types with 'pre_open' option was fixed.

## 4.2.0
- **New:** Added Source Map for external libraries.

## 4.1.9
- **New:** Added Button list type.

## 4.1.8
- **New:** Added 'Default' status on Text Align parameter on Typography settings.

## 4.1.7
- **Fix:** File upload issue

## 4.1.6
- **Fix:** Fixed minor bugs.

## 4.1.5
- **New:** Added extra styles to synchronize the displaying of tags in Vue (WordPress backend) and TinyMCE editor in the frontend description.
- **New:** Added new function, which allows to exclude the substantial tags from clearing, while saving the data on WordPress backend admin panel.
- **Fix:** Bug with removing the Repeater fields process was fixed.

## 4.1.4
- **New:** Background validation of input fields when submitting from the WordPress backend builder.

## 4.1.3
- **FIX:** Security update.

## 4.1.2
- **FIX:** Strings translation issue with non-Latin languages.

## 4.1.1
- **NEW:** RTL compatibility.

## 4.1
- **ADDED:** Message Notification module.
