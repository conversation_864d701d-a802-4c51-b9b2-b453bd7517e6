@media (max-width: 1440px) {
    .elementor{
        &.elementor-302{
            .elementor-element{
                &.elementor-element-8a6d5c3 {
                    padding: 0 30px;
                }
            }            
        }         
    }    
}

/*-------------------------------------------
    Price
-------------------------------------------*/
// style overide woo
.cart2{    
    text-align:center;
    .count{
        font-size:12px;
        text-align:center;
        display:block;
    }
}
.pp_gallery{
    ul{
        height: auto;
        a{
            height: auto;
        }
    }
} 

.woocommerce,.woocommerce-page{
    .woocommerce-ordering{
        margin-bottom: 0;
        select {
            margin-bottom: 0;
        }
    }     
    #review_form{
        #respond p{
            margin: 20px 0 0 0;
            line-height: normal;
            width: 100%;
            padding: 0;
            display: inline-block;            
            &.form-submit{
                margin-bottom: 0;
            }
        }
    }    

    div.product{
        form.cart{
            .group_table{
                td{
                    &.woocommerce-grouped-product-list-item__label {
                        padding-right: 20px;
                        padding-left: 20px;
                    }
                    &.woocommerce-grouped-product-list-item__price{
                        color: $product-price-color;
                        font-weight: 600;
                        font-family: $headings-font-family;
                        del{
                            color: $product-price-old-color;
                        }
                    }
                }                                
            }             

            div.quantity {
                border: 0;
                position: relative;
                height: 56px;
                overflow: visible;
                padding: 0;          
                width: 122px;
                border: 1px solid $border-input-form;
                @include rtl-margin(0, 30px, 0, 0);                
                @include border-radius(5px);   
                @include box-shadow(0px 1px 3px 0px rgba(0, 0, 0, 0.09));
                .minus,.plus{
                    border: 0;
                    color: transparent;
                    background-color: transparent;                    
                    background-image: url("../images/select.png");
                    background-size: 8px;
                    background-color: transparent;
                    background-position: 50% 5px;
                    background-repeat: no-repeat;
                    @include border-radius(0);                                         
                    @include size(30px,24px);
                }
                .plus{
                    bottom: 2px;
                    top: auto;
                    outline: none;
                    @include rtl-right(2px);                    
                }
                .minus{
                    top: 2px;
                    bottom: auto;
                    outline: none;
                    @include rtl-right(2px);
                    @include rotate(180deg);                    
                }
                input.qty{                
                    border: 0;
                    padding: 10px 20px;
                    font-weight: 400;           
                    width: 80px;
                    height: 100%;
                    outline: none;
                    @include border-radius(0);                             
                    @include box-shadow(none);
                }
            }
        }         
    }  
    .woocommerce-tabs{
        .tab-content{
            .woocommerce-noreviews{
                margin-bottom: 10px;
            }
        }
    }  
    .woocommerce-result-count {
        margin: 0 0 25px 0;
        padding: 12px 0;
        color: $input-color;
    }
    table{
        &.shop_attributes{
            margin-bottom: 0;
            border: 1px solid $border-color;
            th{
                font-size: 18px;           
                font-weight: normal;
                text-transform: none;
                width: 30%;
                padding: 18px;
                font-weight: 600;
                color: $headings-color;
                font-family: $headings-font-family;
                background-color:$white !important;
                border: 1px solid $border-color;
            }
            td{
                padding:18px;
                background-color:$white !important;
                border: 1px solid $border-color;
                p{
                    padding:0;
                }
            }
        }
    }
} 

.woocommerce-notices-wrapper{
    outline: none;
}

.woocommerce div.product form.cart .variations select{
    margin-bottom: 0;
}
.woocommerce #respond input#submit.loading, .woocommerce a.button.loading, .woocommerce button.button.loading, .woocommerce input.button.loading{
    @include opacity(1);
    padding-right:$padding-base-horizontal;
    &:after{        
        color: $white;
        margin: -7px;
        background: transparent;
        z-index:9;        
        font-size:14px;       
        position: absolute; 
        left: 50%;
        top: 50%;
        @include square(14px);        
    }
    &:before{
        @include opacity(0);
        z-index:8;
        position:absolute;
        top:-1px;
        left:-1px;
        background:$white;
        @include size(calc(100% + 2px),calc(100% + 2px));
        content:'';
    }
}
.woocommerce div.product div.images .woocommerce-product-gallery__trigger{
    border:1px solid $theme-color;
    background:$theme-color;
    @include transition(all 0.2s ease-in-out 0s);
    &:hover,&:active{
        background:darken($theme-color,5%);
    }
    &:before{
        border-color:$white;
    }
    &:after{
        background:$white;
    }
}
@media(min-width:1200px) {
    .woocommerce div.product div.images .flex-control-thumbs li{
        width: 33.33%;
        &:nth-child(3n + 1) {
            clear: left;
        }
    }
}
.woocommerce div.product div.images .flex-control-thumbs{
    margin-left: -10px;
    margin-right: -10px;
    margin-top: 20px;
    li{
        padding-right:10px;
        padding-left:10px;
        margin-bottom: 20px;
        img{
            border:1px solid $white;
            @include opacity(0.8);
            @include transition(all 0.2s ease-in-out 0s);
            &:hover,
            &:active,
            &.flex-active{
                border-color:$theme-color;
            }
        }
    }
}
.shop-pagination{
    .apus-pagination{
        margin:0;
        @include rtl-float-left();
    }
    .woocommerce-result-count{
        @include rtl-float-right();
        margin:5px 0 0; 
    }
}
.woocommerce div.product form.cart .variations{
    outline: none;
}
table.variations{
    .tawcvs-swatches .swatch-color{
        @include opacity(1);
        @include size(24px,24px);
        line-height: 24px;
        position:relative;
        border:none;
        @include rtl-margin-right(15px);
        &:before{
            display:none !important;
        }
        &:after{
            content:'';
            @include border-radius(50%);
            z-index:2;
            position:absolute;
            top:-1px;
            left:-1px;
            @include size(26px,26px);
            border:5px solid $white;
        }
        &.selected{
            @include box-shadow(none);
            &:after{
                top:1px;
                left:1px;
                @include size(22px,22px);
                border:3px solid $white;
            }
        }
    }
    .tawcvs-swatches .swatch-label{
        font-size:12px;
        font-weight:400;
        color:$text-color;
        padding:9px;
        display:inline-block;
        line-height:1;
        background:#f2f3f5;
        min-width:30px;
        text-align:center;
        height:auto;
        width:auto;
        border:0 !important;
        @include border-radius(50%);
        @include rtl-margin-right(8px);
        text-transform:uppercase;
        @include opacity(1);
        &.selected{
            @include box-shadow(none);
            background:$theme-color;
            color:$white;
        }
    }
    tr:last-child{

    }
}
.woocommerce div.product form.cart .variations td.label{
    padding:10px 0;
    text-align: inherit;
    display: table-cell;
    vertical-align: middle;
    label{
        margin:0;
    }
}
.woocommerce div.product form.cart.swatches-support .variations td.label{
    vertical-align:top;
}
.woocommerce div.product form.cart .reset_variations{
    color: $brand-danger; 
    i{
        font-size: 12px;
        @include rtl-margin-right(3px);
        color: #e23e1d;
    }
}
.woocommerce div.product p.price ins, .woocommerce div.product span.price ins{
    outline: none;
}
.woocommerce #respond input#submit.added:after, 
.woocommerce a.button.added:after, 
.woocommerce button.button.added:after, 
.woocommerce input.button.added:after{
    display: none;
}
.woocommerce form .form-row input.input-text, 
.woocommerce form .form-row textarea{
    resize: none;
    padding: 10px 20px;
    line-height: $line-height-base;    
    border: 1px solid $border-color;
    background-color: transparent;
    @include transition-all();
    @include box-shadow(none !important);
    @include hover-focus-active() {
        border-color: $border-color !important;
    }    
}
.refund-shop{
    margin-bottom:$theme-margin;
    .btn{
        letter-spacing:1px;
    }
}
.woocommerce form .form-row {
    textarea{
        padding: 20px;
        height: 120px;
        resize: none;
        cursor: pointer;
        @include border-radius(5px);
    }
}
.woocommerce table.wishlist_table thead th{    
    color: $headings-color;
    padding: 20px 0;
    border-bottom: 1px solid $border-color;    
}
.woocommerce .wishlist_table td.product-add-to-cart a{
    @extend .btn;
    @extend .btn-theme;
    @include border-radius(50px);
    display: inline-block !important;
    background-image: none !important;        
}
.woocommerce table.wishlist_table tbody td{
    padding:10px 0;
    text-align: inherit;
    border-width:0 0 1px;
    border-bottom:1px solid $border-color;
    @media(min-width:992px) {
        padding:20px 0;
    }
}
.woocommerce table.wishlist_table tfoot td {
    border:none;
}
.woocommerce table.wishlist_table{
    font-size:$font-size-base;
    .product-name{
        white-space: nowrap;
        padding-right:20px;
        padding-left:20px;
        @media(min-width:992px) {
            padding-right:50px;
            padding-left:50px;
        }
    }
    .media-body{
        width:auto;
    }
    .product-thumbnail{
        a{
            display: block;
            width: 80px;
            @media(min-width: 1200px) {
                width:170px;
            }
        }
    }
}
.select2-container .select2-selection--single .select2-selection__rendered{
    padding-top: 13px;
    padding-bottom: 13px;
    line-height: 22px;
}
.select2-container .select2-selection--single{
    height: 50px;
    background-color: transparent;
    border: 0;
    padding: 0;
    outline: none;
    color: $input-color;
    @include border-radius(5px);    
    @include hover-focus-active() {
        outline: none;
    }
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
    top: 12px;
}

.woocommerce #respond input#submit, .woocommerce a.button,
.woocommerce button.button, .woocommerce input.button{
    color: $white;
    background-color: $theme-color;    
    border-color: $theme-color;
    font-weight: 400;
    font-size: 14px;
    padding: 15px 30px;
    @include border-radius(5px);
    @include transition-all();
    @include hover-focus-active() {
        color: $white;
        background-color: $theme-color;
    }
}

p.cart-empty{
    margin-bottom: 30px;
}
.return-to-shop{
    margin-bottom: 0;
}

.woocommerce .return-to-shop .button ,
.woocommerce .track_order .button {
    @include border-radius(5px);
}

.woocommerce #respond input#submit{    
    color: $white;
    text-transform: capitalize;
    font-family: $font-family-base;
    font-weight: 400;    
    font-size: 15px;
    outline: none;
    cursor: pointer;
    border: none;
    background-color: $theme-color;
    background-image: url('../images/btn-arrow.png');
    background-position: 80% 50%;
    background-repeat: no-repeat;
    @include transition-all();
    @include inline-block();
    @include border-radius(5px);
    @include rtl-padding(20px, 78px, 20px, 40px);
    @include hover-focus-active() {    
        color: $white;    
        background-color: $theme-color;
        background-image: url('../images/btn-arrow.png');
    }
}


.track_order{
    max-width:770px;
    margin:auto;
    padding:$theme-margin / 2;
    background:#f2f3f5;
    @media(min-width:992px) {
        padding:70px;
    }
    .form-row{
        width:100% !important;
        input.input-text{
            padding:5px 20px;
            background:$white !important;
            height:$input-height-base;
        }
        &:last-child{
            margin-bottom:0;
        }
        label{
            font-family: $font-family-second;
            color:$link-color;
        }
    }
}
.woocommerce-message{
    line-height: 2.5;
}
.apus-filter{
    .woocommerce-message{
        display: none;
    }
}
#add_payment_method #payment ul.payment_methods, .woocommerce-cart #payment ul.payment_methods, .woocommerce-checkout #payment ul.payment_methods{
    border:0;
    padding:0;
    li{
        padding:0;
        margin-bottom: 15px;
        .payment_box{
            padding: 20px;            
            margin: 20px 0;
            background-color: $white;            
            @include border-radius(5px);
            @include box-shadow(0px 1px 4px 0px rgba(0, 0, 0, 0.09));
        }
        label{
            font-size: 14px;
            cursor: pointer;
            font-weight: 400;
            display: inline;
        }
        &:last-child{
            margin-bottom: 0;
        }
        .about_paypal{
            margin:0 10px;
            float:none;
        }
    }
}
#add_payment_method #payment ul.payment_methods li input, .woocommerce-cart #payment ul.payment_methods li input, .woocommerce-checkout #payment ul.payment_methods li input{
    @include rtl-margin-right(10px);
}
.woocommerce table.shop_table{
    border:0;
    @include border-radius(0);
    th{
        padding: 25px 0;
        &.product-remove{
            .remove{
                border: 0;
            }
        }
    }
    .reader-text{
        display: none;
    }
    td{
        border:none;
        border-top:1px solid $border-color;
        overflow: hidden;
        padding:20px 0;        
        &.product-price{
            color: $text-color;            
            font-size: 15px;
            padding-left: 0;
            padding-right: 0;
        }        
        .btn{
            border: 0;
            padding: 14px 42px;
            color: $white;
            line-height: inherit;            
            text-transform: none;
            background-color: $theme-color;
            @include border-radius(5px);
            @include hover-focus-active() {
                color: $white !important;
            }
        }
        &.product-thumbnail{
            a{
                padding: 5px;
                @include border-radius(5px);
                @include inline-block();
                border: 2px solid $border-color;                
            }
        }
        &.product-quantity{
            .quantity{
                margin: 0px;
                padding: 0px;
                border: 1px solid $border-color;                
                background-color: $white;
                @include size(88px,52px);
                @include border-radius(5px);
                @include box-shadow(0 2px 2px -2px rgba(0, 0, 0, 0.09));
            }
            input.qty{                
                margin: 0;
                font-weight: 400;
                font-size: 14px;
                border: 0;
                color: $input-color;
                padding: 0px 0px 0 15px;
                font-family: $headings-font-family;
                width: 56px;
                height: 100%;
                @include border-radius(0);                                
                @include appearance(none);
            }
            .plus, .minus{            
                @include box-shadow(none);
                @include border-radius(0);     
                outline: none;
                height: 25px;           
                border-width: 0 0 1px 1px;
                border-style: solid;
                border-color: $border-color;
                @include hover-focus-active() {
                    color: $headings-color;
                    background-color: transparent;
                }
            }
            .minus{
                border-bottom: 0px;
            }
        }
        &.product-subtotal{
            color: $text-color !important;   
            font-size: $font-size-base !important;
        }
    }
    .quantity-wrapper{
        > label{
            display: none;
        }
    }
    .product-remove{
        .remove{
            @include flexbox();
            @include align-items(center);
            @include justify-content(center);
            @include size(30px,30px);
            background-image: url('../images/close-dark.png');
            background-repeat: no-repeat;
            background-position: center center;    
            background-color: transparent;
            @include hover-focus-active() {
                background-color: transparent;
            }    
        }
    }
    tbody{
        .actions{
            p{
                margin: 0;
            }
        }
        .product-subtotal{
            color: $theme-color;
            font-size: 16px;
            font-family:$font-family-three;
        }
        .order-total{
            .woocommerce-Price-amount{
                color: #222;
                font-weight:600;
                font-size: 15px;
            }
        }
        .product-name{
            font-size: 18px;
            font-weight: 400;
            font-family: $headings-font-family;
        }
        .cart-subtotal,
        .order-total{
            .woocommerce-Price-amount{
                font-size: 20px;
                font-weight: 400;
            }
        }
    }
    th{
        border:none;
        font-family:$font-family-three;
        text-transform: uppercase;
        color: $link-color;
        font-size: 16px;
        font-weight: 400;
    }
    .list-bundles{
        font-size:14px;
        list-style:none;
        @include rtl-padding-left(25px);
        strong{
            font-weight:500;
        }
        ul{
            list-style:inside none disc;
            padding:0;
            margin:0;
        }
    }
}

#add_payment_method .wc-proceed-to-checkout, 
.woocommerce-cart .wc-proceed-to-checkout, 
.woocommerce-checkout .wc-proceed-to-checkout{
    .btn{
        text-transform: none;
        border-color: $theme-color-second;
        background-color: $theme-color-second;
        font-size: 18px;
        font-weight: 400;
        padding: 12px 30px;
        font-family: $headings-font-family;
    }
}

#add_payment_method .checkout .col-2 h3#ship-to-different-address, 
.woocommerce-cart .checkout .col-2 h3#ship-to-different-address, 
.woocommerce-checkout .checkout .col-2 h3#ship-to-different-address{
    font-size: 20px;
}

.woocommerce{
    .cart_totals{
        border: 2px solid $border-color;
        @include border-radius(5px);
        padding: 30px;
        > h2{
            margin: 0;
            text-transform: none;        
            font-size: 20px;
            position: relative;        
            padding: 0 0 15px 0;
            font-weight: 600;
            border-bottom: 1px solid $border-color;
        }
        table.shop_table{
            border:none;
            margin:0;
            th,td{
                padding: 20px 0;            
                .woocommerce-Price-amount{
                    color: $text-color;
                    font-size: $font-size-base;
                    font-weight: 400;
                }
            }
            label{
                font-weight:400;
            }
            tr.order-total{
                .amount{
                    font-size: 22px;
                    font-weight: 700;
                    color: $product-price-color;                
                    font-family: $headings-font-family;
                }
            }
            th{
                color: $headings-color;
                font-family: $headings-font-family;
                font-weight: 400;
                font-size: 18px;
                text-transform: none;
            }
        }
        .wc-proceed-to-checkout{
            .btn{
                border: 0;
                @include border-radius(50px);
            }
        }
    }
}

.woocommerce{
    table.cart{
        position: relative;        
        border-width: 0 2px 2px 2px;
        border-style: solid;
        border-color: $border-color;
        @include border-radius-separate(0, 0, 5px, 5px);
        thead{                        
            th{
                background-color: $theme-color;
                &.product-thumbnail{
                    @include border-radius-separate(5px, 0, 0px, 0px);
                }
                &.product-remove{
                    @include border-radius-separate(0, 5px, 0px, 0px);
                }
            }
        }        
        th{
            color: $white;
            text-transform: none;            
            padding: 12px;
            &.product-thumbnail{
                @include rtl-padding-left(30px);
            }
            &.product-remove{
                @include rtl-text-align-right();
                .remove{
                    background-size: 16px;
                    background-image: url('../images/close-icon.png');                    
                }                
            }
        }
        td{
            padding: 30px;
        }
    } 
    .woocommerce-shipping-calculator{
        .select2-container--default{
            .select2-selection--single{
                border: 1px solid $border-color;
                .select2-selection__rendered{
                    @include rtl-padding-left(18px);
                    @include rtl-padding-right(18px);
                }
            }
        }
        .shipping-calculator-button{
            @include inline-block();
            margin-bottom: 30px;
        }
    }  
}

.woocommerce-checkout{
    .woocommerce-info{
        border-top: 0px;
        background: transparent;
        text-align: center;
        margin-bottom: 10px;
        padding-bottom: 0;
        @include rtl-padding-left(0);
        &:before{
            content: none;
        }
        a{
            color: $theme-color-second;
        }
    }
    .details-review{
        border: 2px solid $border-color;
        @include border-radius(5px);
        padding: 30px;
    }
    form.woocommerce-form-coupon{        
        border: 0;
        padding: 0;        
        width: 436px;        
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        @include border-radius(0);   
        p{
            &:first-child{
                margin-bottom: 30px;
            }
        }             
        .button{
            padding: 17px 40px;
            @include border-radius(5px);
        }
        .form-row-first{
            width: 210px;
            .input-text{
                width: 100%;
                padding-left: 20px;
                padding-right: 20px;
            }
        }
    }
}

.woocommerce-table--order-details{
    tfoot .woocommerce-Price-amount{
        font-size: 24px;
    }
}
.woocommerce-error li, .woocommerce-info li, .woocommerce-message li{
    font-weight: 400;
}
#add_payment_method #payment, .woocommerce-cart #payment, .woocommerce-checkout #payment{
    background:$white;
    .place-order{
        padding:30px 0 10px !important;
        #place_order{
            font-size: 18px;
            margin-top: 20px;
            color: $white;
            font-weight: 400;
            text-transform: none;
            border-color: $theme-color-second;
            background: $theme-color-second;
            font-family: $headings-font-family;
            text-transform: capitalize;
        }
    }
}
#add_payment_method #payment div.payment_box, .woocommerce-cart #payment div.payment_box, .woocommerce-checkout #payment div.payment_box{
    background:$white;
}
#add_payment_method #payment div.payment_box::before, .woocommerce-cart #payment div.payment_box::before, .woocommerce-checkout #payment div.payment_box::before{
    border-bottom-color:$white;
    content: none;
}
.woocommerce  #customer_details{
    .woocommerce-shipping-fields,
    .woocommerce-account-fields{
        margin-top: $theme-margin;        
    }
    h3.form-row{
        font-size: 18px;
        font-weight: 400;
        text-transform: capitalize;
        margin: 0; 
        padding:20px 0;
    }
    .shipping_address{
        > *{
            > .select2-hidden-accessible{
                height: 0;
            }
        }
    }
}

label.checkbox{            
    span{
        @include rtl-padding-left(20px);
    }
}

.woocommerce form .woocommerce-billing-fields{
    > h3{
        font-size: 18px;
        font-weight: 400;
        text-transform: capitalize;
        margin: 0; 
        padding:20px 0;
    }
    .select2-container{
        height: 50px;        
        border:1px solid $border-color !important;
        @include border-radius(5px);
    }
    .woocommerce-billing-fields__field-wrapper{
        > *{
            > label{
                font-weight: 400;
            }
            > .select2-container,
            > select,
            > input{
                overflow: hidden;
                width: calc(100% - 200px) !important;
                border-width:0 0 1px;
                border-style:solid;
                border-color:$border-color;
                padding:10px 0;
                @include border-radius(0 !important);
                @include rtl-float-right();
                &:focus{
                    border-color:$theme-color;
                }
            }
            > .select2-hidden-accessible{
                height: 0;
            }
        }
    }
}
.woocommerce .cart-collaterals .cross-sells, .woocommerce-page .cart-collaterals .cross-sells,
.woocommerce .cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals{
    width: 100%;
}

.woocommerce{
    div.product{
        &.first{
            @include rtl-clear-left();
        }
        .product_title{
            font-size: 26px;            
            margin: 0 0 15px 0;
            font-weight: 400;
            line-height: 24px;
        }
    }     
} 

.woocommerce p.stars.selected a.active::before, .woocommerce p.stars:hover a::before,
.woocommerce p.stars.selected a:not(.active):before{
    content: '';
}
.woocommerce div.product p.price, .woocommerce div.product span.price{        
    color: $product-price-color;
    font-weight: $product-price-font-weight;
    font-family: $headings-font-family;
    del{
        color: $product-price-old-color;
    }
}
.woocommerce div.product p.price del, .woocommerce div.product span.price del{
    @include opacity(1);
}
.variations{
    label{
        color: $text-color;
        font-size: 15px;
        text-transform: capitalize;
        font-weight: 400 !important;
        @include rtl-padding-right(5px);
    }
    .value{
        padding: 0;
    }
}
.woocommerce div.product form.cart .group_table{
    border:none;
    margin-bottom: 30px;
    .price{
        del{
            font-size: 12px !important;
        }
    }     
    .price,
    .price ins{
        font-size: 15px !important;
        color: $theme-color;
    }
    label{
        font-weight:500;
    }
    td{
        vertical-align: middle;
        &:first-child{
            padding-right:0;
            @include rtl-text-align-left();
        }
    }
    .quantity{
        .reader-text{
            display:none;
        }
    }
}


.woocommerce #respond input#submit.disabled, 
.woocommerce #respond input#submit:disabled, 
.woocommerce #respond input#submit:disabled[disabled], 
.woocommerce a.button.disabled, 
.woocommerce a.button:disabled, 
.woocommerce a.button:disabled[disabled], 
.woocommerce button.button.disabled, 
.woocommerce button.button:disabled, 
.woocommerce button.button:disabled[disabled], 
.woocommerce input.button.disabled, 
.woocommerce input.button:disabled, 
.woocommerce input.button:disabled[disabled] {
    @include opacity(1);
    pointer-events: none;
}

.woocommerce div.product form.cart .button{            
    color: $white;
    border: 0;    
    text-align: center;    
    font-size: 18px;
    font-weight: 400;    
    position: relative;
    text-transform: capitalize;
    background-color: $theme-color-second;    
    @include rtl-padding(19px, 42px, 19px, 80px);
    @include border-radius(50px);
    @include rtl-float-left();
    @include hover-focus-active() {
        color: $white;
        background-color: $theme-color-second;                
    }       
    &:before{
        color: $white;
        content: "\f101";
        font-family: $icon-font-family;
        display: block;
        font-size: 24px;
        @include rtl-left(42px);
        @include vertical-align(absolute);
    }
}
.woocommerce .details-product .information .stock.out-of-stock{        
    background:#f2f3f5;
    border-color:#f2f3f5;
    color:#cccccc;    
    margin: 10px 0;
    width:100%;
    @include border-radius(50px);
}
.woocommerce div.product form.cart.group_product{
    width:100%;
}
.woocommerce div.product form.cart .group_table .label{
    padding: 0.5em;
    vertical-align: middle;
    font-size:14px;
    display: table-cell;
    text-align: inherit;
    white-space: normal;
    label{
        font-weight: 400;
    }
}
.woocommerce div.product form.cart .variations td{
    line-height: inherit;
    font-size: inherit;
    .tawcvs-swatches{
        padding:0;
    }
    padding:10px 0;
    vertical-align: middle;
}
.woocommerce .order_details{
    padding: 0;
}
.woocommerce table.shop_table{
    input.button:disabled,
    input.button{
        background-color: transparent;
        border: 2px solid $theme-color;
        color: $theme-color;
        text-transform: capitalize;
        padding: 16px 48px;
        font-weight: 400;
        outline: none;
        @include border-radius(5px);    
        @include opacity(1);
        @include transition-all();
        @include hover-focus-active() {
            background: $theme-color;
            color: $white;
            border-color: $theme-color;
        }
    }
}
.woocommerce {
    .woocommerce-message,
    .checkout_coupon{
        .button{
            background-color: $theme-color;            
            padding: 12px 30px;
            border: 0;
            color: $white;
            font-size: 15px;            
            font-weight: $font-weight-base;
            font-family: $font-family-base;
            @include border-radius(50px);
            @include inline-block();          
            @include transition-all();
            @include hover-focus-active() {
                color: $white;
                background-color: $theme-color;
            }  
        }
    }
}
.woocommerce #content table.cart td.actions .input-text, 
.woocommerce table.cart td.actions .input-text, 
.woocommerce-page #content table.cart td.actions .input-text, 
.woocommerce-page table.cart td.actions .input-text{
    width: auto;
    height: 41px;
    padding: 5px 10px !important;
    @include rtl-margin-right(10px !important);
    @include border-radius(2px);
}
#add_payment_method table.cart img, .woocommerce-cart table.cart img, .woocommerce-checkout table.cart img{
    width: 100px;
}
.woocommerce .percent-sale,
.woocommerce span.onsale{
    color:$white;
    font-size: 12px;
    background:$theme-color;
    padding:6px 10px;
    position: absolute;
    text-align: center;        
    text-transform:uppercase;
    top: 30px;
    min-height: auto;
    font-family: $font-family-three;
    z-index: 9;
    @include square(56px);
    @include rtl-left(auto);
    @include rtl-right(30px);
    @include border-radius(50%);    
    @include flexbox();
    @include align-items(center);
    @include justify-content(center);    
}
//popup-cart
.popup-cart{
    .title-count,
    .title-add{
        font-size: 20px;
        margin: 0 0 20px;
    }
    .gr-buttons{
        margin: 50px 0 0;
    }
    .title-add{
        color: $brand-success;
    }
    .image{
        img{
            max-width: 100px;
        }
    }
    .name{
         margin: 30px 0 0;
    }
    .widget-product{
        margin-top: 30px;
    }
}
#apus-cart-modal{
    .btn-close{
        position: absolute;
        top:0;
        @include rtl-right(0);
        z-index: 99;
        background: $white;
        @include size(30px,30px);
        line-height: 26px;
        text-align: center;
        display: inline-block;
    }
    .modal-content{
        background: $white none repeat scroll 0 0;
        min-width: 1000px;
        max-width: 100%;
        margin-top: 50px;             
    }
    .modal-body{
        padding: 60px;
    }
}
.name{    
    font-size: 18px;
    line-height: 26px;
    font-weight: 400;        
}
.product-block{
    position:relative;
    .sale-perc{        
        color: $white;
        font-size: 14px;
        font-weight: 400;
        padding: 0 5px;
        line-height: 1.7;
        position: absolute;                
        top: 12px;
        z-index: 8;
        background-color: #fd5f5c;
        text-transform: uppercase;        
        font-family: $font-family-three;
        @include rtl-left(12px);
    }
    .out-of-stock{
        background: darken(#e1e1e1, 5%);
        color: $white !important;
        font-size: 14px !important;
        font-weight: 400;
        padding: 0 8px;
        position: absolute;
        @include rtl-right(12px);
        text-transform: uppercase;
        font-family: $font-family-second;
        top: 12px;
        z-index: 8;  
    }
    .image{
        position: relative;        
        .downsale{
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            position: absolute;
            right:0;
            top:0;
            z-index: 8;
            padding: 2px 10px;            
            background-color: #d42e2e;
            color: $white;
            @include border-radius(2px);
        }
        img{
            display: inline-block;
            width: 100%;
            @include transition(all 0.5s ease-in-out 0s);
        }
        .image-effect{
            top: 0;
            position: absolute;
            left:50%;            
            z-index: 2;
            @include opacity(0);
            @include translateX(-50%);
        }  
        .image-no-effect{
            @include scale(1);
        } 
    }
    .block-inner{
        &:hover{
            .image{
                .image-hover{
                    @include opacity(0);
                }
                .image-effect{
                    @include opacity(1);
                } 
            }
        }
        &.text-center{
            .image{
                img{
                    margin:auto;
                }
                .image-effect{
                    left:50%;
                    @include translateX(-50%);
                } 
            } 
        }
    }
    .clear{
        display: none !important;
    }
    .product-cats{
        font-size: 12px;
        margin: 15px 0 11px;
        text-transform: uppercase;
        a{
            color: #4c4c4c;
            &:hover,&:active{
                color: $theme-color;
            }
        }
    }
    .rating{
        > *{
            display: inline-block !important;
            vertical-align: middle;
            margin:0 !important;
            float: none;
        }
        .counts{
            color: #999591;
            font-size: 13px;
        }
    }
    .feedback,
    .sub-title{
        display: none;
    }
    .product-image{
        position:relative;
        display:block;
    }
    &:hover{        
        .image .image-no-effect{
            @include scale(1.1);
        }
    }
    // product grid
    &.grid{
        position: relative;
        margin: 0 0 30px 0;
        padding: 20px;        
        background-color: transparent;        
        border: 2px solid $border-color;        
        @include rtl-text-align-left();
        @include transition-all();
        @include border-radius(5px);        
        &.noborder{
            border:0 !important;
        }        
        .name{
            margin: 0 0 3px 0;
        }
        .image{
            &.out{
                .product-image{
                    @include opacity(0.5);
                }
            }
        }
        .groups-button{
            position: absolute;
            bottom: 4px;
            @include rtl-right(0);
            .button,
            .add_to_cart_button{
                &.added{
                    display: none;
                }
            }
        }
        .product-info-left{
            @include rtl-padding-right(60px);
        }
        .product-cat{
            margin: 0 0 8px;
            font-size:12px;
            font-family: $font-family-three;
            letter-spacing: 2px;
            text-transform: uppercase;
            a{
                color: $theme-color;
            }
        }
        .caption{
            padding: 20px 15px 15px;
            text-align: center;
        }
        .block-inner{            
            position:relative;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .title-wrapper{
            position: relative;
        }
        .groups-button {            
            > div{
                outline: none;                
            }
            .add-cart{                
                .added_to_cart,
                .button{                                                            
                    border: 0;                    
                    font-size: 0;   
                    padding: 0;
                    display: block;
                    font-weight: 400;
                    background-color: $theme-color-second !important;
                    pointer-events: none;
                    visibility: hidden;
                    position:relative;                                                            
                    color: $white !important;                        
                    @include square(50px);                    
                    @include transition-all();
                    @include border-radius(50%);
                    @include opacity(0);
                    @include scale(0);
                    &:before{
                        font-size: 23px;
                        font-family: $icon-font-family;
                        content: "\f101";                        
                        @include center-box(absolute);                                                                        
                    }                    
                    &.product_type_grouped,
                    &.product_type_external,
                    &.product_type_variable{
                        &:before{
                            content: "\f107";
                        }
                    }
                }
                .added_to_cart{
                    &:before{
                        color: $white;   
                        content: "\f107";                     
                    }
                }
            }
        }
        .metas{
            padding: 0;
            margin: 0;            
        }
        .swatches-wrapper{
            list-style:none;
            padding:0;
            padding:0 0 10px;
            line-height:1.2;
            position:absolute;
            bottom:0;
            margin:0;
            z-index: 9;
            left:0;
            width:100%;
            @include opacity(0);
            @include transition(all 0.2s ease-in-out 0s);
            @include translateY(10px);
            li{
                display:inline-block;
                vertical-align:middle;
                @include rtl-margin-right(5px);
                &:last-child{
                    @include rtl-margin-right(0);
                }
            }
            .label{
                padding:0;
                font-size:$font-size-base;
                color:$text-color;
                font-weight:500;
            }
            .swatch-color{
                display:inline-block;
                @include size(12px,12px);
                @include border-radius(50%);
            }
        }
        .price{
            display: block;            
            margin: 0 0 3px 0;
            font-size: $product-price-font-size !important;    
        }
        &:not(.grid-deal) {
            &:hover{
                border-color:$white !important;
            }
        }
        &:hover{
            @include box-shadow(0px 0px 30px 0px rgba(32, 32, 32, 0.15));            
            .groups-button{
                .add-cart{
                    .button, .added_to_cart {
                        visibility: visible;
                        @include opacity(1);
                        pointer-events: auto;
                        @include scale(1);
                    }
                }       
            }      
        }        
    }
    // deal
    &.grid-deal{
        border:1px solid $theme-color;
        margin-bottom: 0;
        &:hover{
            @include box-shadow(none);
            .name{
                @include opacity(1);
                @include translateY(0px);
            }
            .price{
                @include translateY(0px);
            }
        }
        &:before{
            display: none;
        }
        .groups-button {
            position:static;
            margin:15px 0 -5px;
            @media(min-width: 1200px) {
                margin:25px 0 -8px;
            }
            > div{
                @include opacity(1);
                @include transform(translate(0,0) scale(1,1));
            }
        }
        .time-wrapper{
            margin-top: 10px;
            @media(min-width: 1200px) {
                margin-top: 20px;
            }
        }
    }
}

.woocommerce{
    .archive-shop{
        .products{
            margin-bottom: 32px;
            &.related{
                margin-bottom: 0;   
                margin-top: 55px;
                .widget-title{
                    margin-bottom: 50px;
                }
            }
        }
    }
}

// products list
.products-list{
    .product-block-list{
        margin:0 0 30px;        
    }
}
.product-block-list{
    padding:15px;
    border:1px solid $border-color;
    overflow: hidden;
    @include transition(all 0.3s ease-in-out 0s);
    @media(min-width: 1200px) {
        padding:30px;
        background:$white;
    }
    .onsale{
        top:0 !important;
        left:0 !important;
    }
    &:hover{
        border-color:$theme-color;
    }    
    .product-cat{
        font-family: $font-family-three;
        text-transform: uppercase;
        letter-spacing: 2px;
        font-size: 12px;
    }
    .name{
        font-family: $font-family-second;
        font-size: 24px;
        margin:0 0 10px;
        font-weight:400;
        @media(min-width: 1200px) {
            font-size: 30px;
        }
    }
    .cate-wrapper{
        margin: 0 0 8px;
        .product-cats{
            margin:0;
        }
    }    
    // action
    .add-cart {
        margin-bottom: 10px;
        margin-top: 10px;
        @media(min-width: 1200px) {
            margin-top: 20px;
        }
        .added{
            display: none !important;
        }
        .wc-forward {
            width: 100%;
        }
        .added_to_cart,
        a.button{
            font-size: 14px;
            font-family:$font-family-three;
            display: inline-block;
            width:100%;
            padding:15px;
            background:$white;
            color: $theme-color;
            text-transform: uppercase;
            text-align: center;
            @include transition(all 0.3s ease-in-out 0s);
            @include border-radius(50px);
            border:1px solid $theme-color;
            line-height: 1;
            &:hover,&:focus{
                color:$white;
                background:$theme-color;
                border-color:$theme-color;
            }
            &.loading{
                &:after{
                    margin-top: -7px;
                }
            }
        }
    }
    .top-list-info{
        position:relative;
    }
    .rating{
        margin-bottom:15px;
        > *{
            display: inline-block;
            float: none;
            vertical-align: text-top;
            line-height: 1;
        }
        .counts{
            @include rtl-margin-left(2px);
        }
    }
    // price
    .price{
        display: block;
        margin-top: 10px;
        @media(min-width: 1200px) {
            margin-top: 20px;
        }
        font-family: $font-family-second;
        font-weight:400;
        font-size: 24px !important;
        color:$theme-color !important;
        margin:0;
        del{
            color: #b7b7b7;
            font-family: $font-family-base;
            font-size:14px !important;
        }
    }
    .avaibility-wrapper{
        margin-bottom: 20px;
        font-size: 14px;
    }
    .bottom-list{
        margin-top:35px;
        > div{
            @include rtl-float-left();
        }
    }
    .flex-middle{
        overflow: hidden;
    }
    .left-infor{
        @include rtl-padding-left(20px);
        position:relative;
        @media(min-width: 1200px) {
            @include rtl-padding-left(30px);
        }
        &:before{
            content:'';
            position:absolute;
            top:-200px;
            @include rtl-left(0);
            @include size(1px,1000px);
            background:$border-color;
        }
    }
    .bottom-list{
        > *{
            display:inline-block;
            vertical-align:top;
        }
    }
    .wrapper-image{
        position:relative;        
        .swatches-wrapper{
            z-index:8;
            list-style:none;
            padding:0;
            margin:0;
            line-height:1.2;
            text-align:center;
            position:absolute;
            left:0;
            width:100%;
            bottom:20px;
            li{
                display:inline-block;
                vertical-align:middle;
                @include rtl-margin-right(5px);
                &:last-child{
                    @include rtl-margin-right(0);
                }
            }
            .label{
                padding:5px 8px;
                font-size:$font-size-base;
                color:$text-color;
                font-weight:500;
                display:inline-block;
                background:$white;
                @include border-radius(0);
            }
            .swatch-color{
                display:inline-block;
                @include size(12px,12px);
                @include border-radius(50%);
            }
        }        
    }
}
// list small
.shop-list-small{
    @include transition(all 0.2s ease-in-out 0s);
    border:1px solid $border-color;
    padding:10px;
    @media(min-width: 1200px) {
        padding:30px;
    }
    .content-left{
        width: 110px;
        @include rtl-base-toprightbottomleft(padding,0,15px,0,0);
        @include rtl-float-left();
    }
    .content-body{
        overflow: hidden;
        width: calc(100% - 110px);
    }
    .name{
        margin:0 0 2px;
        font-size: 16px;
        font-family: $font-family-second;
    }
    &:hover{
        border-color:$theme-color;
        z-index: 2;
    }
}
.shop-list-normal{
    margin-bottom:20px;
    .content-left{
        @include transition(all 0.2s ease-in-out 0s);
        width:90px;
        @include rtl-float-left();
        border:1px solid $border-color;
        padding:5px;
        @media(min-width: 1200px) {
            width: 110px;
        }
    }
    .content-body{
        width:calc(100% - 90px);
        overflow: hidden;
        @include rtl-padding-left(15px);
        @media(min-width: 1200px) {
            @include rtl-padding-left(25px);
            width:calc(100% - 110px);
        }
    }
    .name{
        margin:0 0 2px;
        font-size: 16px;
        font-family: $font-family-second;
    }
    &:hover{
        .content-left{
            border-color:$theme-color;
        }
    }
}
// shop-list-smallest
.shop-list-smallest{
    .name{
        a{
            color: $link-color;
            &:hover,&:active{
                color:$theme-color;
                text-decoration: none;
            }
        }
    }
    .content-left{
        width: 90px;
        @include rtl-padding-right(20px);
    }
}
.woocommerce.carousel.inner-list-smallest{
    border-top:1px solid $border-color;
    .shop-list-smallest{
        margin-bottom:0;
        border-top:none;
    }
}
// single product
.woocommerce .woocommerce-product-rating{
    .star-rating{
        margin: 0;
        display: inline-block;
        float: none;
        vertical-align: middle;
    }
    .woocommerce-review-link{
        display: inline-block;
        font-size:14px;
        line-height:1;
    }
}
.woocommerce #content div.product div.summary, .woocommerce div.product div.summary, .woocommerce-page #content div.product div.summary, .woocommerce-page div.product div.summary,
.woocommerce #content div.product div.images, .woocommerce div.product div.images, .woocommerce-page #content div.product div.images, .woocommerce-page div.product div.images{
    width: 100%;
}
.single_variation_wrap{
    div.qty{
        font-size: 15px;
        text-transform: uppercase;
        color: $text-color;
        font-family: $font-family-three;
        margin-top: 10px;
        @include rtl-margin-right(10px);
    }
}
.wrapper-shop{
    @media(min-width:1024px) {
        padding-top:50px;
        padding-bottom:50px;
    }
    .apus-pagination{
        border-top:1px solid $border-color;
        padding-top: 40px;
        margin-top: 0;
    }
    aside.sidebar{
        background: transparent;
    }
}
.thumbnails-image{
    ul{
        list-style: none;
        margin:0;
        padding:0;
    }
    .prev,
    .next{
        display: block;
        width: 100%;
        text-align:center;
        font-size: 18px;
        color: #000;
    }
    .thumb-link{
        display: block;
        @include opacity(0.4);
        margin: 10px 0;
        &:hover,
        &.active{
            @include opacity(1);
        }
    }
}
.user_photo_thumbs{
    list-style: none;
    padding: 0;
    text-align: center;
    margin: 10px 0 0;
    li{
        display: inline-block;
        margin:0 4px;
        width: 70px;
        @include opacity(0.4);
        @include transition(all 0.3s ease-in-out 0s);
        &:hover,&.active,&:active{
            @include opacity(1);
        }
    }
}
.user_photo{
    margin-top: 50px;
}
.delivery_info{
    text-align:center;
    background:#f5f5f5;
    font-size: 14px;
    padding:8px;
    @include transition(all 0.3s ease-in-out 0s);
    &:hover{
        background:darken(#f5f5f5, 10%);
    }
    i{
        font-size: 16px;
        @include rtl-margin-right(10px);
    }
}
.details-product{    
    @media(min-width:1200px) {
        .left-detail{
            @include rtl-padding-right(0);
        }
    }
    .shipping_info{
        margin-top: 15px;
        @media(min-width:1200px) {
            margin-top: 40px;
        }
        font-size: 14px;
        color: #b7b7b7;
        @include transition(all 0.3s ease-in-out 0s);
        &:hover{
            color: $text-color;
        }
        ul{
            list-style: none;
            padding:0;
            margin:0;
            i{
                @include rtl-margin-right(6px);
            }
            li{
                margin-bottom:0px;
                @media(min-width: 1200px) {
                    margin-bottom:5px;
                }
                &:last-child{
                    margin-bottom: 0;
                }
            }
        }
    }
    .price-rating-wrapper{
        margin-top: 10px;
        @media(min-width: 1200px) {
            margin-top: 20px;
        }
        clear: both;
        overflow: hidden;
        .price{
            @include rtl-margin-right(15px !important);
            line-height: 1.4;
            del{
                display: block !important;
            }
        }
        > *{
            display: inline-block;
            vertical-align: bottom;
        }
    }
    .pro-info{
        @media(min-width: 1200px) {
            font-size: 30px;
        }
        margin: 0 0 20px;
    }
    .popup-video{
        background:$white;
        height: 40px;
        line-height: 40px;
        min-width: 40px;
        overflow: hidden;
        display: inline-block;
        @include box-shadow(0 0 10px 0 rgba(0, 0, 0, 0.2));
        @include border-radius(50px);
        @include transition(all 0.3s ease-in-out 0s);
        @include flexbox;
        align-items: center;
        -webkit-align-items: center; /* Safari 7.0+ */
        flex-direction:row;
        -webkit-flex-direction:row;
        i{
            height: 40px;
            line-height: 40px;
            width: 40px;
            font-size: 13px;
            text-align: center;
            text-indent: 3px;
        }
        span{
            @include transition(all 0.3s ease-in-out 0s);
            white-space:nowrap;
            max-width: 0;
            padding: 0;
            overflow: hidden;
        }
        &:hover{
            span{
                max-width: 280px;
                @include rtl-padding-right(12px);
            }
        }
    }
    .product-cat{
        font-family: $font-family-three;
        text-transform: uppercase;
        letter-spacing: 2px;
        font-size: 12px;
        a{
            color: $theme-color;
        }
    }
    // tab
    div.video{
        z-index: 8;
        position:absolute;
        @include rtl-left(10px);
        bottom:10px;
        @media(min-width: 768px) {
            @include rtl-left(20px);
            bottom:20px;
        }
    }
    .apus-countdown {
        margin-top: 5px;
    }
    .special-product{
        padding:8px 0;
    }
    .apus-countdown .times{
        > span{
            color: $theme-color-second;
            margin-bottom:5px;
        }
        margin-bottom: 5px;
        > div{
            text-align: center;
            vertical-align: middle;
          min-width: 40px;
          font-size: 12px;
          display: inline-block;
          font-weight: 400;
          text-transform: uppercase;
          margin:0 5px;
          padding:8px;
          &:first-child{
            @include rtl-margin-left(0);
          }
          span{
            font-weight: 500;
            margin-bottom:5px;
            @include border-radius(3px);
            font-size: 18px;
            display: block;
            color: $link-color;
          }
        }
    }
    .top-content{
        margin-bottom: 54px;        
    }
    .apus-woocommerce-product-gallery-thumbs{
        .slick-slide{
            &:hover,
            &:active,
            &.slick-current{
                .thumbs-inner{
                    border-color:$theme-color;
                }
            }
            .thumbs-inner{                
                padding: 5px;
                max-width: 100%;
                display: block;
                cursor: pointer;
                position: relative;                
                border: 1px solid $border-color;                
                @include transition-all();
                @include hover() {
                    border-color: $theme-color;
                }                
            }
        }
        // fix for position
        &.vertical{
            margin:0;
            .slick-slide{
                padding:0;
                margin-bottom: 10px;
                border:none;
            }
            .slick-arrow{
                text-align: center;
                background-color:transparent !important;
                border:0 !important;
                i{
                    @include size(30px,30px);
                    background-color:$white;
                    @include border-radius(50%);
                    @include box-shadow(0 0 1px 1px rgba(0, 0, 0, 0.2));
                    line-height: 30px;
                    display: inline-block;
                    @include transition(all 0.2s ease-in-outs 0s);
                }
                &:hover,&:focus{
                    i{
                        color: $white;
                        background-color:$theme-color;
                        @include box-shadow(none);
                    }
                }
            }
            .slick-prev{
                top: inherit;
                bottom:100%;
                @include translate(0,-5px);
                width: 100%;
                left:0;
                font-size: 11px;
            }
            .slick-next{
                width: 100%;
                top: 100%;
                bottom:inherit;
                @include translate(0,0);
                right:0;
                font-size: 11px;
            }
        }
    }
    .image-mains{
        max-width:100%;
        position:relative;
        .apus-woocommerce-product-gallery-wrapper.full-width{
            width:100% !important;
            float: none !important;
        }
        &.thumbnails-bottom{
            .apus-woocommerce-product-gallery-wrapper{
                margin-bottom:$theme-margin;
            }
        }
        &.thumbnails-left{
            .apus-woocommerce-product-gallery-wrapper{
                width:calc(100% - 100px);
                @media(min-width: 1200px) {
                    width:calc(100% - 160px);
                }
                float:right;
            }
            .wrapper-thumbs{
                float:left;
                width:100px;
                padding-right:20px;
                @media(min-width: 1200px) {
                    padding-right:30px;
                    width:160px;
                }
            }
            @media(max-width:767px) {
                .apus-woocommerce-product-gallery-wrapper{
                    width:calc(100% - 70px);
                }
                .wrapper-thumbs{
                    width:70px;
                    padding-right:10px;
                }
            }
        }
        &.thumbnails-right{
            .apus-woocommerce-product-gallery-wrapper{
                width:calc(100% - 160px);
                float:left;
            }
            .wrapper-thumbs{
                float:right;
                width:160px;
                padding-left:20px;
                @media(min-width: 1200px) {
                    padding-left:30px;
                }
            }
            @media(max-width:767px) {
                .apus-woocommerce-product-gallery-wrapper{
                    width:calc(100% - 70px);
                }
                .wrapper-thumbs{
                    width:70px;
                    padding-left:10px;
                }
            }
        }
    }
    .description{
        .title{
            font-size:21px;
        }
    }
    .apus-woocommerce-product-gallery-wrapper{
        position: relative;
        border: 2px solid $border-color;
        padding: 30px;    
        @include border-radius(5px);    
        .downsale{
            font-size: 12px;
            font-weight: 500;            
            position: absolute;
            left:0;
            top:0;
            z-index: 9;
            padding:2px 10px;            
            background:#d42e2e;
            color: $white;
            @include inline-block();
            @include border-radius(0);
        }
        .apus-woocommerce-product-gallery {
            margin:0;
            .slick-slide{
                padding:0;
            }
        }
        .woocommerce-product-gallery__trigger{                   
            z-index: 8;
            bottom: 30px;                               
            border: 0;
            position: absolute;
            color: $headings-color;
            background-color: $border-form-color;        
            @include opacity(1);
            @include rtl-left(30px);            
            @include flexbox();
            @include square(60px);
            @include border-radius(50%);
            @include align-items(center);
            @include justify-content(center);  
            @include transition-all();                          
            @include hover-active() {
                color: $white;
                background-color: $theme-color;
            } 
            [class*="icon"]{                                
                &:before{
                    margin: 0;
                    font-size: 24px;
                }
            }
        }
        &:hover{
            .woocommerce-product-gallery__trigger{
                @include opacity(1);
            }
        }
    }
    .woocommerce-product-details__short-description{
        &.hideContent{
            overflow: hidden;
            height: 60px;
            @include transition(all 0.2s ease-in-out 0s);
        }
    }
    .woocommerce-variation-add-to-cart{
        width: 100%;
        overflow: visible;
        @include clearfix();
    }
    .list{
      li{
        margin-bottom: 10px;
      }
      i{
        color: $theme-color;
        @include rtl-margin-right(8px);
      }
    }
    .woocommerce-variation-price{
        margin-bottom: 30px;
        margin-top: 26px;
    }
    .product_meta{
        line-height: 1.1;        
        clear:both;
        padding-top: 30px;
        margin-top: 0;
        color:$text-color;
        a{
            color:$text-color;
            @include transition-all();
            @include hover-active() {
                text-decoration: underline;
            }            
        }
        > *{            
            margin-bottom: 10px;
            display: block;            
            border: 0;           
        }        
        .sku{
            outline: none;
        }
    }
    .information{
        position: relative;        
        @include rtl-padding-left(20px);
        .summary {
            width: 100%;
            float: none !important;            
            margin: 0 !important;
        }
        .single_variation_wrap{
            padding-top:10px;
        }
        .price{
            font-size: 26px !important;                        
        }
        .woocommerce-product-rating{
            margin-bottom: 20px !important;
            .text-customer{
                display: none;
            }
        }
        .woocommerce-product-details__short-description{
            margin-bottom: 25px;            
            p:last-child{
                margin-bottom: 0;
            }
        }
        .view-more-desc {
            font-size: 14px;
            color: #b7b7b7;
            @include transition(all 0.2s ease-in-out 0s);
            &:hover{
                color: $link-color;
            }
            &.view-less{
                color: $brand-danger;
            }
        }
        .woocommerce-product-details__short-description-wrapper.v2{
            margin-bottom: 15px;
            @media(min-width: 1200px) {
                margin-bottom: 30px;
            }
            .woocommerce-product-details__short-description{
                margin-bottom: 3px;
            }
        }
        .top-info-detail{
            margin-bottom:15px;
        }
        .cart{
            width:100%;
            &.grouped_form,
            &.variations_form{
                outline: normal;
            }
            margin:10px 0 !important;
            @media(min-width: 1200px) {
                margin: 22px 0 10px !important;
            }
            .group_table{
                tr{
                    td:first-child{
                        div.quantity{
                            margin:0 !important;
                        }
                    }
                }
            }
            div.quantity-wrapper{
                margin: 0;          
                overflow: visible;                
                @include rtl-float-left();      
                > *{                    
                    @include inline-block();
                    float: none !important;
                }                
                > label{
                    display: none;
                }
            }
            &.grouped_form{
                .quantity-wrapper{
                    margin:0 !important;
                    label{
                        display: none;
                    }
                }
            }
        }
        .clear{
            display: none;
        }
        .product_title{
            clear: both;
        }
    }
    .title-cat-wishlist-wrapper{
        position:relative;
        margin-bottom:20px;
        @include rtl-padding-right(30px);        
        @media(min-width: 1200px) {
            margin-bottom: 30px;
        }
    }
    // social
    .apus-social-share{
        margin-top: 34px;
        .bo-sicolor{
            @include flexbox();
            @include align-items(center);
        }
        span{
            font-family: $headings-font-family;
            color: $headings-color;
            font-size: 18px;
            line-height: 24px;
            @include inline-block();
            @include rtl-margin-right(20px);
        }
        a{
            @include rtl-margin-right(20px);            
            display: inline-block;
            color: #b3b7c8;
            font-size: 16px;
            @include hover-focus-active() {
                color: $headings-color;
            }
        }
    }
    // discount
    .apus-discounts{
        margin:20px 0 15px;
        padding:15px 20px;
        background: #eceff6;
        font-size: 13px;
        ul{
            margin:0;
            list-style: none;
            padding:0;
            li{
                margin: 0 0 3px;
                &:before{
                    font-family: 'FontAwesome';
                    color:$theme-color;
                    content: "\f00c";
                    @include rtl-margin-right(8px);
                }
            }
        }
        .icon{
            display: inline-block;
            vertical-align: middle;
            @include size(35px,35px);
            text-align: center;
            line-height: 35px;
            color: $white;
            background: darken(#eceff6,20%);
            font-size: 14px;
            @include border-radius(50%);
            @include rtl-margin-right(10px);
        }
        .title{
            font-size: 18px;
            margin:0 0 10px;
        }
    }
    .product-free-gift{
        margin:0 0 20px;
        padding:15px 20px;
        background: $state-danger-bg;
        .icon{
            display: inline-block;
            vertical-align: middle;
            @include size(35px,35px);
            text-align: center;
            line-height: 35px;
            color: $white;
            background: #e23e1d;
            font-size: 14px;
            @include border-radius(50%);
            @include rtl-margin-right(10px);
        }
        .title{
            font-size: 18px;
            margin:0 0 10px;
        }
        .list-gift{
            font-size: 13px;
            list-style: none;
            padding:0;
            margin:0;
            li{
                margin-bottom: 3px;
            }
            i{
                color: #e23e1d;
            }
        }
        .hightcolor{
            font-weight: 500;
            color: #e23e1d;
        }
    }
}
.details-product{
    &.layout-v1{
        .summary-right {
            .summary {
                @include rtl-padding-left(20px);
                position:relative;
                @media(min-width: 1600px) {
                    @include rtl-padding-left(50px);
                }
                &:before{
                    content:'';
                    position:absolute;
                    top:0;
                    @include rtl-left(0);
                    @include size(1px,1000px);
                    background:$border-color;
                }
            }
        }
        .summary-left{
            @media(min-width: 1600px) {
                .summary{
                    @include rtl-padding-right(20px);
                }
            }
        }
    }
    &.layout-v2{
        .image-mains.thumbnails-bottom .apus-woocommerce-product-gallery-wrapper{
            margin-top: 10px;
        }
        .product_meta{
            > *{
                display: block;
                border:none;
                width:100%;
                padding:0;
                margin:0 0 8px;
                &:last-child{
                    margin:0;
                }
            }
        }
        .wrapper-thumbs {
            .slick-slider{
                margin-left: -5px;
                margin-right: -5px;
                .slick-slide{
                    padding-right:5px;
                    padding-left:5px;
                }
            }
        }
        .tabs-v1{
            .tab-content{
                padding:15px;
                margin:0;
                border-width:0 1px 1px;
                border-style:solid;
                border-color:$border-color;
                @media(min-width: 1200px) {
                    padding:30px;
                }
            }
            #commentform,
            table.shop_attributes{
                margin:0;
            }
        }
    }
}

.accessoriesproducts-wrapper{
    position: relative;
    &.loading:before{
        position: absolute;
        @include size(100%,100%);
        top: 0;
        left: 0;
        z-index: 99;
        content: '';
        background:url('#{$image-theme-path}loading-quick.gif') center center no-repeat rgba(255,255,255,0.9);
    }
}
.accessoriesproducts{
    .product-block.grid{
        margin-bottom: 25px;
        .accessory-add-product{
            position: absolute;
            @include rtl-left(10px);
            bottom: -25px;
        }
    }
    .check-all-items-wrapper{
        margin: 0 0 10px;
        input{
            @include rtl-margin-right(6px);
        }
    }
    .total-price-wrapper{
        font-size: 14px;
        color: $link-color;
        margin: 0 0 5px;
    }
    .total-price{
        display: block;
        color: $theme-color-second;
        font-size: 18px;
        font-weight: normal;
    }
}
/*------------------------------------*\
    Product Category and Subcategories
\*------------------------------------*/
.product-category{
    .product-category-content{
        position: relative;
        overflow: hidden;
        min-height: $product-category-content-height;
        margin: $product-category-content-margin;
    }
    .product-category-image{
        display: block;
        img{
            @include img-responsive();
        }
    }
    .product-category-title{
        text-transform: none;
        position: absolute;
        text-align: center;
        bottom: 0;
        @include rtl-left(0);
        width: 100%;
        font-weight: $product-category-title-font-weight;
        @include font-size(font-size,$product-category-title-font-size);
        color: $product-category-title-color;
        margin: $product-category-title-margin;
        padding: $product-category-title-padding;
        background: rgba($product-category-title-bg, .3);
        .count{
            background: transparent;
            color: $product-category-title-color;
        }
    }
}

/**
 *
 *  Woocommerce Form
 */
.form-row {
	.checkbox, .input-radio{
		margin-bottom: 0;
		margin-top: 0;
	}
}	
.woocommerce form .form-row{
    margin: 0 0 20px;
    padding:0;
    &.place-order{
        padding-bottom: 0;
        margin-bottom: 0;
    }
}
.woocommerce .col2-set .col-2, .woocommerce-page .col2-set .col-2,
.woocommerce .col2-set .col-1, .woocommerce-page .col2-set .col-1{
    width: 100%;
}
/* End
------------------------------------------------*/
.category-image{
    img{
        @extend .filter-grayscale;
    }
}
p.demo_store {
	top: 0;
	position:fixed;
	@include rtl-left(0);
	@include rtl-right(0);
	@include size(percentage(1),auto);
	@include rtl-text-align-center();
    @include font-size(font-size,$font-size-md);
	padding: .5em 0;
	z-index: 99998;
	border: 1px solid darken($border-color, 10);
	@include box-shadow(0, 0, 0, 3px, rgba($white,0.2));
}
/*-------------------------------*\
    Utilities
\*------------------------------------*/
.woocommerce #reviews #comments ol.commentlist{
    padding:0;
}
//reviews
.woocommerce #reviews #comments ol.commentlist li{
    margin: 0;
    padding:0 0 30px;
    line-height: 1.5;
    .meta{
        .woocommerce-review__author{
            color: $headings-color;
            font-size: 20px;
            font-weight: 400;
            font-family: $headings-font-family;
        }
    }
    .apus-avata{
        min-width: 80px;
        @media(min-width: 768px) {
            min-width: 100px;
        }
        .apus-image{
            display: inline-block;
        }
    }
    img.avatar{
        border:none;
        @include square(70px);        
        @include border-radius(50%);
        padding: 0;
        position: relative; 
    }
    .star-rating{
        margin: 6px 0;
        float:none;
        &:before{
            color: $star-rating-color ;
        }
        span{
            &:before{
                color: $star-rating-color ;
            }
        }        
    }
    .top-info{
        margin: 0 0 8px;
    }
    .dokan-review-author-img{
        @include rtl-float-left();
        @include rtl-padding-right($theme-margin);
    }
    .comment-text{
        overflow: hidden;
        border:none;
        padding:0;
        margin:0;
    }
    .description{
        margin-top: 10px;
        p{
            margin:0;
        }
    }
    .apus-author{
        font-size: 16px;
        color: $link-color;
        margin:0;
    }
    .date{
        font-size:14px;
        color: #b3b7c8;
    }
    .content-comment {
        margin-top:15px;
    }
    .comment-text {        
        @include rtl-padding-left(30px);        
    }
}
.woocommerce p.stars a[class*="star-"]:before{
    display: none;
}
#respond {
    .comment-reply-title{
        font-size: 20px;
        font-weight: 600;
        margin: 0 0 24px !important;
        #cancel-comment-reply-link{
            color:$brand-danger;
        }
    }
    #commentform{
        margin-bottom: 0;
    }
    label{
        cursor: pointer;
        color: $text-color;
        font-weight: 400;
    }
	.form-submit {
		input {
			@include rtl-left(auto);
		}
	}
	textarea {
		@include box-sizing(border-box);
	}
	p.stars {
		position: relative;
		padding: 0 0 10px;
		a{		
			text-indent: -9999px;
			position: relative;			
            color: $star-rating-color;
            font-weight: $headings-font-weight;
            @include inline-block();
            @include rtl-margin-right(1em);
			&:last-child {
				@include rtl-border-right(0);
			}
			&.star-1,
			&.star-2,
			&.star-3,
			&.star-4,
			&.star-5 {				
				&:after {
					top: -6px;
					font-family: "FontAwesome";
					text-indent: 0;
					position: absolute;
					@include rtl-left(0);
                    color:#cccccc;
				}
                &:hover:after,
                &.active:after {
                    color:$star-rating-color;
                }
			}
			&.star-1 {
				width: 1.5em;
				&:after {
					content: "\f005";
				}
				&:hover:after,
				&.active:after {
					content: "\f005";
				}
			}
			&.star-2 {
				width: 2.5em;
				&:after {
					content: "\f005\f005";
				}
				&:hover:after,
				&.active:after {
					content: "\f005\f005";
				}
			}
			&.star-3 {
				width: 3.5em;
				&:after {
					content: "\f005\f005\f005";
				}
				&:hover:after,
				&.active:after {
					content: "\f005\f005\f005";
				}
			}
			&.star-4 {
				width: 4.5em;
				&:after {
					content: "\f005\f005\f005\f005";
				}
				&:hover:after,
				&.active:after {
					content: "\f005\f005\f005\f005";
				}
			}
			&.star-5 {
				width: 5.5em;
				border: 0;
				&:after {
					content: "\f005\f005\f005\f005\f005";
				}
				&:hover:after,
				&.active:after {
					content: "\f005\f005\f005\f005\f005";
				}
			}
            &.active,
            &:hover,
            &:active{
                &:after{
                    color: $star-rating-active-color;
                }
            }
		}
	}
}

.woocommerce{
    #reviews{
        #comment {
            height: 150px;
            resize: none;
        }
        .form-control{
            background-color: $white;
        }
    }     
} 

/*------------------------------------*\
    Quantity inputs
\*------------------------------------*/
.woocommerce .quantity .qty{
    width: 80px;
    font-size: 14px;
    height:50px;
    border:1px solid $border-color;
    padding:10px 20px;
    font-weight:500;
    color:$text-color;
}
.woocommerce .quantity{
    .reader-text{
        font-size: 14px;
        font-weight: 400;
        margin-bottom: 0;
        @include rtl-margin-right(10px);
    }
}
.woocommerce a.remove{
    @include border-radius(0);
}
/*------------------------------------*\
    Forms
\*------------------------------------*/
.form-row {
	@include clearfix();
    label.hidden {
        visibility:hidden;
    }
    label.inline {
        display: inline;
    }
    label{
    	display: block;
        font-weight: 500;
    }
    select {
        cursor: pointer;
    }
    .required {
        color: $red;
        font-weight: $headings-font-weight;
        border: 0;
    }
    .input-text{
    	width: 100%;
    	padding: 8px 10px;
    }
    &.form-row-first{
    	width: 47%;
    	@include rtl-float-left();
    }
    &.form-row-last{
    	width: 47%;
    	@include rtl-float-right();
    }
    &.form-row-wide{
    	clear: both;
    }
}
.select2-container .select2-choice{
    padding:5px 7px;
}
.product-quantity{
	.input-text{
		@include input-size('.input-sm', $input-height-small, $padding-base-vertical, $padding-base-vertical, $font-size-small, $line-height-small, 0);
	}
}
.i-am-new{
    li{
        background-image: none !important;
        background-color: $white !important;
        -webkit-border-radius: 0px !important;
        border-radius: 0px !important;
        .noty_message{
            padding: 20px 0 !important;
        }
    }
}

/*------------------------------------*\
    Mini cart and wishlist
\*------------------------------------*/
.total-minicart{
    display: none;       
}
.wishlist-icon,
.mini-cart{
    display: inline-block;
    position:relative;
    padding:0;
    color:$link-color;
    line-height:1;
    i{
        font-size: 21px;
        margin:0 !important;
    }
    .count{        
        position:absolute;
        top: -5px;            
        font-size: 10px;
        color: $white;        
        background-color: darken($theme-color, 20%);       
        line-height: 16px;
        min-width: 15px;
        padding: 0;        
        overflow: hidden;
        text-align: center;        
        @include rtl-right(-5px);     
        @include border-radius(50%);
        @include inline-block();
        @include square(17px);  
    }
}
.wishlist-icon{
    i{
        @include rtl-margin-right(6px);
    }
}
/*------------------------------------*\
    Star ratings
\*------------------------------------*/
.woocommerce {
    .star-rating span::before,        
    p.stars a:hover::after, 
    p.stars a::after, 
    .star-rating span::before{        
        color: $star-rating-color;
    }
    .star-rating {
        word-break: normal;           
        &:before {       
            color: #d0d23c;     
            @include opacity(1);
        }        
        span {            
            &:before{                
                color: $star-rating-color;
            }
        }
    }
    .woocommerce-review-link{
        color: #999591;
    }
}

/*------------------------------------*\
    Filter
\*------------------------------------*/
.archive-shop{
    .page-title{
        display: none;
    }
}
.show-filter{
    font-size:18px;
    color:$theme-color;
    cursor:pointer;
    font-weight:400;
    text-transform:uppercase;
    letter-spacing:1px;
    @include transition(all 0.2s ease-in-out 0s);
    &:hover,&:active{
        color:$theme-color;
    }
    i{
        @include rtl-margin-left(10px);
    }
}
.apus-shop-menu{
    font-size: 15px;
    margin:0;
    position:relative;
    .filter-action{
        i{
            @include rtl-margin-right(3px);
        }
    }
    ul.apus-filter-menu{
        padding:0;
        margin:5px 0 0;
        list-style: none;
        @include rtl-float-right();
        li{
            display: inline-block;
        }
    }
    ul.apus-categories{
        @include rtl-float-left();
        padding:0;
        margin:2px 0 0;
        list-style: none;
        li{
            display: inline-block;
            @include rtl-margin-right(40px);
            a{
                text-transform: capitalize;
                padding:0;
                font-size: 16px;
                font-weight:500;
                color:$link-color;
                position:relative;
                display:inline-block;
            }
            .product-count{
                font-size: 14px;
                color: $text-color;
                margin: 0 2px;
                vertical-align: top;
                display: inline-block;
            }
            &.current-cat{
                > a{
                    color:$theme-color;
                }
            }
        }
        .apus-shop-sub-categories{
            padding:0px;
            margin: 10px 0 0;
            li{
                a{
                    font-size: 16px;
                }
            }
        }
    }
    .content-inner{
        #apus-orderby{
            @include rtl-margin-left(40px);
        }
    }
}
//apus-shop-header
.apus-shop-header{
    background: transparent;
    @include transition(all 0.3s ease);
    &.filter-active{
        background: $white;
        border-bottom: 1px solid #eeeeee;
        margin-bottom: 30px;
    }
    .apus-sidebar-header{
        display: none;
        border:1px solid $theme-color;
        padding:20px 30px;
        background:$white;
    }
    .apus-widget-scroll{
        ul{
            li{
                padding:0;
                list-style: none;
                font-size: 14px;
                margin: 0 0 10px;
            }
        }
    }
    .apus-sidebar-inner{
        padding: 0 15px;
        ul{
            padding:0;
            margin:0;
            list-style: none;
        }
        .apus-widget-title{
            font-weight: 400;
            font-size: 18px;
            text-transform: capitalize;
            margin:0 0 20px;
        }
    }
    .widget_layered_nav ul li a, .product-categories li a{
        font-size: 14px;
        padding:0 !important;
    }
    .widget_layered_nav ul li .count, .product-categories li .count{
        float: none;
    }
    .widget_layered_nav ,
    .product-categories{
        li.chosen{
            color: $theme-color;
            > a{
                color: $theme-color;
            }
        }
    }
}
.apus-categories-dropdown{
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    border:none;
    color: $link-color;
    font-size: 14px;
    margin-top: 4px;
    .category-dropdown-label{
        cursor: pointer;
    }
    option {
        font-size: 16px;
        color: $text-color;
        &[selected="selected"]{
            color: $link-color;
        }
    }
    .dropdown-menu{
        min-width: 200px;
        padding:20px 30px;
        @include border-radius(0);
        border:1px solid $theme-color;
        @include box-shadow(none);
        ul{
            list-style:none;
            padding:0;
            margin:0;
            li{
                margin: 0 0 5px;
                a{
                    color: $text-color;
                    &:hover,&:active{
                        color: $link-color;
                    }
                }
                &.active{
                    color: $link-color;
                }
                &:last-child{
                    margin: 0;
                }
            }
        }
    }
}
.before-shop-header-wrapper{
    position:relative;
    @media(min-width:768px) {
        .before-shop-loop-fillter{
            position:absolute;
            top:20px;
        }
    }
}
.pagination-top{
    margin-top:-6px;
    .apus-pagination.pagination-woo{
        margin:0;
    }
    .apus-pagination .apus-pagination-inner{
        padding:0;
    }
    &.has-fillter{
        .apus-pagination .apus-pagination-inner{
            padding:0 $theme-margin;
        }
    }
}
.apus-filter{
    padding: 0 0 20px;
    margin: 0px 0 20px;
    border-bottom:1px solid $border-color;
    @media(min-width: 1200px) {
        margin-bottom: 40px;
    }
    .shop-page-title{
        margin-top:0;
        margin-bottom:0;
        font-size: 24px;
    }
    .woocommerce-result-count{
        font-size:18px;
        font-weight:400;
        @include rtl-float-right();
        margin:0;
    }
    #apus-orderby{
         @include rtl-float-left();
    }
    .woocommerce-ordering{
        margin: 0 0 25px 0;
    }
    .orderby-wrapper{
        > *{
            display:inline-block;
            vertical-align:middle;
            float:none;
        }
    }
    select{
        font-size: 16px;
        color: $text-color;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background: url("#{$image-theme-path}select.png") $white right 10px center no-repeat;
        font-weight: 400;
        padding:3px 20px;
        @include border-radius(2px);
        margin:0;
        border:1px solid $border-color;
    }
    .display-mode{
        margin-top: 4px;
    }
    .change-view{
        color: #cccccc;
        i{
            font-size: 24px;
            vertical-align: middle;
        }
        display: inline-block;
        + .change-view{
            @include rtl-margin-left(10px);
            @media(min-width: 1200px) {
                @include rtl-margin-left(20px);
            }
        }
        &:hover,
        &.active{
            color: $theme-color;
        }
    }
    .form-edumy-ppp{
        .edumy-wc-wppp-select{
            @media(min-width: 1200px) {
                min-width: 190px;
            }
        }
    }
    .form-edumy-ppp,
    .orderby-wrapper{
        @include rtl-margin-right(10px);
        @media(min-width: 1200px) {
            @include rtl-margin-right(30px);
        }
    }
}
// show
.form-edumy-ppp{
    @include rtl-float-left();
    select{
        font-size: 16px;
        color: $text-color;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        background: url("#{$image-theme-path}select.png") $white right 10px center no-repeat;
        font-weight: 400;
        border:1px solid $border-color;
        padding:3px 20px;
        @include border-radius(2px);
        margin:0;
        border:1px solid $border-color;
    }
}
.apus-after-loop-shop{
    margin-bottom: 10px;
    .form-edumy-ppp{
        select{
            min-width: 120px;
        }
    }
    @media(min-width: 768px) {
        margin-bottom: 45px;
       .woocommerce-result-count{
            @include rtl-float-right();
            margin:4px 0 0; 
        }
        .apus-pagination {
            @include rtl-float-left();
            margin:0;
            padding:0;
        } 
    }
}
#apus-orderby{
    .orderby-label{
        color: $text-color;
        display: inline-block;
        font-size:14px;
        font-weight: 300;
        cursor: pointer;
        border:1px solid $border-color;
        @include border-radius(50px);
        padding:4px 15px;
    }
    .dropdown-menu{
        min-width: 200px;
        padding:20px 30px;
        @include border-radius(5px);
        @include border-radius(0);
        border:1px solid $theme-color;
        @include box-shadow(none);
        ul{
            list-style:none;
            padding:0;
            margin:0;
            li{
                margin: 0 0 5px;
                a{
                    color: $text-color;
                    &:hover,&:active{
                        color: $link-color;
                    }
                }
                &.active{
                    color: $link-color;
                }
                &:last-child{
                    margin: 0;
                }
            }
        }
    }
}
/*------------------------------------*\
    Mini Cart
\*------------------------------------*/
.apus-topcart{    
	.dropdown-menu{
		top: 50px;
        margin: 0;
        padding: 30px;
		min-width: 414px;		
        border:none;        
        background: $white;         
        visibility: hidden;
        pointer-events: none;       
        display: block !important;
        @include rtl-right(-20px);
        @include border-radius(5px);        
        @include opacity(0);
        @include transition-all();
        @include translateY(30px);
        @include box-shadow(0 0 18px 0 rgba(0,0,0,0.1));    
        &:before{                       
            top: -8px;
            content: "";
            position: absolute;     
            background: $white;
            @include square(0);                             
            @include rtl-right(25px);                                                
            @include square(16px);                              
            @include rotate(45deg);     
            @include box-shadow(-2px -2px 10px -5px rgba(0, 0, 0, 0.2)); 
        }
	}
    .buttons{
        margin: 0;
        .btn{
            @include border-radius(50px);
        }
        .wc-forward{                                    
            text-transform: none;
            padding: 12px 34px;
            font-family: $headings-font-family;
            font-size: 16px;
            line-height: normal;
            border: 0;
            font-weight: $headings-font-weight;
            @include rtl-margin(0,20px,0,0);
            @include hover-focus-active() {                
                color: $white;
                border-color: $theme-color; 
                background: $theme-color;
            }
            &:last-child{
                margin: 0;
            }
        }
    }
	.open{
		.dropdown-menu{
            display: block;
			@include opacity(1);            
            @include translateY(0);
            visibility: visible;    
            pointer-events: auto;        
		}
	}
    .overlay-offcanvas-content{
        background:rgba(0,0,0,0.5);
        position:fixed;
        top:0;
        left:0;
        @include size(100%,100%);
        @include opacity(0);
        @include transition(all 0.3s ease-in-out 0s);
        cursor: no-drop;
        @include translateX(-30px);
        visibility: hidden;
        z-index: 2;
        &.active{
            visibility: visible;
            @include opacity(1);
            @include translateY(0);
        }
    }
    .offcanvas-content{
        z-index: 3;
        position:fixed;
        right:0;
        top:0;
        background:$white;
        @include transition(all 0.35s ease-in-out 0s);
        @include opacity(0);
        width:400px;
        height: 100vh;
        @include translateX(100%);
        &.active{
            @include opacity(1);
            @include translateY(0);
        }
        .shopping_cart_content .cart_list{
            max-height: calc(100% - 180px);
        }
        .title-cart-canvas{
            font-size: 16px;
            text-align: center;
            margin:0 0 10px;
            padding:10px;
            border-bottom:1px solid $border-color;
            text-transform: uppercase;
            position:relative;
            .close-cart{
                position:absolute;
                top:11px;
                @include rtl-left(14px);
                z-index: 1;
                background:$white;
                font-size: 18px;
                cursor: pointer;
                color: $brand-danger;
            }
        }
        .shopping_cart_content{
            padding:10px;
            @media(min-width: 1200px) {
                padding:15px 15px 30px;
            }
            height: calc(100vh - 50px);
            display: -webkit-flex; /* Safari */
            display: flex;
            flex-wrap:wrap;
            -webkit-flex-wrap: wrap;
            .cart-bottom{
                align-self:flex-end;
                -webkit-align-self:flex-end;
                width:100%;
            }
            .cart_list {
                width:100%;
            }
        }
    }
}
.shopping_cart_content{
    font-size: 14px;
    .variation{
        margin:0 0 3px;
        overflow: hidden;
        dt{
            @include rtl-margin-right(5px);
        }
        dt,dd{
            @include rtl-float-left();
            p{
                margin: 0;
            }
        }
    }
    .cart_list{
        padding:0 0 10px;
        max-height: 270px;
        overflow: hidden;
        > div{
            margin: 0;            
            overflow: hidden;
            padding: 10px 0;
            border-bottom: 1px solid #dedede;
            &.empty{
                border:none;
                margin:0;
                color: $link-color;
            }
            &:first-child{                
                padding-top: 0px;
            }
            &:last-child{
                border:none;
                padding-bottom: 0px;
            }
        }
        .image{            
            @include square(60px);                        
            @include border-radius(100%);
            display: block;
            img{               
                @include border-radius(100%);
            }
        }
        .quantity{
            font-family:$font-family-second;
            font-size:16px;
            color: $text-color;
            padding:0;          
            display: inline;
        }
        .name{            
            margin:0;
            font-size:16px;
            font-family: $headings-font-family;
        }
        .cart-item{
            margin: 0;
            font-size: 16px;
        }
        .media-body{
            width: 1000px;
            @include rtl-padding-right(20px);
        }
        .cart-main-content{
            @include rtl-text-align-left();
            position: relative;
            .remove{                
                top: 22px;
                z-index: 9;            
                display: block;                    
                position: absolute;                
                background-image: url('../images/close-dark.png');
                background-repeat: no-repeat;
                background-position: 0 0;
                background-color: transparent;
                @include size(15px,16px);
                @include rtl-right(0);
                &:hover,&:focus{
                    background-color: transparent;
                }
            }
        }
        .cart-item{
            overflow:hidden;
        }
        .remove{
            i{
                display: none;                
            }
        }
    }
    .total{        
        overflow: hidden;
        position: relative;
        margin: 0;
        font-weight: 400;
        text-transform: none;
        padding: 20px 0;
        font-size: 16px;
        color: $headings-color !important;        
        font-weight: $headings-font-weight;
        font-family: $font-family-second;                
        &.empty{
            border:none;            
            margin:0;
            padding-top:0;
            color: $headings-color;
            font-weight: $headings-font-weight;
            text-align: center;
            font-family: $headings-font-family;
            font-size: 22px;            
            + .buttons{
                @include justify-content(center);                
            }
        }
    }
    .buttons{
        @include flexbox();  
        .checkout{
            @include rtl-margin-left(auto);
        }
    }
}
.woocommerce a.remove{
    padding:0;
    margin: auto;
    color: $brand-danger;
    font-size: 32px;
    background:transparent;
}
/** Plugins  add to wishlist, compare **/
.place-order{
	padding: $theme-margin;
}
.input-text {
	border: 1px solid #e5e5e5;
	padding:  5px 10px;
}
.woocommerce{
	address{
		margin-bottom: 20px;
	}
}
.product-categories{
    list-style: none;
    margin: 0;
    font-size: 14px;
    padding:0;
    overflow:hidden;
    + .view-more-list-cat{
        position:absolute;
        background:$white;
        bottom:1px;
        left:1px;
        width: calc(100% - 2px);
        z-index: 99;
        display: block;
        color: $brand-success;
        padding:5px 54px 15px;
        &.view-less{
            color: $brand-danger;
        }
        &:hover,&:active{
            text-decoration: underline;
        }
    }
    &.hideContent{
        height: 435px;
    }
    &.showContent{
        height: auto;
    }
    .children{
        list-style: none;
        padding:0;
    }
    li{
        li{
            @include rtl-padding-left(20px);
        }
        line-height: 32px;
        &.current-cat-parent,
        &.current-cat,
        &:hover{
            > .count{
                color: $theme-color;
            }
            > a{
                color: $theme-color;
            }
        }
        .count{
            font-family: $font-family-base;
            font-size: 14px;
            font-weight: 400;
            display: inline-block;
            @include rtl-float-right();
            margin-top:3px;
        }
        a{
            @include transition(all 0.2s ease-in-out 0s);
            color: $text-color;
            &:hover,&:active{
                color: $theme-color;
            }
        }
        &.cat-parent{
            position:relative;
            > i{
                @include rtl-padding-right(12px);
                cursor: pointer;
                position:absolute;
                @include rtl-left(0);
                top:13px;
            }
        }
    }
}
.top-archive-shop{
    padding-bottom:$theme-margin;
}
//apus-results
.apus-results{
    margin-top:10px;
    .apus-results-reset{
        display:inline-block;
        padding:6px 15px;
        background:$brand-danger;
        color:$white;
        white-space:nowrap;
        font-weight:400;
        font-size:15px;
        @include transition(all 0.2s ease-in-out 0s);
        &:hover,&:active{
            color:$white;
            background:darken($brand-danger, 10%);
        }
    }
}
.ajax-pagination{
    text-align: center;
    margin:10px 0; 
    &.apus-loader{
        .apus-loadmore-btn{
            display: none;
        }
       &:after{
            background-image: url("data:image/svg+xml,%3Csvg xmlns=\"http://www.w3.org/2000/svg\" width=\"38\" height=\"38\" viewBox=\"0 0 38 38\" stroke=\"rgba(102,102,102,0.25)\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg transform=\"translate(1 1)\" stroke-width=\"2\"%3E%3Ccircle stroke-opacity=\".55\" cx=\"18\" cy=\"18\" r=\"18\"/%3E%3Cpath d=\"M36 18c0-9.94-8.06-18-18-18\"%3E%3CanimateTransform attributeName=\"transform\" type=\"rotate\" from=\"0 18 18\" to=\"360 18 18\" dur=\"1s\" repeatCount=\"indefinite\"/%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            background-position: center center;
            background-repeat: no-repeat;
            content: "";
            @include size(40px, 40px);
            display: block;
            width: 100%;
       }
    }
    .apus-loadmore-btn{
        +.apus-allproducts{
            display: none;
        }
        &.hidden{
            +.apus-allproducts{
                display: block;
                color:$brand-danger;
            }
        }
    }
}
.add-cart{
    >.added{
        display: none !important;
    }
    .added_to_cart{
        &:after{
            display: none;
        }
    }
}
.apus-shop-products-wrapper{
    &.loading{
        position:relative;
        &:before{
            background: url('#{$image-theme-path}loading-quick.gif') center 100px / 50px  no-repeat rgba($white, 0.9);
            position: absolute;
            width: 100%;
            height: 100%;
            content: "";
            left: 0;
            top: 0;
            z-index: 99;
        }
    }
}
// my account
.woocommerce-account .woocommerce-MyAccount-content,
.woocommerce-account .woocommerce-MyAccount-navigation{
    width: 100%;
    float: none;
}
.woocommerce-account .woocommerce-MyAccount-navigation{    
    .woocommerce-MyAccount-navigation-link{        
        @include inline-block();
        @include rtl-margin-right(30px);        
        a{
            padding: 0;
            position: relative;            
            @include inline-block();
            &:before{
                background: $theme-color;
                position: absolute;
                bottom:-2px;                
                content: '';
                @include rtl-left(0);
                @include size(100%,2px);                
                @include scale(0);
                @include transition(all 0.2s ease-in-out 0s);
            }
        }
        &.is-active,&:hover,&:active{
            > a{
                color: $theme-color;
                &:before{
                    @include scale(1);
                }
            }
        }
    }
}

.woocommerce-account-wrapper{
    border: 2px solid #edeff7;
    @include border-radius(5px);
    max-width: 945px;
    margin-left: auto;
    margin-right: auto;
    padding: 30px;
}

.woocommerce-MyAccount-content{
    padding: 40px 0; 
    h2{
        margin: 20px 0 10px;
        text-transform: uppercase;
        font-size: 18px;
        font-family: $font-family-second;
    }
}
.edit-account{
    br{
        display: none;
    }
    input[ type="text"],
    input[ type="password"]{
        height: 40px;
        @include border-radius(3px);
        &:focus{
            border-color: $border-color;
        }
    }
    legend{
        font-size: 72px;
        font-weight: 300;
        border:none;
        margin: 30px 0 0;
    }
    label{
        font-weight: normal;
        font-size: 16px;
        color: $link-color;
    }
}
.woocommerce-MyAccount-content,
.woocommerce-MyAccount-navigation{    
    .woocommerce-Message{
        margin-bottom: 0;
    }
}

.woocommerce-MyAccount-content{
    p{
        margin-bottom: 20px;
    }
    .form-group{
        span{
            display: inline-block;
            margin-top: 10px;
        }
    }
}

form.login,
form.register{
    margin: 0 !important;
    border: 0 !important;
    padding: 0 !important;
    .btn{
        height: 55px;        
        font-size: 19px;
        font-weight: 600;
        background-color: $theme-color-dark;
        font-family: $headings-font-family;
        text-transform: capitalize;
        border-color: $theme-color-dark;
        @include inline-block();     
        @include border-radius(5px);
        @include transition-all();
        @include hover-focus-active() {
            background-color: $theme-color-dark;
        }
    }
    .lost_password{
        a{
            outline: none;
            color: $product-price-color;
            text-decoration: underline;            
        }
    }
    br{
        display: none;
    }    
    label{
        font-weight: 400;
    }
    .form-group {
        margin: 0 0 20px;
        &:last-child{
            margin-bottom:0;
        }
        .topmenu-menu{
            @include flexbox();            
            > li{                        
                a{
                    background-color: transparent;
                    color: $text-color;
                    font-size: 16px;                    
                    font-family: $headings-font-family;                                        
                    @include inline-block();         
                    @include hover-focus-active() {
                        color: $theme-color;
                    } 
                    [class*="icon"]{
                        &:before,
                        &:after{
                            font-size: 24px;
                            @include rtl-margin-right(0px);
                            @include rtl-margin-left(0);
                        }
                    }
                }   
                &.register{
                    @include rtl-margin-left(auto);
                    a{
                        [class*="icon"]{
                            &:before,
                            &:after{
                                font-size: 26px;                            
                            }
                        }
                    }                    
                }
            }
        }
    }
    .input-text{
        background:$white !important;
        border:1px solid $border-color !important;                
    }
    input[ type="checkbox"]{
        @include rtl-margin-right(7px);
    }
    .input-submit {
        ~ span{
            margin:10px 0 0;
            &.pull-left{
                @include rtl-margin-left(15px);
            }
            &.lost_password{
                a{
                    color: $theme-color;
                }
            }
        }
    }
    .user-role{
        padding-left:20px;
        [type="radio"]{
            margin-top:11px;
        }
    }    
}
.login-wrapper{
    .mfp-content{
        width: 550px !important;
        max-width: 80%;
        background-color: $white;
        border: 0;
        @include border-radius(5px);
        .header_customer_login{
            padding: 40px;
            .btn{
                outline: none;
                border: 0;
                font-weight: 700;
                font-size: 18px;
                line-height: normal;
                padding: 16px 30px;
                text-transform: capitalize;
                font-family: $headings-font-family;
                @include border-radius(5px);
                background-color: darken($theme-color, 30%);
            }
        }
    }
    .title{
        text-align: center;
    }
    .apus-mfp-close{
        font-size: 20px;        
        background-color: $theme-color;
        color: $white;        
        line-height: normal;
        border:none;
        margin:-25px;
        @include square(50px);
        @include flexbox();
        @include align-items(center);
        @include justify-content(center);
        @include border-radius(50%);
        @include transition-all();        
        @include opacity(1);
        @include hover-focus-active() {
            outline: none;
        }        
        .icon-theme{
            display: block;
            background-image: url("../images/close-icon.png");
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: cover;
            @include size(18px,19px);
        }
    }
}
//cart
.cart_item{
    margin: 0 0 20px;
    padding:0 0 20px;
    border-bottom: 1px solid $border-color;
    > .media-left{
        width: 70%;
    }
    img{
        width: 90px;
        max-width:none;
    }
    .content-left{
        overflow: hidden;
        @include rtl-padding-left(20px);
    }
    .product-name{
        font-size: 18px;
        font-weight: 400;
        margin: 0 0 15px;
    }
    .price{
        font-size: 20px;
        font-family: $font-family-second;
        color: #4c4c4c;
        font-weight: 400;
    }
    a.remove{
        margin: 0 0 15px;
        display: inline-block;
        font-size: 32px;
        color: $text-color !important;
        &:hover,&:active{
            color: $brand-danger !important;
        }
    }
}
div.cart{
    .input-text {
        height: 53px;
        border:2px solid $border-color;
        &:focus,&:active{
            border-color:$link-color;
        }
    }
    label{
        font-size: 18px;
        color: #000;
    }
}
//order_review
.woocommerce .order-review{
    #order_review_heading{
        font-size: 20px;
        font-weight: 600;
        text-transform: none;
        margin: 0; 
        padding: 0;
        color: $headings-color;
    }
    table.shop_table{
        margin:0;
    }
}
#order_review{
    .shop_table{
        border:none;
        margin-bottom:25px;
        td{
            padding:25px 0;            
            border-width:0 0 1px;
            border-style:solid;
            border-color:$border-color;
        }
    }
    .cart_item {
        margin:0;
        padding:0;
        border:none;
    }
    .product-name{
        margin: 0;
        font-size: 16px;
        line-height: 28px;
        strong{
            font-weight: 400;
        }
    }
    .product-total{
        font-weight: 600;
        color: $theme-color;
    }
    > .media-left {
        width: auto;
    }
    .woocommerce-Price-amount{
        color: $link-color;
        font-weight: 600;
    }
    .subtotal{
        tr{
            > *{
                border-bottom:1px solid $border-color !important;
            }
        }
        th{
            border:none;
            font-weight: 600;
            color:$link-color;
            text-transform: none;
            font-size: 18px;
            font-family: $headings-font-family;
        }
        td{            
            padding:10px 0;
            font-weight: 400;
            @include rtl-text-align-right();
            label{
                font-weight: 400;
            }
        }
        .order-total{
            strong{
                font-size: 20px;
            }
        }
        .amount{
            font-weight:600;
        }
    }
    .order-total,
    .cart-subtotal{
        .amount{
            font-family: $headings-font-family;
            color: $product-price-color;            
        }
    }
}

.order-total{
    .amount{
        font-size: 22px;
    }
}

// step check out
.apus-checkout-step{
    padding:0 0 30px;
    ul{
        padding:0;
        list-style:none;
        margin:0 auto;
        text-transform: uppercase;
        width: 100%;
        li{
            position:relative;
            text-align: center;
            @include rtl-float-left();
            @media(min-width:768px) {
                width: 33.33%;
            }
        }
    }
    li{
        font-size: 20px;
        font-weight: 500;
        color: #232530;   
        line-height: 60px;
        overflow: hidden;
        position: relative;
        background: $white;
        &:first-child{
            &:before{
                display:none;
            }
            &:after{
                border-width: 1px 0 1px 1px;
            }
        }
        &:before{
            content:'';
            z-index: 1;
            position: absolute;
            top: 0px;
            left:-43px;
            border:1px solid $border-color;
            @include size(60px,60px);
            @include rotate(45deg);
            background: $white;
        }
        &:after{
            position:absolute;
            content:'';
            border-width: 1px 0;
            border-style: solid;
            border-color: $border-color;
            @include size(calc(100% - 30px),100%);
            z-index: 5;
            @include rtl-left(0);
            top:0;
        }
        .inner{
            position: relative;
            &:after{
                content:'';
                z-index: 1;
                position: absolute;
                top: 0px;
                right:-30px;
                border-style: solid;
                border-color:$white $white $white transparent;
                border-width: 30px;
                @include size(60px,60px);
                background: $white;
            }
            &:before{
                content:'';
                z-index: 1;
                position: absolute;
                top: 0px;
                right:12px;
                border:1px solid $border-color;
                border-width: 1px 1px 0 0;
                @include size(60px,60px);
                @include rotate(45deg);
                background: $white;
                z-index: 2;
            }
        }
        &.active{
            background: $theme-color;
            color: $white;
            &:after{
                border-color:$theme-color;
            }
            .inner:after{
                border-color:$white $theme-color;
            }
            .inner:before{
                display: none;
            }
            .step{
                @include opacity(0.1);
                color: $white;
            }
        }
    }
    .inner-step{
        position: relative;
        z-index: 7;
    }
    .step{
        z-index: 6;
        position: absolute;
        top: -1px;
        @include rtl-right(70px);
        line-height: 60px;
        font-size: 48px;
        text-transform: uppercase;
        font-weight: 600;
        display: inline-block;
        text-align: center;
        color: #eae9ec;
    }
}
.woocommerce-thankyou-order-received{
    font-size: 18px;
    font-weight: 400;
    margin-bottom:$theme-margin;
    text-align:center;
    color: $headings-color;
    @media(min-width:768px) {
        font-size: 28px;
    }
}
.woocommerce-thankyou-order-details{
    text-align: center;
}
.woocommerce ul.order_details li{
    float: none;
    display: inline-block;
    font-size: 12px;
    strong{
        margin-top: 5px;
        font-weight: 400;
        color:$link-color;
    }
    &.method{
        strong{
            color: $brand-danger;
        }
    }
}
.woo-pay-perfect{
    font-size: 18px;
    font-weight: 600;
    text-align:center;
    margin-bottom: 20px;
}
.product-top-title{
    position: relative;
    .view-more{
        position:absolute;
        top: 5px;
        @include rtl-right(0);
    }
}
.layout-detail-product{
    #tabs-list-specifications{
        td{
            padding:15px;
            border-color:#eff0f2;
        }
        td:first-child{
            font-weight: 500;
            text-transform: uppercase;
        }
    }
}
.accessoriesproducts{
    .list-accesories{
        margin-bottom: 10px;
    }
    .check-item{
        margin-top: 10px;
    }
}
// style for vendors
.wcv-pro-vendorlist{
    margin:0 0 30px;
    padding:0 0 30px;
    border-bottom: 1px solid $border-color;
    border-top: none;
    background: $white;
    height: auto;
    .name-store{
        font-size: 18px;
        margin:10px 0;
    }
    &:hover{
        .avatar:before{
            @include opacity(0.5);
        }
    }
    .avatar{
      display: inline-block;
      position: relative;
      line-height: 0;
      max-width: 100%;
      &:before{
        @include transition(all 0.2s ease 0s);
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        @include size(100%,100%);
        background:$theme-color;
        @include opacity(0);
      }  
    }
    .metas{
        margin:0 0 5px; 
        > *{
            display: inline-block;
            font-size: 14px;
            + *{
                @include rtl-margin-left(20px);
            }
        }
        .total-value{
            font-weight: normal;
        }
    }
    .store-address,
    .store-phone{
        font-size: 14px;
        margin:0 0 7px;
        &:last-child{
            margin: 0;
        } 
    }
}
.pv_shop_description{
    padding:0 ($theme-padding / 2) $theme-padding;
}
.wcv-header-container {
    padding-right: 15px;
    padding-left: 15px;
    margin-bottom: $theme-margin;
    background:url('#{$image-theme-path}bg-vendor.jpg') repeat rgba(255,255,255,0.9);
    .store-banner{
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
    }
    .wcv-store-grid{
        padding:0 0 30px 0;
    }
    #inner-element{
        background: transparent none repeat scroll 0 0;
        clear: both;
        overflow: hidden;
        position: static;
        max-width: none;
        width: 100%;
        padding:0;
        .store-info{
            text-align:inherit;
        }
    }
}
.store-info{
    .title-store{
        display: inline-block;
    }
    .wcv-verified-vendor{
        display: inline-block;
        vertical-align: top;
        margin: 0 15px;
        font-size: 12px;
        color: $white;
        background:#4a90de;
        padding:5px 18px;
        @include border-radius(20px);
    }
    .social-icons{
        list-style: none;
        margin:25px 0 0 !important;
        padding:0;
        li{
            display: inline-block;
            @include rtl-margin-right(10px);
            a{
                display: inline-block;
                @include border-radius(50%);
                border:1px solid #405e9c;
                @include size(40px,40px);
                font-size: 16px;
                line-height: 38px;
                text-align:center;
                &.facebook{
                    border:1px solid #405e9c;
                    color: #405e9c !important;
                    &:hover,&:active{
                        background:#405e9c;
                    }
                }
                &.twitter{
                    border:1px solid #55acee;
                    color: #55acee !important;
                    &:hover,&:active{
                        background:#55acee;
                    }
                }
                &.instagram{
                    border:1px solid #5280a5;
                    color: #5280a5 !important;
                    &:hover,&:active{
                        background:#5280a5;
                    }
                }
                &.googleplus{
                    color: #cd2129 !important;
                    border:1px solid #cd2129;
                    &:hover,&:active{
                        background:#cd2129;
                    }
                }
                &.linkedin{
                    color: #318dc1 !important;
                    border:1px solid #318dc1;
                    &:hover,&:active{
                        background:#318dc1;
                    }
                }
                &.youtube{
                    color: #cb312e !important;
                    border:1px solid #cb312e;
                    &:hover,&:active{
                        background:#cb312e;
                    }
                }
                &:hover,&:active{
                    color: $white !important;
                    background:#405e9c;
                }
            }
        }
    }
    .title-store{
        font-size: 24px;
        margin:0 0 10px 0;
        line-height: 1.1;
    }
    .rating-products-wrapper{
        margin: 0 0 20px;
        font-size: 16px;
        > *{
            display: inline-block;
            vertical-align: top;
            > *{
                display: block;
            }
        }
        .total-label{

        }
    }
    .store-address{
        address{
            margin: 0;
        }
    }
    .store-address,
    .store-phone{
        i{
            @include rtl-margin-right(8px);
        }
    }
    .total-products{
        @include rtl-padding-left(50px);
        .total-value{
            font-size: 24px;
            color: #242424;
            font-weight: normal;
            line-height: 1.1;
        }
    }
    .media-body{
        max-width: 600px;
        font-size: 14px;
    }
    .media-left {
        @include rtl-padding-right(30px);
    }
    // add favourite
    .favourite-wrapper{
        clear: both;
        overflow: hidden;
        width: 100%;
        padding: 10px 0 0;
    }
    .denso-favourite-vendor{
        border-color:$border-color;
        font-size: 12px;
        display: block;
        font-weight: 400;
        padding:8px 15px;
        text-transform: capitalize;
        &:hover,&:active{
            border-color:$theme-color;
        }
        i{
            @include rtl-margin-right(3px);
        }
        &.added{
            color: $white;
            border-color:$theme-color;
            background:$theme-color;
        }
    }
}
.store-aurhor-inner{
    text-align:center;
    margin-top: $theme-margin;
    .avatar{
        @include border-radius(50%);
        border:2px solid #e8e8e8;
        overflow: hidden;
    }
    .store-aurhor{
        .name-author{
            margin:5px 0 10px; 
        }
    }
}
.vendor-reviews-inner{
    background:#f8f8f8;
    border:2px solid $border-color;
    padding:20px;
    .title-info{
        font-size: 16px !important;
        margin:10px 0 20px  !important;
    }
    .star-rating{
        @include rtl-float-left();
        @include rtl-margin-right(40px);
    }
    .special-progress {
        > *{
            display: inline-block;
            vertical-align: top;
        }
        .progress{
            width: 210px;
            margin:0;
            .progress-bar{
                @include border-radius(0);
            }
        }
        .value{
            margin:0 8px;
            line-height: 1;
        }
    }
    .average-value{
        font-size: 30px;
        font-weight: normal;
        color: #242424;
        display: inline-block;
        @include size(100px,100px);
        @include border-radius(50%);
        text-align:center;
        line-height: 1.2;
        border:1px solid $border-color;
        padding:20px 5px;
        span{
            font-size: 12px;
            font-weight: 400;
            display: block;
        }
    }
}
.special-progress{
    margin: 5px 0 0;
    .claimed{
        strong{
            color: $link-color;
        }
        margin-bottom: 2px;
    }
    .progress{
        background: #eaeaea;
        @include border-radius(50px);
        height: 12px;
        margin: 5px 0 17px;
        .progress-bar{
            background: $theme-color;
            @include border-radius(50px);
            @include gradient-striped();
            background-size: 10px 10px;
        }
    }
}
.single-rating{
    margin:0 0 30px;
    padding:0 0 20px;
    border-bottom:1px solid $border-color;
    &:last-child{
        border:none;
        padding:0;
        margin: 0;
    }
    .avatar{
        max-width: none;
        min-width: 70px;
        @include border-radius(50%);
    }
    .media-left{
        @include rtl-padding-right(20px);
    }
    .stars-value{
        @include rtl-float-right();
        .fa-star{
            color: #fednormal;
        }
    }
    h4{
        font-weight: 400;
        font-size: 10px;
        margin: 0 0 15px;
        color: $text-color;
        .name{
            font-weight: normal;
            font-size: 12px;
            color: #464646;
            text-transform: uppercase;
        }
    }
    h6{
        margin:0 0 15px; 
    }
}
// style for dokan mutivendors
.btn-showserach-dokan{
    cursor: pointer;
}
.wrapper-dokan{
    .btn-showserach-dokan {
        padding: 6px 9px;
        @include border-radius(50px);
        border-width: 2px;
    }
    .dokan-seller-search-form{
        font-size: 14px;
        margin:0;
        width: 0;
        overflow: hidden;
        @include transition(all 0.2s ease-in-out 0s);
        input{
            width: 100% !important;
            padding-top: 3px !important;
            padding-bottom: 3px !important;
        }
        &.active{
            width: 220px;
        }
    }
    > *{
        display: inline-block;
        vertical-align: top;
        @include rtl-margin-right(10px);
    }
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li{
    margin-bottom: $theme-margin;
}
.dokan-list-inline{
    > li{
        > a{
            font-family: $headings-font-family;
        }
    }
}
.dokan-widget-area,
.dokan-store-menu{
    #cat-drop-stack > ul{
        list-style: none;
        padding:0;
        li {
            margin-bottom: 5px;
            &:last-child{
                margin-bottom:0;
            }
        }
        a:hover,&:focus{
            color: $theme-color;
        }
    }
}
.dokan-single-store .profile-frame.profile-frame-no-banner .profile-layout-layout3 .profile-info-summery-wrapper .profile-info-summery .profile-info .store-name{
    font-weight: 500;
}
.dokan-single-store .profile-info .dokan-store-info{
    list-style: none;
    font-size: 14px;
    li{
        float: none !important;
        &:before{
            display:none;
        }
    }
}
.dokan-store-location,
.dokan-store-contact{
    list-style: none;
    ul{
        list-style: none;
        padding:0;
    }
}
.dokan-store-tabss{
    .dokan-right{
        margin:0;
        margin-top: 10px;
    }
    margin-bottom: 20px;
    @media(min-width: 768px) {
        margin-bottom: 30px;
    }
}
.dokan-store-sidebar #dokan-store-location{
    height: 200px;
    width: 100%;
}
// style for gift
.wfg-popup{
    border:0 !important;
}
.wfg-popup h2.wfg-title{
    background:$theme-color;
    color: $white;
    @include border-radius(0);
    @include box-shadow(none);
}
.wfg-button{
    @extend .btn;
    &.wfg-add-gifts{
        @extend .btn-success;
    }
    &.wfg-no-thanks{
        @extend .btn-danger;
    }
}
.wfg-gifts .wfg-gift-item h3{
    background:$white;
    color: $link-color;
    border-top:1px solid $border-color;
}
.wfg-gifts .wfg-gift-item{
    border:1px solid $border-color;
}
// fix dokan vendors
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-footer .seller-avatar img{
    margin:0;
}
#dokan-seller-listing-wrap{
    ul.dokan-seller-wrap {
        .btn{
            @include border-radius(50px);
            border-width:2px;
            padding:8px 30px;
        }
    }
}
#dokan-seller-listing-wrap ul.dokan-seller-wrap li .store-wrapper{
    @include transition(all 0.3s ease-in-out 0s);
    &:hover{
        @include box-shadow(0px 0px 35px 0px rgba(0,0,0,0.2));
    }
}
.product-block{
    .wcvendors_sold_by_in_loop{
        position:absolute;
        z-index: 99;
        top:0;
        @include rtl-left(0);
        @include size(50px,50px);
        img{
            max-width: 100%;
            max-height: 100%;
            @include border-radius(50%);
        }
    }
}
.seller-info-social{
    list-style: none;
    li{
        display: inline-block;
        @include rtl-margin-right(20px);
    }
}
.seller-info-top{
    margin-bottom: 20px;
    .store-brand img{
        max-width: none;
    }
}
// accoudion for detail
#woocommerce-accordion{
    .panel{
        margin:0;
        border:none;
        border-bottom:1px solid $border-color;
        @include border-radius(0);
        > .panel-heading{
            text-transform: uppercase;
            border:none;
            padding:18px 0;
            font-weight: 400;
            font-size: 16px;
            @include border-radius(0);
            background: $white !important;
            &:hover,&:active{
                a{
                    color: $theme-color;
                }
            }
        }
        .panel-title{
            font-size: 16px;
            font-weight: 400;
            > :not(.collapsed) {
                color: $theme-color;
            }
        }
        .panel-body{
            padding:0;
            border:none;
        }
    }
    .title{
        margin:0 0 10px;
        font-size: 24px;
    }
}
//categories
.wrapper-filter{
    min-height:73px;
    position:relative;
    padding:20px 0;
    border-bottom:1px solid $border-color;
}
.shop-top-sidebar-wrapper{
    background:$white;
    padding:20px 0 0;
    @media(min-width:992px) {
        padding:40px 0 0;
    }
    display:block;
    overflow:hidden;
    width:100% !important;
    .dropdown{
        > span{
            color:#252525;
            font-weight:500;
            font-size:15px;
            display:block;
            margin:0 0 15px;
            text-transform:uppercase;
        }
    }
    .widget{
        margin-bottom:0;
    }
    @media(max-width:767px) {
        margin-bottom:15px;
    }
    .shop-top-sidebar-wrapper-inner {
        margin-left:-15px;
        margin-right:-15px;
        > *{
            padding-left:15px;
            padding-right:15px;
            @include rtl-float-left();
            width:100%;
            @media(min-width:768px) {
                width:20%;
            }
        }
    }
    .wrapper-limit{
        padding:10px;
        .apus-product-sorting,
        .apus-price-filter{
            padding:0;
            margin:0;
            list-style:none;
            li{
                margin-bottom:8px;
                &:last-child{
                    margin:0;
                }
            }
            a{
                white-space:nowrap;
            }
            .active,
            .current{
                color:$theme-color;
            }
        }
        .apus-product-sorting,
        .apus-price-filter,
        .woocommerce-widget-layered-nav-list{
            height:200px;
        }
    }
    .tagcloud {
        height:200px;
    }
}
// fix for shop banner
.products-wrapper-grid-banner{
    .cl-3,
    .cl-2{
        div.product{
            &.col-sm-4{
                &.first{
                    clear:none;
                }
                @media(min-width:768px) {
                    &:nth-child(3n + 1) {
                        clear:both;
                    }
                }
            }
        }
    }
    .col-md-cus-5{
        @include rtl-float-left();
        padding-left:$theme-margin / 2;
        padding-right:$theme-margin / 2;
        @media(min-width:992px) {
            width:20%;
        }
    }
}
// style for bundles
.product-bundles{
    padding:20px 0;
    .product-item{
        overflow:hidden;
        clear:both;
        margin-bottom:10px;
        .product-image{
            @include rtl-float-left();
            width:80px;
            @include rtl-padding-right(10px);
        }
        .product-content{
            overflow:hidden;
        }
        .product-name{
            display:block;
            color:$link-color;
            margin:3px 0;
        }
    }
    .total{
        padding-bottom:10px;
    }
    .total-discount{
        color:$brand-success;
    }
    .total-price{
        font-size:18px;
        font-weight:500;
        color:$link-color;
    }
}
// categories
.product-category {
   h3{
    margin:15px 0 0;
    font-size: 18px;
    .count{
        background:transparent;
        padding:0;
    }
   } 
   .category-body{
        margin:0 0 20px;
        text-align: center;
        @media(min-width: 768px) {
            margin:0 0 30px;
        }
   }
}