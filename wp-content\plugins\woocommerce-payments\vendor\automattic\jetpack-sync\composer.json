{"name": "automattic/jetpack-sync", "description": "Everything needed to allow syncing to the WP.com infrastructure.", "type": "jetpack-library", "license": "GPL-2.0-or-later", "require": {"automattic/jetpack-connection": "^1.51.7", "automattic/jetpack-constants": "^1.6.22", "automattic/jetpack-identity-crisis": "^0.8.43", "automattic/jetpack-password-checker": "^0.2.13", "automattic/jetpack-ip": "^0.1.2", "automattic/jetpack-roles": "^1.4.23", "automattic/jetpack-status": "^1.16.4"}, "require-dev": {"automattic/jetpack-changelogger": "^3.3.2", "yoast/phpunit-polyfills": "1.0.4", "automattic/wordbless": "@dev"}, "suggest": {"automattic/jetpack-autoloader": "Allow for better interoperability with other plugins that use this package."}, "autoload": {"classmap": ["src/"]}, "scripts": {"phpunit": ["./vendor/phpunit/phpunit/phpunit --colors=always"], "test-php": ["@composer phpunit"], "post-install-cmd": "WorDBless\\Composer\\InstallDropin::copy", "post-update-cmd": "WorDBless\\Composer\\InstallDropin::copy"}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"autotagger": true, "mirror-repo": "Automattic/jetpack-sync", "textdomain": "jetpack-sync", "version-constants": {"::PACKAGE_VERSION": "src/class-package-version.php"}, "changelogger": {"link-template": "https://github.com/Automattic/jetpack-sync/compare/v${old}...v${new}"}, "branch-alias": {"dev-trunk": "1.47.x-dev"}}, "config": {"allow-plugins": {"roots/wordpress-core-installer": true}}}