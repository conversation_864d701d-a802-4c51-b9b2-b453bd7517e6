<?php

// This file `jetpack_autoload_classmap.php` was auto generated by automattic/jetpack-autoloader.

$vendorDir = dirname(__DIR__);
$baseDir   = dirname($vendorDir);

return array(
	'Autoloader' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader.php'
	),
	'Autoloader_Handler' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-handler.php'
	),
	'Autoloader_Locator' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-autoloader-locator.php'
	),
	'Automattic\\Jetpack\\A8c_Mc_Stats' => array(
		'version' => '1.4.20.0',
		'path'    => $vendorDir . '/automattic/jetpack-a8c-mc-stats/src/class-a8c-mc-stats.php'
	),
	'Automattic\\Jetpack\\Admin_UI\\Admin_Menu' => array(
		'version' => '0.2.20.0',
		'path'    => $vendorDir . '/automattic/jetpack-admin-ui/src/class-admin-menu.php'
	),
	'Automattic\\Jetpack\\Assets' => array(
		'version' => '1.18.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-assets/src/class-assets.php'
	),
	'Automattic\\Jetpack\\Assets\\Logo' => array(
		'version' => '1.6.1.0',
		'path'    => $vendorDir . '/automattic/jetpack-logo/src/class-logo.php'
	),
	'Automattic\\Jetpack\\Assets\\Semver' => array(
		'version' => '1.18.4.0',
		'path'    => $vendorDir . '/automattic/jetpack-assets/src/class-semver.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadFileWriter' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadFileWriter.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadGenerator' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadGenerator.php'
	),
	'Automattic\\Jetpack\\Autoloader\\AutoloadProcessor' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/AutoloadProcessor.php'
	),
	'Automattic\\Jetpack\\Autoloader\\CustomAutoloaderPlugin' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/CustomAutoloaderPlugin.php'
	),
	'Automattic\\Jetpack\\Autoloader\\ManifestGenerator' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/ManifestGenerator.php'
	),
	'Automattic\\Jetpack\\Config' => array(
		'version' => '1.15.2.0',
		'path'    => $vendorDir . '/automattic/jetpack-config/src/class-config.php'
	),
	'Automattic\\Jetpack\\Connection\\Client' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-client.php'
	),
	'Automattic\\Jetpack\\Connection\\Connection_Notice' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-connection-notice.php'
	),
	'Automattic\\Jetpack\\Connection\\Error_Handler' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-error-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Initial_State' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-initial-state.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Manager_Interface' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/interface-manager.php'
	),
	'Automattic\\Jetpack\\Connection\\Nonce_Handler' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-nonce-handler.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Connection\\Package_Version_Tracker' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-package-version-tracker.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-plugin.php'
	),
	'Automattic\\Jetpack\\Connection\\Plugin_Storage' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-plugin-storage.php'
	),
	'Automattic\\Jetpack\\Connection\\REST_Connector' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-rest-connector.php'
	),
	'Automattic\\Jetpack\\Connection\\Rest_Authentication' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-rest-authentication.php'
	),
	'Automattic\\Jetpack\\Connection\\Secrets' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-secrets.php'
	),
	'Automattic\\Jetpack\\Connection\\Server_Sandbox' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-server-sandbox.php'
	),
	'Automattic\\Jetpack\\Connection\\Tokens' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-tokens.php'
	),
	'Automattic\\Jetpack\\Connection\\Urls' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-urls.php'
	),
	'Automattic\\Jetpack\\Connection\\Utils' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-webhooks.php'
	),
	'Automattic\\Jetpack\\Connection\\Webhooks\\Authorize_Redirect' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/webhooks/class-authorize-redirect.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Async_Call' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-async-call.php'
	),
	'Automattic\\Jetpack\\Connection\\XMLRPC_Connector' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-xmlrpc-connector.php'
	),
	'Automattic\\Jetpack\\Constants' => array(
		'version' => '1.6.22.0',
		'path'    => $vendorDir . '/automattic/jetpack-constants/src/class-constants.php'
	),
	'Automattic\\Jetpack\\CookieState' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-cookiestate.php'
	),
	'Automattic\\Jetpack\\Errors' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-errors.php'
	),
	'Automattic\\Jetpack\\Files' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-files.php'
	),
	'Automattic\\Jetpack\\Heartbeat' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-heartbeat.php'
	),
	'Automattic\\Jetpack\\IP\\Utils' => array(
		'version' => '*******',
		'path'    => $vendorDir . '/automattic/jetpack-ip/src/class-utils.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\REST_Endpoints' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-identity-crisis/src/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\IdentityCrisis\\UI' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-identity-crisis/src/class-ui.php'
	),
	'Automattic\\Jetpack\\Identity_Crisis' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-identity-crisis/src/class-identity-crisis.php'
	),
	'Automattic\\Jetpack\\Modules' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-modules.php'
	),
	'Automattic\\Jetpack\\Password_Checker' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-password-checker/src/class-password-checker.php'
	),
	'Automattic\\Jetpack\\Paths' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-paths.php'
	),
	'Automattic\\Jetpack\\Redirect' => array(
		'version' => '1.7.25.0',
		'path'    => $vendorDir . '/automattic/jetpack-redirect/src/class-redirect.php'
	),
	'Automattic\\Jetpack\\Roles' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-roles/src/class-roles.php'
	),
	'Automattic\\Jetpack\\Status' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-status.php'
	),
	'Automattic\\Jetpack\\Status\\Cache' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-cache.php'
	),
	'Automattic\\Jetpack\\Status\\Host' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-host.php'
	),
	'Automattic\\Jetpack\\Status\\Visitor' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-status/src/class-visitor.php'
	),
	'Automattic\\Jetpack\\Sync\\Actions' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-actions.php'
	),
	'Automattic\\Jetpack\\Sync\\Codec_Interface' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/interface-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Data_Settings' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-data-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Dedicated_Sender' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-dedicated-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Default_Filter_Settings' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-default-filter-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Defaults' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-defaults.php'
	),
	'Automattic\\Jetpack\\Sync\\Functions' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-functions.php'
	),
	'Automattic\\Jetpack\\Sync\\Health' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-health.php'
	),
	'Automattic\\Jetpack\\Sync\\JSON_Deflate_Array_Codec' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-json-deflate-array-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Listener' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-listener.php'
	),
	'Automattic\\Jetpack\\Sync\\Lock' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-lock.php'
	),
	'Automattic\\Jetpack\\Sync\\Main' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-main.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-modules.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Attachments' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-attachments.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Callables' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-callables.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Comments' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-comments.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Constants' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-constants.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-full-sync.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Full_Sync_Immediately' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-full-sync-immediately.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Import' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-import.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Menus' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-menus.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Meta' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-meta.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Module' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-module.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Network_Options' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-network-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Options' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-options.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Plugins' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-plugins.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Posts' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-posts.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Protect' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-protect.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Search' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-search.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Stats' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-stats.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Term_Relationships' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-term-relationships.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Terms' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-terms.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Themes' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-themes.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Updates' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-updates.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\Users' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WP_Super_Cache' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-wp-super-cache.php'
	),
	'Automattic\\Jetpack\\Sync\\Modules\\WooCommerce' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/modules/class-woocommerce.php'
	),
	'Automattic\\Jetpack\\Sync\\Package_Version' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-package-version.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-queue.php'
	),
	'Automattic\\Jetpack\\Sync\\Queue_Buffer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-queue-buffer.php'
	),
	'Automattic\\Jetpack\\Sync\\REST_Endpoints' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-rest-endpoints.php'
	),
	'Automattic\\Jetpack\\Sync\\REST_Sender' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-rest-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-replicastore.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/replicastore/class-table-checksum.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Usermeta' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/replicastore/class-table-checksum-usermeta.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore\\Table_Checksum_Users' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/replicastore/class-table-checksum-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Replicastore_Interface' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/interface-replicastore.php'
	),
	'Automattic\\Jetpack\\Sync\\Sender' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-sender.php'
	),
	'Automattic\\Jetpack\\Sync\\Server' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-server.php'
	),
	'Automattic\\Jetpack\\Sync\\Settings' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-settings.php'
	),
	'Automattic\\Jetpack\\Sync\\Simple_Codec' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-simple-codec.php'
	),
	'Automattic\\Jetpack\\Sync\\Users' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-users.php'
	),
	'Automattic\\Jetpack\\Sync\\Utils' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-sync/src/class-utils.php'
	),
	'Automattic\\Jetpack\\Terms_Of_Service' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-terms-of-service.php'
	),
	'Automattic\\Jetpack\\Tracking' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/src/class-tracking.php'
	),
	'Composer\\Installers\\AglInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AglInstaller.php'
	),
	'Composer\\Installers\\AimeosInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AimeosInstaller.php'
	),
	'Composer\\Installers\\AnnotateCmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AnnotateCmsInstaller.php'
	),
	'Composer\\Installers\\AsgardInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AsgardInstaller.php'
	),
	'Composer\\Installers\\AttogramInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/AttogramInstaller.php'
	),
	'Composer\\Installers\\BaseInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BaseInstaller.php'
	),
	'Composer\\Installers\\BitrixInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BitrixInstaller.php'
	),
	'Composer\\Installers\\BonefishInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/BonefishInstaller.php'
	),
	'Composer\\Installers\\CakePHPInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CakePHPInstaller.php'
	),
	'Composer\\Installers\\ChefInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ChefInstaller.php'
	),
	'Composer\\Installers\\CiviCrmInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CiviCrmInstaller.php'
	),
	'Composer\\Installers\\ClanCatsFrameworkInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ClanCatsFrameworkInstaller.php'
	),
	'Composer\\Installers\\CockpitInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CockpitInstaller.php'
	),
	'Composer\\Installers\\CodeIgniterInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CodeIgniterInstaller.php'
	),
	'Composer\\Installers\\Concrete5Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Concrete5Installer.php'
	),
	'Composer\\Installers\\CraftInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CraftInstaller.php'
	),
	'Composer\\Installers\\CroogoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/CroogoInstaller.php'
	),
	'Composer\\Installers\\DecibelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DecibelInstaller.php'
	),
	'Composer\\Installers\\DframeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DframeInstaller.php'
	),
	'Composer\\Installers\\DokuWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DokuWikiInstaller.php'
	),
	'Composer\\Installers\\DolibarrInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DolibarrInstaller.php'
	),
	'Composer\\Installers\\DrupalInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/DrupalInstaller.php'
	),
	'Composer\\Installers\\ElggInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ElggInstaller.php'
	),
	'Composer\\Installers\\EliasisInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/EliasisInstaller.php'
	),
	'Composer\\Installers\\ExpressionEngineInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ExpressionEngineInstaller.php'
	),
	'Composer\\Installers\\EzPlatformInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/EzPlatformInstaller.php'
	),
	'Composer\\Installers\\FuelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/FuelInstaller.php'
	),
	'Composer\\Installers\\FuelphpInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/FuelphpInstaller.php'
	),
	'Composer\\Installers\\GravInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/GravInstaller.php'
	),
	'Composer\\Installers\\HuradInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/HuradInstaller.php'
	),
	'Composer\\Installers\\ImageCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ImageCMSInstaller.php'
	),
	'Composer\\Installers\\Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Installer.php'
	),
	'Composer\\Installers\\ItopInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ItopInstaller.php'
	),
	'Composer\\Installers\\JoomlaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/JoomlaInstaller.php'
	),
	'Composer\\Installers\\KanboardInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KanboardInstaller.php'
	),
	'Composer\\Installers\\KirbyInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KirbyInstaller.php'
	),
	'Composer\\Installers\\KnownInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KnownInstaller.php'
	),
	'Composer\\Installers\\KodiCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KodiCMSInstaller.php'
	),
	'Composer\\Installers\\KohanaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/KohanaInstaller.php'
	),
	'Composer\\Installers\\LanManagementSystemInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LanManagementSystemInstaller.php'
	),
	'Composer\\Installers\\LaravelInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LaravelInstaller.php'
	),
	'Composer\\Installers\\LavaLiteInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LavaLiteInstaller.php'
	),
	'Composer\\Installers\\LithiumInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/LithiumInstaller.php'
	),
	'Composer\\Installers\\MODULEWorkInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MODULEWorkInstaller.php'
	),
	'Composer\\Installers\\MODXEvoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MODXEvoInstaller.php'
	),
	'Composer\\Installers\\MagentoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MagentoInstaller.php'
	),
	'Composer\\Installers\\MajimaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MajimaInstaller.php'
	),
	'Composer\\Installers\\MakoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MakoInstaller.php'
	),
	'Composer\\Installers\\MantisBTInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MantisBTInstaller.php'
	),
	'Composer\\Installers\\MauticInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MauticInstaller.php'
	),
	'Composer\\Installers\\MayaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MayaInstaller.php'
	),
	'Composer\\Installers\\MediaWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MediaWikiInstaller.php'
	),
	'Composer\\Installers\\MicroweberInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MicroweberInstaller.php'
	),
	'Composer\\Installers\\ModxInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ModxInstaller.php'
	),
	'Composer\\Installers\\MoodleInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/MoodleInstaller.php'
	),
	'Composer\\Installers\\OctoberInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OctoberInstaller.php'
	),
	'Composer\\Installers\\OntoWikiInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OntoWikiInstaller.php'
	),
	'Composer\\Installers\\OsclassInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OsclassInstaller.php'
	),
	'Composer\\Installers\\OxidInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/OxidInstaller.php'
	),
	'Composer\\Installers\\PPIInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PPIInstaller.php'
	),
	'Composer\\Installers\\PhiftyInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PhiftyInstaller.php'
	),
	'Composer\\Installers\\PhpBBInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PhpBBInstaller.php'
	),
	'Composer\\Installers\\PimcoreInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PimcoreInstaller.php'
	),
	'Composer\\Installers\\PiwikInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PiwikInstaller.php'
	),
	'Composer\\Installers\\PlentymarketsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PlentymarketsInstaller.php'
	),
	'Composer\\Installers\\Plugin' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Plugin.php'
	),
	'Composer\\Installers\\PortoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PortoInstaller.php'
	),
	'Composer\\Installers\\PrestashopInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PrestashopInstaller.php'
	),
	'Composer\\Installers\\ProcessWireInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ProcessWireInstaller.php'
	),
	'Composer\\Installers\\PuppetInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PuppetInstaller.php'
	),
	'Composer\\Installers\\PxcmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/PxcmsInstaller.php'
	),
	'Composer\\Installers\\RadPHPInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RadPHPInstaller.php'
	),
	'Composer\\Installers\\ReIndexInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ReIndexInstaller.php'
	),
	'Composer\\Installers\\Redaxo5Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Redaxo5Installer.php'
	),
	'Composer\\Installers\\RedaxoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RedaxoInstaller.php'
	),
	'Composer\\Installers\\RoundcubeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/RoundcubeInstaller.php'
	),
	'Composer\\Installers\\SMFInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SMFInstaller.php'
	),
	'Composer\\Installers\\ShopwareInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ShopwareInstaller.php'
	),
	'Composer\\Installers\\SilverStripeInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SilverStripeInstaller.php'
	),
	'Composer\\Installers\\SiteDirectInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SiteDirectInstaller.php'
	),
	'Composer\\Installers\\StarbugInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/StarbugInstaller.php'
	),
	'Composer\\Installers\\SyDESInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SyDESInstaller.php'
	),
	'Composer\\Installers\\SyliusInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/SyliusInstaller.php'
	),
	'Composer\\Installers\\Symfony1Installer' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/Symfony1Installer.php'
	),
	'Composer\\Installers\\TYPO3CmsInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3CmsInstaller.php'
	),
	'Composer\\Installers\\TYPO3FlowInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TYPO3FlowInstaller.php'
	),
	'Composer\\Installers\\TaoInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TaoInstaller.php'
	),
	'Composer\\Installers\\TheliaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TheliaInstaller.php'
	),
	'Composer\\Installers\\TuskInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/TuskInstaller.php'
	),
	'Composer\\Installers\\UserFrostingInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/UserFrostingInstaller.php'
	),
	'Composer\\Installers\\VanillaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/VanillaInstaller.php'
	),
	'Composer\\Installers\\VgmcpInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/VgmcpInstaller.php'
	),
	'Composer\\Installers\\WHMCSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WHMCSInstaller.php'
	),
	'Composer\\Installers\\WolfCMSInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WolfCMSInstaller.php'
	),
	'Composer\\Installers\\WordPressInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/WordPressInstaller.php'
	),
	'Composer\\Installers\\YawikInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/YawikInstaller.php'
	),
	'Composer\\Installers\\ZendInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ZendInstaller.php'
	),
	'Composer\\Installers\\ZikulaInstaller' => array(
		'version' => '********',
		'path'    => $vendorDir . '/composer/installers/src/Composer/Installers/ZikulaInstaller.php'
	),
	'Container' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-container.php'
	),
	'Hook_Manager' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-hook-manager.php'
	),
	'Jetpack_IXR_Client' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-client.php'
	),
	'Jetpack_IXR_ClientMulticall' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-ixr-clientmulticall.php'
	),
	'Jetpack_Options' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-options.php'
	),
	'Jetpack_Signature' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-signature.php'
	),
	'Jetpack_Tracks_Client' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-client.php'
	),
	'Jetpack_Tracks_Event' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-tracks-event.php'
	),
	'Jetpack_XMLRPC_Server' => array(
		'version' => '********',
		'path'    => $vendorDir . '/automattic/jetpack-connection/legacy/class-jetpack-xmlrpc-server.php'
	),
	'Latest_Autoloader_Guard' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-latest-autoloader-guard.php'
	),
	'Manifest_Reader' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-manifest-reader.php'
	),
	'PHP_Autoloader' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-php-autoloader.php'
	),
	'Path_Processor' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-path-processor.php'
	),
	'Plugin_Locator' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugin-locator.php'
	),
	'Plugins_Handler' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-plugins-handler.php'
	),
	'Shutdown_Handler' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-shutdown-handler.php'
	),
	'Version_Loader' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-loader.php'
	),
	'Version_Selector' => array(
		'version' => '2.11.18.0',
		'path'    => $vendorDir . '/automattic/jetpack-autoloader/src/class-version-selector.php'
	),
	'WCPay\\MultiCurrency\\AdminNotices' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/AdminNotices.php'
	),
	'WCPay\\MultiCurrency\\Analytics' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Analytics.php'
	),
	'WCPay\\MultiCurrency\\BackendCurrencies' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/BackendCurrencies.php'
	),
	'WCPay\\MultiCurrency\\Compatibility' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\BaseCompatibility' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/BaseCompatibility.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceBookings' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceBookings.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceDeposits' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceDeposits.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceFedEx' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceFedEx.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceNameYourPrice' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceNameYourPrice.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommercePointsAndRewards' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommercePointsAndRewards.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommercePreOrders' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommercePreOrders.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceProductAddOns' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceProductAddOns.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceSubscriptions' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceSubscriptions.php'
	),
	'WCPay\\MultiCurrency\\Compatibility\\WooCommerceUPS' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Compatibility/WooCommerceUPS.php'
	),
	'WCPay\\MultiCurrency\\CountryFlags' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/CountryFlags.php'
	),
	'WCPay\\MultiCurrency\\Currency' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Currency.php'
	),
	'WCPay\\MultiCurrency\\CurrencySwitcherBlock' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/CurrencySwitcherBlock.php'
	),
	'WCPay\\MultiCurrency\\CurrencySwitcherWidget' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/CurrencySwitcherWidget.php'
	),
	'WCPay\\MultiCurrency\\Exceptions\\InvalidCurrencyException' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Exceptions/InvalidCurrencyException.php'
	),
	'WCPay\\MultiCurrency\\Exceptions\\InvalidCurrencyRateException' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Exceptions/InvalidCurrencyRateException.php'
	),
	'WCPay\\MultiCurrency\\FrontendCurrencies' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/FrontendCurrencies.php'
	),
	'WCPay\\MultiCurrency\\FrontendPrices' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/FrontendPrices.php'
	),
	'WCPay\\MultiCurrency\\Geolocation' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Geolocation.php'
	),
	'WCPay\\MultiCurrency\\Helpers\\OrderMetaHelper' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Helpers/OrderMetaHelper.php'
	),
	'WCPay\\MultiCurrency\\MultiCurrency' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/MultiCurrency.php'
	),
	'WCPay\\MultiCurrency\\Notes\\NoteMultiCurrencyAvailable' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php'
	),
	'WCPay\\MultiCurrency\\PaymentMethodsCompatibility' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/PaymentMethodsCompatibility.php'
	),
	'WCPay\\MultiCurrency\\RestController' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/RestController.php'
	),
	'WCPay\\MultiCurrency\\Settings' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Settings.php'
	),
	'WCPay\\MultiCurrency\\SettingsOnboardCta' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/SettingsOnboardCta.php'
	),
	'WCPay\\MultiCurrency\\StorefrontIntegration' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/StorefrontIntegration.php'
	),
	'WCPay\\MultiCurrency\\Tracking' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Tracking.php'
	),
	'WCPay\\MultiCurrency\\UserSettings' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/UserSettings.php'
	),
	'WCPay\\MultiCurrency\\Utils' => array(
		'version' => 'dev-release/6.2.2',
		'path'    => $baseDir . '/includes/multi-currency/Utils.php'
	),
);
