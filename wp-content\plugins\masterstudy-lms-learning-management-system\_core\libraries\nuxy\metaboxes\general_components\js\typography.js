(function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);throw new Error("Cannot find module '"+o+"'")}var f=n[o]={exports:{}};t[o][0].call(f.exports,function(e){var n=t[o][1][e];return s(n?n:e)},f,f.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
"use strict";

function _slicedToArray(arr, i) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();
}

function _nonIterableRest() {
  throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

function _unsupportedIterableToArray(o, minLen) {
  if (!o) return;
  if (typeof o === "string") return _arrayLikeToArray(o, minLen);
  var n = Object.prototype.toString.call(o).slice(8, -1);
  if (n === "Object" && o.constructor) n = o.constructor.name;
  if (n === "Map" || n === "Set") return Array.from(o);
  if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);
}

function _arrayLikeToArray(arr, len) {
  if (len == null || len > arr.length) len = arr.length;

  for (var i = 0, arr2 = new Array(len); i < len; i++) {
    arr2[i] = arr[i];
  }

  return arr2;
}

function _iterableToArrayLimit(arr, i) {
  var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];

  if (_i == null) return;
  var _arr = [];
  var _n = true;
  var _d = false;

  var _s, _e;

  try {
    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {
      _arr.push(_s.value);

      if (i && _arr.length === i) break;
    }
  } catch (err) {
    _d = true;
    _e = err;
  } finally {
    try {
      if (!_n && _i["return"] != null) _i["return"]();
    } finally {
      if (_d) throw _e;
    }
  }

  return _arr;
}

function _arrayWithHoles(arr) {
  if (Array.isArray(arr)) return arr;
}
/**
 * @var wpcfto_global_settings
 */


Vue.component('wpcfto_typography', {
  props: ['fields', 'field_label', 'field_name', 'field_id', 'field_value', 'field_data'],
  data: function data() {
    return {
      inited: false,
      google_fonts: wpcfto_global_settings['fonts_list']['google'],
      web_safe_fonts: wpcfto_global_settings['fonts_list']['websafe'],
      variants: wpcfto_global_settings['variants'],
      subsets: wpcfto_global_settings['subsets'],
      align: wpcfto_global_settings['align'],
      translations: wpcfto_global_settings['translations'],
      transform: wpcfto_global_settings['transform'],
      typography: {
        'font-family': '',
        'google-weight': 'regular',
        'font-weight': '400',
        'font-style': 'normal',
        'subset': 'latin',
        'color': '#000',
        'font-size': '14',
        'line-height': '20',
        'text-align': 'left',
        'word-spacing': '0',
        'text-transform': 'normal',
        'letter-spacing': '0',
        'backup-font': '',
        'font-data': {
          'family': '',
          'variants': []
        }
      }
    };
  },
  template: "\n        <div class=\"wpcfto_generic_field wpcfto_generic_field__typography\" v-bind:class=\"field_id\">\n\n            <wpcfto_fields_aside_before :fields=\"fields\" :field_label=\"field_label\"></wpcfto_fields_aside_before>\n            \n            <div class=\"wpcfto-field-content\">\n                <div class=\"wpcfto-typography-fields-wrap\">\n                    <div class=\"row\">\n                        <div class=\"column\">\n                        \n                            <div class=\"column-1\" v-if=\"typography['font-family'].length\">\n                                <div v-if=\"typography['font-family'].length\">\n                                    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\">\n                                    <link :href=\"buildGLink()\" rel=\"stylesheet\">\n                                </div>\n                            </div>\n                \n                            <div class=\"column-50\" v-if=\"notExcluded('font-family')\">\n                                <label class=\"field-label\" v-html=\"translations['font_family']\"></label>                        \n                                <select v-model=\"typography['font-data'].family\" @change=\"fontChanged()\">\n                                    <option value=\"\">Select font</option>\n                                    <option v-bind:value=\"font.family\" v-for=\"font in google_fonts\" v-html=\"font.family\"></option>\n                                </select>\n                            </div>        \n                \n                            <div class=\"column-50\" v-if=\"notExcluded('backup-font')\">\n                                <label class=\"field-label\" v-html=\"translations['backup_font_family']\"></label>       \n                                <select v-model=\"typography['backup-font']\">\n                                    <option value=\"\">Select backup font</option>\n                                    <option v-bind:value=\"font\" v-for=\"font in web_safe_fonts\" v-html=\"font\"></option>\n                                </select>\n                            </div>        \n                                  \n                            <div class=\"column-50\" v-if=\"notExcluded('google-weight')\">\n                                <label class=\"field-label\" v-html=\"translations['font_weight']\"></label>     \n                                <select v-model=\"typography['google-weight']\" @change=\"weightChanged()\">\n                                    <option value=\"\">Select font weight</option>\n                                    <option\n                                        v-bind:value=\"variant_key\"\n                                        :disabled=\"isFontWeightDisabled(variant_key)\"\n                                        v-for=\"(variant, variant_key) in variants\" v-html=\"variant\"></option>\n                                </select>\n                            </div>        \n        \n                            <div class=\"column-50\" v-if=\"notExcluded('subset')\">\n                                <label class=\"field-label\" v-html=\"translations['font_subset']\"></label>       \n                                <select v-model=\"typography['subset']\">\n                                    <option value=\"\">Select subset</option>\n                                    <option\n                                        v-bind:value=\"subset_key\"\n                                        :disabled=\"isSubsetDisabled(subset_key)\"\n                                        v-for=\"(subset, subset_key) in subsets\" v-html=\"subset\"></option>\n                                </select>\n                            </div>        \n        \n                            <div class=\"column-50\" v-if=\"notExcluded('text-align')\">\n                                <label class=\"field-label\" v-html=\"translations['text_align']\"></label> \n                                <select v-model=\"typography['text-align']\">\n                                    <option\n                                        v-bind:value=\"align_key\"\n                                        v-for=\"(align_label, align_key) in align\" v-html=\"align_label\"></option>\n                                </select>\n                            </div>        \n        \n                            <div class=\"column-50\">\n                                <div class=\"row\">\n                                    <div class=\"column\">\n                                        <div class=\"column-50\" v-if=\"notExcluded('font-size')\">\n                                            <label>\n                                                <span class=\"field-label\" v-html=\"translations['font_size']\"></span>\n                                                <div class=\"input-group\">\n                                                    <input type=\"number\" class=\"form-control\" v-model=\"typography['font-size']\" min=\"1\">\n                                                    <span class=\"input-group-addon\">px</span>\n                                                </div>                                                \n                                            </label>                                \n                                        </div>\n                                        <div class=\"column-50\" v-if=\"notExcluded('line-height')\">\n                                            <label>\n                                                <span class=\"field-label\" v-html=\"translations['line_height']\"></span>\n                                                <div class=\"input-group\">\n                                                    <input type=\"number\" class=\"form-control\" v-model=\"typography['line-height']\" min=\"0\">\n                                                    <span class=\"input-group-addon\">px</span>\n                                                </div>                                             \n                                            </label>                                   \n                                        </div>\n                                    </div>  \n                                </div>                         \n                            </div>\n        \n                            <div class=\"column-50\">\n                                <div class=\"row\">\n                                    <div class=\"column\">\n                                        <div class=\"column-50\" v-if=\"notExcluded('word-spacing')\">\n                                            <label>\n                                                <span class=\"field-label\" v-html=\"translations['word_spacing']\"></span>\n                                                <div class=\"input-group\">\n                                                    <input type=\"number\" class=\"form-control\" v-model=\"typography['word-spacing']\" min=\"0\">\n                                                    <span class=\"input-group-addon\">px</span>\n                                                </div>\n                                            </label>                                \n                                        </div>\n                                        <div class=\"column-50\" v-if=\"notExcluded('letter-spacing')\">\n                                            <label>\n                                                <span class=\"field-label\" v-html=\"translations['letter_spacing']\"></span>\n                                                <div class=\"input-group\">\n                                                    <input type=\"number\" class=\"form-control\" v-model=\"typography['letter-spacing']\" min=\"0\">\n                                                    <span class=\"input-group-addon\">px</span>\n                                                </div>                                            \n                                            </label>                            \n                                        </div>\n                                    </div>\n                                </div>                          \n                            </div>     \n        \n                            <div class=\"column-50\" v-if=\"notExcluded('color')\">\n                                <label class=\"field-label\" v-html=\"translations['font_color']\"></label> \n                                <wpcfto_color @wpcfto-get-value=\"typography['color'] = $event\"\n                                        :fields=\"{position: 'bottom'}\"\n                                        v-if=\"inited\"\n                                        :field_value=\"typography['color']\">\n                                </wpcfto_color>\n                            </div>            \n                            \n                            <div class=\"column-50\" v-if=\"notExcluded('text-transform')\"> \n                                <label class=\"field-label\" v-html=\"translations['text-transform']\"></label>\n                                <select v-model=\"typography['text-transform']\">\n                                    <option\n                                        v-bind:value=\"transform_key\"\n                                        v-for=\"(transform_label, transform_key) in transform\" v-html=\"transform_label\"></option>\n                                </select>\n                            </div>\n                \n                            <div class=\"column-1\" v-if=\"notExcluded('preview')\">\n                                <div class=\"wpcfto_generic_field__typography__preview\" :style=\"previewStyles()\">\n                                    ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 1234567890 \u2018?\u2019\u201C!\u201D(%)[#]{@}/&\\<-+\xF7\xD7=>\xAE\xA9$\u20AC\xA3\xA5\xA2:;,.*\n                                </div>            \n                            </div>            \n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <wpcfto_fields_aside_after :fields=\"fields\"></wpcfto_fields_aside_after>\n\n        </div>\n    ",
  mounted: function mounted() {
    if (typeof this.field_value === 'string' && WpcftoIsJsonString(this.field_value)) {
      this.field_value = JSON.parse(this.field_value);
    }

    this.fillTypography();
    this.inited = true;
    this.editVariant();
    this.editSubset();
  },
  methods: {
    fillTypography: function fillTypography() {
      var _this = this;

      var _loop = function _loop() {
        var _Object$entries$_i = _slicedToArray(_Object$entries[_i], 1),
            key = _Object$entries$_i[0];

        var value = _this.field_value[key];

        if (typeof value !== 'undefined') {
          _this.$set(_this.typography, key, value);

          if (key === 'font-family') {
            _this.setGoogleFontFamily(value);
          }

          if (key === 'font-weight') {
            setTimeout(function () {
              _this.$set(_this.typography, 'font-weight', value);

              if (typeof _this.field_value['google-weight'] !== 'undefined') {
                _this.$set(_this.typography, 'google-weight', _this.field_value['google-weight']);
              }
            });
          }
        }
      };

      for (var _i = 0, _Object$entries = Object.entries(_this.typography); _i < _Object$entries.length; _i++) {
        _loop();
      }
    },
    isFontWeightDisabled: function isFontWeightDisabled(variant) {
      if (typeof this.field_data['excluded'] !== 'undefined' && this.field_data['excluded'].includes('font-family')) {
        return false;
      }

      var current_variants = this.typography['font-data']['variants'];
      if (typeof current_variants === 'undefined') return false;
      return !current_variants.includes(variant);
    },
    isSubsetDisabled: function isSubsetDisabled(subset) {
      var current_subsets = this.typography['font-data']['subsets'];
      if (typeof current_subsets === 'undefined') return false;
      return !current_subsets.includes(subset);
    },
    fontChanged: function fontChanged() {
      this.$set(this.typography, 'font-family', this.typography['font-data'].family);
      this.editVariant();
      this.editSubset();
    },
    editVariant: function editVariant() {
      var current_variant = this.typography['google-weight'];
      var current_variants = this.typography['font-data']['variants'];

      if (typeof current_variants !== 'undefined' && !current_variants.includes(current_variant)) {
        this.$set(this.typography, 'google-weight', current_variants[0]);
        this.weightChanged();
      }
    },
    editSubset: function editSubset() {
      var current_subset = this.typography['subset'];
      var current_subsets = this.typography['font-data']['subsets'];

      if (typeof current_subsets !== 'undefined' && !current_subsets.includes(current_subset)) {
        this.$set(this.typography, 'subset', current_subsets[0]);
      }
    },
    buildGLink: function buildGLink() {
      var base = 'https://fonts.googleapis.com/css2?family=';
      base += "".concat(this.typography['font-family']);
      var isItalic = this.typography['font-style'] === 'italic';
      base += isItalic ? ':ital,' : ':';
      base += 'wght@';
      if (isItalic) base += '1,';
      base += this.typography['font-weight'];
      base += '&display=swap';
      return base;
    },
    previewStyles: function previewStyles() {
      var typo = this.typography;
      return {
        'font-family': "'".concat(typo['font-family'], "', ").concat(typo['font-data']['category']),
        'color': typo['color'],
        'font-size': typo['font-size'] + 'px',
        'line-height': typo['line-height'] + 'px',
        'letter-spacing': typo['letter-spacing'] + 'px',
        'word-spacing': typo['word-spacing'] + 'px',
        'text-align': typo['text-align'],
        'font-weight': typo['font-weight'],
        'font-style': typo['font-style'],
        'text-transform': typo['text-transform']
      };
    },
    weightChanged: function weightChanged() {
      var typo = this.typography;
      var weight = typo['google-weight'];
      var multiWeight = typeof weight !== 'undefined' ? weight.match(/[a-zA-Z]+|[0-9]+/g) : ['400', 'normal'];

      if (weight === 'regular') {
        this.$set(typo, 'font-weight', 400);
        this.$set(typo, 'font-style', 'normal');
      } else if (weight === 'italic') {
        this.$set(typo, 'font-weight', 400);
        this.$set(typo, 'font-style', 'italic');
      } else if (multiWeight.length === 2) {
        this.$set(typo, 'font-weight', multiWeight[0]);
        this.$set(typo, 'font-style', multiWeight[1]);
      } else {
        this.$set(typo, 'font-weight', weight);
        this.$set(typo, 'font-style', 'normal');
      }
    },
    notExcluded: function notExcluded(option) {
      if (typeof this.field_data['excluded'] === 'undefined') return true;
      var excluded = this.field_data['excluded'];
      return !excluded.includes(option);
    },
    setGoogleFontFamily: function setGoogleFontFamily(font_family) {
      var _this = this;

      _this.google_fonts.forEach(function (value) {
        if (value.family === font_family) {
          _this.$set(_this.typography, 'font-data', value);

          _this.editVariant();

          _this.editSubset();
        }
      });
    }
  },
  watch: {
    typography: {
      deep: true,
      handler: function handler(typography) {
        this.$emit('wpcfto-get-value', typography);
      }
    }
  }
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
},{}]},{},[1])